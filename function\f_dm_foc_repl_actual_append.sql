-- Name: f_dm_foc_repl_actual_append; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_actual_append(f_view_flag integer, f_caliber_flag character varying, f_year integer, f_cost_type character varying, f_keystr character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述：对通用实际数本年进行补齐，用于同编码指数
参数描述: x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T(通用颗粒度--采购成本),
		FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T(通用颗粒度--制造成本)
目标表:
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_ACTUAL_APPEND()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_ACTUAL_APPEND'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自
  --V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(F_YEAR||'01'),'YYYYMM') ; --本年首月
  V_BEGIN_DATE TIMESTAMP;
  V_END_DATE   TIMESTAMP;
 
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_FROM_TABLE VARCHAR(100); -- 来源表
  V_TO_TABLE VARCHAR(100); -- 目标表
  

  V_RMB_COST_AMT TEXT;
  V_FINAL_RMB_AMT TEXT;
  V_FINAL_AVG_AMT TEXT;
  V_REPLACE_PARA TEXT;
  V_IN_REPLACE_PARA TEXT;
  V_IN_REPLACE_CODE TEXT;
  V_INSERT_REPLACE_PARA TEXT;
  
  V_WHERE_PARA TEXT;
  

  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型：'||F_COST_TYPE);
   
  

  IF F_COST_TYPE = 'P' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_MTD_AVG_T_'||F_CALIBER_FLAG||'_'||F_VIEW_FLAG;
     --V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_MTD_AVG_T';--目标表
  ELSIF F_COST_TYPE = 'M' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_MTD_AVG_T'; 
     --V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_MTD_AVG_T'; --目标表 


	 
  ELSE
    NULL;
  END IF;

--判断版本表
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	

 
  --1.清空目标表数据:
  IF F_COST_TYPE = 'P' THEN
	EXECUTE IMMEDIATE 'DELETE FROM   '||V_TO_TABLE||' WHERE PERIOD_YEAR = '||F_YEAR ;
  ELSIF   F_COST_TYPE = 'M' THEN 
	EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE ;
	
  END IF;	

  
 
  

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,产业类型：'||F_COST_TYPE||',年份：'||f_year,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  

  
 
        
    
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';

	
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='T.LV3_PROD_RD_TEAM_CN_NAME,';

    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND NVL(T.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';

    

	

	--判断不同成本所使用的维度,以及加解密金额
	IF F_COST_TYPE = 'M' THEN 
	V_REPLACE_PARA := ' SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CODE,MANUFACTURE_OBJECT_CN_NAME, ';
	
	V_IN_REPLACE_PARA := ' T.SHIPPING_OBJECT_CODE,T.SHIPPING_OBJECT_CN_NAME,T.MANUFACTURE_OBJECT_CODE,T.MANUFACTURE_OBJECT_CN_NAME, ';
	
	V_IN_REPLACE_CODE := 'T.SHIPPING_OBJECT_CODE,T.MANUFACTURE_OBJECT_CODE, ';
	
	V_INSERT_REPLACE_PARA := ' AND NVL(T.SHIPPING_OBJECT_CODE,2) = NVL(T2.SHIPPING_OBJECT_CODE,2) 
								AND NVL(T.MANUFACTURE_OBJECT_CODE,2) = NVL(T2.MANUFACTURE_OBJECT_CODE,2)';
								
	V_RMB_COST_AMT := ' (RMB_AVG_AMT * SHIP_QUANTITY ) AS RMB_COST_AMT ,';	

    V_FINAL_RMB_AMT := 	'RMB_COST_AMT ,';
	V_FINAL_AVG_AMT := 'RMB_AVG_AMT ,';	

	V_WHERE_PARA :=	 '';
	
	
	F_YEAR := YEAR(CURRENT_TIMESTAMP);
	
    V_BEGIN_DATE  := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP) -3)||'01','YYYYMM') ; --三年首月
	
	
	
	V_END_DATE := CURRENT_TIMESTAMP;
	
	
	
	
	ELSIF F_COST_TYPE = 'P' THEN 
	V_REPLACE_PARA := ' L3_CEG_CODE,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME, L4_CEG_CODE ,L4_CEG_CN_NAME  , L4_CEG_SHORT_CN_NAME , CATEGORY_CODE, CATEGORY_CN_NAME ,';
	
	V_IN_REPLACE_PARA := ' T.L3_CEG_CODE,T.L3_CEG_CN_NAME,T.L3_CEG_SHORT_CN_NAME, T.L4_CEG_CODE ,T.L4_CEG_CN_NAME  , T.L4_CEG_SHORT_CN_NAME , T.CATEGORY_CODE, T.CATEGORY_CN_NAME ,';
	
	V_IN_REPLACE_CODE := ' T.L3_CEG_CODE, T.L4_CEG_CODE , T.CATEGORY_CODE ,';
	
	V_INSERT_REPLACE_PARA := ' AND NVL(T.L3_CEG_CODE,2) = NVL(T2.L3_CEG_CODE,2) 
								AND NVL(T.L4_CEG_CODE,2) = NVL(T2.L4_CEG_CODE,2)
								 AND NVL(T.CATEGORY_CODE,2) = NVL(T2.CATEGORY_CODE,2) ';
								 
								 
	V_RMB_COST_AMT := 'TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')) AS RMB_COST_AMT,';  --采购成本要加解密

	V_FINAL_RMB_AMT:= 'GS_ENCRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') ,';  --采购成本要加解密
	V_FINAL_AVG_AMT:= 'GS_ENCRYPT(RMB_AVG_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') ,';  --采购成本要加解密
	
	V_WHERE_PARA := ' AND PERIOD_YEAR = '||F_YEAR||'
					  AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''
					  AND VIEW_FLAG = '||F_VIEW_FLAG||'';
	
	
	--历史年份跑满1-12月
	IF F_YEAR < YEAR(CURRENT_TIMESTAMP)   THEN
	V_BEGIN_DATE  := TO_DATE(TO_CHAR(F_YEAR||'01'),'YYYYMM') ; --本年首月
	V_END_DATE    := TO_DATE(TO_CHAR(F_YEAR+1 ||'01'),'YYYYMM') ; --来年首月

	--如果是本年且月份不为1,则从年初跑到当前月减一
	ELSIF F_YEAR = YEAR(CURRENT_TIMESTAMP)  AND MONTH(CURRENT_TIMESTAMP) != 1    THEN
	V_BEGIN_DATE  := TO_DATE(TO_CHAR(F_YEAR||'01'),'YYYYMM') ; --本年首月
	V_END_DATE    := CURRENT_TIMESTAMP ; --当前月 	

	--如果是本年且月份为1,返回成功
	ELSIF F_YEAR = YEAR(CURRENT_TIMESTAMP)  AND MONTH(CURRENT_TIMESTAMP) = 1    THEN
	--V_BEGIN_DATE  := TO_DATE(TO_CHAR((F_YEAR -1) ||'01'),'YYYYMM') ; --上年首月
	--V_END_DATE    := TO_DATE(TO_CHAR(F_YEAR||'01'),'YYYYMM') ; --本年首月

	return 'SUCCESS';
	
	END IF;
	
	END IF;


	
--创建月累计临时表
DROP TABLE IF EXISTS ACTUAL_SUMMARY_TEMP;
CREATE TEMPORARY TABLE ACTUAL_SUMMARY_TEMP (
	VIEW_FLAG VARCHAR(2),
	LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
	LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
	LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
	LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
	LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
	LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
	LV3_PROD_RND_TEAM_CODE    VARCHAR(50), -- 7月版本需求新增
	LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(100),

	--采购成本
	L3_CEG_CODE    VARCHAR(50),
	L3_CEG_CN_NAME    VARCHAR(200),
	L3_CEG_SHORT_CN_NAME    VARCHAR(200),
	L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
	L4_CEG_CN_NAME    VARCHAR(200),
	L4_CEG_SHORT_CN_NAME    VARCHAR(200),
	CATEGORY_CODE CHARACTER VARYING(50),
	CATEGORY_CN_NAME CHARACTER VARYING(200),

	--11月版本新增制造对象
	SHIPPING_OBJECT_CODE VARCHAR(200),
	SHIPPING_OBJECT_CN_NAME VARCHAR(200),
	MANUFACTURE_OBJECT_CODE VARCHAR(200),
	MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_CN_NAME CHARACTER VARYING(500),
	PERIOD_YEAR BIGINT,
	PERIOD_ID BIGINT,
	SHIP_QUANTITY NUMERIC,
	RMB_AVG_AMT NUMERIC,
	RMB_COST_AMT NUMERIC,
	NULL_FLAG VARCHAR(2),
	APD_FLAG VARCHAR(2),
	CALIBER_FLAG VARCHAR(2),
	OVERSEA_FLAG VARCHAR(2),
	LV0_PROD_LIST_CODE VARCHAR(50),
	LV0_PROD_LIST_CN_NAME VARCHAR(200),
	LV0_PROD_LIST_EN_NAME VARCHAR(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);

--先进行月累计计算
V_SQL := 'INSERT INTO ACTUAL_SUMMARY_TEMP (
			 VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,'||
             V_LV3_PROD_RND_TEAM_CODE ||
             V_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_REPLACE_PARA||'
             ITEM_CODE,
             ITEM_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             SHIP_QUANTITY,
             RMB_AVG_AMT,
			 RMB_COST_AMT,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV0_PROD_LIST_EN_NAME
			 )
	SELECT 
			  VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,'||
             V_LV3_PROD_RND_TEAM_CODE ||
             V_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_REPLACE_PARA||'
             ITEM_CODE,
             ITEM_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             SHIP_QUANTITY, --月累计后的金额,
             (RMB_COST_AMT /  SHIP_QUANTITY) AS RMB_AVG_AMT,
			 RMB_COST_AMT, --月累计后的金额
             CALIBER_FLAG, 
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV0_PROD_LIST_EN_NAME
		FROM		 
			 
	(	SELECT 
			  VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,'||
             V_LV3_PROD_RND_TEAM_CODE ||
             V_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_REPLACE_PARA||'
             ITEM_CODE,
             ITEM_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             SUM(SHIP_QUANTITY) OVER (PARTITION BY PERIOD_YEAR,LV0_PROD_RND_TEAM_CODE,LV1_PROD_RND_TEAM_CODE,LV2_PROD_RND_TEAM_CODE,'||V_LV3_PROD_RND_TEAM_CODE||V_REPLACE_PARA||'VIEW_FLAG,CALIBER_FLAG,ITEM_CODE,OVERSEA_FLAG,LV0_PROD_LIST_CODE ORDER BY PERIOD_ID) SHIP_QUANTITY, --月累计后的金额,
             RMB_AVG_AMT,
			 SUM(RMB_COST_AMT) OVER (PARTITION BY PERIOD_YEAR,LV0_PROD_RND_TEAM_CODE,LV1_PROD_RND_TEAM_CODE,LV2_PROD_RND_TEAM_CODE,'||V_LV3_PROD_RND_TEAM_CODE||V_REPLACE_PARA||'VIEW_FLAG,CALIBER_FLAG,ITEM_CODE,OVERSEA_FLAG,LV0_PROD_LIST_CODE ORDER BY PERIOD_ID) RMB_COST_AMT, --月累计后的金额
             CALIBER_FLAG, 
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV0_PROD_LIST_EN_NAME
		FROM
			 (
		SELECT 
			 VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,'||
             V_LV3_PROD_RND_TEAM_CODE ||
             V_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_REPLACE_PARA||'
             ITEM_CODE,
             ITEM_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             SHIP_QUANTITY,
             NULL AS RMB_AVG_AMT,
			 '||V_RMB_COST_AMT||'
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV0_PROD_LIST_EN_NAME
		FROM '||V_FROM_TABLE||' 	
		WHERE PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
        AND PERIOD_ID <
             CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)
	    '||V_WHERE_PARA||'
		AND OVERSEA_FLAG = ''G''
		AND  LV0_PROD_LIST_CODE = ''GR''
       )

 )'	;
 
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
	DBMS_OUTPUT.PUT_LINE('累积计算完成');


	 --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '累积计算完成,产业类型：'||F_COST_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
	
  
  
 --创建临时表
            DROP TABLE IF EXISTS ACTUAL_APD_TEMP;
            CREATE TEMPORARY TABLE ACTUAL_APD_TEMP (
                VIEW_FLAG VARCHAR(2),
                LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
                LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
                LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(100),
				--采购成本
				L3_CEG_CODE    VARCHAR(50),
                L3_CEG_CN_NAME    VARCHAR(200),
                L3_CEG_SHORT_CN_NAME    VARCHAR(200),
                L4_CEG_CODE    VARCHAR(50), 
                L4_CEG_CN_NAME    VARCHAR(200),
                L4_CEG_SHORT_CN_NAME    VARCHAR(200),
                CATEGORY_CODE CHARACTER VARYING(50),
                CATEGORY_CN_NAME CHARACTER VARYING(200),
			
				--制造对象
				SHIPPING_OBJECT_CODE VARCHAR(200),
				SHIPPING_OBJECT_CN_NAME VARCHAR(200),
				MANUFACTURE_OBJECT_CODE VARCHAR(200),
				MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
                ITEM_CODE CHARACTER VARYING(50),
                ITEM_CN_NAME CHARACTER VARYING(500),
                PERIOD_YEAR BIGINT,
                PERIOD_ID BIGINT,
                SHIP_QUANTITY NUMERIC,
                RMB_AVG_AMT NUMERIC,
				RMB_COST_AMT NUMERIC,
                NULL_FLAG VARCHAR(2),
                APD_FLAG VARCHAR(2),
                CALIBER_FLAG VARCHAR(2),
                OVERSEA_FLAG VARCHAR(2),
                LV0_PROD_LIST_CODE VARCHAR(50),
                LV0_PROD_LIST_CN_NAME VARCHAR(200),
                LV0_PROD_LIST_EN_NAME VARCHAR(200)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '实际数补齐临时表创建完成,产业类型：'||F_COST_TYPE,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'); 


    V_SQL := 
       'INSERT INTO ACTUAL_APD_TEMP ( 
             VIEW_FLAG,
             LV0_PROD_RND_TEAM_CODE,
             LV0_PROD_RD_TEAM_CN_NAME,
             LV1_PROD_RND_TEAM_CODE,
             LV1_PROD_RD_TEAM_CN_NAME,
             LV2_PROD_RND_TEAM_CODE,
             LV2_PROD_RD_TEAM_CN_NAME,'||
             V_LV3_PROD_RND_TEAM_CODE ||
             V_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_REPLACE_PARA||'
             ITEM_CODE,
             ITEM_CN_NAME,
             PERIOD_YEAR,
             PERIOD_ID,
             SHIP_QUANTITY,
             RMB_AVG_AMT,
			 RMB_COST_AMT,
             NULL_FLAG,
             APD_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME,
             LV0_PROD_LIST_EN_NAME)
 
    WITH ACTUAL_ITEM_TEMP AS
     (
      --实际数历史表中出现的重量级团队、采购信息维，取数范围：物料卷积历史表第1月至当前系统月
      SELECT DISTINCT T.VIEW_FLAG,
                      T.LV0_PROD_RND_TEAM_CODE,
                      T.LV0_PROD_RD_TEAM_CN_NAME,
                      T.LV1_PROD_RND_TEAM_CODE,
                      T.LV1_PROD_RD_TEAM_CN_NAME,
                      T.LV2_PROD_RND_TEAM_CODE,
                      T.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_IN_LV3_PROD_RND_TEAM_CODE ||
                      V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
					  V_IN_REPLACE_PARA ||'
                      T.ITEM_CODE,
                      T.ITEM_CN_NAME,
                      T.CALIBER_FLAG,
                      T.OVERSEA_FLAG,
                      T.LV0_PROD_LIST_CODE,
                      T.LV0_PROD_LIST_CN_NAME,
                      T.LV0_PROD_LIST_EN_NAME
        FROM ACTUAL_SUMMARY_TEMP T
       WHERE 
		 T.PERIOD_ID <
             CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 三年前第一月至当前系统月(不含)
      SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                    V_BEGIN_DATE||''',
                                                      '''||V_END_DATE||''')),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的发散维
      SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              T.VIEW_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_IN_REPLACE_PARA ||'
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM ACTUAL_ITEM_TEMP T, PERIOD_DIM_TEMP B
		--WHERE CASE WHEN SUBSTR(B.PERIOD_ID,5,6) !=1 THEN SUBSTR(B.PERIOD_ID,5,6) < CAST(TO_CHAR(CURRENT_TIMESTAMP, ''MM'') AS BIGINT)
		--			WHEN SUBSTR(B.PERIOD_ID,5,6) =1 THEN SUBSTR(B.PERIOD_ID,5,6) <= 12
		--			END
		)
                
            SELECT T.VIEW_FLAG,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,
             T.LV2_PROD_RND_TEAM_CODE,
             T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_IN_REPLACE_PARA ||'
             T.ITEM_CODE,
             T.ITEM_CN_NAME,
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T2.SHIP_QUANTITY,
             T2.RMB_AVG_AMT,
			 T2.RMB_COST_AMT,
             DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
             DECODE(T2.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始
             T.CALIBER_FLAG,
             T.OVERSEA_FLAG,
             T.LV0_PROD_LIST_CODE,
             T.LV0_PROD_LIST_CN_NAME,
             T.LV0_PROD_LIST_EN_NAME
        FROM CROSS_JOIN_TEMP T
        LEFT JOIN ACTUAL_SUMMARY_TEMP T2
          ON T.VIEW_FLAG = T2.VIEW_FLAG
         AND NVL(T.LV0_PROD_RND_TEAM_CODE,0) = NVL(T2.LV0_PROD_RND_TEAM_CODE,0)
         AND NVL(T.LV1_PROD_RND_TEAM_CODE,1) = NVL(T2.LV1_PROD_RND_TEAM_CODE,1)
         AND NVL(T.LV2_PROD_RND_TEAM_CODE,2) = NVL(T2.LV2_PROD_RND_TEAM_CODE,2)
         AND T.ITEM_CODE = T2.ITEM_CODE
         AND T.PERIOD_ID = T2.PERIOD_ID
         AND T.CALIBER_FLAG = T2.CALIBER_FLAG
         AND T.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE'
         ||V_INSERT_LV3_PROD_RND_TEAM_CODE
		 ||V_INSERT_REPLACE_PARA
		 ;
		DBMS_OUTPUT.PUT_LINE(V_SQL);
         EXECUTE IMMEDIATE V_SQL;

                 
 --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入数据到实际数补齐临时表,产业类型：'||F_COST_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   

 --2.只补齐均价, 发货额和发货数量无需补齐
      V_SQL := 
           'INSERT INTO '||V_TO_TABLE||' 
                (VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,'||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||
				V_REPLACE_PARA||'
                ITEM_CODE,
                ITEM_CN_NAME,
                SHIP_QUANTITY,
                RMB_AVG_AMT,
				RMB_COST_AMT,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                APPEND_FLAG,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME)
    
  WITH FORWARD_FILLER_TEMP AS
     (
      --按照重量级团队, 采购信息维补齐, 前向补齐均价
      SELECT  T.VIEW_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_IN_REPLACE_PARA||'
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              T.SHIP_QUANTITY,
              T.RMB_AVG_AMT,
              FIRST_VALUE(T.RMB_COST_AMT) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE  ||V_IN_REPLACE_PARA ||' T.ITEM_CODE, T.AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS RMB_COST_AMT_2, --新补齐的均价字段
              FIRST_VALUE(T.RMB_AVG_AMT) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE  ||V_IN_REPLACE_PARA ||' T.ITEM_CODE, T.AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS RMB_AVG_AMT_2, --新补齐的均价字段
              T.AVG_AMT_FLAG,
              T.APD_FLAG,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM (SELECT  T.VIEW_FLAG,
                      T.LV0_PROD_RND_TEAM_CODE,
                      T.LV0_PROD_RD_TEAM_CN_NAME,
                      T.LV1_PROD_RND_TEAM_CODE,
                      T.LV1_PROD_RD_TEAM_CN_NAME,
                      T.LV2_PROD_RND_TEAM_CODE,
                      T.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_IN_LV3_PROD_RND_TEAM_CODE ||
                      V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
					  V_IN_REPLACE_PARA||'
                      T.ITEM_CODE,
                      T.ITEM_CN_NAME,
                      T.PERIOD_YEAR,
                      T.PERIOD_ID,
                      T.SHIP_QUANTITY,
                      T.RMB_AVG_AMT,
                      T.RMB_COST_AMT,
                      SUM(T.NULL_FLAG) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_REPLACE_PARA ||' T.ITEM_CODE ORDER BY T.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      T.APD_FLAG,
                      T.CALIBER_FLAG,
                      T.OVERSEA_FLAG,
                      T.LV0_PROD_LIST_CODE,
                      T.LV0_PROD_LIST_CN_NAME,
                      T.LV0_PROD_LIST_EN_NAME
                 FROM ACTUAL_APD_TEMP T) T)


    --向后补齐均价
	SELECT '||V_VERSION_ID||',
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_PROD_RND_TEAM_CODE,
           LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE,
           LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE,
           LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_REPLACE_PARA||'
           ITEM_CODE,
           ITEM_CN_NAME,
           SHIP_QUANTITY,
           '||V_FINAL_AVG_AMT||'
		   '||V_FINAL_RMB_AMT||'
           CREATED_BY,
           CREATION_DATE,
           LAST_UPDATED_BY,
           LAST_UPDATE_DATE,
           DEL_FLAG,
           APPEND_FLAG,
           VIEW_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV0_PROD_LIST_EN_NAME
		FROM
	
	(
	SELECT '||V_VERSION_ID||',
           T.PERIOD_YEAR,
           T.PERIOD_ID,
           T.LV0_PROD_RND_TEAM_CODE,
           T.LV0_PROD_RD_TEAM_CN_NAME,
           T.LV1_PROD_RND_TEAM_CODE,
           T.LV1_PROD_RD_TEAM_CN_NAME,
           T.LV2_PROD_RND_TEAM_CODE,
           T.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_IN_REPLACE_PARA||'
           T.ITEM_CODE,
           T.ITEM_CN_NAME,
           T.SHIP_QUANTITY,
           NVL(T.RMB_AVG_AMT_2, T.RMB_AVG_AMT_3) AS RMB_AVG_AMT,
		   NVL(T.RMB_COST_AMT_2, T.RMB_COST_AMT_3) AS RMB_COST_AMT,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           T.APD_FLAG AS APPEND_FLAG,
           ''S'' AS SCENARIO_FLAG,
           T.VIEW_FLAG,
           T.CALIBER_FLAG,
           T.OVERSEA_FLAG,
           T.LV0_PROD_LIST_CODE,
           T.LV0_PROD_LIST_CN_NAME,
           T.LV0_PROD_LIST_EN_NAME
      FROM (SELECT T.VIEW_FLAG,
                   T.LV0_PROD_RND_TEAM_CODE,
                   T.LV0_PROD_RD_TEAM_CN_NAME,
                   T.LV1_PROD_RND_TEAM_CODE,
                   T.LV1_PROD_RD_TEAM_CN_NAME,
                   T.LV2_PROD_RND_TEAM_CODE,
                   T.LV2_PROD_RD_TEAM_CN_NAME,'||
                   V_IN_LV3_PROD_RND_TEAM_CODE ||
                   V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
				   V_IN_REPLACE_PARA||'
                   T.ITEM_CODE,
                   T.ITEM_CN_NAME,
                   T.PERIOD_YEAR,
                   T.PERIOD_ID,
                   T.SHIP_QUANTITY,
                   T.RMB_COST_AMT_2,
				   T2.RMB_COST_AMT_3,
                   T.RMB_AVG_AMT_2,
                   T2.RMB_AVG_AMT_3,
                   T.APD_FLAG,
                   T.CALIBER_FLAG,
                   T.OVERSEA_FLAG,
                   T.LV0_PROD_LIST_CODE,
                   T.LV0_PROD_LIST_CN_NAME,
                   T.LV0_PROD_LIST_EN_NAME
              FROM FORWARD_FILLER_TEMP T
              LEFT JOIN (SELECT DISTINCT T.CALIBER_FLAG,
                                        T.OVERSEA_FLAG,
                                        T.LV0_PROD_LIST_CODE,
                                        T.VIEW_FLAG,
                                        T.LV0_PROD_RND_TEAM_CODE,
                                        T.LV1_PROD_RND_TEAM_CODE,
                                        T.LV2_PROD_RND_TEAM_CODE,'||
                                        V_IN_LV3_PROD_RND_TEAM_CODE ||
										V_IN_REPLACE_CODE||'
                                        T.ITEM_CODE,
                                        FIRST_VALUE(T.PERIOD_ID) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_REPLACE_CODE ||'  T.ITEM_CODE ORDER BY T.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(T.RMB_AVG_AMT_2) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_REPLACE_CODE ||'  T.ITEM_CODE ORDER BY T.PERIOD_ID ASC) AS RMB_AVG_AMT_3 ,--有均价的首条补齐均价
										FIRST_VALUE(T.RMB_COST_AMT_2) OVER(PARTITION BY T.LV0_PROD_LIST_CODE, T.OVERSEA_FLAG, T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_REPLACE_CODE ||'  T.ITEM_CODE ORDER BY T.PERIOD_ID ASC) AS RMB_COST_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP T
                         WHERE T.AVG_AMT_FLAG > 0) T2
                ON T.VIEW_FLAG = T2.VIEW_FLAG
               AND NVL(T.LV0_PROD_RND_TEAM_CODE,0) = NVL(T2.LV0_PROD_RND_TEAM_CODE,0)
               AND NVL(T.LV1_PROD_RND_TEAM_CODE,1) = NVL(T2.LV1_PROD_RND_TEAM_CODE,1)
               AND NVL(T.LV2_PROD_RND_TEAM_CODE,2) = NVL(T2.LV2_PROD_RND_TEAM_CODE,2)
               AND T.ITEM_CODE = T2.ITEM_CODE
               AND T.PERIOD_ID < T2.PERIOD_ID
               AND T.CALIBER_FLAG = T2.CALIBER_FLAG
               AND T.OVERSEA_FLAG = T2.OVERSEA_FLAG
               AND T.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE'
               ||V_INSERT_LV3_PROD_RND_TEAM_CODE
			   ||V_INSERT_REPLACE_PARA
			   ||'
               ) T
			)';
			DBMS_OUTPUT.PUT_LINE('ok');
         --DBMS_OUTPUT.PUT_LINE(V_SQL);
         EXECUTE IMMEDIATE V_SQL;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价实际数到'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID||',产业类型：'||F_COST_TYPE,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成,产业类型：'||F_COST_TYPE);

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,产业类型：'||F_COST_TYPE, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

