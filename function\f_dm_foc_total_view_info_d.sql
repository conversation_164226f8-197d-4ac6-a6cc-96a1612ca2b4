-- Name: f_dm_foc_total_view_info_d; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_total_view_info_d(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2023-10-27
创建人  ：黄心蕊 hwx1187045
最新修改时间：2024年6月22日
修改人  ：唐钦
修改内容：202401版本量纲颗粒度新增SPART层级
背景描述：总成本-维表数据初始化
参数描述：参数一（）：产业项目标识，'I'为ICT，'E'为数字能源
          参数二(F_DIMENSION_TYPE)：维度入参，'U'为通用颗粒度，'P'为盈利颗粒度，'D'为量纲颗粒度
          参数三(x_result_status)：运行状态返回值 ‘1’为成功，‘0’为失败
通用颗粒度：
来源表： FIN_DM_OPT_FOI.DM_FOC_VIEW_INFO_D             --采购年度分析维表
         FIN_DM_OPT_FOI.DM_FOC_MADE_VIEW_INFO_D      --制造年度分析维表
         FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T         --采购规格品清单
         FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T    --制造规格品清单
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_VIEW_INFO_D    --总成本维表
盈利颗粒度：
来源表： FIN_DM_OPT_FOI.DM_FOC_PFT_VIEW_INFO_D             --采购年度分析维表
         FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_VIEW_INFO_D      --制造年度分析维表
         FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T         --采购规格品清单
         FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_TOP_ITEM_INFO_T    --制造规格品清单
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_VIEW_INFO_D    --总成本维表
量纲颗粒度：
来源表： FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_INFO_D             --采购年度分析维表
         FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_VIEW_INFO_D      --制造年度分析维表
         FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T         --采购规格品清单
         FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_TOP_ITEM_INFO_T    --制造规格品清单
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_VIEW_INFO_D    --总成本维表

事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_VIEW_INFO_D('I','U'); --通用颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_VIEW_INFO_D('I','P'); --盈利颗粒度一个版本的数据
          SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_VIEW_INFO_D('I','D'); --盈利颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME         VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_VIEW_INFO_D';
  V_VERSION         BIGINT;
  V_STEP_NUM        BIGINT := 0; --函数步骤号
  V_PUR_FROM_TABLE  VARCHAR(100);
  V_MADE_FROM_TABLE VARCHAR(100);
  V_TO_TABLE        VARCHAR(100);
  V_DIM_PART        TEXT; --查询字段 不同颗粒度维度字段不同
  V_DIM_LEVEL_PART  TEXT; --查询层级 不同颗粒度所需维度层级不同
  V_SQL_DIM_PART    TEXT;
  V_JOIN_SQL        TEXT;
  V_SQL             TEXT;
  V_GROUP_LEVEL     TEXT;
  V_GROUP_NUM       TEXT;
  V_ITEM_VERSION    TEXT;
  V_VIEW_PART       TEXT:= NULL;
  V_VERSION_TABLE   VARCHAR(100);  -- 202504版本新增
  V_IN_DIFF_COLUMN       VARCHAR(100); 
  V_INTO_DIFF_COLUMN          VARCHAR(100); 
  V_LV2_PROD        VARCHAR(100);
  V_LV1_PROD        VARCHAR(100);
  V_SQL_LV2_PROD    VARCHAR(100);
  V_SQL_LV1_PROD    VARCHAR(100);
  V_BEGIN_NUM       INT;
  V_REL_DIFF_COLUMN VARCHAR(100);
  
BEGIN

--1.变量定义
   IF F_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
    V_GROUP_NUM:= 5;
    V_DIM_LEVEL_PART  := ' (''LV0'',''LV1'',''LV2'',''LV3'',''LV4'') ';  -- 202407新增LV4层级-IAS
    V_VIEW_PART          := ' AND VIEW_FLAG IN (''0'',''1'',''2'',''3'',''7'')';  -- 202407新增视角7-IAS
    -- 入参值为：I/IAS时，变量定义不同
    IF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
       V_JOIN_SQL := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE ,''AAA'') = NVL(T2.LV3_PROD_RND_TEAM_CODE ,''AAA'') 
                      AND NVL(T1.LV4_PROD_RND_TEAM_CODE ,''BBB'') = NVL(T2.LV4_PROD_RND_TEAM_CODE ,''BBB'') ';
       V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,
                      LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,'; --查询字段
       V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,
                          T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
    ELSE   -- ICT/ENERGY
       V_JOIN_SQL := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE ,''AAA'') = NVL(T2.LV3_PROD_RND_TEAM_CODE ,''AAA'') ';
       V_DIM_PART        := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,'; --查询字段
       V_SQL_DIM_PART    := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,';
    END IF;
  ELSIF F_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
    V_DIM_PART       := 'L1_NAME,L2_NAME,';
    V_SQL_DIM_PART   := 'T1.L1_NAME,T1.L2_NAME,';
    V_GROUP_NUM:= 5;
    V_DIM_LEVEL_PART := ' (''LV0'',''LV1'',''LV2'',''L1'',''L2'') ';
    V_JOIN_SQL       := 'AND NVL(T1.L1_NAME || T1.L2_NAME, ''AAA'') =
                          NVL(DECODE(T1.L1_NAME, '''' , '''', T2.L1_NAME) ||
                              DECODE(T1.L2_NAME, '''' , '''', T2.L2_NAME),''AAA'')';
  
  ELSIF F_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
  -- 当产业项目标识为：E时，加COA层级变量
       IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
          V_IN_DIFF_COLUMN := 'COA_CODE,COA_CN_NAME,';
          V_INTO_DIFF_COLUMN := 'T1.COA_CODE,T1.COA_CN_NAME,';
          V_JOIN_SQL := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE || T1.SPART_CODE || T1.COA_CODE ,''AAA'') =
                           NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                             DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
                             DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
                             DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
                             DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE) ||
                             DECODE(T1.COA_CODE,'''','''',T2.COA_CODE) , 
                             ''AAA'')';  -- 202405版本新增COA层级
       ELSIF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
          V_JOIN_SQL := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE || T1.SPART_CODE,''AAA'') =
                           NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                             DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
                             DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
                             DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
                             DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE) , 
                             ''AAA'')';  --202401版本新增SPART层级
       ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS 202407版本新增LV4层级,IAS领域
          V_IN_DIFF_COLUMN := 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';
          V_INTO_DIFF_COLUMN := 'T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
          V_JOIN_SQL := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE || T1.SPART_CODE || T1.LV4_PROD_RND_TEAM_CODE ,''AAA'') =
                           NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                             DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
                             DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
                             DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
                             DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE) ||
                             DECODE(T1.LV4_PROD_RND_TEAM_CODE,'''','''',T2.LV4_PROD_RND_TEAM_CODE) , 
                             ''AAA'')';  -- 202407版本新增LV4层级
       END IF;
          
    V_DIM_PART        := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,
                          DIMENSION_CODE,DIMENSION_CN_NAME,DIMENSION_EN_NAME,
                          DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_EN_NAME,
                          DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,DIMENSION_SUB_DETAIL_EN_NAME,
                          SPART_CODE , SPART_CN_NAME,'||V_IN_DIFF_COLUMN; --202405版本新增COA层级/202407版本新增LV4层级
    V_GROUP_NUM:= 9; --202405版本新增COA层级/202407版本新增LV4层级
    V_SQL_DIM_PART    := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,
                          T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,T1.DIMENSION_EN_NAME,
                          T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,T1.DIMENSION_SUBCATEGORY_EN_NAME,
                          T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,T1.DIMENSION_SUB_DETAIL_EN_NAME,
                          T1.SPART_CODE , T1.SPART_CN_NAME,'||V_INTO_DIFF_COLUMN; --202405版本新增COA层级/202407版本新增LV4层级
    V_DIM_LEVEL_PART  := ' (''LV0'',''LV1'',''LV2'',''LV3'',''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'',''COA'',''LV4'') ';  --202401版本新增SPART层级/202405新增COA层级/202407新增LV4层级
  END IF;

 --1.1 来源表及目标表定义
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
   IF F_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_MID_VIEW_INFO_D';
   ELSIF F_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_PFT_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MID_VIEW_INFO_D';
   ELSIF F_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MID_VIEW_INFO_D';
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 202405版本新增数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
   IF F_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_MID_VIEW_INFO_D';
   ELSIF F_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_MID_VIEW_INFO_D';
   ELSIF F_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_MID_VIEW_INFO_D';
   END IF;
   ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
   IF F_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_MID_VIEW_INFO_D';
   ELSIF F_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_MID_VIEW_INFO_D';
   ELSIF F_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_VIEW_INFO_D';
    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_VIEW_INFO_D';
    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_MID_VIEW_INFO_D';
   END IF;
  END IF;
  
 --1.2 版本号定义
   V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION;
    
   V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''ITEM''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_ITEM_VERSION;
   
--2.维表插数
--2.1维表删数
  V_STEP_NUM := V_STEP_NUM + 1;
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
--2.2采购维表插数
  V_STEP_NUM := V_STEP_NUM + 1;
V_SQL:='
INSERT INTO '||V_TO_TABLE||'(
VERSION_ID,
VIEW_FLAG,
LV0_PROD_RND_TEAM_CODE,
LV0_PROD_RD_TEAM_CN_NAME,
LV1_PROD_RND_TEAM_CODE,
LV1_PROD_RD_TEAM_CN_NAME,
LV2_PROD_RND_TEAM_CODE,
LV2_PROD_RD_TEAM_CN_NAME,
'||V_DIM_PART||'
GROUP_LEVEL,
CREATED_BY,
CREATION_DATE,
LAST_UPDATED_BY,
LAST_UPDATE_DATE,
DEL_FLAG,
CALIBER_FLAG,
OVERSEA_FLAG,
LV0_PROD_LIST_CODE,
LV0_PROD_LIST_CN_NAME,
LV0_PROD_LIST_EN_NAME,
PAGE_FLAG
)
SELECT DISTINCT '||V_VERSION||' AS VERSION_ID,
                VIEW_FLAG,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,
                '||V_DIM_PART||'
                GROUP_LEVEL,
                ''-1'' AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                ''-1'' AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                ''N'' AS DEL_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,
                ''ANNUAL'' AS PAGE_FLAG
  FROM '||V_PUR_FROM_TABLE||'
 WHERE GROUP_LEVEL IN '||V_DIM_LEVEL_PART||' ;';
 DBMS_OUTPUT.PUT_LINE(V_SQL);
 EXECUTE V_SQL;
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_TO_TABLE||'采购维度插数成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_result_status,
   F_ERRBUF => 'SUCCESS');
   
--2.2制造维表插数
  V_STEP_NUM := V_STEP_NUM + 1;
    V_SQL:='
    INSERT INTO '||V_TO_TABLE||'(
    VERSION_ID,
    VIEW_FLAG,
    LV0_PROD_RND_TEAM_CODE,
    LV0_PROD_RD_TEAM_CN_NAME,
    LV1_PROD_RND_TEAM_CODE,
    LV1_PROD_RD_TEAM_CN_NAME,
    LV2_PROD_RND_TEAM_CODE,
    LV2_PROD_RD_TEAM_CN_NAME,
    '||V_DIM_PART||'
    GROUP_LEVEL,
    CREATED_BY,
    CREATION_DATE,
    LAST_UPDATED_BY,
    LAST_UPDATE_DATE,
    DEL_FLAG,
    CALIBER_FLAG,
    OVERSEA_FLAG,
    LV0_PROD_LIST_CODE,
    LV0_PROD_LIST_CN_NAME,
    LV0_PROD_LIST_EN_NAME,
    PAGE_FLAG
    )
--取制造维度
WITH MADE_DIM AS
 (SELECT DISTINCT VIEW_FLAG,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  LV1_PROD_RND_TEAM_CODE,
                  LV1_PROD_RD_TEAM_CN_NAME,
                  LV2_PROD_RND_TEAM_CODE,
                  LV2_PROD_RD_TEAM_CN_NAME,
                  '||V_DIM_PART||'
                  GROUP_LEVEL,
                  CALIBER_FLAG,
                  OVERSEA_FLAG,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME,
                  LV0_PROD_LIST_EN_NAME
    FROM '||V_MADE_FROM_TABLE||'
   WHERE GROUP_LEVEL IN '||V_DIM_LEVEL_PART||' ),
   
--取采购维度
PUR_DIM AS
 (SELECT DISTINCT VIEW_FLAG,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  LV1_PROD_RND_TEAM_CODE,
                  LV1_PROD_RD_TEAM_CN_NAME,
                  LV2_PROD_RND_TEAM_CODE,
                  LV2_PROD_RD_TEAM_CN_NAME,
                  '||V_DIM_PART||'
                  GROUP_LEVEL,
                  CALIBER_FLAG,
                  OVERSEA_FLAG,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME,
                  LV0_PROD_LIST_EN_NAME
    FROM '||V_PUR_FROM_TABLE||'
   WHERE GROUP_LEVEL IN '||V_DIM_LEVEL_PART||' )
   
 SELECT '||V_VERSION||' AS VERSION_ID,
        T1.VIEW_FLAG,
        T1.LV0_PROD_RND_TEAM_CODE,
        T1.LV0_PROD_RD_TEAM_CN_NAME,
        T1.LV1_PROD_RND_TEAM_CODE,
        T1.LV1_PROD_RD_TEAM_CN_NAME,
        T1.LV2_PROD_RND_TEAM_CODE,
        T1.LV2_PROD_RD_TEAM_CN_NAME,
        '||V_SQL_DIM_PART||'
        T1.GROUP_LEVEL,
        ''-1'' AS CREATED_BY,
        CURRENT_TIMESTAMP AS CREATION_DATE,
        ''-1'' AS LAST_UPDATED_BY,
        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
        ''N'' AS DEL_FLAG,
        T1.CALIBER_FLAG,
        T1.OVERSEA_FLAG,
        T1.LV0_PROD_LIST_CODE,
        T1.LV0_PROD_LIST_CN_NAME,
        T1.LV0_PROD_LIST_EN_NAME,
        ''ANNUAL'' AS PAGE_FLAG
   FROM MADE_DIM T1
   WHERE 1=1
     AND NOT EXISTS --去除同类项
         (SELECT 1            
            FROM PUR_DIM T2
           WHERE T1.VIEW_FLAG||T1.CALIBER_FLAG || T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE || T1.GROUP_LEVEL 
                = T2.VIEW_FLAG||T2.CALIBER_FLAG || T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE || T2.GROUP_LEVEL
             AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
            '||V_JOIN_SQL||' )';
 DBMS_OUTPUT.PUT_LINE(V_SQL);
 EXECUTE V_SQL;
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_TO_TABLE||'年度制造维度插数成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_result_status,
   F_ERRBUF => 'SUCCESS');
   
 -- 判断产业项目为ICT还是数字能源还是IAS
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     IF (F_DIMENSION_TYPE = 'U') THEN
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T';
     ELSIF F_DIMENSION_TYPE = 'P' THEN
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_TOP_ITEM_INFO_T';
     ELSIF F_DIMENSION_TYPE = 'D' THEN 
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_TOP_ITEM_INFO_T';
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     IF (F_DIMENSION_TYPE = 'U') THEN
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_TOP_ITEM_INFO_T';
     ELSIF F_DIMENSION_TYPE = 'P' THEN
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_TOP_ITEM_INFO_T';
     ELSIF F_DIMENSION_TYPE = 'D' THEN 
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_TOP_ITEM_INFO_T';
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
     IF (F_DIMENSION_TYPE = 'U') THEN
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_TOP_ITEM_INFO_T';
     ELSIF F_DIMENSION_TYPE = 'P' THEN
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_TOP_ITEM_INFO_T';
     ELSIF F_DIMENSION_TYPE = 'D' THEN 
        V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_ITEM_INFO_T';
        V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_TOP_ITEM_INFO_T';
     END IF;
  END IF;
  
  -- 变量定义
   V_LV2_PROD := 'LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME,';
   V_LV1_PROD := 'LV1_PROD_RND_TEAM_CODE,LV1_PROD_RD_TEAM_CN_NAME,';
   V_SQL_LV2_PROD := 'T1.LV2_PROD_RND_TEAM_CODE,T1.LV2_PROD_RD_TEAM_CN_NAME,';
   V_SQL_LV1_PROD := 'T1.LV1_PROD_RND_TEAM_CODE,T1.LV1_PROD_RD_TEAM_CN_NAME,';
   IF (F_INDUSTRY_FLAG = 'IAS' AND F_DIMENSION_TYPE = 'U') THEN  -- IAS领域、通用颗粒度
      V_BEGIN_NUM := 1;
      V_REL_DIFF_COLUMN := ' AND NVL(T1.LV4_PROD_RND_TEAM_CODE,''BBB'') = NVL(T2.LV4_PROD_RND_TEAM_CODE,''BBB'')';
   ELSIF (F_INDUSTRY_FLAG = 'IAS' AND F_DIMENSION_TYPE = 'D') THEN  -- IAS领域、量纲颗粒度
      V_BEGIN_NUM := 1;
      V_GROUP_LEVEL := '''LV4'''; 
      V_REL_DIFF_COLUMN := ' AND NVL(T1.LV4_PROD_RND_TEAM_CODE,''BBB'') = NVL(T2.LV4_PROD_RND_TEAM_CODE,''BBB'')';
   ELSIF (F_INDUSTRY_FLAG = 'E' AND F_DIMENSION_TYPE = 'D') THEN  -- 数字能源领域、量纲颗粒度
      V_BEGIN_NUM := 1;
      V_GROUP_LEVEL := '''COA'''; 
      V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''BBB'') = NVL(T2.COA_CODE,''BBB'')';
   ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度时
      V_BEGIN_NUM := 1;
   ELSE 
      V_BEGIN_NUM := 2;
   END IF;

    FOR GROUP_NUM IN V_BEGIN_NUM..V_GROUP_NUM LOOP 
     IF (F_DIMENSION_TYPE = 'U') THEN
     --如果是通用颗粒度的
     
      IF (GROUP_NUM = 1 ) THEN
        V_GROUP_LEVEL := '''LV4''';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE ,''AAA'') = NVL(T2.LV3_PROD_RND_TEAM_CODE ,''AAA'') 
                '||V_REL_DIFF_COLUMN;
      ELSIF (GROUP_NUM = 2 ) THEN
        V_GROUP_LEVEL := '''LV3''';
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,';
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE ,''AAA'') = NVL(T2.LV3_PROD_RND_TEAM_CODE ,''AAA'') ';
      ELSIF (GROUP_NUM = 3 ) THEN
        V_GROUP_LEVEL := '''LV2''';
        V_DIM_PART := '';
        V_SQL_DIM_PART := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')';
      ELSIF (GROUP_NUM = 4 ) THEN
        V_GROUP_LEVEL := '''LV1''';
        V_LV2_PROD := '';
        V_SQL_LV2_PROD := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE),''ABC'')';
      ELSIF (GROUP_NUM = 5 ) THEN
        V_GROUP_LEVEL := '''LV0''';
        V_LV1_PROD := '';
        V_SQL_LV1_PROD := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE),''ABC'')';
      END IF;

      --如果是盈利颗粒度的
     ELSIF F_DIMENSION_TYPE = 'P' THEN
       
      IF GROUP_NUM = 1 THEN
        V_GROUP_LEVEL := '''L2''';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.L1_NAME || T1.L2_NAME, ''AAA'') =
                     NVL(DECODE(T1.L1_NAME, '''' , '''', T2.L1_NAME) ||
                         DECODE(T1.L2_NAME, '''' , '''', T2.L2_NAME),''AAA'')';
      ELSIF GROUP_NUM = 2 THEN
        V_GROUP_LEVEL := '''L1''';
        V_DIM_PART := 'L1_NAME,';
        V_SQL_DIM_PART := 'T1.L1_NAME,';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.L1_NAME, ''AAA'') =
                     NVL(DECODE(T1.L1_NAME, '''' , '''', T2.L1_NAME),''AAA'')';
      ELSIF GROUP_NUM = 3 THEN
        V_GROUP_LEVEL := '''LV2''';
        V_DIM_PART := '';
        V_SQL_DIM_PART := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')';
      ELSIF GROUP_NUM = 4 THEN
        V_GROUP_LEVEL := '''LV1''';
        V_LV2_PROD := '';
        V_SQL_LV2_PROD := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE),''ABC'')';
      ELSIF GROUP_NUM = 5 THEN
        V_GROUP_LEVEL := '''LV0''';
        V_LV1_PROD := '';
        V_SQL_LV1_PROD := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE),''ABC'')';
      END IF ;
 
      --如果是量纲颗粒度的
     ELSIF F_DIMENSION_TYPE = 'D' THEN 
       
      IF GROUP_NUM = 1 THEN  -- 202405新增数字能源-COA层级/202407新增IAS-LV4层级
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,'||V_IN_DIFF_COLUMN; 
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,'||V_INTO_DIFF_COLUMN; 
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE ,''AAA'') =
                     NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) , 
                         ''AAA'')
                 '||V_REL_DIFF_COLUMN;
      ELSIF GROUP_NUM = 2 THEN
        V_GROUP_LEVEL := '''SPART''';  --202401版本新增SPART层级 
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,
                       DIMENSION_CODE,DIMENSION_CN_NAME,DIMENSION_EN_NAME,
                       DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_EN_NAME,
                       DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,DIMENSION_SUB_DETAIL_EN_NAME,
                       SPART_CODE , SPART_CN_NAME,'||V_IN_DIFF_COLUMN; 
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,
                       T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,T1.DIMENSION_EN_NAME,
                       T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,T1.DIMENSION_SUBCATEGORY_EN_NAME,
                       T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,T1.DIMENSION_SUB_DETAIL_EN_NAME,
                       T1.SPART_CODE , T1.SPART_CN_NAME,'||V_INTO_DIFF_COLUMN; 
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE || T1.SPART_CODE ,''AAA'') =
                     NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                         DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
                         DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
                         DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
                         DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE) , 
                         ''AAA'')
                 '||V_REL_DIFF_COLUMN;
      ELSIF GROUP_NUM = 3 THEN
        V_GROUP_LEVEL := '''SUB_DETAIL'''; 
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,
                       DIMENSION_CODE,DIMENSION_CN_NAME,DIMENSION_EN_NAME,
                       DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_EN_NAME,
                       DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,DIMENSION_SUB_DETAIL_EN_NAME,'||V_IN_DIFF_COLUMN; 
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,
                       T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,T1.DIMENSION_EN_NAME,
                       T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,T1.DIMENSION_SUBCATEGORY_EN_NAME,
                       T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,T1.DIMENSION_SUB_DETAIL_EN_NAME,'||V_INTO_DIFF_COLUMN; 
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE ,''AAA'') =
                     NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                         DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
                         DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
                         DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) , 
                         ''AAA'')
                 '||V_REL_DIFF_COLUMN;
      ELSIF GROUP_NUM = 4 THEN
        V_GROUP_LEVEL := '''SUBCATEGORY'''; 
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,
                       DIMENSION_CODE,DIMENSION_CN_NAME,DIMENSION_EN_NAME,
                       DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,DIMENSION_SUBCATEGORY_EN_NAME,'||V_IN_DIFF_COLUMN; 
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,
                       T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,T1.DIMENSION_EN_NAME,
                       T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,T1.DIMENSION_SUBCATEGORY_EN_NAME,'||V_INTO_DIFF_COLUMN; 
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE ,''AAA'') =
                     NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                         DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
                         DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE), 
                         ''AAA'')
                 '||V_REL_DIFF_COLUMN;
      ELSIF GROUP_NUM = 5 THEN
        V_GROUP_LEVEL := '''DIMENSION''';  
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,
                       DIMENSION_CODE,DIMENSION_CN_NAME,DIMENSION_EN_NAME,'||V_IN_DIFF_COLUMN; 
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,
                       T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,T1.DIMENSION_EN_NAME,'||V_INTO_DIFF_COLUMN; 
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE || T1.DIMENSION_CODE ,''AAA'') =
                     NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) ||
                         DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) , 
                         ''AAA'')
                 '||V_REL_DIFF_COLUMN;
      ELSIF GROUP_NUM = 6 THEN
        V_GROUP_LEVEL := '''LV3''';  
        V_DIM_PART := 'LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME,';
        V_SQL_DIM_PART := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,'; 
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')
                 AND NVL(T1.LV3_PROD_RND_TEAM_CODE ,''AAA'') =
                     NVL(DECODE(T1.LV3_PROD_RND_TEAM_CODE,'''','''',T2.LV3_PROD_RND_TEAM_CODE) , 
                         ''AAA'')';
      ELSIF GROUP_NUM = 7 THEN
        V_GROUP_LEVEL := '''LV2''';  
        V_DIM_PART := '';
        V_SQL_DIM_PART := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE || T1.LV2_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV2_PROD_RND_TEAM_CODE,'''','''',T2.LV2_PROD_RND_TEAM_CODE),''ABC'')';
      ELSIF GROUP_NUM = 8 THEN
        V_GROUP_LEVEL := '''LV1''';  
        V_LV2_PROD := '';
        V_SQL_LV2_PROD := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE || T1.LV1_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE)||
                 DECODE(T1.LV1_PROD_RND_TEAM_CODE,'''','''',T2.LV1_PROD_RND_TEAM_CODE),''ABC'')';
      ELSIF GROUP_NUM = 9 THEN
        V_GROUP_LEVEL := '''LV0''';  
        V_LV1_PROD := '';
        V_SQL_LV1_PROD := '';
        V_JOIN_SQL := 'AND NVL(T1.LV0_PROD_RND_TEAM_CODE ,''ABC'') =
                 NVL(DECODE(T1.LV0_PROD_RND_TEAM_CODE,'''','''',T2.LV0_PROD_RND_TEAM_CODE),''ABC'')';
       END IF;  
    END IF;
    
        V_SQL:='
        INSERT INTO '||V_TO_TABLE||'(
        VERSION_ID,
        VIEW_FLAG,
        LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME,
        '||V_LV1_PROD||'
        '||V_LV2_PROD||'
        '||V_DIM_PART||'
        GROUP_LEVEL,
        CREATED_BY,
        CREATION_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATE_DATE,
        DEL_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME,
        LV0_PROD_LIST_EN_NAME,
        PAGE_FLAG
        )
        SELECT DISTINCT '||V_VERSION||' AS VERSION_ID,
                        VIEW_FLAG,
                        LV0_PROD_RND_TEAM_CODE,
                        LV0_PROD_RD_TEAM_CN_NAME,
                        '||V_LV1_PROD||'
                        '||V_LV2_PROD||'
                        '||V_DIM_PART||'
                        '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
                        ''-1'' AS CREATED_BY,
                        CURRENT_TIMESTAMP AS CREATION_DATE,
                        ''-1'' AS LAST_UPDATED_BY,
                        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                        ''N'' AS DEL_FLAG,
                        CALIBER_FLAG,
                        OVERSEA_FLAG,
                        LV0_PROD_LIST_CODE,
                        LV0_PROD_LIST_CN_NAME,
                        LV0_PROD_LIST_EN_NAME,
                        ''MONTH'' AS PAGE_FLAG
          FROM '||V_PUR_FROM_TABLE||'
         WHERE VERSION_ID = '||V_ITEM_VERSION||V_VIEW_PART||' ;
         
        -- 制造月度分析-维度数据
            INSERT INTO '||V_TO_TABLE||'(
            VERSION_ID,
            VIEW_FLAG,
            LV0_PROD_RND_TEAM_CODE,
            LV0_PROD_RD_TEAM_CN_NAME,
            '||V_LV1_PROD||'
            '||V_LV2_PROD||'
            '||V_DIM_PART||'
            GROUP_LEVEL,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            DEL_FLAG,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV0_PROD_LIST_EN_NAME,
            PAGE_FLAG
            )
        --取制造维度
        WITH MADE_DIM AS
         (SELECT DISTINCT VIEW_FLAG,
                          LV0_PROD_RND_TEAM_CODE,
                          LV0_PROD_RD_TEAM_CN_NAME,
                          '||V_LV1_PROD||'
                          '||V_LV2_PROD||'
                          '||V_DIM_PART||'
                          CALIBER_FLAG,
                          OVERSEA_FLAG,
                          LV0_PROD_LIST_CODE,
                          LV0_PROD_LIST_CN_NAME,
                          LV0_PROD_LIST_EN_NAME
            FROM '||V_MADE_FROM_TABLE||' 
             WHERE VERSION_ID = '||V_ITEM_VERSION||V_VIEW_PART||'),
           
        --取采购维度
        PUR_DIM AS
         (SELECT DISTINCT VIEW_FLAG,
                          LV0_PROD_RND_TEAM_CODE,
                          LV0_PROD_RD_TEAM_CN_NAME,
                          '||V_LV1_PROD||'
                          '||V_LV2_PROD||'
                          '||V_DIM_PART||'
                          CALIBER_FLAG,
                          OVERSEA_FLAG,
                          LV0_PROD_LIST_CODE,
                          LV0_PROD_LIST_CN_NAME,
                          LV0_PROD_LIST_EN_NAME
            FROM '||V_PUR_FROM_TABLE||' 
             WHERE VERSION_ID = '||V_ITEM_VERSION||V_VIEW_PART||')
           
         SELECT '||V_VERSION||' AS VERSION_ID,
                T1.VIEW_FLAG,
                T1.LV0_PROD_RND_TEAM_CODE,
                T1.LV0_PROD_RD_TEAM_CN_NAME,
                '||V_SQL_LV1_PROD||'
                '||V_SQL_LV2_PROD||'
                '||V_SQL_DIM_PART||'
                '||V_GROUP_LEVEL||' AS GROUP_LEVEL ,
                ''-1'' AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                ''-1'' AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                ''N'' AS DEL_FLAG,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME,
                T1.LV0_PROD_LIST_EN_NAME,
                ''MONTH'' AS PAGE_FLAG
           FROM MADE_DIM T1
           WHERE 1=1
             AND NOT EXISTS --去除同类项
                 (SELECT 1            
                    FROM PUR_DIM T2
                   WHERE T1.VIEW_FLAG||T1.CALIBER_FLAG || T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE
                       = T2.VIEW_FLAG||T2.CALIBER_FLAG || T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE 
                    '||V_JOIN_SQL||' )';
   DBMS_OUTPUT.PUT_LINE(V_SQL);
         EXECUTE V_SQL;
  
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '计算层级标识='||V_GROUP_LEVEL||'的维度数据',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => x_result_status,
     F_ERRBUF => 'SUCCESS');

    END LOOP;
    
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => V_TO_TABLE||'月度页面维度数据插数成功',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => x_result_status,
     F_ERRBUF => 'SUCCESS');
   

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  x_result_status := '0';
  
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => x_result_status, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 
$$
/

