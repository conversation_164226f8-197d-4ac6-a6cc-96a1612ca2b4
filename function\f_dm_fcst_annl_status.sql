-- Name: f_dm_fcst_annl_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_annl_status(f_cost_type character varying, f_granularity_type character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年7月10日
  创建人  ：唐钦
  背景描述：根据年均本计算之后的表，取得对应的金额字段，按不同层级汇总，计算得出权重值
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ANNL_STATUS('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ANNL_STATUS'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_FROM1_TABLE VARCHAR(100);
  V_FROM2_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_TMP1_TABLE VARCHAR(100);
  V_TMP2_TABLE VARCHAR(100);
  V_YEAR INT := YEAR(CURRENT_DATE);
  V_IN_LV_CODE VARCHAR(100);
  V_INTO_LV_CODE VARCHAR(100);
  V_GROUP_CODE VARCHAR(100);
  V_GROUP_LEVEL VARCHAR(100);
  V_SQL_PARENT VARCHAR(100);
  V_PARTITION_DIM VARCHAR(500);
  V_LV_CODE VARCHAR(50);
  V_LV_NAME VARCHAR(50);
  V_LV0_CODE VARCHAR(50);
  V_LV0_NAME VARCHAR(50);
  V_LV1_CODE VARCHAR(50);
  V_LV1_NAME VARCHAR(50);
  V_LV2_CODE VARCHAR(50);
  V_LV2_NAME VARCHAR(50);
  V_LV3_CODE VARCHAR(50);
  V_LV3_NAME VARCHAR(50);
  V_LV4_CODE VARCHAR(50);
  V_LV4_NAME VARCHAR(50);
  V_LAST_YEAR_FLAG VARCHAR(50);
  V_YEAR_FLAG VARCHAR(50);
  V_YEAR_APPEND VARCHAR(50);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 根据入参，对变量进行不同定义
     V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
     V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AMP_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_STATUS_T';
     V_TMP1_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_LACK_STATUS_TMP';
     V_TMP2_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BASE_STATUS_T';
  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_INTO_LV_CODE := 'PROD_RND_TEAM_CODE';
     V_LV_CODE := 'PROD_RND_TEAM_CODE,';
     V_LV_NAME := 'PROD_RD_TEAM_CN_NAME,';
     V_LV0_CODE := 'LV0_PROD_RND_TEAM_CODE,';
     V_LV0_NAME := 'LV0_PROD_RD_TEAM_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_RND_TEAM_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_RND_TEAM_CODE,';
     V_LV2_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_RND_TEAM_CODE,';
     V_LV3_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE,';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_INTO_LV_CODE := 'INDUSTRY_CATG_CODE';
     V_LV_CODE := 'INDUSTRY_CATG_CODE,'; 
     V_LV_NAME := 'INDUSTRY_CATG_CN_NAME,';
     V_LV0_CODE := 'LV0_INDUSTRY_CATG_CODE,'; 
     V_LV0_NAME := 'LV0_INDUSTRY_CATG_CN_NAME,';
     V_LV1_CODE := 'LV1_INDUSTRY_CATG_CODE,'; 
     V_LV1_NAME := 'LV1_INDUSTRY_CATG_CN_NAME,';
     V_LV2_CODE := 'LV2_INDUSTRY_CATG_CODE,';
     V_LV2_NAME := 'LV2_INDUSTRY_CATG_CN_NAME,';
     V_LV3_CODE := 'LV3_INDUSTRY_CATG_CODE,';
     V_LV3_NAME := 'LV3_INDUSTRY_CATG_CN_NAME,';
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE,';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME,';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_INTO_LV_CODE := 'PROD_LIST_CODE';
     V_LV_CODE := 'PROD_LIST_CODE,'; 
     V_LV_NAME := 'PROD_LIST_CN_NAME,';
     V_LV0_CODE := 'LV0_PROD_LIST_CODE,'; 
     V_LV0_NAME := 'LV0_PROD_LIST_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_LIST_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_LIST_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_LIST_CODE,';
     V_LV2_NAME := 'LV2_PROD_LIST_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_LIST_CODE,';
     V_LV3_NAME := 'LV3_PROD_LIST_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_LIST_CODE,';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME,';
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空维表数据
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 创建临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP1_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP1_TABLE||' (
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         LAST_THREE_YEAR_FLAG          VARCHAR(50),
         LAST_THREE_APPEND_YEAR        VARCHAR(50),
         LAST_TWO_YEAR_FLAG            VARCHAR(50),
         LAST_TWO_APPEND_YEAR          VARCHAR(50),
         LAST_YEAR_FLAG                VARCHAR(50),
         LAST_APPEND_YEAR              VARCHAR(50),
         CURRENT_YEAR_FLAG             VARCHAR(50),
         CURRENT_APPEND_YEAR           VARCHAR(50),
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP1_TABLE||'表成功');
	 
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP2_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP2_TABLE||' (
         PERIOD_YEAR                   INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         STATUS_CODE                   INT,
         APPEND_YEAR                   INT,
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP2_TABLE||'表成功');
	 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '最细粒度层级缺失情况临时表创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 最细粒度层级缺失数据情况插入临时表
     V_SQL := '
     INSERT INTO '||V_TMP1_TABLE||'(            
                 LV0_CODE,                     
                 LV0_CN_NAME,                  
                 LV1_CODE,                     
                 LV1_CN_NAME,                  
                 LV2_CODE,                     
                 LV2_CN_NAME,                  
                 LV3_CODE,                     
                 LV3_CN_NAME,                  
                 LV4_CODE,                     
                 LV4_CN_NAME,                  
                 DIMENSION_CODE,               
                 DIMENSION_CN_NAME,            
                 DIMENSION_SUBCATEGORY_CODE,   
                 DIMENSION_SUBCATEGORY_CN_NAME,
                 DIMENSION_SUB_DETAIL_CODE,    
                 DIMENSION_SUB_DETAIL_CN_NAME, 
                 SPART_CODE,                   
                 SPART_CN_NAME,
                 MAIN_FLAG,             
                 CODE_ATTRIBUTES,       
                 LAST_THREE_YEAR_FLAG,
                 LAST_THREE_APPEND_YEAR,
                 LAST_TWO_YEAR_FLAG,    
                 LAST_TWO_APPEND_YEAR,  
                 LAST_YEAR_FLAG,        
                 LAST_APPEND_YEAR,      
                 CURRENT_YEAR_FLAG,     
                 CURRENT_APPEND_YEAR,   
                 VIEW_FLAG,             
                 REGION_CODE,           
                 REGION_CN_NAME,        
                 REPOFFICE_CODE,        
                 REPOFFICE_CN_NAME,     
                 BG_CODE,               
                 BG_CN_NAME,            
                 OVERSEA_FLAG
                 )
      SELECT '||V_LV0_CODE||'
             '||V_LV0_NAME||'
             '||V_LV1_CODE||'
             '||V_LV1_NAME||'
             '||V_LV2_CODE||'
             '||V_LV2_NAME||'
             '||V_LV3_CODE||'
             '||V_LV3_NAME||'
             '||V_LV4_CODE||'
             '||V_LV4_NAME||'
             DIMENSION_CODE,               
             DIMENSION_CN_NAME,            
             DIMENSION_SUBCATEGORY_CODE,   
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,    
             DIMENSION_SUB_DETAIL_CN_NAME, 
             SPART_CODE,                   
             SPART_CN_NAME,
             MAIN_FLAG,             
             CODE_ATTRIBUTES,
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
             SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
             VIEW_FLAG,        
             REGION_CODE,      
             REGION_CN_NAME,   
             REPOFFICE_CODE,   
             REPOFFICE_CN_NAME,
             BG_CODE,          
             BG_CN_NAME,       
             OVERSEA_FLAG
          FROM '||V_FROM1_TABLE||'
          GROUP BY '||V_LV0_CODE||'
                   '||V_LV0_NAME||'
                   '||V_LV1_CODE||'
                   '||V_LV1_NAME||'
                   '||V_LV2_CODE||'
                   '||V_LV2_NAME||'
                   '||V_LV3_CODE||'
                   '||V_LV3_NAME||'
                   '||V_LV4_CODE||'
                   '||V_LV4_NAME||'
                   DIMENSION_CODE,               
                   DIMENSION_CN_NAME,            
                   DIMENSION_SUBCATEGORY_CODE,   
                   DIMENSION_SUBCATEGORY_CN_NAME,
                   DIMENSION_SUB_DETAIL_CODE,    
                   DIMENSION_SUB_DETAIL_CN_NAME, 
                   SPART_CODE,                   
                   SPART_CN_NAME,
                   MAIN_FLAG,             
                   CODE_ATTRIBUTES,
                   VIEW_FLAG,        
                   REGION_CODE,      
                   REGION_CN_NAME,   
                   REPOFFICE_CODE,   
                   REPOFFICE_CN_NAME,
                   BG_CODE,          
                   BG_CN_NAME,       
                   OVERSEA_FLAG';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL; 
         
     --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');        
	
  -- ITEM层级状态码逻辑
  -- 对ITEM层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
        
    IF YEAR_FLAG = V_YEAR-2 THEN
        V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
        V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
    
    ELSIF YEAR_FLAG = V_YEAR-1 THEN
        V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
        V_YEAR_FLAG := 'LAST_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
        
    ELSIF YEAR_FLAG = V_YEAR THEN
        V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
        V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
    ELSE NULL;
    END IF;
    
     V_SQL := '
     INSERT INTO '||V_TMP2_TABLE||' (
            PERIOD_YEAR,
            LV0_CODE,                     
            LV0_CN_NAME,                  
            LV1_CODE,                     
            LV1_CN_NAME,                  
            LV2_CODE,                     
            LV2_CN_NAME,                  
            LV3_CODE,                     
            LV3_CN_NAME,                  
            LV4_CODE,                     
            LV4_CN_NAME,                  
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,
            MAIN_FLAG,             
            CODE_ATTRIBUTES,       
            VIEW_FLAG,        
            REGION_CODE,      
            REGION_CN_NAME,   
            REPOFFICE_CODE,   
            REPOFFICE_CN_NAME,
            BG_CODE,          
            BG_CN_NAME,       
            OVERSEA_FLAG,
            STATUS_CODE,
            APPEND_YEAR)
        SELECT '||YEAR_FLAG||' AS PERIOD_YEAR,
               LV0_CODE,                     
               LV0_CN_NAME,                  
               LV1_CODE,                     
               LV1_CN_NAME,                  
               LV2_CODE,                     
               LV2_CN_NAME,                  
               LV3_CODE,                     
               LV3_CN_NAME,                  
               LV4_CODE,                     
               LV4_CN_NAME,                  
               DIMENSION_CODE,               
               DIMENSION_CN_NAME,            
               DIMENSION_SUBCATEGORY_CODE,   
               DIMENSION_SUBCATEGORY_CN_NAME,
               DIMENSION_SUB_DETAIL_CODE,    
               DIMENSION_SUB_DETAIL_CN_NAME, 
               SPART_CODE,                   
               SPART_CN_NAME,
               MAIN_FLAG,             
               CODE_ATTRIBUTES,       
               VIEW_FLAG,        
               REGION_CODE,      
               REGION_CN_NAME,   
               REPOFFICE_CODE,   
               REPOFFICE_CN_NAME,
               BG_CODE,          
               BG_CN_NAME,       
               OVERSEA_FLAG,
               CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
                    WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' = 0 THEN 4
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' <> 0 THEN 5
               END AS STATUS_CODE,
               '||V_YEAR_APPEND||' AS APPEND_YEAR
            FROM '||V_TMP1_TABLE||' T1';
               
       DBMS_OUTPUT.PUT_LINE(V_SQL);
       EXECUTE IMMEDIATE V_SQL;  
       
        --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级全维度缺失状态码插入'||V_TMP2_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');         
    END LOOP;

    -- 把ITEM层级状态码数据插入状态码表中
     V_SQL := '
     INSERT INTO '||V_TO_TABLE||' (
            VERSION_ID,
            PERIOD_YEAR,
            '||V_LV_CODE||'
            '||V_LV_NAME||'
            DIMENSION_CODE,
            DIMENSION_CN_NAME,
            DIMENSION_SUBCATEGORY_CODE,
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,
            DIMENSION_SUB_DETAIL_CN_NAME,
            GROUP_CODE,
            GROUP_CN_NAME,
            GROUP_LEVEL,
            STATUS_CODE,
            PARENT_CODE,
            PARENT_CN_NAME,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            VIEW_FLAG,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            DEL_FLAG,
            APPEND_YEAR
         )
     SELECT '||V_VERSION_ID||' AS VERSION_ID,
            PERIOD_YEAR,
            LV4_CODE,
            LV4_CN_NAME,
            DIMENSION_CODE,
            DIMENSION_CN_NAME,
            DIMENSION_SUBCATEGORY_CODE,
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,
            DIMENSION_SUB_DETAIL_CN_NAME,
            DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CODE,''DIMENSION'',DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,       
            DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CN_NAME,''DIMENSION'',DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,  
            DECODE(VIEW_FLAG,''PROD_SPART'',''SPART'',''DIMENSION'',''SUB_DETAIL'') AS GROUP_LEVEL,
            STATUS_CODE,
            DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CODE,''DIMENSION'',DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,
            DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CN_NAME,''DIMENSION'',DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            VIEW_FLAG,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG,
            T1.APPEND_YEAR
         FROM '||V_TMP2_TABLE||' T1';
     DBMS_OUTPUT.PUT_LINE(V_SQL); 
     EXECUTE IMMEDIATE V_SQL; 
            
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的最细粒度层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    
  -- 按1-10 ，从品类层级到ICT层级，分不同项目进行循环    
  FOR GRO_LEV IN 1 .. 7 LOOP
  IF GRO_LEV = 1  THEN   -- 量纲子类层级
     V_IN_LV_CODE := 'T1.LV4_CODE AS LV_CODE,';
     V_GROUP_CODE := ' T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'SUBCATEGORY';
     V_SQL_PARENT := ' T1.DIMENSION_CODE AS PARENT_CODE,
                        T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';    
     V_PARTITION_DIM := 'T1.DIMENSION_SUBCATEGORY_CODE,
                         T1.DIMENSION_CODE,
                         T1.DIMENSION_CN_NAME,
                         T1.LV4_CODE,
                         T1.LV3_CODE,
                         T1.LV2_CODE,
                         T1.LV1_CODE,
                         T1.LV0_CODE,';
  ELSIF GRO_LEV = 2  THEN   -- 量纲层级
     V_GROUP_CODE := ' T1.DIMENSION_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'DIMENSION';
     V_SQL_PARENT := ' T1.LV4_CODE AS PARENT_CODE,
                       T1.LV4_CN_NAME AS PARENT_CN_NAME,'; 
     V_PARTITION_DIM := 'T1.DIMENSION_CODE,
                         T1.LV4_CODE,
                         T1.LV4_CN_NAME,
                         T1.LV3_CODE,
                         T1.LV2_CODE,
                         T1.LV1_CODE,
                         T1.LV0_CODE,';
  ELSIF GRO_LEV = 3 THEN   -- LV4层级
     V_GROUP_CODE := ' T1.LV4_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'LV4';
     V_SQL_PARENT := ' T1.LV3_CODE AS PARENT_CODE,
                       T1.LV3_CN_NAME AS PARENT_CN_NAME,'; 
     V_PARTITION_DIM := 'T1.LV4_CODE,
                         T1.LV3_CODE,
                         T1.LV3_CN_NAME,
                         T1.LV2_CODE,
                         T1.LV1_CODE,
                         T1.LV0_CODE,';
  ELSIF GRO_LEV = 4 THEN   -- LV3层级
     V_IN_LV_CODE := 'T1.LV3_CODE AS LV_CODE,';
     V_GROUP_CODE := ' T1.LV3_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'LV3';
     V_SQL_PARENT := ' T1.LV2_CODE AS PARENT_CODE,
                       T1.LV2_CN_NAME AS PARENT_CN_NAME,'; 
     V_PARTITION_DIM := 'T1.LV3_CODE,
                         T1.LV2_CODE,
                         T1.LV2_CN_NAME,
                         T1.LV1_CODE,
                         T1.LV0_CODE,';
  ELSIF GRO_LEV = 5 THEN   -- LV2层级
     V_IN_LV_CODE := 'T1.LV2_CODE AS LV_CODE,';
     V_GROUP_CODE := ' T1.LV2_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'LV2';
     V_SQL_PARENT := ' T1.LV1_CODE AS PARENT_CODE,
                       T1.LV1_CN_NAME AS PARENT_CN_NAME,'; 
     V_PARTITION_DIM := 'T1.LV2_CODE,
                         T1.LV1_CODE,
                         T1.LV1_CN_NAME,
                         T1.LV0_CODE,';
  ELSIF GRO_LEV = 6 THEN   -- LV1层级
     V_IN_LV_CODE := 'T1.LV1_CODE AS LV_CODE,';
     V_GROUP_CODE := ' T1.LV1_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'LV1';
     V_SQL_PARENT := ' T1.LV0_CODE AS PARENT_CODE,
                       T1.LV0_CN_NAME AS PARENT_CN_NAME,'; 
     V_PARTITION_DIM := 'T1.LV1_CODE,
                         T1.LV0_CODE,
                         T1.LV0_CN_NAME,';
  ELSIF GRO_LEV = 7 THEN   -- LV0层级
     V_IN_LV_CODE := 'T1.LV0_CODE AS LV_CODE,';
     V_GROUP_CODE := ' T1.LV0_CODE AS GROUP_CODE,';
     V_GROUP_LEVEL := 'LV0';
     V_SQL_PARENT := ' T1.LV0_CODE AS PARENT_CODE,
                       T1.LV0_CN_NAME AS PARENT_CN_NAME,'; 
     V_PARTITION_DIM := 'T1.LV0_CODE,
                         T1.LV0_CN_NAME,';
  END IF;

  V_SQL := '
      INSERT INTO '||V_TO_TABLE||' (
             VERSION_ID,
             PERIOD_YEAR,
             '||V_LV_CODE||'
             '||V_LV_NAME||'
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,
             DIMENSION_SUB_DETAIL_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             STATUS_CODE,
             PARENT_CODE,
             PARENT_CN_NAME,
             REGION_CODE,
             REGION_CN_NAME,
             REPOFFICE_CODE,
             REPOFFICE_CN_NAME,
             BG_CODE,
             BG_CN_NAME,
             OVERSEA_FLAG,
             VIEW_FLAG,
             MAIN_FLAG,
             CODE_ATTRIBUTES,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG,
             APPEND_YEAR
         ) 
      WITH BASEUP_STATUS_TMP AS(
           SELECT T1.PERIOD_YEAR,
                  '||V_IN_LV_CODE||'
                  '||V_GROUP_CODE||'    -- 取T1表中的上一层级CODE作为GROUP_CODE
                  '||V_SQL_PARENT||'
                  REGION_CODE,
                  REPOFFICE_CODE,
                  BG_CODE,
                  OVERSEA_FLAG,
                  VIEW_FLAG,
                  MAIN_FLAG,
                  CODE_ATTRIBUTES,
                  SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
                  SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
                  SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
               FROM '||V_TMP2_TABLE||' T1
               GROUP BY '
               ||V_PARTITION_DIM||'
               REGION_CODE,
               REPOFFICE_CODE,
               BG_CODE,
               OVERSEA_FLAG,
               VIEW_FLAG,
               MAIN_FLAG,
               CODE_ATTRIBUTES,
               T1.PERIOD_YEAR
            )
        SELECT '||V_VERSION_ID||' AS VERSION_ID,
               T1.PERIOD_YEAR,
               T1.'||V_LV_CODE||'
               T1.'||V_LV_NAME||'
               T1.DIMENSION_CODE,
               T1.DIMENSION_CN_NAME,
               T1.DIMENSION_SUBCATEGORY_CODE,
               T1.DIMENSION_SUBCATEGORY_CN_NAME,
               T1.DIMENSION_SUB_DETAIL_CODE,
               T1.DIMENSION_SUB_DETAIL_CN_NAME,
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
                    WHEN T2.STATUS_1 = 0 THEN 1
                    WHEN T2.STATUS_4 = 0 THEN 2
               ELSE 4 END AS STATUS_CODE,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
               T1.REGION_CODE,
               T1.REGION_CN_NAME,
               T1.REPOFFICE_CODE,
               T1.REPOFFICE_CN_NAME,
               T1.BG_CODE,
               T1.BG_CN_NAME,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.MAIN_FLAG,
               T1.CODE_ATTRIBUTES,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,  
               NULL AS APPEND_YEAR
            FROM '||V_FROM2_TABLE||' T1
            LEFT JOIN BASEUP_STATUS_TMP T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            AND NVL(T1.GROUP_CODE,''SNULL'') = NVL(T2.GROUP_CODE,''SNULL'')
            AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
            AND T1.VIEW_FLAG = T2.VIEW_FLAG
            AND T1.REGION_CODE = T2.REGION_CODE
            AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
            AND T1.BG_CODE = T2.BG_CODE
            AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
            AND T1.'||V_INTO_LV_CODE||' = T2.LV_CODE
            AND NVL(T1.CODE_ATTRIBUTES,''SNULL1'') = NVL(T2.CODE_ATTRIBUTES,''SNULL1'')
            AND NVL(T1.MAIN_FLAG,''SNULL0'') = NVL(T2.MAIN_FLAG,''SNULL0'')
            WHERE T1.GROUP_LEVEL = '''||V_GROUP_LEVEL||'''
            AND T1.VERSION_ID = '||V_VERSION_ID ;        
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL;        
        
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的其余层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');   
     END LOOP;
        
    -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

