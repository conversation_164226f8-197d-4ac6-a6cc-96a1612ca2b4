-- Name: f_dm_foc_annual_amp_t_d; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_annual_amp_t_d(f_lev_num bigint, f_dimension_type character varying, f_keystr character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-03-22
  创建人  ：唐钦
  背景描述：分视角年度涨跌幅表(年度分析-柱状图)
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_AMP_T()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_AMP_T_D'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT ; --版本号ID
  V_YEAR  BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
    
    -- 7月版本新增
    V_SQL                      TEXT;
    V_FROM_TABLE_1   VARCHAR2(500);
    V_FROM_TABLE_2   VARCHAR2(500);
    V_TO_TABLE   VARCHAR2(500);
    V_MID_TABLE   VARCHAR2(500);
    V_SEQUENCE   VARCHAR(500);
    V_PROD_RND_TEAM   VARCHAR2(500);
    V_LV1_PROD_TEAM VARCHAR2(500);
    V_LV2_PROD_TEAM VARCHAR2(500);
    V_IN_LV1_PROD_TEAM VARCHAR2(500);
    V_IN_LV2_PROD_TEAM VARCHAR2(500);
    V_LV3_PROD_RND_TEAM_CODE   VARCHAR2(500);
    V_LV3_PROD_RD_TEAM_CN_NAME   VARCHAR2(500);
    V_SQL_PROFITS_NAME   TEXT; -- 盈利层级归属逻辑
    V_IN_LV3_PROD_RND_TEAM   VARCHAR2(500);
    V_L1_L2   VARCHAR2(500);
    V_IN_L1_L2   VARCHAR2(500);
    V_SQL_CEG_PARENT   TEXT;
    V_SQL_CEGUP_PARENT   TEXT;
    V_PROFITS_NAME VARCHAR2(200);
    V_IN_PROFITS_NAME VARCHAR2(500);
    
    -- 9月版本新增
    V_PUR_LEVEL VARCHAR2(500);   -- 采购层级字段
    V_IN_PUR_LEVEL VARCHAR2(500);   -- 采购层级字段
    V_DMS_CODE VARCHAR2(500);
    V_DMS_NAME VARCHAR2(500);
    V_IN_DMS_CODE VARCHAR2(500);
    V_IN_DMS_NAME VARCHAR2(500);
    V_REL_PROD_RND_TEAM_CODE TEXT;   -- 重量级团队关联逻辑
    V_REL_DMS_CODE TEXT;   -- 量纲层级关联逻辑
    V_REL_PROFITS_NAME TEXT;   -- 盈利层级关联逻辑
    V_SQL_PROD_RND_TEAM_CODE TEXT;   -- 重量级团队CODE归属逻辑
    V_SQL_PROD_RD_TEAM_CN_NAME TEXT;   -- 重量级团队中文名称归属逻辑
    V_SQL_DMS_CODE TEXT;   -- 量纲层级编码归属逻辑
    V_SQL_DMS_NAME TEXT;   -- 量纲层级中文名称归属逻辑
    V_SQL_PARENT TEXT;   -- 父层级CODE逻辑
    V_GROUP_TOTAL TEXT;   -- GROUP层级的3个字段逻辑
    V_CHILD_LEVEL VARCHAR2(500);   -- 子类层级条件
    V_DMS_TOTAL VARCHAR2(500); 
    V_LEVEL_NUM BIGINT := F_LEV_NUM;   -- 循环时不同颗粒度对应的层级数值不同
    V_DMS_TOTAL_BAK VARCHAR(500);   -- 备份
    V_DMS_CODE_BAK VARCHAR(500);   -- 备份
    V_DMS_NAME_BAK VARCHAR(500);   -- 备份
    V_PROFITS_NAME_BAK VARCHAR(500);   -- 备份
    V_L1_L2_BAK VARCHAR(500);   -- 备份
    V_TMP_TABLE VARCHAR(500); 
    V_TAB_LV3_PROD VARCHAR(500); 
    V_TAB_DMS_CODE VARCHAR(500); 
    V_TAB_DMS_NAME VARCHAR(500); 
    V_TAB_L1_L2 VARCHAR(500); 
	V_REL_LV3_PROD_RND_TEAM_CODE VARCHAR(500); 
	V_GROUP_LEVEL VARCHAR(200);
    
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
     
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'DMS_DECRYPT_AVG_TMP';
            V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_GROUP_AMP_S.NEXTVAL AS ID,'; -- 序列
--            V_LEVEL_NUM := 10;
     ELSE
       RETURN '入参错误，不为：D';
     END IF;
   
    --版本号赋值
    V_SQL := 'SELECT VERSION_ID FROM '||V_FROM_TABLE_1||' T LIMIT 1 ';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;

  -- 重置变量入参
  -- 通用颗粒度公用变量
      V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
      V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
      V_PROD_RND_TEAM := '
                         PROD_RND_TEAM_CODE,
                         PROD_RD_TEAM_CN_NAME,';
      V_SQL_PROD_RND_TEAM_CODE := '
                     CASE T1.VIEW_FLAG WHEN ''0'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                       WHEN ''1'' THEN T1.LV1_PROD_RND_TEAM_CODE
                                       WHEN ''2'' THEN T1.LV2_PROD_RND_TEAM_CODE
                     ELSE T1.LV3_PROD_RND_TEAM_CODE
                     END AS PROD_RND_TEAM_CODE,';  
      V_SQL_PROD_RD_TEAM_CN_NAME := '
                    CASE T1.VIEW_FLAG WHEN ''0'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                      WHEN ''1'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                      WHEN ''2'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                    ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                    END AS PROD_RD_TEAM_CN_NAME,';
      V_TAB_LV3_PROD := ' LV3_PROD_RND_TEAM_CODE VARCHAR(50),
                          LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),';
      V_REL_LV3_PROD_RND_TEAM_CODE := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,''SNULL3'') = NVL(T2.LV3_PROD_RND_TEAM_CODE,''SNULL3'') ';
      V_IN_LV3_PROD_RND_TEAM := 'T1.LV3_PROD_RND_TEAM_CODE,
                                 T1.LV3_PROD_RD_TEAM_CN_NAME,';        
   
  -- 盈利颗粒度公用变量
      V_L1_L2 := 'L1_NAME, 
                  L2_NAME,';
      V_IN_L1_L2 := 'T1.L1_NAME, 
                     T1.L2_NAME,';
      V_PROFITS_NAME := ' PROFITS_NAME,'; 
      V_SQL_PROFITS_NAME := '
                            CASE T1.VIEW_FLAG WHEN ''3'' THEN T1.L1_NAME 
                                              WHEN ''4'' THEN T1.L2_NAME 
                                              ELSE NULL
                            END AS PROFITS_NAME,';
							
  -- 量纲颗粒度公用变量
      V_DMS_TOTAL := '
                      DMS_CODE,
                      DMS_CN_NAME,';
      V_SQL_DMS_CODE := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CODE 
                              WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CODE
                         ELSE T1.DIMENSION_SUB_DETAIL_CODE
                         END AS DMS_CODE,';
      V_SQL_DMS_NAME := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CN_NAME 
                              WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
                         ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
                         END AS DMS_CN_NAME,';

  --量纲颗粒度的维度时，不需要L1和L2字段
    IF F_DIMENSION_TYPE = 'D' THEN
         V_L1_L2 := '';
         V_IN_L1_L2 := '';
         V_PROFITS_NAME := '';
         V_SQL_PROFITS_NAME := '';
         V_REL_DMS_CODE := ' AND NVL(T1.DIMENSION_CODE,''SNULL1'') = NVL(T2.DIMENSION_CODE,''SNULL1'')
                             AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL2'')
                             AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL3'') ';  
         V_SQL_PROD_RND_TEAM_CODE := '                 
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'') THEN T1.LV2_PROD_RND_TEAM_CODE
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';    
         V_SQL_PROD_RD_TEAM_CN_NAME:='
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PROD_RD_TEAM_CN_NAME,';
         V_DMS_CODE := '
                        DIMENSION_CODE,
                        DIMENSION_SUBCATEGORY_CODE,
                        DIMENSION_SUB_DETAIL_CODE,';
         V_DMS_NAME:= '
                       DIMENSION_CN_NAME,
                       DIMENSION_SUBCATEGORY_CN_NAME,
                       DIMENSION_SUB_DETAIL_CN_NAME,';
         V_IN_DMS_CODE := '
                        T1.DIMENSION_CODE,
                        T1.DIMENSION_SUBCATEGORY_CODE,
                        T1.DIMENSION_SUB_DETAIL_CODE,';
         V_IN_DMS_NAME:= '
                       T1.DIMENSION_CN_NAME,
                       T1.DIMENSION_SUBCATEGORY_CN_NAME,
                       T1.DIMENSION_SUB_DETAIL_CN_NAME,';
         V_TAB_DMS_CODE := '
                        DIMENSION_CODE VARCHAR(500),
                        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
                        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),';
         V_TAB_DMS_NAME:= '
                       DIMENSION_CN_NAME VARCHAR(2000),
                       DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
                       DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),';
    END IF;
     
  --重置公用变量入参
    V_LV1_PROD_TEAM := 'LV1_PROD_RND_TEAM_CODE,
                        LV1_PROD_RD_TEAM_CN_NAME,
                       ';
    V_LV2_PROD_TEAM := 'LV2_PROD_RND_TEAM_CODE,
                        LV2_PROD_RD_TEAM_CN_NAME,
                       ';
    V_IN_LV1_PROD_TEAM := 'T1.LV1_PROD_RND_TEAM_CODE,
                           T1.LV1_PROD_RD_TEAM_CN_NAME,
                       ';
    V_IN_LV2_PROD_TEAM := 'T1.LV2_PROD_RND_TEAM_CODE,
                           T1.LV2_PROD_RD_TEAM_CN_NAME,
                       ';
    V_REL_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE ';

  --量纲颗粒度的维度时，不需要盈利字段
      IF F_DIMENSION_TYPE = 'D' THEN
             V_REL_PROFITS_NAME := '';
             V_DMS_TOTAL_BAK := V_DMS_TOTAL;
             V_DMS_CODE_BAK := V_DMS_CODE;
             V_DMS_NAME_BAK := V_DMS_NAME;
             V_SQL_CEG_PARENT := ' T1.DMS_CODE AS PARENT_CODE, ';
             V_SQL_CEGUP_PARENT := '
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CODE
                            ELSE T1.DIMENSION_SUBCATEGORY_CODE
                            END AS PARENT_CODE,';
        END IF;                                            
    
  -- 对于不同颗粒度、不同视角的不同层级进行循环计算
  FOR LEVEL_NUM IN V_LEVEL_NUM .. V_LEVEL_NUM LOOP
  -- 重置公用变量
    V_PUR_LEVEL := '';
    V_IN_PUR_LEVEL := '';
    
  -- 品类层级涨跌幅计算
  IF LEVEL_NUM = 1 THEN 
      V_PUR_LEVEL := '
                     L3_CEG_CODE,
                     L3_CEG_SHORT_CN_NAME,
                     L4_CEG_CODE,
                     L4_CEG_SHORT_CN_NAME,
                     CATEGORY_CODE,
                     CATEGORY_CN_NAME,';
      V_IN_PUR_LEVEL := '
                     T1.L3_CEG_CODE,
                     T1.L3_CEG_SHORT_CN_NAME,
                     T1.L4_CEG_CODE,
                     T1.L4_CEG_SHORT_CN_NAME,
                     T1.CATEGORY_CODE,
                     T1.CATEGORY_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.CATEGORY_CODE AS GROUP_CODE,
                        T1.CATEGORY_CN_NAME AS GROUP_CN_NAME,
                        ''CATEGORY'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''ITEM''';
      V_GROUP_LEVEL := '''CATEGORY''';
      V_SQL_PARENT := ' T1.L4_CEG_CODE AS PARENT_CODE,';
  -- 模块层级涨跌幅计算
  ELSIF LEVEL_NUM = 2 THEN 
      V_PUR_LEVEL := '
                     L3_CEG_CODE,
                     L3_CEG_SHORT_CN_NAME,
                     L4_CEG_CODE,
                     L4_CEG_SHORT_CN_NAME,';
      V_IN_PUR_LEVEL := '
                     T1.L3_CEG_CODE,
                     T1.L3_CEG_SHORT_CN_NAME,
                     T1.L4_CEG_CODE,
                     T1.L4_CEG_SHORT_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.L4_CEG_CODE AS GROUP_CODE,
                        T1.L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                        ''MODL'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''CATEGORY''';
      V_GROUP_LEVEL := '''MODL''';
      V_SQL_PARENT := ' T1.L3_CEG_CODE AS PARENT_CODE,';
  -- 专家团层级涨跌幅计算
  ELSIF LEVEL_NUM = 3 THEN 
      V_PUR_LEVEL := '
                     L3_CEG_CODE,
                     L3_CEG_SHORT_CN_NAME,';
      V_IN_PUR_LEVEL := '
                     T1.L3_CEG_CODE,
                     T1.L3_CEG_SHORT_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.L3_CEG_CODE AS GROUP_CODE,
                        T1.L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                        ''CEG'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''MODL''';
      V_GROUP_LEVEL := '''CEG''';  
      V_SQL_PARENT := V_SQL_CEG_PARENT;   -- 专家团层级的父层级维度不一致
  -- 不同颗粒度，不同视角，专家团层级依据不同视角对应的上一层级往上卷积至不同数据层级
  ELSIF LEVEL_NUM = 4 THEN 
      V_CHILD_LEVEL := '''CEG''';  
      V_SQL_PARENT := V_SQL_CEGUP_PARENT;
    IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_GROUP_TOTAL := 'T1.DMS_CODE AS GROUP_CODE,
                        T1.DMS_CN_NAME AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN ''DIMENSION''
                             WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN ''SUBCATEGORY''
                             ELSE ''SUB_DETAIL''
                        END AS GROUP_LEVEL,'; --不同视角层级值不同，需定义
     V_GROUP_LEVEL := '''SUB_DETAIL''';                   
    END IF;
  -- 通用：LV2层级/盈利：L1层级/量纲：SUBCATEGORY层级
  ELSIF LEVEL_NUM = 5 THEN 
    IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''SUB_DETAIL''';  
      V_GROUP_LEVEL := '''SUBCATEGORY'''; 
      V_DMS_CODE := 'DIMENSION_CODE,
                     DIMENSION_SUBCATEGORY_CODE,';
      V_DMS_NAME := 'DIMENSION_CN_NAME,
                     DIMENSION_SUBCATEGORY_CN_NAME,';
      V_IN_DMS_CODE := 'T1.DIMENSION_CODE,
                        T1.DIMENSION_SUBCATEGORY_CODE,';
      V_IN_DMS_NAME := 'T1.DIMENSION_CN_NAME,
                        T1.DIMENSION_SUBCATEGORY_CN_NAME,';
      V_SQL_PARENT := 'T1.DIMENSION_CODE AS PARENT_CODE,';
      V_GROUP_TOTAL := 'T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
                        T1.DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
                        ''SUBCATEGORY'' AS GROUP_LEVEL,
                        ';  
      V_SQL_DMS_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE AS DMS_CODE,';  
      V_SQL_DMS_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME AS DMS_CN_NAME,';    
    END IF;
  -- 通用：LV1层级/盈利：LV2层级/量纲：DIMENSION层级
  ELSIF LEVEL_NUM = 6 THEN 
    IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''SUBCATEGORY'''; 
      V_GROUP_LEVEL := '''DIMENSION''';  
      V_DMS_CODE := 'DIMENSION_CODE,';
      V_DMS_NAME := 'DIMENSION_CN_NAME,';
      V_IN_DMS_CODE := 'T1.DIMENSION_CODE,';
      V_IN_DMS_NAME := 'T1.DIMENSION_CN_NAME,';
      V_SQL_PARENT := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'') THEN T1.LV1_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'') THEN T1.LV2_PROD_RND_TEAM_CODE
                       ELSE T1.LV3_PROD_RND_TEAM_CODE
                       END AS PARENT_CODE,';   -- 量纲层级的父层级CODE
      V_GROUP_TOTAL := 'T1.DIMENSION_CODE AS GROUP_CODE,
                        T1.DIMENSION_CN_NAME AS GROUP_CN_NAME,
                        ''DIMENSION'' AS GROUP_LEVEL,
                        ';  
      V_SQL_DMS_CODE := 'T1.DIMENSION_CODE AS DMS_CODE,';  
      V_SQL_DMS_NAME := 'T1.DIMENSION_CN_NAME AS DMS_CN_NAME,';      
    END IF;
  -- 通用：ICT层级/盈利：LV1层级/量纲：不同视角对应不同的重量级团队层级
  ELSIF LEVEL_NUM = 7 THEN 
    IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''DIMENSION''';  
      V_GROUP_LEVEL := '''LV3'''; 
      V_DMS_CODE := '';
      V_DMS_NAME := '';
      V_IN_DMS_CODE := '';
      V_IN_DMS_NAME := '';
      V_SQL_DMS_CODE := '';  
      V_SQL_DMS_NAME := '';        
      V_DMS_TOTAL := '';
      V_SQL_PARENT := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'') THEN T1.LV0_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'') THEN T1.LV1_PROD_RND_TEAM_CODE
                            ELSE T1.LV2_PROD_RND_TEAM_CODE
                       END AS PARENT_CODE,';
      V_GROUP_TOTAL := 'PROD_RND_TEAM_CODE AS GROUP_CODE,
                        PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'') THEN ''LV1''
                             WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'') THEN ''LV2''
                             ELSE ''LV3''
                        END AS GROUP_LEVEL,';  
    END IF;
  -- 盈利：ICT层级/量纲：LV2层级
  ELSIF LEVEL_NUM = 8 THEN 
    IF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''LV3''';  
      V_GROUP_LEVEL := '''LV2'''; 
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM := '';   -- LV3重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV2_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV2'' AS GROUP_LEVEL,
                        ';  
    END IF;
  -- 量纲：LV1层级
  ELSIF LEVEL_NUM = 9 THEN   
      V_CHILD_LEVEL := '''LV2''';  
      V_GROUP_LEVEL := '''LV1'''; 
      V_LV2_PROD_TEAM := '';
      V_IN_LV2_PROD_TEAM := '';   -- LV2重量级团队层级置空  
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV1'' AS GROUP_LEVEL,
                        ';    
  -- 量纲：ICT层级
  ELSIF LEVEL_NUM = 10 THEN   
      V_CHILD_LEVEL := '''LV1'''; 
      V_GROUP_LEVEL := '''ICT''';  
      V_LV1_PROD_TEAM := '';
      V_IN_LV1_PROD_TEAM := '';   -- LV1重量级团队层级置空  
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''ICT'' AS GROUP_LEVEL,
                        ';    
  END IF;
  
  -- 年度涨跌幅主逻辑SQL
  V_SQL := '
     INSERT INTO '||V_MID_TABLE||'(
--                  ID,
                  VERSION_ID,
                  PERIOD_YEAR,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  '||V_LV1_PROD_TEAM
                  ||V_LV2_PROD_TEAM
                  ||V_LV3_PROD_RND_TEAM_CODE
                  ||V_LV3_PROD_RD_TEAM_CN_NAME
                  ||V_DMS_CODE
                  ||V_DMS_NAME
                  ||V_L1_L2
                  ||V_PROD_RND_TEAM
                  ||V_DMS_TOTAL
                  ||V_PROFITS_NAME
                  ||V_PUR_LEVEL||'
                  GROUP_CODE,
                  GROUP_CN_NAME,
                  GROUP_LEVEL,
                  ANNUAL_AMP,
                  PARENT_CODE,
                  CREATED_BY,
                  CREATION_DATE,
                  LAST_UPDATED_BY,
                  LAST_UPDATE_DATE,
                  DEL_FLAG,
                  VIEW_FLAG,
                  CALIBER_FLAG,
                  OVERSEA_FLAG,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME
                 )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_PROD_RND_TEAM_CODE,
                T1.LV0_PROD_RD_TEAM_CN_NAME,
                '||V_IN_LV1_PROD_TEAM
                ||V_IN_LV2_PROD_TEAM
                ||V_IN_LV3_PROD_RND_TEAM
                ||V_IN_DMS_CODE
                ||V_IN_DMS_NAME
                ||V_IN_L1_L2
                ||V_SQL_PROD_RND_TEAM_CODE
                ||V_SQL_PROD_RD_TEAM_CN_NAME
                ||V_SQL_DMS_CODE
                ||V_SQL_DMS_NAME
                ||V_SQL_PROFITS_NAME
                ||V_IN_PUR_LEVEL
                ||'T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                T1.PARENT_CODE,
                T1.VIEW_FLAG,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME
             FROM '||V_MID_TABLE||' T1
             INNER JOIN '||V_FROM_TABLE_2||' T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             '||V_REL_PROD_RND_TEAM_CODE
             ||V_REL_DMS_CODE
             ||V_REL_PROFITS_NAME
             ||' AND T1.GROUP_CODE =T2.GROUP_CODE
             AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
             AND T1.VIEW_FLAG = T2.VIEW_FLAG
             AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
             AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
             AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             WHERE T1.VERSION_ID = '||V_VERSION_ID||'
             AND T1.GROUP_LEVEL = '||V_CHILD_LEVEL||'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT '
--                ||V_SEQUENCE
                ||V_VERSION_ID||' AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_PROD_RND_TEAM_CODE,
                T1.LV0_PROD_RD_TEAM_CN_NAME,
                '||V_IN_LV1_PROD_TEAM
                ||V_IN_LV2_PROD_TEAM
                ||V_IN_LV3_PROD_RND_TEAM
                ||V_IN_DMS_CODE
                ||V_IN_DMS_NAME
                ||V_L1_L2
                ||V_PROD_RND_TEAM
                ||V_DMS_TOTAL
                ||V_PROFITS_NAME
                ||V_PUR_LEVEL
                ||V_GROUP_TOTAL||'
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                '||V_SQL_PARENT||'
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                ''N'' AS DEL_FLAG,
                T1.VIEW_FLAG,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_PROD_RND_TEAM_CODE,
                      T1.LV0_PROD_RD_TEAM_CN_NAME,
                      '||V_IN_LV3_PROD_RND_TEAM
                      ||V_IN_L1_L2
                      ||V_IN_LV1_PROD_TEAM
                      ||V_IN_LV2_PROD_TEAM
                      ||V_IN_DMS_CODE
                      ||V_IN_DMS_NAME
                      ||V_L1_L2
                      ||V_PROD_RND_TEAM
                      ||V_DMS_TOTAL
                      ||V_PROFITS_NAME
                      ||V_PUR_LEVEL
                      ||'T1.VIEW_FLAG,
                      T1.CALIBER_FLAG,
                      T1.OVERSEA_FLAG,
                      T1.LV0_PROD_LIST_CODE,
                      T1.LV0_PROD_LIST_CN_NAME'
                      ;
                                             
          EXECUTE IMMEDIATE V_SQL; 
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       DBMS_OUTPUT.PUT_LINE('第'||LEVEL_NUM||'次循环，不同层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的第'||LEVEL_NUM||'次循环，颗粒度为：'||F_DIMENSION_TYPE||' 的数据到'||V_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                                                 
   END LOOP;   -- 结束循环
    
 -- 插入所有数据到分视角年度涨跌幅表
 V_SQL := '
    INSERT INTO '||V_TO_TABLE||'(
                ID,
                VERSION_ID,
                PERIOD_YEAR,
                PROD_RND_TEAM_CODE,
                PROD_RD_TEAM_CN_NAME,
                '||V_DMS_TOTAL_BAK
                ||V_DMS_CODE_BAK
                ||V_DMS_NAME_BAK
                ||V_PROFITS_NAME_BAK
                ||V_L1_L2_BAK
                ||'GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                ANNUAL_AMP,
                PARENT_CODE,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
               )

 -- 临时表中全量的数据，并得到对应的状态字段值
        SELECT T1.ID,
               T1.VERSION_ID,
               T1.PERIOD_YEAR,
               T1.PROD_RND_TEAM_CODE,
               T1.PROD_RD_TEAM_CN_NAME,
               '||V_DMS_TOTAL_BAK
               ||V_DMS_CODE_BAK
               ||V_DMS_NAME_BAK
               ||V_PROFITS_NAME_BAK
               ||V_L1_L2_BAK
               ||'T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               T1.ANNUAL_AMP,
               T1.PARENT_CODE,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.OVERSEA_FLAG,
               T1.LV0_PROD_LIST_CODE,
               T1.LV0_PROD_LIST_CN_NAME
           FROM '||V_MID_TABLE||' T1
           WHERE T1.VERSION_ID = '||V_VERSION_ID||'
           AND T1.GROUP_LEVEL = '||V_GROUP_LEVEL;
                         
        EXECUTE IMMEDIATE V_SQL;    
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       DBMS_OUTPUT.PUT_LINE('把层级为：'||V_GROUP_LEVEL||' 的临时表数据插入结果表'); 
                                  
--9.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||'，且层级为：'||V_GROUP_LEVEL||' 的数据到分视角年度涨跌幅表（'||V_TO_TABLE||'）',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');            
   
 -- 10.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_MID_TABLE;
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  DBMS_OUTPUT.PUT_LINE('收集统计信息成功'); 
  
  --11.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_MID_TABLE||'/'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

