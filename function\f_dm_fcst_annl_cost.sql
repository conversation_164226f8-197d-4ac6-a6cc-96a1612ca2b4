-- Name: f_dm_fcst_annl_cost; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_annl_cost(f_cost_type character varying, f_granularity_type character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年8月1日
  创建人  ：唐钦
  背景描述：根据MID_MON_SPART表，包含脏数据一起，计算出成本分布图数据
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ANNL_COST('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ANNL_COST'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_TMP1_TABLE VARCHAR(100);
  V_BEGIN_NUM INT;
  V_END_NUM INT;
  V_VIEW_FLAG VARCHAR(50);
  V_LV_CODE VARCHAR(50);
  V_LV_NAME VARCHAR(50);
  V_LV0_CODE VARCHAR(50);
  V_LV0_NAME VARCHAR(50);
  V_LV1_CODE VARCHAR(50);
  V_LV1_NAME VARCHAR(50);
  V_LV2_CODE VARCHAR(50);
  V_LV2_NAME VARCHAR(50);
  V_LV3_CODE VARCHAR(50);
  V_LV3_NAME VARCHAR(50);
  V_LV4_CODE VARCHAR(50);
  V_LV4_NAME VARCHAR(50);
  V_IN_LV_CODE VARCHAR(50);
  V_IN_LV_NAME VARCHAR(50);
  V_DIMENSION_CODE VARCHAR(50);
  V_DIMENSION_NAME VARCHAR(50);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(50);
  V_DIMENSION_SUBCATEGORY_NAME VARCHAR(50);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(50);
  V_DIMENSION_SUB_DETAIL_NAME VARCHAR(50);
  V_GROUP_CODE VARCHAR(100);
  V_GROUP_NAME VARCHAR(100);
  V_GROUP_LEVEL VARCHAR(100);
  V_PARENT_CODE VARCHAR(100);
  V_PARENT_NAME VARCHAR(100);
  V_SQL_DE_AMT VARCHAR(100);
  V_PARENT_AMT VARCHAR(500);
  V_SQL_CONDITION VARCHAR(100);
  V_CUS_TO_TABLE VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 根据入参，对变量进行不同定义
     V_DIMENSION_CODE := 'DIMENSION_CODE,';
     V_DIMENSION_NAME := 'DIMENSION_CN_NAME,';
     V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
     V_DIMENSION_SUBCATEGORY_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
     V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
     V_DIMENSION_SUB_DETAIL_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DECODE_ENTIRE_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_COST_T';
	 V_CUS_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL_COST_T';
     V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DE_AMT_TMP';
     V_TMP1_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_COST_AMT_TMP';
  -- 判断成本类型，PSP成本不需要加解密，标准成本类型需要加解密
  IF F_COST_TYPE = 'PSP' THEN   -- PSP成本类型
     V_SQL_DE_AMT := 'RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_DE_AMT := 'GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,';   -- 解密金额
  END IF;
  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV_CODE := 'PROD_RND_TEAM_CODE,';
     V_LV_NAME := 'PROD_RD_TEAM_CN_NAME,';
     V_LV0_CODE := 'LV0_PROD_RND_TEAM_CODE,';
     V_LV0_NAME := 'LV0_PROD_RD_TEAM_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_RND_TEAM_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_RND_TEAM_CODE,';
     V_LV2_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_RND_TEAM_CODE,';
     V_LV3_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE,';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV_CODE := 'INDUSTRY_CATG_CODE,'; 
     V_LV_NAME := 'INDUSTRY_CATG_CN_NAME,';
     V_LV0_CODE := 'LV0_INDUSTRY_CATG_CODE,'; 
     V_LV0_NAME := 'LV0_INDUSTRY_CATG_CN_NAME,';
     V_LV1_CODE := 'LV1_INDUSTRY_CATG_CODE,'; 
     V_LV1_NAME := 'LV1_INDUSTRY_CATG_CN_NAME,';
     V_LV2_CODE := 'LV2_INDUSTRY_CATG_CODE,';
     V_LV2_NAME := 'LV2_INDUSTRY_CATG_CN_NAME,';
     V_LV3_CODE := 'LV3_INDUSTRY_CATG_CODE,';
     V_LV3_NAME := 'LV3_INDUSTRY_CATG_CN_NAME,';
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE,';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME,';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV_CODE := 'PROD_LIST_CODE,'; 
     V_LV_NAME := 'PROD_LIST_CN_NAME,';
     V_LV0_CODE := 'LV0_PROD_LIST_CODE,'; 
     V_LV0_NAME := 'LV0_PROD_LIST_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_LIST_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_LIST_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_LIST_CODE,';
     V_LV2_NAME := 'LV2_PROD_LIST_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_LIST_CODE,';
     V_LV3_NAME := 'LV3_PROD_LIST_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_LIST_CODE,';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME,';
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空维表数据
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 创建临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
         PERIOD_YEAR                   INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         RMB_COST_AMT                  NUMERIC,
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP_TABLE||'表成功');

     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP1_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP1_TABLE||' (
         PERIOD_YEAR                   INT,
         LV_CODE                      VARCHAR(50),
         LV_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         GROUP_CODE                    VARCHAR(50),
         GROUP_CN_NAME                 VARCHAR(200),
         GROUP_LEVEL                   VARCHAR(50),
         PARENT_CODE                   VARCHAR(50),
         PARENT_CN_NAME                VARCHAR(200),
         RMB_COST_AMT                  NUMERIC,
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP1_TABLE||'表成功');
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '解密临时表：'||V_TMP_TABLE||'，'||V_TMP1_TABLE||'，创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  IF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_CONDITION := 'AND ONLY_SPART_FLAG = ''N''';    -- 取LV3.5下非单SPART的数据进行计算
  ELSIF F_COST_TYPE = 'PSP' THEN   -- PSP成本类型
     V_SQL_CONDITION := '';
  END IF;
   
  -- 对数据进行加密或直接保存到临时表
     V_SQL := '
     INSERT INTO '||V_TMP_TABLE||'(
            PERIOD_YEAR,                  
            LV0_CODE,                     
            LV0_CN_NAME,                  
            LV1_CODE,                     
            LV1_CN_NAME,                  
            LV2_CODE,                     
            LV2_CN_NAME,                  
            LV3_CODE,                     
            LV3_CN_NAME,                  
            LV4_CODE,                     
            LV4_CN_NAME,                  
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,                
            MAIN_FLAG,                    
            CODE_ATTRIBUTES,              
            RMB_COST_AMT,                 
            VIEW_FLAG,                    
            REGION_CODE,                  
            REGION_CN_NAME,               
            REPOFFICE_CODE,               
            REPOFFICE_CN_NAME,            
            BG_CODE,                      
            BG_CN_NAME,                   
            OVERSEA_FLAG
     )
     SELECT PERIOD_YEAR,                  
            '||V_LV0_CODE||'
            '||V_LV0_NAME||'
            '||V_LV1_CODE||'
            '||V_LV1_NAME||'
            '||V_LV2_CODE||'
            '||V_LV2_NAME||'
            '||V_LV3_CODE||'
            '||V_LV3_NAME||'
            '||V_LV4_CODE||'
            '||V_LV4_NAME||'
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,                
            MAIN_FLAG,                    
            CODE_ATTRIBUTES,              
            '||V_SQL_DE_AMT||'
            VIEW_FLAG,                    
            REGION_CODE,                  
            REGION_CN_NAME,               
            REPOFFICE_CODE,               
            REPOFFICE_CN_NAME,            
            BG_CODE,                      
            BG_CN_NAME,                   
            OVERSEA_FLAG
         FROM '||V_FROM_TABLE||'
		 WHERE VERSION_ID = '||V_VERSION_ID||'
		 '||V_SQL_CONDITION;   -- 取LV3.5下非单SPART的数据进行计算
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('将数据放到临时表：'||V_TMP_TABLE);
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将数据放到临时表：'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 补主力编码-全选的数据、缺失部分全量数据
  V_SQL := '
     INSERT INTO '||V_TMP_TABLE||'(
            PERIOD_YEAR,                  
            LV0_CODE,                     
            LV0_CN_NAME,                  
            LV1_CODE,                     
            LV1_CN_NAME,                  
            LV2_CODE,                     
            LV2_CN_NAME,                  
            LV3_CODE,                     
            LV3_CN_NAME,                  
            LV4_CODE,                     
            LV4_CN_NAME,                  
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,                
            MAIN_FLAG,                    
            CODE_ATTRIBUTES,              
            RMB_COST_AMT,                 
            VIEW_FLAG,                    
            REGION_CODE,                  
            REGION_CN_NAME,               
            REPOFFICE_CODE,               
            REPOFFICE_CN_NAME,            
            BG_CODE,                      
            BG_CN_NAME,                   
            OVERSEA_FLAG
     )
     -- 基础数据：将主力编码标识与全量数据重复的数据，全都赋值为：N，编码属性都赋值为空
     SELECT PERIOD_YEAR,                  
            LV0_CODE,                     
            LV0_CN_NAME,                  
            LV1_CODE,                     
            LV1_CN_NAME,                  
            LV2_CODE,                     
            LV2_CN_NAME,                  
            LV3_CODE,                     
            LV3_CN_NAME,                  
            LV4_CODE,                     
            LV4_CN_NAME,                  
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,
            ''N'' AS MAIN_FLAG,                     
            NULL AS CODE_ATTRIBUTES,
            RMB_COST_AMT,                  
            VIEW_FLAG,                     
            REGION_CODE,                   
            REGION_CN_NAME,                
            REPOFFICE_CODE,                
            REPOFFICE_CN_NAME,             
            BG_CODE,                       
            BG_CN_NAME,                    
            OVERSEA_FLAG
         FROM '||V_TMP_TABLE||'
         WHERE VIEW_FLAG = ''PROD_SPART''   -- 路径1才有主力编码数据
         AND MAIN_FLAG = ''Y''   -- 取标识为主力编码的数据，添加为全量数据
     UNION ALL
     -- 主力编码全选数据，编码属性置为：全选
     SELECT PERIOD_YEAR,                  
            LV0_CODE,                     
            LV0_CN_NAME,                  
            LV1_CODE,                     
            LV1_CN_NAME,                  
            LV2_CODE,                     
            LV2_CN_NAME,                  
            LV3_CODE,                     
            LV3_CN_NAME,                  
            LV4_CODE,                     
            LV4_CN_NAME,                  
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,
            MAIN_FLAG,                     
            ''全选'' AS CODE_ATTRIBUTES,
            RMB_COST_AMT,                  
            VIEW_FLAG,                     
            REGION_CODE,                   
            REGION_CN_NAME,                
            REPOFFICE_CODE,                
            REPOFFICE_CN_NAME,             
            BG_CODE,                       
            BG_CN_NAME,                    
            OVERSEA_FLAG
         FROM '||V_TMP_TABLE||'
         WHERE VIEW_FLAG = ''PROD_SPART''   -- 路径1才有主力编码数据
         AND MAIN_FLAG = ''Y''';    -- 主力标识为：Y时，包含主力编码维表数据，都置为全选数据
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('将已补主力编码：全选的数据、缺失部分全量数据放到临时表：'||V_TMP_TABLE);
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将已补主力编码：全选的数据、缺失部分全量数据放到临时表：'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  FOR GRO_NUM IN 0 .. 8 LOOP
  -- 判断在不同层级计算时，对对应的字段进行置空
  IF GRO_NUM = 0 THEN    -- 路径2：子类明细
     V_VIEW_FLAG := '''DIMENSION''';
     V_GROUP_CODE := 'DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''SUB_DETAIL'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';
     V_IN_LV_CODE := 'LV4_CODE AS LV_CODE,';
     V_IN_LV_NAME := 'LV4_CN_NAME AS LV_CN_NAME,';
  ELSIF GRO_NUM = 1 THEN    -- 路径2：量纲子类
     V_GROUP_CODE := 'DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''SUBCATEGORY'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'DIMENSION_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'DIMENSION_CN_NAME AS PARENT_CN_NAME,';
     V_DIMENSION_SUB_DETAIL_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,';
     V_DIMENSION_SUB_DETAIL_NAME := 'NULL AS DIMENSION_SUB_DETAIL_CN_NAME,';
  ELSIF GRO_NUM = 2 THEN    -- 路径2：量纲
     V_GROUP_CODE := 'DIMENSION_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'DIMENSION_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''DIMENSION'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'LV4_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'LV4_CN_NAME AS PARENT_CN_NAME,';
     V_DIMENSION_SUBCATEGORY_CODE := 'NULL AS DIMENSION_SUBCATEGORY_CODE,';
     V_DIMENSION_SUBCATEGORY_NAME := 'NULL AS DIMENSION_SUBCATEGORY_CN_NAME,';
  ELSIF GRO_NUM = 3 THEN    -- 路径1：SPART
     V_VIEW_FLAG := '''PROD_SPART''';
     V_GROUP_CODE := 'SPART_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'SPART_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''SPART'' AS GROUP_LEVEL,';
     V_DIMENSION_CODE := 'NULL AS DIMENSION_CODE,';
     V_DIMENSION_NAME := 'NULL AS DIMENSION_CN_NAME,';
  ELSIF GRO_NUM = 4 THEN    -- 路径1：LV3.5
     V_GROUP_CODE := 'LV4_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'LV4_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''LV4'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'LV3_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'LV3_CN_NAME AS PARENT_CN_NAME,';
  ELSIF GRO_NUM = 5 THEN    -- 路径1：LV3
     V_GROUP_CODE := 'LV3_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'LV3_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''LV3'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'LV2_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'LV2_CN_NAME AS PARENT_CN_NAME,';
     V_IN_LV_CODE := 'LV3_CODE AS LV_CODE,';
     V_IN_LV_NAME := 'LV3_CN_NAME AS LV_CN_NAME,';
  ELSIF GRO_NUM = 6 THEN    -- 路径1：LV2
     V_GROUP_CODE := 'LV2_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'LV2_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''LV2'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'LV1_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'LV1_CN_NAME AS PARENT_CN_NAME,';
     V_IN_LV_CODE := 'LV2_CODE AS LV_CODE,';
     V_IN_LV_NAME := 'LV2_CN_NAME AS LV_CN_NAME,';
  ELSIF GRO_NUM = 7 THEN    -- 路径1：LV1
     V_GROUP_CODE := 'LV1_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'LV1_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''LV1'' AS GROUP_LEVEL,';
     V_PARENT_CODE := 'LV0_CODE AS PARENT_CODE,';
     V_PARENT_NAME := 'LV0_CN_NAME AS PARENT_CN_NAME,';
     V_IN_LV_CODE := 'LV1_CODE AS LV_CODE,';
     V_IN_LV_NAME := 'LV1_CN_NAME AS LV_CN_NAME,';
  ELSIF GRO_NUM = 8 THEN    -- 路径1：LV0
     V_GROUP_CODE := 'LV0_CODE AS GROUP_CODE,';
     V_GROUP_NAME := 'LV0_CN_NAME AS GROUP_CN_NAME,';
     V_GROUP_LEVEL := '''LV0'' AS GROUP_LEVEL,';
     V_IN_LV_CODE := 'LV0_CODE AS LV_CODE,';
     V_IN_LV_NAME := 'LV0_CN_NAME AS LV_CN_NAME,';
  END IF;
   
  -- 基础数据+主力编码维表数据+主力编码全选数据按层级进行卷积计算
     V_SQL := '
     INSERT INTO '||V_TMP1_TABLE||'(
            PERIOD_YEAR,                   
            LV_CODE,                       
            LV_CN_NAME,                    
            DIMENSION_CODE,                
            DIMENSION_CN_NAME,             
            DIMENSION_SUBCATEGORY_CODE,    
            DIMENSION_SUBCATEGORY_CN_NAME, 
            DIMENSION_SUB_DETAIL_CODE,     
            DIMENSION_SUB_DETAIL_CN_NAME,  
            GROUP_CODE,                    
            GROUP_CN_NAME,                 
            GROUP_LEVEL,                   
            PARENT_CODE,
            PARENT_CN_NAME,
            RMB_COST_AMT,                  
            VIEW_FLAG,                     
            REGION_CODE,                   
            REGION_CN_NAME,                
            REPOFFICE_CODE,                
            REPOFFICE_CN_NAME,             
            BG_CODE,                       
            BG_CN_NAME,                    
            OVERSEA_FLAG,                  
            MAIN_FLAG,                     
            CODE_ATTRIBUTES
     )
     WITH BASE_DATE_TMP AS (
     -- 取出已补全主力编码/全量的基础数据
     SELECT PERIOD_YEAR,                   
            '||V_IN_LV_CODE||'                       
            '||V_IN_LV_NAME||'                    
            '||V_DIMENSION_CODE||'
            '||V_DIMENSION_NAME||'
            '||V_DIMENSION_SUBCATEGORY_CODE||'
            '||V_DIMENSION_SUBCATEGORY_NAME||'
            '||V_DIMENSION_SUB_DETAIL_CODE||'
            '||V_DIMENSION_SUB_DETAIL_NAME||'
            '||V_GROUP_CODE||'
            '||V_GROUP_NAME||'
            '||V_GROUP_LEVEL||'
            '||V_PARENT_CODE||'
            '||V_PARENT_NAME||'
            RMB_COST_AMT,                  
            VIEW_FLAG,                     
            REGION_CODE,                   
            REGION_CN_NAME,                
            REPOFFICE_CODE,                
            REPOFFICE_CN_NAME,             
            BG_CODE,                       
            BG_CN_NAME,                    
            OVERSEA_FLAG,                  
            MAIN_FLAG,                     
            CODE_ATTRIBUTES
         FROM '||V_TMP_TABLE||'
         WHERE VIEW_FLAG = '||V_VIEW_FLAG||'
    )
     SELECT PERIOD_YEAR,                   
            LV_CODE,                       
            LV_CN_NAME,                    
            DIMENSION_CODE,                
            DIMENSION_CN_NAME,             
            DIMENSION_SUBCATEGORY_CODE,    
            DIMENSION_SUBCATEGORY_CN_NAME, 
            DIMENSION_SUB_DETAIL_CODE,     
            DIMENSION_SUB_DETAIL_CN_NAME,  
            GROUP_CODE,                    
            GROUP_CN_NAME,                 
            GROUP_LEVEL,                   
            PARENT_CODE,
            PARENT_CN_NAME,
            SUM(RMB_COST_AMT) AS RMB_COST_AMT, 
            VIEW_FLAG,                     
            REGION_CODE,                   
            REGION_CN_NAME,                
            REPOFFICE_CODE,                
            REPOFFICE_CN_NAME,             
            BG_CODE,                       
            BG_CN_NAME,                    
            OVERSEA_FLAG,                  
            MAIN_FLAG,                     
            CODE_ATTRIBUTES
         FROM BASE_DATE_TMP 
         GROUP BY PERIOD_YEAR,                   
                  LV_CODE,                       
                  LV_CN_NAME,                    
                  DIMENSION_CODE,                
                  DIMENSION_CN_NAME,             
                  DIMENSION_SUBCATEGORY_CODE,    
                  DIMENSION_SUBCATEGORY_CN_NAME, 
                  DIMENSION_SUB_DETAIL_CODE,     
                  DIMENSION_SUB_DETAIL_CN_NAME,  
                  GROUP_CODE,                    
                  GROUP_CN_NAME,                 
                  GROUP_LEVEL,
                  PARENT_CODE,
                  PARENT_CN_NAME,
                  VIEW_FLAG,                     
                  REGION_CODE,                   
                  REGION_CN_NAME,                
                  REPOFFICE_CODE,                
                  REPOFFICE_CN_NAME,             
                  BG_CODE,                       
                  BG_CN_NAME,                    
                  OVERSEA_FLAG,                  
                  MAIN_FLAG,                     
                  CODE_ATTRIBUTES';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('将数据进行处理后，按层级卷积并放到临时表：'||V_TMP1_TABLE);
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||GRO_NUM||'将数据进行处理后，按层级卷积并放到临时表：'||V_TMP1_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   END LOOP;   -- 结束循环
                                
  -- 插入各视角、各层级的金额数据到成本分布图表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         '||V_LV_CODE||'
         '||V_LV_NAME||'
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         APPEND_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
        )    
  -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  WITH PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   ),    
  -- 生成连续年的发散维
  CONTIN_DIM_TMP AS(
     SELECT DISTINCT T2.PERIOD_YEAR,
            T1.LV_CODE , 
            T1.LV_CN_NAME ,
            T1.DIMENSION_CODE,                
            T1.DIMENSION_CN_NAME,             
            T1.DIMENSION_SUBCATEGORY_CODE,    
            T1.DIMENSION_SUBCATEGORY_CN_NAME, 
            T1.DIMENSION_SUB_DETAIL_CODE,     
            T1.DIMENSION_SUB_DETAIL_CN_NAME,  
            T1.GROUP_CODE,                    
            T1.GROUP_CN_NAME,                 
            T1.GROUP_LEVEL,                   
            T1.PARENT_CODE,
            T1.PARENT_CN_NAME,
            T1.VIEW_FLAG,                     
            T1.REGION_CODE,                   
            T1.REGION_CN_NAME,                
            T1.REPOFFICE_CODE,                
            T1.REPOFFICE_CN_NAME,             
            T1.BG_CODE,                       
            T1.BG_CN_NAME,                    
            T1.OVERSEA_FLAG,                  
            T1.MAIN_FLAG,                     
            T1.CODE_ATTRIBUTES
         FROM '||V_TMP1_TABLE||' T1,PERIOD_YEAR_TMP T2
         WHERE T1.GROUP_LEVEL NOT IN (''SUB_DETAIL'',''SPART'')   -- 最细粒度无需展示成本分布图
  )
  -- 各视角下各层级的数据补齐逻辑计算
     SELECT '||V_VERSION_ID||' AS VERSION_ID,
            T1.PERIOD_YEAR,
            T1.LV_CODE,
            T1.LV_CN_NAME,
            T1.DIMENSION_CODE,
            T1.DIMENSION_CN_NAME,
            T1.DIMENSION_SUBCATEGORY_CODE,
            T1.DIMENSION_SUBCATEGORY_CN_NAME,
            T1.DIMENSION_SUB_DETAIL_CODE,
            T1.DIMENSION_SUB_DETAIL_CN_NAME,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
            T1.GROUP_LEVEL,
            NVL(T2.RMB_COST_AMT,0) AS RMB_COST_AMT,
            T1.PARENT_CODE,
            T1.PARENT_CN_NAME,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.VIEW_FLAG,
            T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES,
            DECODE(T2.RMB_COST_AMT, NULL, ''Y'',''N'') AS APPEND_FLAG,  --补齐标识：Y为补齐，N为原始
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG
         FROM CONTIN_DIM_TMP T1
         LEFT JOIN '||V_TMP1_TABLE||' T2
         ON NVL(T1.LV_CODE,''SNULL'') = NVL(T2.LV_CODE,''SNULL'')
         AND NVL(T1.DIMENSION_CODE,''SNULL1'') = NVL(T2.DIMENSION_CODE,''SNULL1'')
         AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL2'')
         AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL3'')
         AND T1.GROUP_CODE = T2.GROUP_CODE
         AND T1.GROUP_LEVEL =T2.GROUP_LEVEL
         AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
         AND T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.PARENT_CODE = T2.PARENT_CODE
         AND NVL(T1.MAIN_FLAG,''SNULL4'') = NVL(T2.MAIN_FLAG,''SNULL4'')
         AND NVL(T1.CODE_ATTRIBUTES,''SNULL5'') = NVL(T2.CODE_ATTRIBUTES,''SNULL5'')
         WHERE T1.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2
                 ';

     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;   
     DBMS_OUTPUT.PUT_LINE('权重计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||' 的权重数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    

  --------------------------------------------------------------------主力编码预处理虚化逻辑-成本分布图逻辑处理----------------------------------------------------------------------
  -- 清空该版本结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_CUS_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID IS NULL';
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的预处理虚化-成本分布图表：'||V_CUS_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 计算预处理虚化的成本分布图数据值
  V_SQL := '
  INSERT INTO '||V_CUS_TO_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH SUM_AMT_TMP AS (
  SELECT PERIOD_YEAR,
         SPART_CODE AS GROUP_CODE,       
         SPART_CN_NAME AS GROUP_CN_NAME,  
         ''SPART'' AS GROUP_LEVEL,
         SUM(NVL(RMB_COST_AMT,0)) AS RMB_COST_AMT,
         LV0_CODE AS PARENT_CODE,
         LV0_CN_NAME AS PARENT_CN_NAME,
         ''LV0'' AS PARENT_LEVEL,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES
      FROM '||V_TMP_TABLE||'
      WHERE MAIN_FLAG = ''Y''   -- 取所有主力编码的数据
      GROUP BY PERIOD_YEAR,
               SPART_CODE,
               SPART_CN_NAME,
               LV0_CODE,
               LV0_CN_NAME,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               BG_CODE,
               BG_CN_NAME,
               OVERSEA_FLAG,
               VIEW_FLAG,
               MAIN_FLAG,
               CODE_ATTRIBUTES
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,       
         GROUP_CN_NAME,  
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM SUM_AMT_TMP';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将预处理的成本分布图数据插入对应表：'||V_CUS_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE 'ANALYZE '||V_CUS_TO_TABLE;

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'、'||V_CUS_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
/*
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  */
END$$
/

