-- Name: f_dm_fom_standard_cst_abs_t_sum; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_standard_cst_abs_t_sum(f_keystr character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最近修改时间: 2024年11月21日16点52分
修改时间: 2024年4月3日14点16分
修改内容: 任务令吸收明细表取数逻辑更新 1,关联字段修改  2,金额字段取相反数 3,筛选条件修改
修改人 :黄心蕊
创建时间：2023/12/05
创建人  ：许灿烽
背景描述：先新建年月版本号, 再生成 标准成本吸收_总(自制)
参数描述:
        参数一(f_keystr)：绝密数据解密密钥串
        参数二(x_result_status)：运行状态返回值, 1 为成功，0 为失败
		参数四(f_version_id)：前端传入的版本号
来源表:FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T  --标准成本吸收收敛  FIN_DM_OPT_FOI.DM_FOM_WIP_QUANTITY_T   --任务令交易量
FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I A1 --任务令吸收明细(不是补录)  FIN_DM_OPT_FOI.APD_FOM_RESOUCE_CODE_T A2  --资源代码类型
目标表:FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  --标准成本吸收_总(自制)
事例：fin_dm_opt_foi.f_dm_fom_standard_cst_abs_t_sum()
--20240328 去除不为0的数据
*/

DECLARE
  V_SP_NAME  VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_STANDARD_CST_ABS_T_SUM'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM'; -- 目标表
  V_STEP_NUM BIGINT := 0; --步骤号
  V_CURRENT_FLAG_MONTH BIGINT;   -- 存放当前是否已有版本号标识(月度)
  V_CURRENT_FLAG_ANNUAL BIGINT;   -- 存放当前是否已有版本号标识(年度)
  V_VERSION_ID_MONTH BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_S(月度)
  V_VERSION_ID_ANNUAL BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_S(年度)
  V_VERSION_NAME_MONTH VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'); --新的版本中文名称(月度)
  V_VERSION_NAME_ANNUAL VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD'); --新的版本中文名称(年度)
  V_DIM_VERSION_ID BIGINT ; --量纲维表要取的版本号
  V_FORECAST_VERSION_ID BIGINT ; --预测量表要取的版本号
  V_DIM_RESOURCE_VERSION_ID BIGINT ; --资源代码类型维表的版本号
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NOT NULL 
	THEN V_VERSION_ID_MONTH := F_VERSION_ID;
ELSE
	-- 查询该月版本是否已存在，若存在，沿用，否则新建 数据类型(DATA_TYPE:MONTH 月度) 
  SELECT COUNT(1) INTO V_CURRENT_FLAG_MONTH
    FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH' AND VERSION_TYPE='AUTO';

	-- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG_MONTH <> 0 THEN 
		SELECT VERSION_ID INTO V_VERSION_ID_MONTH
			FROM 
				FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
			WHERE
				SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
				AND DEL_FLAG = 'N'
				AND STATUS = 1
				AND UPPER(DATA_TYPE) = 'MONTH' AND VERSION_TYPE='AUTO';
				
	-- 更新LAST_UPDATE_DATE时间为最新系统时间，方便JAVA取最新数据
	UPDATE FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T SET LAST_UPDATE_DATE = CURRENT_TIMESTAMP WHERE VERSION_ID = V_VERSION_ID_MONTH;

  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_S')              
    INTO V_VERSION_ID_MONTH
    FROM DUAL;
     
  --版本号为 V_VERSION_ID_MONTH,PARENT_VERSION_ID 父版本id置空 （只有adjust、final才有父版本）
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_VERSION_ID_MONTH,NULL,V_VERSION_NAME_MONTH,1,'AUTO','MONTH',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');
   END IF;
END IF; 

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '往版本信息表记录月度版本号信息, 版本号='||V_VERSION_ID_MONTH||', 版本名称='||V_VERSION_NAME_MONTH,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NOT NULL 
	THEN V_VERSION_ID_ANNUAL := F_VERSION_ID;
ELSE
  -- 查询该年版本是否已存在，若存在，沿用，否则新建 数据类型(DATA_TYPE:ANNUAL 年度) 
  SELECT COUNT(1) INTO V_CURRENT_FLAG_ANNUAL
    FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL' AND VERSION_TYPE='AUTO';

 -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG_ANNUAL <> 0 THEN 
  
		SELECT VERSION_ID INTO V_VERSION_ID_ANNUAL
		FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
		WHERE
        SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL' AND VERSION_TYPE='AUTO';
  
  -- 更新LAST_UPDATE_DATE时间为最新系统时间，方便JAVA取最新数据
	UPDATE FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T SET LAST_UPDATE_DATE = CURRENT_TIMESTAMP WHERE VERSION_ID = V_VERSION_ID_ANNUAL;

  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_S')
    INTO V_VERSION_ID_ANNUAL
    FROM DUAL;
     
  --版本号为 V_VERSION_ID_ANNUAL,PARENT_VERSION_ID 父版本id置空 （只有adjust、final才有父版本）
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_VERSION_ID_ANNUAL,NULL,V_VERSION_NAME_ANNUAL,1,'AUTO','ANNUAL',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');
   END IF;
 END IF; 
 
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '往版本信息表记录年度版本号信息, 版本号='||V_VERSION_ID_ANNUAL||', 版本名称='||V_VERSION_NAME_ANNUAL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

-- 查询量纲维表的最新版本号
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIM_DMS'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;
	 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '制造量纲版本号：'||V_DIM_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

-- 查询资源代码类型维表的版本号
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_RESOURCE_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIM_RESOURCE'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '资源代码类型维表的版本号：'||V_DIM_RESOURCE_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

--创建临时表, 插入解密的数据 (标准成本吸收收敛,自制历史金额)
  DROP TABLE IF EXISTS STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP;
  CREATE TEMPORARY TABLE STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP
  (
     PRIMARY_ID BIGINT,
     AMOUNT NUMERIC(100,2)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN
;

INSERT INTO STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP
SELECT 
PRIMARY_ID
,TO_NUMBER(GS_DECRYPT(AMOUNT,f_keystr, 'AES128', 'CBC', 'SHA256')) AS AMOUNT
FROM FIN_DM_OPT_FOI.STANDARD_CST_JAVA_ABS_AGGRE_T
;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入解密的数据 (标准成本吸收收敛,自制历史金额)',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


--创建临时表, 插入解密的数据 (任务令吸收明细(不是补录) 自制新增金额)
  DROP TABLE IF EXISTS RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP;
  CREATE TEMPORARY TABLE RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP
  (
     PRIMARY_ID BIGINT,
     GC_TRANSACTION_AMOUNT NUMERIC(100,2)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN
;

INSERT INTO RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP
SELECT 
PRIMARY_ID
,TO_NUMBER(GS_DECRYPT(GC_TRANSACTION_AMOUNT,f_keystr, 'AES128', 'CBC', 'SHA256')) AS GC_TRANSACTION_AMOUNT
FROM FIN_DM_OPT_FOI.RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I
;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入解密的数据 (任务令吸收明细(不是补录) 自制新增金额)',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM;
INSERT INTO  FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM 
--自制历史数据
--202411版本 原制造量纲维表替换
SELECT V_VERSION_ID_ANNUAL AS VERSION_ID  , --版本号
       V_VERSION_NAME_ANNUAL AS VERSION_NAME , --版本名称
       SUBSTR(A.PERIOD_ID, 1, 4) :: INT AS PERIOD_YEAR , --会计年
       A.PERIOD_ID :: INT , --会计期
       C.APD_MANUFACTURE_PROD_LV0 AS LV0_CODE, --LV0中文名称
       C.APD_MANUFACTURE_PROD_LV0 AS LV0_CN_NAME , --LV0中文名称
       C.APD_MANUFACTURE_PROD_LV1 AS LV1_CODE , --LV1中文名称
       C.APD_MANUFACTURE_PROD_LV1 AS LV1_CN_NAME , --LV1中文名称
       C.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT_CODE , --经营对象编码
       C.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT_CN_NAME , --经营对象名称
       CASE
         WHEN C.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND C.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN C.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND C.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE C.APD_SHIPMENT_OBJECT
       END AS SHIPPING_OBJECT_CODE  , --发货对象编码
       CASE
         WHEN C.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND C.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN C.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND C.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE C.APD_SHIPMENT_OBJECT
       END AS SHIPPING_OBJECT_CN_NAME  , --发货对象名称
       CASE
         WHEN C.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND C.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN C.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND C.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE C.APD_MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CODE  , --制造对象编码
       CASE
         WHEN C.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND C.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN C.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND C.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE C.APD_MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CN_NAME , --制造对象名称
       A.ITEM AS ITEM_CODE ,--ITEM编码
       NULL AS ITEM_CN_NAME  , --ITEM中文名称	202411版本 ITEM名称无实际运用
       -1 AS CREATED_BY ,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY ,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
       'N' AS DEL_FLAG ,
       GS_ENCRYPT(A.AMOUNT, f_keystr, 'AES128', 'CBC', 'SHA256') AS RMB_MADE_AMT , --吸收金额
       B.TRANSACTION_QUANTITY --交易数量
  FROM (SELECT A1.ITEM, A1.PERIOD_ID, SUM(A2.AMOUNT) AS AMOUNT --吸收金额  加密后使用
          FROM FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T A1 --标准成本吸收收敛
         INNER JOIN STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP A2
            ON (A1.PRIMARY_ID = A2.PRIMARY_ID)
         WHERE A1.RESOURCE_TYPE IN ('RES吸收_人工',
                                    'RES吸收_通用机器',
                                    'FT机器',
                                    'OH吸收_制造部')
           AND A1.HOMEMADE_EMS = '自制'
		   --AND A2.AMOUNT > 0  
					--剔除小于等于0的数据(2024/03/07修改)
           AND A2.AMOUNT <> 0 
        --修改为取不为0的数据 20240328
         GROUP BY A1.ITEM, A1.PERIOD_ID) A
 INNER JOIN 
(SELECT CAST(CONCAT(SUBSTR(PERIOD_NAME, 1, 4),
                                SUBSTR(PERIOD_NAME, 6, 2)) AS BIGINT) AS PERIOD_NAME,
                    ITEM_CODE,
                    SUM(TRANSACTION_QUANTITY) AS TRANSACTION_QUANTITY --交易数量（PCS）
               FROM FIN_DM_OPT_FOI.DM_FOM_WIP_QUANTITY_T --任务令交易量
              WHERE VERSION_ID IN
                    ( --按月取最新版本号
                     SELECT MAX(T.VERSION_ID)
                       FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
                      WHERE UPPER(T.DATA_TYPE) = 'DIM_WIP'
                        AND UPPER(DEL_FLAG) = 'N'
                        AND STATUS = 1
                      GROUP BY SUBSTR(VERSION, 1, 6))
                AND HOMEMADE_EMS = '自制'
                AND TRANSACTION_QUANTITY <> 0 --取不为0的数据 20240328
              GROUP BY CAST(CONCAT(SUBSTR(PERIOD_NAME, 1, 4),
                                   SUBSTR(PERIOD_NAME, 6, 2)) AS BIGINT),
                       ITEM_CODE) B
    ON (A.PERIOD_ID = B.PERIOD_NAME AND A.ITEM = B.ITEM_CODE)
 /*INNER JOIN FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T C
    ON (A.ITEM = C.ITEM_CODE)
 WHERE C.DIM_TREE_FLAG = 1 --只取在维度树的数据
   AND C.VERSION_ID = V_DIM_VERSION_ID --只取一个版本的数据
   */
   INNER JOIN FIN_DM_OPT_FOI.APD_INV_ITEM_MANUFACTURE_T C
    ON (A.ITEM = C.ITEM_CODE)	--202411版本 维表修改
   

/*
--自制新增数据
UNION ALL
SELECT V_VERSION_ID_ANNUAL AS VERSION_ID , --版本号
       V_VERSION_NAME_ANNUAL AS VERSION_NAME , --版本名称
       SUBSTR(A.PERIOD_NAME, 1, 4) AS PERIOD_YEAR , --会计年
       CAST(A.PERIOD_NAME AS BIGINT) AS PERIOD_ID ,  --会计期
       C.LV0_CODE , --重量级团队LV0编码
       C.LV0_CN_NAME , --重量级团队LV0中文名称
       C.LV1_CODE , --重量级团队LV1编码
       C.LV1_CN_NAME , --重量级团队LV1中文名称
       C.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CODE , --经营对象编码
       C.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CN_NAME , --经营对象名称
       CASE
         WHEN C.LV0_CN_NAME = 'ICT' AND C.LV1_CN_NAME = '云核心网' THEN ''
         WHEN C.LV0_CN_NAME = '海思光电' AND C.LV1_CN_NAME = '海思光电' THEN ''
         ELSE C.SHIPPING_OBJECT
       END AS SHIPPING_OBJECT_CODE , --发货对象编码
       CASE
         WHEN C.LV0_CN_NAME = 'ICT' AND C.LV1_CN_NAME = '云核心网' THEN ''
         WHEN C.LV0_CN_NAME = '海思光电' AND C.LV1_CN_NAME = '海思光电' THEN ''
         ELSE C.SHIPPING_OBJECT
       END AS SHIPPING_OBJECT_CN_NAME , --发货对象名称
       CASE
         WHEN C.LV0_CN_NAME = 'ICT' AND C.LV1_CN_NAME = '云核心网' THEN ''
         WHEN C.LV0_CN_NAME = '海思光电' AND C.LV1_CN_NAME = '海思光电' THEN ''
         ELSE C.MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CODE , --制造对象编码
       CASE
         WHEN C.LV0_CN_NAME = 'ICT' AND C.LV1_CN_NAME = '云核心网' THEN ''
         WHEN C.LV0_CN_NAME = '海思光电' AND C.LV1_CN_NAME = '海思光电' THEN ''
         ELSE C.MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CN_NAME , --制造对象名称
       A.ASSEMBLY_ITEM_CODE AS ITEM_CODE , --子项ITEM编码
       C.CN_DESC AS ITEM_CN_NAME , --子项ITEM中文名称
       -1 AS CREATED_BY ,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY ,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
       'N' AS DEL_FLAG ,
       GS_ENCRYPT(A.GC_TRANSACTION_AMOUNT,
                  f_keystr,
                  'AES128',
                  'CBC',
                  'SHA256') AS RMB_MADE_AMT , --吸收金额
       B.TRANSACTION_QUANTITY --交易数量
  FROM (SELECT A1.ASSEMBLY_ITEM_CODE,
               CONCAT('20',
                      RIGHT(A1.PERIOD_NAME, 2),
                      CASE LEFT(A1.PERIOD_NAME, 3)
                        WHEN 'JAN' THEN '01'
                        WHEN 'FEB' THEN '02'
                        WHEN 'MAR' THEN '03'
                        WHEN 'APR' THEN '04'
                        WHEN 'MAY' THEN '05'
                        WHEN 'JUN' THEN '06'
                        WHEN 'JUL' THEN '07'
                        WHEN 'AUG' THEN '08'
                        WHEN 'SEP' THEN '09'
                        WHEN 'OCT' THEN '10'
                        WHEN 'NOV' THEN '11'
                        WHEN 'DEC' THEN '12'
                      END) AS PERIOD_NAME , --交易日期
               --SUM(A1.GC_TRANSACTION_AMOUNT) AS AMOUNT ,--吸收金额
               SUM(A3.GC_TRANSACTION_AMOUNT * (-1)) AS GC_TRANSACTION_AMOUNT --20240402修改 取相反数再求和
          FROM FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_RESOUCE_TRANS_I A1	--资源吸收 202411版本 表替换
		  --FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I A1 --任务令吸收明细(不是补录)
         INNER JOIN FIN_DM_OPT_FOI.APD_FOM_RESOUCE_CODE_T A2 --资源代码类型		202411版本 去除
            ON ( --A1.RESOURCE_CODE = A2.RESOURCE_CODE
				A1.RESOURCE_OVERHEAD_CODE = A2.RESOURCE_CODE) --20240402修改 
         INNER JOIN RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP A3
            ON (A1.PRIMARY_ID = A3.PRIMARY_ID)
         WHERE A2.VERSION_ID = V_DIM_RESOURCE_VERSION_ID
           --AND A2.HOMEMADE_EMS = '自制' --********修改
		   AND A1.WIP_ORDER_NUMBER IN
								   (SELECT DISTINCT T1.WIP_ORDER_NUMBER
									  FROM FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I T1
									  JOIN FIN_DM_OPT_FOI.APD_FOM_RESOUCE_CODE_T T2		
										ON T1. RESOURCE_OVERHEAD_CODE = T2.RESOURCE_CODE
									 WHERE T2.HOMEMADE_EMS = '自制'
									   AND T2.VERSION_ID = V_DIM_RESOURCE_VERSION_ID)  --********修改 
           AND A2.RESOURCE_TYPE IN ('RES-人工',
                                    'RES-通用机器',
                                    'RES-FT机器',
                                    'OH-制造平台',
                                    'OH-制造平台-自制')
        --           AND A3.GC_TRANSACTION_AMOUNT > 0 --剔除小于等于0的数据(2024/03/07修改)
		  AND A3.GC_TRANSACTION_AMOUNT <> 0
		  AND A1.COA_ACCOUNT_CODE in('5980201','5980202','5980203') --********修改 
         GROUP BY A1.ASSEMBLY_ITEM_CODE,
                  CONCAT('20',RIGHT(A1.PERIOD_NAME, 2), CASE LEFT(A1.PERIOD_NAME, 3)
														  WHEN 'JAN' THEN '01'
														  WHEN 'FEB' THEN '02'
														  WHEN 'MAR' THEN '03'
														  WHEN 'APR' THEN '04'
														  WHEN 'MAY' THEN '05'
														  WHEN 'JUN' THEN '06'
														  WHEN 'JUL' THEN '07'
														  WHEN 'AUG' THEN '08'
														  WHEN 'SEP' THEN '09'
														  WHEN 'OCT' THEN '10'
														  WHEN 'NOV' THEN '11'
														  WHEN 'DEC' THEN '12'			  
														END )
			) A --任务令吸收明细(不是补录)
 INNER JOIN (SELECT ITEM_CODE,
                    CONCAT(SUBSTR(PERIOD_NAME, 1, 4),
                           SUBSTR(PERIOD_NAME, 6, 2)) AS PERIOD_NAME,
                    SUM(TRANSACTION_QUANTITY) AS TRANSACTION_QUANTITY
               FROM FIN_DM_OPT_FOI.DM_FOM_WIP_QUANTITY_T --任务令交易量
              WHERE VERSION_ID IN
                    ( --按月取最新版本号
                     SELECT MAX(T.VERSION_ID)
                       FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
                      WHERE UPPER(T.DATA_TYPE) = 'DIM_WIP'
                        AND UPPER(DEL_FLAG) = 'N'
                        AND STATUS = 1
                      GROUP BY SUBSTR(VERSION, 1, 6))
                AND HOMEMADE_EMS = '自制'
              GROUP BY ITEM_CODE, PERIOD_NAME) B
    ON (A.ASSEMBLY_ITEM_CODE = B.ITEM_CODE AND
       A.PERIOD_NAME = B.PERIOD_NAME)
 INNER JOIN FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T C
    ON (A.ASSEMBLY_ITEM_CODE = C.ITEM_CODE)
 WHERE C.DIM_TREE_FLAG = 1 --只取在维度树的数据
   AND C.VERSION_ID = V_DIM_VERSION_ID --只取一个版本的数据
;
*/

--202411版本 
UNION ALL

SELECT V_VERSION_ID_ANNUAL AS VERSION_ID, --版本号
       V_VERSION_NAME_ANNUAL AS VERSION_NAME, --版本名称
       SUBSTR(T3.PERIOD_ID, 1, 4)::INT AS PERIOD_YEAR, --会计年
       T3.PERIOD_ID :: INT, --会计期
       T3.LV0_CN_NAME AS LV0_CODE, --重量级团队LV0编码
       T3.LV0_CN_NAME, --重量级团队LV0中文名称
       T3.LV1_CN_NAME AS LV1_CODE, --重量级团队LV1编码
       T3.LV1_CN_NAME, --重量级团队LV1中文名称
       T3.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CODE, --经营对象编码
       T3.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CN_NAME, --经营对象名称
       CASE
         WHEN T3.LV0_CN_NAME = 'ICT' AND T3.LV1_CN_NAME = '云核心网' THEN ''
         WHEN T3.LV0_CN_NAME = '海思光电' AND T3.LV1_CN_NAME = '海思光电' THEN ''
         ELSE T3.SHIPPING_OBJECT
       END AS SHIPPING_OBJECT_CODE, --发货对象编码
       CASE
         WHEN T3.LV0_CN_NAME = 'ICT' AND T3.LV1_CN_NAME = '云核心网' THEN ''
         WHEN T3.LV0_CN_NAME = '海思光电' AND T3.LV1_CN_NAME = '海思光电' THEN ''
         ELSE T3.SHIPPING_OBJECT
       END AS SHIPPING_OBJECT_CN_NAME, --发货对象名称
       CASE
         WHEN T3.LV0_CN_NAME = 'ICT' AND T3.LV1_CN_NAME = '云核心网' THEN ''
         WHEN T3.LV0_CN_NAME = '海思光电' AND T3.LV1_CN_NAME = '海思光电' THEN ''
         ELSE T3.MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CODE, --制造对象编码
       CASE
         WHEN T3.LV0_CN_NAME = 'ICT' AND T3.LV1_CN_NAME = '云核心网' THEN ''
         WHEN T3.LV0_CN_NAME = '海思光电' AND T3.LV1_CN_NAME = '海思光电' THEN ''
         ELSE T3.MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CN_NAME, --制造对象名称
       T3.ITEM_CODE, --子项ITEM编码
       NULL AS ITEM_CN_NAME, --子项ITEM中文名称
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       'N' AS DEL_FLAG,
       GS_ENCRYPT(T3.TRANSACTION_AMOUNT,
                  f_keystr,
                  'AES128',
                  'CBC',
                  'SHA256') AS RMB_MADE_AMT, --加密吸收金额
       T4.TRANSACTION_QUANTITY --交易数量
  FROM (--吸收金额
		  SELECT TO_CHAR(T1.TRANSACTION_DATE, 'YYYYMM') AS PERIOD_ID,--会计期
				 T1.ASSEMBLY_ITEM_CODE AS ITEM_CODE, --ITEM_CODE
				 SUM(T2.GC_TRANSACTION_AMOUNT) AS TRANSACTION_AMOUNT, --吸收金额
				 T1.APD_MANUFACTURE_PROD_LV0 AS LV0_CN_NAME, --制造产品LV0
				 T1.APD_MANUFACTURE_PROD_LV1 AS LV1_CN_NAME, --制造产品LV1
				 T1.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT, --经营对象
				 T1.APD_SHIPMENT_OBJECT AS SHIPPING_OBJECT, --发货对象
				 T1.APD_MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT, --制造对象
				 NULL AS ITEM_CN_NAME --ITEM描述
			FROM FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_RESOUCE_TRANS_T T1
		   INNER JOIN RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP T2		--关联金额加密表，获取加密金额字段
				ON (T1.PRIMARY_ID = T2.PRIMARY_ID)
		   INNER JOIN DWRDIM.DWR_DIM_DEPARTMENT_D D		--******** 加入部门维表
				ON T1.COA_DEPT_KEY = D.DEPT_KEY
		   WHERE T1.ABSORPTION_ACCOUNT IN (5980201,5980202, 5980203)
			  AND D.L1_DEPT_CODE = '035347'		--******** 加入部门维表条件筛选
			  AND T2.GC_TRANSACTION_AMOUNT <> 0
			  AND T1.HOMEMADE_TYPE = '自制'
			  AND T1.APD_RESOUCE_TYPE IN ('RES-FT机器',
										'RES-FT低耗',
										'RES-通用机器',
										'RES_通用物耗',
										'RES-人工',
										'OH-制造平台')
		   GROUP BY TO_CHAR(T1.TRANSACTION_DATE, 'YYYYMM'),
					T1.ASSEMBLY_ITEM_CODE,
					T1.APD_MANUFACTURE_PROD_LV0,
					T1.APD_MANUFACTURE_PROD_LV1,
					T1.APD_OPERATE_OBJECT,
					T1.APD_SHIPMENT_OBJECT,
					T1.APD_MANUFACTURE_OBJECT
		  ) T3
 INNER JOIN (--交易量
				SELECT CONCAT('20',
						RIGHT(PERIOD_NAME, 2),
						CASE LEFT(PERIOD_NAME, 3)
						  WHEN 'JAN' THEN '01'
						  WHEN 'FEB' THEN '02'
						  WHEN 'MAR' THEN '03'
						  WHEN 'APR' THEN '04'
						  WHEN 'MAY' THEN '05'
						  WHEN 'JUN' THEN '06'
						  WHEN 'JUL' THEN '07'
						  WHEN 'AUG' THEN '08'
						  WHEN 'SEP' THEN '09'
						  WHEN 'OCT' THEN '10'
						  WHEN 'NOV' THEN '11'
						  WHEN 'DEC' THEN '12'
						END) AS PERIOD_ID, --会计期
				 APD_MANUFACTURE_PROD_LV0 AS LV0_CN_NAME, --制造产品LV0
				 APD_MANUFACTURE_PROD_LV1 AS LV1_CN_NAME, --制造产品LV1
				 NULL AS ITEM_CN_NAME, --物料描述
				 APD_OPERATE_OBJECT AS BUSSINESS_OBJECT, --经营对象
				 APD_SHIPMENT_OBJECT AS SHIPPING_OBJECT, --发货对象
				 APD_MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT, --制造对象
				 ITEM_CODE, --物料编码
				 SUM(TRANSACTION_QUANTITY) AS TRANSACTION_QUANTITY --交易量
			FROM FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_MATERIAL_TRANS_T
		   WHERE WIP_ORDER_CATEGORY = 1
			 --20241118修改 新增以下三项筛选逻辑
			 AND TRANSACTION_TYPE_CODE IN (44, 90, 17)
			 AND IF_TABSORPTION_AMOUNT = 'Y'
			 AND HOMEMADE_TYPE = '自制'  --20241120修改 加入自制筛选
			 AND ORGANIZATION_CODE IN ('DG1',
									 'H80',
									 'H87',
									 'H90',
									 'H91',
									 'JX1',
									 'NY1',
									 'OP1',
									 'SH2',
									 'H99',
									 'PM4',
									 'PM2')	
		   GROUP BY PERIOD_NAME,
					ITEM_CODE, --物料编码
					APD_MANUFACTURE_PROD_LV0, --制造产品LV0
					APD_MANUFACTURE_PROD_LV1, --制造产品LV1
					APD_OPERATE_OBJECT, --经营对象
					APD_SHIPMENT_OBJECT, --发货对象
					APD_MANUFACTURE_OBJECT --制造对象 
					) T4
    ON T3.PERIOD_ID = T4.PERIOD_ID
   AND T3.LV0_CN_NAME = T4.LV0_CN_NAME
   AND T3.LV1_CN_NAME = T4.LV1_CN_NAME
   AND T3.ITEM_CODE = T4.ITEM_CODE
   AND NVL(T3.BUSSINESS_OBJECT, 'AO') = NVL(T4.BUSSINESS_OBJECT, 'AO')
   AND NVL(T3.SHIPPING_OBJECT, 'AS') = NVL(T4.SHIPPING_OBJECT, 'AS')
   AND NVL(T3.MANUFACTURE_OBJECT, 'AM') = NVL(T4.MANUFACTURE_OBJECT, 'AM')
   INNER JOIN FIN_DM_OPT_FOI.APD_INV_ITEM_MANUFACTURE_T C
    ON (T3.ITEM_CODE = C.ITEM_CODE);		--20241129修改 使新增数与历史数维度保持一致
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '标准成本吸收_总(自制),并重新插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

-- 202411版本 经营对象 发货对象 制造对象中，任意层级为空，则继承上级后赋值‘未定义’
--制造对象依赖发货对象，发货对象依赖经营对象，即UPDATE存在先后顺序，不可更改

--替换经营对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  SET BUSSINESS_OBJECT_CODE = LV1_CN_NAME||'_未定义经营对象' WHERE 
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE BUSSINESS_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  SET BUSSINESS_OBJECT_CN_NAME = LV1_CN_NAME||'_未定义经营对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE BUSSINESS_OBJECT_CN_NAME IS NULL
  END );
  
--替换发货对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  SET SHIPPING_OBJECT_CODE = BUSSINESS_OBJECT_CN_NAME||'_未定义发货对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE SHIPPING_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  SET SHIPPING_OBJECT_CN_NAME = BUSSINESS_OBJECT_CN_NAME||'_未定义发货对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE SHIPPING_OBJECT_CN_NAME IS NULL
  END );
  
--替换制造对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  SET MANUFACTURE_OBJECT_CODE = SHIPPING_OBJECT_CODE||'_未定义制造对象' WHERE 
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE MANUFACTURE_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_T_SUM  SET MANUFACTURE_OBJECT_CN_NAME = SHIPPING_OBJECT_CN_NAME||'_未定义制造对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE MANUFACTURE_OBJECT_CN_NAME IS NULL
  END );
	


  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '标准成本吸收_总(自制)空值维度字段重置完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END


$$
/

