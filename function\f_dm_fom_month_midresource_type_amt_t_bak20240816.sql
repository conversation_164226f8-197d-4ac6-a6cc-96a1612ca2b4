-- Name: f_dm_fom_month_midresource_type_amt_t_bak20240816; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_midresource_type_amt_t_bak20240816(f_keystr character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-08
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-制造对象分资源类型数据，及ITEM层级分资源类型百分比初始化
参数描述：参数一(F_KEYSTR)：绝密数据解密密钥串
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T 自制分资源类型金额表
		FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T 规格品清单
目标表：FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T 月度分析分资源类型金额中间表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T('密钥串',''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T';
  V_STEP_NUM     INT := 0; --函数步骤号
  V_KEYSTR       VARCHAR(200) := F_KEYSTR; --解密密钥串
  V_VERSION		INT4 ;

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T ;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '数据删除成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  V_STEP_NUM := V_STEP_NUM + 1;
--1.建临时表，承载规格品金额
DROP TABLE IF EXISTS DM_DECRYP_TOP_AMT_TEMP;
CREATE TEMPORARY TABLE DM_DECRYP_TOP_AMT_TEMP(
	PERIOD_YEAR CHARACTER VARYING(50),
	PERIOD_ID CHARACTER VARYING(50),
	LV0_CODE CHARACTER VARYING(50),
	LV0_CN_NAME CHARACTER VARYING(200),
	LV1_CODE CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CODE CHARACTER VARYING(50),
	BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
	SHIPPING_OBJECT_CODE CHARACTER VARYING(50),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(50),
	MANUFACTURE_OBJECT_CODE CHARACTER VARYING(50),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(50),
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_CN_NAME CHARACTER VARYING(1000),
	RMB_COST_AMT  NUMERIC,
	RESOURCE_TYPE  CHARACTER VARYING(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
    --2.自制金额落表，需解密
    V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_TOP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_COST_AMT,
	   RESOURCE_TYPE)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,
                                  V_KEYSTR,
                                  'aes128',
                                  'cbc',
                                  'sha256')) AS RMB_COST_AMT,
			RESOURCE_TYPE
        FROM FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T ;
  
    --写入日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME       => V_SP_NAME,
     F_STEP_NUM      => V_STEP_NUM,
     F_CAL_LOG_DESC  => '自制金额落表',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_SUCCESS_FLAG,
     F_ERRBUF        => 'SUCCESS');

  
	--2.2 收敛制造对象层级金额
	V_STEP_NUM := V_STEP_NUM + 1;
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T 
    (VERSION_ID,
     PERIOD_YEAR,
	 PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
	 MANUFACTURE_OBJECT_CODE,
	 MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
	 GROUP_LEVEL,
     RMB_COST_AMT,
	 WEIGHT_RATE,
	 RESOURCE_TYPE,
	 PARENT_CODE,
	 PARENT_CN_NAME,
	 CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT V_VERSION AS VERSION_ID,
           T1.PERIOD_YEAR,
		   T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           T1.SHIPPING_OBJECT_CODE,
           T1.SHIPPING_OBJECT_CN_NAME,
		   T1.MANUFACTURE_OBJECT_CODE,
		   T1.MANUFACTURE_OBJECT_CN_NAME,
           T1.MANUFACTURE_OBJECT_CODE 	 AS GROUP_CODE,
           T1.MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME,
		   'MANUFACTURE_OBJECT' AS GROUP_LEVEL,
           SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT,
		   SUM(T1.RMB_COST_AMT)/
			NULLIF(SUM(SUM(T1.RMB_COST_AMT))OVER(PARTITION BY T1.LV0_CODE,T1.LV1_CODE,T1.BUSSINESS_OBJECT_CODE,T1.SHIPPING_OBJECT_CODE,T1.MANUFACTURE_OBJECT_CODE,T1.PERIOD_ID),0) AS WEIGHT_RATE,
		   T1.RESOURCE_TYPE,
		   T1.SHIPPING_OBJECT_CODE AS PARENT_CODE,
		   T1.SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_DECRYP_TOP_AMT_TEMP T1
	          JOIN FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T T2
          ON T1.ITEM_CODE = T2.TOP_ITEM_CODE
         AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
         AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
         AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
             NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
         AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
		     NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
		 AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
		     NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD')
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') <> 'SNULL'
		AND T2.IS_TOP_FLAG = 'Y'
		AND T2.CALIBER_FLAG = 'M'
         AND T2.VERSION_ID = V_VERSION
     GROUP BY T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
              T1.SHIPPING_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CN_NAME,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CN_NAME,
			  T1.PERIOD_YEAR,
			  T1.RESOURCE_TYPE,
			  T1.PERIOD_ID
			  
UNION ALL

    SELECT V_VERSION AS VERSION_ID,
           T1.PERIOD_YEAR,
		   T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           T1.SHIPPING_OBJECT_CODE,
           T1.SHIPPING_OBJECT_CN_NAME,
		   T1.MANUFACTURE_OBJECT_CODE,
		   T1.MANUFACTURE_OBJECT_CN_NAME,
           T1.ITEM_CODE AS GROUP_CODE,
           T1.ITEM_CN_NAME AS GROUP_CN_NAME,
		   'ITEM' AS GROUP_LEVEL,
           '' AS RMB_COST_AMT,
		   SUM(T1.RMB_COST_AMT)/
			NULLIF(SUM(SUM(T1.RMB_COST_AMT))OVER(PARTITION BY T1.LV0_CODE,T1.LV1_CODE,T1.BUSSINESS_OBJECT_CODE,T1.SHIPPING_OBJECT_CODE,T1.MANUFACTURE_OBJECT_CODE,T1.ITEM_CODE,T1.PERIOD_ID),0) AS WEIGHT_RATE,
		   T1.RESOURCE_TYPE,
		   T1.MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
		   T1.MANUFACTURE_OBJECT_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_DECRYP_TOP_AMT_TEMP T1
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') <> 'SNULL'
	 GROUP BY T1.PERIOD_YEAR,
	          T1.PERIOD_ID,
	          T1.LV0_CODE,
	          T1.LV0_CN_NAME,
	          T1.LV1_CODE,
	          T1.LV1_CN_NAME,
	          T1.BUSSINESS_OBJECT_CODE,
	          T1.BUSSINESS_OBJECT_CN_NAME,
	          T1.SHIPPING_OBJECT_CODE,
	          T1.SHIPPING_OBJECT_CN_NAME,
	          T1.MANUFACTURE_OBJECT_CODE,
	          T1.MANUFACTURE_OBJECT_CN_NAME,
	          T1.ITEM_CODE ,
	          T1.ITEM_CN_NAME,
			  T1.RESOURCE_TYPE

UNION ALL			  
--加入海思与云核心网的特殊数据计算	
    SELECT V_VERSION AS VERSION_ID,
           T1.PERIOD_YEAR,
		   T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           '',
           '',
		   '',
		   '',
           T1.BUSSINESS_OBJECT_CODE 	 AS GROUP_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
		   'BUSSINESS_OBJECT' AS GROUP_LEVEL,
           SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT,
		   SUM(T1.RMB_COST_AMT)/
			NULLIF(SUM(SUM(T1.RMB_COST_AMT))OVER(PARTITION BY T1.LV0_CODE,T1.LV1_CODE,T1.BUSSINESS_OBJECT_CODE,T1.PERIOD_ID),0) AS WEIGHT_RATE,
		   T1.RESOURCE_TYPE,
		   T1.LV1_CODE AS PARENT_CODE,
		   T1.LV1_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_DECRYP_TOP_AMT_TEMP T1
	          JOIN FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T T2
          ON T1.ITEM_CODE = T2.TOP_ITEM_CODE
         AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
         AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
         AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
             NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL'
		AND T2.IS_TOP_FLAG = 'Y'
		AND T2.CALIBER_FLAG = 'M'
         AND T2.VERSION_ID = V_VERSION
     GROUP BY T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
			  T1.PERIOD_YEAR,
			  T1.RESOURCE_TYPE,
			  T1.PERIOD_ID
			  
UNION ALL
--加入海思与云核心网下ITEM的特殊数据计算
    SELECT V_VERSION AS VERSION_ID,
           T1.PERIOD_YEAR,
		   T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           '',
           '',
		   '',
		   '',
           T1.ITEM_CODE AS GROUP_CODE,
           T1.ITEM_CN_NAME AS GROUP_CN_NAME,
		   'ITEM' AS GROUP_LEVEL,
           '' AS RMB_COST_AMT,
		   SUM(T1.RMB_COST_AMT)/
			NULLIF(SUM(SUM(T1.RMB_COST_AMT))OVER(PARTITION BY T1.LV0_CODE,T1.LV1_CODE,T1.BUSSINESS_OBJECT_CODE,T1.ITEM_CODE,T1.PERIOD_ID),0) AS WEIGHT_RATE,
		   T1.RESOURCE_TYPE,
		   T1.BUSSINESS_OBJECT_CODE AS PARENT_CODE,
		   T1.BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_DECRYP_TOP_AMT_TEMP T1
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL'
	 GROUP BY T1.PERIOD_YEAR,
	          T1.PERIOD_ID,
	          T1.LV0_CODE,
	          T1.LV0_CN_NAME,
	          T1.LV1_CODE,
	          T1.LV1_CN_NAME,
	          T1.BUSSINESS_OBJECT_CODE,
	          T1.BUSSINESS_OBJECT_CN_NAME,
	          T1.ITEM_CODE ,
	          T1.ITEM_CN_NAME,
			  T1.RESOURCE_TYPE;
	
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '制造对象分资源类型金额落表成功',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	
	
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

