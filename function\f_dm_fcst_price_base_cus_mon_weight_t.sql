-- Name: f_dm_fcst_price_base_cus_mon_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_weight_t(f_custom_id character varying, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 	/*
创建时间：2024-11-12
创建人  ：朱雅欣
背景描述：虚化后月累计权重表 函数,调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一： F_CUSTOM_ID 组合ID
          参数二:  F_VERSION_ID 版本ID
          参数三:  x_result_status 返回状态 运行状态返回值 ‘1’为成功，‘0’为失败
来源表  ：年均本补齐表        ：DM_FCST_PRICE_ANNL_AVG_T
          虚化维表            ：DM_FCST_PRICE_BASE_CUS_DIM_T
		  
结果表  ：虚化后月累计权重表  ：DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T

事例    ：select fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_weight_t()

*/
DECLARE
  V_SP_NAME    VARCHAR(400) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T';
  V_VERSION_ID BIGINT;      --版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_LV_CODE    TEXT; 
  
BEGIN   
  
   -- 当传入版本为空时，取版本表的最大版本ID，当传入版本不为空时，取传入的版本ID
   IF (F_VERSION_ID IS NULL OR F_VERSION_ID = '')  THEN
SELECT MAX(VERSION_ID) AS VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
	   ;
 ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  
    -- 从虚化维表中取 LV_CODE 
    SELECT  DISTINCT  LV_CODE  INTO V_LV_CODE
  	FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T
	WHERE 1=1
	  AND UPPER(PAGE_FLAG) = 'MONTH'
	  AND UPPER(DEL_FLAG)  = 'N'
	  AND CUSTOM_ID = F_CUSTOM_ID
	  ;
  
  
    -- 创建 PRICE_BASE_MON_WEIGHT_TMP1 临时表
    DROP TABLE IF EXISTS PRICE_BASE_MON_WEIGHT_TMP1;
	CREATE TEMPORARY TABLE PRICE_BASE_MON_WEIGHT_TMP1(
      VERSION_ID	                    BIGINT	       /*版本ID*/
     ,PERIOD_YEAR	                    BIGINT	       /*会计年*/
     ,CUSTOM_ID	                        VARCHAR(50)	   /*虚化组合ID*/
     ,CUSTOM_CN_NAME                    VARCHAR(500)   /*虚化组合名称*/
	 ,LV4_PROD_LIST_CODE	            VARCHAR(50)	   /*LV3.5重量级团队编码*/
     ,LV4_PROD_LIST_CN_NAME             VARCHAR(2000)  /*LV3.5重量级团队中文名称*/
     ,GROUP_CODE	                    VARCHAR(50)	   /*层级编码*/
     ,GROUP_CN_NAME	                    VARCHAR(2000)  /*层级中文名称*/
     ,GROUP_LEVEL	                    VARCHAR(50)	   /*层级描述（LV0:ICT、LV1：LV1层级、LV2：LV2层级、LV3：LV3层级、LV4：LV4层级、SPART：SPART层级）*/
	 ,PARENT_CODE	                    VARCHAR(50)    /*父层级编码*/
     ,PARENT_CN_NAME	                VARCHAR(200)   /*父层级中文名称*/
	 ,PARENT_LEVEL                      VARCHAR(50)	 	 
     ,WEIGHT_RATE	                    NUMERIC	       /*PNP(CNP)_美元*/
     ,OVERSEA_FLAG	                    VARCHAR(10)	   /*国内海外标识*/
     ,REGION_CODE	                    VARCHAR(50)	   /*地区部编码*/
     ,REGION_CN_NAME	                VARCHAR(200)   /*地区部名称*/
     ,REPOFFICE_CODE	                VARCHAR(50)	   /*代表处编码*/
     ,REPOFFICE_CN_NAME	                VARCHAR(200)   /*代表处名称*/
     ,SIGN_TOP_CUST_CATEGORY_CODE	    VARCHAR(50)	   /*签约客户_大T系统部编码*/
     ,SIGN_TOP_CUST_CATEGORY_CN_NAME	VARCHAR(200)   /*签约客户_大T系统部名称*/
     ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	VARCHAR(200)   /*签约客户_子网系统部名称*/
     ,VIEW_FLAG	                        VARCHAR(50)    /*视角标识，用于区分不同视角下的数据*/    
	 ,BG_CODE                           VARCHAR(50)    /*BG编码*/
     ,BG_CN_NAME                        VARCHAR(200)   /*BG中文名称*/
 )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_YEAR,CUSTOM_ID)
  ;
	  	  		  
	  
	  -- 将年均本补齐表 与 虚化维表 关联，取 虚化维度 和 SPART层级的真实金额
  INSERT INTO PRICE_BASE_MON_WEIGHT_TMP1(
      VERSION_ID	                    /*版本ID*/
     ,PERIOD_YEAR	                    /*会计年*/
     ,CUSTOM_ID	                        /*虚化组合ID*/
     ,CUSTOM_CN_NAME                    /*虚化组合名称*/
	 ,LV4_PROD_LIST_CODE	            /*LV3.5重量级团队编码*/
     ,LV4_PROD_LIST_CN_NAME             /*LV3.5重量级团队中文名称*/
     ,GROUP_CODE	                    /*层级编码*/
     ,GROUP_CN_NAME	                    /*层级中文名称*/
     ,GROUP_LEVEL	                    /*层级描述（LV0:ICT、LV1：LV1层级、LV2：LV2层级、LV3：LV3层级、LV4：LV4层级、SPART：SPART层级）*/
	 ,PARENT_CODE	                    /*父层级编码*/
     ,PARENT_CN_NAME	                /*父层级中文名称*/
	 ,PARENT_LEVEL                       
     ,WEIGHT_RATE	                    /*PNP(CNP)_美元*/
     ,OVERSEA_FLAG	                    /*国内海外标识*/
     ,REGION_CODE	                    /*地区部编码*/
     ,REGION_CN_NAME	                /*地区部名称*/
     ,REPOFFICE_CODE	                /*代表处编码*/
     ,REPOFFICE_CN_NAME	                /*代表处名称*/
     ,SIGN_TOP_CUST_CATEGORY_CODE	    /*签约客户_大T系统部编码*/
     ,SIGN_TOP_CUST_CATEGORY_CN_NAME	/*签约客户_大T系统部名称*/
     ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	/*签约客户_子网系统部名称*/
     ,VIEW_FLAG	                        /*视角标识，用于区分不同视角下的数据*/    
	 ,BG_CODE                           /*BG编码*/
     ,BG_CN_NAME                        /*BG中文名称*/
      )
  WITH PRICE_ANNL_AVG_TMP AS (
    SELECT VERSION_ID
          ,PERIOD_YEAR 
          ,LV0_PROD_LIST_CODE
          ,LV1_PROD_LIST_CODE
          ,LV2_PROD_LIST_CODE
          ,LV3_PROD_LIST_CODE	  
          ,LV4_PROD_LIST_CODE	
          ,LV4_PROD_LIST_CN_NAME		  
          ,SPART_CODE
          ,SPART_CN_NAME
          ,USD_PNP_AMT     
          ,OVERSEA_FLAG
          ,REGION_CODE
          ,REGION_CN_NAME
          ,REPOFFICE_CODE
          ,REPOFFICE_CN_NAME
          ,SIGN_TOP_CUST_CATEGORY_CODE
          ,SIGN_TOP_CUST_CATEGORY_CN_NAME
          ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
          ,VIEW_FLAG
		  ,BG_CODE                           
          ,BG_CN_NAME                       
    FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T
	WHERE UPPER(APPEND_FLAG) = 'N'
	  AND UPPER(DEL_FLAG)  = 'N'
	  AND VERSION_ID = (SELECT MAX(VERSION_ID) AS VERSION_ID  
                         FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
                        WHERE DEL_FLAG = 'N'
                          AND STATUS = 1
                          AND UPPER(DATA_TYPE) = 'ANNUAL')
       ),
	   PRICE_BASE_CUS_DIM_TMP AS (
    SELECT CUSTOM_ID
          ,CUSTOM_CN_NAME
          ,trim(regexp_split_to_table(REPLACE(lv_code,'''',''), ',')) as LV_CODE        
          ,LV_CN_NAME     
		  ,PARENT_LEVEL
          ,SPART_CODE     
          ,SPART_CN_NAME  
          ,GROUP_LEVEL    
          ,OVERSEA_FLAG
          ,REGION_CODE
          ,REGION_CN_NAME
          ,REPOFFICE_CODE
          ,REPOFFICE_CN_NAME
          ,SIGN_TOP_CUST_CATEGORY_CODE
          ,SIGN_TOP_CUST_CATEGORY_CN_NAME
          ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
          ,VIEW_FLAG
		  ,BG_CODE                           
          ,BG_CN_NAME 
	FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T
	WHERE 1=1
	  AND UPPER(PAGE_FLAG) = 'MONTH'
	  AND UPPER(DEL_FLAG)  = 'N'
	  AND CUSTOM_ID = F_CUSTOM_ID
	  )
    SELECT V_VERSION_ID        AS VERSION_ID
          ,T1.PERIOD_YEAR
          ,T2.CUSTOM_ID
          ,T2.CUSTOM_CN_NAME
		  ,T1.LV4_PROD_LIST_CODE	
          ,T1.LV4_PROD_LIST_CN_NAME
          ,T1.SPART_CODE       AS GROUP_CODE
          ,T1.SPART_CN_NAME    AS GROUP_CN_NAME
          ,'SPART'             AS GROUP_LEVEL
		  ,T2.LV_CODE          AS PARENT_CODE
          ,T2.LV_CN_NAME       AS PARENT_CN_NAME
		  ,T2.PARENT_LEVEL     AS PARENT_LEVEL
          ,SUM(T1.USD_PNP_AMT) / NULLIF(SUM(SUM(T1.USD_PNP_AMT)) OVER(PARTITION BY T1.VERSION_ID
                                                                            ,T1.PERIOD_YEAR
                                                                            ,T2.CUSTOM_ID	                        
		                                                                    ,T2.CUSTOM_CN_NAME 	
		                                                                    ,T2.LV_CODE        	        
		                                                                    ,T2.LV_CN_NAME																		
                                                                            ,T1.OVERSEA_FLAG
                                                                            ,T1.REGION_CODE
                                                                            ,T1.REGION_CN_NAME
                                                                            ,T1.REPOFFICE_CODE
                                                                            ,T1.REPOFFICE_CN_NAME
                                                                            ,T1.SIGN_TOP_CUST_CATEGORY_CODE
                                                                            ,T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
                                                                            ,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                                                                            ,T1.VIEW_FLAG), 0)	AS WEIGHT_RATE  
          ,T1.OVERSEA_FLAG
          ,T1.REGION_CODE
          ,T1.REGION_CN_NAME
          ,T1.REPOFFICE_CODE
          ,T1.REPOFFICE_CN_NAME
          ,T1.SIGN_TOP_CUST_CATEGORY_CODE
          ,T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
          ,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
          ,T1.VIEW_FLAG
		  ,T1.BG_CODE                           
          ,T1.BG_CN_NAME 
    FROM  PRICE_ANNL_AVG_TMP  T1
	INNER JOIN PRICE_BASE_CUS_DIM_TMP T2
	ON   T1.SPART_CODE = T2.SPART_CODE 
	AND  NVL(T1.OVERSEA_FLAG,'SNULL')   = NVL(T2.OVERSEA_FLAG,'SNULL')
    AND  NVL(T1.REGION_CODE,'SNULL')    = NVL(T2.REGION_CODE,'SNULL')
    AND  NVL(T1.REPOFFICE_CODE,'SNULL') = NVL(T2.REPOFFICE_CODE,'SNULL')
	AND  NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')      = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')
	AND  NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL')
	AND  NVL(T1.VIEW_FLAG,'SNULL')      = NVL(T2.VIEW_FLAG,'SNULL')
	AND  NVL(T1.BG_CODE,'SNULL')        = NVL(T2.BG_CODE,'SNULL')
	WHERE INSTR(V_LV_CODE,DECODE(T2.PARENT_LEVEL,
                                  'LV0',T1.LV0_PROD_LIST_CODE,
                                  'LV1',T1.LV1_PROD_LIST_CODE,
                                  'LV2',T1.LV2_PROD_LIST_CODE,
                                  'LV3',T1.LV3_PROD_LIST_CODE,
	                              'LV4',T1.LV4_PROD_LIST_CODE) 
				)>0
	GROUP BY T1.VERSION_ID
	      ,T1.PERIOD_YEAR
          ,T2.CUSTOM_ID
          ,T2.CUSTOM_CN_NAME
		  ,T1.LV4_PROD_LIST_CODE	
          ,T1.LV4_PROD_LIST_CN_NAME
          ,T1.SPART_CODE       
          ,T1.SPART_CN_NAME                
		  ,T2.LV_CODE          
          ,T2.LV_CN_NAME       
		  ,T2.PARENT_LEVEL     
		  ,T1.OVERSEA_FLAG
          ,T1.REGION_CODE
          ,T1.REGION_CN_NAME
          ,T1.REPOFFICE_CODE
          ,T1.REPOFFICE_CN_NAME
          ,T1.SIGN_TOP_CUST_CATEGORY_CODE
          ,T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
          ,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
          ,T1.VIEW_FLAG
		  ,T1.BG_CODE                           
          ,T1.BG_CN_NAME 
		  ;
		  
		  
		  
		   --写入日志
        V_STEP_MUM := V_STEP_MUM + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '取 虚化维度 和 SPART层级的真实金额,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_FORMULA_SQL_TXT => NULL,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS'); 
		  
		  DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T WHERE VERSION_ID = V_VERSION_ID AND CUSTOM_ID = F_CUSTOM_ID ;
		  
		  -- 计算 虚化层级的权重，入到 虚化后月累计权重 结果表
		  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T(
		  VERSION_ID                            
         ,PERIOD_YEAR
         ,CUSTOM_ID
         ,CUSTOM_CN_NAME
		 ,LV4_PROD_LIST_CODE	
         ,LV4_PROD_LIST_CN_NAME
         ,GROUP_CODE
         ,GROUP_CN_NAME
         ,GROUP_LEVEL
		 ,PARENT_CODE
         ,PARENT_CN_NAME
         ,WEIGHT_RATE
         ,OVERSEA_FLAG
         ,REGION_CODE
         ,REGION_CN_NAME
         ,REPOFFICE_CODE
         ,REPOFFICE_CN_NAME
         ,SIGN_TOP_CUST_CATEGORY_CODE
         ,SIGN_TOP_CUST_CATEGORY_CN_NAME
         ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
         ,VIEW_FLAG
		 ,BG_CODE                           
         ,BG_CN_NAME
         ,CREATED_BY
         ,CREATION_DATE
         ,LAST_UPDATED_BY
         ,LAST_UPDATE_DATE
         ,DEL_FLAG		  
		  )
		 SELECT VERSION_ID	                    
		       ,PERIOD_YEAR	                    
		       ,CUSTOM_ID	                        
		       ,CUSTOM_CN_NAME 
               ,LV4_PROD_LIST_CODE	
               ,LV4_PROD_LIST_CN_NAME			   
		       ,GROUP_CODE	                    
		       ,GROUP_CN_NAME	                    
		       ,GROUP_LEVEL	                    
		       ,PARENT_CODE	         	        
		       ,PARENT_CN_NAME	            	
		       ,WEIGHT_RATE                    
		       ,OVERSEA_FLAG	                    
		       ,REGION_CODE	                    
		       ,REGION_CN_NAME	                
		       ,REPOFFICE_CODE	                
		       ,REPOFFICE_CN_NAME	                
		       ,SIGN_TOP_CUST_CATEGORY_CODE	    
		       ,SIGN_TOP_CUST_CATEGORY_CN_NAME	
		       ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	
		       ,VIEW_FLAG	
               ,BG_CODE                           
               ,BG_CN_NAME			   
		       ,-1 AS CREATED_BY
	           ,CURRENT_TIMESTAMP AS CREATION_DATE
	           ,-1 AS LAST_UPDATED_BY
	           ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
	           ,'N' AS DEL_FLAG		 
        FROM PRICE_BASE_MON_WEIGHT_TMP1		 
		
		     ;
		  
		  
		  --写入日志
        V_STEP_MUM := V_STEP_MUM + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '虚化层级的权重计算完成,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_FORMULA_SQL_TXT => NULL,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS'); 
		  
		  
		   RETURN 'SUCCESS';
 
          --收集统计信息
        ANALYSE FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T;


        EXCEPTION
          WHEN OTHERS THEN
          X_RESULT_STATUS := '0';
          
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
          (F_SP_NAME => V_SP_NAME, 
           F_STEP_NUM => V_STEP_MUM,
           F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_MUM||'步'||'运行失败,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID, 
           F_RESULT_STATUS => X_RESULT_STATUS, 
           F_ERRBUF => SQLSTATE||':'||SQLERRM
           );




end;
$$
/

