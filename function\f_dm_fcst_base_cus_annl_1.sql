-- Name: f_dm_fcst_base_cus_annl; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_base_cus_annl(f_cost_type character varying, f_granularity_type character varying, f_ytd_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年7月4日
  创建人  ：唐钦
  背景描述：根据年均本计算之后的表，取得对应的金额字段，按不同层级汇总，计算得出权重值
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_BASE_CUS_ANNL('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_BASE_CUS_ANNL'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_YEAR BIGINT := TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY');   -- 当前年份
  V_LAST_YEAR_FLAG varchar(100);
  V_YEAR_FLAG VARCHAR(50);
  V_TO_AMP_TABLE VARCHAR(100);
  V_TO_STATUS_TABLE VARCHAR(100);
  V_TO_WEIGHT_TABLE VARCHAR(100);
  V_FROM_AVG_TABLE VARCHAR(100);
  V_FROM_AMP_TABLE VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_TMP1_TABLE VARCHAR(100);
  V_SQL_DE_AMT VARCHAR(200);
  V_LV_CODE VARCHAR(100);
  V_PBI_LEVEL TEXT;
  V_IN_SOFTWARE VARCHAR(50);
  V_INTO_SOFTWARE VARCHAR(50);
  V_REL_SOFTWARE VARCHAR(200);
  V_YTD_FLAG VARCHAR(50);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 判断F_YTD_FLAG标识是年度还是年度YTD，定义不同变量值
  IF F_YTD_FLAG = 'Y' THEN    -- 年度YTD
     V_YTD_FLAG := '_YTD';
  ELSIF F_YTD_FLAG = 'N' THEN    -- 年度
     NULL;
  END IF;
   
  -- 根据入参，对变量进行不同定义
     V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_AMT_TMP';
     V_TMP1_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BYYEAR_TMP';
     V_FROM_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
     V_FROM_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL'||V_YTD_FLAG||'_AMP_T';
     V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL'||V_YTD_FLAG||'_WEIGHT_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL'||V_YTD_FLAG||'_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL'||V_YTD_FLAG||'_STATUS_T';
     V_REL_SOFTWARE := 'AND NVL(T1.SOFTWARE_MARK,''S2'') = NVL(T2.SOFTWARE_MARK,''S2'')';
     V_IN_SOFTWARE := 'SOFTWARE_MARK,';
     V_INTO_SOFTWARE := 'T1.SOFTWARE_MARK,';
  IF F_COST_TYPE = 'PSP' THEN    -- PSP成本类型
     V_SQL_DE_AMT := 'RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_DE_AMT := 'GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,';   -- 解密金额
  END IF;
  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV_CODE := 'PROD_RND_TEAM_CODE';
     V_PBI_LEVEL := 'LV0_PROD_RND_TEAM_CODE,
                     LV0_PROD_RD_TEAM_CN_NAME,
                     LV1_PROD_RND_TEAM_CODE,
                     LV1_PROD_RD_TEAM_CN_NAME,
                     LV2_PROD_RND_TEAM_CODE,
                     LV2_PROD_RD_TEAM_CN_NAME,
                     LV3_PROD_RND_TEAM_CODE,
                     LV3_PROD_RD_TEAM_CN_NAME,
                     LV4_PROD_RND_TEAM_CODE,
                     LV4_PROD_RD_TEAM_CN_NAME,';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV_CODE := 'INDUSTRY_CATG_CODE'; 
     V_PBI_LEVEL := 'LV0_INDUSTRY_CATG_CODE,
                     LV0_INDUSTRY_CATG_CN_NAME,
                     LV1_INDUSTRY_CATG_CODE,
                     LV1_INDUSTRY_CATG_CN_NAME,
                     LV2_INDUSTRY_CATG_CODE,
                     LV2_INDUSTRY_CATG_CN_NAME,
                     LV3_INDUSTRY_CATG_CODE,
                     LV3_INDUSTRY_CATG_CN_NAME,
                     LV4_INDUSTRY_CATG_CODE,
                     LV4_INDUSTRY_CATG_CN_NAME,';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV_CODE := 'PROD_LIST_CODE'; 
     V_PBI_LEVEL := 'LV0_PROD_LIST_CODE,
                     LV0_PROD_LIST_CN_NAME,
                     LV1_PROD_LIST_CODE,
                     LV1_PROD_LIST_CN_NAME,
                     LV2_PROD_LIST_CODE,
                     LV2_PROD_LIST_CN_NAME,
                     LV3_PROD_LIST_CODE,
                     LV3_PROD_LIST_CN_NAME,
                     LV4_PROD_LIST_CODE,
                     LV4_PROD_LIST_CN_NAME,';
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID IS NULL';
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_AMP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID IS NULL';
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_STATUS_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID IS NULL';

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'，'||V_TO_WEIGHT_TABLE||'、'||V_TO_AMP_TABLE||'、'||V_TO_STATUS_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建金额临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
         PERIOD_YEAR                   INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         RMB_COST_AMT                  VARCHAR(200),
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10),
         SOFTWARE_MARK                 VARCHAR(50)
        )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP_TABLE||'表成功');
     
  -- 将金额数据解密，插入临时表
  V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
         PERIOD_YEAR,                  
         LV0_CODE,                     
         LV0_CN_NAME,                     
         LV1_CODE,                     
         LV1_CN_NAME,                     
         LV2_CODE,                     
         LV2_CN_NAME,                     
         LV3_CODE,                     
         LV3_CN_NAME,                     
         LV4_CODE,                     
         LV4_CN_NAME,                     
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         SPART_CODE,                   
         SPART_CN_NAME,                
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         '||V_IN_SOFTWARE||'
         RMB_COST_AMT,                 
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
    )
  SELECT PERIOD_YEAR,                  
         '||V_PBI_LEVEL||'
         DIMENSION_CODE, 
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         SPART_CODE,                   
         SPART_CN_NAME,                
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         '||V_IN_SOFTWARE||'
         '||V_SQL_DE_AMT||'
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
      FROM '||V_FROM_AVG_TABLE||'
      WHERE MAIN_FLAG = ''Y''
      AND VERSION_ID = '||V_VERSION_ID||'       -- 取出所有主力编码的数据
      AND YTD_FLAG = '''||F_YTD_FLAG||'''';   -- 202410新增根据YTD_FLAG标识取不同数据
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将解密数据存放入临时表：'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 计算预处理的权重数据值
  V_SQL := '
  INSERT INTO '||V_TO_WEIGHT_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         ABSOLUTE_PARENT_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         LV4_CODE,
         LV4_CN_NAME
    )
  WITH PARENT_AMT_TMP AS (
  SELECT PERIOD_YEAR,
         SPART_CODE AS GROUP_CODE,       
         SPART_CN_NAME AS GROUP_CN_NAME,  
         ''SPART'' AS GROUP_LEVEL,
         NVL(RMB_COST_AMT,0) AS RMB_COST_AMT,
         SUM(NVL(RMB_COST_AMT,0)) OVER(PARTITION BY PERIOD_YEAR,SPART_CODE,GROUP_LEVEL,LV0_CODE,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,'||V_IN_SOFTWARE||'VIEW_FLAG,MAIN_FLAG,CODE_ATTRIBUTES) AS PARENT_AMT,
         LV0_CODE AS PARENT_CODE,
         LV0_CN_NAME AS PARENT_CN_NAME,
         ''LV0'' AS PARENT_LEVEL,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         LV4_CODE,
         LV4_CN_NAME
      FROM '||V_TMP_TABLE||'
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,       
         GROUP_CN_NAME,  
         GROUP_LEVEL,
         DECODE(PARENT_AMT,0,0,RMB_COST_AMT/PARENT_AMT)AS WEIGHT_RATE,
         PARENT_AMT AS ABSOLUTE_PARENT_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         LV4_CODE,
         LV4_CN_NAME
      FROM PARENT_AMT_TMP';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将预处理的权重数据插入对应权重表：'||V_TO_WEIGHT_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 计算预处理的涨跌幅数据
  V_SQL := '
  INSERT INTO '||V_TO_AMP_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
        )
  WITH BASE_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP*T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
         T2.PARENT_CODE,
         T2.PARENT_CN_NAME,
         T2.PARENT_LEVEL,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         '||V_INTO_SOFTWARE||'
         T1.VIEW_FLAG,
         T1.MAIN_FLAG,
         T1.CODE_ATTRIBUTES
      FROM '||V_FROM_AMP_TABLE||' T1
      INNER JOIN '||V_TO_WEIGHT_TABLE||' T2
      ON T1.VERSION_ID = T2.VERSION_ID 
      AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.GROUP_CODE = T2.GROUP_CODE 
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.'||V_LV_CODE||' = T2.LV4_CODE 
      AND T1.REGION_CODE = T2.REGION_CODE
      AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.MAIN_FLAG = T2.MAIN_FLAG
      AND T1.CODE_ATTRIBUTES = T2.CODE_ATTRIBUTES
      '||V_REL_SOFTWARE||'
      WHERE T1.MAIN_FLAG = ''Y''
      AND T1.GROUP_LEVEL = ''SPART''
      AND T1.VERSION_ID = '||V_VERSION_ID||'
      AND T2.CUSTOM_ID IS NULL
      AND T2.GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
    )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SUM(ANNUAL_AMP_WEIGHT) AS ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM BASE_AMP_TMP
      GROUP BY PERIOD_YEAR,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               PARENT_CODE,
               PARENT_CN_NAME,
               PARENT_LEVEL,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               BG_CODE,
               BG_CN_NAME,
               OVERSEA_FLAG,
               '||V_IN_SOFTWARE||'
               VIEW_FLAG,
               MAIN_FLAG,
               CODE_ATTRIBUTES
            ';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;
     
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将涨跌幅数据进行处理后插入到结果表：'||V_TO_AMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建预处理状态码临时表
  V_SQL := '
  DROP TABLE IF EXISTS '||V_TMP1_TABLE||';
  CREATE TEMPORARY TABLE '||V_TMP1_TABLE||'(
      PARENT_CODE            VARCHAR(50),
      PARENT_CN_NAME         VARCHAR(1000),
      PARENT_LEVEL           VARCHAR(50),
      GROUP_CODE             VARCHAR(50), 
      GROUP_CN_NAME          VARCHAR(200),
      GROUP_LEVEL            VARCHAR(50), 
      LAST_THREE_YEAR_FLAG   BIGINT,
      LAST_THREE_APPEND_YEAR BIGINT,
      LAST_TWO_YEAR_FLAG     BIGINT,
      LAST_TWO_APPEND_YEAR   BIGINT,
      LAST_YEAR_FLAG         BIGINT,
      LAST_APPEND_YEAR       BIGINT,
      CURRENT_YEAR_FLAG      BIGINT,
      CURRENT_APPEND_YEAR    BIGINT,
      REGION_CODE            VARCHAR(50),
      REGION_CN_NAME         VARCHAR(200),
      REPOFFICE_CODE         VARCHAR(50),
      REPOFFICE_CN_NAME      VARCHAR(200),
      BG_CODE                VARCHAR(50),
      BG_CN_NAME             VARCHAR(200),
      OVERSEA_FLAG           VARCHAR(2),
      VIEW_FLAG              VARCHAR(50),
      MAIN_FLAG              VARCHAR(2),
      CODE_ATTRIBUTES        VARCHAR(50),
      SOFTWARE_MARK          VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH (GROUP_CODE)'; 
  EXECUTE IMMEDIATE V_SQL;
   
  -- 计算预处理的状态码数据值
  V_SQL := '
  INSERT INTO '||V_TMP1_TABLE||'(
         PARENT_CODE,           
         PARENT_CN_NAME,        
         PARENT_LEVEL,          
         GROUP_CODE,            
         GROUP_CN_NAME,         
         GROUP_LEVEL,           
         LAST_THREE_YEAR_FLAG,  
         LAST_TWO_YEAR_FLAG,    
         LAST_YEAR_FLAG,        
         CURRENT_YEAR_FLAG,     
         REGION_CODE,           
         REGION_CN_NAME,        
         REPOFFICE_CODE,        
         REPOFFICE_CN_NAME,     
         BG_CODE,               
         BG_CN_NAME,            
         OVERSEA_FLAG,          
         '||V_IN_SOFTWARE||'
         VIEW_FLAG,             
         MAIN_FLAG,             
         CODE_ATTRIBUTES
    )
  WITH DISINNCT_DIM_TMP AS(
  SELECT DISTINCT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ABSOLUTE_PARENT_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         '||V_IN_SOFTWARE||'
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES
      FROM '||V_TO_WEIGHT_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND CUSTOM_ID IS NULL
      AND GRANULARITY_TYPE = '''||F_GRANULARITY_TYPE||'''
  )
  SELECT PARENT_CODE,           
         PARENT_CN_NAME,        
         PARENT_LEVEL,          
         GROUP_CODE,            
         GROUP_CN_NAME,         
         GROUP_LEVEL, 
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(ABSOLUTE_PARENT_AMT,0,1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋0，否则赋1
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(ABSOLUTE_PARENT_AMT,0,1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,    -- 为当年-2年时，赋予该年数据为补齐时赋0，否则赋1
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(ABSOLUTE_PARENT_AMT,0,1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
         SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(ABSOLUTE_PARENT_AMT,0,1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,
         REGION_CODE,           
         REGION_CN_NAME,        
         REPOFFICE_CODE,        
         REPOFFICE_CN_NAME,     
         BG_CODE,               
         BG_CN_NAME,            
         OVERSEA_FLAG,          
         '||V_IN_SOFTWARE||'
         VIEW_FLAG,             
         MAIN_FLAG,             
         CODE_ATTRIBUTES
       FROM DISINNCT_DIM_TMP
       GROUP BY PARENT_CODE,
                PARENT_CN_NAME,
                PARENT_LEVEL,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                GRANULARITY_TYPE,
                REGION_CODE,           
                REGION_CN_NAME,        
                REPOFFICE_CODE,        
                REPOFFICE_CN_NAME,     
                BG_CODE,               
                BG_CN_NAME,            
                OVERSEA_FLAG,          
                '||V_IN_SOFTWARE||'
                VIEW_FLAG,             
                MAIN_FLAG,             
                CODE_ATTRIBUTES';
     
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;   
    DBMS_OUTPUT.PUT_LINE('状态码各年数据情况插入临时表');            
  
  -- 对ITEM层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
    IF YEAR_FLAG = V_YEAR-2 THEN
       V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
       V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
    ELSIF YEAR_FLAG = V_YEAR-1 THEN
       V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
       V_YEAR_FLAG := 'LAST_YEAR_FLAG';
    ELSIF YEAR_FLAG = V_YEAR THEN
       V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
       V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
    ELSE NULL;
    END IF;
  
  -- ITEM层级年度涨跌幅状态码数据计算逻辑
  V_SQL := '
   INSERT INTO '||V_TO_STATUS_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
      ) 
   SELECT '||V_VERSION_ID||' AS VERSION_ID,
          '||YEAR_FLAG||' AS PERIOD_YEAR,   -- 循环的年份即是当次计算的年份
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
               WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 THEN 4
          END AS STATUS_CODE,
          PARENT_CODE,
          PARENT_CN_NAME,
          PARENT_LEVEL,
          '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
          REGION_CODE,
          REGION_CN_NAME,
          REPOFFICE_CODE,
          REPOFFICE_CN_NAME,
          BG_CODE,
          BG_CN_NAME,
          OVERSEA_FLAG,
          VIEW_FLAG,
          MAIN_FLAG,
          CODE_ATTRIBUTES,
          '||V_IN_SOFTWARE||'
          -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
       FROM '||V_TMP1_TABLE;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    DBMS_OUTPUT.PUT_LINE('状态码逻辑执行前');
    EXECUTE IMMEDIATE V_SQL;   
    DBMS_OUTPUT.PUT_LINE('状态码逻辑执行后');    
       
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '预处理状态码数据插入结果表'||V_TO_STATUS_TABLE,
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  END LOOP;   -- 结束循环
  
  EXECUTE IMMEDIATE 'ANALYSE '||V_TO_WEIGHT_TABLE;
  EXECUTE IMMEDIATE 'ANALYSE '||V_TO_AMP_TABLE;
  EXECUTE IMMEDIATE 'ANALYSE '||V_TO_STATUS_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

END$$
/

