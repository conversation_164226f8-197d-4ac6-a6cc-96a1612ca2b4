-- Name: f_dm_fom_month_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_cost_idx_t(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最后更新时间: 2024年4月15日15点21分
修改人: 黄心蕊 HWX1187045
修改内容: 202405版本修改 指数权重时间范围前滚12个月 加入USE_TYPE字段判断
创建时间：2023-12-08
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-权重表数据初始化
修改时间：2024-02-04
修改人  ：黄心蕊 HWX1187045
背景描述：202403版本新增分层级判断指数是否补齐逻辑
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T 指数中间表
		FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T 月度分析权重表
目标表：FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T 月度分析指数表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_COST_IDX_T('E',''); --EMS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_COST_IDX_T('M',''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_MONTH_COST_IDX_T';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;
  V_BASE_PERIOD_ID INT4 := TO_NUMBER(YEAR(CURRENT_DATE)-1||'01');

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
	SELECT VERSION_ID
	  INTO V_VERSION
	  FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	 WHERE UPPER(VERSION_TYPE) IN ('AUTO', 'FINAL')
	   AND UPPER(DATA_TYPE) = 'MONTH'
	   AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
 
--1.删除同版本同口径数据 
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T WHERE VERSION_ID = V_VERSION AND CALIBER_FLAG = V_CALIBER_FLAG;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'''||V_CALIBER_FLAG||''' 口径数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  V_STEP_NUM := V_STEP_NUM + 1;
--2.指数计算
--2.1 制造对象指数计算
  WITH BASE_IDX AS
   (SELECT BASE_PERIOD_ID,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_IDX,
		   DECODE(APPEND_FLAG,'N',1,0) AS APPEND_FLAG  --202403版本新增补齐逻辑判断
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND NVL(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'SNULL') <> 'SNULL' --排除海思与云核心网的ITEM数据
       AND TOP_FLAG = 'Y'
	   AND GROUP_LEVEL = 'ITEM'),
	   
  BASE_WEIGHT AS
   (SELECT LV0_CODE,
           LV1_CODE,
           BUSSINESS_OBJECT_CODE,
           SHIPPING_OBJECT_CODE,
           MANUFACTURE_OBJECT_CODE,
           GROUP_CODE,
           WEIGHT_RATE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND USE_TYPE = 'I' --202405版本修改 加入USE_TYPE字段判断
	   AND GROUP_LEVEL = 'ITEM' 
	   AND NVL(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'SNULL') <> 'SNULL' --排除海思与云核心网的ITEM数据
	   AND VERSION_ID = V_VERSION )
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_IDX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 APPEND_FLAG)  --202403版本新增补齐逻辑判断
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.BUSSINESS_OBJECT_CODE,
         T1.BUSSINESS_OBJECT_CN_NAME,
         T1.SHIPPING_OBJECT_CODE,
         T1.SHIPPING_OBJECT_CN_NAME,
         T1.MANUFACTURE_OBJECT_CODE,
         T1.MANUFACTURE_OBJECT_CN_NAME,
         T1.MANUFACTURE_OBJECT_CODE AS GROUP_CODE,
         T1.MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME,
         'MANUFACTURE_OBJECT' AS GROUP_LEVEL,
         SUM(T1.COST_IDX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.SHIPPING_OBJECT_CODE AS PARENT_CODE,
         T1.SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         V_CALIBER_FLAG AS CALIBER_FLAG,
		 DECODE(SUM(APPEND_FLAG),0,'Y','N') AS APPEND_FLAG  --202403版本新增补齐逻辑判断
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.LV0_CODE = T2.LV0_CODE
     AND T1.LV1_CODE = T2.LV1_CODE
     AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
     AND T1.SHIPPING_OBJECT_CODE = T2.SHIPPING_OBJECT_CODE
     AND T1.MANUFACTURE_OBJECT_CODE = T2.MANUFACTURE_OBJECT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
   GROUP BY T1.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.BUSSINESS_OBJECT_CODE,
            T1.BUSSINESS_OBJECT_CN_NAME,
            T1.SHIPPING_OBJECT_CODE,
            T1.SHIPPING_OBJECT_CN_NAME,
            T1.MANUFACTURE_OBJECT_CODE,
            T1.MANUFACTURE_OBJECT_CN_NAME;

  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '制造对象指数卷积完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  V_STEP_NUM := V_STEP_NUM + 1;
--2.2 发货对象指数计算
  WITH BASE_IDX AS
   (SELECT BASE_PERIOD_ID,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_IDX,
		   DECODE(APPEND_FLAG,'N',1,0) AS APPEND_FLAG   --202403版本新增补齐逻辑判断
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'MANUFACTURE_OBJECT' ),
	   
  BASE_WEIGHT AS
   (SELECT LV0_CODE,
           LV1_CODE,
           BUSSINESS_OBJECT_CODE,
           SHIPPING_OBJECT_CODE,
           GROUP_CODE,
           WEIGHT_RATE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND USE_TYPE = 'I' --202405版本修改 加入USE_TYPE字段判断
	   AND GROUP_LEVEL = 'MANUFACTURE_OBJECT'
	   AND VERSION_ID = V_VERSION	   )
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_IDX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 APPEND_FLAG)  --202403版本新增补齐逻辑判断
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.BUSSINESS_OBJECT_CODE,
         T1.BUSSINESS_OBJECT_CN_NAME,
         T1.SHIPPING_OBJECT_CODE,
         T1.SHIPPING_OBJECT_CN_NAME,
         T1.SHIPPING_OBJECT_CODE AS GROUP_CODE,
         T1.SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
         'SHIPPING_OBJECT' AS GROUP_LEVEL,
         SUM(T1.COST_IDX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.BUSSINESS_OBJECT_CODE AS PARENT_CODE,
         T1.BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         V_CALIBER_FLAG AS CALIBER_FLAG,
		 DECODE(SUM(APPEND_FLAG),0,'Y','N') AS APPEND_FLAG --202403版本新增补齐逻辑判断
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.LV0_CODE = T2.LV0_CODE
     AND T1.LV1_CODE = T2.LV1_CODE
     AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
     AND T1.SHIPPING_OBJECT_CODE = T2.SHIPPING_OBJECT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
   GROUP BY T1.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.BUSSINESS_OBJECT_CODE,
            T1.BUSSINESS_OBJECT_CN_NAME,
            T1.SHIPPING_OBJECT_CODE,
            T1.SHIPPING_OBJECT_CN_NAME;

  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '发货对象指数卷积完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 

  
  V_STEP_NUM:= V_STEP_NUM+1;
--2.3 经营对象指数计算
  WITH BASE_IDX AS
   (SELECT BASE_PERIOD_ID,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_IDX,
		   DECODE(APPEND_FLAG,'N',1,0) AS APPEND_FLAG  --202403版本新增补齐逻辑判断
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND (GROUP_LEVEL = 'SHIPPING_OBJECT' 
			OR (GROUP_LEVEL = 'ITEM' AND NVL(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL' ))
	   ),  --加入海思与云核心网的计算
	   
  BASE_WEIGHT AS
   (SELECT LV0_CODE,
           LV1_CODE,
           BUSSINESS_OBJECT_CODE,
           GROUP_CODE,
		   GROUP_LEVEL,
           WEIGHT_RATE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND USE_TYPE = 'I' --202405版本修改 加入USE_TYPE字段判断
	   AND (GROUP_LEVEL = 'SHIPPING_OBJECT' 
			OR (GROUP_LEVEL = 'ITEM' AND NVL(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL' ))
	   AND VERSION_ID = V_VERSION	   ) --加入海思与云核心网的计算
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_IDX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 APPEND_FLAG) --202403版本新增补齐逻辑判断
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.BUSSINESS_OBJECT_CODE,
         T1.BUSSINESS_OBJECT_CN_NAME,
         T1.BUSSINESS_OBJECT_CODE AS GROUP_CODE,
         T1.BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
         'BUSSINESS_OBJECT' AS GROUP_LEVEL,
         SUM(T1.COST_IDX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.LV1_CODE AS PARENT_CODE,
         T1.LV1_CN_NAME AS PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         V_CALIBER_FLAG AS CALIBER_FLAG,
		 DECODE(SUM(APPEND_FLAG),0,'Y','N') AS APPEND_FLAG --202403版本新增补齐逻辑判断
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.LV0_CODE = T2.LV0_CODE
     AND T1.LV1_CODE = T2.LV1_CODE
     AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
	 AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
   GROUP BY T1.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.BUSSINESS_OBJECT_CODE,
            T1.BUSSINESS_OBJECT_CN_NAME;

  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '经营对象指数卷积完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  V_STEP_NUM:= V_STEP_NUM+1;
--2.4 LV1指数计算
  WITH BASE_IDX AS
   (SELECT BASE_PERIOD_ID,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_IDX,
		   DECODE(APPEND_FLAG,'N',1,0) AS APPEND_FLAG  --202403版本新增补齐逻辑判断
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'BUSSINESS_OBJECT' ),
	   
  BASE_WEIGHT AS
   (SELECT LV0_CODE,
           LV1_CODE,
           GROUP_CODE,
           WEIGHT_RATE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'BUSSINESS_OBJECT'
	   AND VERSION_ID = V_VERSION
	   AND USE_TYPE = 'I' --202405版本修改 加入USE_TYPE字段判断
	   )
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
	 LV1_CODE,
	 LV1_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_IDX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 APPEND_FLAG)  --202403版本新增补齐逻辑判断
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV1_CODE AS GROUP_CODE,
         T1.LV1_CN_NAME AS GROUP_CN_NAME,
         'LV1' AS GROUP_LEVEL,
         SUM(T1.COST_IDX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.LV0_CODE AS PARENT_CODE,
         T1.LV0_CN_NAME AS PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         V_CALIBER_FLAG AS CALIBER_FLAG,
		 DECODE(SUM(APPEND_FLAG),0,'Y','N') AS APPEND_FLAG --202403版本新增补齐逻辑判断
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.LV0_CODE = T2.LV0_CODE
     AND T1.LV1_CODE = T2.LV1_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
   GROUP BY T1.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME;

  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => 'LV1指数卷积完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  
  V_STEP_NUM:= V_STEP_NUM+1;
--2.5 LV0指数计算
  WITH BASE_IDX AS
   (SELECT BASE_PERIOD_ID,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_IDX,
		   DECODE(APPEND_FLAG,'N',1,0) AS APPEND_FLAG  --202403版本新增补齐逻辑判断
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'LV1' ),
	   
  BASE_WEIGHT AS
   (SELECT LV0_CODE,
           LV1_CODE,
           GROUP_CODE,
           WEIGHT_RATE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'LV1'
	   AND VERSION_ID = V_VERSION	   
	   AND USE_TYPE = 'I' --202405版本修改 加入USE_TYPE字段判断
	   )
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_IDX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 APPEND_FLAG)  --202403版本新增补齐逻辑判断
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV0_CODE AS GROUP_CODE,
         T1.LV0_CN_NAME AS GROUP_CN_NAME,
         'LV0' AS GROUP_LEVEL,
         SUM(T1.COST_IDX * T2.WEIGHT_RATE) AS COST_INDEX,
         '' AS PARENT_CODE,
         '' AS PARENT_CN_NAME,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         V_CALIBER_FLAG AS CALIBER_FLAG,
		 DECODE(SUM(APPEND_FLAG),0,'Y','N') AS APPEND_FLAG --202403版本新增补齐逻辑判断
    FROM BASE_IDX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.LV0_CODE = T2.LV0_CODE
     AND T1.LV1_CODE = T2.LV1_CODE
     AND T1.GROUP_CODE = T2.GROUP_CODE
   GROUP BY T1.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME;

--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => 'LV0指数卷积完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
--3. 结果表插数
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     BASE_PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CALIBER_FLAG,
	 APPEND_FLAG)
    SELECT V_VERSION AS VERSION_ID,
           LEFT(PERIOD_ID,4) AS PERIOD_YEAR,
           PERIOD_ID,
           V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_IDX,
           PARENT_CODE,
           PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           V_CALIBER_FLAG AS CALIBER_FLAG,
		   APPEND_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T;
	  
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '指数表插数完成',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
  
 ANALYZE FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

