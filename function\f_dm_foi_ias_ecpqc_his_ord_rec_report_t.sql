-- Name: f_dm_foi_ias_ecpqc_his_ord_rec_report_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_ias_ecpqc_his_ord_rec_report_t(f_caliber_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*

创建人  ：唐钦
创建时间：2024年6月20日11点44分
背景描述：1. IAS&华东采购-实际数历史数表关联维表
专家团维表     DM_DIM_CEG_D
库存组织维表 DWR_DIM_INVENTORY_ORG_D
物料维表     DWR_DIM_MATERIAL_CODE_D
供应商维表     DWR_DIM_SUPPLIER_D
参数描述：F_CALIBER_FLAG ：口径标识（IAS：IAS/华东采购：EAST_CHINA_PQC）
          X_RESULT_STATUS ：是否成功
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T(IAS);
*/
DECLARE
  V_SP_NAME    VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 分别由IAS/华东采购版本信息表取数
  V_ITEM_VERSION_ID BIGINT ; --新版本号ID,分别由IAS/华东采购版本信息表取数
  V_VERSION_NAME VARCHAR(100) ; --新的版本中文名称
  V_ITEM_VERSION_NAME VARCHAR(100) ; --新的版本中文名称
  V_PARENT_VERSION BIGINT;
  V_DIM_VERSION_ID BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_VERSION_TABLE VARCHAR(100) ;  -- 版本信息表
  V_SEQUENCE VARCHAR(100) ;  -- 版本序列
  V_SQL_JOIN VARCHAR(300) ;
  V_REL_DIM VARCHAR(200) ; 
  V_IN_ITEM VARCHAR(100) ; 
  V_SQL_CONDITION VARCHAR(500) ; 
  V_ECPQC_DIM_VERSION INT;
  V_IAS_DIM_VERSION INT;
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --清空目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||',清空 FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    
  -- 查询华东采购/IAS的映射维表版本号/ITEM维表版本号
  SELECT VERSION_ID INTO V_ECPQC_DIM_VERSION   -- 华东采购
    FROM FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'DIMENSION'
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;
   
  SELECT VERSION_ID INTO V_IAS_DIM_VERSION   -- IAS
    FROM FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'DIMENSION'
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，取得IAS维表版本号为：'||V_IAS_DIM_VERSION||'，华东采购维表版本号为：'||V_ECPQC_DIM_VERSION,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  -- 判断入参值，根据不同入参值对变量赋予不同值
  IF F_CALIBER_FLAG = 'IAS' THEN  -- IAS领域
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
     V_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_S''';
     V_IN_ITEM := 'DIM.ITEM_CODE,DIM.ITEM_NAME,';
     V_SQL_CONDITION := ' AND ITEMDIM.VERSION_ID = '||V_IAS_DIM_VERSION||'
       AND D.L2_CEG_CODE IN (''51132'',''12229'')  -- 限制ICT及华东采购L2层级的编码
       AND D.L3_CEG_CODE IN (''12256'',''12257'',''12251'',''12849'',''12253'',''16449'',''19319'',''12252'',''51135'',''13610'',''19484'',''19382'',''51133'',''16349'')  -- IAS共14个专家团数据';
  ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN  -- 华东采购领域
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
     V_SEQUENCE := '''FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_S''';
     V_IN_ITEM := 'DIM.ITEM_CODE,DIM.ITEM_NAME,';
     V_SQL_CONDITION := ' AND D.L2_CEG_CODE IN (''51132'')  -- 限制华东采购L2层级编码
       AND D.L3_CEG_CODE IN (''51135'',''19484'',''51133'')  -- 华东采购共3个专家团数据';
  END IF;
 
  -- 查询品类该月版本是否已存在，若存在，沿用，否则新建 
  V_SQL := '
  SELECT COUNT(1) 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, ''YYYYMM'')||''-TOP品类-Auto''
        AND DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''';
  EXECUTE IMMEDIATE V_SQL INTO V_CURRENT_FLAG;

  -- V_CURRENT_FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  V_SQL := '
  SELECT VERSION_ID 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, ''YYYYMM'')||''-TOP品类-Auto''
        AND DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE
  --新版本号赋值
  V_SQL := '
  SELECT NEXTVAL('||V_SEQUENCE||')
    FROM DUAL';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  V_VERSION_NAME := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')||'-TOP品类-Auto';

  --往版本信息表记录本次CATEGORY版本号, 版本号为V_VERSION_ID
  V_SQL := '
  INSERT INTO '||V_VERSION_TABLE||'
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   ('||V_VERSION_ID||',NULL,'''||V_VERSION_NAME||''',1,''auto'',''category'',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,''N'')';
   EXECUTE IMMEDIATE V_SQL;
   END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||',往版本信息表记录CATEGORY版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  -- 查询ITEM该月版本是否已存在，若存在，沿用，否则新建 
  V_SQL := '
  SELECT COUNT(1) 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        VERSION = TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')||''-ITEM-Auto''
        AND DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''ITEM''';
  EXECUTE IMMEDIATE V_SQL INTO V_CURRENT_FLAG;
    -- FLAG 等于0，说明没有版本号，取新版本号        
  IF V_CURRENT_FLAG = 0 THEN 
  V_SQL := '
  SELECT NEXTVAL('||V_SEQUENCE||')
    FROM DUAL';
  EXECUTE IMMEDIATE V_SQL INTO V_ITEM_VERSION_ID;
    V_ITEM_VERSION_NAME := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')||'-ITEM-Auto';
    V_PARENT_VERSION := V_VERSION_ID;
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  V_SQL := '
  INSERT INTO '||V_VERSION_TABLE||'
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   ('||V_ITEM_VERSION_ID||','||V_PARENT_VERSION||','''||V_ITEM_VERSION_NAME||''',1,''auto'',''item'',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,''N'')';
   EXECUTE IMMEDIATE V_SQL;
   END IF;
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||',往版本信息表记录ITEM版本号信息, 版本号='||V_ITEM_VERSION_ID||', 版本名称='||V_ITEM_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建会话级临时表，保存映射维表数据
  DROP TABLE IF EXISTS VIEW_DIM_TMP;
  CREATE TEMPORARY TABLE VIEW_DIM_TMP(
    ITEM_CODE CHARACTER VARYING(50),
    ITEM_NAME CHARACTER VARYING(1000),
    CATEGORY_CODE CHARACTER VARYING(50),
    CATEGORY_NAME CHARACTER VARYING(200),
    L4_CEG_CODE CHARACTER VARYING(50),
    L4_CEG_CN_NAME CHARACTER VARYING(200),
    L4_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
    L3_CEG_CODE CHARACTER VARYING(50),
    L3_CEG_CN_NAME CHARACTER VARYING(200),
    L3_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
    L2_CEG_CODE CHARACTER VARYING(200),
    L2_CEG_CN_NAME CHARACTER VARYING(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY REPLICATION;
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  IF F_CALIBER_FLAG = 'IAS' THEN  -- IAS领域
  -- ICT和华东采购的映射维表数据拼接并去重后得到IAS映射维表数据，并将数据插入临时表
    INSERT INTO VIEW_DIM_TMP (
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L4_CEG_CODE,
           L4_CEG_CN_NAME,
           L4_CEG_SHORT_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_CN_NAME,
           L3_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME
    )
    WITH OPT_DIM_ITEM_TMP AS (
    SELECT
        ITEM_CODE, ITEM_NAME, ITEM_SUBTYPE_CODE
    FROM
        ( SELECT ITEM_CODE, ITEM_NAME, ITEM_SUBTYPE_CODE,
                 ROW_NUMBER ( ) OVER ( PARTITION BY ITEM_CODE ORDER BY SRC_LAST_UPDATE_DATE DESC) RN 
             FROM DWRDIM.DWR_DIM_MATERIAL_CODE_D ) 
    WHERE RN = 1 
    )
    SELECT DISTINCT T3.ITEM_CODE,T3.ITEM_NAME,
           T1.CATEGORY_CODE,T1.CATEGORY_NAME,
           T2.L4_CEG_CODE,T1.L4_CEG_CN_NAME,T1.L4_CEG_SHORT_CN_NAME,
           T2.L3_CEG_CODE,T1.L3_CEG_CN_NAME,T1.L3_CEG_SHORT_CN_NAME,
           '5113212229' AS L2_CEG_CODE,'IAS' AS L2_CEG_CN_NAME  -- L2层级编码及名称自定义
        FROM
       ( SELECT DISTINCT CATEGORY_CODE,CATEGORY_NAME,L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME,
        L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME ,ROW_NUMBER() OVER(PARTITION BY CATEGORY_CODE ORDER BY LABLE ASC) AS RANK  -- 按品类编码去重，优先取华东采购维度数据
        FROM (
            SELECT 'ICT' AS LABLE,CATEGORY_CODE,CATEGORY_NAME,
                   L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,
                   L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME
               FROM FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ICT_T 
               WHERE VERSION_ID = (
                                   SELECT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T 
                                      WHERE DEL_FLAG = 'N' AND STATUS = 1 AND UPPER(DATA_TYPE) = 'DIMENSION'
                                      ORDER BY LAST_UPDATE_DATE DESC LIMIT 1
               )
            UNION ALL 
            SELECT 'EAST_CHINA_PQC' AS LABLE,CATEGORY_CODE,CATEGORY_NAME,
                   L4_CEG_CN_NAME,L4_CEG_SHORT_CN_NAME,
                   L3_CEG_CN_NAME,L3_CEG_SHORT_CN_NAME
               FROM FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ECPQC_T
               WHERE VERSION_ID = V_ECPQC_DIM_VERSION
    )) T1
    LEFT JOIN DMDIM.DM_DIM_CEG_D T2
    ON T1.L4_CEG_CN_NAME = T2.L4_CEG_CN_NAME
    AND T1.L3_CEG_CN_NAME = T2.L3_CEG_CN_NAME
    LEFT JOIN OPT_DIM_ITEM_TMP T3
    ON T1.CATEGORY_CODE = T3.ITEM_SUBTYPE_CODE
    WHERE T1.RANK = 1
    AND T2.L2_CEG_CODE IN ('51132','12229')  -- 限制ICT及华东采购L2层级的编码
    AND T2.L3_CEG_CODE IN ('12256','12257','12251','12849','12253','16449','19319','12252','51135','13610','19484','19382','51133','16349');  -- IAS共14个专家团数据
    V_SQL_JOIN := 'INNER JOIN FIN_DM_OPT_FOI.DM_FOI_IAS_ITEM_DIM_T ITEMDIM
                   ON I.ITEM_CODE = ITEMDIM.ITEM_CODE';  -- IAS需要内关联ITEM补录表，确定数据范围
  ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN  -- 华东采购领域
  -- 华东采购映射维表数据进行处理后，并将数据插入临时表
    INSERT INTO VIEW_DIM_TMP (
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L4_CEG_CODE,
           L4_CEG_CN_NAME,
           L4_CEG_SHORT_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_CN_NAME,
           L3_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME
    )
    WITH OPT_DIM_ITEM_TMP AS (
    SELECT
        ITEM_CODE, ITEM_NAME, ITEM_SUBTYPE_CODE
    FROM
        ( SELECT ITEM_CODE, ITEM_NAME, ITEM_SUBTYPE_CODE,
                 ROW_NUMBER ( ) OVER ( PARTITION BY ITEM_CODE ORDER BY SRC_LAST_UPDATE_DATE DESC) RN 
             FROM DWRDIM.DWR_DIM_MATERIAL_CODE_D ) 
    WHERE RN = 1 
    )
    SELECT T3.ITEM_CODE,T3.ITEM_NAME,
           T1.CATEGORY_CODE,T1.CATEGORY_NAME,
           T2.L4_CEG_CODE,T1.L4_CEG_CN_NAME,T1.L4_CEG_SHORT_CN_NAME,
           T2.L3_CEG_CODE,T1.L3_CEG_CN_NAME,T1.L3_CEG_SHORT_CN_NAME,
           T2.L2_CEG_CODE,T2.L2_CEG_CN_NAME
       FROM FIN_DM_OPT_FOI.DM_DIM_CATG_MODL_CEG_ECPQC_T T1
       LEFT JOIN DMDIM.DM_DIM_CEG_D T2
       ON T1.L4_CEG_CN_NAME = T2.L4_CEG_CN_NAME
       AND T1.L3_CEG_CN_NAME = T2.L3_CEG_CN_NAME
       LEFT JOIN OPT_DIM_ITEM_TMP T3
       ON T1.CATEGORY_CODE = T3.ITEM_SUBTYPE_CODE
       WHERE VERSION_ID = V_ECPQC_DIM_VERSION
       AND T2.L2_CEG_CODE IN ('51132')  -- 限制华东采购L2层级编码
       AND T2.L3_CEG_CODE IN ('51135','19484','51133');  -- 华东采购共3个专家团数据
  END IF;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||',将映射维表数据处理完成后，插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建会话级临时表，保存计算结果数据
  DROP TABLE IF EXISTS DATA_DIM_TMP;
  CREATE TEMPORARY TABLE DATA_DIM_TMP(
    YEAR INT,
    PERIOD_ID INT,
    ITEM_ID CHARACTER VARYING(200),
    SUPPLIER_CODE CHARACTER VARYING(50),
    ITEM_CODE CHARACTER VARYING(50),
    ITEM_NAME CHARACTER VARYING(1000),
    CATEGORY_CODE CHARACTER VARYING(50),
    CATEGORY_NAME CHARACTER VARYING(200),
    CATEGORY_EN_NAME CHARACTER VARYING(200),
    L4_CEG_CODE CHARACTER VARYING(50),
    L4_CEG_CN_NAME CHARACTER VARYING(200),
    L4_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
    L3_CEG_CODE CHARACTER VARYING(50),
    L3_CEG_CN_NAME CHARACTER VARYING(200),
    L3_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
    L2_CEG_CODE CHARACTER VARYING(200),
    L2_CEG_CN_NAME CHARACTER VARYING(200),
    RECEIVE_QTY NUMERIC,
    RECEIVE_AMT_USD NUMERIC,
    RECEIVE_AMT_CNY NUMERIC
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH (SUPPLIER_CODE,ITEM_CODE);
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   V_SQL := '
    INSERT INTO DATA_DIM_TMP(
           YEAR,
           PERIOD_ID,
           ITEM_ID,
           ITEM_CODE,
           ITEM_NAME,
           RECEIVE_QTY,
           RECEIVE_AMT_USD,
           RECEIVE_AMT_CNY,
           CATEGORY_CODE,
           CATEGORY_NAME,
           CATEGORY_EN_NAME,
           SUPPLIER_CODE,
           L4_CEG_CODE,
           L4_CEG_SHORT_CN_NAME,
           L4_CEG_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_SHORT_CN_NAME,
           L3_CEG_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME
    )
    SELECT I.YEAR,
           I.PERIOD_ID,
           I.ITEM_ID,
           '||V_IN_ITEM||'
           I.RECEIVE_QTY,
           I.RECEIVE_AMT_USD,
           I.RECEIVE_AMT_CNY,
           DIM.CATEGORY_CODE,
           DIM.CATEGORY_NAME,
           M.ITEM_SUBTYPE_EN_NAME AS CATEGORY_EN_NAME,
           I.SUPPLIER_CODE,
           DIM.L4_CEG_CODE,
           DIM.L4_CEG_SHORT_CN_NAME,
           DIM.L4_CEG_CN_NAME,
           DIM.L3_CEG_CODE,
           DIM.L3_CEG_SHORT_CN_NAME,
           DIM.L3_CEG_CN_NAME,
           DIM.L2_CEG_CODE,
           DIM.L2_CEG_CN_NAME
        FROM FIN_DM_OPT_FOI.FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I I
        INNER JOIN VIEW_DIM_TMP DIM   -- 品类-模块-专家团映射维表
        ON I.ITEM_SUBTYPE_CODE = DIM.CATEGORY_CODE
        AND I.ITEM_CODE = DIM.ITEM_CODE
        '||V_SQL_JOIN||'
        LEFT JOIN DMDIM.DM_DIM_CEG_D D
        ON I.CEG_KEY = D.CEG_KEY
        LEFT JOIN DWRDIM.DWR_DIM_SUPPLIER_D SUP
        ON I.SUPPLIER_KEY = SUP.SUPPLIER_KEY
        LEFT JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D M
        ON I.ITEM_CODE = M.ITEM_CODE
        AND I.ITEM_SUBTYPE_CODE = M.ITEM_SUBTYPE_CODE
        WHERE UPPER(I.PO_PURCHASE_TARGET_TYPE) = ''INVENTORY''
        AND UPPER(I.PROCUREMENT_PP_FLAG) = ''Y''
        AND UPPER(I.INTERCOMPANY_TRANSACTION_FLAG) = ''N''
        AND I.EMS_STRATEGY IS NULL
        AND I.ITEM_CODE IS NOT NULL
        AND I.ITEM_SUBTYPE_CODE IS NOT NULL
        AND I.YEAR >= YEAR(CURRENT_TIMESTAMP)-3
        AND I.PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')   -- 20240902新增：不取当前月份数据
        '||V_SQL_CONDITION||'
        AND NOT EXISTS (SELECT ITEM_CODE 
                          FROM(SELECT ITEM_CODE 
                               FROM FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I S 
                               WHERE SUBSTR(S.ITEM_CODE,1,2) IN (''88'',''99''))S1
                          WHERE I.ITEM_CODE = S1.ITEM_CODE)  --剔除88，99开头
        AND NOT EXISTS (SELECT ITEM_CODE
                          FROM(SELECT ITEM_CODE 
                               FROM FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I S 
                               WHERE SUBSTR(S.ITEM_CODE,1,1) IN (''W'',''Z''))S1
                          WHERE  I.ITEM_CODE = S1.ITEM_CODE)  --剔除W,Z开头
    ';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '关联逻辑处理完成，并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 历史下单&到货明细表数据关联筛选逻辑
  V_SQL := '
    INSERT INTO FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T
    (VERSION_ID,
     YEAR,
     PERIOD_ID,
     ITEM_ID,
     ITEM_CODE,
     ITEM_NAME,
     RECEIVE_QTY,
     RECEIVE_AMT_USD,
     RECEIVE_AMT_CNY,
     CATEGORY_CODE,
     CATEGORY_NAME,
     CATEGORY_EN_NAME,
     SUPPLIER_CODE,
     SUPPLIER_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_SHORT_CN_NAME,
     L4_CEG_CN_NAME,
     L3_CEG_CODE,
     L3_CEG_SHORT_CN_NAME,
     L3_CEG_CN_NAME,
     L2_CEG_CODE,
     L2_CEG_CN_NAME,
     CALIBER_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG
     )
    WITH OPT_DIM_SUPPLIER_TMP AS (
      SELECT SUPPLIER_KEY,SUPPLIER_CODE,SUPPLIER_NAME FROM(
      SELECT SUPPLIER_KEY,SUPPLIER_CODE,SUPPLIER_NAME,
             ROW_NUMBER() OVER(PARTITION BY SUPPLIER_CODE ORDER BY SCD_ACTIVE_BEGIN_DATE DESC) RN
         FROM DWRDIM.DWR_DIM_SUPPLIER_D
         WHERE SCD_ACTIVE_IND = 1
          ) WHERE RN = 1
    )
    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           D.YEAR,
           D.PERIOD_ID,
           D.ITEM_ID,
           D.ITEM_CODE,
           D.ITEM_NAME,
           SUM(D.RECEIVE_QTY) AS RECEIVE_QTY,
           SUM(D.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
           SUM(D.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
           D.CATEGORY_CODE,
           D.CATEGORY_NAME,
           D.CATEGORY_EN_NAME,
           D.SUPPLIER_CODE,
           SUP.SUPPLIER_NAME,
           D.L4_CEG_CODE,
           D.L4_CEG_SHORT_CN_NAME,
           D.L4_CEG_CN_NAME,
           D.L3_CEG_CODE,
           D.L3_CEG_SHORT_CN_NAME,
           D.L3_CEG_CN_NAME,
           D.L2_CEG_CODE,
           D.L2_CEG_CN_NAME,
           '''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
        FROM DATA_DIM_TMP D
        LEFT JOIN OPT_DIM_SUPPLIER_TMP SUP
        ON D.SUPPLIER_CODE = SUP.SUPPLIER_CODE
        GROUP BY D.YEAR,
                 D.PERIOD_ID,
                 D.ITEM_ID,
                 D.ITEM_CODE,
                 D.ITEM_NAME,
                 D.CATEGORY_CODE,
                 D.CATEGORY_NAME,
                 D.CATEGORY_EN_NAME,
                 D.SUPPLIER_CODE,
                 SUP.SUPPLIER_NAME,
                 D.L4_CEG_CODE,
                 D.L4_CEG_SHORT_CN_NAME,
                 D.L4_CEG_CN_NAME,
                 D.L3_CEG_CODE,
                 D.L3_CEG_SHORT_CN_NAME,
                 D.L3_CEG_CN_NAME,
                 D.L2_CEG_CODE,
                 D.L2_CEG_CN_NAME
        HAVING SUM(D.RECEIVE_QTY) >= 10 
        AND SUM(D.RECEIVE_AMT_CNY) >= 1';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||',实际数据计算完成，插入历史数表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集fin_dm_opt_foi.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T统计信息完成!');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

