-- Name: f_dm_fom_source_primary_add_20240309bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_source_primary_add_20240309bak(f_data_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-12-5
  创建人  ：唐钦
  背景描述：上游数据添加主键，为JAVA加解密做处理
            涉及表：
            --标准成本吸收收敛(自制历史 金额) 
            standard_cst_abs_aggre_t_tmp(和上游/补录数据完全保持一致的表)/standard_cst_abs_aggre_t(新增一个PRIMARY_ID字段，其余保持一致)/dm_fom_standard_cst_abs_aggre_t
            --任务令吸收明细(只包含最新月)
            rtd_cst_wip_trans_ae_detail_i_tmp/rtd_cst_wip_trans_ae_detail_i/fom_rtd_cst_wip_trans_ae_detail_i
  参数描述：f_data_flag : 数据口径(标准成本吸收收敛(历史数据)：HIS/任务令吸收明细（本月最新数据）：CURR；清空数据：TRUNCATE_HIS/TRUNCATE_CURR)
            x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.f_dm_fom_source_primary_add('CURR')
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_SOURCE_PRIMARY_ADD'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_DATA_FLAG VARCHAR(50) := F_DATA_FLAG;   -- 数据类型标识
  V_LAST_MONTH VARCHAR(50);  -- 上个月月份
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1'; 

  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
IF V_DATA_FLAG <> 'TRUNCATE_HIS' AND V_DATA_FLAG <> 'TRUNCATE_CURR' THEN
  -- 删除目标表数据
  IF V_DATA_FLAG = 'HIS' THEN  -- 标准成本吸收收敛表
     V_LAST_MONTH := TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM');
    EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T WHERE PERIOD_ID = '''||V_LAST_MONTH||'''';  -- 删除与插入数据月份相同的数据
  ELSIF V_DATA_FLAG = 'CURR' THEN  -- 任务令吸收明细表
    SELECT DISTINCT PERIOD_NAME INTO V_LAST_MONTH FROM RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP;
    EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I WHERE PERIOD_NAME = '''||V_LAST_MONTH||'''';  -- 删除与插入数据月份相同的数据
  END IF;
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除数据类型标识为：'||V_DATA_FLAG||',且月份为：'''||V_LAST_MONTH||'''的表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 判断入参值，刷新哪张表数据
  IF V_DATA_FLAG = 'HIS' THEN  -- 标准成本吸收收敛表
     INSERT INTO FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T (  -- 加主键的表
            ITEM ,
            PERIOD_ID ,
            HOMEMADE_EMS ,
            RESOURCE_TYPE ,
            AMOUNT 
            )
        SELECT ITEM ,
               CAST(CONCAT(SUBSTR(PERIOD_ID,1,4), SUBSTR(PERIOD_ID,6,2)) AS BIGINT) AS PERIOD_ID,
               HOMEMADE_EMS ,
               RESOURCE_TYPE ,
               AMOUNT 
            FROM FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T_TMP ;
            
     INSERT INTO FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T (  -- 逻辑处理需要的表
            ITEM ,
            PERIOD_ID ,
            HOMEMADE_EMS ,
            RESOURCE_TYPE ,
            PRIMARY_ID
            )
        SELECT ITEM ,
               PERIOD_ID ,
               HOMEMADE_EMS ,
               RESOURCE_TYPE ,
               PRIMARY_ID
            FROM FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T
			WHERE PERIOD_ID = TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM');
   ELSIF V_DATA_FLAG = 'CURR' THEN  -- 任务令吸收明细表
     INSERT INTO FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I (  -- 插入数据,增加 PRIMARY_ID
            COA_ACCOUNT_CODE
            ,GC_TRANSACTION_AMOUNT
            ,PERIOD_NAME
            ,UNIT_CODE
            ,ORGANIZATION_CODE
            ,GC_SUB_ELEMENT_RATE
            ,USAGE_RATE_OR_AMOUNT
            ,RESOURCE_CODE
            ,RESOURCE_DESCRIPTION
            ,TRANSACTION_UOM_CODE
            ,WIP_ORDER_NUMBER
            ,ASSEMBLY_ITEM_CODE
            ,OPERATION_CODE
            )
        SELECT COA_ACCOUNT_CODE
              ,GC_TRANSACTION_AMOUNT
              ,PERIOD_NAME
              ,UNIT_CODE
              ,ORGANIZATION_CODE
              ,GC_SUB_ELEMENT_RATE
              ,USAGE_RATE_OR_AMOUNT
              ,RESOURCE_CODE
              ,RESOURCE_DESCRIPTION
              ,TRANSACTION_UOM_CODE
              ,WIP_ORDER_NUMBER
              ,ASSEMBLY_ITEM_CODE
              ,OPERATION_CODE
            FROM FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP;

     INSERT INTO FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I (  -- 逻辑处理需要的表
            COA_ACCOUNT_CODE
            ,PERIOD_NAME
            ,UNIT_CODE
            ,ORGANIZATION_CODE
            ,GC_SUB_ELEMENT_RATE
            ,USAGE_RATE_OR_AMOUNT
            ,RESOURCE_CODE
            ,RESOURCE_DESCRIPTION
            ,TRANSACTION_UOM_CODE
            ,WIP_ORDER_NUMBER
            ,ASSEMBLY_ITEM_CODE
            ,OPERATION_CODE
            ,PRIMARY_ID
            )
        SELECT COA_ACCOUNT_CODE
              ,PERIOD_NAME
              ,UNIT_CODE
              ,ORGANIZATION_CODE
              ,GC_SUB_ELEMENT_RATE
              ,USAGE_RATE_OR_AMOUNT
              ,RESOURCE_CODE
              ,RESOURCE_DESCRIPTION
              ,TRANSACTION_UOM_CODE
              ,WIP_ORDER_NUMBER
              ,ASSEMBLY_ITEM_CODE
              ,OPERATION_CODE
              ,PRIMARY_ID
            FROM FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I;
   END IF;
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入数据类型标识为：'||V_DATA_FLAG||',且月份为：'''||V_LAST_MONTH||'''的表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  -- 删除数据
ELSIF V_DATA_FLAG = 'TRUNCATE_HIS' THEN  
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T_TMP';  -- 上游保持一致的表
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T';  -- 加主键的表
     
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空数据类型标识为：'||V_DATA_FLAG||'的上游表和加主键表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
ELSIF V_DATA_FLAG = 'TRUNCATE_CURR' THEN  
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP';  -- 上游保持一致的表
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I';  -- 加主键的表
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空数据类型标识为：'||V_DATA_FLAG||'的上游表和加主键表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
END IF;
   
  -- 收集统计信息
  IF V_DATA_FLAG = 'HIS' THEN  
     EXECUTE IMMEDIATE 'ANALYZE STANDARD_CST_ABS_AGGRE_T_TMP';
     EXECUTE IMMEDIATE 'ANALYZE STANDARD_CST_ABS_AGGRE_T';
     EXECUTE IMMEDIATE 'ANALYZE DM_FOM_STANDARD_CST_ABS_AGGRE_T';
  ELSIF V_DATA_FLAG = 'CURR' THEN  
     EXECUTE IMMEDIATE 'ANALYZE RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP';
     EXECUTE IMMEDIATE 'ANALYZE RTD_CST_WIP_TRANS_AE_DETAIL_I';
     EXECUTE IMMEDIATE 'ANALYZE FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I';
  END IF;
  
  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END
$$
/

