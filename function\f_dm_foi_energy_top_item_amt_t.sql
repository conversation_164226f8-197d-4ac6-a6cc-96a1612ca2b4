-- Name: f_dm_foi_energy_top_item_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_top_item_amt_t(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创  建  人 ：杨泽宝 ywx1106160
参数描述：
        参数一(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
		参数二(f_cate_version)：top品类清单表最新版本号
		参数三(f_item_version)：导入通用版本号（规格品清单版本号）
		参数四(F_CALIBER_FLAG)：I 代表ICT采购，E代表数字能源
背景描述：规格品月金额表
根据历史下单明细表和预测数表生成和数据筛选条件选出4年的TOP品类集合，再关联ICT映射表和专家团维表生成四年的综合TOP品类清单，
根据上游的历史下单明细数据更新，就可以调度函数重新生成一版数据，按版本号全量抽取，支持删除重跑。
备注：
来源表:FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T --数字能源_历史数表
	   FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T --数字能源_规格品清单
	   FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T  --ICT采购 历史数表
	   FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T       --ICT采购 规格品清单
目标表:FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T  --数字能源_规格品月金额表
       FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T --ICT采购 规格品月金额表
事例：fin_dm_opt_foi.f_dm_foi_energy_top_item_amt_t()
*/
DECLARE
	V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_TOP_ITEM_AMT_T'; --存储过程名称
	V_VERSION_ID BIGINT; --新的版本号, 取自序列:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
	V_TOP_MADE_ID BIGINT; --TOP制造对象清单最新的版本号
	V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
	V_STEP_NUM   BIGINT := 0; --步骤号
	V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
	V_TYPE_OR_FLAG VARCHAR2(50);--存放字段：CONTINUITY_TYPE还是GROUP_PUR_FLAG
	
	V_SQL        TEXT;   --SQL逻辑
	V_FROM_TABLE1 VARCHAR(50); -- 来源表1
	V_FROM_TABLE2 VARCHAR(50); -- 来源表2
	V_FROM_TABLE3 VARCHAR(50); -- 来源表2
	V_TO_TABLE   VARCHAR(50); -- 目标表


BEGIN
  X_RESULT_STATUS = '1';
  
  --开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行'); 
 
  --对入参合法性判断
  IF F_CALIBER_FLAG NOT IN ('I','E') THEN
     RETURN '0';
  END IF;

  --通过F_DIMENSION_TYPE传参,确认来源表和目标表
  IF F_CALIBER_FLAG = 'I' THEN -- ICT采购
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T';--来源表2
	 V_FROM_TABLE3 := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';--来源表3
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T';--目标表
  ELSIF F_CALIBER_FLAG = 'E' THEN -- 数字能源
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T';--来源表2
	 V_FROM_TABLE3 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';--来源表3
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T'; --目标表
  ELSE
    NULL;
  END IF; 

   
   --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF /*F_CATE_VERSION = 0 AND*/ F_ITEM_VERSION IS NULL THEN
	V_SQL := ' SELECT T.VERSION_ID FROM '||V_FROM_TABLE3||' T
			WHERE SUBSTR(VERSION,1,6) = ( SELECT MAX (SUBSTR(VERSION,1,6)) FROM '||V_FROM_TABLE3||' 
							WHERE UPPER(DATA_TYPE) IN (''CATEGORY'',''ITEM'')) 
				AND UPPER(DEL_FLAG) = ''N''
				AND STATUS = 1
				AND UPPER(DATA_TYPE) = ''ITEM''
				AND UPPER(version_type) IN( ''AUTO'',''FINAL'') ';
				
	EXECUTE V_SQL INTO V_VERSION_ID;
  --入参不为空，则以入参为版本号
  ELSE
    /*V_VERSION_ID := NVL(F_ITEM_VERSION, F_CATE_VERSION);*/
	V_VERSION_ID := F_ITEM_VERSION;
  END IF;  
 
  --1、写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION_ID,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  EXECUTE IMMEDIATE 'DELETE FROM   '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
  
   --2、写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '清空表 '||V_TO_TABLE,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  --1.建临时表，承载制造单领域金额
	DROP TABLE IF EXISTS DM_AMT_TEMP;
	CREATE TEMPORARY TABLE DM_AMT_TEMP(
	YEAR integer,
	PERIOD_ID integer,
	ITEM_CODE character varying(100),
	ITEM_CN_NAME character varying(500),
	RECEIVE_QTY BIGINT,
	RECEIVE_AMT_USD numeric,
	RECEIVE_AMT_CNY numeric,
	CATEGORY_CODE character varying(50),
	CATEGORY_NAME character varying(200),
	L4_CEG_CODE character varying(200),
	L4_CEG_SHORT_CN_NAME character varying(200),
	L4_CEG_CN_NAME character varying(255),
	L3_CEG_CODE character varying(200),
	L3_CEG_SHORT_CN_NAME character varying(200),
	L3_CEG_CN_NAME character varying(255),
	L2_CEG_CODE character varying(200),
	L2_CEG_CN_NAME character varying(200),
	GROUP_PUR_FLAG 	VARCHAR(2),
	PARENT_CODE	VARCHAR(50),
    APPEND_FLAG	VARCHAR(2),
	AVG_PRICE_CNY	NUMERIC,
	SUPPLIER_CODE	VARCHAR(50),
    SUPPLIER_CN_NAME	VARCHAR(1000),
    GROUP_LEVEL	VARCHAR(50),
	CONTINUITY_TYPE VARCHAR(100)
	)
	ON COMMIT PRESERVE ROWS
	DISTRIBUTE BY ROUNDROBIN;
	
  --3、写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  
  
  --2.1 区分ICT采购和数字能源,供应商层级
  --ICT采购
  IF F_CALIBER_FLAG = 'I' THEN
	INSERT INTO DM_AMT_TEMP(
	YEAR,
	PERIOD_ID,
	ITEM_CODE,
	ITEM_CN_NAME,
	RECEIVE_QTY,
	RECEIVE_AMT_USD,
	RECEIVE_AMT_CNY,
	CATEGORY_CODE,
	CATEGORY_NAME,
	L4_CEG_CODE,
	L4_CEG_SHORT_CN_NAME ,
	L4_CEG_CN_NAME ,
	L3_CEG_CODE ,
	L3_CEG_SHORT_CN_NAME ,
	L3_CEG_CN_NAME,
	L2_CEG_CODE ,
	L2_CEG_CN_NAME,
	SUPPLIER_CODE,
    SUPPLIER_CN_NAME,
    GROUP_LEVEL,
	PARENT_CODE	,
    APPEND_FLAG,
	AVG_PRICE_CNY,
	CONTINUITY_TYPE
	)
	SELECT 
	T.YEAR ,
	T.PERIOD_ID ,
	T.ITEM_CODE ,
	T.ITEM_NAME ,
	T.RECEIVE_QTY ,
	T.RECEIVE_AMT_USD ,
	T.RECEIVE_AMT_CNY ,
	T.CATEGORY_CODE ,
	T.CATEGORY_NAME ,
	T.L4_CEG_CODE,
	T.L4_CEG_SHORT_CN_NAME ,
	T.L4_CEG_CN_NAME ,
	T.L3_CEG_CODE ,
	T.L3_CEG_SHORT_CN_NAME ,
	T.L3_CEG_CN_NAME,
	T.L2_CEG_CODE ,
	T.L2_CEG_CN_NAME,
	T.SUPPLIER_CODE,  /*供应商编码*/
    T.SUPPLIER_CN_NAME,
    'SUPPLIER' AS GROUP_LEVEL,
	T.ITEM_CODE AS PARENT_CODE,
    'N' AS APPEND_FLAG,
	CASE WHEN NVL(T.RECEIVE_QTY, 0) = 0 THEN 0
             ELSE T.RECEIVE_AMT_CNY / T.RECEIVE_QTY
           END AS AVG_PRICE_CNY, --到货均价(CNY)
	T2.CONTINUITY_TYPE /*是否含连续性影响：含连续性影响/不含连续性影响/NULL*/
	FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T T
	/*关联DM_FOI_PLAN_VAR_PARA_T取出字段CONTINUITY_TYPE*/
	LEFT JOIN (SELECT VALUE, PARA_NAME AS CONTINUITY_TYPE
									   FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
									  WHERE ENABLE_FLAG = 'Y'
										AND PARA_NAME = '不含连续性影响') T2
	ON T.L3_CEG_CN_NAME = T2.VALUE
	WHERE UPPER(T.APPEND_FLAG) = 'N'
		AND YEAR BETWEEN YEAR(CURRENT_DATE)-3 AND YEAR(CURRENT_DATE)/*取过去三年至今的数据*/
		;
	
  --数字能源
  ELSIF F_CALIBER_FLAG = 'E' THEN
	INSERT INTO DM_AMT_TEMP(
	YEAR,
	PERIOD_ID,
	ITEM_CODE,
	ITEM_CN_NAME,
	RECEIVE_QTY,
	RECEIVE_AMT_USD,
	RECEIVE_AMT_CNY,
	CATEGORY_CODE,
	CATEGORY_NAME,
	L4_CEG_CODE,
	L4_CEG_SHORT_CN_NAME ,
	L4_CEG_CN_NAME ,
	L3_CEG_CODE ,
	L3_CEG_SHORT_CN_NAME ,
	L3_CEG_CN_NAME,
	L2_CEG_CODE ,
	L2_CEG_CN_NAME,
	SUPPLIER_CODE,
    SUPPLIER_CN_NAME,
    GROUP_LEVEL,
	PARENT_CODE	,
    APPEND_FLAG,
	AVG_PRICE_CNY,
	GROUP_PUR_FLAG
	)
	SELECT 
	T1.YEAR,
	T1.PERIOD_ID,
	T1.ITEM_CODE,
	T1.ITEM_NAME,
	T1.RECEIVE_QTY,
	T1.RECEIVE_AMT_USD,
	T1.RECEIVE_AMT_CNY,
	T1.CATEGORY_CODE,
	T1.CATEGORY_NAME,
	T1.L4_CEG_CODE,
	T1.L4_CEG_SHORT_CN_NAME ,
	T1.L4_CEG_CN_NAME ,
	T1.L3_CEG_CODE ,
	T1.L3_CEG_SHORT_CN_NAME ,
	T1.L3_CEG_CN_NAME,
	T1.L2_CEG_CODE ,
	T1.L2_CEG_CN_NAME,
	T1.SUPPLIER_CODE,  /*供应商编码*/
    T1.SUPPLIER_CN_NAME,
    'SUPPLIER' AS GROUP_LEVEL,
	T1.ITEM_CODE AS PARENT_CODE,
    'N' AS APPEND_FLAG,
	CASE WHEN NVL(T1.RECEIVE_QTY, 0) = 0 THEN 0
             ELSE T1.RECEIVE_AMT_CNY / T1.RECEIVE_QTY
           END AS AVG_PRICE_CNY,--到货均价(CNY)
    T2.GROUP_PUR_FLAG  /*是否集团代采*/
	FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T T1
	LEFT JOIN (SELECT VALUE, 'N' AS GROUP_PUR_FLAG
											   FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
											  WHERE ENABLE_FLAG = 'Y'
												AND PARA_NAME = 'ENERGY') T2
									ON T1.L3_CEG_CN_NAME = T2.VALUE 
	
	WHERE  YEAR BETWEEN YEAR(CURRENT_DATE)-3 AND YEAR(CURRENT_DATE)/*取过去三年至今的数据*/
	;
   END IF;
  --4、写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '数据已经插入中间表 DM_AMT_TEMP',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  --取不同的字段名：字段CONTINUITY_TYPE采购中使用,字段GROUP_PUR_FLAG在数字能源中使用，所以需要再次做一下处理
	IF  F_CALIBER_FLAG = 'I' THEN -- ICT采购
		V_TYPE_OR_FLAG:='CONTINUITY_TYPE';
	ELSIF F_CALIBER_FLAG = 'E' THEN --数字能源
		V_TYPE_OR_FLAG:='GROUP_PUR_FLAG';
	END IF;
  
  --2.2 将全量金额与规格品清单关联，得到规格品金额
  
  V_SQL := 
  'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
	PERIOD_YEAR,
	PERIOD_ID,
	ITEM_CODE,
	ITEM_NAME,
	RECEIVE_QTY,
	RECEIVE_AMT_USD,
	RECEIVE_AMT_CNY,
	CATEGORY_CODE,
	CATEGORY_NAME,
	L4_CEG_CODE,
	L4_CEG_SHORT_CN_NAME,
	L4_CEG_CN_NAME,
	L3_CEG_CODE,
	L3_CEG_SHORT_CN_NAME,
	L3_CEG_CN_NAME,
	L2_CEG_CODE,
	L2_CEG_CN_NAME,
	CREATED_BY,
	CREATION_DATE,
	LAST_UPDATED_BY,
	LAST_UPDATE_DATE,
	DEL_FLAG, 
	TOP_FLAG,
	supplier_code,
    supplier_cn_name,
    GROUP_LEVEL,
	PARENT_CODE,
    APPEND_FLAG,
	AVG_PRICE_CNY,
	'||V_TYPE_OR_FLAG||')
	SELECT '||V_VERSION_ID||' AS VERSION_ID,
		T1.YEAR,
		T1.PERIOD_ID,
		T1.ITEM_CODE,
		T1.ITEM_CN_NAME,
		T1.RECEIVE_QTY,
		T1.RECEIVE_AMT_USD,
		T1.RECEIVE_AMT_CNY,
		T1.CATEGORY_CODE,
		T1.CATEGORY_NAME,
		T1.L4_CEG_CODE,
		T1.L4_CEG_SHORT_CN_NAME ,
		T1.L4_CEG_CN_NAME ,
		T1.L3_CEG_CODE ,
		T1.L3_CEG_SHORT_CN_NAME ,
		T1.L3_CEG_CN_NAME,
		T1.L2_CEG_CODE ,
		T1.L2_CEG_CN_NAME,
        ''-1'' AS CREATED_BY,
		CURRENT_TIMESTAMP AS CREATION_DATE,
	    ''-1'' AS LAST_UPDATED_BY,
	    CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
        ''N'' AS DEL_FLAG,
		CASE WHEN T2.ITEM_CODE IS NULL THEN ''N'' 
			 WHEN T2.ITEM_CODE IS NOT NULL THEN ''Y'' END AS TOP_FLAG,
		T1.supplier_code,
		T1.supplier_cn_name,
		T1.GROUP_LEVEL,
		T1.PARENT_CODE,
		T1.APPEND_FLAG,
		T1.AVG_PRICE_CNY,
		T1.'||V_TYPE_OR_FLAG||'
        FROM DM_AMT_TEMP T1
        LEFT JOIN (SELECT ITEM_CODE,
						  L2_CEG_CODE,
						  L3_CEG_CODE,
						  L4_CEG_CODE,
						  CATEGORY_CODE 
						  FROM '||V_FROM_TABLE2||' WHERE VERSION_ID ='||V_VERSION_ID||') T2
		ON T1.ITEM_CODE = T2.ITEM_CODE
         AND NVL(T1.L2_CEG_CODE, ''L2'') = NVL(T2.L2_CEG_CODE, ''L2'')
         AND NVL(T1.L3_CEG_CODE, ''L3'') = NVL(T2.L3_CEG_CODE, ''L3'')
         AND NVL(T1.L4_CEG_CODE, ''L4'') = NVL(T2.L4_CEG_CODE, ''L4'')
         AND NVL(T1.CATEGORY_CODE, ''CATE'') = NVL(T2.CATEGORY_CODE, ''CATE'') ';
		 
   EXECUTE IMMEDIATE V_SQL;

   
   --5、写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '数据已经插入结果表 '||V_TO_TABLE,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  
   
 RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

