-- Name: f_dm_foc_view_annl_cost_t_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_view_annl_cost_t_dms(f_industry_flag character varying, f_cost_type character varying, f_dimension_type character varying, f_view_flag bigint, f_keystr character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年1月24日16点12分
  创建人：twx1139790
  修改时间：2024年6月11日
  修改人：twx1139790
  背景描述：分视角下ITEM的年均本基础表(补齐后，加密)
  参数描述：f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P,量纲颗粒度：D),f_cost_type：成本类型（P：采购成本，M：制造成本），x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.F_DM_FOC_VIEW_ANNL_COST_T_DMS()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_VIEW_ANNL_COST_T_DMS'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自OPT_FCST.DM_FOC_MID_MONTH_ITEM_T
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  
    -- 7月版本需求新增
    V_SQL        TEXT;   --SQL逻辑
    V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); 
    V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500);
    V_L1_NAME VARCHAR(500);
    V_L2_NAME VARCHAR(500);
    V_IN_LV3 VARCHAR(500);
    V_IN_PFT VARCHAR(500);
    V_FROM_TABLE VARCHAR(500);
    V_TO_TABLE VARCHAR(500);
    V_TMP_TABLE VARCHAR(500);
    V_SQL_LV3   TEXT; 
    V_SQL_PFT   TEXT; 
    V_TMP_TABLE2 VARCHAR(500);
    -- 9月版本需求新增
    V_DIMENSION_CODE VARCHAR(500);         -- 量纲编码
    V_DIMENSION_NAME VARCHAR(500);         -- 量纲中文名称
    V_DIMENSION_SUB_CODE VARCHAR(500);     -- 量纲子类编码
    V_DIMENSION_SUB_NAME VARCHAR(500);     -- 量纲子类中文名称
    V_DIMENSION_DETAIL_CODE VARCHAR(500);  -- 量纲子类明细编码
    V_DIMENSION_DETAIL_NAME VARCHAR(500);  -- 量纲子类明细中文名称
    V_IN_DMS VARCHAR(500);
    V_SQL_DMS   TEXT; 
    -- 1月版本需求新增
    V_BASE_LEVEL TEXT;    -- 基础层级（采购成本/制造成本）
    V_BASE_LEVEL_TABLE TEXT;
    V_IN_BASE_LEVEL TEXT;
    V_REL_BASE_LEVEL TEXT;
    V_BASE_CODE TEXT;
    V_DECRYPT_AMT TEXT;
    V_ENCRYPT_AMT TEXT;
    V_IN_SPART VARCHAR(500) := '';
    V_INTO_SPART VARCHAR(500) := '';
    V_REL_SPART VARCHAR(500) := '';
    
    -- 5月版本需求
    V_IN_DIFF_COLUMN VARCHAR(200) := '';
    V_INTO_DIFF_COLUMN VARCHAR(200) := '';
    V_REL_DIFF_COLUMN VARCHAR(500) := '';
    V_VERSION_TABLE VARCHAR(100);
    
BEGIN
  X_RESULT_STATUS = '1';

  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
 
 -- 判断入参的成本类型是采购成本，还是制造成本的类型(11月版本需求新增)
  IF F_COST_TYPE = 'P' THEN  -- 采购成本类型
     V_BASE_CODE := '
             L3_CEG_CODE,
             L4_CEG_CODE,
             CATEGORY_CODE,';
     V_DECRYPT_AMT := 'RMB_COST_AMT,   --金额解密字段';
     V_ENCRYPT_AMT := ' NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS RMB_AVG_AMT,    -- 加密金额字段';
     V_BASE_LEVEL := '
             L3_CEG_CODE,
             L3_CEG_CN_NAME,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_CN_NAME,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,';
             
     V_BASE_LEVEL_TABLE := '
             L3_CEG_CODE    VARCHAR(50),
             L3_CEG_CN_NAME    VARCHAR(500),
             L3_CEG_SHORT_CN_NAME    VARCHAR(500),
             L4_CEG_CODE    VARCHAR(50),
             L4_CEG_CN_NAME    VARCHAR(500),
             L4_CEG_SHORT_CN_NAME    VARCHAR(500),
             CATEGORY_CODE VARCHAR(50),
             CATEGORY_CN_NAME VARCHAR(500),';
             
     V_IN_BASE_LEVEL := '
                  T1.L3_CEG_CODE,
                  T1.L3_CEG_CN_NAME,
                  T1.L3_CEG_SHORT_CN_NAME,
                  T1.L4_CEG_CODE,
                  T1.L4_CEG_CN_NAME,
                  T1.L4_CEG_SHORT_CN_NAME,
                  T1.CATEGORY_CODE,
                  T1.CATEGORY_CN_NAME,';
                  
     V_REL_BASE_LEVEL := '
         AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
         AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
         AND T1.CATEGORY_CODE = T2.CATEGORY_CODE ';

 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T ';
 -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS';--来源表
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T_DMS'; --目标表 
            V_TMP_TABLE := 'DMS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'DMS_DELETE_ZERO_TMP';
    END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T ';
 -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS';--来源表
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_VIEW_ANNL_COST_T_DMS'; --目标表 
            V_TMP_TABLE := 'ENERGY_DMS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'ENERGY_DMS_DELETE_ZERO_TMP';
    END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T ';
 -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS';--来源表
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_VIEW_ANNL_COST_T_DMS'; --目标表 
            V_TMP_TABLE := 'IAS_DMS_AVG_APD_TMP';   -- 临时表
            V_TMP_TABLE2 := 'IAS_DMS_DELETE_ZERO_TMP';
    END IF;
  END IF;
  END IF;   -- 结束成本类型逻辑判断
   
    --版本号赋值
  V_SQL := '
    SELECT VERSION_ID 
    FROM
        '||V_VERSION_TABLE||'
    WHERE
        DEL_FLAG = ''N''
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = ''CATEGORY''
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
    DBMS_OUTPUT.PUT_LINE('取版本号：'||V_VERSION_ID); 
     
  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VIEW_FLAG = '||F_VIEW_FLAG;
  DBMS_OUTPUT.PUT_LINE('清空目标表数据');    

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
     --重置变量入参
     V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
     V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
     V_L1_NAME := 'L1_NAME,';
     V_L2_NAME := 'L2_NAME,';
     V_IN_LV3 := 'T1.LV3_PROD_RND_TEAM_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME,';
     V_IN_PFT := 'T1.L1_NAME,T1.L2_NAME,';
     V_SQL_LV3 := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV3_PROD_RND_TEAM_CODE,''SNULL'')'; 
     V_SQL_PFT := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'')
                    AND NVL(T1.L2_NAME,''SNULL'') = NVL(T2.L2_NAME,''SNULL'')'; 
     -- 9月版本需求新增
     V_DIMENSION_CODE := 'DIMENSION_CODE,';
     V_DIMENSION_NAME := 'DIMENSION_CN_NAME,';
     V_DIMENSION_SUB_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
     V_DIMENSION_SUB_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
     V_DIMENSION_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
     V_DIMENSION_DETAIL_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
     V_IN_DMS := 'T1.DIMENSION_CODE,
                  T1.DIMENSION_CN_NAME,
                  T1.DIMENSION_SUBCATEGORY_CODE,
                  T1.DIMENSION_SUBCATEGORY_CN_NAME,
                  T1.DIMENSION_SUB_DETAIL_CODE,
                  T1.DIMENSION_SUB_DETAIL_CN_NAME,
                 ';
     V_SQL_DMS := ' AND NVL(T1.DIMENSION_CODE,''SNULL'') = NVL(T2.DIMENSION_CODE,''SNULL'')
                    AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL'')
                    AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL'')
                   '; 
    
    --量纲颗粒度的维度时，不需要L1、L2字段
    IF F_DIMENSION_TYPE = 'D' THEN
            V_L1_NAME := '';
            V_L2_NAME := '';
            V_IN_PFT := '';
            V_SQL_PFT := '';
            V_IN_SPART := 'SPART_CODE,SPART_CN_NAME,';
            V_INTO_SPART := 'T1.SPART_CODE,T1.SPART_CN_NAME,';
            V_REL_SPART := ' AND NVL(T1.SPART_CODE,''S1'') = NVL(T2.SPART_CODE,''S1'') ';

  -- 当产业项目标识为：202405-E时，加COA层级变量、202407-IAS时，加LV4层级
    IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
       V_IN_DIFF_COLUMN := 'COA_CODE,COA_CN_NAME,';
       V_INTO_DIFF_COLUMN := 'T1.COA_CODE,T1.COA_CN_NAME,';
       V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''S2'') = NVL(T2.COA_CODE,''S2'') ';
    ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- IAS
       V_IN_DIFF_COLUMN := 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';
       V_INTO_DIFF_COLUMN := 'T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
       V_REL_DIFF_COLUMN := ' AND NVL(T1.LV4_PROD_RND_TEAM_CODE,''S2'') = NVL(T2.LV4_PROD_RND_TEAM_CODE,''S2'') ';
    ELSE NULL;
    END IF;

    ELSE
      NULL;
    END IF;
     
 --创建年均本临时表
  V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE ||' (
        PERIOD_YEAR BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        DIMENSION_CODE VARCHAR(500),
        DIMENSION_CN_NAME VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
        COA_CODE VARCHAR(200),
        COA_CN_NAME VARCHAR(200),
        L1_NAME VARCHAR(500),
        L2_NAME VARCHAR(500),
        '||V_BASE_LEVEL_TABLE||'
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(2000),
        AVG_AMT NUMERIC,
        NULL_FLAG VARCHAR(2),
        APD_FLAG VARCHAR(2),
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(500)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;
    
    V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE2 ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE2 ||' (
        PERIOD_YEAR BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        DIMENSION_CODE VARCHAR(500),
        DIMENSION_CN_NAME VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
        SPART_CODE VARCHAR(200),
        SPART_CN_NAME VARCHAR(200),
        COA_CODE VARCHAR(200),
        COA_CN_NAME VARCHAR(200),
        L1_NAME VARCHAR(500),
        L2_NAME VARCHAR(500),
        '||V_BASE_LEVEL_TABLE||'
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(2000),
        SHIP_QUANTITY NUMERIC,
        RMB_COST_AMT NUMERIC,
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(500)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;
    
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => 'ITEM年均本临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS, 
     F_ERRBUF => 'SUCCESS');  
  DBMS_OUTPUT.PUT_LINE('临时表建表成功');

  V_SQL := '
    INSERT INTO '|| V_TMP_TABLE ||'(         
                PERIOD_YEAR,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,'
                || V_LV3_PROD_RND_TEAM_CODE
                || V_LV3_PROD_RD_TEAM_CN_NAME
                || V_DIMENSION_CODE 
                || V_DIMENSION_NAME 
                || V_DIMENSION_SUB_CODE 
                || V_DIMENSION_SUB_NAME 
                || V_DIMENSION_DETAIL_CODE 
                || V_DIMENSION_DETAIL_NAME
                || V_IN_SPART
                || V_IN_DIFF_COLUMN
                || V_L1_NAME
                || V_L2_NAME 
                ||V_BASE_LEVEL ||'
                ITEM_CODE,
                ITEM_CN_NAME,
                AVG_AMT,
                NULL_FLAG,
                APD_FLAG,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME)         
-- 年均本计算
  WITH VIEW_AVG_TMP AS(
  SELECT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,'
         || V_LV3_PROD_RND_TEAM_CODE
         || V_LV3_PROD_RD_TEAM_CN_NAME
         || V_DIMENSION_CODE 
         || V_DIMENSION_NAME 
         || V_DIMENSION_SUB_CODE 
         || V_DIMENSION_SUB_NAME 
         || V_DIMENSION_DETAIL_CODE 
         || V_DIMENSION_DETAIL_NAME
         || V_IN_SPART
         || V_IN_DIFF_COLUMN
         || V_L1_NAME
         || V_L2_NAME 
         ||V_BASE_LEVEL ||'
         ITEM_CODE,
         ITEM_CN_NAME,
         NULLIF(SUM( RMB_COST_AMT )/NULLIF(SUM(SHIP_QUANTITY),0),0) AS RMB_AVG_AMT,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
     FROM '||V_FROM_TABLE||'
     WHERE RMB_COST_AMT <> 0     -- 剔除金额为0的数据
     AND ONLY_ITEM_FLAG = ''N''  -- 剔除单ITEM的数据
     AND REVIEW_ITEM_FLAG = 0   -- 不属于底层数据审视范围的数据
     AND VIEW_FLAG = '||F_VIEW_FLAG||'
     GROUP BY PERIOD_YEAR,
              LV0_PROD_RND_TEAM_CODE,
              LV0_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,'
              || V_LV3_PROD_RND_TEAM_CODE
              || V_LV3_PROD_RD_TEAM_CN_NAME
              || V_DIMENSION_CODE 
              || V_DIMENSION_NAME 
              || V_DIMENSION_SUB_CODE 
              || V_DIMENSION_SUB_NAME 
              || V_DIMENSION_DETAIL_CODE 
              || V_DIMENSION_DETAIL_NAME
              || V_IN_SPART
              || V_IN_DIFF_COLUMN
              || V_L1_NAME
              || V_L2_NAME 
              ||V_BASE_LEVEL ||'
              ITEM_CODE,
              ITEM_CN_NAME,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG,
              LV0_PROD_LIST_CODE,
              LV0_PROD_LIST_CN_NAME)  
    
 -- 实际数均价临时表中出现的重量级团队、采购信息维，取数范围：三年前至当前系统年(若当年为1月时，当年年份不含)
  , DIM_TEAM_TMP AS(
        SELECT DISTINCT LV0_PROD_RND_TEAM_CODE,
                        LV0_PROD_RD_TEAM_CN_NAME,
                        LV1_PROD_RND_TEAM_CODE,
                        LV1_PROD_RD_TEAM_CN_NAME,
                        LV2_PROD_RND_TEAM_CODE,
                        LV2_PROD_RD_TEAM_CN_NAME,'
                        || V_LV3_PROD_RND_TEAM_CODE
                        || V_LV3_PROD_RD_TEAM_CN_NAME
                        || V_DIMENSION_CODE 
                        || V_DIMENSION_NAME 
                        || V_DIMENSION_SUB_CODE 
                        || V_DIMENSION_SUB_NAME 
                        || V_DIMENSION_DETAIL_CODE 
                        || V_DIMENSION_DETAIL_NAME
                        || V_IN_SPART
                        || V_IN_DIFF_COLUMN
                        || V_L1_NAME
                        || V_L2_NAME 
                        ||V_BASE_LEVEL ||'
                        ITEM_CODE,
                        ITEM_CN_NAME,
                        VIEW_FLAG,
                        CALIBER_FLAG,
                        OVERSEA_FLAG,
                        LV0_PROD_LIST_CODE,
                        LV0_PROD_LIST_CN_NAME
                    FROM VIEW_AVG_TMP
  )                        

 -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  , PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )
 -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT T2.PERIOD_YEAR,
              T1.LV0_PROD_RND_TEAM_CODE,
              T1.LV0_PROD_RD_TEAM_CN_NAME,
              T1.LV1_PROD_RND_TEAM_CODE,
              T1.LV1_PROD_RD_TEAM_CN_NAME,
              T1.LV2_PROD_RND_TEAM_CODE,
              T1.LV2_PROD_RD_TEAM_CN_NAME,'
              || V_IN_LV3
              || V_IN_PFT
              || V_IN_DMS
              || V_IN_SPART
              || V_IN_DIFF_COLUMN
              ||V_IN_BASE_LEVEL ||'
              T1.ITEM_CODE,
              T1.ITEM_CN_NAME,
              T1.VIEW_FLAG,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME
          FROM DIM_TEAM_TMP T1,PERIOD_YEAR_TMP T2
  )
    SELECT T1.PERIOD_YEAR,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME,'
           || V_IN_LV3
           || V_IN_PFT
           || V_IN_DMS
           || V_INTO_SPART
           || V_INTO_DIFF_COLUMN
           ||V_IN_BASE_LEVEL ||'
           T1.ITEM_CODE,
           T1.ITEM_CN_NAME,
           T2.RMB_AVG_AMT AS AVG_AMT,
           DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
           DECODE(T2.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME
       FROM CONTIN_DIM_TMP T1
       LEFT JOIN VIEW_AVG_TMP T2
       ON T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE
       AND NVL(T1.LV1_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV1_PROD_RND_TEAM_CODE,''SNULL'')
       AND NVL(T1.LV2_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV2_PROD_RND_TEAM_CODE,''SNULL'') '
       || V_SQL_LV3
       || V_SQL_PFT
       || V_SQL_DMS
       || V_REL_SPART
       || V_REL_DIFF_COLUMN
       ||V_REL_BASE_LEVEL ||'
       AND T1.ITEM_CODE = T2.ITEM_CODE
       AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL; 

   
     --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '计算年均本数据插入到临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  DBMS_OUTPUT.PUT_LINE('计算年均本数据插入到临时表');    
        
    -- 插入补齐后的分视角下年均本数据
        V_SQL := '
        INSERT INTO ' || V_TO_TABLE ||' (
                      PERIOD_YEAR,
                      LV0_PROD_RND_TEAM_CODE,
                      LV0_PROD_RD_TEAM_CN_NAME,
                      LV1_PROD_RND_TEAM_CODE,
                      LV1_PROD_RD_TEAM_CN_NAME,
                      LV2_PROD_RND_TEAM_CODE,
                      LV2_PROD_RD_TEAM_CN_NAME,'
                      || V_LV3_PROD_RND_TEAM_CODE
                      || V_LV3_PROD_RD_TEAM_CN_NAME
                      || V_DIMENSION_CODE 
                      || V_DIMENSION_NAME 
                      || V_DIMENSION_SUB_CODE 
                      || V_DIMENSION_SUB_NAME 
                      || V_DIMENSION_DETAIL_CODE 
                      || V_DIMENSION_DETAIL_NAME
                      || V_IN_SPART
                      || V_IN_DIFF_COLUMN
                      || V_L1_NAME
                      || V_L2_NAME 
                      ||V_BASE_LEVEL ||'
                      ITEM_CODE,
                      ITEM_CN_NAME,
                      RMB_AVG_AMT,
                      CREATED_BY,
                      CREATION_DATE,
                      LAST_UPDATED_BY,
                      LAST_UPDATE_DATE,
                      DEL_FLAG,
                      APPEND_FLAG,
                      VIEW_FLAG,
                      VERSION_ID,
                      CALIBER_FLAG,
                      APPEND_YEAR,
                      OVERSEA_FLAG,
                      LV0_PROD_LIST_CODE,
                      LV0_PROD_LIST_CN_NAME)
 -- 按不同视角，补齐对应的重量级团队，以及采购信息维，前向补齐年均本
 WITH FORWARD_FILLER_TEMP AS
     (
      SELECT SS.LV0_PROD_RND_TEAM_CODE,
             SS.LV0_PROD_RD_TEAM_CN_NAME,
             SS.LV1_PROD_RND_TEAM_CODE,
             SS.LV1_PROD_RD_TEAM_CN_NAME,
             SS.LV2_PROD_RND_TEAM_CODE,
             SS.LV2_PROD_RD_TEAM_CN_NAME,'
             || V_LV3_PROD_RND_TEAM_CODE
             || V_LV3_PROD_RD_TEAM_CN_NAME
             || V_DIMENSION_CODE 
             || V_DIMENSION_NAME 
             || V_DIMENSION_SUB_CODE 
             || V_DIMENSION_SUB_NAME 
             || V_DIMENSION_DETAIL_CODE 
             || V_DIMENSION_DETAIL_NAME
             || V_IN_SPART
             || V_IN_DIFF_COLUMN
             || V_L1_NAME
             || V_L2_NAME 
             ||V_BASE_LEVEL ||'
             SS.ITEM_CODE,
             SS.ITEM_CN_NAME,
             SS.PERIOD_YEAR,
             SS.AVG_AMT,
             FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.VIEW_FLAG, SS.CALIBER_FLAG, SS.OVERSEA_FLAG, SS.LV0_PROD_LIST_CODE, SS.LV0_PROD_RND_TEAM_CODE, SS.LV1_PROD_RND_TEAM_CODE, SS.LV2_PROD_RND_TEAM_CODE, '||V_LV3_PROD_RND_TEAM_CODE||V_DIMENSION_CODE||V_DIMENSION_SUB_CODE||V_DIMENSION_DETAIL_CODE||V_IN_SPART||V_IN_DIFF_COLUMN||V_L1_NAME||V_L2_NAME||V_BASE_CODE ||' SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS AVG_AMT_2, --新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_YEAR) OVER(PARTITION BY SS.VIEW_FLAG, SS.CALIBER_FLAG, SS.OVERSEA_FLAG, SS.LV0_PROD_LIST_CODE, SS.LV0_PROD_RND_TEAM_CODE, SS.LV1_PROD_RND_TEAM_CODE, SS.LV2_PROD_RND_TEAM_CODE, '||V_LV3_PROD_RND_TEAM_CODE||V_DIMENSION_CODE||V_DIMENSION_SUB_CODE||V_DIMENSION_DETAIL_CODE||V_IN_SPART||V_IN_DIFF_COLUMN||V_L1_NAME||V_L2_NAME||V_BASE_CODE ||' SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS PERIOD_YEAR_2, --新补齐的年份字段
             SS.AVG_AMT_FLAG,
             SS.APD_FLAG,
             SS.VIEW_FLAG,
             SS.CALIBER_FLAG,
             SS.OVERSEA_FLAG,
             SS.LV0_PROD_LIST_CODE,
             SS.LV0_PROD_LIST_CN_NAME
         FROM (SELECT S.LV0_PROD_RND_TEAM_CODE,
                      S.LV0_PROD_RD_TEAM_CN_NAME,
                      S.LV1_PROD_RND_TEAM_CODE,
                      S.LV1_PROD_RD_TEAM_CN_NAME,
                      S.LV2_PROD_RND_TEAM_CODE,
                      S.LV2_PROD_RD_TEAM_CN_NAME,'
                      || V_LV3_PROD_RND_TEAM_CODE
                      || V_LV3_PROD_RD_TEAM_CN_NAME
                      || V_DIMENSION_CODE 
                      || V_DIMENSION_NAME 
                      || V_DIMENSION_SUB_CODE 
                      || V_DIMENSION_SUB_NAME 
                      || V_DIMENSION_DETAIL_CODE 
                      || V_DIMENSION_DETAIL_NAME
                      || V_IN_SPART
                      || V_IN_DIFF_COLUMN
                      || V_L1_NAME
                      || V_L2_NAME 
                      || V_BASE_LEVEL ||'
                      S.ITEM_CODE,
                      S.ITEM_CN_NAME,
                      S.PERIOD_YEAR,
                      S.AVG_AMT,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.VIEW_FLAG, S.CALIBER_FLAG, S.OVERSEA_FLAG, S.LV0_PROD_LIST_CODE, S.LV0_PROD_RND_TEAM_CODE, S.LV1_PROD_RND_TEAM_CODE, S.LV2_PROD_RND_TEAM_CODE, '||V_LV3_PROD_RND_TEAM_CODE||V_DIMENSION_CODE||V_DIMENSION_SUB_CODE||V_DIMENSION_DETAIL_CODE||V_IN_SPART||V_IN_DIFF_COLUMN||V_L1_NAME||V_L2_NAME||V_BASE_CODE ||' S.ITEM_CODE ORDER BY S.PERIOD_YEAR) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      S.APD_FLAG,
                      S.VIEW_FLAG,
                      S.CALIBER_FLAG,
                      S.OVERSEA_FLAG,
                      S.LV0_PROD_LIST_CODE,
                      S.LV0_PROD_LIST_CN_NAME
                  FROM '||V_TMP_TABLE||' S) SS)
    
    --向后补齐均价
    SELECT S.PERIOD_YEAR,
           S.LV0_PROD_RND_TEAM_CODE,
           S.LV0_PROD_RD_TEAM_CN_NAME,
           S.LV1_PROD_RND_TEAM_CODE,
           S.LV1_PROD_RD_TEAM_CN_NAME,
           S.LV2_PROD_RND_TEAM_CODE,
           S.LV2_PROD_RD_TEAM_CN_NAME,'
           || V_LV3_PROD_RND_TEAM_CODE
           || V_LV3_PROD_RD_TEAM_CN_NAME
           || V_DIMENSION_CODE 
           || V_DIMENSION_NAME 
           || V_DIMENSION_SUB_CODE 
           || V_DIMENSION_SUB_NAME 
           || V_DIMENSION_DETAIL_CODE 
           || V_DIMENSION_DETAIL_NAME
           || V_IN_SPART
           || V_IN_DIFF_COLUMN
           || V_L1_NAME
           || V_L2_NAME 
           || V_BASE_LEVEL ||'
           S.ITEM_CODE,
           S.ITEM_CN_NAME,
           '||V_ENCRYPT_AMT||'
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           S.APD_FLAG AS APPEND_FLAG,
           S.VIEW_FLAG,
           '||V_VERSION_ID||' AS VERSION_ID,
           S.CALIBER_FLAG,
           S.APPEND_YEAR,
           S.OVERSEA_FLAG,
           S.LV0_PROD_LIST_CODE,
           S.LV0_PROD_LIST_CN_NAME
       FROM (SELECT T1.LV0_PROD_RND_TEAM_CODE,
                    T1.LV0_PROD_RD_TEAM_CN_NAME,
                    T1.LV1_PROD_RND_TEAM_CODE,
                    T1.LV1_PROD_RD_TEAM_CN_NAME,
                    T1.LV2_PROD_RND_TEAM_CODE,
                    T1.LV2_PROD_RD_TEAM_CN_NAME,'
                    ||V_IN_LV3
                    ||V_IN_PFT
                    ||V_IN_DMS
                    ||V_INTO_SPART
                    ||V_INTO_DIFF_COLUMN
                    ||V_IN_BASE_LEVEL ||'
                    T1.ITEM_CODE,
                    T1.ITEM_CN_NAME,
                    T1.PERIOD_YEAR,
                    T1.AVG_AMT_2,
                    T1.PERIOD_YEAR_2,
                    T2.AVG_AMT_3,
                    T2.PERIOD_YEAR_3,
                    T1.APD_FLAG,
                    T1.VIEW_FLAG,
                    T1.CALIBER_FLAG,
                    CASE WHEN T1.APD_FLAG = ''Y'' AND T1.AVG_AMT_2 IS NOT NULL THEN
                           T1.PERIOD_YEAR_2 ELSE NULL END AS APPEND_YEAR,
                    T1.OVERSEA_FLAG,
                    T1.LV0_PROD_LIST_CODE,
                    T1.LV0_PROD_LIST_CN_NAME   
                FROM FORWARD_FILLER_TEMP T1
                LEFT JOIN (SELECT DISTINCT P.LV0_PROD_RND_TEAM_CODE,
                                  P.LV1_PROD_RND_TEAM_CODE,
                                  P.LV2_PROD_RND_TEAM_CODE,'
                                  ||V_LV3_PROD_RND_TEAM_CODE
                                  ||V_DIMENSION_CODE
                                  ||V_DIMENSION_SUB_CODE
                                  ||V_DIMENSION_DETAIL_CODE
                                  ||V_IN_SPART
                                  ||V_IN_DIFF_COLUMN
                                  ||V_L1_NAME
                                  ||V_L2_NAME
                                  ||V_BASE_CODE||' 
                                  P.ITEM_CODE,
                                  FIRST_VALUE(P.PERIOD_YEAR) OVER(PARTITION BY P.VIEW_FLAG, P.CALIBER_FLAG, P.OVERSEA_FLAG, P.LV0_PROD_LIST_CODE, P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE,'||V_LV3_PROD_RND_TEAM_CODE||V_DIMENSION_CODE||V_DIMENSION_SUB_CODE||V_DIMENSION_DETAIL_CODE||V_IN_SPART||V_IN_DIFF_COLUMN||V_L1_NAME||V_L2_NAME||V_BASE_CODE||' P.ITEM_CODE ORDER BY P.PERIOD_YEAR ASC) AS PERIOD_YEAR_3, --有均价的首条会计期
                                  FIRST_VALUE(P.AVG_AMT_2) OVER(PARTITION BY P.VIEW_FLAG, P.CALIBER_FLAG, P.OVERSEA_FLAG, P.LV0_PROD_LIST_CODE, P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE,'||V_LV3_PROD_RND_TEAM_CODE||V_DIMENSION_CODE||V_DIMENSION_SUB_CODE||V_DIMENSION_DETAIL_CODE||V_IN_SPART||V_IN_DIFF_COLUMN||V_L1_NAME||V_L2_NAME||V_BASE_CODE||' P.ITEM_CODE ORDER BY P.PERIOD_YEAR ASC) AS AVG_AMT_3, --有均价的首条补齐均价
                                  P.VIEW_FLAG,
                                  P.CALIBER_FLAG,
                                  P.OVERSEA_FLAG,
                                  P.LV0_PROD_LIST_CODE
                              FROM FORWARD_FILLER_TEMP P
                              WHERE P.AVG_AMT_FLAG > 0) T2
                              ON T1.VIEW_FLAG = T2.VIEW_FLAG
                              AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
                              AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                              AND NVL(T1.LV0_PROD_LIST_CODE,''SNULL'') = NVL(T2.LV0_PROD_LIST_CODE,''SNULL'')
                              AND T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE
                              AND NVL(T1.LV1_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV1_PROD_RND_TEAM_CODE,''SNULL'')
                              AND NVL(T1.LV2_PROD_RND_TEAM_CODE,''SNULL'') = NVL(T2.LV2_PROD_RND_TEAM_CODE,''SNULL'') '
                             ||V_SQL_LV3
                             ||V_SQL_PFT
                             ||V_SQL_DMS
                             ||V_REL_SPART
                             ||V_REL_DIFF_COLUMN
                             ||V_REL_BASE_LEVEL||'
                             AND T1.ITEM_CODE = T2.ITEM_CODE
                             AND T1.PERIOD_YEAR < T2.PERIOD_YEAR_3) S                          
                   ';                
        DBMS_OUTPUT.PUT_LINE(V_SQL);
        EXECUTE IMMEDIATE V_SQL; 


 --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '补齐年均本数据，并插入版本号为：'||V_VERSION_ID||'的全量加密数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');      
  DBMS_OUTPUT.PUT_LINE('补齐数据并全量加密保存');    
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

