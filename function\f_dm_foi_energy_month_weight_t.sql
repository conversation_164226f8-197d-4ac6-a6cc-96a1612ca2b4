-- Name: f_dm_foi_energy_month_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_month_weight_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2024-02-05
创建人  ：黄心蕊 HWX1187045
背景描述：配置页面-月度分析权重表初始化
参数描述：参数一(F_CALIBER_FLAG)：'I'为ICT，'E'为数字能源
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
--数字能源
来源表 ： FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T 数字能源_ITEM基础金额表
		  FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T 数字能源_规格品清单
目标表 ： FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_WEIGHT_T 数字能源_月度页面权重表
--采购ICT
来源表 ： FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T 采购ICT_ITEM基础金额表
		  FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T 采购ICT_规格品清单
目标表 ： FIN_DM_OPT_FOI.DM_FOI_MONTH_WEIGHT_T 采购ICT_月度页面权重表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_MONTH_WEIGHT_T('I',''); --ICT一个版本数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_MONTH_WEIGHT_T('E',''); --数字能源一个版本数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME              VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_MONTH_WEIGHT_T';
  V_VERSION              INT; --版本号
  V_STEP_NUM             INT := 0; --函数步骤号
  V_CALIBER_FLAG         VARCHAR(2) := F_CALIBER_FLAG; 
  V_TO_TABLE             VARCHAR(50); --目标表
  V_FROM_TABLE           TEXT; --来源表
  V_VERSION_TABLE        VARCHAR(50); --版本号取数表
  V_SUM_AMT              NUMERIC; --四年到货总额
  V_SQL                  TEXT;
  V_PERIOD_YEAR          VARCHAR(200) := YEAR(NOW()) - 3 || '-' ||
                                         YEAR(NOW());
  V_ITEM_PART            TEXT;	--ITEM维度字段
  V_CATE_PART            TEXT;  --品类维度字段
  V_LV4_PART             TEXT;  --模块维度字段
  V_LV3_PART             TEXT;  --专家团维度字段
  V_LV2_PART             TEXT;  --LV2维度字段
  V_SUP_PART             TEXT;  --供应商维度字段
  V_SQL_ITEM_PART        TEXT;  --ITEM维度字段(带表别名)
  V_SQL_CATE_PART        TEXT;  --品类维度字段(带表别名)
  V_SQL_LV4_PART         TEXT;  --模块维度字段(带表别名)
  V_SQL_LV3_PART         TEXT;  --专家团维度字段(带表别名)
  V_SQL_LV2_PART         TEXT;  --LV2维度字段(带表别名)
  V_SQL_SUP_PART         TEXT;  --供应商维度字段(带表别名)
  V_SQL_GROUP_PART       TEXT;  --本层级编码及名称定义
  V_SQL_PARENT_CODE      TEXT;  --上层级编码及名称定义
  V_SQL_GROUP_LEVEL      TEXT;  --本层级层级定义
  V_SQL_PARENT_LEVEL     TEXT;  --上层级层级定义
  V_TOP_FLAG             TEXT;  
  V_SQL_TOP_FLAG         TEXT;
  V_SQL_PART1            TEXT;  --特殊筛选条件
  V_SQL_WEIGHT_RATE      TEXT;  --权重值字段定义
  V_CALIBER_COLUMN       TEXT;  --不同数据源特殊字段
  V_SQL_CALIBER_COLUMN   TEXT;  --不同数据源特殊字段
  V_CALIBER_COLUMN_BAC   TEXT;  --特殊字段备份
  V_SQL_CALIBER_COLUMN_Y TEXT;  --字段赋值 采购为含连续性影响 数能为含集团代采
  V_SQL_CALIBER_COLUMN_N TEXT;  --字段赋值 采购为不含连续性影响 数能为不含集团代采
  V_SQL_CALIBER_COLUMN1  TEXT; --涉及特殊字段时的查询条件
  
BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  IF V_CALIBER_FLAG = 'I' THEN 
   V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T'; --明细数据来源表
   V_TO_TABLE             := 'FIN_DM_OPT_FOI.DM_FOI_MONTH_WEIGHT_T'; --月度权重结果表
   V_VERSION_TABLE        := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T'; --版本号来源表
   V_CALIBER_COLUMN       := 'CONTINUITY_TYPE,'; --不同数据口径差异性字段,生产采购为是否含连续性影响
   V_SQL_CALIBER_COLUMN   := 'T1.CONTINUITY_TYPE,';
   V_SQL_CALIBER_COLUMN_Y := ' ''含连续性影响'' AS CONTINUITY_TYPE, ';
   V_SQL_CALIBER_COLUMN_N := ' ''不含连续性影响'' AS CONTINUITY_TYPE, ';
   V_SQL_CALIBER_COLUMN1  := ' AND CONTINUITY_TYPE = ''不含连续性影响'' ';

  ELSIF V_CALIBER_FLAG = 'E' THEN 
   V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T'; --明细数据来源表
   V_TO_TABLE             := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_WEIGHT_T'; --月度权重结果表
   V_VERSION_TABLE        := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T'; --版本号来源表
   V_CALIBER_COLUMN       := 'GROUP_PUR_FLAG, '; --不同数据口径差异性字段,生产采购为是否含连续性影响
   V_SQL_CALIBER_COLUMN   := 'T1.GROUP_PUR_FLAG, ';
   V_SQL_CALIBER_COLUMN_Y := ' ''Y'' AS GROUP_PUR_FLAG, ';
   V_SQL_CALIBER_COLUMN_N := ' ''N'' AS GROUP_PUR_FLAG, ';
   V_SQL_CALIBER_COLUMN1  := ' AND GROUP_PUR_FLAG = ''N'' ';

  END IF;
  V_CALIBER_COLUMN_BAC := V_CALIBER_COLUMN;
   

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_VERSION_ID IS NOT NULL THEN
    V_VERSION := F_VERSION_ID;
  ELSE
    V_SQL :='SELECT VERSION_ID FROM '||V_VERSION_TABLE||' WHERE LAST_UPDATE_DATE IS NOT NULL ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
	EXECUTE V_SQL INTO V_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
--删除结果表同版本数据
V_SQL:='DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION;
EXECUTE V_SQL;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'||V_VERSION||' 数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--1.基础数据准备，ITEM四年金额分年卷积  
----1.1 金额临时表创建
V_STEP_NUM := V_STEP_NUM + 1;
DROP TABLE IF EXISTS LEV_AMT_TEMP;
CREATE TEMPORARY TABLE LEV_AMT_TEMP(
	L2_CEG_CODE 			VARCHAR(50),
	L2_CEG_CN_NAME			VARCHAR(200),
	L3_CEG_CODE 			VARCHAR(50),
	L3_CEG_SHORT_CN_NAME	VARCHAR(200),
	L3_CEG_CN_NAME			VARCHAR(200),
	L4_CEG_CODE				VARCHAR(50),
	L4_CEG_SHORT_CN_NAME	VARCHAR(200),
	L4_CEG_CN_NAME			VARCHAR(200),
	CATEGORY_CODE			VARCHAR(50),
	CATEGORY_NAME  			VARCHAR(200),
	SUPPLIER_CODE			VARCHAR(50),
	SUPPLIER_CN_NAME        VARCHAR(200),
	ITEM_CODE				VARCHAR(50),
	ITEM_NAME				VARCHAR(500),
	GROUP_CODE				VARCHAR(50),
	GROUP_CN_NAME   		VARCHAR(500),
	RECEIVE_AMT_CNY 		NUMERIC,
	GROUP_LEVEL				VARCHAR(50),
	PARENT_LEVEL			VARCHAR(50),	--仅用于ITEM与供应商层级
	TOP_FLAG				VARCHAR(2),	 --用于区分供应商层级
	GROUP_PUR_FLAG 			VARCHAR(2),	 --是否含集团代采 (数字能源)
	CONTINUITY_TYPE			VARCHAR(50)  --是否含连续性影响 (生产采购)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '金额临时表创建完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  ----1.2 不同金额卷积
  FOR LEVEL_FLAG IN 1 .. 8 LOOP
  V_STEP_NUM := V_STEP_NUM + 1;
  
  IF LEVEL_FLAG = 1 THEN
    --ITEM下钻到供应商层级金额 全量
    V_LV3_PART         := ' L3_CEG_CODE,L3_CEG_SHORT_CN_NAME,L3_CEG_CN_NAME, ';
    V_LV4_PART         := ' L4_CEG_CODE,L4_CEG_SHORT_CN_NAME,L4_CEG_CN_NAME, ';
    V_CATE_PART        := ' CATEGORY_CODE,CATEGORY_NAME,';
    V_ITEM_PART        := ' ITEM_CODE,ITEM_NAME,';
    V_SUP_PART         := ' SUPPLIER_CODE ,SUPPLIER_CN_NAME , ';
    V_SQL_LV3_PART     := ' T1.L3_CEG_CODE,T1.L3_CEG_SHORT_CN_NAME,T1.L3_CEG_CN_NAME, ';
    V_SQL_LV4_PART     := ' T1.L4_CEG_CODE,T1.L4_CEG_SHORT_CN_NAME,T1.L4_CEG_CN_NAME, ';
    V_SQL_CATE_PART    := ' T1.CATEGORY_CODE,T1.CATEGORY_NAME,';
    V_SQL_ITEM_PART    := ' T1.ITEM_CODE,T1.ITEM_NAME,';
    V_SQL_SUP_PART     := ' T1.SUPPLIER_CODE ,T1.SUPPLIER_CN_NAME , ';
    V_SQL_GROUP_PART   := ' T1.SUPPLIER_CODE AS GROUP_CODE,T1.SUPPLIER_CN_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL  := ' ''SUPPLIER'' AS GROUP_LEVEL, ';
    V_SQL_PARENT_LEVEL := ' ''ITEM'' AS PARENT_LEVEL ,';
    V_SQL_PART1        := ' WHERE T1.PERIOD_YEAR BETWEEN (YEAR(NOW()) - 3) AND (YEAR(NOW())) 
							AND VERSION_ID = '||V_VERSION||' '; --来源表变更为多版本表
    V_TOP_FLAG         := ' TOP_FLAG,';
    V_SQL_TOP_FLAG     := ' T1.TOP_FLAG,';
  ELSIF LEVEL_FLAG = 2 THEN
    --供应商下ITEM金额 TOP
    V_TOP_FLAG         := '';
    V_SQL_TOP_FLAG     := '';
    V_SQL_GROUP_PART   := ' T1.ITEM_CODE AS GROUP_CODE,T1.ITEM_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL  := ' ''ITEM'' AS GROUP_LEVEL, ';
    V_SQL_PARENT_LEVEL := ' ''SUPPLIER'' AS PARENT_LEVEL ,';
    V_SQL_PART1        := ' WHERE T1.TOP_FLAG = ''Y'' '; --ITEM到供应商权重，只需要计算TOP
    V_FROM_TABLE       := ' LEV_AMT_TEMP ';
  ELSIF LEVEL_FLAG = 3 THEN
    --品类下供应商 TOP
    V_ITEM_PART        := '';
    V_SQL_ITEM_PART    := '';
    V_SQL_GROUP_PART   := ' T1.SUPPLIER_CODE AS GROUP_CODE,T1.SUPPLIER_CN_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL  := ' ''SUPPLIER'' AS GROUP_LEVEL, ';
    V_SQL_PARENT_LEVEL := ' ''CATEGORY'' AS PARENT_LEVEL ,';
    V_SQL_PART1        := ' WHERE T1.TOP_FLAG = ''Y'' '; --ITEM到供应商权重，只需要计算TOP
  ELSIF LEVEL_FLAG = 4 THEN
    --品类下ITEM金额 TOP
    V_ITEM_PART        := ' ITEM_CODE,ITEM_NAME,';
    V_SUP_PART         := ''; --去除供应商层级维度
    V_SQL_ITEM_PART    := ' T1.ITEM_CODE,T1.ITEM_NAME,';
    V_SQL_SUP_PART     := '';
    V_SQL_GROUP_PART   := ' T1.ITEM_CODE AS GROUP_CODE,T1.ITEM_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL  := ' ''ITEM'' AS GROUP_LEVEL, ';
    V_SQL_PARENT_LEVEL := ' ''CATEGORY'' AS PARENT_LEVEL ,';
    V_SQL_PART1        := ' WHERE T1.GROUP_LEVEL = ''ITEM'' ';
  ELSIF LEVEL_FLAG = 5 THEN
    --品类金额 TOP
    V_ITEM_PART        := ''; --去除ITEM层级维度
    V_SQL_ITEM_PART    := '';
    V_SQL_GROUP_PART   := ' T1.CATEGORY_CODE AS GROUP_CODE,T1.CATEGORY_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL  := ' ''CATEGORY'' AS GROUP_LEVEL, ';
    V_SQL_PARENT_LEVEL := ' '''' ,';
    V_SQL_PART1        := ' WHERE T1.GROUP_LEVEL = ''ITEM'' AND T1.PARENT_LEVEL = ''CATEGORY'' ';
  ELSIF LEVEL_FLAG = 6 THEN
    --LV4 TOP
    V_CATE_PART       := '';
    V_SQL_CATE_PART   := '';
    V_SQL_GROUP_PART  := ' T1.L4_CEG_CODE AS GROUP_CODE, T1.L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL := ' ''LV4'' AS GROUP_LEVEL, ';
    V_SQL_PART1       := ' WHERE T1.GROUP_LEVEL = ''CATEGORY'' ';
  ELSIF LEVEL_FLAG = 7 THEN
    --LV3 TOP
    V_LV4_PART        := '';
    V_SQL_LV4_PART    := '';
    V_SQL_GROUP_PART  := ' T1.L3_CEG_CODE AS GROUP_CODE,T1.L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL := ' ''LV3'' AS GROUP_LEVEL, ';
    V_SQL_PART1       := ' WHERE T1.GROUP_LEVEL = ''LV4'' ';
  ELSIF LEVEL_FLAG = 8 THEN
    --LV2 TOP  
    V_LV3_PART        := '';
    V_SQL_LV3_PART    := '';
    V_SQL_GROUP_PART  := ' T1.L2_CEG_CODE AS GROUP_CODE,T1.L2_CEG_CN_NAME AS GROUP_CN_NAME, ';
    V_SQL_GROUP_LEVEL := ' ''LV2'' AS GROUP_LEVEL, ';
    V_SQL_PART1       := '';
    IF V_CALIBER_FLAG = 'E' THEN
      V_FROM_TABLE := ' (SELECT GROUP_CODE, L2_CEG_CODE, L2_CEG_CN_NAME,RECEIVE_AMT_CNY, ''N'' AS GROUP_PUR_FLAG
                                FROM LEV_AMT_TEMP
                               WHERE GROUP_PUR_FLAG = ''N''
                                 AND GROUP_LEVEL = ''LV3''    --不是集团代采的LV3
                              UNION ALL
                              SELECT GROUP_CODE, L2_CEG_CODE, L2_CEG_CN_NAME,RECEIVE_AMT_CNY, ''Y'' AS GROUP_PUR_FLAG
                                FROM LEV_AMT_TEMP       
                               WHERE GROUP_LEVEL = ''LV3''      --所有LV3（即含有集团代采的LV3
             )';
    ELSIF V_CALIBER_FLAG = 'I' THEN
      V_FROM_TABLE := ' (SELECT GROUP_CODE, L2_CEG_CODE, L2_CEG_CN_NAME,RECEIVE_AMT_CNY, ''不含连续性影响'' AS CONTINUITY_TYPE
                                FROM LEV_AMT_TEMP
                               WHERE CONTINUITY_TYPE = ''不含连续性影响''
                                 AND GROUP_LEVEL = ''LV3''    --不含连续性影响的LV3
                              UNION ALL
                              SELECT GROUP_CODE, L2_CEG_CODE, L2_CEG_CN_NAME,RECEIVE_AMT_CNY, ''含连续性影响'' AS CONTINUITY_TYPE
                                FROM LEV_AMT_TEMP       
                               WHERE GROUP_LEVEL = ''LV3''      --所有LV3（即含连续性影响的LV3
             )';
    END IF;
  END IF;
	
V_SQL:='
INSERT INTO LEV_AMT_TEMP
  (L2_CEG_CODE,
   L2_CEG_CN_NAME,
   '||V_LV3_PART
   ||V_LV4_PART
   ||V_CATE_PART
   ||V_ITEM_PART
   ||V_SUP_PART
   ||V_TOP_FLAG||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_LEVEL,
   '||V_CALIBER_COLUMN||'
   RECEIVE_AMT_CNY)		--生产采购为是否含连续性影响 数字能源为是否含集团代采
  SELECT T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,
		 '||V_SQL_LV3_PART
		  ||V_SQL_LV4_PART
		  ||V_SQL_CATE_PART
		  ||V_SQL_ITEM_PART
		  ||V_SQL_SUP_PART
		  ||V_SQL_TOP_FLAG
		  ||V_SQL_GROUP_PART
		  ||V_SQL_GROUP_LEVEL
		  ||V_SQL_PARENT_LEVEL
		  ||V_SQL_CALIBER_COLUMN||'		--生产采购为是否含连续性影响 数字能源为是否含集团代采
         SUM(T1.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY
    FROM '||V_FROM_TABLE||' T1 
		 '||V_SQL_PART1||'
   GROUP BY '||V_SQL_LV3_PART
			 ||V_SQL_LV4_PART
			 ||V_SQL_CATE_PART
			 ||V_SQL_ITEM_PART
			 ||V_SQL_SUP_PART
			 ||V_SQL_TOP_FLAG
			 ||V_SQL_CALIBER_COLUMN||'		--生产采购为是否含连续性影响 数字能源为是否含集团代采
			T1.L2_CEG_CODE,
            T1.L2_CEG_CN_NAME
';
--DBMS_OUTPUT.PUT_LINE(''||V_SQL_GROUP_LEVEL||'''插数');
--DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;		  

			  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环，'''||V_SQL_GROUP_LEVEL||'''层级金额插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
END LOOP;

--2.权重计算 
  
  FOR LEVEL_NUM IN 1 .. 13 LOOP
  V_STEP_NUM := V_STEP_NUM + 1;
  IF LEVEL_NUM = 1 THEN
    --供应商/ITEM
    V_SQL_PART1          := ' WHERE GROUP_LEVEL = ''SUPPLIER'' AND PARENT_LEVEL = ''ITEM'' ';
    V_SQL_PARENT_CODE    := ' ITEM_CODE AS PARENT_CODE , ITEM_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL    := ' ''SUPPLIER'' ';
    V_SQL_PARENT_LEVEL   := ' ''ITEM'' ';
    V_SQL_WEIGHT_RATE    := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY CATEGORY_CODE,ITEM_CODE ),0)';
    V_CALIBER_COLUMN := V_CALIBER_COLUMN_BAC;
	V_SQL_CALIBER_COLUMN:= ' '''' , ';		--其他层级去除特殊字段
	
  ELSIF LEVEL_NUM = 2 THEN
    --供应商/品类
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''SUPPLIER'' AND PARENT_LEVEL = ''CATEGORY'' ';
    V_SQL_PARENT_CODE  := ' CATEGORY_CODE AS PARENT_CODE , CATEGORY_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL  := ' ''SUPPLIER'' ';
    V_SQL_PARENT_LEVEL := ' ''CATEGORY'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY CATEGORY_CODE ),0)';
	
  ELSIF LEVEL_NUM = 3 THEN
    --ITEM/供应商
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''ITEM'' AND PARENT_LEVEL = ''SUPPLIER'' ';
    V_SQL_PARENT_CODE  := ' SUPPLIER_CODE AS PARENT_CODE , SUPPLIER_CN_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL  := ' ''ITEM'' ';
    V_SQL_PARENT_LEVEL := ' ''SUPPLIER'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY CATEGORY_CODE,SUPPLIER_CODE ),0)';
	
  ELSIF LEVEL_NUM = 4 THEN
    --ITEM/品类
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''ITEM'' AND PARENT_LEVEL = ''CATEGORY'' ';
    V_SQL_PARENT_CODE  := ' CATEGORY_CODE AS PARENT_CODE , CATEGORY_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL  := ' ''ITEM'' ';
    V_SQL_PARENT_LEVEL := ' ''CATEGORY'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY CATEGORY_CODE ),0)';
	
  ELSIF LEVEL_NUM = 5 THEN
    --品类/LV4
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''CATEGORY'' ';
    V_SQL_PARENT_CODE  := ' L4_CEG_CODE AS PARENT_CODE , L4_CEG_CN_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL  := ' ''CATEGORY'' ';
    V_SQL_PARENT_LEVEL := ' ''LV4'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L4_CEG_CODE ),0)';
	
  ELSIF LEVEL_NUM = 6 THEN
    --品类/LV3
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''CATEGORY'' ';
    V_SQL_GROUP_LEVEL  := ' ''CATEGORY'' ';
    V_SQL_PARENT_LEVEL := ' ''LV3'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L3_CEG_CODE ),0)';
	
  ELSIF LEVEL_NUM = 7 THEN
    --品类/LV2 (含集团代采/含连续性影响)
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''CATEGORY'' ';
    V_SQL_GROUP_LEVEL  := ' ''CATEGORY'' ';
    V_SQL_PARENT_LEVEL := ' ''LV2'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L2_CEG_CODE ),0)';
	V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_Y;  --(含集团代采/含连续性影响)赋值
	
  ELSIF LEVEL_NUM = 8 THEN
    --品类/LV2(不含集团代采/不含连续性影响)
	V_SQL_PART1          := V_SQL_PART1||V_SQL_CALIBER_COLUMN1; --加入对(不含集团代采/不含连续性影响)的筛选条件
	V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_N; --(不含集团代采/不含连续性影响)赋值
	
  ELSIF LEVEL_NUM = 9 THEN
    --LV4/LV3
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''LV4'' ';
    V_SQL_PARENT_CODE  := ' L3_CEG_CODE AS PARENT_CODE , L3_CEG_CN_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL  := ' ''LV4'' ';
    V_SQL_PARENT_LEVEL := ' ''LV3'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L3_CEG_CODE ),0)';
	V_SQL_CALIBER_COLUMN := ' '''' ,';
	
  ELSIF LEVEL_NUM = 10 THEN
    --LV4/LV2(含集团代采/含连续性影响)
    V_SQL_PART1        := ' WHERE GROUP_LEVEL = ''LV4'' ';
    
    V_SQL_GROUP_LEVEL  := ' ''LV4'' ';
    V_SQL_PARENT_LEVEL := ' ''LV2'' ';
    V_SQL_WEIGHT_RATE  := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L2_CEG_CODE ),0)';
	V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_Y; ---(含集团代采/含连续性影响)赋值
	
  ELSIF LEVEL_NUM = 11 THEN
    --LV4/LV2(不含集团代采/不含连续性影响)
	V_SQL_PART1          := V_SQL_PART1||V_SQL_CALIBER_COLUMN1; --加入对(不含集团代采/不含连续性影响)的筛选条件
	V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_N; --(不含集团代采/不含连续性影响)赋值
	
  ELSIF LEVEL_NUM = 12 THEN
    --LV3/LV2(含集团代采/含连续性影响)
    V_SQL_PART1          := ' WHERE GROUP_LEVEL = ''LV3'' ';
    V_SQL_PARENT_CODE    := ' L2_CEG_CODE AS PARENT_CODE , L2_CEG_CN_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL    := ' ''LV3'' ';
    V_SQL_PARENT_LEVEL   := ' ''LV2'' ';
    V_SQL_WEIGHT_RATE    := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L2_CEG_CODE ),0)';
	V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_Y ; ---(含集团代采/含连续性影响)赋值
	
  ELSIF LEVEL_NUM = 13 THEN
    --LV3/LV2(不含集团代采/不含连续性影响)
	V_SQL_PART1          := V_SQL_PART1||V_SQL_CALIBER_COLUMN1; --加入对(不含集团代采/不含连续性影响)的筛选条件
    V_SQL_PARENT_CODE    := ' L2_CEG_CODE AS PARENT_CODE , L2_CEG_CN_NAME AS PARENT_CN_NAME,';
    V_SQL_GROUP_LEVEL    := ' ''LV3'' ';
    V_SQL_PARENT_LEVEL   := ' ''LV2'' ';
    V_SQL_WEIGHT_RATE    := ' RECEIVE_AMT_CNY/NULLIF(SUM(RECEIVE_AMT_CNY)OVER(PARTITION BY L2_CEG_CODE ),0)';
	V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_N ; ---(不含集团代采/不含连续性影响)赋值
	
  END IF;
	
  V_SQL:='
INSERT INTO '||V_TO_TABLE||'
  (PERIOD_YEAR,
   VERSION_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PARENT_CN_NAME,
   PARENT_LEVEL,
   WEIGHT_RATE,
   ITEM_CODE,
   ITEM_NAME,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L4_CEG_CODE,
   L4_CEG_SHORT_CN_NAME,
   L4_CEG_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_SHORT_CN_NAME,
   L3_CEG_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   '||V_CALIBER_COLUMN||'
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
SELECT '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
       '||V_VERSION||' AS VERSION_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       '||V_SQL_GROUP_LEVEL||' AS GROUP_LEVEL,
       '||V_SQL_PARENT_CODE||' 
       '||V_SQL_PARENT_LEVEL||' AS PARENT_LEVEL,
       '||V_SQL_WEIGHT_RATE||' AS  WEIGHT_RATE,
       ITEM_CODE,
       ITEM_NAME,
       CATEGORY_CODE,
       CATEGORY_NAME,
       L4_CEG_CODE,
       L4_CEG_SHORT_CN_NAME,
       L4_CEG_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_SHORT_CN_NAME,
       L3_CEG_CN_NAME,
       L2_CEG_CODE,
       L2_CEG_CN_NAME,
	   '||V_SQL_CALIBER_COLUMN||'
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG
  FROM LEV_AMT_TEMP
 '||V_SQL_PART1||'		
			';
			
			
--DBMS_OUTPUT.PUT_LINE('子层级为 '||V_SQL_GROUP_LEVEL||' ,父层级为 '||V_SQL_PARENT_LEVEL||' 权重');
--DBMS_OUTPUT.PUT_LINE(V_SQL);			
EXECUTE V_SQL;

--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '子层级为 '||V_SQL_GROUP_LEVEL||' ,父层级为 '||V_SQL_PARENT_LEVEL||' 的四年综合权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
END LOOP;
  
V_SQL:='ANALYZE '||V_TO_TABLE||';';
EXECUTE V_SQL;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

