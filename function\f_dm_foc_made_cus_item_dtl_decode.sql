-- Name: f_dm_foc_made_cus_item_dtl_decode; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_cus_item_dtl_decode(f_industry_flag character varying, f_dimension_type character varying, f_custom_id character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近更新时间:2024年4月26日17点07分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间:2023-10-25
创建人  :黄心蕊
背景描述:1. 发货额解密; 2. 分视角收敛均价
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P,量纲颗粒度：D), x_success_flag :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T
		FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_ITEM_T
		FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_ITEM_T
目标表:FIN_DM_OPT_FOI.DM_FOC_MADE_CUS_ITEM_DECODE_DTL_T
事例:
SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUS_ITEM_DTL_DECODE('I','U','');
     SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUS_ITEM_DTL_DECODE('I','P','');
     SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUS_ITEM_DTL_DECODE('I','D','');
		 SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUS_ITEM_DTL_DECODE('IAS','U','');
		 SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUS_ITEM_DTL_DECODE('E','U','');
最后更新时间:2024年8月8日16点07分
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_CUS_ITEM_DTL_DECODE'; --存储过程名称
  V_VERSION_ID BIGINT ; --版本号
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(1000);
  V_L1_NAME VARCHAR(1000);
  V_L2_NAME VARCHAR(1000);
  V_DIMENSION_CODE                               VARCHAR(1000); --量纲颗粒度
  V_DIMENSION_CN_NAME                            VARCHAR(1000); 
  V_DIMENSION_EN_NAME                            VARCHAR(1000); 
  V_DIMENSION_SUBCATEGORY_CODE                   VARCHAR(1000); --量纲子类
  V_DIMENSION_SUBCATEGORY_CN_NAME                VARCHAR(1000); 
  V_DIMENSION_SUBCATEGORY_EN_NAME                VARCHAR(1000); 
  V_DIMENSION_SUB_DETAIL_CODE                    VARCHAR(1000); --量纲子类明细
  V_DIMENSION_SUB_DETAIL_CN_NAME                 VARCHAR(1000); 
  V_DIMENSION_SUB_DETAIL_EN_NAME                 VARCHAR(1000);  
  
    V_SQL_LV3_PROD_RND_TEAM_CODE            VARCHAR(1000);
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME          VARCHAR(1000);
    V_SQL_L1_NAME                           VARCHAR(1000);
    V_SQL_L2_NAME                           VARCHAR(1000);
    V_SQL_DIMENSION_CODE                    VARCHAR(1000);
    V_SQL_DIMENSION_CN_NAME                 VARCHAR(1000);
	V_SQL_DIMENSION_EN_NAME                 VARCHAR(1000);
    V_SQL_DIMENSION_SUBCATEGORY_CODE        VARCHAR(1000);
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME     VARCHAR(1000);
	V_SQL_DIMENSION_SUBCATEGORY_EN_NAME     VARCHAR(1000);
    V_SQL_DIMENSION_SUB_DETAIL_CODE         VARCHAR(1000);
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME      VARCHAR(1000);
	V_SQL_DIMENSION_SUB_DETAIL_EN_NAME      VARCHAR(1000);
  
  V_FROM_TABLE VARCHAR(200); -- 来源表
  V_TO_TABLE VARCHAR(200); -- 目标表
  V_TOP_ITEM_INFO_T VARCHAR(200); 
  V_JOIN_SQL   TEXT;
  V_DIM_TABLE TEXT;
  V_SETS_LV3_PROD VARCHAR(1000);
  V_SETS_L1_NAME  VARCHAR(1000);
  V_SETS_L2_NAME  VARCHAR(1000);
  V_SETS_DIMENSION   VARCHAR(1000);
  V_SETS_DIMENSION_SUBCATEGORY   VARCHAR(1000);
  V_SETS_DIMENSION_SUB_DETAIL    VARCHAR(1000);
  V_J_LV3_PROD_RND_TEAM_CODE VARCHAR(1000);
  V_J_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(1000);
  V_J_L1_NAME VARCHAR(1000);
  V_J_L2_NAME VARCHAR(1000);
  V_J_DIMENSION_CODE VARCHAR(1000);              
  V_J_DIMENSION_CN_NAME  VARCHAR(1000);          
  V_J_DIMENSION_SUBCATEGORY_CODE VARCHAR(1000);  
  V_J_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(1000);
  V_J_DIMENSION_SUB_DETAIL_CODE     VARCHAR(1000);
  V_J_DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(1000);
  V_JION_LV3_PROD_RND_TEAM_CODE                  TEXT; --重量级团队CODE关联逻辑
  V_JOIN_L1_NAME                                 TEXT; --盈利颗粒度L1关联逻辑
  V_JOIN_L2_NAME                                 TEXT; --盈利颗粒度L2关联逻辑
  V_JOIN_DIMENSION_CODE                          TEXT; --量纲关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE              TEXT; --量纲子类关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE               TEXT; --子类明细关联条件  
  
  
  
  
   V_SQL_LVL  VARCHAR(1000);
   
   CURSOR C_DIM IS SELECT CUSTOM_ID,CUSTOM_CN_NAME,
                           MAX(DECODE(GROUP_LEVEL,'ITEM','01ITEM','MANUFACTURE_OBJECT','02MANUFACTURE_OBJECT','SHIPPING_OBJECT','03SHIPPING_OBJECT',
												'SPART','04SPART','SUB_DETAIL','05SUB_DETAIL','SUBCATEGORY','06SUBCATEGORY','DIMENSION','07DIMENSION',
												'L2','08L2','L1','09L1',
												'LV3','10LV3','LV2','11LV2','LV1','12LV1','LV0','13LV0')) AS MAX_LEVEL		--202405版本 ICT修改为LV0
                   FROM FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_COMB_D   --202401版本新增SPART层级
                   WHERE DEL_FLAG='N' AND ENABLE_FLAG='Y'  
				   AND PAGE_FLAG IN ('ALL_MONTH','MONTH')
				   AND GRANULARITY_TYPE = F_DIMENSION_TYPE
                   GROUP BY CUSTOM_ID,CUSTOM_CN_NAME ;
				   
				   
    V_C_DIM_ID    VARCHAR(1000);   
    V_C_DIM_NAME  VARCHAR(1000);   
    V_C_MAX_LEVEL  VARCHAR(1000);
	
	--202401版本新增SPART层级
	V_SPART_CODE         		TEXT;
	V_SPART_CN_NAME             TEXT;
	V_JOIN_SPART_CODE           TEXT;
	V_SQL_SPART_CODE            TEXT;
	V_SQL_SPART_CN_NAME	        TEXT;
	V_J_SPART_CODE              TEXT;
    V_J_SPART_CN_NAME           TEXT;
	V_SETS_SPART				TEXT;
	
  CURSOR C_DIM_ENERGY IS SELECT  CUSTOM_ID,CUSTOM_CN_NAME,
                           MAX(DECODE(GROUP_LEVEL,'ITEM','02ITEM','MANUFACTURE_OBJECT','03MANUFACTURE_OBJECT','SHIPPING_OBJECT','04SHIPPING_OBJECT',
						   'SPART','05SPART','SUB_DETAIL','06SUB_DETAIL','SUBCATEGORY','07SUBCATEGORY','DIMENSION','08DIMENSION',
						   'L2','09L2','L1','10L1',
						   'COA','11COA',
						   'LV3','12LV3','LV2','13LV2','LV1','14LV1','LV0','15LV0')) AS MAX_LEVEL	--202405版本 ICT修改为LV0
                   FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_COMB_D
                   WHERE DEL_FLAG='N' 
				   AND ENABLE_FLAG='Y'
                   AND PAGE_FLAG IN ('ALL_MONTH','MONTH')
                   AND GRANULARITY_TYPE = F_DIMENSION_TYPE
                   GROUP BY CUSTOM_ID,CUSTOM_CN_NAME;
				   
    --202405版本 数字能源新增COA层级
    V_COA_PART			TEXT;
    V_JOIN_COA_CODE		TEXT;
    V_SQL_COA_PART		TEXT;
    V_J_COA_CODE		TEXT;
    V_J_COA_CN_NAME		TEXT;
    V_SETS_COA			TEXT;
	
  CURSOR C_IAS_DIM IS SELECT  CUSTOM_ID,CUSTOM_CN_NAME,
                           MAX(DECODE(GROUP_LEVEL,'ITEM','02ITEM','MANUFACTURE_OBJECT','03MANUFACTURE_OBJECT','SHIPPING_OBJECT','04SHIPPING_OBJECT',
						   'SPART','05SPART','SUB_DETAIL','06SUB_DETAIL','SUBCATEGORY','07SUBCATEGORY','DIMENSION','08DIMENSION',
						   'L2','09L2','L1','10L1',
						   'LV4','11LV4',
						   'LV3','12LV3','LV2','13LV2','LV1','14LV1','LV0','15LV0')) AS MAX_LEVEL	--202405版本 ICT修改为LV0
                   FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_COMB_D
                   WHERE DEL_FLAG='N' 
				   AND ENABLE_FLAG='Y'
                   AND PAGE_FLAG IN ('ALL_MONTH','MONTH')
                   AND GRANULARITY_TYPE = F_DIMENSION_TYPE
                   GROUP BY CUSTOM_ID,CUSTOM_CN_NAME;
				   
    --202407版本 IAS新增LV4层级
    V_LV4_PART			TEXT;
    V_JOIN_LV4_CODE		TEXT;
    V_SQL_LV4_PART		TEXT;
    V_J_LV4_CODE		TEXT;
    V_J_LV4_CN_NAME		TEXT;
    V_SETS_LV4			TEXT;

BEGIN

  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
 --取版本号
 /*
   V_SQL := 'SELECT VERSION_ID FROM FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T T ORDER BY LAST_UPDATE_DATE DESC LIMIT 1 ';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  
  V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUS_ITEM_DECODE_DTL_T';--目标表
  V_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_COMB_D';       --组合维度表 
  */
  
 --取版本号
  IF F_INDUSTRY_FLAG = 'I' THEN
  	SELECT VERSION_ID
  	  INTO V_VERSION_ID
  	  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
  	 WHERE DEL_FLAG = 'N'
  	   AND STATUS = 1
  	   AND UPPER(DATA_TYPE) = 'ITEM'
  	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
   --20240327 修改版本号取数逻辑
   
    V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUS_ITEM_DECODE_DTL_T';--目标表
    V_DIM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_COMB_D';       --组合维度表 
  
  ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
  	SELECT VERSION_ID
  	  INTO V_VERSION_ID
  	  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
  	 WHERE DEL_FLAG = 'N'
  	   AND STATUS = 1
  	   AND UPPER(DATA_TYPE) = 'ITEM'
  	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
  	 
    V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUS_ITEM_DECODE_DTL_T';--目标表
    V_DIM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_COMB_D';       --组合维度表 
    
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
  	SELECT VERSION_ID
  	  INTO V_VERSION_ID
  	  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
  	 WHERE DEL_FLAG = 'N'
  	   AND STATUS = 1
  	   AND UPPER(DATA_TYPE) = 'ITEM'
  	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
  	 
    V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUS_ITEM_DECODE_DTL_T';--目标表
    V_DIM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_COMB_D';       --组合维度表 
    
  END IF ;

  --表名变量赋值
  IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
  
	IF F_INDUSTRY_FLAG = 'I' THEN
     V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T';--来源表 
	 V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T';
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
	 V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T';--来源表 
     V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_TOP_ITEM_INFO_T';
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
	 V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T';--来源表 
     V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_TOP_ITEM_INFO_T';
	END IF;
	
  ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
  
	IF F_INDUSTRY_FLAG = 'I' THEN
     V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_ITEM_T';--来源表
	 V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_TOP_ITEM_INFO_T';
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
	 V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MID_MONTH_ITEM_T';--来源表 
     V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_TOP_ITEM_INFO_T';
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
	 V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MID_MONTH_ITEM_T';--来源表 
     V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_TOP_ITEM_INFO_T';
	END IF;
	
  ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
  
	IF F_INDUSTRY_FLAG = 'I' THEN
     V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_ITEM_T';--来源表    
	 V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_TOP_ITEM_INFO_T';
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
	 V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MID_MONTH_ITEM_T';--来源表 
     V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_TOP_ITEM_INFO_T';
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
	 V_FROM_TABLE		:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MID_MONTH_ITEM_T';--来源表 
     V_TOP_ITEM_INFO_T	:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_TOP_ITEM_INFO_T';
	END IF;
	 
  ELSE
    NULL;
  END IF;
  
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||F_DIMENSION_TYPE||'，以及版本号：'||V_VERSION_ID ,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --1.清空目标表数据:
 
   IF  F_CUSTOM_ID IS NULL THEN 
   V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE GRANULARITY_TYPE = '''||F_DIMENSION_TYPE||''' ;';
   EXECUTE IMMEDIATE V_SQL;
  ELSE 
   V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE CUSTOM_ID = '||F_CUSTOM_ID||';';
   EXECUTE IMMEDIATE V_SQL;
  END  IF; 

  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 --创建Item实际数解密收敛临时表
   DROP TABLE IF EXISTS DECODE_MADE_DATA_TEMP;
   CREATE TEMPORARY TABLE DECODE_MADE_DATA_TEMP (
      PERIOD_YEAR BIGINT,
      PERIOD_ID BIGINT,
      LV0_PROD_RND_TEAM_CODE   VARCHAR(50),
      LV0_PROD_RD_TEAM_CN_NAME   VARCHAR(1000),
      LV1_PROD_RND_TEAM_CODE   VARCHAR(50),
      LV1_PROD_RD_TEAM_CN_NAME   VARCHAR(1000),
      LV2_PROD_RND_TEAM_CODE   VARCHAR(50),
      LV2_PROD_RD_TEAM_CN_NAME   VARCHAR(1000),
      LV3_PROD_RND_TEAM_CODE   VARCHAR(50), 
      LV3_PROD_RD_TEAM_CN_NAME   VARCHAR(1000),
      LV4_PROD_RND_TEAM_CODE   VARCHAR(50), -- 202407版本 IAS新增LV4层级
      LV4_PROD_RD_TEAM_CN_NAME   VARCHAR(1000),
      L1_NAME VARCHAR(1000),
      L2_NAME VARCHAR(1000),
	  COA_CODE			 VARCHAR(200),
	  COA_CN_NAME	     VARCHAR(200),		--202405版本 数字能源新增COA层级
      DIMENSION_CODE   VARCHAR(500),
      DIMENSION_CN_NAME   VARCHAR(10000),
      DIMENSION_SUBCATEGORY_CODE   VARCHAR(500),
      DIMENSION_SUBCATEGORY_CN_NAME   VARCHAR(10000),
      DIMENSION_SUB_DETAIL_CODE   VARCHAR(500),
      DIMENSION_SUB_DETAIL_CN_NAME   VARCHAR(10000),  
	  SPART_CODE   			VARCHAR(500), 
	  SPART_CN_NAME			VARCHAR(500),  --202401版本新增SPART层级
      SHIPPING_OBJECT_CODE   VARCHAR(1000),
      SHIPPING_OBJECT_CN_NAME   VARCHAR(1000),      
      MANUFACTURE_OBJECT_CODE VARCHAR(1000),
      MANUFACTURE_OBJECT_CN_NAME VARCHAR(1000),
      ITEM_CODE VARCHAR(50),
      ITEM_CN_NAME VARCHAR(10000),
      SHIP_QUANTITY NUMERIC,
      RMB_COST_AMT NUMERIC,
      VIEW_FLAG VARCHAR(2),
      CALIBER_FLAG VARCHAR(2),
      OVERSEA_FLAG   VARCHAR(2),
      LV0_PROD_LIST_CODE   VARCHAR(50),
      LV0_PROD_LIST_CN_NAME   VARCHAR(1000),
      CUSTOM_ID VARCHAR(50),
      CUSTOM_CN_NAME    VARCHAR(1000),
      CUSTOM_LEVEL    VARCHAR(50)
   )
   ON COMMIT PRESERVE ROWS
   DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);

   --2.写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
      F_STEP_NUM =>  V_STEP_NUM,
      F_CAL_LOG_DESC => 'Item实际数解密收敛临时表创建完成',
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS');  
   
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_DIMENSION_CODE                := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME             := 'DIMENSION_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE    := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE     := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME  := 'DIMENSION_SUB_DETAIL_CN_NAME,';
	--202401版本新增SPART层级
	V_SPART_CODE      				:= 'SPART_CODE,'   ;
	V_SPART_CN_NAME   				:= 'SPART_CN_NAME,';
  
     V_SQL_LV3_PROD_RND_TEAM_CODE :=    'T.LV3_PROD_RND_TEAM_CODE,';
     V_SQL_LV3_PROD_RD_TEAM_CN_NAME :=  'T.LV3_PROD_RD_TEAM_CN_NAME,';
     V_SQL_L1_NAME := 'T.L1_NAME,';
     V_SQL_L2_NAME := 'T.L2_NAME,';
     V_SQL_DIMENSION_CODE                := 'T.DIMENSION_CODE,';
     V_SQL_DIMENSION_CN_NAME             := 'T.DIMENSION_CN_NAME,';
	 V_SQL_DIMENSION_EN_NAME             := 'T.DIMENSION_EN_NAME,';
     V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T.DIMENSION_SUBCATEGORY_CODE,';
     V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T.DIMENSION_SUBCATEGORY_CN_NAME,';
	 V_SQL_DIMENSION_SUBCATEGORY_EN_NAME := 'T.DIMENSION_SUBCATEGORY_EN_NAME,';
     V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T.DIMENSION_SUB_DETAIL_CODE,';
     V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T.DIMENSION_SUB_DETAIL_CN_NAME,';  
	 V_SQL_DIMENSION_SUB_DETAIL_EN_NAME  := 'T.DIMENSION_SUB_DETAIL_EN_NAME,';
	 --202401版本新增SPART层级
	 V_SQL_SPART_CODE      				:= 'T.SPART_CODE,'   ;
     V_SQL_SPART_CN_NAME   				:= 'T.SPART_CN_NAME,';
   
     V_SETS_LV3_PROD := '(LV3_PROD_RND_TEAM_CODE,LV3_PROD_RD_TEAM_CN_NAME),';
     V_SETS_L1_NAME := '(L1_NAME),';
     V_SETS_L2_NAME := '(L2_NAME),';

     V_SETS_DIMENSION  := '(DIMENSION_CODE,DIMENSION_CN_NAME),';
     V_SETS_DIMENSION_SUBCATEGORY  := '(DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME),';
     V_SETS_DIMENSION_SUB_DETAIL   := '(DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME),';
	 --202401版本新增SPART层级
	 V_SETS_SPART  					:='(SPART_CODE,SPART_CN_NAME),';
	 

     V_J_LV3_PROD_RND_TEAM_CODE :=    '||LV3_PROD_RND_TEAM_CODE'; 
     V_J_LV3_PROD_RD_TEAM_CN_NAME :=  '||LV3_PROD_RD_TEAM_CN_NAME';
     V_J_L1_NAME := '||L1_NAME';
     V_J_L2_NAME := '||L2_NAME';
     V_J_DIMENSION_CODE                := '||DIMENSION_CODE';
     V_J_DIMENSION_CN_NAME             := '||DIMENSION_CN_NAME';
     V_J_DIMENSION_SUBCATEGORY_CODE    := '||DIMENSION_SUBCATEGORY_CODE';
     V_J_DIMENSION_SUBCATEGORY_CN_NAME := '||DIMENSION_SUBCATEGORY_CN_NAME';
     V_J_DIMENSION_SUB_DETAIL_CODE     := '||DIMENSION_SUB_DETAIL_CODE';
     V_J_DIMENSION_SUB_DETAIL_CN_NAME  := '||DIMENSION_SUB_DETAIL_CN_NAME';  
	 --202401版本新增SPART层级
	 V_J_SPART_CODE     :='||SPART_CODE';
     V_J_SPART_CN_NAME 	:='||SPART_CN_NAME';

    
    --通用颗粒度的维度时，不需要L1和L2字段已经DIMENSION字段
    IF F_DIMENSION_TYPE = 'U' THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_DIMENSION_CODE                := '';
       V_DIMENSION_CN_NAME             := '';
       V_DIMENSION_SUBCATEGORY_CODE    := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE     := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME  := '';   
       V_SQL_L1_NAME := '';
       V_SQL_L2_NAME := '';
       V_SQL_DIMENSION_CODE                := '';
       V_SQL_DIMENSION_CN_NAME             := '';
	   V_SQL_DIMENSION_EN_NAME             := '';
       V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
       V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
	   V_SQL_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_SQL_DIMENSION_SUB_DETAIL_CODE     := '';
       V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := '';  
	   V_SQL_DIMENSION_SUB_DETAIL_EN_NAME  := ''; 
       V_SETS_L1_NAME := '';
       V_SETS_L2_NAME := '';
       
       V_SETS_DIMENSION  := '';
       V_SETS_DIMENSION_SUBCATEGORY  := '';
       V_SETS_DIMENSION_SUB_DETAIL   := '';
     
       V_J_L1_NAME := '';
       V_J_L2_NAME := '';
       V_J_DIMENSION_CODE                := '';
       V_J_DIMENSION_CN_NAME             := '';
       V_J_DIMENSION_SUBCATEGORY_CODE    := '';
       V_J_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_J_DIMENSION_SUB_DETAIL_CODE     := '';
       V_J_DIMENSION_SUB_DETAIL_CN_NAME  := '';
	   
	   --202401版本新增SPART层级
	   V_SPART_CODE        				:='';
	   V_SPART_CN_NAME                  :='';
	   V_JOIN_SPART_CODE                :='';
	   V_SQL_SPART_CODE                 :='';
	   V_SQL_SPART_CN_NAME	            :='';
	   V_J_SPART_CODE                   :='';
	   V_J_SPART_CN_NAME                :='';
	   V_SETS_SPART		                :='';

	  /* 
       V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
					  LV4_PROD_RND_TEAM_CODE,''LV4'',' ;	--202407版本 IAS新增LV4层级
        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE
							||V.LV4_PROD_RND_TEAM_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
						   DECODE(V.LV4_PROD_RND_TEAM_CODE,'''','''',T.LV4_PROD_RND_TEAM_CODE)';	--202407版本 IAS新增LV4层级
						   */
						   
	IF F_INDUSTRY_FLAG = 'IAS' THEN 
       V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
					  LV4_PROD_RND_TEAM_CODE,''LV4'',' ;	--202407版本 IAS新增LV4层级
       V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE
							||V.LV4_PROD_RND_TEAM_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
						   DECODE(V.LV4_PROD_RND_TEAM_CODE,'''','''',T.LV4_PROD_RND_TEAM_CODE)';	--202407版本 IAS新增LV4层级
		--202407版本 IAS新增LV4层级
		V_LV4_PART		:='LV4_PROD_RND_TEAM_CODE,
							LV4_PROD_RD_TEAM_CN_NAME,';
		V_SQL_LV4_PART	:='	T.LV4_PROD_RND_TEAM_CODE,
							T.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_LV4_CODE	:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,3) = NVL(T.LV4_PROD_RND_TEAM_CODE,3)'; 	
		V_SETS_LV4			 := '(LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME),';
		V_J_LV4_CODE	 :=	'||LV4_PROD_RND_TEAM_CODE';
		V_J_LV4_CN_NAME  :=	'||LV4_PROD_RD_TEAM_CN_NAME';
	ELSIF F_INDUSTRY_FLAG IN ('I','E') THEN
       V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',' ;	
       V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)';	--202407版本 IAS新增LV4层级
	END IF ;
	
    V_JION_LV3_PROD_RND_TEAM_CODE := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T.LV3_PROD_RND_TEAM_CODE,3)';           
    V_JOIN_L1_NAME := '';
    V_JOIN_L2_NAME := '';
    V_JOIN_DIMENSION_CODE := '';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := '';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE := '';
	
	
    --盈利颗粒度的维度时，不需要LV3字段
    ELSIF F_DIMENSION_TYPE = 'P' THEN
        V_LV3_PROD_RND_TEAM_CODE := '';
        V_LV3_PROD_RD_TEAM_CN_NAME := '';
        V_DIMENSION_CODE                := '';
        V_DIMENSION_CN_NAME             := '';
        V_DIMENSION_SUBCATEGORY_CODE    := '';
        V_DIMENSION_SUBCATEGORY_CN_NAME := '';
        V_DIMENSION_SUB_DETAIL_CODE     := '';
        V_DIMENSION_SUB_DETAIL_CN_NAME  := '';         
      
        V_SQL_LV3_PROD_RND_TEAM_CODE := '';
        V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
        V_SQL_DIMENSION_CODE                := '';
        V_SQL_DIMENSION_CN_NAME             := '';
		V_SQL_DIMENSION_EN_NAME             := '';
        V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
        V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_SQL_DIMENSION_SUBCATEGORY_EN_NAME := '';
        V_SQL_DIMENSION_SUB_DETAIL_CODE     := '';
        V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := '';
		V_SQL_DIMENSION_SUB_DETAIL_EN_NAME  := '';

         V_SETS_LV3_PROD := '';
         V_SETS_DIMENSION  := '';
         V_SETS_DIMENSION_SUBCATEGORY  := '';
         V_SETS_DIMENSION_SUB_DETAIL   := '';
         
         V_J_LV3_PROD_RND_TEAM_CODE  := '';
         V_J_LV3_PROD_RD_TEAM_CN_NAME  := '';
         V_J_DIMENSION_CODE                := '';
         V_J_DIMENSION_CN_NAME             := '';
         V_J_DIMENSION_SUBCATEGORY_CODE    := '';
         V_J_DIMENSION_SUBCATEGORY_CN_NAME := '';
         V_J_DIMENSION_SUB_DETAIL_CODE     := '';
         V_J_DIMENSION_SUB_DETAIL_CN_NAME  := '';  
		 
		  --202401版本新增SPART层级
		  V_SPART_CODE        				:='';
		  V_SPART_CN_NAME                  :='';
		  V_JOIN_SPART_CODE                :='';
		  V_SQL_SPART_CODE                 :='';
		  V_SQL_SPART_CN_NAME	            :='';
		  V_J_SPART_CODE                   :='';
		  V_J_SPART_CN_NAME                :='';
		  V_SETS_SPART		                :='';

        V_SQL_LVL := 'L1_NAME,''L1'' ,
                        L2_NAME,''L2'' ,' ;
        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||
                            V.L1_NAME||V.L2_NAME
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.L1_NAME,'''','''',T.L1_NAME)||
                           DECODE(V.L2_NAME,'''','''',T.L2_NAME)';
                        
    V_JION_LV3_PROD_RND_TEAM_CODE := '';           
    V_JOIN_L1_NAME := ' AND NVL(T1.L1_NAME,3) = NVL(T.L1_NAME,3)';
    V_JOIN_L2_NAME := ' AND NVL(T1.L2_NAME,4) = NVL(T.L2_NAME,4)';
    V_JOIN_DIMENSION_CODE := '';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := '';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE := '';
	
						
    --量纲颗粒度的维度时，不需要L1和L2字段
    ELSIF F_DIMENSION_TYPE = 'D' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';    
       V_SQL_L1_NAME := '';
       V_SQL_L2_NAME := '';  
       V_SETS_L1_NAME := '';
       V_SETS_L2_NAME := '';    
         
        V_J_L1_NAME := '';
        V_J_L2_NAME := ''; 
        
    V_JION_LV3_PROD_RND_TEAM_CODE := 'AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T.LV3_PROD_RND_TEAM_CODE,3)';           
    V_JOIN_L1_NAME := '';
    V_JOIN_L2_NAME := '';
    V_JOIN_DIMENSION_CODE := 'AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T.DIMENSION_CODE,''D1'')';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := 'AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T.DIMENSION_SUBCATEGORY_CODE,''D2'')';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE := 'AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T.DIMENSION_SUB_DETAIL_CODE,''D3'')';
	--202401版本新增SPART层级
	V_JOIN_SPART_CODE :=' AND NVL(T1.SPART_CODE,''D4'') = NVL(T.SPART_CODE,''D4'') ';
	
	
	IF F_INDUSTRY_FLAG = 'I' THEN 
        V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
                   DIMENSION_CODE,''DIMENSION'',
                   DIMENSION_SUBCATEGORY_CODE,''SUBCATEGORY'',
                   DIMENSION_SUB_DETAIL_CODE,''SUB_DETAIL'',
				   SPART_CODE,''SPART'', ' ; --202401版本新增SPART层级
        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||
                            V.DIMENSION_CODE||V.DIMENSION_SUBCATEGORY_CODE||V.DIMENSION_SUB_DETAIL_CODE||V.SPART_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
                           DECODE(V.DIMENSION_CODE,'''','''',T.DIMENSION_CODE)||
                           DECODE(V.DIMENSION_SUBCATEGORY_CODE,'''','''',T.DIMENSION_SUBCATEGORY_CODE)||
                           DECODE(V.DIMENSION_SUB_DETAIL_CODE,'''','''',T.DIMENSION_SUB_DETAIL_CODE)||
						   DECODE(V.SPART_CODE,'''','''',T.SPART_CODE)'; --202401版本新增SPART层级

	ELSIF F_INDUSTRY_FLAG = 'E' THEN 	--202405版本 数字能源新增COA层级
	  V_COA_PART		:= 'COA_CODE,COA_CN_NAME,';
	  V_SQL_COA_PART	:= 'T.COA_CODE,T.COA_CN_NAME,';	
	  V_SETS_COA		:= '(COA_CODE,COA_CN_NAME),';	
	  V_J_COA_CODE		:= '||COA_CODE';		
	  V_J_COA_CN_NAME	:= '||COA_CN_NAME';	
	  
        V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
                   DIMENSION_CODE,''DIMENSION'',
                   DIMENSION_SUBCATEGORY_CODE,''SUBCATEGORY'',
                   DIMENSION_SUB_DETAIL_CODE,''SUB_DETAIL'',
				   SPART_CODE,''SPART'', 
				   COA_CODE,''COA'' ,' ; --202401版本新增SPART层级
						--202405版本 数字能源新增COA层级
						
        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||
                            V.DIMENSION_CODE||V.DIMENSION_SUBCATEGORY_CODE||V.DIMENSION_SUB_DETAIL_CODE||V.SPART_CODE
							||V.COA_CODE
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
                           DECODE(V.DIMENSION_CODE,'''','''',T.DIMENSION_CODE)||
                           DECODE(V.DIMENSION_SUBCATEGORY_CODE,'''','''',T.DIMENSION_SUBCATEGORY_CODE)||
                           DECODE(V.DIMENSION_SUB_DETAIL_CODE,'''','''',T.DIMENSION_SUB_DETAIL_CODE)||
						   DECODE(V.SPART_CODE,'''','''',T.SPART_CODE)|| 	
						   DECODE(V.COA_CODE,'''','''',T.COA_CODE)'; --202401版本新增SPART层级
									--202405版本 数字能源新增COA层级
	
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 	--202407版本 IAS新增LV4层级
		V_LV4_PART	:='LV4_PROD_RND_TEAM_CODE,
					   LV4_PROD_RD_TEAM_CN_NAME,';
					   
		V_SQL_LV4_PART	:='	T.LV4_PROD_RND_TEAM_CODE,
							T.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_LV4_CODE	:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,3) = NVL(T.LV4_PROD_RND_TEAM_CODE,3)'; 	
		V_SETS_LV4			 := '(LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME),';
		V_J_LV4_CODE	 :=	'||LV4_PROD_RND_TEAM_CODE';
		V_J_LV4_CN_NAME  :=	'||LV4_PROD_RD_TEAM_CN_NAME';
	  
        V_SQL_LVL := 'LV3_PROD_RND_TEAM_CODE,''LV3'',
					LV4_PROD_RND_TEAM_CODE,''LV4'',	--202407版本 IAS新增LV4层级
                   DIMENSION_CODE,''DIMENSION'',
                   DIMENSION_SUBCATEGORY_CODE,''SUBCATEGORY'',
                   DIMENSION_SUB_DETAIL_CODE,''SUB_DETAIL'',
                   SPART_CODE,''SPART'' ,' ;
        V_JOIN_SQL := 'AND V.LV0_PROD_RND_TEAM_CODE||V.LV1_PROD_RND_TEAM_CODE||V.LV2_PROD_RND_TEAM_CODE||V.LV3_PROD_RND_TEAM_CODE||
                            V.DIMENSION_CODE||V.DIMENSION_SUBCATEGORY_CODE||V.DIMENSION_SUB_DETAIL_CODE||V.SPART_CODE
							||V.LV4_PROD_RND_TEAM_CODE	--202407版本 IAS新增LV4层级
                         = DECODE(V.LV0_PROD_RND_TEAM_CODE,'''','''',T.LV0_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV1_PROD_RND_TEAM_CODE,'''','''',T.LV1_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV2_PROD_RND_TEAM_CODE,'''','''',T.LV2_PROD_RND_TEAM_CODE)||
                           DECODE(V.LV3_PROD_RND_TEAM_CODE,'''','''',T.LV3_PROD_RND_TEAM_CODE)||
						   DECODE(V.LV4_PROD_RND_TEAM_CODE,'''','''',T.LV4_PROD_RND_TEAM_CODE)||	--202407版本 IAS新增LV4层级
                           DECODE(V.DIMENSION_CODE,'''','''',T.DIMENSION_CODE)||
                           DECODE(V.DIMENSION_SUBCATEGORY_CODE,'''','''',T.DIMENSION_SUBCATEGORY_CODE)||
                           DECODE(V.DIMENSION_SUB_DETAIL_CODE,'''','''',T.DIMENSION_SUB_DETAIL_CODE)||
                           DECODE(V.SPART_CODE,'''','''',T.SPART_CODE)';
									
	  
	END IF;
	
		
    ELSE
      NULL;
    END IF;


 
IF F_CUSTOM_ID IS NULL AND F_INDUSTRY_FLAG = 'I' THEN

    OPEN C_DIM;
    LOOP 
    FETCH  C_DIM   INTO  V_C_DIM_ID,V_C_DIM_NAME,V_C_MAX_LEVEL ;
    EXIT WHEN C_DIM%NOTFOUND;

     
        V_SQL := 
       'INSERT INTO DECODE_MADE_DATA_TEMP(
                    PERIOD_YEAR,
                    PERIOD_ID,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'||
                    V_LV3_PROD_RND_TEAM_CODE ||
                    V_LV3_PROD_RD_TEAM_CN_NAME ||
                    V_L1_NAME ||
                    V_L2_NAME ||
                    V_DIMENSION_CODE||
                    V_DIMENSION_CN_NAME||
                    V_DIMENSION_SUBCATEGORY_CODE||
                    V_DIMENSION_SUBCATEGORY_CN_NAME||
                    V_DIMENSION_SUB_DETAIL_CODE||
                    V_DIMENSION_SUB_DETAIL_CN_NAME||
					V_SPART_CODE|| 
					V_SPART_CN_NAME||' --202401版本新增SPART层级
                    SHIPPING_OBJECT_CODE,
                    SHIPPING_OBJECT_CN_NAME,
                    MANUFACTURE_OBJECT_CODE,
                    MANUFACTURE_OBJECT_CN_NAME,
                    ITEM_CODE,
                    ITEM_CN_NAME,
                    SHIP_QUANTITY,
                    RMB_COST_AMT,
                    VIEW_FLAG,
                    CALIBER_FLAG,
                    OVERSEA_FLAG,    
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME,
                    CUSTOM_ID,
                    CUSTOM_CN_NAME,
					CUSTOM_LEVEL
        )
           SELECT PERIOD_YEAR,
                  PERIOD_ID,
                  T.LV0_PROD_RND_TEAM_CODE,
                  T.LV0_PROD_RD_TEAM_CN_NAME,
                  T.LV1_PROD_RND_TEAM_CODE,
                  T.LV1_PROD_RD_TEAM_CN_NAME,
                  T.LV2_PROD_RND_TEAM_CODE,
                  T.LV2_PROD_RD_TEAM_CN_NAME,'||
                  V_SQL_LV3_PROD_RND_TEAM_CODE ||
                  V_SQL_LV3_PROD_RD_TEAM_CN_NAME ||
                  V_SQL_L1_NAME ||
                  V_SQL_L2_NAME ||
                  V_SQL_DIMENSION_CODE||
                  V_SQL_DIMENSION_CN_NAME||
                  V_SQL_DIMENSION_SUBCATEGORY_CODE||
                  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                  V_SQL_DIMENSION_SUB_DETAIL_CODE||
                  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
				  V_SQL_SPART_CODE||
				  V_SQL_SPART_CN_NAME||' --202401版本新增SPART层级
                  T.SHIPPING_OBJECT_CODE,
                  T.SHIPPING_OBJECT_CN_NAME,
                  T.MANUFACTURE_OBJECT_CODE,
                  T.MANUFACTURE_OBJECT_CN_NAME,
                  T.ITEM_CODE,
                  T.ITEM_CN_NAME,
                  T.SHIP_QUANTITY,
                  T.RMB_COST_AMT,
                  T.VIEW_FLAG,
                  T.CALIBER_FLAG,
                  T.OVERSEA_FLAG,    
                  T.LV0_PROD_LIST_CODE,
                  T.LV0_PROD_LIST_CN_NAME,
                  '''|| V_C_DIM_ID ||''',
				  '''|| V_C_DIM_NAME ||''',
                  '''|| V_C_MAX_LEVEL ||'''
                FROM  (SELECT * FROM    '||V_FROM_TABLE||'   WHERE ONLY_ITEM_FLAG = ''N'' AND RMB_COST_AMT >0 ) T 
                     WHERE 1=1
                     AND EXISTS 
                           (SELECT NULL 
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N'' 
                            AND V.ENABLE_FLAG=''Y'' 
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.MANUFACTURE_OBJECT_CODE||V.SHIPPING_OBJECT_CODE,''AAAAA'')
                             = NVL(DECODE(V.MANUFACTURE_OBJECT_CODE,'''','''',T.MANUFACTURE_OBJECT_CODE)||DECODE(V.SHIPPING_OBJECT_CODE,'''','''',T.SHIPPING_OBJECT_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||'
                        ); '
                    ; 
          


    EXECUTE IMMEDIATE V_SQL;
    END LOOP ;
    CLOSE  C_DIM; 
	
ELSIF F_CUSTOM_ID IS NULL AND F_INDUSTRY_FLAG = 'E' THEN

    OPEN C_DIM_ENERGY;
    LOOP 
    FETCH  C_DIM_ENERGY   INTO  V_C_DIM_ID,V_C_DIM_NAME,V_C_MAX_LEVEL ;
    EXIT WHEN C_DIM_ENERGY%NOTFOUND;

     
        V_SQL := 
       'INSERT INTO DECODE_MADE_DATA_TEMP(
                    PERIOD_YEAR,
                    PERIOD_ID,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'||
                    V_LV3_PROD_RND_TEAM_CODE ||
                    V_LV3_PROD_RD_TEAM_CN_NAME ||
                    V_L1_NAME ||
                    V_L2_NAME ||
					V_COA_PART ||	--202405版本 数字能源新增COA层级
                    V_DIMENSION_CODE||
                    V_DIMENSION_CN_NAME||
                    V_DIMENSION_SUBCATEGORY_CODE||
                    V_DIMENSION_SUBCATEGORY_CN_NAME||
                    V_DIMENSION_SUB_DETAIL_CODE||
                    V_DIMENSION_SUB_DETAIL_CN_NAME||
					V_SPART_CODE|| 
					V_SPART_CN_NAME||' --202401版本新增SPART层级
                    SHIPPING_OBJECT_CODE,
                    SHIPPING_OBJECT_CN_NAME,
                    MANUFACTURE_OBJECT_CODE,
                    MANUFACTURE_OBJECT_CN_NAME,
                    ITEM_CODE,
                    ITEM_CN_NAME,
                    SHIP_QUANTITY,
                    RMB_COST_AMT,
                    VIEW_FLAG,
                    CALIBER_FLAG,
                    OVERSEA_FLAG,    
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME,
                    CUSTOM_ID,
                    CUSTOM_CN_NAME,
					CUSTOM_LEVEL
        )
           SELECT PERIOD_YEAR,
                  PERIOD_ID,
                  T.LV0_PROD_RND_TEAM_CODE,
                  T.LV0_PROD_RD_TEAM_CN_NAME,
                  T.LV1_PROD_RND_TEAM_CODE,
                  T.LV1_PROD_RD_TEAM_CN_NAME,
                  T.LV2_PROD_RND_TEAM_CODE,
                  T.LV2_PROD_RD_TEAM_CN_NAME,'||
                  V_SQL_LV3_PROD_RND_TEAM_CODE ||
                  V_SQL_LV3_PROD_RD_TEAM_CN_NAME ||
                  V_SQL_L1_NAME ||
                  V_SQL_L2_NAME ||
				  V_SQL_COA_PART ||	--202405版本 数字能源新增COA层级
                  V_SQL_DIMENSION_CODE||
                  V_SQL_DIMENSION_CN_NAME||
                  V_SQL_DIMENSION_SUBCATEGORY_CODE||
                  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                  V_SQL_DIMENSION_SUB_DETAIL_CODE||
                  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
				  V_SQL_SPART_CODE||
				  V_SQL_SPART_CN_NAME||' --202401版本新增SPART层级
                  T.SHIPPING_OBJECT_CODE,
                  T.SHIPPING_OBJECT_CN_NAME,
                  T.MANUFACTURE_OBJECT_CODE,
                  T.MANUFACTURE_OBJECT_CN_NAME,
                  T.ITEM_CODE,
                  T.ITEM_CN_NAME,
                  T.SHIP_QUANTITY,
                  T.RMB_COST_AMT,
                  T.VIEW_FLAG,
                  T.CALIBER_FLAG,
                  T.OVERSEA_FLAG,    
                  T.LV0_PROD_LIST_CODE,
                  T.LV0_PROD_LIST_CN_NAME,
                  '''|| V_C_DIM_ID ||''',
				  '''|| V_C_DIM_NAME ||''',
                  '''|| V_C_MAX_LEVEL ||'''
                FROM  (SELECT * FROM    '||V_FROM_TABLE||'   WHERE ONLY_ITEM_FLAG = ''N'' AND RMB_COST_AMT >0 ) T 
                     WHERE 1=1
                     AND EXISTS 
                           (SELECT NULL 
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N'' 
                            AND V.ENABLE_FLAG=''Y'' 
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.MANUFACTURE_OBJECT_CODE||V.SHIPPING_OBJECT_CODE,''AAAAA'')
                             = NVL(DECODE(V.MANUFACTURE_OBJECT_CODE,'''','''',T.MANUFACTURE_OBJECT_CODE)||DECODE(V.SHIPPING_OBJECT_CODE,'''','''',T.SHIPPING_OBJECT_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||'
                        ); '
                    ; 

    EXECUTE IMMEDIATE V_SQL;
    END LOOP ;
    CLOSE  C_DIM_ENERGY; 
	
	
--202407版本 IAS新增LV4层级
ELSIF F_CUSTOM_ID IS NULL AND F_INDUSTRY_FLAG = 'IAS' THEN

    OPEN C_IAS_DIM;
    LOOP 
    FETCH  C_IAS_DIM   INTO  V_C_DIM_ID,V_C_DIM_NAME,V_C_MAX_LEVEL ;
    EXIT WHEN C_IAS_DIM%NOTFOUND;

     
        V_SQL := 
       'INSERT INTO DECODE_MADE_DATA_TEMP(
                    PERIOD_YEAR,
                    PERIOD_ID,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'||
                    V_LV3_PROD_RND_TEAM_CODE ||
                    V_LV3_PROD_RD_TEAM_CN_NAME ||
                    V_L1_NAME ||
                    V_L2_NAME ||
					V_LV4_PART||		--202407版本 IAS新增LV4层级
                    V_DIMENSION_CODE||
                    V_DIMENSION_CN_NAME||
                    V_DIMENSION_SUBCATEGORY_CODE||
                    V_DIMENSION_SUBCATEGORY_CN_NAME||
                    V_DIMENSION_SUB_DETAIL_CODE||
                    V_DIMENSION_SUB_DETAIL_CN_NAME||
					V_SPART_CODE|| 
					V_SPART_CN_NAME||' --202401版本新增SPART层级
                    SHIPPING_OBJECT_CODE,
                    SHIPPING_OBJECT_CN_NAME,
                    MANUFACTURE_OBJECT_CODE,
                    MANUFACTURE_OBJECT_CN_NAME,
                    ITEM_CODE,
                    ITEM_CN_NAME,
                    SHIP_QUANTITY,
                    RMB_COST_AMT,
                    VIEW_FLAG,
                    CALIBER_FLAG,
                    OVERSEA_FLAG,    
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME,
                    CUSTOM_ID,
                    CUSTOM_CN_NAME,
					CUSTOM_LEVEL
        )
           SELECT PERIOD_YEAR,
                  PERIOD_ID,
                  T.LV0_PROD_RND_TEAM_CODE,
                  T.LV0_PROD_RD_TEAM_CN_NAME,
                  T.LV1_PROD_RND_TEAM_CODE,
                  T.LV1_PROD_RD_TEAM_CN_NAME,
                  T.LV2_PROD_RND_TEAM_CODE,
                  T.LV2_PROD_RD_TEAM_CN_NAME,'||
                  V_SQL_LV3_PROD_RND_TEAM_CODE ||
                  V_SQL_LV3_PROD_RD_TEAM_CN_NAME ||
				  V_SQL_LV4_PART ||		--202407版本 IAS新增LV4层级
                  V_SQL_L1_NAME ||
                  V_SQL_L2_NAME ||
                  V_SQL_DIMENSION_CODE||
                  V_SQL_DIMENSION_CN_NAME||
                  V_SQL_DIMENSION_SUBCATEGORY_CODE||
                  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                  V_SQL_DIMENSION_SUB_DETAIL_CODE||
                  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
				  V_SQL_SPART_CODE||
				  V_SQL_SPART_CN_NAME||' --202401版本新增SPART层级
                  T.SHIPPING_OBJECT_CODE,
                  T.SHIPPING_OBJECT_CN_NAME,
                  T.MANUFACTURE_OBJECT_CODE,
                  T.MANUFACTURE_OBJECT_CN_NAME,
                  T.ITEM_CODE,
                  T.ITEM_CN_NAME,
                  T.SHIP_QUANTITY,
                  T.RMB_COST_AMT,
                  T.VIEW_FLAG,
                  T.CALIBER_FLAG,
                  T.OVERSEA_FLAG,    
                  T.LV0_PROD_LIST_CODE,
                  T.LV0_PROD_LIST_CN_NAME,
                  '''|| V_C_DIM_ID ||''',
				  '''|| V_C_DIM_NAME ||''',
                  '''|| V_C_MAX_LEVEL ||'''
                FROM  (SELECT * FROM    '||V_FROM_TABLE||'   WHERE ONLY_ITEM_FLAG = ''N'' AND RMB_COST_AMT >0 ) T 
                     WHERE 1=1
                     AND EXISTS 
                           (SELECT NULL 
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N'' 
                            AND V.ENABLE_FLAG=''Y'' 
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.MANUFACTURE_OBJECT_CODE||V.SHIPPING_OBJECT_CODE,''AAAAA'')
                             = NVL(DECODE(V.MANUFACTURE_OBJECT_CODE,'''','''',T.MANUFACTURE_OBJECT_CODE)||DECODE(V.SHIPPING_OBJECT_CODE,'''','''',T.SHIPPING_OBJECT_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||'
                        ); '
                    ; 

    EXECUTE IMMEDIATE V_SQL;
    END LOOP ;
    CLOSE  C_IAS_DIM; 
    
ELSE 
    V_C_DIM_ID    := F_CUSTOM_ID;   
		   
  V_SQL := '
    SELECT NVL(MAX(CUSTOM_CN_NAME),''空集''),
           NVL(MAX(DECODE(GROUP_LEVEL,''ITEM'',''02ITEM'',''MANUFACTURE_OBJECT'',''03MANUFACTURE_OBJECT'',''SHIPPING_OBJECT'',''04SHIPPING_OBJECT'',
									''SPART'',''05SPART'',''SUB_DETAIL'',''06SUB_DETAIL'',''SUBCATEGORY'',''07SUBCATEGORY'',''DIMENSION'',''08DIMENSION'', 
									''L2'',''09L2'',''L1'',''10L1'',
									''COA'',''11COA'',		--202405版本 数字能源新增COA层级
									''LV4'',''11LV4'',		--202407版本 IAS新增LV4层级
									''LV3'',''12LV3'',
									''LV2'',''13LV2'',''LV1'',''14LV1'',''LV0'',''15LV0'')),''空集'')		--202405版本 ICT修改为LV0
           FROM '||V_DIM_TABLE||'
           WHERE CUSTOM_ID = '||F_CUSTOM_ID||' AND GRANULARITY_TYPE = '''||F_DIMENSION_TYPE||'''
           AND PAGE_FLAG IN (''ALL_MONTH'',''MONTH'')
           AND DEL_FLAG=''N'' AND ENABLE_FLAG=''Y''' ;
 EXECUTE IMMEDIATE V_SQL INTO V_C_DIM_NAME,V_C_MAX_LEVEL;


	   V_SQL := 
       'INSERT INTO DECODE_MADE_DATA_TEMP(
                    PERIOD_YEAR,
                    PERIOD_ID,
                    LV0_PROD_RND_TEAM_CODE,
                    LV0_PROD_RD_TEAM_CN_NAME,
                    LV1_PROD_RND_TEAM_CODE,
                    LV1_PROD_RD_TEAM_CN_NAME,
                    LV2_PROD_RND_TEAM_CODE,
                    LV2_PROD_RD_TEAM_CN_NAME,'||
                    V_LV3_PROD_RND_TEAM_CODE ||
                    V_LV3_PROD_RD_TEAM_CN_NAME ||
					V_LV4_PART||	--202407版本 IAS新增LV4层级
                    V_L1_NAME ||
                    V_L2_NAME ||
					V_COA_PART || 	--202405版本 数字能源新增COA层级
                    V_DIMENSION_CODE||
                    V_DIMENSION_CN_NAME||
                    V_DIMENSION_SUBCATEGORY_CODE||
                    V_DIMENSION_SUBCATEGORY_CN_NAME||
                    V_DIMENSION_SUB_DETAIL_CODE||
                    V_DIMENSION_SUB_DETAIL_CN_NAME||
					V_SPART_CODE|| 
					V_SPART_CN_NAME||'  --202401版本新增SPART层级
                    SHIPPING_OBJECT_CODE,
                    SHIPPING_OBJECT_CN_NAME,
                    MANUFACTURE_OBJECT_CODE,
                    MANUFACTURE_OBJECT_CN_NAME,
                    ITEM_CODE,
                    ITEM_CN_NAME,
                    SHIP_QUANTITY,
                    RMB_COST_AMT,
                    VIEW_FLAG,
                    CALIBER_FLAG,
                    OVERSEA_FLAG,    
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME,
                    CUSTOM_ID,
                    CUSTOM_CN_NAME,
					CUSTOM_LEVEL
        )
           SELECT PERIOD_YEAR,
                  PERIOD_ID,
                  T.LV0_PROD_RND_TEAM_CODE,
                  T.LV0_PROD_RD_TEAM_CN_NAME,
                  T.LV1_PROD_RND_TEAM_CODE,
                  T.LV1_PROD_RD_TEAM_CN_NAME,
                  T.LV2_PROD_RND_TEAM_CODE,
                  T.LV2_PROD_RD_TEAM_CN_NAME,'||
                  V_SQL_LV3_PROD_RND_TEAM_CODE ||
                  V_SQL_LV3_PROD_RD_TEAM_CN_NAME ||
				  V_SQL_LV4_PART || 	--202407版本 IAS新增LV4层级
                  V_SQL_L1_NAME ||
                  V_SQL_L2_NAME ||
				  V_SQL_COA_PART || 	--202405版本 数字能源新增COA层级
                  V_SQL_DIMENSION_CODE||
                  V_SQL_DIMENSION_CN_NAME||
                  V_SQL_DIMENSION_SUBCATEGORY_CODE||
                  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME||
                  V_SQL_DIMENSION_SUB_DETAIL_CODE||
                  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
				  V_SQL_SPART_CODE|| 
				  V_SQL_SPART_CN_NAME||' --202401版本新增SPART层级
                  T.SHIPPING_OBJECT_CODE,
                  T.SHIPPING_OBJECT_CN_NAME,
                  T.MANUFACTURE_OBJECT_CODE,
                  T.MANUFACTURE_OBJECT_CN_NAME,
                  T.ITEM_CODE,
                  T.ITEM_CN_NAME,
                  T.SHIP_QUANTITY,
                  RMB_COST_AMT,
                  T.VIEW_FLAG,
                  T.CALIBER_FLAG,
                  T.OVERSEA_FLAG,    
                  T.LV0_PROD_LIST_CODE,
                  T.LV0_PROD_LIST_CN_NAME,
                  ''' || V_C_DIM_ID ||''',
                  ''' || V_C_DIM_NAME ||''',
                  ''' || V_C_MAX_LEVEL ||'''
                FROM  (SELECT * FROM  '||V_FROM_TABLE||'  WHERE ONLY_ITEM_FLAG = ''N'' AND RMB_COST_AMT > 0 ) T 
                     WHERE 1=1
                     AND EXISTS 
                           (SELECT NULL 
                            FROM ' || V_DIM_TABLE || ' V
                            WHERE V.DEL_FLAG=''N'' 
                            AND V.ENABLE_FLAG=''Y'' 
                            AND V.CUSTOM_ID='||V_C_DIM_ID||'
                            AND V.VIEW_FLAG=T.VIEW_FLAG
                            AND V.CALIBER_FLAG=T.CALIBER_FLAG
                            AND V.OVERSEA_FLAG=T.OVERSEA_FLAG
                            AND V.LV0_PROD_LIST_CODE=T.LV0_PROD_LIST_CODE
                            AND NVL(V.MANUFACTURE_OBJECT_CODE||V.SHIPPING_OBJECT_CODE,''AAAAA'')
                             = NVL(DECODE(V.MANUFACTURE_OBJECT_CODE,'''','''',T.MANUFACTURE_OBJECT_CODE)||DECODE(V.SHIPPING_OBJECT_CODE,'''','''',T.SHIPPING_OBJECT_CODE),''AAAAA'')
                            '  ||V_JOIN_SQL||'
                        ); '
                    ; 
          

DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
    
END IF;    

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '数据插入会话级临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
   
DROP TABLE IF EXISTS TOP_MONTH_ITEM_TEMP;
CREATE TEMPORARY TABLE TOP_MONTH_ITEM_TEMP (
	PERIOD_YEAR BIGINT,
	PERIOD_ID BIGINT,
	LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(1000),
	LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(1000),
	LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(1000),
	LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
	LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(1000),
	LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
    LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(1000),	--202407版本 IAS新增LV4层级
    L1_NAME VARCHAR(1000),
    L2_NAME VARCHAR(1000),
    DIMENSION_CODE    VARCHAR(500), 
    DIMENSION_CN_NAME    VARCHAR(10000) ,
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500) ,
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(10000) ,
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500) ,
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(10000) ,  
	COA_CODE		 VARCHAR(500), 		--202405版本 数字能源新增COA层级
	COA_CN_NAME      VARCHAR(10000),
	SPART_CODE		 VARCHAR(500) ,		
	SPART_CN_NAME 	VARCHAR(500) ,  --202401版本新增SPART层级
	SHIPPING_OBJECT_CODE CHARACTER VARYING(1000),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(1000),
	MANUFACTURE_OBJECT_CODE CHARACTER VARYING(1000),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(1000),
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_CN_NAME CHARACTER VARYING(1000),
	SHIP_QUANTITY NUMERIC,
	RMB_COST_AMT NUMERIC,
	VIEW_FLAG CHARACTER VARYING(2),
	CALIBER_FLAG CHARACTER VARYING(2),
	OVERSEA_FLAG CHARACTER VARYING(2),
	LV0_PROD_LIST_CODE CHARACTER VARYING(50),
	LV0_PROD_LIST_CN_NAME CHARACTER VARYING(1000),
	CUSTOM_ID CHARACTER VARYING(50),
	CUSTOM_CN_NAME CHARACTER VARYING(1000),
	CUSTOM_LEVEL CHARACTER VARYING(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(PERIOD_ID,item_code);

V_SQL := 
'INSERT INTO TOP_MONTH_ITEM_TEMP
(
PERIOD_YEAR,
PERIOD_ID,
LV0_PROD_RND_TEAM_CODE,
LV0_PROD_RD_TEAM_CN_NAME,
LV1_PROD_RND_TEAM_CODE,
LV1_PROD_RD_TEAM_CN_NAME,
LV2_PROD_RND_TEAM_CODE,
LV2_PROD_RD_TEAM_CN_NAME,'||
V_LV3_PROD_RND_TEAM_CODE ||
V_LV3_PROD_RD_TEAM_CN_NAME ||
V_LV4_PART||	--202407版本 IAS新增LV4层级
V_L1_NAME ||
V_L2_NAME ||
V_COA_PART ||	--202405版本 数字能源新增COA层级
V_DIMENSION_CODE||
V_DIMENSION_CN_NAME||
V_DIMENSION_SUBCATEGORY_CODE||
V_DIMENSION_SUBCATEGORY_CN_NAME||
V_DIMENSION_SUB_DETAIL_CODE||
V_DIMENSION_SUB_DETAIL_CN_NAME||
V_SPART_CODE|| 
V_SPART_CN_NAME||'  --202401版本新增SPART层级
SHIPPING_OBJECT_CODE,
SHIPPING_OBJECT_CN_NAME,
MANUFACTURE_OBJECT_CODE,
MANUFACTURE_OBJECT_CN_NAME,
ITEM_CODE,
ITEM_CN_NAME ,
SHIP_QUANTITY ,
RMB_COST_AMT,
VIEW_FLAG,
CALIBER_FLAG,
OVERSEA_FLAG,
LV0_PROD_LIST_CODE,
LV0_PROD_LIST_CN_NAME,
CUSTOM_ID   ,
CUSTOM_CN_NAME,
CUSTOM_LEVEL 
)
WITH TOP_ITEM AS
 (SELECT VIEW_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         '||V_LV3_PROD_RND_TEAM_CODE||
         V_LV3_PROD_RD_TEAM_CN_NAME||
		 V_LV4_PART||	--202407版本 IAS新增LV4层级
         V_L1_NAME||
         V_L2_NAME||
		 V_COA_PART ||	--202405版本 数字能源新增COA层级
         V_DIMENSION_CODE||
         V_DIMENSION_CN_NAME||
         V_DIMENSION_EN_NAME||
         V_DIMENSION_SUBCATEGORY_CODE||
         V_DIMENSION_SUBCATEGORY_CN_NAME||
         V_DIMENSION_SUBCATEGORY_EN_NAME||
         V_DIMENSION_SUB_DETAIL_CODE||
         V_DIMENSION_SUB_DETAIL_CN_NAME||
         V_DIMENSION_SUB_DETAIL_EN_NAME||
		 V_SPART_CODE|| 
		 V_SPART_CN_NAME||'   --202401版本新增SPART层级
         TOP_SHIPPING_OBJECT_CODE,
         TOP_SHIPPING_OBJECT_CN_NAME,
         TOP_MANUFACTURE_OBJECT_CODE,
         TOP_MANUFACTURE_OBJECT_CN_NAME,           
         TOP_ITEM_CODE,
         TOP_ITEM_CN_NAME,
         CALIBER_FLAG,
         OVERSEA_FLAG,  
         LV0_PROD_LIST_CODE  ,  
         LV0_PROD_LIST_CN_NAME
    FROM '||V_TOP_ITEM_INFO_T||'
   WHERE VERSION_ID ='|| V_VERSION_ID||'
     AND IS_TOP_FLAG =''Y''/*取规格品*/
     AND DOUBLE_FLAG =''Y'')  /*取全量规格品*/ 	
  SELECT 
         T1.PERIOD_YEAR,
		 T1.PERIOD_ID,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         '||V_SQL_LV3_PROD_RND_TEAM_CODE
          ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
		  ||V_SQL_LV4_PART	--202407版本 IAS新增LV4层级
          ||V_SQL_L1_NAME
          ||V_SQL_L2_NAME
		  ||V_SQL_COA_PART
          ||V_SQL_DIMENSION_CODE
          ||V_SQL_DIMENSION_CN_NAME
          ||V_SQL_DIMENSION_SUBCATEGORY_CODE
          ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
          ||V_SQL_DIMENSION_SUB_DETAIL_CODE
          ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME||
		  V_SQL_SPART_CODE|| 
		  V_SQL_SPART_CN_NAME||'  --202401版本新增SPART层级
         T1.SHIPPING_OBJECT_CODE,
         T1.SHIPPING_OBJECT_CN_NAME,
         T1.MANUFACTURE_OBJECT_CODE,
         T1.MANUFACTURE_OBJECT_CN_NAME, 
         T1.ITEM_CODE  ,
         T1.ITEM_CN_NAME ,
         T1.SHIP_QUANTITY,
         T1.RMB_COST_AMT,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         T1.OVERSEA_FLAG ,  
         T1.LV0_PROD_LIST_CODE  ,  
         T1.LV0_PROD_LIST_CN_NAME,
         T1.CUSTOM_ID   ,
         T1.CUSTOM_CN_NAME,
         T1.CUSTOM_LEVEL 
    FROM DECODE_MADE_DATA_TEMP  T1
   INNER JOIN TOP_ITEM T
      ON T1.VIEW_FLAG = T.VIEW_FLAG
     AND T1.LV0_PROD_RND_TEAM_CODE = T.LV0_PROD_RND_TEAM_CODE
     AND NVL(T1.LV1_PROD_RND_TEAM_CODE, 1) =
         NVL(T.LV1_PROD_RND_TEAM_CODE, 1)
     AND NVL(T1.LV2_PROD_RND_TEAM_CODE, 2) =
         NVL(T.LV2_PROD_RND_TEAM_CODE, 2)
         '||V_JION_LV3_PROD_RND_TEAM_CODE
          ||V_JOIN_L1_NAME
          ||V_JOIN_L2_NAME
		  ||V_JOIN_LV4_CODE		--202407版本 IAS新增LV4层级
		  ||V_JOIN_COA_CODE		--202405版本 数字能源新增COA层级
          ||V_JOIN_DIMENSION_CODE
          ||V_JOIN_DIMENSION_SUBCATEGORY_CODE
          ||V_JOIN_DIMENSION_SUB_DETAIL_CODE
		  ||V_JOIN_SPART_CODE||'  --202401版本新增SPART层级
     AND T1.SHIPPING_OBJECT_CODE = 	  T.TOP_SHIPPING_OBJECT_CODE
     
     AND T1.MANUFACTURE_OBJECT_CODE = T.TOP_MANUFACTURE_OBJECT_CODE
     AND T1.ITEM_CODE = T.TOP_ITEM_CODE
     AND T1.CALIBER_FLAG = T.CALIBER_FLAG
     AND T1.OVERSEA_FLAG = T.OVERSEA_FLAG  
     AND T1.LV0_PROD_LIST_CODE = T.LV0_PROD_LIST_CODE  
     ;';
   
 EXECUTE IMMEDIATE V_SQL;

 
 
    V_SQL := 
  'INSERT INTO '|| V_TO_TABLE ||' 
       (VERSION_ID,
        PERIOD_YEAR,
        PERIOD_ID,
        VIEW_FLAG,
        GRANULARITY_TYPE,
        PARENT_CODE,
        PARENT_CN_NAME,
        PARENT_LEVEL,
        GROUP_CODE,
        GROUP_CN_NAME,
        RMB_AVG_AMT,
        RMB_COST_AMT ,
        SHIP_QUANTITY ,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME,
        OVERSEA_FLAG,
        CALIBER_FLAG,
        CUSTOM_ID,
        CUSTOM_CN_NAME,
        CREATED_BY ,
        CREATION_DATE,
        LAST_UPDATED_BY ,
        LAST_UPDATE_DATE
        )

  SELECT T3.VERSION_ID,
         T3.PERIOD_YEAR,
         T3.PERIOD_ID,
         T3.VIEW_FLAG,
         T3.DIMENSION_TYPE,
         T3.PARENT_CODE,
         T3.PARENT_CN_NAME,
         T3.PARENT_LEVEL, 
         T3.GROUP_CODE,
         T3.GROUP_CN_NAME,
         CASE
           WHEN SUM(T3.RMB_SUM_AMT) = 0 OR SUM(T3.RMB_SUM_QTY) = 0 THEN
            NULL --聚合后, 有量无价或者有价无量的情况, 均价置空
           ELSE
            SUM(T3.RMB_SUM_AMT) / SUM(T3.RMB_SUM_QTY) --总金额/总销量
         END AS RMB_AVG_AMT, 
         SUM(T3.RMB_SUM_AMT) AS RMB_SUM_AMT,
         SUM(T3.RMB_SUM_QTY) AS RMB_SUM_QTY,
         T3.LV0_PROD_LIST_CODE,
         T3.LV0_PROD_LIST_CN_NAME,
         T3.OVERSEA_FLAG,
         T3.CALIBER_FLAG,
         T3.CUSTOM_ID,
         T3.CUSTOM_CN_NAME,
         T3.CREATED_BY,
         T3.CREATION_DATE,
         T3.LAST_UPDATED_BY,
         T3.LAST_UPDATE_DATE
FROM 
(
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         T2.PERIOD_YEAR,
         T2.PERIOD_ID,
         T2.VIEW_FLAG,
         T2.DIMENSION_TYPE,
         DECODE(PARENT_LEVEL,SUBSTR(CUSTOM_LEVEL,3),T2.CUSTOM_ID,T2.PARENT_CODE) AS PARENT_CODE,
         DECODE(PARENT_LEVEL,SUBSTR(CUSTOM_LEVEL,3),T2.CUSTOM_CN_NAME,T2.PARENT_CN_NAME)  AS PARENT_CN_NAME,
         T2.PARENT_LEVEL, 
         T2.GROUP_CODE,
         T2.GROUP_CN_NAME,
         T2.RMB_SUM_AMT,
         T2.RMB_SUM_QTY,
         T2.LV0_PROD_LIST_CODE,
         T2.LV0_PROD_LIST_CN_NAME,
         T2.OVERSEA_FLAG,
         T2.CALIBER_FLAG,
         T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T2.CREATED_BY,
         T2.CREATION_DATE,
         T2.LAST_UPDATED_BY,
         T2.LAST_UPDATE_DATE
    FROM (
       SELECT T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.VIEW_FLAG,
              '''||F_DIMENSION_TYPE||''' AS DIMENSION_TYPE,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
            DECODE(NVL(T1.PARENT_CODE,''999999999999''),LV0_PROD_RND_TEAM_CODE,''LV0'',
                              LV1_PROD_RND_TEAM_CODE,''LV1'',
                              LV2_PROD_RND_TEAM_CODE,''LV2'','||
                              V_SQL_LVL||'
                              SHIPPING_OBJECT_CODE,''SHIPPING_OBJECT'',
                              MANUFACTURE_OBJECT_CODE,''MANUFACTURE_OBJECT'',
                                                               
                   '''')AS PARENT_LEVEL, 
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
            --T1.RMB_AVG_AMT,
            T1.RMB_SUM_AMT,
            T1.RMB_SUM_QTY,
            T1.LV0_PROD_LIST_CODE,
            T1.LV0_PROD_LIST_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.CALIBER_FLAG,
            T1.CUSTOM_ID,
            T1.CUSTOM_CN_NAME,
			T1.CUSTOM_LEVEL,
            -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
      FROM (
      SELECT T.PERIOD_YEAR,
              T.PERIOD_ID,
              T.CUSTOM_ID,
              T.CUSTOM_CN_NAME,
			  T.CUSTOM_LEVEL,
              T.VIEW_FLAG,
              T.ITEM_CODE AS GROUP_CODE,
              T.ITEM_CN_NAME AS GROUP_CN_NAME,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.OVERSEA_FLAG, 
              T.CALIBER_FLAG,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV2_PROD_RND_TEAM_CODE,'||
             V_LV3_PROD_RND_TEAM_CODE||
			 V_LV4_PART||	--202407版本 IAS新增LV4层级
             V_L1_NAME||
             V_L2_NAME||
			 V_COA_PART||	--202405版本 数字能源新增COA层级
             V_DIMENSION_CODE||
             V_DIMENSION_SUBCATEGORY_CODE||
             V_DIMENSION_SUB_DETAIL_CODE||
			 V_SPART_CODE||'  --202401版本新增SPART层级
             T.SHIPPING_OBJECT_CODE,
             T.MANUFACTURE_OBJECT_CODE,
             
      SUM(T.RMB_COST_AMT) AS RMB_SUM_AMT,
      SUM(SHIP_QUANTITY) AS RMB_SUM_QTY,  
     (LV0_PROD_RND_TEAM_CODE||LV1_PROD_RND_TEAM_CODE||LV2_PROD_RND_TEAM_CODE
	 '||V_J_LV3_PROD_RND_TEAM_CODE||V_J_L1_NAME||V_J_L2_NAME
	 ||V_J_DIMENSION_CODE||V_J_DIMENSION_SUBCATEGORY_CODE||V_J_DIMENSION_SUB_DETAIL_CODE
	 ||V_J_COA_CODE		--202405版本 数字能源新增COA层级
	 ||V_J_LV4_CODE		--202407版本 IAS新增LV4层级
	 ||V_J_SPART_CODE||' 
	 ||SHIPPING_OBJECT_CODE||MANUFACTURE_OBJECT_CODE) AS PARENT_CODE,      --202401版本新增SPART层级
     (LV0_PROD_RD_TEAM_CN_NAME||LV1_PROD_RD_TEAM_CN_NAME||LV2_PROD_RD_TEAM_CN_NAME
	 '||V_J_LV3_PROD_RD_TEAM_CN_NAME||V_J_L1_NAME||V_J_L2_NAME
	 ||V_J_DIMENSION_CN_NAME||V_J_DIMENSION_SUBCATEGORY_CN_NAME||V_J_DIMENSION_SUB_DETAIL_CN_NAME
	 ||V_J_COA_CN_NAME		--202405版本 数字能源新增COA层级
	 ||V_J_LV4_CN_NAME		--202407版本 IAS新增LV4层级
	 ||V_J_SPART_CN_NAME||'
	 ||SHIPPING_OBJECT_CN_NAME||MANUFACTURE_OBJECT_CN_NAME) AS PARENT_CN_NAME  --202401版本新增SPART层级
     FROM TOP_MONTH_ITEM_TEMP T
       WHERE ITEM_CODE IS NOT NULL 
        GROUP BY T.PERIOD_YEAR,
                 T.PERIOD_ID,
                 T.CUSTOM_ID, 
                 T.CUSTOM_CN_NAME, 
				 T.CUSTOM_LEVEL,
                 T.VIEW_FLAG,
                 T.ITEM_CODE,
                 T.ITEM_CN_NAME,
                 T.LV0_PROD_LIST_CODE,
                 T.LV0_PROD_LIST_CN_NAME,
                 T.OVERSEA_FLAG,             
                 T.CALIBER_FLAG,
             GROUPING SETS(
             (LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME),
             (LV1_PROD_RND_TEAM_CODE,LV1_PROD_RD_TEAM_CN_NAME),
              (LV2_PROD_RND_TEAM_CODE,LV2_PROD_RD_TEAM_CN_NAME),'||
              V_SETS_LV3_PROD||
			  V_SETS_LV4	||		--202407版本 IAS新增LV4层级
              V_SETS_L1_NAME||
              V_SETS_L2_NAME||
			  V_SETS_COA	||	--202405版本 数字能源新增COA层级
              V_SETS_DIMENSION||
              V_SETS_DIMENSION_SUBCATEGORY||
              V_SETS_DIMENSION_SUB_DETAIL||
			  V_SETS_SPART||'  --202401版本新增SPART层级
             (SHIPPING_OBJECT_CODE,SHIPPING_OBJECT_CN_NAME),
             (MANUFACTURE_OBJECT_CODE,MANUFACTURE_OBJECT_CN_NAME)
			 
              )
		) T1
	 )T2 
	WHERE DECODE(PARENT_LEVEL,''ITEM'',''02'',''MANUFACTURE_OBJECT'',''03'',''SHIPPING_OBJECT'',''04'',
						   ''SPART'',''05'',''SUB_DETAIL'',''06'',''SUBCATEGORY'',''07'',''DIMENSION'',''08'',
						   ''L2'',''09'',''L1'',''10'',
						   ''COA'',''11'',		--202405版本 数字能源新增COA层级
						   ''LV4'',''11'',		--202407版本 IAS新增LV4层级
						   ''LV3'',''12'',''LV2'',''13'',''LV1'',''14'',''LV0'',''15'')		--202405版本 ICT修改为LV0
            <= SUBSTR(CUSTOM_LEVEL,1,2)  --202401版本新增SPART层级
) T3
GROUP BY 
         T3.VERSION_ID,
         T3.PERIOD_YEAR,
         T3.PERIOD_ID,
         T3.VIEW_FLAG,
         T3.DIMENSION_TYPE,
         T3.PARENT_CODE,
         T3.PARENT_CN_NAME,
         T3.PARENT_LEVEL, 
         T3.GROUP_CODE,
         T3.GROUP_CN_NAME,
         T3.LV0_PROD_LIST_CODE,
         T3.LV0_PROD_LIST_CN_NAME,
         T3.OVERSEA_FLAG,
         T3.CALIBER_FLAG,
         T3.CUSTOM_ID,
         T3.CUSTOM_CN_NAME,
         T3.CREATED_BY,
         T3.CREATION_DATE,
         T3.LAST_UPDATED_BY,
         T3.LAST_UPDATE_DATE
';    
 DBMS_OUTPUT.PUT_LINE(V_SQL);
   EXECUTE IMMEDIATE V_SQL;         
                  
  --3.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '收敛均价, 并插数到'||V_TO_TABLE||', 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
   
END



$$
/

