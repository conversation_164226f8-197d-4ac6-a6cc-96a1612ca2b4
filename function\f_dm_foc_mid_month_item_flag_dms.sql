-- Name: f_dm_foc_mid_month_item_flag_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_item_flag_dms(f_industry_flag character varying, f_caliber_flag character varying, f_dimension_type character varying, f_view_flag character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
最后修改人:罗若文
背景描述:分视角统计ITEM的月卷积发货额
修改： 量纲增加 SPART_CODE 和 SPART_CN_NAME 字段,并增加字段 REVIEW_ITEM_FLAG 是否底层数据审视 许灿烽 20231221
	   增加数字能源适配，COA层级
参数描述:f_caliber_flag : 业务口径(R：收入时点,C：发货成本), f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T(发货成本),FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T(收入时点)
目标表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_ITEM_FLAG_DMS'; --存储过程名称
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_SQL        TEXT;   --SQL逻辑
  V_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50); -- 7月版本需求新增
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV1_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV2_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_JOIN_TABLE VARCHAR(500);
  
  -- 7月版本需求新增
  V_FROM_TABLE VARCHAR(100); -- 来源表
  V_TO_TABLE VARCHAR(100); -- 目标表
  V_VIEW_CNT BIGINT; -- 处理通用颗粒度和盈利颗粒度视角数目不同

  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(100);
  V_DIMENSION_EN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_INSERT_DIMENSION_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
--202401月版本需求新增SPART
  V_SPART_CODE VARCHAR(50);
  V_SPART_CN_NAME VARCHAR(50);
  V_IN_SPART_CODE VARCHAR(50);
  V_IN_SPART_CN_NAME VARCHAR(50);
  V_INSERT_SPART_CODE VARCHAR(100);
  
  V_VERSION_REVIEW_PURC_ID BIGINT; --采购成本-底层数据审视  版本号
  V_REVIEW_TYPE  VARCHAR(50);
  
  
  --202405月版本需求新增
  V_COA_CODE VARCHAR(50);
  V_COA_CN_NAME VARCHAR(50);
  V_IN_COA_CODE VARCHAR(50);
  V_IN_COA_CN_NAME VARCHAR(50);
  V_INSERT_COA_CODE VARCHAR(100);
  V_VERSION_TABLE VARCHAR(100);
  
  V_SQL_VIEW TEXT;

BEGIN
  X_RESULT_STATUS = '1';
  
  --开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG);

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') OR F_CALIBER_FLAG NOT IN ('C','R') OR F_INDUSTRY_FLAG NOT IN ('I','E','IAS') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;

  
  --判断入参类型--ICT
   IF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CU';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'I' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CP'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'I' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_CD_DMS'; --来源表 
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS'; --目标表
	 
   ELSIF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RU';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'I' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RP'; --来源表 
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'I' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_TMP_RD_DMS'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_DMS'; --目标表
  
   --判断入参类型--数字能源
  ELSIF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'E' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CU';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'E' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CP'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_ITEM_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'E' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_CD_DMS'; --来源表 
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS'; --目标表
	 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'E' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RU';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'E' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RP'; --来源表 
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_ITEM_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'E' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_TMP_RD_DMS'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T_DMS'; --目标表 
  
     --判断入参类型--IAS
  ELSIF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CU';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CP'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_ITEM_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'C' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_CD_DMS'; --来源表 
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS'; --目标表
	 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RU';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RP'; --来源表 
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_ITEM_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_CALIBER_FLAG = 'R' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_TMP_RD_DMS'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T_DMS'; --目标表 
  
  ELSE
    NULL;
  END IF;  
  
  
  
 
    --清空目标表数据
  IF F_VIEW_FLAG IS NULL THEN
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' T WHERE T.CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  ELSE 
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' T WHERE T.CALIBER_FLAG = '''||F_CALIBER_FLAG||''' AND T.VIEW_FLAG = '''||F_VIEW_FLAG||'''';
  V_SQL_VIEW := ' AND A.VIEW_FLAG = '''||F_VIEW_FLAG||'''';
  END IF;

  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


    --7月版本需求新增
    --重置变量入参
    V_LV1_PROD_RND_TEAM_CODE := 'LV1_PROD_RND_TEAM_CODE,';
    V_LV1_PROD_RD_TEAM_CN_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
    V_LV2_PROD_RND_TEAM_CODE := 'LV2_PROD_RND_TEAM_CODE,';
    V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';

    V_IN_LV1_PROD_RND_TEAM_CODE := 'A.LV1_PROD_RND_TEAM_CODE,';
    V_IN_LV1_PROD_RD_TEAM_CN_NAME :='A.LV1_PROD_RD_TEAM_CN_NAME,';    
    V_IN_LV2_PROD_RND_TEAM_CODE := 'A.LV2_PROD_RND_TEAM_CODE,';
    V_IN_LV2_PROD_RD_TEAM_CN_NAME :='A.LV2_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'A.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME := 'A.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'A.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := 'A.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'A.L1_NAME,';
    V_IN_L2_NAME := 'A.L2_NAME,';
	
	
	V_INSERT_LV4_PROD_RND_TEAM_CODE := ' AND NVL(A.LV4_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV4_PROD_RND_TEAM_CODE,''SNULL'')';
	
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'A.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'A.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'A.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'A.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'A.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'A.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'A.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'A.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'A.DIMENSION_SUB_DETAIL_EN_NAME,';

	--202401月版本需求新增
    V_SPART_CODE :='SPART_CODE,';
    V_SPART_CN_NAME := 'SPART_CN_NAME,';
    V_IN_SPART_CODE := 'A.SPART_CODE,';
    V_IN_SPART_CN_NAME := 'A.SPART_CN_NAME,';
	
    --202405月版本新增
    V_COA_CODE :='COA_CODE,';
    V_COA_CN_NAME := 'COA_CN_NAME,';
    V_IN_COA_CODE := 'A.COA_CODE,';
    V_IN_COA_CN_NAME := 'A.COA_CN_NAME,';
	V_INSERT_COA_CODE := ' AND NVL(A.COA_CODE,''SNULL'') = NVL(B.COA_CODE,''SNULL'') ';

		--通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
	IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN 
		V_L1_NAME := '';
		V_L2_NAME := '';
		V_IN_L1_NAME := '';
		V_IN_L2_NAME := '';

		V_DIMENSION_CODE := '';
		V_DIMENSION_CN_NAME := '';
		V_DIMENSION_EN_NAME := '';
		V_DIMENSION_SUBCATEGORY_CODE := '';
		V_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_DIMENSION_SUBCATEGORY_EN_NAME := '';
		V_DIMENSION_SUB_DETAIL_CODE := '';
		V_DIMENSION_SUB_DETAIL_CN_NAME := '';
		V_DIMENSION_SUB_DETAIL_EN_NAME := '';
		V_IN_DIMENSION_CODE := '';
		V_IN_DIMENSION_CN_NAME := '';
		V_IN_DIMENSION_EN_NAME := '';
		V_IN_DIMENSION_SUBCATEGORY_CODE := '';
		V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
		V_IN_DIMENSION_SUB_DETAIL_CODE := '';
		V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
		V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
		--202401月版本需求新增
		V_SPART_CODE :='';
		V_SPART_CN_NAME := '';
		V_IN_SPART_CODE := '';
		V_IN_SPART_CN_NAME := '';

		--202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		V_INSERT_COA_CODE := '';
		
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
		--通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
	ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('IAS') THEN 
		V_L1_NAME := '';
		V_L2_NAME := '';
		V_IN_L1_NAME := '';
		V_IN_L2_NAME := '';

		V_DIMENSION_CODE := '';
		V_DIMENSION_CN_NAME := '';
		V_DIMENSION_EN_NAME := '';
		V_DIMENSION_SUBCATEGORY_CODE := '';
		V_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_DIMENSION_SUBCATEGORY_EN_NAME := '';
		V_DIMENSION_SUB_DETAIL_CODE := '';
		V_DIMENSION_SUB_DETAIL_CN_NAME := '';
		V_DIMENSION_SUB_DETAIL_EN_NAME := '';
		V_IN_DIMENSION_CODE := '';
		V_IN_DIMENSION_CN_NAME := '';
		V_IN_DIMENSION_EN_NAME := '';
		V_IN_DIMENSION_SUBCATEGORY_CODE := '';
		V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
		V_IN_DIMENSION_SUB_DETAIL_CODE := '';
		V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
		V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
		--202401月版本需求新增
		V_SPART_CODE :='';
		V_SPART_CN_NAME := '';
		V_IN_SPART_CODE := '';
		V_IN_SPART_CN_NAME := '';

		--202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		V_INSERT_COA_CODE := '';
		

	
	--盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
	ELSIF F_DIMENSION_TYPE = 'P'  THEN
		V_LV3_PROD_RND_TEAM_CODE := '';
		V_LV3_PROD_RD_TEAM_CN_NAME := '';
		V_IN_LV3_PROD_RND_TEAM_CODE := '';
		V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';

		V_DIMENSION_CODE := '';
		V_DIMENSION_CN_NAME := '';
		V_DIMENSION_EN_NAME := '';
		V_DIMENSION_SUBCATEGORY_CODE := '';
		V_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_DIMENSION_SUBCATEGORY_EN_NAME := '';
		V_DIMENSION_SUB_DETAIL_CODE := '';
		V_DIMENSION_SUB_DETAIL_CN_NAME := '';
		V_DIMENSION_SUB_DETAIL_EN_NAME := '';
		V_IN_DIMENSION_CODE := '';
		V_IN_DIMENSION_CN_NAME := '';
		V_IN_DIMENSION_EN_NAME := '';
		V_IN_DIMENSION_SUBCATEGORY_CODE := '';
		V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
		V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
		V_IN_DIMENSION_SUB_DETAIL_CODE := '';
		V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
		V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
		--202401月版本需求新增
		V_SPART_CODE :='';
		V_SPART_CN_NAME := '';
		V_IN_SPART_CODE := '';
		V_IN_SPART_CN_NAME := '';

		--202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		V_INSERT_COA_CODE := '';
		
		--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';


	
	--ICT-量纲颗粒度的维度时，不需要L1、L2字段,COA字段
	ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
		V_L1_NAME := '';
		V_L2_NAME := '';
		V_IN_L1_NAME := '';
		V_IN_L2_NAME := '';

		--202405月版本新增
		V_COA_CODE := '';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		V_INSERT_COA_CODE := '';
        
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	--数字能源-量纲颗粒度的维度时，不需要L1、L2字段
	ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN
		V_L1_NAME := '';
		V_L2_NAME := '';
		V_IN_L1_NAME := '';
		V_IN_L2_NAME := '';
		
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN
		V_L1_NAME := '';
		V_L2_NAME := '';
		V_IN_L1_NAME := '';
		V_IN_L2_NAME := '';

		--202405月版本新增
		V_COA_CODE := '';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		V_INSERT_COA_CODE := '';
	
    ELSE
      NULL;
    END IF;
    

    
--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSE 
     NULL ;
	 
  END IF;


  --创建单item品类临时表
    DROP TABLE IF EXISTS  DIST_DATA_FLAG;
    CREATE TEMPORARY TABLE  DIST_DATA_FLAG (
        VIEW_FLAG VARCHAR(2),
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
		LV4_PROD_RND_TEAM_CODE    VARCHAR(50),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        DIMENSION_CODE    VARCHAR(500),
        DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
        DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
        SPART_CODE VARCHAR(50),
		COA_CODE VARCHAR(50),
        L3_CEG_CODE    VARCHAR(50),
        L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
        CATEGORY_CODE CHARACTER VARYING(50),
        ITEM_CODE CHARACTER VARYING(50),
        OVERSEA_FLAG VARCHAR(2),-- 9月版本需求新增
        LV0_PROD_LIST_CODE VARCHAR(50)
    )
    ON COMMIT PRESERVE ROWS 
    DISTRIBUTE BY ROUNDROBIN;


 
 
--将加工好的数据插入单item品类临时表
    V_SQL := 
    'INSERT INTO DIST_DATA_FLAG
    (VIEW_FLAG,
    OVERSEA_FLAG,
    LV0_PROD_LIST_CODE,
    L3_CEG_CODE,
    L4_CEG_CODE,
    CATEGORY_CODE,
    ITEM_CODE,'||
    V_DIMENSION_SUB_DETAIL_CODE ||
    V_DIMENSION_SUBCATEGORY_CODE ||
    V_DIMENSION_CODE ||
    V_SPART_CODE ||
	V_COA_CODE||
    V_L1_NAME ||
    V_L2_NAME ||
    V_LV1_PROD_RND_TEAM_CODE ||
    V_LV2_PROD_RND_TEAM_CODE ||
    V_LV3_PROD_RND_TEAM_CODE ||
	V_LV4_PROD_RND_TEAM_CODE ||'
    LV0_PROD_RND_TEAM_CODE
    )
      --筛选出单item品类的维度信息
      SELECT B.VIEW_FLAG,B.OVERSEA_FLAG,B.LV0_PROD_LIST_CODE,B.L3_CEG_CODE, B.L4_CEG_CODE, B.CATEGORY_CODE, B.ITEM_CODE ,'||
             V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_CODE || V_SPART_CODE ||V_COA_CODE|| V_L1_NAME || V_L2_NAME || V_LV1_PROD_RND_TEAM_CODE || V_LV2_PROD_RND_TEAM_CODE || V_LV3_PROD_RND_TEAM_CODE || V_LV4_PROD_RND_TEAM_CODE||' B.LV0_PROD_RND_TEAM_CODE
        FROM (SELECT A.LV0_PROD_RND_TEAM_CODE,'||
                     V_LV1_PROD_RND_TEAM_CODE ||
                     V_LV2_PROD_RND_TEAM_CODE ||
                     V_LV3_PROD_RND_TEAM_CODE ||
					 V_LV4_PROD_RND_TEAM_CODE ||
                     V_L1_NAME || 
                     V_L2_NAME ||
                     V_DIMENSION_CODE ||
                     V_DIMENSION_SUBCATEGORY_CODE ||
                     V_DIMENSION_SUB_DETAIL_CODE ||
                     V_SPART_CODE ||
					 V_COA_CODE||'
                     A.L3_CEG_CODE,
                     A.L4_CEG_CODE,
                     A.CATEGORY_CODE,
                     A.ITEM_CODE,
                     A.VIEW_FLAG,
                     A.OVERSEA_FLAG,
                     A.LV0_PROD_LIST_CODE,
                     COUNT(1) OVER(PARTITION BY '||V_COA_CODE||V_SPART_CODE|| V_DIMENSION_SUB_DETAIL_CODE||V_DIMENSION_SUBCATEGORY_CODE||V_DIMENSION_CODE||V_L1_NAME||V_L2_NAME||V_LV4_PROD_RND_TEAM_CODE||V_LV3_PROD_RND_TEAM_CODE||V_LV2_PROD_RND_TEAM_CODE||V_LV1_PROD_RND_TEAM_CODE||'A.LV0_PROD_RND_TEAM_CODE,A.L3_CEG_CODE,A.L4_CEG_CODE, A.CATEGORY_CODE, A.VIEW_FLAG,A.OVERSEA_FLAG,A.LV0_PROD_LIST_CODE) AS ITEM_FLAG
                  FROM (SELECT DISTINCT VIEW_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,L3_CEG_CODE,L4_CEG_CODE,CATEGORY_CODE,ITEM_CODE,
                               '||V_COA_CODE||V_SPART_CODE ||V_DIMENSION_SUB_DETAIL_CODE||V_DIMENSION_SUBCATEGORY_CODE||V_DIMENSION_CODE||V_L1_NAME||V_L2_NAME||V_LV4_PROD_RND_TEAM_CODE||V_LV3_PROD_RND_TEAM_CODE||V_LV2_PROD_RND_TEAM_CODE||V_LV1_PROD_RND_TEAM_CODE||' LV0_PROD_RND_TEAM_CODE
                          FROM '||V_FROM_TABLE||' A
                          				 WHERE 1=1 
										 '||V_SQL_VIEW||'

                          ) A
                     ) B
       WHERE ITEM_FLAG = 1;
    ';
 DBMS_OUTPUT.PUT_LINE(V_SQL); 
 EXECUTE IMMEDIATE V_SQL;
 
 
    V_SQL := 
    'INSERT INTO '|| V_TO_TABLE ||' 
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,'||
       V_LV1_PROD_RND_TEAM_CODE ||
       V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE ||
       V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||
	   V_LV4_PROD_RND_TEAM_CODE ||
       V_LV4_PROD_RD_TEAM_CN_NAME ||
       V_L1_NAME ||
       V_L2_NAME ||
       V_DIMENSION_CODE ||
       V_DIMENSION_CN_NAME ||
       V_DIMENSION_EN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE ||
       V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_EN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE ||
       V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_EN_NAME ||
       V_SPART_CODE ||
       V_SPART_CN_NAME ||
	   V_COA_CODE||
	   V_COA_CN_NAME||'
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       SHIP_QUANTITY,
       RMB_COST_AMT,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       VIEW_FLAG,
       ONLY_ITEM_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
       LV0_PROD_LIST_EN_NAME,
       REVIEW_ITEM_FLAG)
   
      SELECT A.VERSION_ID,
                        A.PERIOD_YEAR,
                        A.PERIOD_ID,
                        A.LV0_PROD_RND_TEAM_CODE,
                        A.LV0_PROD_RD_TEAM_CN_NAME,' ||
                        V_IN_LV1_PROD_RND_TEAM_CODE ||
                        V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
                        V_IN_LV2_PROD_RND_TEAM_CODE ||
                        V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
                        V_IN_LV3_PROD_RND_TEAM_CODE ||
                        V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
						V_IN_LV4_PROD_RND_TEAM_CODE ||
                        V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                        V_IN_L1_NAME ||
                        V_IN_L2_NAME ||
                        V_IN_DIMENSION_CODE ||
                        V_IN_DIMENSION_CN_NAME ||
                        V_IN_DIMENSION_EN_NAME||
                        V_IN_DIMENSION_SUBCATEGORY_CODE ||
                        V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                        V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                        V_IN_DIMENSION_SUB_DETAIL_CODE ||
                        V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                        V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
                        V_IN_SPART_CODE ||
                        V_IN_SPART_CN_NAME ||
						V_IN_COA_CODE||
						V_IN_COA_CN_NAME||'
                        A.L3_CEG_CODE,
                        A.L3_CEG_CN_NAME,
                        A.L3_CEG_SHORT_CN_NAME,
                        A.L4_CEG_CODE,
                        A.L4_CEG_CN_NAME,
                        A.L4_CEG_SHORT_CN_NAME,
                        A.CATEGORY_CODE,
                        A.CATEGORY_CN_NAME,
                        A.ITEM_CODE,
                        A.ITEM_CN_NAME,
                        A.SHIP_QUANTITY,
                        A.COST_AMT,
                        -1 AS CREATED_BY,
                        CURRENT_TIMESTAMP AS CREATION_DATE,
                        -1 AS LAST_UPDATED_BY,
                        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                        ''N'' AS DEL_FLAG,
                        A.VIEW_FLAG,
                        DECODE(B.ITEM_CODE, NULL, ''N'', ''Y'') AS ONLY_ITEM_FLAG,
                        '''||F_CALIBER_FLAG||''',
                        A.OVERSEA_FLAG,
                        A.LV0_PROD_LIST_CODE,
                        A.LV0_PROD_LIST_CN_NAME,
                        A.LV0_PROD_LIST_EN_NAME,
                        CASE WHEN A.PERIOD_ID BETWEEN C.START_PERIOD AND C.END_PERIOD THEN 1 ELSE 0 END AS REVIEW_ITEM_FLAG
                    FROM '||V_FROM_TABLE||' A
               LEFT JOIN DIST_DATA_FLAG B
                     ON A.L3_CEG_CODE = B.L3_CEG_CODE
                    AND A.L4_CEG_CODE = B.L4_CEG_CODE
                    AND A.CATEGORY_CODE = B.CATEGORY_CODE
                    AND A.ITEM_CODE = B.ITEM_CODE
                    AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
                    AND A.VIEW_FLAG = B.VIEW_FLAG
                    AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE
                    AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
                   AND NVL(A.LV1_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV1_PROD_RND_TEAM_CODE,''SNULL'')
                   AND NVL(A.LV2_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV2_PROD_RND_TEAM_CODE,''SNULL'')
				   AND NVL(A.LV3_PROD_RND_TEAM_CODE,''SNULL'') = NVL(B.LV3_PROD_RND_TEAM_CODE,''SNULL'')
				   '||V_INSERT_LV4_PROD_RND_TEAM_CODE||'
                   AND NVL(A.L1_NAME,''SNULL'') = NVL(B.L1_NAME,''SNULL'') 
                   AND NVL(A.L2_NAME,''SNULL'') = NVL(B.L2_NAME,''SNULL'')  
                   AND NVL(A.DIMENSION_CODE,''SNULL'') = NVL(B.DIMENSION_CODE,''SNULL'')   
                   AND NVL(A.DIMENSION_SUBCATEGORY_CODE,''SNULL'') = NVL(B.DIMENSION_SUBCATEGORY_CODE,''SNULL'')   
                   AND NVL(A.DIMENSION_SUB_DETAIL_CODE,''SNULL'') = NVL(B.DIMENSION_SUB_DETAIL_CODE,''SNULL'')   
                   AND NVL(A.SPART_CODE,''SNULL'') = NVL(B.SPART_CODE,''SNULL'')
				   '||V_INSERT_COA_CODE||'
               LEFT JOIN (SELECT  DISTINCT ITEM_CODE,
							FIRST_VALUE(START_PERIOD)OVER(PARTITION BY ITEM_CODE ORDER BY START_PERIOD) AS START_PERIOD,
							FIRST_VALUE(END_PERIOD)OVER(PARTITION BY ITEM_CODE ORDER BY END_PERIOD DESC) AS END_PERIOD
							FROM 
							(SELECT ITEM_CODE,START_PERIOD,END_PERIOD FROM					
							(SELECT ITEM_CODE,START_PERIOD,END_PERIOD,DEL_FLAG,MODIFY_TYPE,
							ROW_NUMBER()OVER(PARTITION BY ITEM_CODE,START_PERIOD,END_PERIOD ORDER BY LAST_UPDATE_DATE DESC) AS RN
							FROM FIN_DM_OPT_FOI.DM_FOC_BASE_DATA_REVIEW_INFO_T WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||''') 
						WHERE RN=1 AND DEL_FLAG = ''N'' AND MODIFY_TYPE <> ''REVOKE'')) C 
                 ON A.ITEM_CODE = C.ITEM_CODE 
				 WHERE 1=1 '||V_SQL_VIEW
                    ;


 DBMS_OUTPUT.PUT_LINE(V_SQL); 
 EXECUTE IMMEDIATE V_SQL;

--END LOOP ;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '视角标识:, 计算ITEM的月卷积发货额, 版本号='||V_VERSION_ID||', 并给单ITEM的品类打上标识,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
 

  --收集统计信息
  --EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
  
  RETURN 'SUCCESS';
    
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,颗粒度：'||F_DIMENSION_TYPE||',时点：'||F_CALIBER_FLAG||',产业类型：'||F_INDUSTRY_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END
$$
/

