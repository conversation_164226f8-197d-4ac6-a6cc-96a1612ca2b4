-- 索引优化脚本
-- 为相关表创建必要的索引，特别是针对JOIN条件和WHERE过滤条件的字段
-- 优化策略8：未充分利用索引加速查询，要积极加索引，当前是数据更新频率较低的场景，加索引是更优解

-- =====================================================
-- 1. 主要事实表索引优化
-- =====================================================

-- 1.1 DWL_PROD_BOM_ITEM_SHIP_DIM_I 主表索引
-- 基于PERIOD_ID的分区索引（如果表已分区）
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_period_id 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(PERIOD_ID);

-- 基于ITEM_CODE的索引，用于关联物料维表
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_item_code 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(ITEM_CODE);

-- 基于PROD_KEY的索引，用于关联产品维表
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_prod_key 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(PROD_KEY);

-- 基于GEO_PC_KEY的索引，用于关联区域维表
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_geo_pc_key 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(GEO_PC_KEY);

-- 基于MAIN_DIMENSION_KEY的索引，用于关联量纲维表
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_main_dim_key 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(MAIN_DIMENSION_KEY);

-- 基于PRIMARY_ID的索引，用于关联加密表
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_primary_id 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(PRIMARY_ID);

-- 复合索引：基于常用过滤条件
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_filter_combo 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(PERIOD_ID, MODEL_NUM, RECOGNISE_TYPE_ID, IS_RESALE_FLAG);

-- 复合索引：基于数量过滤
CREATE INDEX IF NOT EXISTS idx_dwl_prod_bom_quantity_filter 
ON FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I(SHIP_QUANTITY, PERIOD_ID) 
WHERE SHIP_QUANTITY >= 0;

-- =====================================================
-- 2. 维度表索引优化
-- =====================================================

-- 2.1 物料维度表索引
CREATE INDEX IF NOT EXISTS idx_dwr_dim_material_item_code 
ON DWRDIM.DWR_DIM_MATERIAL_CODE_D(ITEM_CODE);

CREATE INDEX IF NOT EXISTS idx_dwr_dim_material_subtype_code 
ON DWRDIM.DWR_DIM_MATERIAL_CODE_D(ITEM_SUBTYPE_CODE);

-- 复合索引：物料代码和子类型
CREATE INDEX IF NOT EXISTS idx_dwr_dim_material_combo 
ON DWRDIM.DWR_DIM_MATERIAL_CODE_D(ITEM_CODE, ITEM_SUBTYPE_CODE);

-- 2.2 产品维度表索引
CREATE INDEX IF NOT EXISTS idx_dm_dim_product_prod_key 
ON DMDIM.DM_DIM_PRODUCT_D(PROD_KEY);

CREATE INDEX IF NOT EXISTS idx_dm_dim_product_prod_code 
ON DMDIM.DM_DIM_PRODUCT_D(PROD_CODE);

-- 基于重量级团队LV0的索引
CREATE INDEX IF NOT EXISTS idx_dm_dim_product_lv0_team 
ON DMDIM.DM_DIM_PRODUCT_D(LV0_PROD_RND_TEAM_CODE);

-- 基于重量级团队LV1的索引（IAS专用）
CREATE INDEX IF NOT EXISTS idx_dm_dim_product_lv1_team 
ON DMDIM.DM_DIM_PRODUCT_D(LV1_PROD_RND_TEAM_CODE);

-- 复合索引：重量级团队层级
CREATE INDEX IF NOT EXISTS idx_dm_dim_product_team_combo 
ON DMDIM.DM_DIM_PRODUCT_D(LV0_PROD_RND_TEAM_CODE, LV1_PROD_RND_TEAM_CODE, LV2_PROD_RND_TEAM_CODE);

-- 复合索引：产品列表代码过滤
CREATE INDEX IF NOT EXISTS idx_dm_dim_product_list_code 
ON DMDIM.DM_DIM_PRODUCT_D(LV0_PROD_LIST_CODE);

-- 2.3 量纲维度表索引
CREATE INDEX IF NOT EXISTS idx_dm_foc_dim_productdim_key 
ON DM_FOC_DIM_PRODUCTDIMENSION_D(DIMENSION_KEY);

-- 2.4 区域维度表索引
CREATE INDEX IF NOT EXISTS idx_dwr_dim_region_geo_key 
ON DWRDIM.DWR_DIM_REGION_RC_D(GEO_PC_KEY);

CREATE INDEX IF NOT EXISTS idx_dwr_dim_region_oversea_flag 
ON DWRDIM.DWR_DIM_REGION_RC_D(OVERSEA_FLAG);

-- =====================================================
-- 3. 品类专家团映射表索引优化
-- =====================================================

-- 3.1 ICT品类专家团映射表
CREATE INDEX IF NOT EXISTS idx_dm_foc_catg_ceg_ict_version 
ON FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D(VERSION_ID, DEL_FLAG);

CREATE INDEX IF NOT EXISTS idx_dm_foc_catg_ceg_ict_category 
ON FIN_DM_OPT_FOI.DM_FOC_CATG_CEG_ICT_D(CATEGORY_CODE);

-- 3.2 数字能源品类专家团映射表
CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_catg_ceg_version 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_CATG_CEG_ICT_D(VERSION_ID, DEL_FLAG);

CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_catg_ceg_category 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_CATG_CEG_ICT_D(CATEGORY_CODE);

-- 3.3 IAS品类专家团映射表
CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_catg_ceg_version 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_CATG_CEG_ICT_D(VERSION_ID, DEL_FLAG);

CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_catg_ceg_category 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_CATG_CEG_ICT_D(CATEGORY_CODE);

-- =====================================================
-- 4. 版本信息表索引优化
-- =====================================================

-- 4.1 ICT版本信息表
CREATE INDEX IF NOT EXISTS idx_dm_foc_version_info_type_status 
ON FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(DATA_TYPE, DEL_FLAG, STATUS);

CREATE INDEX IF NOT EXISTS idx_dm_foc_version_info_version 
ON FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(VERSION, DEL_FLAG, STATUS, DATA_TYPE);

CREATE INDEX IF NOT EXISTS idx_dm_foc_version_info_update_date 
ON FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(LAST_UPDATE_DATE DESC);

-- 4.2 数字能源版本信息表
CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_version_type_status 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T(DATA_TYPE, DEL_FLAG, STATUS);

CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_version_version 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T(VERSION, DEL_FLAG, STATUS, DATA_TYPE);

CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_version_update_date 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T(LAST_UPDATE_DATE DESC);

-- 4.3 IAS版本信息表
CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_version_type_status 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T(DATA_TYPE, DEL_FLAG, STATUS);

CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_version_version 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T(VERSION, DEL_FLAG, STATUS, DATA_TYPE);

CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_version_update_date 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T(LAST_UPDATE_DATE DESC);

-- =====================================================
-- 5. 业务相关表索引优化
-- =====================================================

-- 5.1 加密数据表
CREATE INDEX IF NOT EXISTS idx_dm_foc_data_primary_encrypt_id 
ON FIN_DM_OPT_FOI.DM_FOC_DATA_PRIMARY_ENCRYPT_T(PRIMARY_ID);

-- 5.2 ICT业务预测全景图表
CREATE INDEX IF NOT EXISTS idx_apd_fop_ict_fcst_lv_codes 
ON FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T(LV1_CODE, LV2_CODE, LV3_CODE);

CREATE INDEX IF NOT EXISTS idx_apd_fop_ict_fcst_status 
ON FIN_DM_OPT_FOP.APD_FOP_ICT_FCST_HOLISTIC_VIEW_T(DEL_FLAG, STATUS);

-- 5.3 产品L1配置表
CREATE INDEX IF NOT EXISTS idx_dm_foc_prod_l1_cfg_team_names 
ON FIN_DM_OPT_FOI.DM_FOC_PROD_L1_CFG_T(LV1_PROD_RD_TEAM_CN_NAME, LV2_PROD_RD_TEAM_CN_NAME, LV3_PROD_RD_TEAM_CN_NAME);

-- 5.4 COA L1表
CREATE INDEX IF NOT EXISTS idx_apd_fop_coa_l1_name_code 
ON FIN_DM_OPT_FOP.APD_FOP_COA_L1_T(L1_NAME, COA_CODE);

CREATE INDEX IF NOT EXISTS idx_apd_fop_coa_l1_status 
ON FIN_DM_OPT_FOP.APD_FOP_COA_L1_T(DEL_FLAG, STATUS);

-- 5.5 SPART盈利关系表
CREATE INDEX IF NOT EXISTS idx_dm_dim_fop_spart_profiting_period 
ON FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T(PERIOD_ID);

CREATE INDEX IF NOT EXISTS idx_dm_dim_fop_spart_profiting_status 
ON FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T(DEL_FLAG, STATUS);

CREATE INDEX IF NOT EXISTS idx_dm_dim_fop_spart_profiting_combo 
ON FIN_DM_OPT_FOP.DM_DIM_FOP_SPART_PROFITING_RELATION_T(L1_NAME, ITEM_CODE);

-- =====================================================
-- 6. 目标表索引优化
-- =====================================================

-- 6.1 ICT目标表
CREATE INDEX IF NOT EXISTS idx_dm_foc_bom_item_ship_dtl_period 
ON FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T(PERIOD_ID);

CREATE INDEX IF NOT EXISTS idx_dm_foc_bom_item_ship_dtl_version 
ON FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T(VERSION_ID);

-- 6.2 数字能源目标表
CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_bom_item_ship_period 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T(PERIOD_ID);

CREATE INDEX IF NOT EXISTS idx_dm_foc_energy_bom_item_ship_version 
ON FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T(VERSION_ID);

-- 6.3 IAS目标表
CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_bom_item_ship_period 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T(PERIOD_ID);

CREATE INDEX IF NOT EXISTS idx_dm_foc_ias_bom_item_ship_version 
ON FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T(VERSION_ID);

-- =====================================================
-- 索引创建完成提示
-- =====================================================
-- 注意：
-- 1. 所有索引都使用IF NOT EXISTS避免重复创建
-- 2. 索引命名遵循统一规范：idx_表名简称_字段名
-- 3. 复合索引按照查询频率和选择性排序
-- 4. 建议在业务低峰期执行此脚本
-- 5. 执行后需要收集表统计信息：ANALYZE TABLE_NAME;
