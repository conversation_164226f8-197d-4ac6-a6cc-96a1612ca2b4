-- Name: f_dm_foc_mid_month_cate; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_cate(f_industry_flag character varying, f_view_flag character varying, f_dimension_type character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*

最后修改人:罗若文
背景描述： 分视角统计品类的月卷积发货额
		   增加数字能源适配
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_CATE_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_CATE_T(量纲颗粒度)
事例：FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_CATE()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_CATE'; --存储过程名称
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); 
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50); 
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_L1_NAME VARCHAR(100);
  V_L2_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_L1_NAME VARCHAR(100);
  V_IN_L2_NAME VARCHAR(100);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_L1_NAME VARCHAR(200);
  V_INSERT_L2_NAME VARCHAR(200);
  
  V_FROM_TABLE VARCHAR(50); -- 来源表
  V_TO_TABLE VARCHAR(50); -- 目标表
  V_VIEW_CNT BIGINT; -- 处理通用颗粒度和盈利颗粒度视角数目不同
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(100);
  V_DIMENSION_EN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_CODE VARCHAR(100);
  V_IN_DIMENSION_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);


--202401月版本需求新增SPART
  V_SPART_CODE VARCHAR(50);
  V_SPART_CN_NAME VARCHAR(50);
  V_IN_SPART_CODE VARCHAR(50);
  V_IN_SPART_CN_NAME VARCHAR(50);
  
  --202405月版本需求
  V_COA_CODE VARCHAR(50);
  V_COA_CN_NAME VARCHAR(50);
  V_IN_COA_CODE VARCHAR(50);
  V_IN_COA_CN_NAME VARCHAR(50);
  V_INSERT_COA_CODE VARCHAR(100);
  V_VERSION_TABLE VARCHAR(100);
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
 
 --判断入参是ICT还是数字能源 和 颗粒度
  IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'I' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_CATE_T'; --目标表 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_CATE_T'; --目标表 
	 
   ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'E' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_CATE_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'E' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_CATE_T'; --目标表 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_CATE_T'; --目标表 	

  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_CATE_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_CATE_T'; --目标表 
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_CATE_T'; --目标表
	 
  ELSE
    NULL;
  END IF;
  
  --1.清空目标表数据
  EXECUTE IMMEDIATE 'DELETE FROM   '||V_TO_TABLE||' WHERE VIEW_FLAG = '||F_VIEW_FLAG;
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG||'视角：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  
--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
   ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSE 
     NULL ;
	 
  END IF;
  
    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T.LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='T.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'T.LV4_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_IN_LV4_PROD_RD_TEAM_CN_NAME :='T.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'T.L1_NAME,';
    V_IN_L2_NAME := 'T.L2_NAME,';
    
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'T.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'T.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'T.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'T.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'T.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'T.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'T.DIMENSION_SUB_DETAIL_EN_NAME,';

	--202401月版本需求新增SPART
    V_SPART_CODE :='SPART_CODE,';
    V_SPART_CN_NAME := 'SPART_CN_NAME,';
    V_IN_SPART_CODE := 'T.SPART_CODE,';
    V_IN_SPART_CN_NAME := 'T.SPART_CN_NAME,';
	
	--202405月版本新增
    V_COA_CODE :='COA_CODE,';
    V_COA_CN_NAME := 'COA_CN_NAME,';
    V_IN_COA_CODE := 'T.COA_CODE,';
    V_IN_COA_CN_NAME := 'T.COA_CN_NAME,';
	
    --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
		--202401月版本需求新增SPART
		V_SPART_CODE :='';
		V_SPART_CN_NAME := '';
		V_IN_SPART_CODE := '';
		V_IN_SPART_CN_NAME := '';
		
		--202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	
	ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('IAS') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
		--202401月版本需求新增SPART
		V_SPART_CODE :='';
		V_SPART_CN_NAME := '';
		V_IN_SPART_CODE := '';
		V_IN_SPART_CN_NAME := '';
		
		--202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';

	
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
   ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
		--202401月版本需求新增SPART
		V_SPART_CODE :='';
		V_SPART_CN_NAME := '';
		V_IN_SPART_CODE := '';
		V_IN_SPART_CN_NAME := '';
	
	--202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
		
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	

    --ICT-量纲颗粒度的维度时，不需要L1、L2字段,COA字段
   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   --202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
	
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	
	--数字能源-量纲颗粒度的维度时，不需要L1、L2字段
   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
    
	--202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	
	   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   --202405月版本新增
		V_COA_CODE :='';
		V_COA_CN_NAME := '';
		V_IN_COA_CODE := '';
		V_IN_COA_CN_NAME := '';
	

	
    ELSE
      NULL;
    END IF;



  --创建临时表, 用于插入ITEM层的数据
  DROP TABLE IF EXISTS MONTH_CATE_TEMP;
  CREATE TEMPORARY TABLE MONTH_CATE_TEMP
  (
     VERSION_ID INT8,
     PERIOD_YEAR INT8,
     PERIOD_ID INT8,
     LV0_PROD_RND_TEAM_CODE VARCHAR(50),
     LV0_PROD_RD_TEAM_CN_NAME VARCHAR(100),
     LV1_PROD_RND_TEAM_CODE VARCHAR(50),
     LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
     LV2_PROD_RND_TEAM_CODE VARCHAR(50),
     LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
     LV3_PROD_RND_TEAM_CODE VARCHAR(50),
     LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
	 LV4_PROD_RND_TEAM_CODE VARCHAR(50),
     LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
     L1_NAME VARCHAR(100),
     L2_NAME VARCHAR(100),
     --9月版本需求新增量纲
     DIMENSION_CODE    VARCHAR(100),
     DIMENSION_CN_NAME    VARCHAR(100),
     DIMENSION_EN_NAME    VARCHAR(100),
     DIMENSION_SUBCATEGORY_CODE    VARCHAR(100),
     DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(100),
     DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(100),
     DIMENSION_SUB_DETAIL_CODE    VARCHAR(100),
     DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(200),
     DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(200),
	--202401月版本需求新增SPART
	SPART_CODE VARCHAR(50),
	SPART_CN_NAME VARCHAR(50),
	--202405月版本需求新增COA
	COA_CODE VARCHAR(50),
	COA_CN_NAME VARCHAR(600),
     L3_CEG_CODE VARCHAR(50),
     L3_CEG_CN_NAME VARCHAR(200),
     L3_CEG_SHORT_CN_NAME VARCHAR(200),
     L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
     L4_CEG_CN_NAME    VARCHAR(200),
     L4_CEG_SHORT_CN_NAME    VARCHAR(200),
     CATEGORY_CODE VARCHAR(50),
     CATEGORY_CN_NAME VARCHAR(200),
     ITEM_CODE VARCHAR(50),
     ITEM_CN_NAME VARCHAR(500),
     COST_AMT VARCHAR(4000),
     VIEW_FLAG VARCHAR(2),
     CALIBER_FLAG VARCHAR(2),
     OVERSEA_FLAG VARCHAR(2),
     LV0_PROD_LIST_CODE VARCHAR(50),
     LV0_PROD_LIST_CN_NAME VARCHAR(200),
     LV0_PROD_LIST_EN_NAME VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  

    --分视角下计算ITEM的月卷积发货额
    V_SQL := 
   'INSERT INTO MONTH_CATE_TEMP
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,'||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||
	   V_LV4_PROD_RND_TEAM_CODE ||
       V_LV4_PROD_RD_TEAM_CN_NAME ||
       V_L1_NAME ||
       V_L2_NAME ||
       V_DIMENSION_CODE ||
       V_DIMENSION_CN_NAME ||
       V_DIMENSION_EN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE ||
       V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_EN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE ||
       V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_EN_NAME ||
       V_SPART_CODE ||
       V_SPART_CN_NAME ||
	   V_COA_CODE ||  --202405版本新增
       V_COA_CN_NAME ||'  
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       COST_AMT,
       VIEW_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
       LV0_PROD_LIST_EN_NAME)
       SELECT T.VERSION_ID,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_IN_LV4_PROD_RND_TEAM_CODE ||
              V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME||
              V_IN_L2_NAME||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_IN_SPART_CODE ||
              V_IN_SPART_CN_NAME ||
			  V_IN_COA_CODE ||  --202405版本新增
			  V_IN_COA_CN_NAME ||'  
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              TO_NUMBER(GS_DECRYPT(T.RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')) AS COST_AMT,
              T.VIEW_FLAG,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
          FROM '||V_FROM_TABLE||' T
          WHERE T.VIEW_FLAG = '||F_VIEW_FLAG||'
		  AND T.ONLY_ITEM_FLAG = ''N''
          AND T.REVIEW_ITEM_FLAG = 0 -- 底层数据审视逻辑处理（202401新增）
          AND T.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
          AND T.PERIOD_ID <
                  CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)';

        EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '计算ITEM的月卷积发货额, 版本号='||V_VERSION_ID||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');



    --分视角下计算品类的月卷积额
    V_SQL := 
   'INSERT INTO '||V_TO_TABLE||'
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,'||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||
	   V_LV4_PROD_RND_TEAM_CODE ||
       V_LV4_PROD_RD_TEAM_CN_NAME ||
       V_L1_NAME ||
       V_L2_NAME ||
       V_DIMENSION_CODE ||
       V_DIMENSION_CN_NAME ||
       V_DIMENSION_EN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE ||
       V_DIMENSION_SUBCATEGORY_CN_NAME ||
       V_DIMENSION_SUBCATEGORY_EN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE ||
       V_DIMENSION_SUB_DETAIL_CN_NAME ||
       V_DIMENSION_SUB_DETAIL_EN_NAME ||
       V_SPART_CODE ||
       V_SPART_CN_NAME ||
	   V_COA_CODE ||  --202405版本新增
	   V_COA_CN_NAME ||' 
       L3_CEG_CODE,
       L3_CEG_CN_NAME,
       L3_CEG_SHORT_CN_NAME,
       L4_CEG_CODE,
       L4_CEG_CN_NAME,
       L4_CEG_SHORT_CN_NAME,
       CATEGORY_CODE,
       CATEGORY_CN_NAME,
       RMB_COST_AMT,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       VIEW_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME,
       LV0_PROD_LIST_EN_NAME)
             
      SELECT T.VERSION_ID,
             T.PERIOD_YEAR,
             T.PERIOD_ID,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,
             T.LV2_PROD_RND_TEAM_CODE,
             T.LV2_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
			 V_IN_LV4_PROD_RND_TEAM_CODE ||
             V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
             V_IN_L1_NAME||
             V_IN_L2_NAME||
             V_IN_DIMENSION_CODE ||
             V_IN_DIMENSION_CN_NAME ||
             V_IN_DIMENSION_EN_NAME||
             V_IN_DIMENSION_SUBCATEGORY_CODE ||
             V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
             V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
             V_IN_DIMENSION_SUB_DETAIL_CODE ||
             V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
             V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
             V_IN_SPART_CODE ||
             V_IN_SPART_CN_NAME ||
			 V_IN_COA_CODE ||  --202405版本新增
			 V_IN_COA_CN_NAME ||' 
             T.L3_CEG_CODE,
             T.L3_CEG_CN_NAME,
             T.L3_CEG_SHORT_CN_NAME,
             T.L4_CEG_CODE,
             T.L4_CEG_CN_NAME,
             T.L4_CEG_SHORT_CN_NAME,
             T.CATEGORY_CODE,
             T.CATEGORY_CN_NAME,
             SUM(T.COST_AMT) AS COST_AMT,
             -1 AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             -1 AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             ''N'' AS DEL_FLAG,
             T.VIEW_FLAG,
             T.CALIBER_FLAG,
             T.OVERSEA_FLAG,
             T.LV0_PROD_LIST_CODE,
             T.LV0_PROD_LIST_CN_NAME,
             T.LV0_PROD_LIST_EN_NAME
        FROM MONTH_CATE_TEMP T
       GROUP BY T.VERSION_ID,
                T.VIEW_FLAG,
                T.LV0_PROD_RND_TEAM_CODE,
                T.LV0_PROD_RD_TEAM_CN_NAME,
                T.LV1_PROD_RND_TEAM_CODE,
                T.LV1_PROD_RD_TEAM_CN_NAME,
                T.LV2_PROD_RND_TEAM_CODE,
                T.LV2_PROD_RD_TEAM_CN_NAME,'||
                V_IN_LV3_PROD_RND_TEAM_CODE ||
                V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
				V_IN_LV4_PROD_RND_TEAM_CODE ||
                V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
                V_IN_L1_NAME||
                V_IN_L2_NAME||
                V_IN_DIMENSION_CODE ||
                V_IN_DIMENSION_CN_NAME ||
                V_IN_DIMENSION_EN_NAME||
                V_IN_DIMENSION_SUBCATEGORY_CODE ||
                V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                V_IN_DIMENSION_SUB_DETAIL_CODE ||
                V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
                V_IN_SPART_CODE ||
                V_IN_SPART_CN_NAME ||
				V_IN_COA_CODE ||  --202405版本新增
			    V_IN_COA_CN_NAME ||' 
                T.L3_CEG_CODE,
                T.L3_CEG_CN_NAME,
                T.L3_CEG_SHORT_CN_NAME,
                T.L4_CEG_CODE,
                T.L4_CEG_CN_NAME,
                T.L4_CEG_SHORT_CN_NAME,
                T.CATEGORY_CODE,
                T.CATEGORY_CN_NAME,
                T.PERIOD_YEAR,
                T.PERIOD_ID,
                T.CALIBER_FLAG,
                T.OVERSEA_FLAG,
                T.LV0_PROD_LIST_CODE,
                T.LV0_PROD_LIST_CN_NAME,
                T.LV0_PROD_LIST_EN_NAME';

        EXECUTE IMMEDIATE V_SQL;
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '计算品类的月卷积发货额, 版本号='||V_VERSION_ID||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

