-- Name: f_dm_total_annual_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_total_annual_status(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-10-23
  创建人  ：唐钦
  最新更新时间：2024年4月22日10点30分
  修改人：唐钦
  背景描述：年度涨跌幅状态码表
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_view_annual_status()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_TOTAL_ANNUAL_STATUS'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT;
  V_VIEW_NUM BIGINT;
  V_GROUP_LEVEL VARCHAR(200);
  V_GROUP_CODE TEXT;
  V_SQL_PARENT TEXT;
  V_IN_PROD_RND_TEAM_CODE TEXT;
  V_SQL_PROD_RND_TEAM_TOTAL TEXT;
  V_PFT_NAME VARCHAR(500);
  V_IN_PFT_NAME VARCHAR(500);
  V_REL_PFT_NAME VARCHAR(500);
  V_IN_DMS_CODE TEXT;
  V_DMS_LEV_CODE TEXT;
  V_INTO_DMS_CODE TEXT;
  V_INTO_DMS_NAME TEXT;
  V_REL_PROD_RND_TEAM TEXT;
  V_REL_DMS TEXT;
  V_FROM1_TABLE VARCHAR(500);
  V_FROM2_TABLE VARCHAR(500);
  V_FROM3_TABLE VARCHAR(500);
  V_TO_TABLE VARCHAR(500);
  V_SEQUENCE VARCHAR(500);
  V_TMP_TABLE VARCHAR(500);
  V_LV3_PROD_TEAM TEXT;
  V_DMS_CODE TEXT;
  V_DMS_NAME TEXT;
  V_GRO_NUM BIGINT;
  V_SPART_CODE VARCHAR(200);
  V_SPART_NAME VARCHAR(200);
  V_REL_SPART VARCHAR(500);
  V_IN_SPART VARCHAR(200);
  
-- 202405版本
  V_DIFF_COLUMN_CODE VARCHAR(200);
  V_DIFF_COLUMN_NAME VARCHAR(200);
  V_REL_DIFF_COLUMN VARCHAR(500);
  V_IN_DIFF_COLUMN VARCHAR(200);
  V_VERSION_TABLE VARCHAR(100);
  V_DIFF_COLUMN_CODE_BAK VARCHAR(200);
  V_DIFF_COLUMN_NAME_BAK VARCHAR(200);
  V_IN_DIFF_COLUMN_BAK VARCHAR(200);
  V_IN_PUBLIC VARCHAR(500);
  V_INTO_PUBLIC VARCHAR(500);
  V_REL_PUBLIC VARCHAR(500);
  V_LV_CODE VARCHAR(100);
  V_INTO_LV_CODE VARCHAR(100);
  V_REL_LV_CODE VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
    
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 202410版本新增：替代指数字段代码兼容
  V_LV_CODE := 'PROD_RND_TEAM_CODE,
                PROD_RD_TEAM_CN_NAME,';
  V_INTO_LV_CODE := 'T1.PROD_RND_TEAM_CODE,
                   T1.PROD_RD_TEAM_CN_NAME,';
  V_REL_LV_CODE := 'AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE';
  V_IN_PUBLIC := 'OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME,';
  V_INTO_PUBLIC := 'T1.OVERSEA_FLAG,
                   T1.LV0_PROD_LIST_CODE,
                   T1.LV0_PROD_LIST_CN_NAME,';
  V_REL_PUBLIC := 'AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE';
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG IN ('I','REPL')  THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
  -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
   IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_STATUS_CODE_T';--目标表
      ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'REPL' THEN   -- 通用颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_YTD_AMP_T';   
          V_TMP_TABLE := 'YTD_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_YTD_STATUS_T';--目标表
	      V_IN_PUBLIC := 'DATA_TYPE,';
	      V_INTO_PUBLIC := 'T1.DATA_TYPE,';
	      V_REL_PUBLIC := '';
	      V_LV_CODE := '';
	      V_INTO_LV_CODE := '';
	      V_REL_LV_CODE := '';
      ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'PFT_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ANNUAL_STATUS_CODE_T'; --目标表 
      ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'DMS_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ANNUAL_STATUS_CODE_T'; --目标表 
   ELSE
    NULL;
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
  -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
   IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'ENERGY_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ANNUAL_STATUS_CODE_T';--目标表
      ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'ENERGY_PFT_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ANNUAL_STATUS_CODE_T'; --目标表 
      ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'ENERGY_DMS_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ANNUAL_STATUS_CODE_T'; --目标表 
   ELSE
    NULL;
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
  -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
   IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'IAS_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ANNUAL_STATUS_CODE_T';--目标表
      ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'IAS_PFT_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ANNUAL_STATUS_CODE_T'; --目标表 
      ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
          V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_DIM_ITEM_STATUS_T';   --来源表(采购成本)
          V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_DIM_ITEM_STATUS_T';   --来源表(制造成本)
          V_FROM3_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ANNUAL_AMP_T';   
          V_TMP_TABLE := 'IAS_DMS_TOTAL_ITEM_STATUS';
          V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ANNUAL_STATUS_CODE_T'; --目标表 
   ELSE
    NULL;
   END IF;
  END IF;
  
  --版本号赋值
     V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
   
  --1.删除年度分析状态码表数据:
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
    
     --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 -- 创建ITEM层级缺失情况临时表
    V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE ||' (
        PERIOD_YEAR BIGINT,
        LV0_PROD_RND_TEAM_CODE VARCHAR(200),
        LV0_PROD_RD_TEAM_CN_NAME  VARCHAR(500),
        LV1_PROD_RND_TEAM_CODE VARCHAR(200),
        LV1_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        LV2_PROD_RND_TEAM_CODE VARCHAR(200),
        LV2_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        LV3_PROD_RND_TEAM_CODE VARCHAR(200),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        LV4_PROD_RND_TEAM_CODE VARCHAR(200),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        DIMENSION_CODE VARCHAR(500),
        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),
        DIMENSION_CN_NAME VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),
        SPART_CODE VARCHAR(500),
        SPART_CN_NAME VARCHAR(500),
        COA_CODE VARCHAR(500),
        COA_CN_NAME VARCHAR(500),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(100),
        STATUS_CODE BIGINT,
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),        
        LV0_PROD_LIST_CODE VARCHAR(200),  
        LV0_PROD_LIST_CN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;
    
    --写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => 'ITEM层级缺失情况临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS, 
     F_ERRBUF => 'SUCCESS');   
     
   -- 变量重赋值
      V_GRO_NUM := 5;
      V_LV3_PROD_TEAM := 'LV3_PROD_RND_TEAM_CODE,
                          LV3_PROD_RD_TEAM_CN_NAME,';
      V_SQL_PROD_RND_TEAM_TOTAL := ' T1.LV0_PROD_RND_TEAM_CODE,
                                     T1.LV1_PROD_RND_TEAM_CODE,
                                     T1.LV2_PROD_RND_TEAM_CODE,
                                     T1.LV3_PROD_RND_TEAM_CODE,
                                     T1.LV0_PROD_RD_TEAM_CN_NAME,
                                     T1.LV1_PROD_RD_TEAM_CN_NAME,
                                     T1.LV2_PROD_RD_TEAM_CN_NAME,
                                     T1.LV3_PROD_RD_TEAM_CN_NAME,'; 
    IF F_INDUSTRY_FLAG = 'IAS' AND F_DIMENSION_TYPE <> 'P' THEN   -- 202407版本新增IAS
      V_DIFF_COLUMN_CODE := 'LV4_PROD_RND_TEAM_CODE,';
      V_DIFF_COLUMN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
      V_IN_DIFF_COLUMN := 'T1.LV4_PROD_RND_TEAM_CODE,
                   T1.LV4_PROD_RD_TEAM_CN_NAME,';
      V_SQL_PROD_RND_TEAM_TOTAL := ' T1.LV0_PROD_RND_TEAM_CODE,
                                     T1.LV1_PROD_RND_TEAM_CODE,
                                     T1.LV2_PROD_RND_TEAM_CODE,
                                     T1.LV3_PROD_RND_TEAM_CODE,
                                     T1.LV4_PROD_RND_TEAM_CODE,
                                     T1.LV0_PROD_RD_TEAM_CN_NAME,
                                     T1.LV1_PROD_RD_TEAM_CN_NAME,
                                     T1.LV2_PROD_RD_TEAM_CN_NAME,
                                     T1.LV3_PROD_RD_TEAM_CN_NAME,
                                     T1.LV4_PROD_RD_TEAM_CN_NAME,'; 
    ELSIF F_INDUSTRY_FLAG = 'E' AND F_DIMENSION_TYPE = 'D' THEN
      V_DIFF_COLUMN_CODE := 'COA_CODE,';
      V_DIFF_COLUMN_NAME := 'COA_CN_NAME,';
      V_IN_DIFF_COLUMN := 'T1.COA_CODE,
                   T1.COA_CN_NAME,';
      V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''S2'') = NVL(T2.COA_CODE,''S2'') ';
      V_DIFF_COLUMN_CODE_BAK := V_DIFF_COLUMN_CODE;
      V_DIFF_COLUMN_NAME_BAK := V_DIFF_COLUMN_NAME;
      V_IN_DIFF_COLUMN_BAK := V_IN_DIFF_COLUMN;
    END IF;

   IF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
      V_GRO_NUM := 5;
      V_LV3_PROD_TEAM := '';
      V_REL_PFT_NAME := ' AND NVL(T1.L1_NAME,''SNULL1'') = NVL(T2.L1_NAME,''SNULL1'')
                          AND NVL(T1.L2_NAME,''SNULL2'') = NVL(T2.L2_NAME,''SNULL2'')';
      V_SQL_PROD_RND_TEAM_TOTAL := ' T1.LV0_PROD_RND_TEAM_CODE,
                                     T1.LV1_PROD_RND_TEAM_CODE,
                                     T1.LV2_PROD_RND_TEAM_CODE,
                                     T1.LV0_PROD_RD_TEAM_CN_NAME,
                                     T1.LV1_PROD_RD_TEAM_CN_NAME,
                                     T1.LV2_PROD_RD_TEAM_CN_NAME,'; 
      V_PFT_NAME := 'L1_NAME,
                     L2_NAME,';
      V_IN_PFT_NAME := 'T1.L1_NAME,
                        T1.L2_NAME,';
   ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
      V_GRO_NUM := 9;
      V_DMS_CODE := '
                    DIMENSION_CODE,
                    DIMENSION_SUBCATEGORY_CODE,
                    DIMENSION_SUB_DETAIL_CODE,'; 
      V_DMS_NAME := '
                    DIMENSION_CN_NAME,
                    DIMENSION_SUBCATEGORY_CN_NAME,
                    DIMENSION_SUB_DETAIL_CN_NAME,';  
      V_INTO_DMS_CODE := '
                    T1.DIMENSION_CODE,
                    T1.DIMENSION_SUBCATEGORY_CODE,
                    T1.DIMENSION_SUB_DETAIL_CODE,'; 
      V_INTO_DMS_NAME := '
                    T1.DIMENSION_CN_NAME,
                    T1.DIMENSION_SUBCATEGORY_CN_NAME,
                    T1.DIMENSION_SUB_DETAIL_CN_NAME,';  
      V_SPART_CODE := 'SPART_CODE,';
      V_SPART_NAME := 'SPART_CN_NAME,';
      V_IN_SPART := 'T1.SPART_CODE,
                     T1.SPART_CN_NAME,';
      V_REL_SPART := ' AND NVL(T1.SPART_CODE,''S1'') = NVL(T2.SPART_CODE,''S1'') ';
      V_DMS_LEV_CODE := V_INTO_DMS_CODE||V_INTO_DMS_NAME;
      V_REL_DMS := '
               AND NVL(T1.DIMENSION_CODE,''SNULL'') = NVL(T2.DIMENSION_CODE,''SNULL'')
               AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') 
               AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') '; 
   END IF;
   
  -- 取出采购成本和制造成本ITEM层级状态码数据
  V_SQL := '
    INSERT INTO '|| V_TMP_TABLE ||'(
           PERIOD_YEAR,
           LV0_PROD_RND_TEAM_CODE,
           LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE,
           LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE,
           LV2_PROD_RD_TEAM_CN_NAME,
           '||V_LV3_PROD_TEAM
           ||V_DMS_CODE
           ||V_DMS_NAME
           ||V_SPART_CODE
           ||V_SPART_NAME
           ||V_DIFF_COLUMN_CODE
           ||V_DIFF_COLUMN_NAME
           ||V_PFT_NAME||'
           ITEM_CODE,
           STATUS_CODE,
           VIEW_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,         
           LV0_PROD_LIST_CODE,   
           LV0_PROD_LIST_CN_NAME
           )
       SELECT PERIOD_YEAR,
              LV0_PROD_RND_TEAM_CODE,
              LV0_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,
              '||V_LV3_PROD_TEAM
              ||V_DMS_CODE
              ||V_DMS_NAME
              ||V_SPART_CODE
              ||V_SPART_NAME
              ||V_DIFF_COLUMN_CODE
              ||V_DIFF_COLUMN_NAME
              ||V_PFT_NAME||'
              ITEM_CODE,
              STATUS_CODE,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG,         
              LV0_PROD_LIST_CODE,   
              LV0_PROD_LIST_CN_NAME
           FROM '||V_FROM1_TABLE||'   -- 采购成本数据
       UNION ALL
       SELECT PERIOD_YEAR,
              LV0_PROD_RND_TEAM_CODE,
              LV0_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,
              '||V_LV3_PROD_TEAM
              ||V_DMS_CODE
              ||V_DMS_NAME
              ||V_SPART_CODE
              ||V_SPART_NAME
              ||V_DIFF_COLUMN_CODE
              ||V_DIFF_COLUMN_NAME
              ||V_PFT_NAME||'
              ITEM_CODE,
              STATUS_CODE,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG,         
              LV0_PROD_LIST_CODE,   
              LV0_PROD_LIST_CN_NAME
           FROM '||V_FROM2_TABLE;   -- 制造成本数据
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL; 
     
     --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '采购成本+制造成本：ITEM层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');          
       
    -- 按1-7 ，从重量级团队/盈利/量纲层级到ICT层级，分不同项目进行循环    
        FOR GRO_LEV IN 1 .. V_GRO_NUM LOOP
        IF GRO_LEV = 1 THEN   -- 通用：LV3层级/盈利:L2层级/量纲：SPART层级

          IF F_DIMENSION_TYPE = 'U' THEN
                V_GROUP_LEVEL := 'LV4';
                V_SQL_PARENT := ' T1.LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                  T1.LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
             IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
                V_GROUP_CODE := ' T1.LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
             ELSE 
                V_GROUP_CODE := ' NULL AS GROUP_CODE,';
                V_IN_PROD_RND_TEAM_CODE := ' NULL AS PROD_RND_TEAM_CODE,';
             END IF;
          ELSIF F_DIMENSION_TYPE = 'P' THEN    
                   V_GROUP_LEVEL := 'L2';
                   V_GROUP_CODE := ' T1.L2_NAME AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.L1_NAME AS PARENT_CODE,
                                     T1.L1_NAME AS PARENT_CN_NAME, ';
                   V_IN_PROD_RND_TEAM_CODE := '                  
                            CASE T1.VIEW_FLAG
                               WHEN 0 THEN T1.LV0_PROD_RND_TEAM_CODE
                               WHEN 1 THEN T1.LV1_PROD_RND_TEAM_CODE
                            ELSE T1.LV2_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'SPART';
                   V_GROUP_CODE := ' T1.SPART_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE,
                                     T1.DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_PROD_RND_TEAM_CODE := '  
                                  CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                       WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                                  ELSE T1.LV3_PROD_RND_TEAM_CODE
                                  END AS PROD_RND_TEAM_CODE,';    
                   V_IN_DMS_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE,
                                     T1.DIMENSION_SUBCATEGORY_CODE,
                                     T1.DIMENSION_CODE,';
          END IF;    
        ELSIF GRO_LEV = 2 THEN   -- 通用：LV3层级/盈利:L1层级/量纲：SUB_DETAIL (量纲子类明细)层级
          IF F_DIMENSION_TYPE = 'U' THEN  
                V_DIFF_COLUMN_CODE := '';
                V_DIFF_COLUMN_NAME := '';
                V_IN_DIFF_COLUMN := '';
                V_REL_DIFF_COLUMN := '';
                V_GROUP_LEVEL := 'LV3';
                V_GROUP_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV3_PROD_RND_TEAM_CODE,
                                              T1.LV2_PROD_RND_TEAM_CODE,
                                              T1.LV1_PROD_RND_TEAM_CODE,
                                              T1.LV2_PROD_RD_TEAM_CN_NAME,
                                              T1.LV1_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN    
                   V_GROUP_LEVEL := 'L1';    
                   V_GROUP_CODE := ' T1.L1_NAME AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_PFT_NAME := 'L1_NAME,';
                   V_IN_PFT_NAME := 'T1.L1_NAME,';
                   V_REL_PFT_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'') ';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_SPART_CODE := '';
                   V_SPART_NAME := '';
                   V_IN_SPART := '';
                   V_REL_SPART := '';
                   V_GROUP_LEVEL := 'SUB_DETAIL';
                   V_GROUP_CODE := ' T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,
                                      T1.DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_DMS_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE,
                                     T1.DIMENSION_SUBCATEGORY_CODE,
                                     T1.DIMENSION_CODE,';
          END IF;
        ELSIF GRO_LEV = 3 THEN   -- 通用：LV2层级/盈利:LV2层级/量纲：SUBCATEGORY (量纲子类)层级
          IF F_DIMENSION_TYPE = 'U' THEN
                V_GROUP_LEVEL := 'LV2';    
                V_GROUP_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                  T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE,
                                              T1.LV1_PROD_RND_TEAM_CODE,
                                              T1.LV1_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN    
                   V_GROUP_LEVEL := 'LV2';
                   V_GROUP_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                   V_PFT_NAME := '';
                   V_IN_PFT_NAME := '';
                   V_REL_PFT_NAME := '';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'SUBCATEGORY';
                   V_GROUP_CODE := ' T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.DIMENSION_CODE AS PARENT_CODE,
                                      T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_DMS_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,
                                     T1.DIMENSION_SUBCATEGORY_CODE,
                                     T1.DIMENSION_CODE,';
                   V_DMS_LEV_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE,
                                      T1.DIMENSION_CODE,
                                      T1.DIMENSION_CN_NAME,';
          END IF;
         ELSIF GRO_LEV = 4 THEN   -- 通用：LV1层级/盈利:LV1层级/量纲：DIMENSION (量纲)层级
          IF F_DIMENSION_TYPE = 'U' THEN      
                V_GROUP_LEVEL := 'LV1';
                V_GROUP_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN
                   V_GROUP_LEVEL := 'LV1';
                   V_GROUP_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_PFT_NAME := '';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                   V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RD_TEAM_CN_NAME,';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_GROUP_LEVEL := 'DIMENSION';
                   V_GROUP_CODE := ' T1.DIMENSION_CODE AS GROUP_CODE,';
                   V_IN_DMS_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,
                                     NULL AS DIMENSION_SUBCATEGORY_CODE,
                                     T1.DIMENSION_CODE,';
                   V_DMS_LEV_CODE := 'T1.DIMENSION_CODE,';
           -- [202405版本]当产业项目标识为：E时，加COA层级变量
                IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
                   V_SQL_PARENT := '
                          CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                               WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                               WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CODE
                          ELSE T1.LV3_PROD_RND_TEAM_CODE
                          END AS PARENT_CODE,
                          CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                               WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                               WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CN_NAME
                          ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                          END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
                ELSIF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
                   V_SQL_PARENT := '
                          CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                               WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                          ELSE T1.LV3_PROD_RND_TEAM_CODE
                          END AS PARENT_CODE,
                          CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                               WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                          ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                          END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
                ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
                   V_SQL_PARENT := '
                          CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                               WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                               WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RND_TEAM_CODE
                          ELSE T1.LV3_PROD_RND_TEAM_CODE
                          END AS PARENT_CODE,
                          CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                               WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                               WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RD_TEAM_CN_NAME
                          ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                          END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
                END IF;
          END IF;         
         ELSIF GRO_LEV = 5 THEN   -- 通用：ICT/盈利:ICT层级/量纲：LV3
          IF F_DIMENSION_TYPE = 'U' THEN      
                V_GROUP_LEVEL := 'LV0';
                V_GROUP_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RD_TEAM_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
          ELSIF F_DIMENSION_TYPE = 'P' THEN                   
                V_GROUP_LEVEL := 'LV0';
                V_GROUP_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                   T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE,
                                              T1.LV0_PROD_RD_TEAM_CN_NAME,';
                V_IN_PROD_RND_TEAM_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
          ELSIF F_DIMENSION_TYPE = 'D' THEN
                   V_SQL_PARENT := ' T1.LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                     T1.LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_DMS_CODE := 'NULL AS DIMENSION_SUB_DETAIL_CODE,
                                     NULL AS DIMENSION_SUBCATEGORY_CODE,
                                     NULL AS DIMENSION_CODE,';          
                   V_DMS_LEV_CODE := '';
            IF F_INDUSTRY_FLAG = 'E' THEN 
               V_GROUP_CODE := ' T1.COA_CODE AS GROUP_CODE,';    -- 202405版本新增COA层级
               V_GROUP_LEVEL := 'COA';
               V_IN_PROD_RND_TEAM_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
            ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
               V_GROUP_CODE := ' T1.LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,';    -- 202405版本新增COA层级
               V_GROUP_LEVEL := 'LV4';
               V_IN_PROD_RND_TEAM_CODE := ' T1.LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
            ELSIF F_INDUSTRY_FLAG = 'I' THEN 
               V_GROUP_CODE := 'NULL AS GROUP_CODE,';
               V_GROUP_LEVEL := 'COA';
            END IF;
          END IF;
          ELSIF GRO_LEV = 6 THEN   -- 量纲：LV3
                   V_DIFF_COLUMN_CODE := '';
                   V_DIFF_COLUMN_NAME := '';
                   V_IN_DIFF_COLUMN := '';
                   V_REL_DIFF_COLUMN := '';
                   V_GROUP_LEVEL := 'LV3';
                   V_GROUP_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                     T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
                   V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV3_PROD_RND_TEAM_CODE,
                                                 T1.LV2_PROD_RND_TEAM_CODE,
                                                 T1.LV1_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RND_TEAM_CODE,
                                                 T1.LV2_PROD_RD_TEAM_CN_NAME,
                                                 T1.LV1_PROD_RD_TEAM_CN_NAME,';
          ELSIF GRO_LEV = 7 THEN   -- 量纲：LV2
                   V_GROUP_LEVEL := 'LV2';
                   V_GROUP_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                   V_SQL_PARENT := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                      T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                   V_IN_PROD_RND_TEAM_CODE := ' T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';   
                   V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE,
                                                 T1.LV1_PROD_RND_TEAM_CODE,
                                                 T1.LV0_PROD_RND_TEAM_CODE,
                                                 T1.LV1_PROD_RD_TEAM_CN_NAME,';
          ELSIF GRO_LEV = 8 THEN   -- 量纲：LV1         
                 V_GROUP_LEVEL := 'LV1';
                 V_GROUP_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                 V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                    T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                 V_IN_PROD_RND_TEAM_CODE := ' T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';   
                 V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RD_TEAM_CN_NAME,';    
          ELSIF GRO_LEV = 9 THEN   -- 量纲：ICT         
                 V_GROUP_LEVEL := 'LV0';
                 V_GROUP_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,';
                 V_SQL_PARENT := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                                    T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
                 V_IN_PROD_RND_TEAM_CODE := ' T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';   
                 V_SQL_PROD_RND_TEAM_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE,
                                               T1.LV0_PROD_RD_TEAM_CN_NAME,';    
        END IF;

  V_SQL := '
      INSERT INTO '||V_TO_TABLE||' (
               PERIOD_YEAR,
               VERSION_ID,
               '||V_LV_CODE||'
               '||V_DMS_CODE
               ||V_DMS_NAME
               ||V_SPART_CODE
               ||V_SPART_NAME
               ||V_DIFF_COLUMN_CODE_BAK
               ||V_DIFF_COLUMN_NAME_BAK
               ||V_PFT_NAME||'
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               VIEW_FLAG,
               CALIBER_FLAG,
               '||V_IN_PUBLIC||'
               STATUS_CODE,
               PARENT_CODE,
               PARENT_CN_NAME,
               CREATED_BY,
               CREATION_DATE,
               LAST_UPDATED_BY,
               LAST_UPDATE_DATE,
               DEL_FLAG,
               APPEND_YEAR
               )
               
      WITH ITEM_STATUS_TMP AS(
           SELECT T1.PERIOD_YEAR,
                  '||V_IN_PROD_RND_TEAM_CODE
                  ||V_IN_DMS_CODE
                  ||V_SPART_CODE
                  ||V_DIFF_COLUMN_CODE
                  ||V_PFT_NAME
                  ||V_GROUP_CODE    -- 取T1表中的上一层级CODE作为GROUP_CODE
                  ||V_SQL_PARENT||'
                  T1.VIEW_FLAG,
                  T1.CALIBER_FLAG,
                  T1.OVERSEA_FLAG,
                  T1.LV0_PROD_LIST_CODE,
                  SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
                  SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
                  SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
               FROM '||V_TMP_TABLE||' T1
               GROUP BY '
               ||V_SQL_PROD_RND_TEAM_TOTAL
               ||V_DMS_LEV_CODE
               ||V_SPART_CODE
               ||V_DIFF_COLUMN_CODE
               ||V_DIFF_COLUMN_NAME
               ||V_PFT_NAME
               ||'T1.PERIOD_YEAR,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.OVERSEA_FLAG,
               T1.LV0_PROD_LIST_CODE)
                                    
        SELECT 
               T1.PERIOD_YEAR,
               '||V_VERSION_ID||' AS VERSION_ID,
               '||V_INTO_LV_CODE||'
               '||V_INTO_DMS_CODE
               ||V_INTO_DMS_NAME
               ||V_IN_SPART
               ||V_IN_DIFF_COLUMN_BAK
               ||V_IN_PFT_NAME
               ||'T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               '||V_INTO_PUBLIC||'
               CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
                    WHEN T2.STATUS_1 = 0 THEN 1
                    WHEN T2.STATUS_4 = 0 THEN 2
               ELSE 4 END AS STATUS_CODE,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,    
               NULL AS APPEND_YEAR
            FROM '||V_FROM3_TABLE||' T1
            LEFT JOIN ITEM_STATUS_TMP T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            AND NVL(T1.GROUP_CODE,''SNULL'') = NVL(T2.GROUP_CODE,''SNULL'')
            AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
            AND NVL(T1.VIEW_FLAG,''SNULL'') = NVL(T2.VIEW_FLAG,''SNULL'')
            AND NVL(T1.CALIBER_FLAG,''SNULL'') = NVL(T2.CALIBER_FLAG,''SNULL'') 
            '||V_REL_PUBLIC||'
            '||V_REL_LV_CODE||'
            '||V_REL_DMS
            ||V_REL_SPART
            ||V_REL_DIFF_COLUMN
            ||V_REL_PFT_NAME||'
            WHERE T1.GROUP_LEVEL = '''||V_GROUP_LEVEL||'''
            AND T1.VERSION_ID = '||V_VERSION_ID ;        

       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL;        
        
   --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的其余层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');   
     END LOOP;
        
    -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
 
END$$
/

