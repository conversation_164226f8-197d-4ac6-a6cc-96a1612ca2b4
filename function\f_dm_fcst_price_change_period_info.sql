-- Name: f_dm_fcst_price_change_period_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_change_period_info(f_version_id bigint DEFAULT NULL::bigint, f_change_period_id bigint DEFAULT NULL::bigint, f_custom_id character varying DEFAULT NULL::character varying, f_bg_code character varying DEFAULT NULL::character varying, f_oversea_flag character varying DEFAULT NULL::character varying, f_region_code character varying DEFAULT NULL::character varying, f_repoffice_code character varying DEFAULT NULL::character varying, f_st_code character varying DEFAULT NULL::character varying, f_st_sub_code character varying DEFAULT NULL::character varying, f_group_level character varying DEFAULT NULL::character varying, f_group_code character varying DEFAULT NULL::character varying, f_parent_code character varying DEFAULT NULL::character varying, f_view_flag character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：20241112
创建人  ：qwx1110218
背景描述：定价指数-基期切换，只涉及综合指数分析页面的月累计价格指数、虚化月累计价格指数；
          1、根据传入基期判断是否是默认基期，如果是，则不用计算，否则需计算；
          2、根据传入组合ID判断从哪取数：有组合ID，则从虚化月累计表取数；无组合ID，则总非虚化月累计表取数；
          3、根据传入大T、子网系统，判断视角标识（大T、非大T）；
          4、根据传入层级判断取数逻辑：非虚化表LV0层级没有父层级，其它层级都有父层级；虚化表的group_level只有一个值：SPART；
          5、根据传入层级判断取数逻辑：非虚化表SPART层级没有多子项，其它层级都有多子项；虚化的基期切换，价格指数图从虚化表取值计算，价格指数多子项图从非虚化表取值计算；
          6、关于默认基期的逻辑：每年<1月8号用上上年12月，>=1月8号则用上年12月；
参数描述：参数一： f_version_id 版本号
          参数二： f_change_period_id 切换基期
          参数三： f_custom_id   组合ID
          参数四： f_bg_code  BG编码
          参数五： f_oversea_flag  国内海外
          参数六： f_region_code   地区部编码
          参数七： f_repoffice_code 代表处编码
          参数八： f_st_code 大T系统编码
          参数九： f_st_sub_code 子网系统编码
          参数十： f_group_level 层级（LV0:ICT、LV1：LV1层级、LV2：LV2层级、LV3：LV3层级、LV4：LV4层级、SPART：SPART层级）；
          参数十一： f_group_code 层级编码
          参数十二： f_parent_code 父层级编码
          参数十三： f_view_flag 视角标识（地代办 LOCAL_AGENT（国内海外、地区部、代表处）；系统部 SYS_DEPT（大T系统、子网系统））
          输出参数： x_result_status 运行状态返回值 'SUCCESS'为成功，'FAILED'为失败
事例    ：select fin_dm_opt_foi.f_dm_fcst_price_change_period_info()
变更记录：

*/


declare
	v_sp_name     varchar(200) := 'fin_dm_opt_foi.f_dm_fcst_price_change_period_info';
	v_table       varchar(100) := '';  -- 月度累计表
	v_cus_table   varchar(100) := '';  -- 虚化月度累计表
	v_version_id     bigint;     -- 版本号
	v_base_period_id bigint;     -- 默认基期
	v_select_sql     text;       -- 月度累计字段
	v_cus_select_sql text;       -- 虚化月度累计字段
	v_sql            text;
	v_parent_code    text;
	v_step_num       int;
	v_cn             int;


begin

	x_result_status := 'SUCCESS';        --1表示成功
	v_step_num = 1;

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '函数 '||v_sp_name||' 开始运行',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  -- 默认基期，每年<1月8号用上上年12月，>=1月8号则用上年12月
  if(to_char(current_date,'mm') = '01' ) then

    v_base_period_id := year(current_date)-2||'12';  -- 默认基期（最新年份上上一年12月）；

  else

    v_base_period_id := year(current_date)-1||'12';  -- 默认基期（最新年份上一年12月即次新年）；

  end if;


  v_table     := 'fin_dm_opt_foi.dm_fcst_price_mon_cost_idx_t';   -- 月度累计指数
  v_cus_table := 'fin_dm_opt_foi.dm_fcst_price_base_cus_mon_cost_idx_t';    -- 虚化月度累计指数

  raise notice'v_base_period_id:%',v_base_period_id;
  raise notice'v_table:%',v_table;
  raise notice'v_cus_table:%',v_cus_table;

  -- 月度累计表的查询字段
  v_select_sql := 'version_id
     , period_year
     , period_id
     , base_period_id
     , bg_code
     , bg_cn_name
     , parent_code
     , parent_cn_name
     , group_code
     , group_cn_name
     , group_level
     , oversea_flag
     , region_code
     , region_cn_name
     , repoffice_code
     , repoffice_cn_name
     , sign_top_cust_category_code
     , sign_top_cust_category_cn_name
     , sign_subsidiary_custcatg_cn_name
     , view_flag
     , cost_index
  ';

  -- 虚化月度累计表的查询字段
  v_cus_select_sql := 'version_id
     , period_year
     , period_id
     , base_period_id
     , bg_code
     , bg_cn_name
     , custom_id
     , custom_cn_name
     , group_code
     , group_cn_name
     , group_level
     , oversea_flag
     , region_code
     , region_cn_name
     , repoffice_code
     , repoffice_cn_name
     , sign_top_cust_category_code
     , sign_top_cust_category_cn_name
     , sign_subsidiary_custcatg_cn_name
     , view_flag
     , cost_index
  ';


  -- 从版本表取最新版本
  if(f_version_id is null) then
    select version_id into v_version_id
      from fin_dm_opt_foi.dm_fcst_price_version_info_t
     where del_flag = 'N'
       and status = 1
       and upper(data_type) = 'MONTH'  -- 用月度版本号
     order by last_update_date desc
     limit 1
    ;
  else
    v_version_id := f_version_id;
  end if;

  raise notice'月度版本：%',v_version_id;

  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '获取最新版本： '||v_version_id,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  -- 多选
  f_group_code := replace(f_group_code,',',''',''');

  raise notice'f_group_code：%',f_group_code;

  f_parent_code := replace(f_parent_code,',',''',''');

  raise notice'f_parent_code：%',f_parent_code;
  
  f_custom_id := replace(f_custom_id,',',''',''');

  raise notice'f_custom_id：%',f_custom_id;

  -- 【第一层IF BEGIN】
  -- 如果传入基期参数 <> 默认基期（最新年份前一年12月即次新年），则计算；否则不用计算
  if(f_change_period_id <> v_base_period_id) then
    -- 【第二层IF BEGIN】
    -- 根据传入组合ID判断来源表
    --【非虚化逻辑】
    -- 如果组合ID为空值，则从月度累计指数表 dm_fcst_price_mon_cost_idx_t 取值
    if(f_custom_id is null) then
      /************************************************* 非虚化LV0层级 begin ***********************************************************/
      if(f_group_level = 'LV0') then
        -- 【非虚化】根据传入视角标识判断是取大T、子网系统还是国内海外、地区部、代表处
        -- 【非虚化】传入视角标识=地代办 LOCAL_AGENT（国内海外、地区部、代表处）
        if(f_view_flag = 'LOCAL_AGENT') then
          -- 删除数据
          v_sql := 'delete from '||v_table||'
                     where version_id = '||v_version_id||'
                       and del_flag = ''N''
                       and base_period_id = '||f_change_period_id||'    -- 切换基期
                       and bg_code = '''||f_bg_code||'''
                       and sign_top_cust_category_code is null
                       and sign_subsidiary_custcatg_cn_name is null
                       and oversea_flag = '''||f_oversea_flag||'''
                       and region_code = '''||f_region_code||'''
                       and repoffice_code = '''||f_repoffice_code||'''
                       and group_level = '''||f_group_level||'''
                       and group_code in('''||f_group_code||''')
                       and parent_code is null
                       and view_flag = '''||f_view_flag||'''
          ';

          raise notice'LV0层级 v_version_id：%',v_version_id;

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => '删除'||f_group_level||'层级的数据 '||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );
          
          -- 【非虚化】根据传入参数取切换基期指数信息
          -- 当前层级的指数计算
          v_sql := '
          insert into '||v_table||'(
          	      version_id
               , period_year
               , period_id
               , base_period_id
               , bg_code
               , bg_cn_name
               , parent_code
               , parent_cn_name
               , group_code
               , group_cn_name
               , group_level
               , oversea_flag
               , region_code
               , region_cn_name
               , repoffice_code
               , repoffice_cn_name
               , sign_top_cust_category_code
               , sign_top_cust_category_cn_name
               , sign_subsidiary_custcatg_cn_name
               , view_flag
               , cost_index
               , created_by
               , creation_date
               , last_updated_by
               , last_update_date
               , del_flag
          )
          with change_period_info_tmp as(
            select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'  -- 默认基期
               and period_id = '||f_change_period_id||'    -- 切换基期
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code is null
               and sign_subsidiary_custcatg_cn_name is null
               and oversea_flag = '''||f_oversea_flag||'''
               and region_code = '''||f_region_code||'''
               and repoffice_code = '''||f_repoffice_code||'''
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code is null
               and view_flag = '''||f_view_flag||'''
          ),
          -- 根据传入参数取报告期指数信息
          all_period_info_tmp as(
          select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code is null
               and sign_subsidiary_custcatg_cn_name is null
               and oversea_flag = '''||f_oversea_flag||'''
               and region_code = '''||f_region_code||'''
               and repoffice_code = '''||f_repoffice_code||'''
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code is null
               and view_flag = '''||f_view_flag||'''
          )
          select t1.version_id
               , t1.period_year
               , t1.period_id
               , '||f_change_period_id||' as base_period_id
               , t1.bg_code
               , t1.bg_cn_name
               , t1.parent_code
               , t1.parent_cn_name
               , t1.group_code
               , t1.group_cn_name
               , t1.group_level
               , t1.oversea_flag
               , t1.region_code
               , t1.region_cn_name
               , t1.repoffice_code
               , t1.repoffice_cn_name
               , t1.sign_top_cust_category_code
               , t1.sign_top_cust_category_cn_name
               , t1.sign_subsidiary_custcatg_cn_name
               , t1.view_flag
               , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
               , -1 as created_by
               , current_timestamp as creation_date
               , -1 as last_updated_by
               , current_timestamp as last_update_date
               , ''N'' as del_flag
            from all_period_info_tmp t1
            left join change_period_info_tmp t2
              on t1.version_id = t2.version_id
             and t1.bg_code = t2.bg_code
             and nvl(t1.parent_code,''A'') = nvl(t2.parent_code,''A'')
             and t1.group_code = t2.group_code
             and t1.group_level = t2.group_level
             and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
             and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
             and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
             and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
             and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
             and t1.view_flag = t2.view_flag
          ';

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => 'LV0层级国内海外、地区部、代表处数据入到月度指数表，数据量：'||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );

          /********************************************************* group_level<>spart，则执行 begin *******************************************************/
          if(f_group_level <> 'SPART') then
            -- 删除数据（多子项）
            v_sql := 'delete from '||v_table||'
                       where version_id = '||v_version_id||'
                         and del_flag = ''N''
                         and base_period_id = '||f_change_period_id||'    -- 切换基期
                         and bg_code = '''||f_bg_code||'''
                         and sign_top_cust_category_code is null
                         and sign_subsidiary_custcatg_cn_name is null
                         and oversea_flag = '''||f_oversea_flag||'''
                         and region_code = '''||f_region_code||'''
                         and repoffice_code = '''||f_repoffice_code||'''
                         and parent_code in('''||f_group_code||''')
                         and view_flag = '''||f_view_flag||'''
            ';

            raise notice'LV0层级多子项 v_version_id：%',v_version_id;

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => '删除'||f_group_level||'层级多子项的数据 '||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );
            
            -- 选定层级的下一层级指数计算（多子项指数）
            v_sql := '
            insert into '||v_table||'(
            	     version_id
                 , period_year
                 , period_id
                 , base_period_id
                 , bg_code
                 , bg_cn_name
                 , parent_code
                 , parent_cn_name
                 , group_code
                 , group_cn_name
                 , group_level
                 , oversea_flag
                 , region_code
                 , region_cn_name
                 , repoffice_code
                 , repoffice_cn_name
                 , sign_top_cust_category_code
                 , sign_top_cust_category_cn_name
                 , sign_subsidiary_custcatg_cn_name
                 , view_flag
                 , cost_index
                 , created_by
                 , creation_date
                 , last_updated_by
                 , last_update_date
                 , del_flag
            )
            with change_period_info_tmp1 as(
              select '||v_select_sql||'
                from '||v_table||'
               where version_id = '||v_version_id||'
                 and del_flag = ''N''
                 and base_period_id = '||v_base_period_id||'  -- 默认基期
                 and period_id = '||f_change_period_id||'    -- 切换基期
                 and bg_code = '''||f_bg_code||'''
                 and sign_top_cust_category_code is null
                 and sign_subsidiary_custcatg_cn_name is null
                 and oversea_flag = '''||f_oversea_flag||'''
                 and region_code = '''||f_region_code||'''
                 and repoffice_code = '''||f_repoffice_code||'''
                 and parent_code in('''||f_group_code||''')
                 and view_flag = '''||f_view_flag||'''
            ),
            -- 根据传入参数取报告期指数信息
            all_period_info_tmp1 as(
            select '||v_select_sql||'
                from '||v_table||'
               where version_id = '||v_version_id||'
                 and del_flag = ''N''
                 and base_period_id = '||v_base_period_id||'
                 and bg_code = '''||f_bg_code||'''
                 and sign_top_cust_category_code is null
                 and sign_subsidiary_custcatg_cn_name is null
                 and oversea_flag = '''||f_oversea_flag||'''
                 and region_code = '''||f_region_code||'''
                 and repoffice_code = '''||f_repoffice_code||'''
                 and parent_code in('''||f_group_code||''')
                 and view_flag = '''||f_view_flag||'''
            )
            select t1.version_id
                 , t1.period_year
                 , t1.period_id
                 , '||f_change_period_id||' as base_period_id
                 , t1.bg_code
                 , t1.bg_cn_name
                 , t1.parent_code
                 , t1.parent_cn_name
                 , t1.group_code
                 , t1.group_cn_name
                 , t1.group_level
                 , t1.oversea_flag
                 , t1.region_code
                 , t1.region_cn_name
                 , t1.repoffice_code
                 , t1.repoffice_cn_name
                 , t1.sign_top_cust_category_code
                 , t1.sign_top_cust_category_cn_name
                 , t1.sign_subsidiary_custcatg_cn_name
                 , t1.view_flag
                 , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
                 , -1 as created_by
                 , current_timestamp as creation_date
                 , -1 as last_updated_by
                 , current_timestamp as last_update_date
                 , ''N'' as del_flag
              from all_period_info_tmp1 t1
              left join change_period_info_tmp1 t2
                on t1.version_id = t2.version_id
               and t1.bg_code = t2.bg_code
               and nvl(t1.parent_code,''A'') = nvl(t2.parent_code,''A'')
               and t1.group_code = t2.group_code
               and t1.group_level = t2.group_level
               and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
               and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
               and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
               and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
               and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
               and t1.view_flag = t2.view_flag
            ';

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => 'LV0层级国内海外、地区部、代表处数据（多子项）入到月度指数表，数据量：'||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );

          end if;
          /********************************************************* group_level<>spart，则执行 end *******************************************************/

        -- 【非虚化】传入视角标识=系统部 SYS_DEPT（大T系统、子网系统）
        elseif(f_view_flag = 'SYS_DEPT') then
          -- 删除数据
          v_sql := 'delete from '||v_table||'
                     where version_id = '||v_version_id||'
                       and del_flag = ''N''
                       and base_period_id = '||f_change_period_id||'    -- 切换基期
                       and bg_code = '''||f_bg_code||'''
                       and sign_top_cust_category_code = '''||f_st_code||'''
                       and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                       and oversea_flag is null
                       and region_code is null
                       and repoffice_code is null
                       and group_level = '''||f_group_level||'''
                       and group_code in('''||f_group_code||''')
                       and parent_code is null
                       and view_flag = '''||f_view_flag||'''
          ';

          raise notice'v_version_id：%',v_version_id;

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => '删除'||f_group_level||'层级的数据 '||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );
          
          -- 【非虚化】根据传入参数取切换基期指数信息
          v_sql := '
          insert into '||v_table||'(
           	     version_id
               , period_year
               , period_id
               , base_period_id
               , bg_code
               , bg_cn_name
               , parent_code
               , parent_cn_name
               , group_code
               , group_cn_name
               , group_level
               , oversea_flag
               , region_code
               , region_cn_name
               , repoffice_code
               , repoffice_cn_name
               , sign_top_cust_category_code
               , sign_top_cust_category_cn_name
               , sign_subsidiary_custcatg_cn_name
               , view_flag
               , cost_index
               , created_by
               , creation_date
               , last_updated_by
               , last_update_date
               , del_flag
          )
          with change_period_info_tmp as(
            select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'  -- 默认基期
               and period_id = '||f_change_period_id||'    -- 切换基期
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code = '''||f_st_code||'''
               and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
               and oversea_flag is null
               and region_code is null
               and repoffice_code is null
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code is null
               and view_flag = '''||f_view_flag||'''
          ),
          -- 根据传入参数取报告期指数信息
          all_period_info_tmp as(
          select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code = '''||f_st_code||'''
               and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
               and oversea_flag is null
               and region_code is null
               and repoffice_code is null
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code is null
               and view_flag = '''||f_view_flag||'''
          )
          select t1.version_id
               , t1.period_year
               , t1.period_id
               , '||f_change_period_id||' as base_period_id
               , t1.bg_code
               , t1.bg_cn_name
               , t1.parent_code
               , t1.parent_cn_name
               , t1.group_code
               , t1.group_cn_name
               , t1.group_level
               , t1.oversea_flag
               , t1.region_code
               , t1.region_cn_name
               , t1.repoffice_code
               , t1.repoffice_cn_name
               , t1.sign_top_cust_category_code
               , t1.sign_top_cust_category_cn_name
               , t1.sign_subsidiary_custcatg_cn_name
               , t1.view_flag
               , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
               , -1 as created_by
               , current_timestamp as creation_date
               , -1 as last_updated_by
               , current_timestamp as last_update_date
               , ''N'' as del_flag
            from all_period_info_tmp t1
            left join change_period_info_tmp t2
              on t1.version_id = t2.version_id
             and t1.bg_code = t2.bg_code
             and nvl(t1.parent_code,''A'') = nvl(t2.parent_code,''A'')
             and t1.group_code = t2.group_code
             and t1.group_level = t2.group_level
             and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
             and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
             and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
             and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
             and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
             and t1.view_flag = t2.view_flag
          ';

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => 'LV0层级大T系统、子网系统数据入到月累计指数表，数据量：'||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );

          /******************************************************* group_level<>spart，则执行 begin ***********************************************************/
          if(f_group_level <> 'SPART') then
            -- 删除数据（多子项）
            v_sql := 'delete from '||v_table||'
                       where version_id = '||v_version_id||'
                         and del_flag = ''N''
                         and base_period_id = '||f_change_period_id||'    -- 切换基期
                         and bg_code = '''||f_bg_code||'''
                         and sign_top_cust_category_code = '''||f_st_code||'''
                         and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                         and oversea_flag is null
                         and region_code is null
                         and repoffice_code is null
                         and parent_code in('''||f_group_code||''')
                         and view_flag = '''||f_view_flag||'''
            ';

            raise notice'v_version_id：%',v_version_id;

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => '删除'||f_group_level||'层级多子项的数据 '||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );
            
            -- 【非虚化】根据传入参数取切换基期指数信息（多子项）
            v_sql := '
            insert into '||v_table||'(
            	     version_id
                 , period_year
                 , period_id
                 , base_period_id
                 , bg_code
                 , bg_cn_name
                 , parent_code
                 , parent_cn_name
                 , group_code
                 , group_cn_name
                 , group_level
                 , oversea_flag
                 , region_code
                 , region_cn_name
                 , repoffice_code
                 , repoffice_cn_name
                 , sign_top_cust_category_code
                 , sign_top_cust_category_cn_name
                 , sign_subsidiary_custcatg_cn_name
                 , view_flag
                 , cost_index
                 , created_by
                 , creation_date
                 , last_updated_by
                 , last_update_date
                 , del_flag
            )
            with change_period_info_tmp1 as(
              select '||v_select_sql||'
                from '||v_table||'
               where version_id = '||v_version_id||'
                 and del_flag = ''N''
                 and base_period_id = '||v_base_period_id||'  -- 默认基期
                 and period_id = '||f_change_period_id||'    -- 切换基期
                 and bg_code = '''||f_bg_code||'''
                 and sign_top_cust_category_code = '''||f_st_code||'''
                 and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                 and oversea_flag is null
                 and region_code is null
                 and repoffice_code is null
                 and parent_code in('''||f_group_code||''')
                 and view_flag = '''||f_view_flag||'''
            ),
            -- 根据传入参数取报告期指数信息
            all_period_info_tmp1 as(
            select '||v_select_sql||'
                from '||v_table||'
               where version_id = '||v_version_id||'
                 and del_flag = ''N''
                 and base_period_id = '||v_base_period_id||'
                 and bg_code = '''||f_bg_code||'''
                 and sign_top_cust_category_code = '''||f_st_code||'''
                 and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                 and oversea_flag is null
                 and region_code is null
                 and repoffice_code is null
                 and parent_code in('''||f_group_code||''')
                 and view_flag = '''||f_view_flag||'''
            )
            select t1.version_id
                 , t1.period_year
                 , t1.period_id
                 , '||f_change_period_id||' as base_period_id
                 , t1.bg_code
                 , t1.bg_cn_name
                 , t1.parent_code
                 , t1.parent_cn_name
                 , t1.group_code
                 , t1.group_cn_name
                 , t1.group_level
                 , t1.oversea_flag
                 , t1.region_code
                 , t1.region_cn_name
                 , t1.repoffice_code
                 , t1.repoffice_cn_name
                 , t1.sign_top_cust_category_code
                 , t1.sign_top_cust_category_cn_name
                 , t1.sign_subsidiary_custcatg_cn_name
                 , t1.view_flag
                 , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
                 , -1 as created_by
                 , current_timestamp as creation_date
                 , -1 as last_updated_by
                 , current_timestamp as last_update_date
                 , ''N'' as del_flag
              from all_period_info_tmp1 t1
              left join change_period_info_tmp1 t2
                on t1.version_id = t2.version_id
               and t1.bg_code = t2.bg_code
               and nvl(t1.parent_code,''A'') = nvl(t2.parent_code,''A'')
               and t1.group_code = t2.group_code
               and t1.group_level = t2.group_level
               and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
               and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
               and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
               and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
               and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
               and t1.view_flag = t2.view_flag
            ';

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => 'LV0层级大T系统、子网系统数据（多子项）入到月累计指数表，数据量：'||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );
          end if;
          /******************************************************* group_level<>spart，则执行 end ***********************************************************/

        end if;
        /************************************************* 非虚化LV0层级 end ***********************************************************/

      /************************************************* 非虚化非LV0层级 begin ***********************************************************/
      else
        -- 如果前台页面没有选父层级，则需要根据选定的层级获取父层级
        if(f_parent_code is null) then
          v_sql := 'select array_to_string(array_agg(distinct parent_code),'','') as parent_code from '||v_table||'
                     where version_id = '||v_version_id||'
                       and del_flag = ''N''
                       and base_period_id = '||v_base_period_id||'  -- 默认基期
                       and bg_code = '''||f_bg_code||'''
                       and sign_top_cust_category_code is null
                       and sign_subsidiary_custcatg_cn_name is null
                       and oversea_flag = '''||f_oversea_flag||'''
                       and region_code = '''||f_region_code||'''
                       and repoffice_code = '''||f_repoffice_code||'''
                       and group_level = '''||f_group_level||'''
                       and group_code in('''||f_group_code||''')
                       and view_flag = '''||f_view_flag||'''
          ';

          dbms_output.put_line(v_sql);

          execute immediate v_sql into v_parent_code;

          raise notice'非LV0，v_parent_code：%',v_parent_code;

          f_parent_code := replace(v_parent_code,',',''',''');

          raise notice'非LV0，f_parent_code：%',f_parent_code;

        end if;

        -- 【非虚化】根据传入视角标识判断是取大T、子网系统还是国内海外、地区部、代表处
        -- 【非虚化】传入视角标识=地代办 LOCAL_AGENT（国内海外、地区部、代表处）
        if(f_view_flag = 'LOCAL_AGENT') then
          -- 删除数据
          v_sql := 'delete from '||v_table||'
                     where version_id = '||v_version_id||'
                       and del_flag = ''N''
                       and base_period_id = '||f_change_period_id||'    -- 切换基期
                       and bg_code = '''||f_bg_code||'''
                       and sign_top_cust_category_code is null
                       and sign_subsidiary_custcatg_cn_name is null
                       and oversea_flag = '''||f_oversea_flag||'''
                       and region_code = '''||f_region_code||'''
                       and repoffice_code = '''||f_repoffice_code||'''
                       and group_level = '''||f_group_level||'''
                       and group_code in('''||f_group_code||''')
                       and parent_code in('''||f_parent_code||''')
                       and view_flag = '''||f_view_flag||'''
          ';

          raise notice'v_version_id：%',v_version_id;

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => '删除'||f_group_level||'层级（非LV0）的数据 '||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );
          
          -- 【非虚化】根据传入参数取切换基期指数信息
          v_sql := '
          insert into '||v_table||'(
           	     version_id
               , period_year
               , period_id
               , base_period_id
               , bg_code
               , bg_cn_name
               , parent_code
               , parent_cn_name
               , group_code
               , group_cn_name
               , group_level
               , oversea_flag
               , region_code
               , region_cn_name
               , repoffice_code
               , repoffice_cn_name
               , sign_top_cust_category_code
               , sign_top_cust_category_cn_name
               , sign_subsidiary_custcatg_cn_name
               , view_flag
               , cost_index
               , created_by
               , creation_date
               , last_updated_by
               , last_update_date
               , del_flag
          )
          with change_period_info_tmp as(
          select '||v_select_sql||'
            from '||v_table||'
           where version_id = '||v_version_id||'
             and del_flag = ''N''
             and base_period_id = '||v_base_period_id||'  -- 默认基期
             and period_id = '||f_change_period_id||'    -- 切换基期
             and bg_code = '''||f_bg_code||'''
             and sign_top_cust_category_code is null
             and sign_subsidiary_custcatg_cn_name is null
             and oversea_flag = '''||f_oversea_flag||'''
             and region_code = '''||f_region_code||'''
             and repoffice_code = '''||f_repoffice_code||'''
             and group_level = '''||f_group_level||'''
             and group_code in('''||f_group_code||''')
             and parent_code in('''||f_parent_code||''')
             and view_flag = '''||f_view_flag||'''
          ),
          -- 根据传入参数取报告期指数信息
          all_period_info_tmp as(
          select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code is null
               and sign_subsidiary_custcatg_cn_name is null
               and oversea_flag = '''||f_oversea_flag||'''
               and region_code = '''||f_region_code||'''
               and repoffice_code = '''||f_repoffice_code||'''
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code in('''||f_parent_code||''')
               and view_flag = '''||f_view_flag||'''
          )
          select t1.version_id
               , t1.period_year
               , t1.period_id
               , '||f_change_period_id||' as base_period_id
               , t1.bg_code
               , t1.bg_cn_name
               , t1.parent_code
               , t1.parent_cn_name
               , t1.group_code
               , t1.group_cn_name
               , t1.group_level
               , t1.oversea_flag
               , t1.region_code
               , t1.region_cn_name
               , t1.repoffice_code
               , t1.repoffice_cn_name
               , t1.sign_top_cust_category_code
               , t1.sign_top_cust_category_cn_name
               , t1.sign_subsidiary_custcatg_cn_name
               , t1.view_flag
               , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
               , -1 as created_by
               , current_timestamp as creation_date
               , -1 as last_updated_by
               , current_timestamp as last_update_date
               , ''N'' as del_flag
            from all_period_info_tmp t1
            left join change_period_info_tmp t2
              on t1.version_id = t2.version_id
             and t1.bg_code = t2.bg_code
             and t1.parent_code = t2.parent_code
             and t1.group_code = t2.group_code
             and t1.group_level = t2.group_level
             and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
             and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
             and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
             and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
             and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
             and t1.view_flag = t2.view_flag
          ';

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => '非LV0层级国内海外、地区部、代表处数据入到月度指数表，数据量：'||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );

          /**************************************** group_level<>spart，则执行 begin ****************************************************/
          if(f_group_level <> 'SPART') then
            -- 删除数据（多子项）
            v_sql := 'delete from '||v_table||'
                       where version_id = '||v_version_id||'
                         and del_flag = ''N''
                         and base_period_id = '||f_change_period_id||'    -- 切换基期
                         and bg_code = '''||f_bg_code||'''
                         and sign_top_cust_category_code is null
                         and sign_subsidiary_custcatg_cn_name is null
                         and oversea_flag = '''||f_oversea_flag||'''
                         and region_code = '''||f_region_code||'''
                         and repoffice_code = '''||f_repoffice_code||'''
                         and parent_code in('''||f_group_code||''')
                         and view_flag = '''||f_view_flag||'''
            ';

            raise notice'v_version_id：%',v_version_id;

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => '删除'||f_group_level||'层级（非LV0）多子项的数据 '||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );
            
            -- 【非虚化】根据传入参数取切换基期指数信息（多子项）
            v_sql := '
            insert into '||v_table||'(
           	       version_id
                 , period_year
                 , period_id
                 , base_period_id
                 , bg_code
                 , bg_cn_name
                 , parent_code
                 , parent_cn_name
                 , group_code
                 , group_cn_name
                 , group_level
                 , oversea_flag
                 , region_code
                 , region_cn_name
                 , repoffice_code
                 , repoffice_cn_name
                 , sign_top_cust_category_code
                 , sign_top_cust_category_cn_name
                 , sign_subsidiary_custcatg_cn_name
                 , view_flag
                 , cost_index
                 , created_by
                 , creation_date
                 , last_updated_by
                 , last_update_date
                 , del_flag
            )
            with change_period_info_tmp1 as(
            select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'  -- 默认基期
               and period_id = '||f_change_period_id||'    -- 切换基期
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code is null
               and sign_subsidiary_custcatg_cn_name is null
               and oversea_flag = '''||f_oversea_flag||'''
               and region_code = '''||f_region_code||'''
               and repoffice_code = '''||f_repoffice_code||'''
               and parent_code in('''||f_group_code||''')
               and view_flag = '''||f_view_flag||'''
            ),
            -- 根据传入参数取报告期指数信息
            all_period_info_tmp1 as(
            select '||v_select_sql||'
                from '||v_table||'
               where version_id = '||v_version_id||'
                 and del_flag = ''N''
                 and base_period_id = '||v_base_period_id||'
                 and bg_code = '''||f_bg_code||'''
                 and sign_top_cust_category_code is null
                 and sign_subsidiary_custcatg_cn_name is null
                 and oversea_flag = '''||f_oversea_flag||'''
                 and region_code = '''||f_region_code||'''
                 and repoffice_code = '''||f_repoffice_code||'''
                 and parent_code in('''||f_group_code||''')
                 and view_flag = '''||f_view_flag||'''
            )
            select t1.version_id
                 , t1.period_year
                 , t1.period_id
                 , '||f_change_period_id||' as base_period_id
                 , t1.bg_code
                 , t1.bg_cn_name
                 , t1.parent_code
                 , t1.parent_cn_name
                 , t1.group_code
                 , t1.group_cn_name
                 , t1.group_level
                 , t1.oversea_flag
                 , t1.region_code
                 , t1.region_cn_name
                 , t1.repoffice_code
                 , t1.repoffice_cn_name
                 , t1.sign_top_cust_category_code
                 , t1.sign_top_cust_category_cn_name
                 , t1.sign_subsidiary_custcatg_cn_name
                 , t1.view_flag
                 , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
                 , -1 as created_by
                 , current_timestamp as creation_date
                 , -1 as last_updated_by
                 , current_timestamp as last_update_date
                 , ''N'' as del_flag
              from all_period_info_tmp1 t1
              left join change_period_info_tmp1 t2
                on t1.version_id = t2.version_id
               and t1.bg_code = t2.bg_code
               and t1.parent_code = t2.parent_code
               and t1.group_code = t2.group_code
               and t1.group_level = t2.group_level
               and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
               and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
               and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
               and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
               and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
               and t1.view_flag = t2.view_flag
            ';

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => '非LV0层级国内海外、地区部、代表处数据（多子项）入到月度指数表，数据量：'||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );

          end if;
          /**************************************** group_level<>spart，则执行 end ****************************************************/

        -- 【非虚化】传入视角标识=系统部 SYS_DEPT（大T系统、子网系统）
        elseif(f_view_flag = 'SYS_DEPT') then
          -- 删除数据
          v_sql := 'delete from '||v_table||'
                     where version_id = '||v_version_id||'
                       and del_flag = ''N''
                       and base_period_id = '||f_change_period_id||'    -- 切换基期
                       and bg_code = '''||f_bg_code||'''
                       and sign_top_cust_category_code = '''||f_st_code||'''
                       and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                       and oversea_flag is null
                       and region_code is null
                       and repoffice_code is null
                       and group_level = '''||f_group_level||'''
                       and group_code in('''||f_group_code||''')
                       and parent_code in('''||f_parent_code||''')
                       and view_flag = '''||f_view_flag||'''
          ';

          raise notice'v_version_id：%',v_version_id;

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => '删除'||f_group_level||'层级（非LV0）的数据 '||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );
          
          -- 【非虚化】根据传入参数取切换基期指数信息
          v_sql := '
          insert into '||v_table||'(
          	     version_id
               , period_year
               , period_id
               , base_period_id
               , bg_code
               , bg_cn_name
               , parent_code
               , parent_cn_name
               , group_code
               , group_cn_name
               , group_level
               , oversea_flag
               , region_code
               , region_cn_name
               , repoffice_code
               , repoffice_cn_name
               , sign_top_cust_category_code
               , sign_top_cust_category_cn_name
               , sign_subsidiary_custcatg_cn_name
               , view_flag
               , cost_index
               , created_by
               , creation_date
               , last_updated_by
               , last_update_date
               , del_flag
          )
          with change_period_info_tmp as(
          select '||v_select_sql||'
            from '||v_table||'
           where version_id = '||v_version_id||'
             and del_flag = ''N''
             and base_period_id = '||v_base_period_id||'  -- 默认基期
             and period_id = '||f_change_period_id||'    -- 切换基期
             and bg_code = '''||f_bg_code||'''
             and sign_top_cust_category_code = '''||f_st_code||'''
             and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
             and oversea_flag is null
             and region_code is null
             and repoffice_code is null
             and group_level = '''||f_group_level||'''
             and group_code in('''||f_group_code||''')
             and parent_code in('''||f_parent_code||''')
             and view_flag = '''||f_view_flag||'''
          ),
          -- 根据传入参数取报告期指数信息
          all_period_info_tmp as(
          select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code = '''||f_st_code||'''
               and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
               and oversea_flag is null
               and region_code is null
               and repoffice_code is null
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code in('''||f_parent_code||''')
               and view_flag = '''||f_view_flag||'''
          )
          select t1.version_id
               , t1.period_year
               , t1.period_id
               , '||f_change_period_id||' as base_period_id
               , t1.bg_code
               , t1.bg_cn_name
               , t1.parent_code
               , t1.parent_cn_name
               , t1.group_code
               , t1.group_cn_name
               , t1.group_level
               , t1.oversea_flag
               , t1.region_code
               , t1.region_cn_name
               , t1.repoffice_code
               , t1.repoffice_cn_name
               , t1.sign_top_cust_category_code
               , t1.sign_top_cust_category_cn_name
               , t1.sign_subsidiary_custcatg_cn_name
               , t1.view_flag
               , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
               , -1 as created_by
               , current_timestamp as creation_date
               , -1 as last_updated_by
               , current_timestamp as last_update_date
               , ''N'' as del_flag
            from all_period_info_tmp t1
            left join change_period_info_tmp t2
              on t1.version_id = t2.version_id
             and t1.bg_code = t2.bg_code
             and t1.parent_code = t2.parent_code
             and t1.group_code = t2.group_code
             and t1.group_level = t2.group_level
             and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
             and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
             and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
             and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
             and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
             and t1.view_flag = t2.view_flag
          ';

          dbms_output.put_line(v_sql);

          execute immediate v_sql;

          v_step_num := v_step_num+1;
          perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
              f_sp_name => v_sp_name,    --sp名称
              f_step_num => v_step_num,
              f_cal_log_desc => '非LV0层级大T系统、子网系统数据入到月累计指数表，数据量：'||sql%rowcount,--日志描述
              f_formula_sql_txt  => v_sql,
              f_dml_row_count => sql%rowcount,
              f_result_status => x_result_status,
              f_errbuf => 'SUCCESS'
          );

          /********************************************************** group_level<>spart，则执行 begin ******************************************************/
          if(f_group_level <> 'SPART') then
            -- 删除数据（多子项）
            v_sql := 'delete from '||v_table||'
                       where version_id = '||v_version_id||'
                         and del_flag = ''N''
                         and base_period_id = '||f_change_period_id||'    -- 切换基期
                         and bg_code = '''||f_bg_code||'''
                         and sign_top_cust_category_code = '''||f_st_code||'''
                         and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                         and oversea_flag is null
                         and region_code is null
                         and repoffice_code is null
                         and parent_code in('''||f_group_code||''')
                         and view_flag = '''||f_view_flag||'''
            ';

            raise notice'v_version_id：%',v_version_id;

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => '删除'||f_group_level||'层级（非LV0）多子项的数据 '||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );
            
            -- 【非虚化】根据传入参数取切换基期指数信息（多子项）
            v_sql := '
            insert into '||v_table||'(
            	     version_id
                 , period_year
                 , period_id
                 , base_period_id
                 , bg_code
                 , bg_cn_name
                 , parent_code
                 , parent_cn_name
                 , group_code
                 , group_cn_name
                 , group_level
                 , oversea_flag
                 , region_code
                 , region_cn_name
                 , repoffice_code
                 , repoffice_cn_name
                 , sign_top_cust_category_code
                 , sign_top_cust_category_cn_name
                 , sign_subsidiary_custcatg_cn_name
                 , view_flag
                 , cost_index
                 , created_by
                 , creation_date
                 , last_updated_by
                 , last_update_date
                 , del_flag
            )
            with change_period_info_tmp1 as(
            select '||v_select_sql||'
              from '||v_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'  -- 默认基期
               and period_id = '||f_change_period_id||'    -- 切换基期
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code = '''||f_st_code||'''
               and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
               and oversea_flag is null
               and region_code is null
               and repoffice_code is null
               and parent_code in('''||f_group_code||''')
               and view_flag = '''||f_view_flag||'''
            ),
            -- 根据传入参数取报告期指数信息
            all_period_info_tmp1 as(
            select '||v_select_sql||'
                from '||v_table||'
               where version_id = '||v_version_id||'
                 and del_flag = ''N''
                 and base_period_id = '||v_base_period_id||'
                 and bg_code = '''||f_bg_code||'''
                 and sign_top_cust_category_code = '''||f_st_code||'''
                 and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                 and oversea_flag is null
                 and region_code is null
                 and repoffice_code is null
                 and parent_code in('''||f_group_code||''')
                 and view_flag = '''||f_view_flag||'''
            )
            select t1.version_id
                 , t1.period_year
                 , t1.period_id
                 , '||f_change_period_id||' as base_period_id
                 , t1.bg_code
                 , t1.bg_cn_name
                 , t1.parent_code
                 , t1.parent_cn_name
                 , t1.group_code
                 , t1.group_cn_name
                 , t1.group_level
                 , t1.oversea_flag
                 , t1.region_code
                 , t1.region_cn_name
                 , t1.repoffice_code
                 , t1.repoffice_cn_name
                 , t1.sign_top_cust_category_code
                 , t1.sign_top_cust_category_cn_name
                 , t1.sign_subsidiary_custcatg_cn_name
                 , t1.view_flag
                 , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
                 , -1 as created_by
                 , current_timestamp as creation_date
                 , -1 as last_updated_by
                 , current_timestamp as last_update_date
                 , ''N'' as del_flag
              from all_period_info_tmp1 t1
              left join change_period_info_tmp1 t2
                on t1.version_id = t2.version_id
               and t1.bg_code = t2.bg_code
               and t1.parent_code = t2.parent_code
               and t1.group_code = t2.group_code
               and t1.group_level = t2.group_level
               and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
               and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
               and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
               and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
               and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
               and t1.view_flag = t2.view_flag
            ';

            dbms_output.put_line(v_sql);

            execute immediate v_sql;

            v_step_num := v_step_num+1;
            perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
                f_sp_name => v_sp_name,    --sp名称
                f_step_num => v_step_num,
                f_cal_log_desc => '非LV0层级大T系统、子网系统数据（多子项）入到月累计指数表，数据量：'||sql%rowcount,--日志描述
                f_formula_sql_txt  => v_sql,
                f_dml_row_count => sql%rowcount,
                f_result_status => x_result_status,
                f_errbuf => 'SUCCESS'
            );

          end if;
          /********************************************************** group_level<>spart，则执行 end ******************************************************/

        end if;

      end if;
      /************************************************* 非虚化非LV0层级 end ***********************************************************/

    --【第二层IF的ELSE】
    --【虚化逻辑】
    -- 如果组合ID不为空值，则从虚化后月累计指数表 dm_fcst_price_base_cus_mon_cost_idx_t 取值
    else
      -- 【虚化】传入视角标识=地代办 LOCAL_AGENT（国内海外、地区部、代表处）
      if(f_view_flag = 'LOCAL_AGENT') then
        -- 删除数据
        v_sql := 'delete from '||v_cus_table||'
                   where version_id = '||v_version_id||'
                     and del_flag = ''N''
                     and base_period_id = '||f_change_period_id||'    -- 切换基期
                     and bg_code = '''||f_bg_code||'''
                     and sign_top_cust_category_code is null
                     and sign_subsidiary_custcatg_cn_name is null
                     and oversea_flag = '''||f_oversea_flag||'''
                     and region_code = '''||f_region_code||'''
                     and repoffice_code = '''||f_repoffice_code||'''
                     and group_level = '''||f_group_level||'''
                     and group_code in('''||f_group_code||''')
                     and parent_code in('''||f_parent_code||''')
                     and view_flag = '''||f_view_flag||'''
                     and custom_id in('''||f_custom_id||''')
        ';

        raise notice'v_version_id：%',v_version_id;

        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '删除虚化表 '||f_view_flag||' 的数据 '||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
      
        -- 【虚化】根据传入参数取切换基期指数信息
        v_sql := '
        insert into '||v_cus_table||'(
           	     version_id
               , period_year
               , period_id
               , base_period_id
               , bg_code
               , bg_cn_name
               , parent_code
               , parent_cn_name
               , custom_id
               , custom_cn_name
               , group_code
               , group_cn_name
               , group_level
               , oversea_flag
               , region_code
               , region_cn_name
               , repoffice_code
               , repoffice_cn_name
               , sign_top_cust_category_code
               , sign_top_cust_category_cn_name
               , sign_subsidiary_custcatg_cn_name
               , view_flag
               , cost_index
               , created_by
               , creation_date
               , last_updated_by
               , last_update_date
               , del_flag
          )
          with change_period_info_tmp as(
          select '||v_cus_select_sql||'
               , parent_code
               , parent_cn_name
            from '||v_cus_table||'
           where version_id = '||v_version_id||'
             and del_flag = ''N''
             and base_period_id = '||v_base_period_id||'  -- 默认基期
             and period_id = '||f_change_period_id||'    -- 切换基期
             and bg_code = '''||f_bg_code||'''
             and sign_top_cust_category_code is null
             and sign_subsidiary_custcatg_cn_name is null
             and oversea_flag = '''||f_oversea_flag||'''
             and region_code = '''||f_region_code||'''
             and repoffice_code = '''||f_repoffice_code||'''
             and group_level = '''||f_group_level||'''
             and group_code in('''||f_group_code||''')
             and parent_code in('''||f_parent_code||''')
             and view_flag = '''||f_view_flag||'''
             and custom_id in('''||f_custom_id||''')
          ),
          -- 根据传入参数取报告期指数信息
          all_period_info_tmp as(
          select '||v_cus_select_sql||'
               , parent_code
               , parent_cn_name
              from '||v_cus_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code is null
               and sign_subsidiary_custcatg_cn_name is null
               and oversea_flag = '''||f_oversea_flag||'''
               and region_code = '''||f_region_code||'''
               and repoffice_code = '''||f_repoffice_code||'''
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code in('''||f_parent_code||''')
               and view_flag = '''||f_view_flag||'''
               and custom_id in('''||f_custom_id||''')
          )
          select t1.version_id
               , t1.period_year
               , t1.period_id
               , '||f_change_period_id||' as base_period_id
               , t1.bg_code
               , t1.bg_cn_name
               , t1.parent_code
               , t1.parent_cn_name
               , t1.custom_id
               , t1.custom_cn_name
               , t1.group_code
               , t1.group_cn_name
               , t1.group_level
               , t1.oversea_flag
               , t1.region_code
               , t1.region_cn_name
               , t1.repoffice_code
               , t1.repoffice_cn_name
               , t1.sign_top_cust_category_code
               , t1.sign_top_cust_category_cn_name
               , t1.sign_subsidiary_custcatg_cn_name
               , t1.view_flag
               , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
               , -1 as created_by
               , current_timestamp as creation_date
               , -1 as last_updated_by
               , current_timestamp as last_update_date
               , ''N'' as del_flag
            from all_period_info_tmp t1
            left join change_period_info_tmp t2
              on t1.version_id = t2.version_id
             and t1.bg_code = t2.bg_code
             and nvl(t1.parent_code,''A'') = nvl(t2.parent_code,''A'')
             and t1.custom_id = t2.custom_id
             and t1.group_code = t2.group_code
             and t1.group_level = t2.group_level
             and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
             and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
             and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
             and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
             and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
             and t1.view_flag = t2.view_flag
          ';
      
        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '价格指数数据入到虚化月度指数表，数据量：'||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
        
        -- 删除数据
        v_sql := 'delete from '||v_table||'
                   where version_id = '||v_version_id||'
                     and del_flag = ''N''
                     and base_period_id = '||f_change_period_id||'    -- 切换基期
                     and bg_code = '''||f_bg_code||'''
                     and sign_top_cust_category_code is null
                     and sign_subsidiary_custcatg_cn_name is null
                     and oversea_flag = '''||f_oversea_flag||'''
                     and region_code = '''||f_region_code||'''
                     and repoffice_code = '''||f_repoffice_code||'''
                     and group_code in('''||f_group_code||''')
                     and view_flag = '''||f_view_flag||'''
        ';

        raise notice'v_version_id：%',v_version_id;

        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '删除虚化基期切换的多子项数据，数据量：'||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
        
        -- 【虚化】根据传入参数取切换基期指数信息（多子项） 
        v_sql := '
        insert into '||v_table||'(
          	   version_id
             , period_year
             , period_id
             , base_period_id
             , bg_code
             , bg_cn_name
             , parent_code
             , parent_cn_name
             , group_code
             , group_cn_name
             , group_level
             , oversea_flag
             , region_code
             , region_cn_name
             , repoffice_code
             , repoffice_cn_name
             , sign_top_cust_category_code
             , sign_top_cust_category_cn_name
             , sign_subsidiary_custcatg_cn_name
             , view_flag
             , cost_index
             , created_by
             , creation_date
             , last_updated_by
             , last_update_date
             , del_flag
        )
        -- 根据传入参数从虚化表取切换基期的数据
        with cus_mon_cost_idx_tmp as(
        select distinct '||v_cus_select_sql||'
          from '||v_cus_table||'
         where version_id = '||v_version_id||'
           and del_flag = ''N''
           and base_period_id = '||v_base_period_id||'  -- 默认基期
           and period_id = '||f_change_period_id||'    -- 切换基期
           and bg_code = '''||f_bg_code||'''
           and sign_top_cust_category_code is null
           and sign_subsidiary_custcatg_cn_name is null
           and oversea_flag = '''||f_oversea_flag||'''
           and region_code = '''||f_region_code||'''
           and repoffice_code = '''||f_repoffice_code||'''
           and group_code in('''||f_group_code||''')
           and view_flag = '''||f_view_flag||'''
           and custom_id in('''||f_custom_id||''')
        ),
        -- 根据传入参数从非虚化表取报告期数据
        mon_cost_idx_tmp as(
        select '||v_select_sql||'
          from '||v_table||'
         where version_id = '||v_version_id||'
           and del_flag = ''N''
           and base_period_id = '||v_base_period_id||'  -- 默认基期
           and bg_code = '''||f_bg_code||'''
           and sign_top_cust_category_code is null
           and sign_subsidiary_custcatg_cn_name is null
           and oversea_flag = '''||f_oversea_flag||'''
           and region_code = '''||f_region_code||'''
           and repoffice_code = '''||f_repoffice_code||'''
           and group_code in('''||f_group_code||''')
           and view_flag = '''||f_view_flag||'''
        ),
        -- 获取切换基期的数据
        change_period_info_tmp as(
        select t1.version_id
             , t1.period_year
             , t1.period_id
             , t1.base_period_id
             , t1.bg_code
             , t1.bg_cn_name
             , t1.parent_code
             , t1.parent_cn_name
             , t1.group_code
             , t1.group_cn_name
             , t1.group_level
             , t1.oversea_flag
             , t1.region_code
             , t1.region_cn_name
             , t1.repoffice_code
             , t1.repoffice_cn_name
             , t1.sign_top_cust_category_code
             , t1.sign_top_cust_category_cn_name
             , t1.sign_subsidiary_custcatg_cn_name
             , t1.view_flag
             , t1.cost_index
          from mon_cost_idx_tmp t1
          join cus_mon_cost_idx_tmp t2
            on t1.version_id = t2.version_id
           and t1.base_period_id = t2.base_period_id
           and t1.period_id = t2.period_id
           and t1.bg_code = t2.bg_code
           and t1.group_code = t2.group_code
           and t1.group_level = t2.group_level
           and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
           and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
           and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
           and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
           and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
           and t1.view_flag = t2.view_flag
        ),
        -- 获取报告期的数据
        all_period_info_tmp as(
        select t1.version_id
             , t1.period_year
             , t1.period_id
             , t1.base_period_id
             , t1.bg_code
             , t1.bg_cn_name
             , t1.parent_code
             , t1.parent_cn_name
             , t1.group_code
             , t1.group_cn_name
             , t1.group_level
             , t1.oversea_flag
             , t1.region_code
             , t1.region_cn_name
             , t1.repoffice_code
             , t1.repoffice_cn_name
             , t1.sign_top_cust_category_code
             , t1.sign_top_cust_category_cn_name
             , t1.sign_subsidiary_custcatg_cn_name
             , t1.view_flag
             , t1.cost_index
          from mon_cost_idx_tmp t1
          join cus_mon_cost_idx_tmp t2
            on t1.version_id = t2.version_id
           and t1.base_period_id = t2.base_period_id
           and t1.bg_code = t2.bg_code
           and t1.group_code = t2.group_code
           and t1.group_level = t2.group_level
           and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
           and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
           and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
           and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
           and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
           and t1.view_flag = t2.view_flag
        )
        select t1.version_id
             , t1.period_year
             , t1.period_id
             , '||f_change_period_id||' as base_period_id
             , t1.bg_code
             , t1.bg_cn_name
             , t1.parent_code
             , t1.parent_cn_name
             , t1.group_code
             , t1.group_cn_name
             , t1.group_level
             , t1.oversea_flag
             , t1.region_code
             , t1.region_cn_name
             , t1.repoffice_code
             , t1.repoffice_cn_name
             , t1.sign_top_cust_category_code
             , t1.sign_top_cust_category_cn_name
             , t1.sign_subsidiary_custcatg_cn_name
             , t1.view_flag
             , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
             , -1 as created_by
             , current_timestamp as creation_date
             , -1 as last_updated_by
             , current_timestamp as last_update_date
             , ''N'' as del_flag
          from all_period_info_tmp t1
          left join change_period_info_tmp t2
            on t1.version_id = t2.version_id
           and t1.base_period_id = t2.base_period_id
           and t1.bg_code = t2.bg_code
           and t1.parent_code = t2.parent_code
           and t1.group_code = t2.group_code
           and t1.group_level = t2.group_level
           and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
           and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
           and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
           and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
           and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
           and t1.view_flag = t2.view_flag
        ';
        
        raise notice'111111111';
        
        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '价格指数（多子项）数据入到非虚化月度指数表，数据量：'||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
      
        raise notice'2222222';
      
      -- 【虚化】传入视角标识=系统部 SYS_DEPT（大T系统、子网系统）
      elseif(f_view_flag = 'SYS_DEPT') then
        -- 删除数据
        v_sql := 'delete from '||v_cus_table||'
                   where version_id = '||v_version_id||'
                     and del_flag = ''N''
                     and base_period_id = '||f_change_period_id||'    -- 切换基期
                     and bg_code = '''||f_bg_code||'''
                     and sign_top_cust_category_code = '''||f_st_code||'''
                     and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                     and oversea_flag is null
                     and region_code is null
                     and repoffice_code is null
                     and group_level = '''||f_group_level||'''
                     and group_code in('''||f_group_code||''')
                     and parent_code in('''||f_parent_code||''')
                     and view_flag = '''||f_view_flag||'''
                     and custom_id in('''||f_custom_id||''')
        ';

        raise notice'v_version_id：%',v_version_id;

        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '删除虚化表 '||f_view_flag||' 的数据 '||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
      
        -- 【虚化】根据传入参数取切换基期指数信息
        v_sql := '
        insert into '||v_cus_table||'(
           	     version_id
               , period_year
               , period_id
               , base_period_id
               , bg_code
               , bg_cn_name
               , parent_code
               , parent_cn_name
               , custom_id
               , custom_cn_name
               , group_code
               , group_cn_name
               , group_level
               , oversea_flag
               , region_code
               , region_cn_name
               , repoffice_code
               , repoffice_cn_name
               , sign_top_cust_category_code
               , sign_top_cust_category_cn_name
               , sign_subsidiary_custcatg_cn_name
               , view_flag
               , cost_index
               , created_by
               , creation_date
               , last_updated_by
               , last_update_date
               , del_flag
          )
          with change_period_info_tmp as(
          select '||v_cus_select_sql||'
               , parent_code
               , parent_cn_name
            from '||v_cus_table||'
           where version_id = '||v_version_id||'
             and del_flag = ''N''
             and base_period_id = '||v_base_period_id||'  -- 默认基期
             and period_id = '||f_change_period_id||'    -- 切换基期
             and bg_code = '''||f_bg_code||'''
             and sign_top_cust_category_code = '''||f_st_code||'''
             and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
             and oversea_flag is null
             and region_code is null
             and repoffice_code is null
             and group_level = '''||f_group_level||'''
             and group_code in('''||f_group_code||''')
             and parent_code in('''||f_parent_code||''')
             and view_flag = '''||f_view_flag||'''
             and custom_id in('''||f_custom_id||''')
          ),
          -- 根据传入参数取报告期指数信息
          all_period_info_tmp as(
          select '||v_cus_select_sql||'
               , parent_code
               , parent_cn_name
              from '||v_cus_table||'
             where version_id = '||v_version_id||'
               and del_flag = ''N''
               and base_period_id = '||v_base_period_id||'
               and bg_code = '''||f_bg_code||'''
               and sign_top_cust_category_code = '''||f_st_code||'''
               and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
               and oversea_flag is null
               and region_code is null
               and repoffice_code is null
               and group_level = '''||f_group_level||'''
               and group_code in('''||f_group_code||''')
               and parent_code in('''||f_parent_code||''')
               and view_flag = '''||f_view_flag||'''
               and custom_id in('''||f_custom_id||''')
          )
          select t1.version_id
               , t1.period_year
               , t1.period_id
               , '||f_change_period_id||' as base_period_id
               , t1.bg_code
               , t1.bg_cn_name
               , t1.parent_code
               , t1.parent_cn_name
               , t1.custom_id
               , t1.custom_cn_name
               , t1.group_code
               , t1.group_cn_name
               , t1.group_level
               , t1.oversea_flag
               , t1.region_code
               , t1.region_cn_name
               , t1.repoffice_code
               , t1.repoffice_cn_name
               , t1.sign_top_cust_category_code
               , t1.sign_top_cust_category_cn_name
               , t1.sign_subsidiary_custcatg_cn_name
               , t1.view_flag
               , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
               , -1 as created_by
               , current_timestamp as creation_date
               , -1 as last_updated_by
               , current_timestamp as last_update_date
               , ''N'' as del_flag
            from all_period_info_tmp t1
            left join change_period_info_tmp t2
              on t1.version_id = t2.version_id
             and t1.bg_code = t2.bg_code
             and nvl(t1.parent_code,''A'') = nvl(t2.parent_code,''A'')
             and t1.custom_id = t2.custom_id
             and t1.group_code = t2.group_code
             and t1.group_level = t2.group_level
             and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
             and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
             and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
             and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
             and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
             and t1.view_flag = t2.view_flag
          ';
      
        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '价格指数数据入到虚化月度指数表，数据量：'||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
        
        -- 删除数据
        v_sql := 'delete from '||v_table||'
                   where version_id = '||v_version_id||'
                     and del_flag = ''N''
                     and base_period_id = '||f_change_period_id||'    -- 切换基期
                     and bg_code = '''||f_bg_code||'''
                     and sign_top_cust_category_code = '''||f_st_code||'''
                     and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
                     and oversea_flag is null
                     and region_code is null
                     and repoffice_code is null
                     and group_code in('''||f_group_code||''')
                     and view_flag = '''||f_view_flag||'''
        ';

        raise notice'v_version_id：%',v_version_id;

        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '删除虚化基期切换的多子项数据，数据量：'||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
        
        -- 【虚化】根据传入参数取切换基期指数信息（多子项）  
        v_sql := '
        insert into '||v_table||'(
          	   version_id
             , period_year
             , period_id
             , base_period_id
             , bg_code
             , bg_cn_name
             , parent_code
             , parent_cn_name
             , group_code
             , group_cn_name
             , group_level
             , oversea_flag
             , region_code
             , region_cn_name
             , repoffice_code
             , repoffice_cn_name
             , sign_top_cust_category_code
             , sign_top_cust_category_cn_name
             , sign_subsidiary_custcatg_cn_name
             , view_flag
             , cost_index
             , created_by
             , creation_date
             , last_updated_by
             , last_update_date
             , del_flag
        )
        -- 根据传入参数从虚化表取切换基期的数据
        with cus_mon_cost_idx_tmp as(
        select distinct '||v_cus_select_sql||'
          from '||v_cus_table||'
         where version_id = '||v_version_id||'
           and del_flag = ''N''
           and base_period_id = '||v_base_period_id||'  -- 默认基期
           and period_id = '||f_change_period_id||'    -- 切换基期
           and bg_code = '''||f_bg_code||'''
           and sign_top_cust_category_code = '''||f_st_code||'''
           and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
           and oversea_flag is null
           and region_code is null
           and repoffice_code is null
           and group_code in('''||f_group_code||''')
           and view_flag = '''||f_view_flag||'''
           and custom_id in('''||f_custom_id||''')
        ),
        -- 根据传入参数从非虚化表取报告期数据
        mon_cost_idx_tmp as(
        select '||v_select_sql||'
          from '||v_table||'
         where version_id = '||v_version_id||'
           and del_flag = ''N''
           and base_period_id = '||v_base_period_id||'  -- 默认基期
           and bg_code = '''||f_bg_code||'''
           and sign_top_cust_category_code = '''||f_st_code||'''
           and sign_subsidiary_custcatg_cn_name = '''||f_st_sub_code||'''
           and oversea_flag is null
           and region_code is null
           and repoffice_code is null
           and group_code in('''||f_group_code||''')
           and view_flag = '''||f_view_flag||'''
        ),
        -- 获取切换基期的数据
        change_period_info_tmp as(
        select t1.version_id
             , t1.period_year
             , t1.period_id
             , t1.base_period_id
             , t1.bg_code
             , t1.bg_cn_name
             , t1.parent_code
             , t1.parent_cn_name
             , t1.group_code
             , t1.group_cn_name
             , t1.group_level
             , t1.oversea_flag
             , t1.region_code
             , t1.region_cn_name
             , t1.repoffice_code
             , t1.repoffice_cn_name
             , t1.sign_top_cust_category_code
             , t1.sign_top_cust_category_cn_name
             , t1.sign_subsidiary_custcatg_cn_name
             , t1.view_flag
             , t1.cost_index
          from mon_cost_idx_tmp t1
          join cus_mon_cost_idx_tmp t2
            on t1.version_id = t2.version_id
           and t1.base_period_id = t2.base_period_id
           and t1.period_id = t2.period_id
           and t1.bg_code = t2.bg_code
           and t1.group_code = t2.group_code
           and t1.group_level = t2.group_level
           and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
           and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
           and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
           and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
           and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
           and t1.view_flag = t2.view_flag
        ),
        -- 获取报告期的数据
        all_period_info_tmp as(
        select t1.version_id
             , t1.period_year
             , t1.period_id
             , t1.base_period_id
             , t1.bg_code
             , t1.bg_cn_name
             , t1.parent_code
             , t1.parent_cn_name
             , t1.group_code
             , t1.group_cn_name
             , t1.group_level
             , t1.oversea_flag
             , t1.region_code
             , t1.region_cn_name
             , t1.repoffice_code
             , t1.repoffice_cn_name
             , t1.sign_top_cust_category_code
             , t1.sign_top_cust_category_cn_name
             , t1.sign_subsidiary_custcatg_cn_name
             , t1.view_flag
             , t1.cost_index
          from mon_cost_idx_tmp t1
          join cus_mon_cost_idx_tmp t2
            on t1.version_id = t2.version_id
           and t1.base_period_id = t2.base_period_id
           and t1.bg_code = t2.bg_code
           and t1.group_code = t2.group_code
           and t1.group_level = t2.group_level
           and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
           and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
           and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
           and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
           and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
           and t1.view_flag = t2.view_flag
        )
        select t1.version_id
             , t1.period_year
             , t1.period_id
             , '||f_change_period_id||' as base_period_id
             , t1.bg_code
             , t1.bg_cn_name
             , t1.parent_code
             , t1.parent_cn_name
             , t1.group_code
             , t1.group_cn_name
             , t1.group_level
             , t1.oversea_flag
             , t1.region_code
             , t1.region_cn_name
             , t1.repoffice_code
             , t1.repoffice_cn_name
             , t1.sign_top_cust_category_code
             , t1.sign_top_cust_category_cn_name
             , t1.sign_subsidiary_custcatg_cn_name
             , t1.view_flag
             , (case when t2.cost_index = 0 then 0 else (t1.cost_index/t2.cost_index)*100 end) as cost_index   -- 报告期指数/切换基期指数
             , -1 as created_by
             , current_timestamp as creation_date
             , -1 as last_updated_by
             , current_timestamp as last_update_date
             , ''N'' as del_flag
          from all_period_info_tmp t1
          left join change_period_info_tmp t2
            on t1.version_id = t2.version_id
           and t1.base_period_id = t2.base_period_id
           and t1.bg_code = t2.bg_code
           and t1.parent_code = t2.parent_code
           and t1.group_code = t2.group_code
           and t1.group_level = t2.group_level
           and nvl(t1.oversea_flag,''A'') = nvl(t2.oversea_flag,''A'')
           and nvl(t1.region_code,''A'') = nvl(t2.region_code,''A'')
           and nvl(t1.repoffice_code,''A'') = nvl(t2.repoffice_code,''A'')
           and nvl(t1.sign_top_cust_category_code,''A'') = nvl(t2.sign_top_cust_category_code,''A'')
           and nvl(t1.sign_subsidiary_custcatg_cn_name,''A'') = nvl(t2.sign_subsidiary_custcatg_cn_name,''A'')
           and t1.view_flag = t2.view_flag
        ';
        
        raise notice'111111111';
        
        dbms_output.put_line(v_sql);

        execute immediate v_sql;

        v_step_num := v_step_num+1;
        perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
            f_sp_name => v_sp_name,    --sp名称
            f_step_num => v_step_num,
            f_cal_log_desc => '价格指数（多子项）数据入到非虚化月度指数表，数据量：'||sql%rowcount,--日志描述
            f_formula_sql_txt  => v_sql,
            f_dml_row_count => sql%rowcount,
            f_result_status => x_result_status,
            f_errbuf => 'SUCCESS'
        );
        
        
        raise notice'33333333';
        
      end if;
      
    -- 【第二层IF END】
    end if;

  -- 【第一层IF END】
  end if;

  --收集统计信息
	v_sql := 'analyse '||v_table;
	execute v_sql;

	v_sql := 'analyse '||v_cus_table;
	execute v_sql;

  v_step_num := v_step_num + 1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
     f_sp_name => v_sp_name,
     f_step_num => v_step_num,
     f_cal_log_desc => v_sp_name||'运行结束, 收集'||v_table||'、'||v_cus_table||' 统计信息完成!'
  );


  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
         f_sp_name => v_sp_name,    -- sp名称
         f_step_num => v_step_num,
         f_cal_log_desc => v_sp_name||'：运行错误',-- 日志描述
         f_formula_sql_txt  => v_sql,
         f_dml_row_count => sql%rowcount,
         f_result_status => '0',
         f_errbuf => sqlstate  -- 错误编码
      ) ;

      x_result_status := 'FAILED';

end
$$
/

