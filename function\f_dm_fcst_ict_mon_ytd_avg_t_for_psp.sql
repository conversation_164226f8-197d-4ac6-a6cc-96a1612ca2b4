-- Name: f_dm_fcst_ict_mon_ytd_avg_t_for_psp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_ytd_avg_t_for_psp(f_oversea_flag character varying, f_bg_code character varying, f_cost_type character varying, f_granularity_type character varying, f_keystr text DEFAULT NULL::text, f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间: 2024年8月2日15点46分
创建人  : 黄心蕊 hwx1187045
背景描述: 全量月累计均本表数据计算
参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二: F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录
		参数三: F_KEYSTR	密钥
		参数四: F_VERSION_ID 版本号
		参数五: X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

-----来源表
DM_FCST_ICT_PSP_IRB_MID_MON_SPART_ENTIRE_T
DM_FCST_ICT_PSP_PROD_MID_MON_SPART_ENTIRE_T
DM_FCST_ICT_PSP_INDUS_MID_MON_SPART_ENTIRE_T
DM_FCST_ICT_STD_IRB_MID_MON_SPART_ENTIRE_T
DM_FCST_ICT_STD_PROD_MID_MON_SPART_ENTIRE_T
DM_FCST_ICT_STD_INDUS_MID_MON_SPART_ENTIRE_T

-----目标表
DM_FCST_ICT_PSP_IRB_MON_YTD_AVG_T
DM_FCST_ICT_PSP_PROD_MON_YTD_AVG_T
DM_FCST_ICT_PSP_INDUS_MON_YTD_AVG_T
DM_FCST_ICT_STD_IRB_MON_YTD_AVG_T
DM_FCST_ICT_STD_PROD_MON_YTD_AVG_T
DM_FCST_ICT_STD_INDUS_MON_YTD_AVG_T

-----编码替代页面用到的目标表
DM_FCST_ICT_PSP_IRB_REPL_MON_YTD_AVG_T
DM_FCST_ICT_PSP_PROD_REPL_MON_YTD_AVG_T
DM_FCST_ICT_PSP_INDUS_REPL_MON_YTD_AVG_T
DM_FCST_ICT_STD_IRB_REPL_MON_YTD_AVG_T
DM_FCST_ICT_STD_PROD_REPL_MON_YTD_AVG_T
DM_FCST_ICT_STD_INDUS_REPL_MON_YTD_AVG_T


事例 SELECT FIN_DM_OPT_FOI.f_dm_fcst_ict_mon_ytd_avg_t('PSP','IRB','','');		--PSP成本 重量级团队目录一个版本数据
	 SELECT FIN_DM_OPT_FOI.f_dm_fcst_ict_mon_ytd_avg_t('PSP','INDUS','',''); 	--PSP成本 产业目录一个版本数据
	 SELECT FIN_DM_OPT_FOI.f_dm_fcst_ict_mon_ytd_avg_t('PSP','PROD','','');		--PSP成本 销售目录一个版本数据
	 SELECT FIN_DM_OPT_FOI.f_dm_fcst_ict_mon_ytd_avg_t('STD''IRB','密钥',''); 	--标准成本 重量级团队目录一个版本数据
	 SELECT FIN_DM_OPT_FOI.f_dm_fcst_ict_mon_ytd_avg_t('STD''INDUS','密钥','');	--标准成本 产业目录一个版本数据
	 SELECT FIN_DM_OPT_FOI.f_dm_fcst_ict_mon_ytd_avg_t('STD''PROD','密钥','');	--标准成本 销售目录一个版本数据

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME             VARCHAR(100) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_YTD_AVG_T_FOR_PSP';
  V_VERSION             INT; --版本号
  V_EXCEPTION_FLAG		INT;
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY')-2 ||'01','YYYYMM');
  V_TO_TABLE            VARCHAR(200);
  V_TO_REPL_TABLE       VARCHAR(200);  -- 编码替代页面中用到的月累积目标表，与月度分析页面的月累计目标表区分开
  V_FROM_TABLE          VARCHAR(200);
  V_JOIN_TABLE			VARCHAR(200);
  V_YEAR				INT;
  V_RMB_COST_AMT        TEXT;
  V_RMB_REPL_COST_AMT   TEXT;  -- 编码替代页面中用到的
  V_RMB_AVG_AMT         TEXT;
  V_OTHER_DIM_PART      TEXT;
  V_DIMENSION_PART      TEXT;
  V_PBI_PART            TEXT;
  V_PUBLIC_PBI_PART     TEXT;
  V_SQL_PUBLIC_PBI_PART TEXT;
  V_SQL_CONDITION       TEXT;
  V_SQL_DIMENSION_PART  TEXT;
  V_SQL_OTHER_DIM_PART  TEXT;
  V_SQL_PBI_PART        TEXT;
  V_JOIN_OTHER_DIM_PART TEXT;
  V_JOIN_DIMENSION_CODE TEXT;
  V_SQL					TEXT;
  V_RMB_PARA			TEXT;
  V_JOIN_PARA			TEXT;
  
BEGIN

--写入日志
V_EXCEPTION_FLAG	:= 0;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

	V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MID_MON_SPART_ENTIRE_T';
	V_TO_TABLE 	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_YTD_AVG_T';
	V_TO_REPL_TABLE 	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_REPL_MON_YTD_AVG_T';  -- 编码替代页面用到月累计目标表
	V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
	
	
  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
		';

  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')		
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';
					
  V_PUBLIC_PBI_PART:='LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,
                      LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
  '	;
  
  V_SQL_PUBLIC_PBI_PART:='T1.LV0_CODE,T1.LV1_CODE,T1.LV2_CODE,T1.LV3_CODE,T1.LV4_CODE,
                      T1.LV0_CN_NAME,T1.LV1_CN_NAME,T1.LV2_CN_NAME,T1.LV3_CN_NAME,T1.LV4_CN_NAME,
  '	;

  IF F_GRANULARITY_TYPE = 'IRB' THEN
	
    V_PBI_PART := '
      LV0_PROD_RND_TEAM_CODE,
      LV1_PROD_RND_TEAM_CODE,
      LV2_PROD_RND_TEAM_CODE,
      LV3_PROD_RND_TEAM_CODE,
      LV4_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RD_TEAM_CN_NAME,
      LV3_PROD_RD_TEAM_CN_NAME,
      LV4_PROD_RD_TEAM_CN_NAME,
      ';
	  
    V_SQL_PBI_PART  := '
      T1.LV0_PROD_RND_TEAM_CODE,
      T1.LV1_PROD_RND_TEAM_CODE,
      T1.LV2_PROD_RND_TEAM_CODE,
      T1.LV3_PROD_RND_TEAM_CODE,
      T1.LV4_PROD_RND_TEAM_CODE,
      T1.LV0_PROD_RD_TEAM_CN_NAME,
      T1.LV1_PROD_RD_TEAM_CN_NAME,
      T1.LV2_PROD_RD_TEAM_CN_NAME,
      T1.LV3_PROD_RD_TEAM_CN_NAME,
      T1.LV4_PROD_RD_TEAM_CN_NAME,
      ';
		
	 V_JOIN_PARA := ' T1.PERIOD_YEAR = T2.PERIOD_YEAR
					AND T1.LV0_CODE = T2.LV0_PROD_RND_TEAM_CODE
					AND T1.LV1_CODE = T2.LV1_PROD_RND_TEAM_CODE
					AND T1.LV2_CODE = T2.LV2_PROD_RND_TEAM_CODE
					AND T1.LV3_CODE = T2.LV3_PROD_RND_TEAM_CODE
					AND T1.LV4_CODE = T2.LV4_PROD_RND_TEAM_CODE
	 ';
		
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_PBI_PART := '
      LV0_INDUSTRY_CATG_CODE    ,
      LV1_INDUSTRY_CATG_CODE      ,
      LV2_INDUSTRY_CATG_CODE      ,
      LV3_INDUSTRY_CATG_CODE      ,
      LV4_INDUSTRY_CATG_CODE      ,
      LV0_INDUSTRY_CATG_CN_NAME,
      LV1_INDUSTRY_CATG_CN_NAME,
      LV2_INDUSTRY_CATG_CN_NAME,
      LV3_INDUSTRY_CATG_CN_NAME,
      LV4_INDUSTRY_CATG_CN_NAME,
      ';
	

  
    V_SQL_PBI_PART := '
      T1.LV0_INDUSTRY_CATG_CODE,
      T1.LV1_INDUSTRY_CATG_CODE,
      T1.LV2_INDUSTRY_CATG_CODE,
      T1.LV3_INDUSTRY_CATG_CODE,
      T1.LV4_INDUSTRY_CATG_CODE,
      T1.LV0_INDUSTRY_CATG_CN_NAME,
      T1.LV1_INDUSTRY_CATG_CN_NAME,
      T1.LV2_INDUSTRY_CATG_CN_NAME,
      T1.LV3_INDUSTRY_CATG_CN_NAME,
      T1.LV4_INDUSTRY_CATG_CN_NAME,
      ';
	  
	 V_JOIN_PARA  := ' 
					T1.PERIOD_YEAR = T2.PERIOD_YEAR
					AND T1.LV0_CODE = T2.LV0_INDUSTRY_CATG_CODE
					AND T1.LV1_CODE = T2.LV1_INDUSTRY_CATG_CODE
					AND T1.LV2_CODE = T2.LV2_INDUSTRY_CATG_CODE
					AND T1.LV3_CODE = T2.LV3_INDUSTRY_CATG_CODE
					AND T1.LV4_CODE = T2.LV4_INDUSTRY_CATG_CODE
	 ';
	  
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_PBI_PART      := '
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      ';
    V_SQL_PBI_PART  := '
      T1.LV0_PROD_LIST_CODE,
      T1.LV1_PROD_LIST_CODE,
      T1.LV2_PROD_LIST_CODE,
      T1.LV3_PROD_LIST_CODE,
      T1.LV4_PROD_LIST_CODE,
      T1.LV0_PROD_LIST_CN_NAME,
      T1.LV1_PROD_LIST_CN_NAME,
      T1.LV2_PROD_LIST_CN_NAME,
      T1.LV3_PROD_LIST_CN_NAME,
      T1.LV4_PROD_LIST_CN_NAME,
      ';
  
		V_JOIN_PARA := ' T1.PERIOD_YEAR = T2.PERIOD_YEAR
					AND T1.LV0_CODE = T2.LV0_PROD_LIST_CODE
					AND T1.LV1_CODE = T2.LV1_PROD_LIST_CODE
					AND T1.LV2_CODE = T2.LV2_PROD_LIST_CODE
					AND T1.LV3_CODE = T2.LV3_PROD_LIST_CODE
					AND T1.LV4_CODE = T2.LV4_PROD_LIST_CODE
	 ';
	
  END IF;
  
  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_COST_AMT := 'RMB_COST_AMT,';
	V_RMB_AVG_AMT     := 'RMB_AVG_AMT,';
	V_SQL_CONDITION   := '';
	V_RMB_REPL_COST_AMT := 'RMB_COST_AMT,';
	
	V_RMB_PARA := ' RMB_COST_AMT <> 0 ';
	
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_COST_AMT := '
					 TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,
					   '''||F_KEYSTR||''',
					   ''aes128'',
					   ''cbc'',
					   ''sha256'')) AS RMB_COST_AMT,
				  ';
	V_RMB_AVG_AMT     := 'GS_ENCRYPT(RMB_AVG_AMT,'''|| F_KEYSTR ||''',''AES128'',''CBC'',''SHA256'') AS RMB_AVG_AMT,';
	V_SQL_CONDITION   := ' AND ONLY_SPART_FLAG = ''N'' ';
	
	V_RMB_PARA := ' TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''|| F_KEYSTR ||''',''aes128'',''cbc'',''sha256'')) <> 0 ';
	
	V_RMB_REPL_COST_AMT := '
					 (GS_ENCRYPT(RMB_COST_AMT,
					   '''||F_KEYSTR||''',
					   ''aes128'',
					   ''cbc'',
					   ''sha256'')) AS RMB_COST_AMT,
				  ';  -- 加密
	
  END IF;
  
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ANNUAL'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  

 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，目录树：'||F_GRANULARITY_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--解密落表
--建表
RAISE NOTICE '解密落表';
DROP TABLE IF EXISTS DM_BASE_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_AMT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 HW_CONTRACT_NUM	VARCHAR(200),	--合同号
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 STD_COST_AMT			VARCHAR(2000) ,
 RMB_COST_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),	--是否主力编码
 CODE_ATTRIBUTES    VARCHAR(20)  ,
 SOFTWARE_MARK      VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

RAISE NOTICE '源数据解密落表';
--落表
V_SQL:='  
  INSERT INTO DM_BASE_AMT_TEMP
    ('||V_PUBLIC_PBI_PART||'
	 '||V_DIMENSION_PART||'
     PERIOD_YEAR,
     PERIOD_ID,
     SPART_CODE,
     SPART_CN_NAME,
     RMB_COST_AMT,
     ACTUAL_QTY,
	 HW_CONTRACT_NUM,
     '||V_OTHER_DIM_PART||'
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 VIEW_FLAG,
	 SOFTWARE_MARK)
    SELECT'||V_PBI_PART||'
		   '||V_DIMENSION_PART||'
           PERIOD_YEAR,
           PERIOD_ID,
           SPART_CODE,
           SPART_CN_NAME,
           '||V_RMB_COST_AMT||'
           PROD_QTY,
		   HW_CONTRACT_NUM,
           '||V_OTHER_DIM_PART||'
		   MAIN_FLAG,
           CODE_ATTRIBUTES,
		   VIEW_FLAG,
		   SOFTWARE_MARK
      FROM '||V_FROM_TABLE||'
	 WHERE (SPART_CODE <> ''SNULL'' OR VIEW_FLAG = ''DIMENSION'')
	   AND ( (PROD_QTY <> 0 AND '||V_RMB_PARA||' ) )
	  AND PERIOD_YEAR >= TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''YYYY'')-2
	  AND BG_CODE  = '''||F_BG_CODE||'''
	  AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||'''
	  '||V_SQL_CONDITION||'
	  ;
	 
	  ';
	  
--DBMS_OUTPUT.PUT_LINE(V_SQL);

EXECUTE V_SQL;


 --写入日志
 V_EXCEPTION_FLAG := 2;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '原始数据插表完成' ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_FORMULA_SQL_TXT => V_SQL,
  F_ERRBUF => 'SUCCESS'); 


--分年逐月累积s
RAISE NOTICE '累积计算';
DROP TABLE IF EXISTS DM_YTD_AVG_TEMP;
CREATE TEMPORARY TABLE DM_YTD_AVG_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_AVG_AMT			NUMERIC,
 RMB_COST_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),	--是否主力编码
 CODE_ATTRIBUTES    VARCHAR(20)   ,
APPEND_FLAG 		VARCHAR(3),
SOFTWARE_MARK      VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);
	
		V_SQL:='
	INSERT INTO DM_YTD_AVG_TEMP
				 (PERIOD_YEAR,
				  PERIOD_ID,
				  LV0_CODE,
				  LV1_CODE,
				  LV2_CODE,
				  LV3_CODE,
				  LV4_CODE,
				  LV0_CN_NAME,
				  LV1_CN_NAME,
				  LV2_CN_NAME,
				  LV3_CN_NAME,
				  LV4_CN_NAME,
				  '||V_DIMENSION_PART||' 
				  SPART_CODE,
				  SPART_CN_NAME,
				  REGION_CODE,
				  REGION_CN_NAME,
				  REPOFFICE_CODE,
				  REPOFFICE_CN_NAME,
				  BG_CODE,
				  BG_CN_NAME,
				  OVERSEA_FLAG,
				  CODE_ATTRIBUTES,
				  MAIN_FLAG,
				  VIEW_FLAG,
				  RMB_AVG_AMT,
				  ACTUAL_QTY,
				  RMB_COST_AMT,
				  APPEND_FLAG,
				  SOFTWARE_MARK)
		  SELECT T2.PERIOD_YEAR,
				 T2.PERIOD_ID,
				 T2.LV0_CODE,
				 T2.LV1_CODE,
				 T2.LV2_CODE,
				 T2.LV3_CODE,
				 T2.LV4_CODE,
				 T2.LV0_CN_NAME,
				 T2.LV1_CN_NAME,
				 T2.LV2_CN_NAME,
				 T2.LV3_CN_NAME,
				 T2.LV4_CN_NAME,
				 T2.DIMENSION_CODE,
				 T2.DIMENSION_CN_NAME,
				 T2.DIMENSION_SUBCATEGORY_CODE,
				 T2.DIMENSION_SUBCATEGORY_CN_NAME,
				 T2.DIMENSION_SUB_DETAIL_CODE,
				 T2.DIMENSION_SUB_DETAIL_CN_NAME,
				 T2.SPART_CODE,
				 T2.SPART_CN_NAME,
				 T2.REGION_CODE,
				 T2.REGION_CN_NAME,
				 T2.REPOFFICE_CODE,
				 T2.REPOFFICE_CN_NAME,
				 T2.BG_CODE,
				 T2.BG_CN_NAME,
				 T2.OVERSEA_FLAG,
				 T2.CODE_ATTRIBUTES,
				 T2.MAIN_FLAG,
				 T2.VIEW_FLAG,
				 NVL(SUM(T2.BASE_AMT) / NULLIF(SUM(T2.BASE_QTY), 0), 0) AS RMB_AVG_AMT,
				 SUM(T2.BASE_QTY) AS ACTUAL_QTY,
				 SUM(T2.BASE_AMT) AS RMB_COST_AMT,
				 ''N'' AS APPEND_FLAG,
				 SOFTWARE_MARK
			FROM (SELECT T1.PERIOD_YEAR,
						 T1.PERIOD_ID,
						 T1.LV0_CODE,
						 T1.LV1_CODE,
						 T1.LV2_CODE,
						 T1.LV3_CODE,
						 T1.LV4_CODE,
						 T1.LV0_CN_NAME,
						 T1.LV1_CN_NAME,
						 T1.LV2_CN_NAME,
						 T1.LV3_CN_NAME,
						 T1.LV4_CN_NAME,
						 '||V_SQL_DIMENSION_PART||' 
						 T1.SPART_CODE,
						 T1.SPART_CN_NAME,
						 T1.REGION_CODE,
						 T1.REGION_CN_NAME,
						 T1.REPOFFICE_CODE,
						 T1.REPOFFICE_CN_NAME,
						 T1.BG_CODE,
						 T1.BG_CN_NAME,
						 T1.OVERSEA_FLAG,
						 T1.CODE_ATTRIBUTES,
						 T1.MAIN_FLAG,
						 T1.VIEW_FLAG,
						 T1.HW_CONTRACT_NUM,
						 SUM(NVL(T1.RMB_COST_AMT, 0)) OVER(PARTITION BY T1.PERIOD_YEAR, '||V_SQL_PUBLIC_PBI_PART||'
																	NVL(T1.SPART_CODE,''SPART''), NVL(T1.HW_CONTRACT_NUM,''HCN''), 
																	NVL(T1.DIMENSION_CODE,''DC''), NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																	'||V_SQL_OTHER_DIM_PART||'
																	NVL(T1.CODE_ATTRIBUTES,''CA''), NVL(T1.MAIN_FLAG,''MF''), T1.SOFTWARE_MARK,
																	NVL(T1.VIEW_FLAG,''VF'') ORDER BY T1.PERIOD_ID) AS BASE_AMT,
						 SUM(NVL(T1.ACTUAL_QTY, 0)) OVER(PARTITION BY T1.PERIOD_YEAR, '||V_SQL_PUBLIC_PBI_PART||'
																	NVL(T1.SPART_CODE,''SPART''), NVL(T1.HW_CONTRACT_NUM,''HCN''), 
																	NVL(T1.DIMENSION_CODE,''DC''), NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC''),
																	'||V_SQL_OTHER_DIM_PART||'
																	NVL(T1.CODE_ATTRIBUTES,''CA''), NVL(T1.MAIN_FLAG,''MF''), T1.SOFTWARE_MARK,
																	NVL(T1.VIEW_FLAG,''VF'') ORDER BY T1.PERIOD_ID) AS BASE_QTY,
																	SOFTWARE_MARK
					FROM DM_BASE_AMT_TEMP T1) T2
		   WHERE ((T2.BASE_AMT <> 0 OR T2.BASE_QTY <> 0) 
		   --AND ABS(BASE_AMT) > 0.01	--原方案 剔除绝对值0.01
		   AND ABS(BASE_AMT) > 1 	--剔除异常数据 20240806修改新方案
		   AND ABS(BASE_QTY) >= 1) 	--剔除异常数据 20240808新增数量绝对值需大于1
		   GROUP BY T2.PERIOD_YEAR,
					T2.PERIOD_ID,
					T2.LV0_CODE,
					T2.LV1_CODE,
					T2.LV2_CODE,
					T2.LV3_CODE,
					T2.LV4_CODE,
					T2.LV0_CN_NAME,
					T2.LV1_CN_NAME,
					T2.LV2_CN_NAME,
					T2.LV3_CN_NAME,
					T2.LV4_CN_NAME,
					T2.DIMENSION_CODE,
					T2.DIMENSION_CN_NAME,
					T2.DIMENSION_SUBCATEGORY_CODE,
					T2.DIMENSION_SUBCATEGORY_CN_NAME,
					T2.DIMENSION_SUB_DETAIL_CODE,
					T2.DIMENSION_SUB_DETAIL_CN_NAME,
					T2.SPART_CODE,
					T2.SPART_CN_NAME,
					T2.REGION_CODE,
					T2.REGION_CN_NAME,
					T2.REPOFFICE_CODE,
					T2.REPOFFICE_CN_NAME,
					T2.BG_CODE,
					T2.BG_CN_NAME,
					T2.OVERSEA_FLAG,
					T2.CODE_ATTRIBUTES,
					T2.SOFTWARE_MARK,
					T2.MAIN_FLAG,
					T2.VIEW_FLAG
		  HAVING   ((SUM(T2.BASE_AMT) <> 0 OR SUM(T2.BASE_QTY) <> 0) 
		   --AND ABS(SUM(BASE_AMT)) > 0.01	--原方案 剔除绝对值0.01
		   AND ABS(SUM(BASE_AMT)) > 1 	--剔除异常数据 20240806修改新方案
		   AND ABS(SUM(BASE_QTY)) >= 1) 	--剔除异常数据 20240808新增数量绝对值需大于1
					;
					
					 ';
			
			-- DBMS_OUTPUT.PUT_LINE(V_SQL);
			EXECUTE V_SQL;
			
 --写入日志
 V_EXCEPTION_FLAG := 3;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_EXCEPTION_FLAG,
  F_CAL_LOG_DESC => '金额累积完成' ,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 


--不分年逐月累积（编码替代页面用到）
RAISE NOTICE '累积计算';
DROP TABLE IF EXISTS DM_REPL_YTD_AVG_TEMP;
CREATE TEMPORARY TABLE DM_REPL_YTD_AVG_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_COST_AMT		NUMERIC,
 ACTUAL_QTY			NUMERIC,
 RMB_AVG_AMT        NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),	--是否主力编码
 CODE_ATTRIBUTES    VARCHAR(20)   ,
APPEND_FLAG 		VARCHAR(3),
SOFTWARE_MARK		VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

RAISE NOTICE'1111111111';
	
		V_SQL:='
	INSERT INTO DM_REPL_YTD_AVG_TEMP
				 (PERIOD_YEAR,
				  PERIOD_ID,
				  LV0_CODE,
				  LV1_CODE,
				  LV2_CODE,
				  LV3_CODE,
				  LV4_CODE,
				  LV0_CN_NAME,
				  LV1_CN_NAME,
				  LV2_CN_NAME,
				  LV3_CN_NAME,
				  LV4_CN_NAME,
				  '||V_DIMENSION_PART||' 
				  SPART_CODE,
				  SPART_CN_NAME,
				  REGION_CODE,
				  REGION_CN_NAME,
				  REPOFFICE_CODE,
				  REPOFFICE_CN_NAME,
				  BG_CODE,
				  BG_CN_NAME,
				  OVERSEA_FLAG,
				  CODE_ATTRIBUTES,
				  MAIN_FLAG,
				  VIEW_FLAG,
				  ACTUAL_QTY,
				  RMB_COST_AMT,
				  RMB_AVG_AMT,
				  APPEND_FLAG,
				  SOFTWARE_MARK)
		  WITH DM_REPL_YTD_AVG_TEMP2 AS(
		  SELECT T1.PERIOD_YEAR,
						 T1.PERIOD_ID,
						 T1.LV0_CODE,
						 T1.LV1_CODE,
						 T1.LV2_CODE,
						 T1.LV3_CODE,
						 T1.LV4_CODE,
						 T1.LV0_CN_NAME,
						 T1.LV1_CN_NAME,
						 T1.LV2_CN_NAME,
						 T1.LV3_CN_NAME,
						 T1.LV4_CN_NAME,
						 '||V_SQL_DIMENSION_PART||' 
						 T1.SPART_CODE,
						 T1.SPART_CN_NAME,
						 T1.REGION_CODE,
						 T1.REGION_CN_NAME,
						 T1.REPOFFICE_CODE,
						 T1.REPOFFICE_CN_NAME,
						 T1.BG_CODE,
						 T1.BG_CN_NAME,
						 T1.OVERSEA_FLAG,
						 T1.CODE_ATTRIBUTES,
						 T1.MAIN_FLAG,
						 T1.VIEW_FLAG,
						  T1.HW_CONTRACT_NUM,
						 SUM(NVL(T1.RMB_COST_AMT, 0)) OVER(PARTITION BY '||V_SQL_PUBLIC_PBI_PART||'
																	NVL(T1.SPART_CODE,''SPART''), NVL(T1.HW_CONTRACT_NUM,''HCN''), 
																	NVL(T1.DIMENSION_CODE,''DC''), NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																	'||V_SQL_OTHER_DIM_PART||'
																	NVL(T1.CODE_ATTRIBUTES,''CA''), NVL(T1.MAIN_FLAG,''MF''), T1.SOFTWARE_MARK,
																	NVL(T1.VIEW_FLAG,''VF'') ORDER BY T1.PERIOD_YEAR, T1.PERIOD_ID) AS BASE_AMT,
						 SUM(NVL(T1.ACTUAL_QTY, 0)) OVER(PARTITION BY '||V_SQL_PUBLIC_PBI_PART||'
																	NVL(T1.SPART_CODE,''SPART''), NVL(T1.HW_CONTRACT_NUM,''HCN''), 
																	NVL(T1.DIMENSION_CODE,''DC''), NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC''),
																	'||V_SQL_OTHER_DIM_PART||'
																	NVL(T1.CODE_ATTRIBUTES,''CA''), NVL(T1.MAIN_FLAG,''MF''), T1.SOFTWARE_MARK,
																	NVL(T1.VIEW_FLAG,''VF'') ORDER BY T1.PERIOD_YEAR, T1.PERIOD_ID) AS BASE_QTY,
																	SOFTWARE_MARK
					FROM DM_BASE_AMT_TEMP T1
		  ),
		  DM_REPL_YTD_AVG_TEMP3 AS(
		  SELECT T2.PERIOD_YEAR,
				 T2.PERIOD_ID,
				 T2.LV0_CODE,
				 T2.LV1_CODE,
				 T2.LV2_CODE,
				 T2.LV3_CODE,
				 T2.LV4_CODE,
				 T2.LV0_CN_NAME,
				 T2.LV1_CN_NAME,
				 T2.LV2_CN_NAME,
				 T2.LV3_CN_NAME,
				 T2.LV4_CN_NAME,
				 T2.DIMENSION_CODE,
				 T2.DIMENSION_CN_NAME,
				 T2.DIMENSION_SUBCATEGORY_CODE,
				 T2.DIMENSION_SUBCATEGORY_CN_NAME,
				 T2.DIMENSION_SUB_DETAIL_CODE,
				 T2.DIMENSION_SUB_DETAIL_CN_NAME,
				 T2.SPART_CODE,
				 T2.SPART_CN_NAME,
				 T2.REGION_CODE,
				 T2.REGION_CN_NAME,
				 T2.REPOFFICE_CODE,
				 T2.REPOFFICE_CN_NAME,
				 T2.BG_CODE,
				 T2.BG_CN_NAME,
				 T2.OVERSEA_FLAG,
				 T2.CODE_ATTRIBUTES,
				 T2.MAIN_FLAG,
				 T2.VIEW_FLAG,
				 SUM(T2.BASE_QTY) AS ACTUAL_QTY,
				 SUM(T2.BASE_AMT) AS RMB_COST_AMT,
				 NVL(SUM(T2.BASE_AMT) / NULLIF(SUM(T2.BASE_QTY), 0), 0) AS RMB_AVG_AMT,
				 T2.SOFTWARE_MARK
			FROM DM_REPL_YTD_AVG_TEMP2 T2
	   WHERE ((T2.BASE_AMT <> 0 OR T2.BASE_QTY <> 0) 
		   --AND ABS(BASE_AMT) > 0.01	--原方案 剔除绝对值0.01
		   AND ABS(BASE_AMT) > 1 	--剔除异常数据 20240806修改新方案
		   AND ABS(BASE_QTY) >= 1) 	--剔除异常数据 20240808新增数量绝对值需大于1
		 GROUP BY T2.PERIOD_YEAR,
				 T2.PERIOD_ID,
				 T2.LV0_CODE,
				 T2.LV1_CODE,
				 T2.LV2_CODE,
				 T2.LV3_CODE,
				 T2.LV4_CODE,
				 T2.LV0_CN_NAME,
				 T2.LV1_CN_NAME,
				 T2.LV2_CN_NAME,
				 T2.LV3_CN_NAME,
				 T2.LV4_CN_NAME,
				 T2.DIMENSION_CODE,
				 T2.DIMENSION_CN_NAME,
				 T2.DIMENSION_SUBCATEGORY_CODE,
				 T2.DIMENSION_SUBCATEGORY_CN_NAME,
				 T2.DIMENSION_SUB_DETAIL_CODE,
				 T2.DIMENSION_SUB_DETAIL_CN_NAME,
				 T2.SPART_CODE,
				 T2.SPART_CN_NAME,
				 T2.REGION_CODE,
				 T2.REGION_CN_NAME,
				 T2.REPOFFICE_CODE,
				 T2.REPOFFICE_CN_NAME,
				 T2.BG_CODE,
				 T2.BG_CN_NAME,
				 T2.OVERSEA_FLAG,
				 T2.CODE_ATTRIBUTES,
				 T2.MAIN_FLAG,
				 T2.VIEW_FLAG,
				 T2.SOFTWARE_MARK
		HAVING   ((SUM(T2.BASE_AMT) <> 0 OR SUM(T2.BASE_QTY) <> 0) 
		   --AND ABS(SUM(BASE_AMT)) > 0.01	--原方案 剔除绝对值0.01
		   AND ABS(SUM(BASE_AMT)) > 1 	--剔除异常数据 20240806修改新方案
		   AND ABS(SUM(BASE_QTY)) >= 1) 	--剔除异常数据 20240808新增数量绝对值需大于1		 
				 
			)
			SELECT PERIOD_YEAR,
				 PERIOD_ID,
				 LV0_CODE,
				 LV1_CODE,
				 LV2_CODE,
				 LV3_CODE,
				 LV4_CODE,
				 LV0_CN_NAME,
				 LV1_CN_NAME,
				 LV2_CN_NAME,
				 LV3_CN_NAME,
				 LV4_CN_NAME,
				 DIMENSION_CODE,
				 DIMENSION_CN_NAME,
				 DIMENSION_SUBCATEGORY_CODE,
				 DIMENSION_SUBCATEGORY_CN_NAME,
				 DIMENSION_SUB_DETAIL_CODE,
				 DIMENSION_SUB_DETAIL_CN_NAME,
				 SPART_CODE,
				 SPART_CN_NAME,
				 REGION_CODE,
				 REGION_CN_NAME,
				 REPOFFICE_CODE,
				 REPOFFICE_CN_NAME,
				 BG_CODE,
				 BG_CN_NAME,
				 OVERSEA_FLAG,
				 CODE_ATTRIBUTES,
				 MAIN_FLAG,
				 VIEW_FLAG,
				 ACTUAL_QTY,
				 RMB_COST_AMT,
				 RMB_AVG_AMT,
				 ''N'' AS APPEND_FLAG,
				 SOFTWARE_MARK
			  FROM DM_REPL_YTD_AVG_TEMP3

			;
					
		 ';
			
			--DBMS_OUTPUT.PUT_LINE(V_SQL);
			EXECUTE V_SQL;
			
			RAISE NOTICE'222222222';
			
 --写入日志
 V_EXCEPTION_FLAG := 4;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_EXCEPTION_FLAG,
  F_CAL_LOG_DESC => '编码替代的月累计金额完成' ,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 


--补齐
DROP TABLE IF EXISTS DM_APPEND_AVG_TEMP;
CREATE TEMPORARY TABLE DM_APPEND_AVG_TEMP(
LV0_PROD_RND_TEAM_CODE VARCHAR(50),
LV1_PROD_RND_TEAM_CODE VARCHAR(50),
LV2_PROD_RND_TEAM_CODE VARCHAR(50),
LV3_PROD_RND_TEAM_CODE VARCHAR(50),
LV4_PROD_RND_TEAM_CODE VARCHAR(50),
LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV0_INDUSTRY_CATG_CODE   VARCHAR(50) ,
LV1_INDUSTRY_CATG_CODE   VARCHAR(50)   ,
LV2_INDUSTRY_CATG_CODE    VARCHAR(50)  ,
LV3_INDUSTRY_CATG_CODE    VARCHAR(50)  ,
LV4_INDUSTRY_CATG_CODE     VARCHAR(50) ,
LV0_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV1_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV2_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV3_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV4_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV0_PROD_LIST_CODE VARCHAR(50),
LV1_PROD_LIST_CODE VARCHAR(50),
LV2_PROD_LIST_CODE VARCHAR(50),
LV3_PROD_LIST_CODE VARCHAR(50),
LV4_PROD_LIST_CODE VARCHAR(50),
LV0_PROD_LIST_CN_NAME VARCHAR(200),
LV1_PROD_LIST_CN_NAME VARCHAR(200),
LV2_PROD_LIST_CN_NAME VARCHAR(200),
LV3_PROD_LIST_CN_NAME VARCHAR(200),
LV4_PROD_LIST_CN_NAME VARCHAR(200),
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_AVG_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),	--是否主力编码
 CODE_ATTRIBUTES    VARCHAR(20),
APPEND_FLAG		 VARCHAR(5),
SOFTWARE_MARK	    VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

V_SQL:='
	WITH ACTUAL_SPART_DIM_TEMP AS
	 (
	  --取全量PBI及SPART层级维度
	  SELECT DISTINCT '||V_PUBLIC_PBI_PART||' 
					  '||V_DIMENSION_PART||' 
					  SPART_CODE,
					  SPART_CN_NAME,
					  '||V_OTHER_DIM_PART||' 
					  CODE_ATTRIBUTES,
					  MAIN_FLAG,
					  VIEW_FLAG,
					  SOFTWARE_MARK
		FROM DM_YTD_AVG_TEMP),

	PERIOD_DIM_TEMP AS
	 (
	  --生成连续月份, 一年前首月至当前系统实际月, (当前系统实际月 = 当前系统月-1)
	  SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''', NUM.VAL - 1),
						  ''YYYYMM'') AS BIGINT) AS PERIOD_ID
		FROM GENERATE_SERIES(1,
							  TO_NUMBER(TIMESTAMPDIFF(MONTH,
													 '''||V_BEGIN_DATE||''',
													  CURRENT_TIMESTAMP)),
							  1) NUM(VAL)),

	CROSS_JOIN_TEMP AS
	 (
	  --生成连续年月的发散维
	  SELECT CAST(SUBSTR(T2.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
			  T2.PERIOD_ID,
			  '||V_SQL_PUBLIC_PBI_PART||' 
			  '||V_SQL_DIMENSION_PART||' 
			  T1.SPART_CODE,
			  T1.SPART_CN_NAME,
			  '||V_SQL_OTHER_DIM_PART||' 
			  T1.CODE_ATTRIBUTES,
			  T1.MAIN_FLAG,
			  T1.VIEW_FLAG,
			  SOFTWARE_MARK
		FROM ACTUAL_SPART_DIM_TEMP T1, PERIOD_DIM_TEMP T2),

	FORWARD_FILLER_TEMP AS
	 (
	  --按照所有维度，向前寻找会计期补齐均价
	  SELECT SS.LV0_CODE,
			  SS.LV1_CODE,
			  SS.LV2_CODE,
			  SS.LV3_CODE,
			  SS.LV4_CODE,
			  SS.LV0_CN_NAME,
			  SS.LV1_CN_NAME,
			  SS.LV2_CN_NAME,
			  SS.LV3_CN_NAME,
			  SS.LV4_CN_NAME,
			  SS.DIMENSION_CODE,
			  SS.DIMENSION_CN_NAME,
			  SS.DIMENSION_SUBCATEGORY_CODE,
			  SS.DIMENSION_SUBCATEGORY_CN_NAME,
			  SS.DIMENSION_SUB_DETAIL_CODE,
			  SS.DIMENSION_SUB_DETAIL_CN_NAME,
			  SS.SPART_CODE,
			  SS.SPART_CN_NAME,
			  SS.VIEW_FLAG,
			  SS.CODE_ATTRIBUTES,
			  SS.SOFTWARE_MARK,
			  SS.MAIN_FLAG,
			  SS.REGION_CODE,
			  SS.REGION_CN_NAME,
			  SS.REPOFFICE_CODE,
			  SS.REPOFFICE_CN_NAME,
			  SS.BG_CODE,
			  SS.BG_CN_NAME,
			  SS.OVERSEA_FLAG,
			  SS.PERIOD_YEAR,
			  SS.PERIOD_ID,
			  SS.RMB_AVG_AMT,
			  FIRST_VALUE(SS.RMB_AVG_AMT) OVER(PARTITION BY SS.PERIOD_YEAR, SS.LV0_CODE, SS.LV1_CODE, SS.LV2_CODE, SS.LV3_CODE, SS.LV4_CODE, NVL(SS.SPART_CODE,''SPART''), NVL(SS.DIMENSION_CODE,''DC''), NVL(SS.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(SS.DIMENSION_SUB_DETAIL_CODE,''DSDC''), SS.REGION_CODE, SS.REPOFFICE_CODE, SS.BG_CODE, SS.OVERSEA_FLAG, NVL(SS.CODE_ATTRIBUTES,''CA''), NVL(SS.MAIN_FLAG,''MF''),NVL(SS.SOFTWARE_MARK,''SW''), NVL(SS.VIEW_FLAG,''VF''), SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS RMB_AVG_AMT_2, --新补齐的均价字段
			  FIRST_VALUE(SS.ACTUAL_QTY) OVER(PARTITION BY SS.PERIOD_YEAR, SS.LV0_CODE, SS.LV1_CODE, SS.LV2_CODE, SS.LV3_CODE, SS.LV4_CODE, NVL(SS.SPART_CODE,''SPART''), NVL(SS.DIMENSION_CODE,''DC''), NVL(SS.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(SS.DIMENSION_SUB_DETAIL_CODE,''DSDC''), SS.REGION_CODE, SS.REPOFFICE_CODE, SS.BG_CODE, SS.OVERSEA_FLAG, NVL(SS.CODE_ATTRIBUTES,''CA''), NVL(SS.MAIN_FLAG,''MF''),NVL(SS.SOFTWARE_MARK,''SW''), NVL(SS.VIEW_FLAG,''VF''), SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS ACTUAL_QTY_2, --新补齐的均价字段
			  SS.AVG_AMT_FLAG,
			  SS.APD_FLAG
		FROM (SELECT '||V_SQL_PUBLIC_PBI_PART||' 
					 '||V_SQL_DIMENSION_PART||' 
					 T1.SPART_CODE,
					  T1.SPART_CN_NAME,
					  '||V_SQL_OTHER_DIM_PART||' 
					  T1.CODE_ATTRIBUTES,
					  T1.MAIN_FLAG,
					  T1.VIEW_FLAG,
					  T1.PERIOD_YEAR,
					  T1.PERIOD_ID,
					  T1.RMB_AVG_AMT,
					  T1.ACTUAL_QTY,
					  SUM(T1.NULL_FLAG) OVER(PARTITION BY T1.PERIOD_YEAR, '||V_SQL_PUBLIC_PBI_PART||' NVL(T1.SPART_CODE,''SPART''), NVL(T1.DIMENSION_CODE,''DC''), NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC''), '||V_SQL_OTHER_DIM_PART||' NVL(T1.CODE_ATTRIBUTES,''CA''), NVL(T1.MAIN_FLAG,''MF''), NVL(T1.SOFTWARE_MARK,''SW''),NVL(T1.VIEW_FLAG,''VF'') ORDER BY T1.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
					  T1.APD_FLAG,
					  SOFTWARE_MARK
				 FROM (SELECT '||V_SQL_PUBLIC_PBI_PART||' 
							'||V_SQL_DIMENSION_PART||' 
							T1.SPART_CODE,
							  T1.SPART_CN_NAME,
							  '||V_SQL_OTHER_DIM_PART||' 
							  T1.CODE_ATTRIBUTES,
							  T1.SOFTWARE_MARK,
							  T1.MAIN_FLAG,
							  T1.VIEW_FLAG,
							  T1.PERIOD_YEAR,
							  T1.PERIOD_ID,
							  T2.RMB_AVG_AMT,
							  T2.ACTUAL_QTY,
							  DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
							  NVL(T2.APPEND_FLAG, ''Y'') AS APD_FLAG --补齐标识：Y为补齐，N为原始存在
						 FROM CROSS_JOIN_TEMP T1
						 LEFT JOIN DM_YTD_AVG_TEMP T2
						   ON T1.PERIOD_ID = T2.PERIOD_ID
						  AND T1.LV4_CODE = T2.LV4_CODE
						  AND NVL(T1.MAIN_FLAG,''MF'') =
							  NVL(T2.MAIN_FLAG,''MF'')
						  AND NVL(T1.CODE_ATTRIBUTES,''CA'') =
							  NVL(T2.CODE_ATTRIBUTES,''CA'')
						  AND NVL(T1.SOFTWARE_MARK,''SW'') =
							  NVL(T2.SOFTWARE_MARK,''SW'')
						'||V_JOIN_DIMENSION_CODE||'
						  AND NVL(T1.SPART_CODE,''SC'') =
							  NVL(T2.SPART_CODE,''SC'')
						'||V_JOIN_OTHER_DIM_PART||'
						  AND T1.VIEW_FLAG = T2.VIEW_FLAG) T1) SS)

	INSERT INTO DM_APPEND_AVG_TEMP
	  ('||V_PBI_PART||'
	   DIMENSION_CODE,
	   DIMENSION_CN_NAME,
	   DIMENSION_SUBCATEGORY_CODE,
	   DIMENSION_SUBCATEGORY_CN_NAME,
	   DIMENSION_SUB_DETAIL_CODE,
	   DIMENSION_SUB_DETAIL_CN_NAME,
	   VIEW_FLAG,
	   CODE_ATTRIBUTES,
	   MAIN_FLAG,
	   SPART_CODE,
	   SPART_CN_NAME,
	   REGION_CODE,
	   REGION_CN_NAME,
	   REPOFFICE_CODE,
	   REPOFFICE_CN_NAME,
	   BG_CODE,
	   BG_CN_NAME,
	   OVERSEA_FLAG,
	   PERIOD_YEAR,
	   PERIOD_ID,
	   RMB_AVG_AMT,
	   ACTUAL_QTY,
	   APPEND_FLAG,
	   SOFTWARE_MARK)
	--向后补齐均价
	SELECT S.LV0_CODE,
		   S.LV1_CODE,
		   S.LV2_CODE,
		   S.LV3_CODE,
		   S.LV4_CODE,
		   S.LV0_CN_NAME,
		   S.LV1_CN_NAME,
		   S.LV2_CN_NAME,
		   S.LV3_CN_NAME,
		   S.LV4_CN_NAME,
		   S.DIMENSION_CODE,
		   S.DIMENSION_CN_NAME,
		   S.DIMENSION_SUBCATEGORY_CODE,
		   S.DIMENSION_SUBCATEGORY_CN_NAME,
		   S.DIMENSION_SUB_DETAIL_CODE,
		   S.DIMENSION_SUB_DETAIL_CN_NAME,
		   S.VIEW_FLAG,
		   S.CODE_ATTRIBUTES,
		   S.MAIN_FLAG,
		   S.SPART_CODE,
		   S.SPART_CN_NAME,
		   S.REGION_CODE,
		   S.REGION_CN_NAME,
		   S.REPOFFICE_CODE,
		   S.REPOFFICE_CN_NAME,
		   S.BG_CODE,
		   S.BG_CN_NAME,
		   S.OVERSEA_FLAG,
		   S.PERIOD_YEAR,
		   S.PERIOD_ID,
		   NVL(S.RMB_AVG_AMT_2, S.RMB_AVG_AMT_3) AS RMB_AVG_AMT,
		   NVL(S.ACTUAL_QTY_2, S.ACTUAL_QTY_3) AS ACTUAL_QTY,
		   S.APD_FLAG AS APPEND_FLAG,
		   S.SOFTWARE_MARK
	  FROM (SELECT '||V_SQL_PUBLIC_PBI_PART||'
					'||V_SQL_DIMENSION_PART||' 
					T1.SPART_CODE,
				   T1.SPART_CN_NAME,
				   '||V_SQL_OTHER_DIM_PART||' 
				   T1.CODE_ATTRIBUTES,
				   T1.MAIN_FLAG,
				   T1.VIEW_FLAG,
				   T1.PERIOD_YEAR,
				   T1.PERIOD_ID,
				   T1.RMB_AVG_AMT_2,
				   T2.RMB_AVG_AMT_3,
				   T1.ACTUAL_QTY_2,
				   T2.ACTUAL_QTY_3,
				   T1.APD_FLAG,
				   T1.SOFTWARE_MARK
			  FROM FORWARD_FILLER_TEMP T1
			  LEFT JOIN (SELECT DISTINCT S.LV4_CODE,
			                             S.DIMENSION_CODE,
			                             S.DIMENSION_SUBCATEGORY_CODE,
			                             S.DIMENSION_SUB_DETAIL_CODE,
			                             S.SPART_CODE,
			                             S.REGION_CODE,
			                             S.REPOFFICE_CODE,
			                             S.BG_CODE,
			                             S.OVERSEA_FLAG,
										 S.VIEW_FLAG,
										 S.MAIN_FLAG,
										 S.CODE_ATTRIBUTES,
										 S.PERIOD_YEAR,
										FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY S.PERIOD_YEAR, 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''), 
																				NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''),
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG, S.SOFTWARE_MARK, 
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
										FIRST_VALUE(S.RMB_AVG_AMT_2) OVER(PARTITION BY S.PERIOD_YEAR, 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''),
																				NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG,S.SOFTWARE_MARK,
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS RMB_AVG_AMT_3, --有量的首条补齐量
										FIRST_VALUE(S.ACTUAL_QTY_2) OVER(PARTITION BY S.PERIOD_YEAR, 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''), NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG, S.SOFTWARE_MARK,
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS ACTUAL_QTY_3 ,--有均价的首条补齐均价
																				SOFTWARE_MARK
						  FROM FORWARD_FILLER_TEMP S
						 WHERE S.AVG_AMT_FLAG > 0) T2
				ON T1.LV4_CODE = T2.LV4_CODE
			   AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
			   AND NVL(T1.CODE_ATTRIBUTES,''CA'') =
				   NVL(T2.CODE_ATTRIBUTES,''CA'')
			   AND NVL(T1.SOFTWARE_MARK,''SW'') =
				   NVL(T2.SOFTWARE_MARK,''SW'')
			 '||V_JOIN_DIMENSION_CODE||'
			   AND NVL(T1.SPART_CODE,''SC'') = NVL(T2.SPART_CODE,''SC'')
			 '||V_JOIN_OTHER_DIM_PART||'
			   AND T1.VIEW_FLAG = T2.VIEW_FLAG
			   AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
			   AND T1.PERIOD_ID < T2.PERIOD_ID) S;

';

 --DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

 V_EXCEPTION_FLAG := 5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_EXCEPTION_FLAG,
  F_CAL_LOG_DESC =>'数据补齐完成' ,
  F_RESULT_STATUS => X_RESULT_STATUS,
   F_FORMULA_SQL_TXT => V_SQL,
  F_ERRBUF => 'SUCCESS');

raise notice'0000000000000000000';


--加密落表
RAISE NOTICE '加密落表';
V_SQL:='
DELETE FROM  '||V_TO_TABLE||'  WHERE BG_CODE = '''||F_BG_CODE||''' AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||''' ;

  INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_PBI_PART||' 
	 '||V_DIMENSION_PART||' 
	 SPART_CODE,
     SPART_CN_NAME,
     '||V_OTHER_DIM_PART||' 
	 CODE_ATTRIBUTES,
	 SOFTWARE_MARK,
     MAIN_FLAG,
     VIEW_FLAG,
     RMB_AVG_AMT,
     ACTUAL_QTY,
	 APPEND_FLAG,
	 CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           '||V_PBI_PART||' 
		   '||V_DIMENSION_PART||' 
		   SPART_CODE,
           SPART_CN_NAME,
           '||V_OTHER_DIM_PART||' 
		   CODE_ATTRIBUTES,
		   SOFTWARE_MARK,
           MAIN_FLAG,
           VIEW_FLAG,
           '||V_RMB_AVG_AMT||'
           ACTUAL_QTY,
		   APPEND_FLAG,
		   -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_APPEND_AVG_TEMP;';
      
-- DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

 V_EXCEPTION_FLAG := 7;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_EXCEPTION_FLAG,
  F_CAL_LOG_DESC =>'数据插表完成' ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
  F_ERRBUF => 'SUCCESS');

  V_SQL:= 'ANALYZE  '||V_TO_TABLE;
  EXECUTE V_SQL;
  
  raise notice'333333333333333'; 

 
  --编码替代页面的补齐
DROP TABLE IF EXISTS DM_REPL_APPEND_AVG_TEMP;
CREATE TEMPORARY TABLE DM_REPL_APPEND_AVG_TEMP(
LV0_PROD_RND_TEAM_CODE VARCHAR(50),
LV1_PROD_RND_TEAM_CODE VARCHAR(50),
LV2_PROD_RND_TEAM_CODE VARCHAR(50),
LV3_PROD_RND_TEAM_CODE VARCHAR(50),
LV4_PROD_RND_TEAM_CODE VARCHAR(50),
LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200),
LV0_INDUSTRY_CATG_CODE   VARCHAR(50) ,
LV1_INDUSTRY_CATG_CODE   VARCHAR(50)   ,
LV2_INDUSTRY_CATG_CODE    VARCHAR(50)  ,
LV3_INDUSTRY_CATG_CODE    VARCHAR(50)  ,
LV4_INDUSTRY_CATG_CODE     VARCHAR(50) ,
LV0_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV1_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV2_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV3_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV4_INDUSTRY_CATG_CN_NAME VARCHAR(200),
LV0_PROD_LIST_CODE VARCHAR(50),
LV1_PROD_LIST_CODE VARCHAR(50),
LV2_PROD_LIST_CODE VARCHAR(50),
LV3_PROD_LIST_CODE VARCHAR(50),
LV4_PROD_LIST_CODE VARCHAR(50),
LV0_PROD_LIST_CN_NAME VARCHAR(200),
LV1_PROD_LIST_CN_NAME VARCHAR(200),
LV2_PROD_LIST_CN_NAME VARCHAR(200),
LV3_PROD_LIST_CN_NAME VARCHAR(200),
LV4_PROD_LIST_CN_NAME VARCHAR(200),
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_AVG_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 RMB_COST_AMT			NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),	--是否主力编码
 CODE_ATTRIBUTES    VARCHAR(20),
 APPEND_FLAG        VARCHAR(10),
 SOFTWARE_MARK		VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

raise notice'1111111111111111';


V_SQL:='
 INSERT INTO DM_REPL_APPEND_AVG_TEMP
	  ('||V_PBI_PART||'
	   DIMENSION_CODE,
	   DIMENSION_CN_NAME,
	   DIMENSION_SUBCATEGORY_CODE,
	   DIMENSION_SUBCATEGORY_CN_NAME,
	   DIMENSION_SUB_DETAIL_CODE,
	   DIMENSION_SUB_DETAIL_CN_NAME,
	   VIEW_FLAG,
	   CODE_ATTRIBUTES,
	   MAIN_FLAG,
	   SPART_CODE,
	   SPART_CN_NAME,
	   REGION_CODE,
	   REGION_CN_NAME,
	   REPOFFICE_CODE,
	   REPOFFICE_CN_NAME,
	   BG_CODE,
	   BG_CN_NAME,
	   OVERSEA_FLAG,
	   PERIOD_YEAR,
	   PERIOD_ID,
	   RMB_AVG_AMT,
	   ACTUAL_QTY,
	   RMB_COST_AMT,
	   APPEND_FLAG,
	   SOFTWARE_MARK
	)
	WITH ACTUAL_SPART_DIM_TEMP AS(
	  --取全量PBI及SPART层级维度
	  SELECT DISTINCT '||V_PUBLIC_PBI_PART||' 
					  '||V_DIMENSION_PART||' 
					  SPART_CODE,
					  SPART_CN_NAME,
					  '||V_OTHER_DIM_PART||' 
					  CODE_ATTRIBUTES,
					  MAIN_FLAG,
					  VIEW_FLAG,
					  SOFTWARE_MARK
		FROM DM_REPL_YTD_AVG_TEMP
	),
		PERIOD_DIM_TEMP AS
	 (
	  --生成连续月份, 一年前首月至当前系统实际月, (当前系统实际月 = 当前系统月-1)
	  SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''', NUM.VAL - 1),
						  ''YYYYMM'') AS BIGINT) AS PERIOD_ID
		FROM GENERATE_SERIES(1,
							  TO_NUMBER(TIMESTAMPDIFF(MONTH,
													 '''||V_BEGIN_DATE||''',
													  CURRENT_TIMESTAMP)),
							  1) NUM(VAL)),

	CROSS_JOIN_TEMP AS
	 (
	  --生成连续年月的发散维
	  SELECT CAST(SUBSTR(T2.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
			  T2.PERIOD_ID,
			  '||V_SQL_PUBLIC_PBI_PART||' 
			  '||V_SQL_DIMENSION_PART||' 
			  T1.SPART_CODE,
			  T1.SPART_CN_NAME,
			  '||V_SQL_OTHER_DIM_PART||' 
			  T1.CODE_ATTRIBUTES,
			  T1.MAIN_FLAG,
			  T1.VIEW_FLAG,
			  SOFTWARE_MARK
		FROM ACTUAL_SPART_DIM_TEMP T1, PERIOD_DIM_TEMP T2),

	FORWARD_FILLER_TEMP AS
	 (
	  --按照所有维度，向前寻找会计期补齐均价
	  SELECT SS.LV0_CODE,
			  SS.LV1_CODE,
			  SS.LV2_CODE,
			  SS.LV3_CODE,
			  SS.LV4_CODE,
			  SS.LV0_CN_NAME,
			  SS.LV1_CN_NAME,
			  SS.LV2_CN_NAME,
			  SS.LV3_CN_NAME,
			  SS.LV4_CN_NAME,
			  SS.DIMENSION_CODE,
			  SS.DIMENSION_CN_NAME,
			  SS.DIMENSION_SUBCATEGORY_CODE,
			  SS.DIMENSION_SUBCATEGORY_CN_NAME,
			  SS.DIMENSION_SUB_DETAIL_CODE,
			  SS.DIMENSION_SUB_DETAIL_CN_NAME,
			  SS.SPART_CODE,
			  SS.SPART_CN_NAME,
			  SS.VIEW_FLAG,
			  SS.CODE_ATTRIBUTES,
			  SS.SOFTWARE_MARK,
			  SS.MAIN_FLAG,
			  SS.REGION_CODE,
			  SS.REGION_CN_NAME,
			  SS.REPOFFICE_CODE,
			  SS.REPOFFICE_CN_NAME,
			  SS.BG_CODE,
			  SS.BG_CN_NAME,
			  SS.OVERSEA_FLAG,
			  SS.PERIOD_YEAR,
			  SS.PERIOD_ID,
			  SS.RMB_AVG_AMT,
			  SS.RMB_COST_AMT,
			  FIRST_VALUE(SS.RMB_COST_AMT) OVER(PARTITION BY  SS.LV0_CODE, SS.LV1_CODE, SS.LV2_CODE, SS.LV3_CODE, SS.LV4_CODE, NVL(SS.SPART_CODE,''SPART''), NVL(SS.DIMENSION_CODE,''DC''), NVL(SS.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(SS.DIMENSION_SUB_DETAIL_CODE,''DSDC''), SS.REGION_CODE, SS.REPOFFICE_CODE, SS.BG_CODE, SS.OVERSEA_FLAG, NVL(SS.CODE_ATTRIBUTES,''CA''), NVL(SS.MAIN_FLAG,''MF''),NVL(SS.SOFTWARE_MARK,''SW''), NVL(SS.VIEW_FLAG,''VF''), SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS RMB_COST_AMT_2, --新补齐的均价字段
			  FIRST_VALUE(SS.RMB_AVG_AMT) OVER(PARTITION BY  SS.LV0_CODE, SS.LV1_CODE, SS.LV2_CODE, SS.LV3_CODE, SS.LV4_CODE, NVL(SS.SPART_CODE,''SPART''), NVL(SS.DIMENSION_CODE,''DC''), NVL(SS.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(SS.DIMENSION_SUB_DETAIL_CODE,''DSDC''), SS.REGION_CODE, SS.REPOFFICE_CODE, SS.BG_CODE, SS.OVERSEA_FLAG, NVL(SS.CODE_ATTRIBUTES,''CA''), NVL(SS.MAIN_FLAG,''MF''),NVL(SS.SOFTWARE_MARK,''SW''), NVL(SS.VIEW_FLAG,''VF''), SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS RMB_AVG_AMT_2, --新补齐的均价字段
			  FIRST_VALUE(SS.ACTUAL_QTY) OVER(PARTITION BY  SS.LV0_CODE, SS.LV1_CODE, SS.LV2_CODE, SS.LV3_CODE, SS.LV4_CODE, NVL(SS.SPART_CODE,''SPART''), NVL(SS.DIMENSION_CODE,''DC''), NVL(SS.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(SS.DIMENSION_SUB_DETAIL_CODE,''DSDC''), SS.REGION_CODE, SS.REPOFFICE_CODE, SS.BG_CODE, SS.OVERSEA_FLAG, NVL(SS.CODE_ATTRIBUTES,''CA''), NVL(SS.MAIN_FLAG,''MF''),NVL(SS.SOFTWARE_MARK,''SW''), NVL(SS.VIEW_FLAG,''VF''), SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS ACTUAL_QTY_2, --新补齐的均价字段
			  SS.AVG_AMT_FLAG,
			  SS.APD_FLAG
		FROM (SELECT '||V_SQL_PUBLIC_PBI_PART||' 
					 '||V_SQL_DIMENSION_PART||' 
					 T1.SPART_CODE,
					  T1.SPART_CN_NAME,
					  '||V_SQL_OTHER_DIM_PART||' 
					  T1.CODE_ATTRIBUTES,
					  T1.MAIN_FLAG,
					  T1.VIEW_FLAG,
					  T1.PERIOD_YEAR,
					  T1.PERIOD_ID,
					  T1.RMB_COST_AMT,
					  T1.RMB_AVG_AMT,
					  T1.ACTUAL_QTY,
					  SUM(T1.NULL_FLAG) OVER(PARTITION BY  '||V_SQL_PUBLIC_PBI_PART||' NVL(T1.SPART_CODE,''SPART''), NVL(T1.DIMENSION_CODE,''DC''), NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC''), '||V_SQL_OTHER_DIM_PART||' NVL(T1.CODE_ATTRIBUTES,''CA''), NVL(T1.MAIN_FLAG,''MF''), NVL(T1.SOFTWARE_MARK,''SW''),NVL(T1.VIEW_FLAG,''VF'') ORDER BY T1.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
					  T1.APD_FLAG,
					  SOFTWARE_MARK
				 FROM (SELECT '||V_SQL_PUBLIC_PBI_PART||' 
							'||V_SQL_DIMENSION_PART||' 
							T1.SPART_CODE,
							  T1.SPART_CN_NAME,
							  '||V_SQL_OTHER_DIM_PART||' 
							  T1.CODE_ATTRIBUTES,
							  T1.SOFTWARE_MARK,
							  T1.MAIN_FLAG,
							  T1.VIEW_FLAG,
							  T1.PERIOD_YEAR,
							  T1.PERIOD_ID,
							  T2.RMB_COST_AMT,
							  T2.RMB_AVG_AMT,
							  T2.ACTUAL_QTY,
							  DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
							  NVL(T2.APPEND_FLAG, ''Y'') AS APD_FLAG --补齐标识：Y为补齐，N为原始存在
						 FROM CROSS_JOIN_TEMP T1
						 LEFT JOIN DM_REPL_YTD_AVG_TEMP T2
						   ON T1.PERIOD_ID = T2.PERIOD_ID
						  AND T1.LV4_CODE = T2.LV4_CODE
						  AND NVL(T1.MAIN_FLAG,''MF'') =
							  NVL(T2.MAIN_FLAG,''MF'')
						  AND NVL(T1.CODE_ATTRIBUTES,''CA'') =
							  NVL(T2.CODE_ATTRIBUTES,''CA'')
						  AND NVL(T1.SOFTWARE_MARK,''SW'') =
							  NVL(T2.SOFTWARE_MARK,''SW'')
						'||V_JOIN_DIMENSION_CODE||'
						  AND NVL(T1.SPART_CODE,''SC'') =
							  NVL(T2.SPART_CODE,''SC'')
						'||V_JOIN_OTHER_DIM_PART||'
						  AND T1.VIEW_FLAG = T2.VIEW_FLAG) T1) SS)
	SELECT 
		   T1.LV0_CODE,
		   T1.LV1_CODE,
		   T1.LV2_CODE,
		   T1.LV3_CODE,
		   T1.LV4_CODE,
		   T1.LV0_CN_NAME,
		   T1.LV1_CN_NAME,
		   T1.LV2_CN_NAME,
		   T1.LV3_CN_NAME,
		   T1.LV4_CN_NAME,
		   T1.DIMENSION_CODE,
		   T1.DIMENSION_CN_NAME,
		   T1.DIMENSION_SUBCATEGORY_CODE,
		   T1.DIMENSION_SUBCATEGORY_CN_NAME,
		   T1.DIMENSION_SUB_DETAIL_CODE,
		   T1.DIMENSION_SUB_DETAIL_CN_NAME,
		   T1.VIEW_FLAG,
		   T1.CODE_ATTRIBUTES,
		   T1.MAIN_FLAG,
		   T1.SPART_CODE,
		   T1.SPART_CN_NAME,
		   T1.REGION_CODE,
		   T1.REGION_CN_NAME,
		   T1.REPOFFICE_CODE,
		   T1.REPOFFICE_CN_NAME,
		   T1.BG_CODE,
		   T1.BG_CN_NAME,
		   T1.OVERSEA_FLAG,
		   T1.PERIOD_YEAR,
		   T1.PERIOD_ID,
		   NVL(T1.RMB_AVG_AMT, T2.RMB_AVG_AMT) AS RMB_AVG_AMT,
		   T1.ACTUAL_QTY,
		   NVL(T1.RMB_COST_AMT, T2.RMB_COST_AMT) AS RMB_COST_AMT,
		   T1.APPEND_FLAG,
		   T1.SOFTWARE_MARK
	FROM
	
	(--向后补齐均价
	SELECT 
		distinct S.LV0_CODE,
		   S.LV1_CODE,
		   S.LV2_CODE,
		   S.LV3_CODE,
		   S.LV4_CODE,
		   S.LV0_CN_NAME,
		   S.LV1_CN_NAME,
		   S.LV2_CN_NAME,
		   S.LV3_CN_NAME,
		   S.LV4_CN_NAME,
		   S.DIMENSION_CODE,
		   S.DIMENSION_CN_NAME,
		   S.DIMENSION_SUBCATEGORY_CODE,
		   S.DIMENSION_SUBCATEGORY_CN_NAME,
		   S.DIMENSION_SUB_DETAIL_CODE,
		   S.DIMENSION_SUB_DETAIL_CN_NAME,
		   S.VIEW_FLAG,
		   S.CODE_ATTRIBUTES,
		   S.MAIN_FLAG,
		   S.SPART_CODE,
		   S.SPART_CN_NAME,
		   S.REGION_CODE,
		   S.REGION_CN_NAME,
		   S.REPOFFICE_CODE,
		   S.REPOFFICE_CN_NAME,
		   S.BG_CODE,
		   S.BG_CN_NAME,
		   S.OVERSEA_FLAG,
		   S.PERIOD_YEAR,
		   S.PERIOD_ID,
		   NVL(S.RMB_AVG_AMT_2, S.RMB_AVG_AMT_3) AS RMB_AVG_AMT,
		   NVL(S.ACTUAL_QTY_2, S.ACTUAL_QTY_3) AS ACTUAL_QTY,
		   NVL(S.RMB_COST_AMT_2, S.RMB_COST_AMT_3) AS RMB_COST_AMT,
		   S.APD_FLAG AS APPEND_FLAG,
		   S.SOFTWARE_MARK
	  FROM (SELECT '||V_SQL_PUBLIC_PBI_PART||'
					'||V_SQL_DIMENSION_PART||' 
					T1.SPART_CODE,
				   T1.SPART_CN_NAME,
				   '||V_SQL_OTHER_DIM_PART||' 
				   T1.CODE_ATTRIBUTES,
				   T1.MAIN_FLAG,
				   T1.VIEW_FLAG,
				   T1.PERIOD_YEAR,
				   T1.PERIOD_ID,
				   T1.RMB_COST_AMT_2,
				   T2.RMB_COST_AMT_3,
				   T1.RMB_AVG_AMT_2,
				   T2.RMB_AVG_AMT_3,
				   T1.ACTUAL_QTY_2,
				   T2.ACTUAL_QTY_3,
				   T1.APD_FLAG,
				   T1.SOFTWARE_MARK
			  FROM FORWARD_FILLER_TEMP T1
			  LEFT JOIN (SELECT DISTINCT S.LV4_CODE,
			                             S.DIMENSION_CODE,
			                             S.DIMENSION_SUBCATEGORY_CODE,
			                             S.DIMENSION_SUB_DETAIL_CODE,
			                             S.SPART_CODE,
			                             S.REGION_CODE,
			                             S.REPOFFICE_CODE,
			                             S.BG_CODE,
			                             S.OVERSEA_FLAG,
										 S.VIEW_FLAG,
										 S.MAIN_FLAG,
										 S.CODE_ATTRIBUTES,
										 S.PERIOD_YEAR,
										FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''), 
																				NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''),
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG, S.SOFTWARE_MARK, 
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
										FIRST_VALUE(S.RMB_AVG_AMT_2) OVER(PARTITION BY 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''),
																				NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG,S.SOFTWARE_MARK,
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS RMB_AVG_AMT_3, --有量的首条补齐量
										FIRST_VALUE(S.RMB_COST_AMT_2) OVER(PARTITION BY 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''),
																				NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG,S.SOFTWARE_MARK,
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS RMB_COST_AMT_3, --有量的首条补齐量										
																				
										FIRST_VALUE(S.ACTUAL_QTY_2) OVER(PARTITION BY 
																				S.LV0_CODE, S.LV1_CODE, S.LV2_CODE, S.LV3_CODE, S.LV4_CODE, 
																				NVL(S.SPART_CODE,''SPART''), NVL(S.DIMENSION_CODE,''DC''), NVL(S.DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(S.DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
																				S.REGION_CODE, S.REPOFFICE_CODE, S.BG_CODE, S.OVERSEA_FLAG, S.SOFTWARE_MARK,
																				NVL(S.CODE_ATTRIBUTES,''CA''), NVL(S.MAIN_FLAG,''MF''), NVL(S.VIEW_FLAG,''VF'') 
																				ORDER BY S.PERIOD_ID ASC) AS ACTUAL_QTY_3 ,--有均价的首条补齐均价
																				SOFTWARE_MARK
						  FROM FORWARD_FILLER_TEMP S
						 WHERE S.AVG_AMT_FLAG > 0) T2
				ON T1.LV4_CODE = T2.LV4_CODE
			   AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
			   AND NVL(T1.CODE_ATTRIBUTES,''CA'') =
				   NVL(T2.CODE_ATTRIBUTES,''CA'')
			   AND NVL(T1.SOFTWARE_MARK,''SW'') =
				   NVL(T2.SOFTWARE_MARK,''SW'')
			 '||V_JOIN_DIMENSION_CODE||'
			   AND NVL(T1.SPART_CODE,''SC'') = NVL(T2.SPART_CODE,''SC'')
			 '||V_JOIN_OTHER_DIM_PART||'
			   AND T1.VIEW_FLAG = T2.VIEW_FLAG
			   AND T1.PERIOD_ID < T2.PERIOD_ID) S
			   ) T1
			   LEFT JOIN '||V_JOIN_TABLE||' T2
			   ON '||V_JOIN_PARA||'
			   AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
			   AND NVL(T1.CODE_ATTRIBUTES,''CA'') =
				   NVL(T2.CODE_ATTRIBUTES,''CA'')
			   AND NVL(T1.SOFTWARE_MARK,''SW'') =
				   NVL(T2.SOFTWARE_MARK,''SW'')
			 '||V_JOIN_DIMENSION_CODE||'
			   AND NVL(T1.SPART_CODE,''SC'') = NVL(T2.SPART_CODE,''SC'')
			 '||V_JOIN_OTHER_DIM_PART||'
			   AND T1.VIEW_FLAG = T2.VIEW_FLAG
			  

';

--DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;


raise notice'222222222222222222';


 V_EXCEPTION_FLAG := 6;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_EXCEPTION_FLAG,
  F_CAL_LOG_DESC =>'编码替代页面用的数据补齐完成' ,
  F_RESULT_STATUS => X_RESULT_STATUS,
   F_FORMULA_SQL_TXT => V_SQL,
  F_ERRBUF => 'SUCCESS');
  
  
  
  --编码替代页面的月累计加密落表
RAISE NOTICE '编码替代页面的月累计加密落表';
V_SQL:='
DELETE FROM  '||V_TO_REPL_TABLE||' WHERE BG_CODE = '''||F_BG_CODE||''' AND OVERSEA_FLAG = '''||F_OVERSEA_FLAG||''';

  INSERT INTO '||V_TO_REPL_TABLE||'(
     VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_PBI_PART||' 
	   '||V_DIMENSION_PART||' 
	   SPART_CODE,
     SPART_CN_NAME,
     '||V_OTHER_DIM_PART||' 
	   CODE_ATTRIBUTES,
	   SOFTWARE_MARK,
     MAIN_FLAG,
     VIEW_FLAG,
     RMB_AVG_AMT,
     RMB_COST_AMT,
     ACTUAL_QTY,
	   APPEND_FLAG,
	   CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG
  )
  SELECT '||V_VERSION||' AS VERSION_ID,
        PERIOD_YEAR,
        PERIOD_ID,
        '||V_PBI_PART||' 
        '||V_DIMENSION_PART||' 
        SPART_CODE,
        SPART_CN_NAME,
        '||V_OTHER_DIM_PART||' 
        CODE_ATTRIBUTES,
		SOFTWARE_MARK,
        MAIN_FLAG,
        VIEW_FLAG,
        '||V_RMB_AVG_AMT||'
        '||V_RMB_REPL_COST_AMT||'
        ACTUAL_QTY,
        APPEND_FLAG,
        -1 AS CREATED_BY,
        CURRENT_TIMESTAMP AS CREATION_DATE,
        -1 AS LAST_UPDATED_BY,
        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
        ''N'' AS DEL_FLAG
   FROM DM_REPL_APPEND_AVG_TEMP
  ;
 ';
      
  --DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE V_SQL;

 V_EXCEPTION_FLAG := 8;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_EXCEPTION_FLAG,
  F_CAL_LOG_DESC =>'编码替代页面的月累计数据插表完成' ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
  F_ERRBUF => 'SUCCESS');

  V_SQL:= 'ANALYZE  '||V_TO_REPL_TABLE;
  EXECUTE V_SQL;
  
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

