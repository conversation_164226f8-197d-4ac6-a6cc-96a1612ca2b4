-- Name: f_dm_fcst_price_top_spart_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_top_spart_info_t(f_version_id integer, character varying DEFAULT NULL::integer, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间: 20241108
创建人  : 黄心蕊 hwx1187045
背景描述: 定价指数-TOP_SAPRT筛选 筛选本年TOP
参数描述: 参数一:  F_VERSION_ID 版本号
		  参数二:  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

--来源表
DM_FCST_PRICE_MID_MON_SPART_T --SPART层级月卷积汇总表-包含全球、全选

--目标表
DM_FCST_PRICE_TOP_SPART_INFO_T	--(月累计TOP_SPART表)
事例
SELECT F_DM_FCST_PRICE_TOP_SPART_INFO_T('');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_TOP_SPART_INFO_T';
  V_VERSION        INT; --版本号
  V_VERSION2       INT;
  V_EXCEPTION_FLAG INT; --异常步骤
  V_YEAR           INT;

BEGIN 
X_RESULT_STATUS := '1';

 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
 V_EXCEPTION_FLAG	:= 1;
--月度版本号取值
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
  
  SELECT VERSION_ID
      INTO V_VERSION2
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ANNUAL'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  
  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
 
  V_EXCEPTION_FLAG	:= 2;
  DROP TABLE IF EXISTS DM_SPART_SINGLE_YEAR_WEIGHT_TEMP ;
  CREATE TEMPORARY TABLE DM_SPART_SINGLE_YEAR_WEIGHT_TEMP(
	PERIOD_YEAR	BIGINT,
	LV0_PROD_LIST_CODE	VARCHAR(50),
	LV0_PROD_LIST_CN_NAME	VARCHAR(200),
	LV1_PROD_LIST_CODE	VARCHAR(50),
	LV1_PROD_LIST_CN_NAME	VARCHAR(200),
	LV2_PROD_LIST_CODE	VARCHAR(50),
	LV2_PROD_LIST_CN_NAME	VARCHAR(200),
	LV3_PROD_LIST_CODE	VARCHAR(50),
	LV3_PROD_LIST_CN_NAME	VARCHAR(200),
	LV4_PROD_LIST_CODE	VARCHAR(50),
	LV4_PROD_LIST_CN_NAME	VARCHAR(200),
	SPART_CODE	VARCHAR(50),
	SPART_CN_NAME	VARCHAR(2000),
	WEIGHT_RATE	NUMERIC,
	OVERSEA_FLAG	VARCHAR(10),
	REGION_CODE	VARCHAR(50),
	REGION_CN_NAME	VARCHAR(200),
	REPOFFICE_CODE	VARCHAR(50),
	REPOFFICE_CN_NAME	VARCHAR(200),
	SIGN_TOP_CUST_CATEGORY_CODE	VARCHAR(50),
	SIGN_TOP_CUST_CATEGORY_CN_NAME	VARCHAR(200),
	SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	VARCHAR(200),
	VIEW_FLAG	INT,
	BG_CODE	VARCHAR(50),
	BG_CN_NAME	VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  

  if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
  
  --分字段 计算每年权重
  WITH BASE_YEAR_AMT AS
   (SELECT PERIOD_YEAR,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CODE,
           LV1_PROD_LIST_CN_NAME,
           LV2_PROD_LIST_CODE,
           LV2_PROD_LIST_CN_NAME,
           LV3_PROD_LIST_CODE,
           LV3_PROD_LIST_CN_NAME,
           LV4_PROD_LIST_CODE,
           LV4_PROD_LIST_CN_NAME,
           SPART_CODE,
           SPART_CN_NAME,
           USD_PNP_AMT,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           DECODE(VIEW_FLAG,'LOCAL_AGENT',1,2) AS VIEW_FLAG,
           BG_CODE,
           BG_CN_NAME
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_MON_SPART_T
     WHERE RIGHT(PERIOD_ID, 2) ::INT = (CASE WHEN PERIOD_YEAR = YEAR(NOW()) AND MONTH(CURRENT_TIMESTAMP) = 1 
	                                        THEN 12     
											WHEN PERIOD_YEAR = YEAR(NOW()) AND MONTH(CURRENT_TIMESTAMP) > 1  
											THEN (MONTH(NOW()) - 1)::INT              
											ELSE 12
											END ) --取每年最大月份金额
	   AND USD_PNP_AMT <> 0
	   AND ENABLE_FLAG = 'Y'
	   AND VERSION_ID = V_VERSION2
    )
INSERT INTO DM_SPART_SINGLE_YEAR_WEIGHT_TEMP
  (PERIOD_YEAR,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   LV1_PROD_LIST_CODE,
   LV1_PROD_LIST_CN_NAME,
   LV2_PROD_LIST_CODE,
   LV2_PROD_LIST_CN_NAME,
   LV3_PROD_LIST_CODE,
   LV3_PROD_LIST_CN_NAME,
   LV4_PROD_LIST_CODE,
   LV4_PROD_LIST_CN_NAME,
   SPART_CODE,
   SPART_CN_NAME,
   WEIGHT_RATE,
   OVERSEA_FLAG,
   REGION_CODE,
   REGION_CN_NAME,
   REPOFFICE_CODE,
   REPOFFICE_CN_NAME,
   SIGN_TOP_CUST_CATEGORY_CODE,
   SIGN_TOP_CUST_CATEGORY_CN_NAME,
   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
   VIEW_FLAG,
   BG_CODE,
   BG_CN_NAME)
  SELECT PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         NVL((USD_PNP_AMT / NULLIF(SUM(USD_PNP_AMT)OVER(PARTITION BY PERIOD_YEAR, LV4_PROD_LIST_CODE, 
														OVERSEA_FLAG, REGION_CODE, REPOFFICE_CODE, 
														SIGN_TOP_CUST_CATEGORY_CODE, SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, 
														VIEW_FLAG, BG_CODE),0) ),0) AS WEIGHT_RATE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         BG_CODE,
         BG_CN_NAME
    FROM BASE_YEAR_AMT;
	
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => 'SPART分年权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  V_EXCEPTION_FLAG	:= 3;
  DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_TOP_SPART_INFO_T WHERE VERSION_ID = V_VERSION;
  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => 'TOP_SPART同'||V_VERSION||'数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  V_EXCEPTION_FLAG	:= 4;
  WITH BASE_FLAG_TEMP AS
   (
    --如果LV4下所有SPART的权重值相同, 或第二位权重值到末尾权重值相同, 则最大排序值标记为1或2, 反之为一般数据情况
    SELECT PERIOD_YEAR,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
			OVERSEA_FLAG,
			BG_CODE,
			BG_CN_NAME,
			REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,  
            SPART_CODE,
            VIEW_FLAG,
            WEIGHT_RATE,
            DRANK_SORT,
            MAX(DRANK_SORT) OVER(PARTITION BY PERIOD_YEAR,BG_CODE, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV1_PROD_LIST_CODE, LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE, LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE, LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE, LV4_PROD_LIST_CN_NAME,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,OVERSEA_FLAG, VIEW_FLAG) AS MAX_DRANK_SORT
      FROM (SELECT PERIOD_YEAR,
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME,
                    LV1_PROD_LIST_CODE,
                    LV1_PROD_LIST_CN_NAME,
                    LV2_PROD_LIST_CODE,
                    LV2_PROD_LIST_CN_NAME,
                    LV3_PROD_LIST_CODE,
                    LV3_PROD_LIST_CN_NAME,
                    LV4_PROD_LIST_CODE,
                    LV4_PROD_LIST_CN_NAME,
					OVERSEA_FLAG,
					BG_CODE,
			BG_CN_NAME,
					REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
                    SPART_CODE,
                    VIEW_FLAG,
                    WEIGHT_RATE,
                    DENSE_RANK()OVER(PARTITION BY PERIOD_YEAR, BG_CODE,LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV1_PROD_LIST_CODE, LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE, LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE, LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE, LV4_PROD_LIST_CN_NAME,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,OVERSEA_FLAG, VIEW_FLAG ORDER BY WEIGHT_RATE DESC) AS DRANK_SORT
               FROM DM_SPART_SINGLE_YEAR_WEIGHT_TEMP
                    )
                     R),
  
  NORMAL_DATA_TEMP AS
   (
    --筛选一般数据情况: 最大排序值标识大于2的数据, 并带出ROW_NUMBER排序字段
    SELECT PERIOD_YEAR,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
			OVERSEA_FLAG,
			BG_CODE,
			BG_CN_NAME,
			REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
            SPART_CODE,
            VIEW_FLAG,
            T.WEIGHT_RATE,
            ROW_NUMBER() OVER(PARTITION BY PERIOD_YEAR, BG_CODE,LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV1_PROD_LIST_CODE, LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE, LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE, LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE, LV4_PROD_LIST_CN_NAME,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,OVERSEA_FLAG, VIEW_FLAG ORDER BY T.WEIGHT_RATE DESC) AS RRANK_SORT,
            T.DRANK_SORT,
            T.MAX_DRANK_SORT
      FROM BASE_FLAG_TEMP T
     WHERE T.MAX_DRANK_SORT > 2),
  
  FLAG_DATA_TEMP AS
   (
    --对NORMAL_DATA_TEMP临时表打上辅助列标识:(ACCU_WEIGHT->ACCU_FLAG->CAL_FLAG)
    SELECT PERIOD_YEAR,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
			OVERSEA_FLAG,
			BG_CODE,
			BG_CN_NAME,
			REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
            SPART_CODE,
			VIEW_FLAG,
            WEIGHT_RATE,
            ACCU_WEIGHT,
            ACCU_FLAG,
            SUM(ACCU_FLAG) OVER(PARTITION BY PERIOD_YEAR, BG_CODE,LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV1_PROD_LIST_CODE, LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE, LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE, LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE, LV4_PROD_LIST_CN_NAME,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,OVERSEA_FLAG, VIEW_FLAG ORDER BY S.RRANK_SORT) AS CAL_FLAG --根据ROW_NUMBER累计求和ACCU_FLAG标识
      FROM (SELECT PERIOD_YEAR,
                    LV0_PROD_LIST_CODE,
                    LV0_PROD_LIST_CN_NAME,
                    LV1_PROD_LIST_CODE,
                    LV1_PROD_LIST_CN_NAME,
                    LV2_PROD_LIST_CODE,
                    LV2_PROD_LIST_CN_NAME,
                    LV3_PROD_LIST_CODE,
                    LV3_PROD_LIST_CN_NAME,
                    LV4_PROD_LIST_CODE,
                    LV4_PROD_LIST_CN_NAME,
					OVERSEA_FLAG,
					BG_CODE,
			BG_CN_NAME,
					REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
                    SPART_CODE,
                    VIEW_FLAG,
                    R.WEIGHT_RATE,
                    R.ACCU_WEIGHT,
                    R.RRANK_SORT,
                    CASE
                      WHEN R.ACCU_WEIGHT >= 0.95 THEN
                       1
                      ELSE
                       0
                    END AS ACCU_FLAG --累计权重大于95,标识为1,反之为0
               FROM (SELECT PERIOD_YEAR,
                            LV0_PROD_LIST_CODE,
                            LV0_PROD_LIST_CN_NAME,
                            LV1_PROD_LIST_CODE,
                            LV1_PROD_LIST_CN_NAME,
                            LV2_PROD_LIST_CODE,
                            LV2_PROD_LIST_CN_NAME,
                            LV3_PROD_LIST_CODE,
                            LV3_PROD_LIST_CN_NAME,
                            LV4_PROD_LIST_CODE,
                            LV4_PROD_LIST_CN_NAME,
							BG_CODE,
			BG_CN_NAME,
							OVERSEA_FLAG,
							REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
                            SPART_CODE,
                            Z.WEIGHT_RATE,
                            SUM(Z.WEIGHT_RATE) OVER(PARTITION BY PERIOD_YEAR, BG_CODE,LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, LV1_PROD_LIST_CODE, LV1_PROD_LIST_CN_NAME, LV2_PROD_LIST_CODE, LV2_PROD_LIST_CN_NAME, LV3_PROD_LIST_CODE, LV3_PROD_LIST_CN_NAME, LV4_PROD_LIST_CODE, LV4_PROD_LIST_CN_NAME,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,OVERSEA_FLAG, VIEW_FLAG ORDER BY Z.RRANK_SORT) AS ACCU_WEIGHT, --根据ROW_NUMBER累计求和权重值
                            Z.RRANK_SORT,
                            VIEW_FLAG
                       FROM NORMAL_DATA_TEMP Z) R) S),
  TOP_DATA_TEMP AS
   (
    --TOP_SPART的数据
    SELECT PERIOD_YEAR,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
            SPART_CODE,
            VIEW_FLAG,
            A.WEIGHT_RATE
      FROM BASE_FLAG_TEMP A
     WHERE MAX_DRANK_SORT <= 2
    UNION ALL
    SELECT PERIOD_YEAR,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME,
            LV1_PROD_LIST_CODE,
            LV1_PROD_LIST_CN_NAME,
            LV2_PROD_LIST_CODE,
            LV2_PROD_LIST_CN_NAME,
            LV3_PROD_LIST_CODE,
            LV3_PROD_LIST_CN_NAME,
            LV4_PROD_LIST_CODE,
            LV4_PROD_LIST_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			REGION_CODE,
			REGION_CN_NAME	,
			REPOFFICE_CODE	,
			REPOFFICE_CN_NAME	,
			SIGN_TOP_CUST_CATEGORY_CODE	,
			SIGN_TOP_CUST_CATEGORY_CN_NAME	,
			SIGN_SUBSIDIARY_CUSTCATG_CN_NAME	,
            SPART_CODE,
            VIEW_FLAG,
            B.WEIGHT_RATE
      FROM FLAG_DATA_TEMP B
     WHERE CAL_FLAG <= 2)
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_TOP_SPART_INFO_T
    (VERSION_ID,
     PERIOD_YEAR,
     LV0_PROD_LIST_CODE,
     LV1_PROD_LIST_CODE,
     LV2_PROD_LIST_CODE,
     LV3_PROD_LIST_CODE,
     LV4_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV1_PROD_LIST_CN_NAME,
     LV2_PROD_LIST_CN_NAME,
     LV3_PROD_LIST_CN_NAME,
     LV4_PROD_LIST_CN_NAME,
     TOP_SPART_CODE,
     TOP_SPART_CN_NAME,
     WEIGHT_RATE,
     IS_TOP_FLAG,
     YTD_TOP_FLAG,
     OVERSEA_FLAG,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     SIGN_TOP_CUST_CATEGORY_CODE,
     SIGN_TOP_CUST_CATEGORY_CN_NAME,
     SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
     VIEW_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     BG_CODE,
     BG_CN_NAME)
  --临时表左关联TOP品类临时表, 打上是否为TOP类标识
  SELECT DISTINCT V_VERSION AS VERSION_ID,
                  T1.PERIOD_YEAR,
                  T1.LV0_PROD_LIST_CODE,
                  T1.LV1_PROD_LIST_CODE,
                  T1.LV2_PROD_LIST_CODE,
                  T1.LV3_PROD_LIST_CODE,
                  T1.LV4_PROD_LIST_CODE,
                  T1.LV0_PROD_LIST_CN_NAME,
                  T1.LV1_PROD_LIST_CN_NAME,
                  T1.LV2_PROD_LIST_CN_NAME,
                  T1.LV3_PROD_LIST_CN_NAME,
                  T1.LV4_PROD_LIST_CN_NAME,
                  T1.SPART_CODE,
                  T1.SPART_CN_NAME,
                  T1.WEIGHT_RATE,
				  DECODE(T2.SPART_CODE, NULL, 'N', 'Y') AS IS_TOP_FLAG,
				  DECODE(T3.SPART_CODE, NULL, 'N', 'Y') AS YTD_TOP_FLAG,
                  T1.OVERSEA_FLAG,
                  T1.REGION_CODE,
                  T1.REGION_CN_NAME,
                  T1.REPOFFICE_CODE,
                  T1.REPOFFICE_CN_NAME,
                  T1.SIGN_TOP_CUST_CATEGORY_CODE,
                  T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
                  T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
                  DECODE(T1.VIEW_FLAG,1,'LOCAL_AGENT','SYS_DEPT') AS VIEW_FLAG,
                  -1 AS CREATED_BY,
                  CURRENT_TIMESTAMP AS CREATION_DATE,
                  -1 AS LAST_UPDATED_BY,
                  CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                  'N' AS DEL_FLAG,
				  T1.BG_CODE,
                  T1.BG_CN_NAME
    FROM DM_SPART_SINGLE_YEAR_WEIGHT_TEMP T1
    LEFT JOIN TOP_DATA_TEMP T2
      ON T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
     AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
     AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
     AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
     AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
	 AND NVL(T1.OVERSEA_FLAG,'A') =  NVL(T2.OVERSEA_FLAG,'A')
	 AND T1.BG_CODE = T2.BG_CODE
	 AND NVL(T1.REGION_CODE,'A') =  NVL(T2.REGION_CODE,'A')
	 AND NVL(T1.REPOFFICE_CODE,'A') =  NVL(T2.REPOFFICE_CODE,'A')
	 AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'A') =  NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'A')
	 AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'A') =  NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'A')
     AND T1.SPART_CODE = T2.SPART_CODE
    LEFT JOIN (SELECT * FROM TOP_DATA_TEMP WHERE PERIOD_YEAR = V_YEAR) T3
  --关联最新年份TOP结果,用于年度均本补齐
      ON T1.SPART_CODE = T3.SPART_CODE
     AND T1.VIEW_FLAG = T3.VIEW_FLAG
	   AND T1.LV0_PROD_LIST_CODE = T3.LV0_PROD_LIST_CODE
     AND T1.LV1_PROD_LIST_CODE = T3.LV1_PROD_LIST_CODE
     AND T1.LV2_PROD_LIST_CODE = T3.LV2_PROD_LIST_CODE
     AND T1.LV3_PROD_LIST_CODE = T3.LV3_PROD_LIST_CODE
     AND T1.LV4_PROD_LIST_CODE = T3.LV4_PROD_LIST_CODE
	 AND NVL(T1.OVERSEA_FLAG,'A') =  NVL(T3.OVERSEA_FLAG,'A')
	 AND T1.BG_CODE = T3.BG_CODE
	 AND NVL(T1.REGION_CODE,'A') =  NVL(T3.REGION_CODE,'A')
	 AND NVL(T1.REPOFFICE_CODE,'A') =  NVL(T3.REPOFFICE_CODE,'A')
	 AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'A') =  NVL(T3.SIGN_TOP_CUST_CATEGORY_CODE,'A')
	 AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'A') =  NVL(T3.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'A')
	 ;
	 
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => 'TOP_SPART第 '||V_VERSION||' 版本数据计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

