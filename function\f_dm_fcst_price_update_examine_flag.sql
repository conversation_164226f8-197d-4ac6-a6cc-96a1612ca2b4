-- Name: f_dm_fcst_price_update_examine_flag; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_update_examine_flag(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：	1.更新底层数据审视版本
*/



DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.f_dm_fcst_price_update_examine_flag'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 查看是否更新完成
  V_SQL TEXT;
  
  V_RMB_COST_AMT VARCHAR(100);
  V_SUM_RMB_AMT VARCHAR(200);
  V_AVG_RMB_AMT VARCHAR(200);
  V_FROM_TABLE VARCHAR(50);
  V_TO_TABLE VARCHAR(50);
  V_JOIN_TABLE VARCHAR(50);


BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   

   --设定目标表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T'; 



	 
	--更新语句 
	V_SQL := 'UPDATE '||V_TO_TABLE||' SET STATUS = 1 ,
			   IS_RUNNING = ''N''
			  WHERE DATA_TYPE = ''DATA_REVIEW''
			  AND STATUS = 0
			  AND IS_RUNNING = ''Y''
			  ';
	DBMS_OUTPUT.PUT_LINE(V_SQL);		  
	EXECUTE IMMEDIATE V_SQL ;
	
	
		V_SQL := 'UPDATE '||V_TO_TABLE||' SET STATUS = 1 ,
			   IS_RUNNING = ''N''
			  WHERE DATA_TYPE = ''MONTH''
			  AND STATUS = 1
			  AND IS_RUNNING = ''Y''
			  ';
	DBMS_OUTPUT.PUT_LINE(V_SQL);		  
	EXECUTE IMMEDIATE V_SQL ;
	
	
		V_SQL := 'UPDATE '||V_TO_TABLE||' SET STATUS = 1 ,
			   IS_RUNNING = ''N''
			  WHERE DATA_TYPE = ''ANNUAL''
			  AND STATUS = 1
			  AND IS_RUNNING = ''Y''
			  ';
	DBMS_OUTPUT.PUT_LINE(V_SQL);		  
	EXECUTE IMMEDIATE V_SQL ;
	
	
	
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '更新到 '||V_TO_TABLE||'表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  	
	
	
	
--判断是否还有代刷数的版本
  SELECT COUNT(1) INTO V_CURRENT_FLAG 
  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
	WHERE DATA_TYPE = 'DATA_REVIEW'
			  AND STATUS = 0
			  AND IS_RUNNING = 'Y' ;
	
	
	
--如果还有待刷数的版本，则返回失败	
   IF V_CURRENT_FLAG <> 0 THEN 
     RETURN '0';
	
   END IF;



  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

