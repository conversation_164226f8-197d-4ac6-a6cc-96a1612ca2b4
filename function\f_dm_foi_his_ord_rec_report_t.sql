-- Name: f_dm_foi_his_ord_rec_report_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_his_ord_rec_report_t(OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************
创建时间：2022-10-11
创建人  ：陈艺筱 cwx1182904
最近更新时间：2023-01-04
最近更新人：黄心蕊 hwx1187045
更新背景描述： 202401版本新增对来源表的前置处理，取DWR_DIM_MATERIAL_CODE_D表的ITEM_CODE作后续关联
背景描述：根据上游的采购明细数据更新，就可以调度函数重新生成一版数据，全量抽取，支持删除重跑。
****************************************************************************************************************************************************************/
declare 
    v_sp_name        varchar(50):= 'FIN_DM_OPT_FOI.F_DM_FOI_HIS_ORD_REC_REPORT_T';
    v_dml_row_count  number default 0 ;
begin
    x_success_flag := '1';

--0.日志开始
    perform fin_dm_opt_foi.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => 0,
   f_cal_log_desc => v_sp_name||'开始执行');

--删除重跑
    TRUNCATE TABLE fin_dm_opt_foi.DM_FOI_HIS_ORD_REC_REPORT_T;

--1.写入日志    
    perform fin_dm_opt_foi.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => 1,
   f_cal_log_desc => '清空fin_dm_opt_foi.dm_foi_his_ord_rec_report_t表的数据',
   f_dml_row_count => sql%rowcount,
   f_result_status => x_success_flag,
   f_errbuf => 'success');

--插入目标表数据
insert into fin_dm_opt_foi.dm_foi_his_ord_rec_report_t(
                 year,                                    --会计期(年)
                 period_id,                            --会计期
                 item_id,                                --ITEM_ID
                 item_code,                            --ITEM_编码
                 item_name,                            --ITEM_名称
                 receive_qty,                        --到货数量
                 receive_amt_usd,                --到货金额(USD)
                 receive_amt_cny,                --到货金额(CNY)
                 category_code,                    --品类编码
                 category_name,                    --品类名称
                 category_en_name,            --品类英文名
                 supplier_code,                    --供应商编码
                 supplier_cn_name,            --供应商中文名
                 l2_ceg_cn_name,                --生产采购中文名
                 l3_ceg_cn_name,                --专家团中文名
                 l4_ceg_cn_name,                --模块中文名
                 l3_ceg_short_cn_name,    --专家团（Group Lv3简称）
                 l4_ceg_short_cn_name,    --模块（Group Lv4简称）
                 l2_ceg_code,                        --生产采购编码
                 l3_ceg_code,                        --专家团编码
                 l4_ceg_code,                        --模块编码
                 created_by,                        --创建人
                 creation_date,                    --创建时间
                 last_updated_by,                --修改人
                 last_update_date,            --修改时间
                 del_flag,                      --是否删除
                 append_flag            --是否为真实值
                )         

with opt_dim_supplier_tmp as (
select * from(
select supplier_key,supplier_code,supplier_name,
             row_number() over(partition by supplier_code order by SCD_ACTIVE_BEGIN_DATE DESC) rn
        from dwrdim.dwr_dim_supplier_d
        where scd_active_ind = 1
) where rn = 1
)            
,  --202401版本新增对明细表的提前关联处理
DWL_PRO_PO_SHIPMENT_RECEIPT_TEMP AS (
SELECT S.YEAR,
       S.PERIOD_ID,
       S.SUPPLIER_CODE,
       S.RECEIVE_QTY,
       S.RECEIVE_AMT_CNY,
       S.RECEIVE_AMT_USD,
       S.ITEM_CODE,
       S.PO_PURCHASE_TARGET_TYPE,
       S.EMS_STRATEGY,
       S.INTERCOMPANY_TRANSACTION_FLAG,
       S.PROCUREMENT_PP_FLAG,
       S.CEG_KEY,
       T.ITEM_SUBTYPE_CODE,        --202401版本取T表做后续表关联
       T.ITEM_SUBTYPE_CN_NAME,
       T.ITEM_SUBTYPE_EN_NAME,
       S.SUPPLIER_KEY
  FROM FIN_DM_OPT_FOI.FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I S
  LEFT JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D T
    ON S.ITEM_ID = T.ITEM_ID
 WHERE T.ITEM_SUBTYPE_CODE IS NOT NULL
)
,
opt_his_total_tmp as(
select  s.year,
        s.period_id,
        m.item_id,
        ict.item_code,
        ict.item_name,
        sum(s.receive_qty)       as  receive_qty,        
        sum(s.receive_amt_usd)   as  receive_amt_usd,
        sum(s.receive_amt_cny)   as  receive_amt_cny,
        ict.category_code,
        ict.category_name,
        m.item_subtype_en_name   as  category_en_name,
        s.supplier_code,
        ict.l2_ceg_cn_name,
        ict.l3_ceg_cn_name,
        ict.l4_ceg_cn_name,
        ict.l3_ceg_short_cn_name,
        ict.l4_ceg_short_cn_name,
        ict.l2_ceg_code,
        ict.l3_ceg_code,
        ict.l4_ceg_code
from DWL_PRO_PO_SHIPMENT_RECEIPT_TEMP s --202401版本新增前置规则，表置换
left join dwrdim.dwr_dim_supplier_d sup
on    s.supplier_key = sup.supplier_key
left join dmdim.dm_dim_ceg_d c  
on    s.ceg_key = c.ceg_key
left join dwrdim.dwr_dim_material_code_d m
on    s.item_code = m.item_code
and   s.item_subtype_code = m.item_subtype_code
left join fin_dm_opt_foi.dm_dim_foi_item_catg_modl_ceg_t ict
on    ict.category_code  = s.item_subtype_code
and   ict.item_code = s.item_code
where    s.ceg_key is not null
and   nvl( s.receive_qty,0) !='0' 
and   nvl( s.receive_amt_cny,0) !='0'
and   s.intercompany_transaction_flag = 'N'
and      s.procurement_pp_flag = 'Y'
and      s.ems_strategy is null
and      upper (s.po_purchase_target_type) = 'INVENTORY'--加个大写函数
and   not exists (select item_code 
                  from(select item_code 
                       from DWL_PRO_PO_SHIPMENT_RECEIPT_TEMP s --202401版本新增前置规则，表置换
                       where substr(s.item_code,1,2)='88' 
                       or    substr(s.item_code,1,2)='99')s1
                  where  s.item_code=s1.item_code)--剔除88，99开头，不能用not in
and   not exists (select item_code
                  from(select item_code 
                       from DWL_PRO_PO_SHIPMENT_RECEIPT_TEMP s --202401版本新增前置规则，表置换
                       where substr(s.item_code,1,1)='W' 
                       or    substr(s.item_code,1,1)='Z')s1
                  where  s.item_code=s1.item_code)--剔除W,Z开头，不能用not in
and   not exists (select supplier_code
                  from(select supplier_code 
                       from dwrdim.dwr_dim_supplier_d sup
                       where sup.supplier_code in('Z02A4V','A01312','Z01305','Z02P9U','A01719','Z00A95','4J2010','5Y1005','A03922','481023'))s1
                  where  s.supplier_code=s1.supplier_code)--剔除EMS厂商清单，不能用not in
and   ict.category_code is not null
and   s.item_code is not null
and   s.period_id between to_char(date_trunc('year',sysdate),'yyyy')-3 || '01' and 
                          to_char(add_months(sysdate, -1),'yyyymm')
and   c.l2_ceg_code  = '12229'--生产采购认证部 code
and   c.l3_ceg_code in ('12251','12252','12253','12256','12257','12849','16349','19382','16449','13610','19319')--11个专家团code(202407版本剔除：'19484'-智能汽车物料采购认证部)
and   c.ceg_level in('3','4')
and   c.del_flag = 'N'
--and   c.scd_active_end_date >= '4712-12-31 00:00:00' -- 限制取最新一条数据
--and   c.enable_flag = 1
--and   c.scd_active_ind = 1
group by  s.year,
          s.period_id,
          m.item_id,
          ict.item_code,
          ict.item_name,
          ict.category_code,
          ict.category_name,
          m.item_subtype_en_name,
          s.supplier_code,
          ict.l2_ceg_cn_name,
          ict.l3_ceg_cn_name,
          ict.l4_ceg_cn_name,
          ict.l3_ceg_short_cn_name,
          ict.l4_ceg_short_cn_name,
          ict.l2_ceg_code,
          ict.l3_ceg_code,
          ict.l4_ceg_code 
having sum(s.receive_qty)    >=10 
and    sum(s.receive_amt_cny)>=1
)
select  t1.year,
        t1.period_id,
        t1.item_id,
        t1.item_code,
        t1.item_name,
        t1.receive_qty,        
        t1.receive_amt_usd,
        t1.receive_amt_cny,
        t1.category_code,
        t1.category_name,
        t1.category_en_name,
        t1.supplier_code,
                t2.supplier_name as supplier_cn_name,
        t1.l2_ceg_cn_name,
        t1.l3_ceg_cn_name,
        t1.l4_ceg_cn_name,
        t1.l3_ceg_short_cn_name,
        t1.l4_ceg_short_cn_name,
        t1.l2_ceg_code,
        t1.l3_ceg_code,
        t1.l4_ceg_code,
        -1 as created_by,
        current_timestamp as creation_date,
        -1 as last_updated_by,
        current_timestamp as last_update_date,
        'N'as del_flag,
        'N'as append_flag
            from opt_his_total_tmp t1
            left join opt_dim_supplier_tmp t2
            on t1.supplier_code = t2.supplier_code
;

--2.写入日志
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => 2,
   f_cal_log_desc => '插入历史数据到fin_dm_opt_foi.dm_foi_his_ord_rec_report_t表',
   f_dml_row_count => sql%rowcount,
   f_result_status => x_success_flag,
   f_errbuf => 'success');
   
--3.收集统计信息
  analyze fin_dm_opt_foi.dm_foi_his_ord_rec_report_t;

--4.日志结束
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name,
   f_step_num => 3,
   f_cal_log_desc => v_sp_name||'运行结束，收集fin_dm_opt_foi.dm_foi_his_ord_rec_report_t统计信息完成!');
   
   return 'SUCCESS';
        
--处理异常信息
    exception
  when others then
  x_success_flag := 0;
  
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name, 
   f_cal_log_desc => v_sp_name||'运行失败', 
   f_result_status => x_success_flag, 
   f_errbuf => sqlstate||':'||sqlerrm
   );
     
end;


$$
/

