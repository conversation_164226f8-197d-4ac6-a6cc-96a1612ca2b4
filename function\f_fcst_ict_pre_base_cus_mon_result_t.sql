-- Name: f_fcst_ict_pre_base_cus_mon_result_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_fcst_ict_pre_base_cus_mon_result_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text DEFAULT NULL::text, f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************

--虚化预处理 主力编码SPART-LV0 成本指数与降成本指数

--------------来源表
--------指数数据来源
--成本指数中间表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_MID_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_MID_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_MID_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_MID_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_MID_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_MID_COST_IDX_T     --STD PROD

--降成本指数中间表
重量级团队目录-PSP:	DM_FCST_ICT_PSP_MON_MID_COST_RED_IDX_T	--PSP
重量级团队目录-STD:	DM_FCST_ICT_STD_MON_MID_COST_RED_IDX_T	--STD

--------权重数据来源
--补齐月均本表
重量级团队目录		DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_BASE_DETAIL_SPART_T   --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_T    --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_T     --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_BASE_DETAIL_SPART_T   --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_BASE_DETAIL_SPART_T    --STD PROD

--------------目标表
--权重表
重量级团队目录-PSP:	DM_FCST_ICT_PSP_BASE_CUS_MON_WEIGHT_T
重量级团队目录-STD:	DM_FCST_ICT_STD_BASE_CUS_MON_WEIGHT_T

--指数表
重量级团队目录-PSP: DM_FCST_ICT_PSP_BASE_CUS_MON_COST_IDX_T
重量级团队目录-STD: DM_FCST_ICT_STD_BASE_CUS_MON_COST_IDX_T

--降成本指数表
重量级团队目录-PSP: DM_FCST_ICT_PSP_BASE_CUS_MON_COST_RED_IDX_T
重量级团队目录-STD: DM_FCST_ICT_STD_BASE_CUS_MON_COST_RED_IDX_T

--同环比表
重量级团队目录-PSP: DM_FCST_ICT_PSP_BASE_CUS_MON_RATE_T
重量级团队目录-STD: DM_FCST_ICT_STD_BASE_CUS_MON_RATE_T

事例
  SELECT FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T('PSP','IRB','',''); 		--PSP成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T('PSP','INDUS','',''); 	--PSP成本 产业目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T('PSP','PROD','',''); 	--PSP成本 销售目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T('STD''IRB','密钥',''); 	--标准成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T('STD''INDUS','密钥',''); --标准成本 产业目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T('STD''PROD','密钥','');  --标准成本 销售目录一个版本数据

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_FCST_ICT_PRE_BASE_CUS_MON_RESULT_T';
  V_VERSION                 BIGINT; --版本号
  V_BASE_PERIOD_ID          INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_EXCEPTION_FLAG 			TEXT;
  V_FROM_AMT_TABLE          VARCHAR(200);
  V_FROM_BASE_IDX_TABLE     VARCHAR(200);
  V_FROM_BASE_RED_IDX_TABLE VARCHAR(200);
  V_TO_WEIGHT_TABLE         VARCHAR(200);
  V_TO_RED_IDX_TABLE        VARCHAR(200);
  V_TO_COST_IDX_TABLE       VARCHAR(200);
  V_TO_MONTH_RATE_TABLE     VARCHAR(200);
  V_YEAR3                   VARCHAR(50);
  V_YEAR2                   VARCHAR(50);
  V_YEAR1                   VARCHAR(50);
  V_OTHER_DIM_PART          TEXT;
  V_SQL_OTHER_DIM_PART      TEXT;
  V_PUBLIC_PBI_PART         TEXT;
  V_PBI_PART                TEXT;
  V_RMB_COST_AMT            TEXT;
  V_AMT_SQL                 TEXT;
  V_WEIGHT_SQL1             TEXT;
  V_WEIGHT_SQL2             TEXT;
  V_WEIGHT_SQL3             TEXT;
  V_COST_IDX_SQL            TEXT;
  V_RED_IDX_SQL             TEXT;
  V_MONTH_RATE_SQL          TEXT;
  V_JOIN_OTHER_DIM_PART     TEXT;
  V_EXECUTE_SQL             TEXT;
  V_SP_NUM                  BIGINT := 0;

BEGIN
  
--RAISE NOTICE '取版本号';
--版本表
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

  
  
--表名定义
  V_FROM_AMT_TABLE          := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_' ||F_GRANULARITY_TYPE ||'_BASE_DETAIL_SPART_T';
  V_FROM_BASE_IDX_TABLE     := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_' ||F_GRANULARITY_TYPE ||'_MON_MID_COST_IDX_T';
  V_FROM_BASE_RED_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_MON_MID_COST_RED_IDX_T';
  V_TO_WEIGHT_TABLE         := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_WEIGHT_T';
  V_TO_RED_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_COST_RED_IDX_T';
  V_TO_COST_IDX_TABLE       := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_COST_IDX_T';
  V_TO_MONTH_RATE_TABLE     := 'FIN_DM_OPT_FOI.DM_FCST_ICT_' || F_COST_TYPE || '_BASE_CUS_MON_RATE_T';
  
  V_YEAR3 := TO_CHAR((YEAR(CURRENT_DATE) - 3) || '-' ||(YEAR(CURRENT_DATE) - 2));
  V_YEAR2 := TO_CHAR((YEAR(CURRENT_DATE) - 2) || '-' ||(YEAR(CURRENT_DATE) - 1));
  V_YEAR1 := TO_CHAR((YEAR(CURRENT_DATE) - 1) || '-' || YEAR(CURRENT_DATE));
  
  
    V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
		';

 
  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  
  V_PUBLIC_PBI_PART:='LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,
                      LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
					  SPART_CODE,SPART_CN_NAME,
  '	;
  
--不同目录维度字段判断
  IF F_GRANULARITY_TYPE = 'IRB' THEN
	
    V_PBI_PART := '
      LV0_PROD_RND_TEAM_CODE,
      LV1_PROD_RND_TEAM_CODE,
      LV2_PROD_RND_TEAM_CODE,
      LV3_PROD_RND_TEAM_CODE,
      LV4_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RD_TEAM_CN_NAME,
      LV3_PROD_RD_TEAM_CN_NAME,
      LV4_PROD_RD_TEAM_CN_NAME,
      ';
	  
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_PBI_PART := '
      LV0_INDUSTRY_CATG_CODE    ,
      LV1_INDUSTRY_CATG_CODE      ,
      LV2_INDUSTRY_CATG_CODE      ,
      LV3_INDUSTRY_CATG_CODE      ,
      LV4_INDUSTRY_CATG_CODE      ,
      LV0_INDUSTRY_CATG_CN_NAME,
      LV1_INDUSTRY_CATG_CN_NAME,
      LV2_INDUSTRY_CATG_CN_NAME,
      LV3_INDUSTRY_CATG_CN_NAME,
      LV4_INDUSTRY_CATG_CN_NAME,
      ';

  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_PBI_PART      := '
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      ';
  
  END IF;
  
  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_COST_AMT := 'RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_COST_AMT := '
					 TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,
					   '''||F_KEYSTR||''',
					   ''aes128'',
					   ''cbc'',
					   ''sha256'')) AS RMB_COST_AMT,
				  ';
  END IF;
  

----补齐均本解密落表
--建表
V_AMT_SQL:='
----RAISE NOTICE ''数据解密落表'';
----金额解密落表
--建临时表
DROP TABLE IF EXISTS DM_MONTH_AMT_TEMP;
CREATE TEMPORARY TABLE DM_MONTH_AMT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_COST_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 CODE_ATTRIBUTES    VARCHAR(20),     --是否主力编码 
 SOFTWARE_MARK    VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--落表
--解密落表
  INSERT INTO DM_MONTH_AMT_TEMP
    (PERIOD_YEAR,
	 PERIOD_ID,
     '||V_PUBLIC_PBI_PART||'
	 RMB_COST_AMT,
	 '||V_OTHER_DIM_PART||'
     CODE_ATTRIBUTES,
	 VIEW_FLAG)
    SELECT PERIOD_YEAR,
           PERIOD_ID,
		   '||V_PBI_PART||' 
		   SPART_CODE,
           SPART_CN_NAME,
           '||V_RMB_COST_AMT||'
           '||V_OTHER_DIM_PART||' 
           CODE_ATTRIBUTES,
           SOFTWARE_MARK
		   VIEW_FLAG
      FROM '||V_FROM_AMT_TABLE||' 
	 WHERE APPEND_FLAG =''N''
	   AND VIEW_FLAG =''PROD_SPART''
	   AND MAIN_FLAG =''Y'';
';
  

  
  
--------权重计算
V_WEIGHT_SQL1:='
--RAISE NOTICE ''权重表建表'';

DROP TABLE IF EXISTS DM_BASE_WEIGHT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_WEIGHT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 PROD_CODE		VARCHAR(50),
 PROD_CN_NAME   VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		VARCHAR(20),
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) ,
 WEIGHT_RATE		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20) ,
 SOFTWARE_MARK     VARCHAR(30)   
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);';

----金额卷积出主力编码部分(并分出区间年)
 V_WEIGHT_SQL2:= '
 --RAISE NOTICE ''权重表计算'';
 
  WITH BASE_AMT AS
   (
    --Y-3到Y-2金额
    SELECT '''||V_YEAR3||''' AS PERIOD_YEAR,
			'||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||' 
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
            CODE_ATTRIBUTES,
            SOFTWARE_MARK
      FROM DM_MONTH_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 3 AND
           YEAR(CURRENT_DATE) - 2
	   AND VIEW_FLAG = ''PROD_SPART''
     GROUP BY '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
               CODE_ATTRIBUTES,SOFTWARE_MARK
    UNION ALL
    --Y-2到Y-1金额
    SELECT '''||V_YEAR2||''' AS PERIOD_YEAR,
			'||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
            CODE_ATTRIBUTES,
            SOFTWARE_MARK
      FROM DM_MONTH_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 2 AND
           YEAR(CURRENT_DATE) - 1
	   AND VIEW_FLAG = ''PROD_SPART''
     GROUP BY '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
               CODE_ATTRIBUTES,SOFTWARE_MARK
    UNION ALL
    --Y-1到Y金额
    SELECT '''||V_YEAR1||''' AS PERIOD_YEAR,
			'||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
            CODE_ATTRIBUTES,
            SOFTWARE_MARK
      FROM DM_MONTH_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 1 AND YEAR(CURRENT_DATE)
	   AND VIEW_FLAG = ''PROD_SPART''
     GROUP BY '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
               CODE_ATTRIBUTES,SOFTWARE_MARK
			   )
	INSERT INTO DM_BASE_WEIGHT_TEMP
	('||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
	 WEIGHT_RATE,
	 PERIOD_YEAR,
	 CODE_ATTRIBUTES,
            SOFTWARE_MARK
	)			   		   
  --全量主力编码
  SELECT '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
		 RMB_COST_AMT/NULLIF(
		 SUM(RMB_COST_AMT)OVER(PARTITION BY PERIOD_YEAR,CODE_ATTRIBUTES,'||V_OTHER_DIM_PART||'SPART_CODE),0) AS WEIGHT_RATE,
		 PERIOD_YEAR,
         CODE_ATTRIBUTES,
            SOFTWARE_MARK
    FROM BASE_AMT
  UNION ALL
  --全量路径一主力编码,编码类型写死
  SELECT '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||'
		 RMB_COST_AMT/NULLIF(
		 SUM(RMB_COST_AMT)OVER(PARTITION BY PERIOD_YEAR,'||V_OTHER_DIM_PART||'SPART_CODE),0) AS WEIGHT_RATE,
		 PERIOD_YEAR,
		 ''全选''AS CODE_ATTRIBUTES,
            SOFTWARE_MARK
    FROM BASE_AMT;';
   
V_WEIGHT_SQL3:='  
--RAISE NOTICE ''权重插数'';
DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE CUSTOM_ID IS NULL AND VERSION_ID = '||V_VERSION||';

   INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     WEIGHT_RATE,
     PARENT_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     CODE_ATTRIBUTES,
      SOFTWARE_MARK)
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_YEAR,
           SPART_CODE AS GROUP_CODE,
           SPART_CN_NAME AS GROUP_CN_NAME,
		   ''SPART''AS GROUP_LEVEL,
           WEIGHT_RATE,
		   ''LV0'' AS PARENT_LEVEL,
           LV0_CODE AS PARENT_CODE,
           LV0_CN_NAME AS PARENT_CN_NAME,
		   ''Y''AS MAIN_FLAG,
		   ''PROD_SPART''AS VIEW_FLAG,
           '||V_OTHER_DIM_PART||' 
		   -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
           CODE_ATTRIBUTES,
           SOFTWARE_MARK
      FROM DM_BASE_WEIGHT_TEMP;';
	  
	  
--指数计算
V_COST_IDX_SQL:='
--RAISE NOTICE ''指数计算'';
DELETE FROM '||V_TO_COST_IDX_TABLE||' WHERE CUSTOM_ID IS NULL AND VERSION_ID = '||V_VERSION||';

INSERT INTO '||V_TO_COST_IDX_TABLE||'
  (VERSION_ID,
   BASE_PERIOD_ID,
   PERIOD_YEAR,
   PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_LEVEL,
   COST_INDEX,
   PARENT_CODE,
   PARENT_CN_NAME,
   MAIN_FLAG,
   VIEW_FLAG,
   '||V_OTHER_DIM_PART||' 
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   GRANULARITY_TYPE,
   CODE_ATTRIBUTES,
            SOFTWARE_MARK)
  SELECT '||V_VERSION||' AS VERSION_ID,
         '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         ''SPART'' AS GROUP_LEVEL,
         ''LV0'' AS PARENT_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.LV0_CODE AS PARENT_CODE,
         T1.LV0_CN_NAME AS PARENT_CN_NAME,
         ''Y'' AS MAIN_FLAG,
         ''PROD_SPART'' AS VIEW_FLAG,
         '||V_SQL_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         T1.CODE_ATTRIBUTES,
         T1.SOFTWARE_MARK
    FROM '||V_FROM_BASE_IDX_TABLE||' T1
    LEFT JOIN (SELECT SPART_CODE,
                      LV4_CODE,
                      '||V_OTHER_DIM_PART||' 
                      WEIGHT_RATE,
                      CODE_ATTRIBUTES,
					  SOFTWARE_MARK
                 FROM DM_BASE_WEIGHT_TEMP
                WHERE PERIOD_YEAR = '''||V_YEAR1||''') T2
      ON T1.GROUP_CODE = T2.SPART_CODE
     AND T1.LV4_CODE = T2.LV4_CODE
     AND T1.CODE_ATTRIBUTES = T2.CODE_ATTRIBUTES
     AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   '||V_JOIN_OTHER_DIM_PART||'
   WHERE T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
     AND T1.GROUP_LEVEL = ''SPART''
     AND T1.VIEW_FLAG = ''PROD_SPART''
     AND T1.MAIN_FLAG = ''Y''
   GROUP BY T1.PERIOD_ID,
            T1.GROUP_CODE,
            '||V_SQL_OTHER_DIM_PART||' 
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.GROUP_CN_NAME,
            T1.CODE_ATTRIBUTES,
            T1.SOFTWARE_MARK;';

--降成本指数计算 入参为IRB时触发
V_RED_IDX_SQL:='
--RAISE NOTICE ''降成本指数计算'';
DELETE FROM '||V_TO_RED_IDX_TABLE||' WHERE CUSTOM_ID IS NULL AND VERSION_ID = '||V_VERSION||';

  INSERT INTO '||V_TO_RED_IDX_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     PARENT_LEVEL,
     COST_INDEX,
	 YTD_COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     CODE_ATTRIBUTES,
     SOFTWARE_MARK)
    SELECT '||V_VERSION||' AS VERSION_ID,
           LEFT(T1.PERIOD_ID,4) AS PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           ''SPART'' AS GROUP_LEVEL,
           ''LV0'' AS PARENT_LEVEL,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		   SUM(T1.YTD_COST_INDEX * T2.WEIGHT_RATE) AS YTD_COST_INDEX,
           T2.LV0_CODE AS PARENT_CODE,
           T2.LV0_CN_NAME AS PARENT_CN_NAME,
           ''Y'' AS MAIN_FLAG,
           ''PROD_SPART'' AS VIEW_FLAG,
           '||V_SQL_OTHER_DIM_PART||'
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           T1.CODE_ATTRIBUTES,
           T1.SOFTWARE_MARK
      FROM '||V_FROM_BASE_RED_IDX_TABLE||' T1
      LEFT JOIN DM_BASE_WEIGHT_TEMP T2
        ON T1.GROUP_CODE = T2.SPART_CODE
       AND T1.LV4_PROD_RND_TEAM_CODE = T2.LV4_CODE
       AND T1.CODE_ATTRIBUTES = T2.CODE_ATTRIBUTES
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
	   AND T1.PERIOD_YEAR = SUBSTR(T2.PERIOD_YEAR,-4,4)
	   '||V_JOIN_OTHER_DIM_PART||'
     WHERE T1.GROUP_LEVEL = ''SPART''
       AND T1.VIEW_FLAG = ''PROD_SPART''
       AND T1.MAIN_FLAG = ''Y''
     GROUP BY T1.PERIOD_ID,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
			  '||V_SQL_OTHER_DIM_PART||'
			  T2.LV0_CODE,
			  T2.LV0_CN_NAME,
              T1.CODE_ATTRIBUTES,
              T1.SOFTWARE_MARK;';

--同环比插数			  
V_MONTH_RATE_SQL:='
--RAISE NOTICE ''同环比'';
DELETE FROM '||V_TO_MONTH_RATE_TABLE||' WHERE CUSTOM_ID IS NULL AND VERSION_ID = '||V_VERSION||';

--同环比计算
  WITH LEV_INDEX AS
   (SELECT PERIOD_ID,
           SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           '||V_OTHER_DIM_PART||' 
		   CODE_ATTRIBUTES,
		   SOFTWARE_MARK
      FROM '||V_TO_COST_IDX_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'),
  
  BASE_YOY AS
   (SELECT PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           '||V_OTHER_DIM_PART||' 
		   LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' GROUP_CODE, GROUP_LEVEL, PARENT_CODE, NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' GROUP_CODE, GROUP_LEVEL, PARENT_CODE, NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' GROUP_CODE, GROUP_LEVEL, PARENT_CODE, NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY '||V_OTHER_DIM_PART||' GROUP_CODE, GROUP_LEVEL, PARENT_CODE, NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK ORDER BY PERIOD_ID) AS POP_COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           CODE_ATTRIBUTES,
           SOFTWARE_MARK
      FROM LEV_INDEX) 
   INSERT INTO '||V_TO_MONTH_RATE_TABLE||'
     (VERSION_ID,
      PERIOD_YEAR,
      PERIOD_ID,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      PARENT_LEVEL,
      RATE,
      RATE_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      MAIN_FLAG,
      VIEW_FLAG,
      '||V_OTHER_DIM_PART||' 
	  CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG,
      GRANULARITY_TYPE,
      CODE_ATTRIBUTES,
      SOFTWARE_MARK)
  SELECT '||V_VERSION||' AS VERSION_ID,
         LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
		 ''LV0'' AS PARENT_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
		 ''YOY'' AS RATE_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
		 ''Y'' AS MAIN_FLAG,
		 ''PROD_SPART'' AS VIEW_FLAG,
         '||V_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N'' AS DEL_FLAG,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT '||V_VERSION||' AS VERSION_ID,
         LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
		 ''LV0'' AS PARENT_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
		 ''POP'' AS RATE_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
		 ''Y'' AS MAIN_FLAG,
		 ''PROD_SPART'' AS VIEW_FLAG,
		 '||V_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N'' AS DEL_FLAG,
         '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
         CODE_ATTRIBUTES,
         SOFTWARE_MARK
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL;';
   
 
--执行判断 
  IF F_GRANULARITY_TYPE = 'IRB' THEN
  --产业目录触发降成本指数SPART虚化
    V_EXECUTE_SQL := V_AMT_SQL || V_WEIGHT_SQL1 || V_WEIGHT_SQL2 ||
                     V_WEIGHT_SQL3 || V_COST_IDX_SQL || V_MONTH_RATE_SQL ||
                     V_RED_IDX_SQL;
  ELSE
    V_EXECUTE_SQL := V_AMT_SQL || V_WEIGHT_SQL1 || V_WEIGHT_SQL2 ||
                     V_WEIGHT_SQL3 || V_COST_IDX_SQL || V_MONTH_RATE_SQL;
  END IF;
 
    --写入日志
  V_SP_NUM := V_SP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => '计算前,成本类型：'||f_cost_type||'PBI:'||F_GRANULARITY_TYPE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_EXECUTE_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
 
  EXECUTE V_EXECUTE_SQL;

   --写入日志
  V_SP_NUM := V_SP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => '计算完成,成本类型：'||f_cost_type||'PBI:'||F_GRANULARITY_TYPE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_EXECUTE_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
  
RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_SP_NUM||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

