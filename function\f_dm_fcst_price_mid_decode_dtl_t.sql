-- Name: f_dm_fcst_price_mid_decode_dtl_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mid_decode_dtl_t(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：20241109
创建人  ：qwx1110218
背景描述：SPART层级月累计收敛表，只保留1个版本，需要关联月累计TOP_SPART表（DM_FCST_PRICE_TOP_SPART_INFO_T，取IS_TOP_FLAG=Y，PERIOD_YEAR取最新年份）只取Top-Spart的数据，计算月累计的均价；
参数描述：输出参数： x_result_status 运行状态返回值 'SUCCESS'为成功，'FAILED'为失败
事例    ：select fin_dm_opt_foi.f_dm_fcst_price_mid_decode_dtl_t()
变更记录：

*/


declare
	v_sp_name     varchar(200) := 'fin_dm_opt_foi.f_dm_fcst_price_mid_decode_dtl_t';
	v_to_table    varchar(100) := 'fin_dm_opt_foi.dm_fcst_price_mid_decode_dtl_t';  -- 目标表
	v_annual_version_id  bigint;       -- 年度版本号
	v_month_version_id  bigint;        -- 月度版本号
	v_sql         text;
	v_step_num    int;
	v_cn          int;


begin

	x_result_status := 'SUCCESS';        --1表示成功
	v_step_num = 1;

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '函数 '||v_sp_name||' 开始运行',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  -- 获取版本  
  -- 获取月度版本
  select version_id into v_month_version_id
    from fin_dm_opt_foi.dm_fcst_price_version_info_t
   where del_flag = 'N'
     and status = 1
     and upper(data_type) = 'MONTH'
   order by last_update_date desc
   limit 1
  ;
  
  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '月度版本： '||v_month_version_id,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );
  
  -- 获取年度版本
  select version_id into v_annual_version_id
    from fin_dm_opt_foi.dm_fcst_price_version_info_t
   where del_flag = 'N'
     and status = 1
     and upper(data_type) = 'ANNUAL'
   order by last_update_date desc
   limit 1
  ;
  
  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '年度版本：'||v_annual_version_id,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );


  -- 清理数据
  truncate table fin_dm_opt_foi.dm_fcst_price_mid_decode_dtl_t;
  
  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '清理对应版本的数据，版本号：'||v_month_version_id||'，数据量：'||sql%rowcount,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  -- 数据入到目标表
  insert into fin_dm_opt_foi.dm_fcst_price_mid_decode_dtl_t(
         version_id                         -- 版本ID
       , period_year                        -- 会计年
       , period_id                          -- 会计月
       , bg_code                            -- BG编码
       , bg_cn_name                         -- BG中文名称
       , lv0_prod_list_code                 -- 产品LV0编码
       , lv1_prod_list_code                 -- 产品LV1编码
       , lv2_prod_list_code                 -- 产品LV2编码
       , lv3_prod_list_code                 -- 产品LV3编码
       , lv4_prod_list_code                 -- 产品LV4编码
       , lv0_prod_list_cn_name              -- 产品LV0中文名称
       , lv1_prod_list_cn_name              -- 产品LV1中文名称
       , lv2_prod_list_cn_name              -- 产品LV2中文名称
       , lv3_prod_list_cn_name              -- 产品LV3中文名称
       , lv4_prod_list_cn_name              -- 产品LV4中文名称
       , spart_code                         -- SPART编码
       , spart_cn_name                      -- SPART中文名称
       , oversea_flag                       -- 国内海外标识
       , region_code                        -- 地区部编码
       , region_cn_name                     -- 地区部名称
       , repoffice_code                     -- 代表处编码
       , repoffice_cn_name                  -- 代表处名称
       , sign_top_cust_category_code        -- 签约客户_大T系统部编码
       , sign_top_cust_category_cn_name     -- 签约客户_大T系统部名称
       , sign_subsidiary_custcatg_cn_name   -- 签约客户_子网系统部名称
       , view_flag                          -- 视角标识，用于区分不同视角下的数据()
       , usd_pnp_amt                        -- PNP(CNP)_美元
       , rmb_pnp_amt                        -- PNP(CNP)_人民币
       , usd_pnp_avg                        -- PNP(CNP)均价_美元
       , rmb_pnp_avg                        -- PNP(CNP)均价_人民币
       , created_by                         -- 创建人
       , creation_date                      -- 创建时间
       , last_updated_by                    -- 修改人
       , last_update_date                   -- 修改时间
       , del_flag                           -- 删除标识(未删除：N，已删除：Y)
  )
  -- 取Top-Spart数据，不需要关注是哪年的Top-Spart
  with max_period_year_tmp as(
  select max(period_year) as max_period_year
    from fin_dm_opt_foi.dm_fcst_price_top_spart_info_t  -- 月累计TOP_SPART表
   where version_id = v_month_version_id
     and del_flag = 'N'
  ),
  price_top_spart_info_tmp as(
  select distinct t1.period_year
       , t1.bg_code                           -- BG编码
       , t1.bg_cn_name                        -- BG中文名称
       , t1.lv0_prod_list_code                -- 产品LV0编码
       , t1.lv1_prod_list_code                -- 产品LV1编码
       , t1.lv2_prod_list_code                -- 产品LV2编码
       , t1.lv3_prod_list_code                -- 产品LV3编码
       , t1.lv4_prod_list_code                -- 产品LV4编码
       , t1.lv0_prod_list_cn_name             -- 产品LV0中文名称
       , t1.lv1_prod_list_cn_name             -- 产品LV1中文名称
       , t1.lv2_prod_list_cn_name             -- 产品LV2中文名称
       , t1.lv3_prod_list_cn_name             -- 产品LV3中文名称
       , t1.lv4_prod_list_cn_name             -- 产品LV4中文名称
       , t1.top_spart_code                    -- TOP_SPART编码
       , t1.top_spart_cn_name                 -- TOP_SPART中文名称
       , t1.oversea_flag                      -- 国内海外标识
       , t1.region_code                       -- 地区部编码
       , t1.region_cn_name                    -- 地区部名称
       , t1.repoffice_code                    -- 代表处编码
       , t1.repoffice_cn_name                 -- 代表处名称
       , t1.sign_top_cust_category_code       -- 签约客户_大T系统部编码
       , t1.sign_top_cust_category_cn_name    -- 签约客户_大T系统部名称
       , t1.sign_subsidiary_custcatg_cn_name  -- 签约客户_子网系统部名称
       , t1.view_flag                         -- 视角标识，用于区分不同视角下的数据()
    from fin_dm_opt_foi.dm_fcst_price_top_spart_info_t t1  -- 月累计TOP_SPART表（月度版本号）
    join max_period_year_tmp t2
      on t1.period_year = t2.max_period_year
   where upper(t1.is_top_flag) = 'Y'  -- 是否TOP标识（Y：TOP、N：非TOP）
     and t1.version_id = v_month_version_id
     and t1.del_flag = 'N'
  ),
  -- 只取Top-Spart的数据
  price_mid_mon_spart_tmp as(
  select t1.period_year                       -- 会计年
       , t1.period_id                         -- 会计月
       , t1.bg_code                           -- BG编码
       , t1.bg_cn_name                        -- BG中文名称
       , t1.lv0_prod_list_code                -- LV0重量级团队编码
       , t1.lv0_prod_list_cn_name             -- LV0重量级团队中文名称
       , t1.lv1_prod_list_code                -- LV1重量级团队编码
       , t1.lv1_prod_list_cn_name             -- LV1重量级团队中文名称
       , t1.lv2_prod_list_code                -- LV2重量级团队编码
       , t1.lv2_prod_list_cn_name             -- LV2重量级团队中文名称
       , t1.lv3_prod_list_code                -- LV3重量级团队编码
       , t1.lv3_prod_list_cn_name             -- LV3重量级团队中文名称
       , t1.lv4_prod_list_code                -- LV3.5重量级团队编码
       , t1.lv4_prod_list_cn_name             -- LV3.5重量级团队中文名称
       , t1.spart_code                        -- SPART编码
       , t1.spart_cn_name                     -- SPART中文名称
       , t1.oversea_flag                      -- 国内海外标识
       , t1.region_code                       -- 地区部编码
       , t1.region_cn_name                    -- 地区部名称
       , t1.repoffice_code                    -- 代表处编码
       , t1.repoffice_cn_name                 -- 代表处名称
       , t1.sign_top_cust_category_code       -- 签约客户_大T系统部编码
       , t1.sign_top_cust_category_cn_name    -- 签约客户_大T系统部名称
       , t1.sign_subsidiary_custcatg_cn_name  -- 签约客户_子网系统部名称
       , t1.view_flag                         -- 视角标识，用于区分不同视角下的数据(地代办 LOCAL_AGENT（国内海外、地区部、代表处）；系统部 SYS_DEPT（大T系统、子网系统）)
       , t1.enable_flag                       -- 有效标识（Y：有效数据、N：无效数据）
       , t1.pnp_qty                           -- PNP数量
       , t1.usd_pnp_amt                       -- PNP(CNP)_美元
       , t1.rmb_pnp_amt                       -- PNP(CNP)_人民币
    from fin_dm_opt_foi.dm_fcst_price_mid_mon_spart_t t1  -- SPART层级月卷积汇总表（年度版本号）
    join price_top_spart_info_tmp t2  -- t2表没数据，暂时先注释
      on t1.bg_code = t2.bg_code
     and t1.lv0_prod_list_code = t2.lv0_prod_list_code
     and t1.lv1_prod_list_code = t2.lv1_prod_list_code
     and t1.lv2_prod_list_code = t2.lv2_prod_list_code
     and t1.lv3_prod_list_code = t2.lv3_prod_list_code
     and t1.lv4_prod_list_code = t2.lv4_prod_list_code
     and t1.spart_code = t2.top_spart_code
     and nvl(t1.oversea_flag,'A') = nvl(t2.oversea_flag,'A')
     and nvl(t1.region_code,'A') = nvl(t2.region_code,'A')
     and nvl(t1.repoffice_code,'A') = nvl(t2.repoffice_code,'A')
     and nvl(t1.sign_top_cust_category_code,'A') = nvl(t2.sign_top_cust_category_code,'A')
     and nvl(t1.sign_subsidiary_custcatg_cn_name,'A') = nvl(t2.sign_subsidiary_custcatg_cn_name,'A')
     and t1.view_flag = t2.view_flag
   where t1.del_flag = 'N'
     and t1.version_id = v_annual_version_id
     and t1.enable_flag = 'Y'  -- 只取有效数据
  )
  -- 计算月累计的均价
  select v_month_version_id as version_id  -- 版本ID
       , period_year                       -- 会计年
       , period_id                         -- 会计月
       , bg_code                           -- BG编码
       , bg_cn_name                        -- BG中文名称
       , lv0_prod_list_code                -- LV0重量级团队编码
       , lv1_prod_list_code                -- LV1重量级团队编码
       , lv2_prod_list_code                -- LV2重量级团队编码
       , lv3_prod_list_code                -- LV3重量级团队编码
       , lv4_prod_list_code                -- LV4重量级团队编码
       , lv0_prod_list_cn_name             -- LV0重量级团队中文名称
       , lv1_prod_list_cn_name             -- LV1重量级团队中文名称
       , lv2_prod_list_cn_name             -- LV2重量级团队中文名称
       , lv3_prod_list_cn_name             -- LV3重量级团队中文名称
       , lv4_prod_list_cn_name             -- LV4重量级团队中文名称
       , spart_code                        -- SPART编码
       , spart_cn_name                     -- SPART中文名称
       , oversea_flag                      -- 国内海外标识
       , region_code                       -- 地区部编码
       , region_cn_name                    -- 地区部名称
       , repoffice_code                    -- 代表处编码
       , repoffice_cn_name                 -- 代表处名称
       , sign_top_cust_category_code       -- 签约客户_大T系统部编码
       , sign_top_cust_category_cn_name    -- 签约客户_大T系统部名称
       , sign_subsidiary_custcatg_cn_name  -- 签约客户_子网系统部名称
       , view_flag                         -- 视角标识，用于区分不同视角下的数据()
       , usd_pnp_amt                       -- PNP(CNP)_美元
       , rmb_pnp_amt                       -- PNP(CNP)_人民币
       , (case when pnp_qty = 0 then 0 else usd_pnp_amt/pnp_qty end) as usd_pnp_avg -- PNP(CNP)均价_美元
       , (case when pnp_qty = 0 then 0 else rmb_pnp_amt/pnp_qty end) as rmb_pnp_avg -- PNP(CNP)均价_人民币
       , -1 as created_by
			 , current_timestamp as creation_date
			 , -1 as last_updated_by
			 , current_timestamp as last_update_date
			 , 'N' as del_flag
    from price_mid_mon_spart_tmp
  ;

  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => '数据入到目标表，数据量：'||sql%rowcount,--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );


  --收集统计信息
	v_sql := 'analyse '||v_to_table;
	execute v_sql;
  
  v_step_num := v_step_num+1;
  perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
      f_sp_name => v_sp_name,    --sp名称
      f_step_num => v_step_num,
      f_cal_log_desc => v_to_table||' 目标表统计信息收集完成，运行结束！',--日志描述
      f_formula_sql_txt  => v_sql,
      f_dml_row_count => sql%rowcount,
      f_result_status => x_result_status,
      f_errbuf => 'SUCCESS'
  );

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foi_cal_log_t(
         f_sp_name => v_sp_name,    -- sp名称
         f_step_num => v_step_num,
         f_cal_log_desc => v_sp_name||'：运行错误',-- 日志描述
         f_formula_sql_txt  => v_sql,
         f_dml_row_count => sql%rowcount,
         f_result_status => '0',
         f_errbuf => sqlstate  -- 错误编码
      ) ;

      x_result_status := 'FAILED';

end
$$
/

