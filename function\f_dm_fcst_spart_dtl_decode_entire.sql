-- Name: f_dm_fcst_spart_dtl_decode_entire; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_spart_dtl_decode_entire(f_cost_type character varying, f_granularity_type character varying, f_view_flag character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建人：lwx1165532
最后修改人：TWX1139790
背景描述： 求含脏数据的均价
		   关联主力编码表
事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_SPART_DTL_DECODE_ENTIRE()
变更记录-202503：
限制来源表版本号，避免取到多余数据
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FCST_SPART_DTL_DECODE_ENTIRE'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-2)||'01','YYYYMM');  --两年前首月
  
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑

  
  V_LV0_PROD_PARA VARCHAR(150);
  V_LV1_PROD_PARA VARCHAR(150);  
  V_LV2_PROD_PARA VARCHAR(150); 
  V_LV3_PROD_PARA VARCHAR(150); 
  V_LV4_PROD_PARA VARCHAR(150); 
  V_LV0_PROD_CODE VARCHAR(150);
  V_LV1_PROD_CODE VARCHAR(150);  
  V_LV2_PROD_CODE VARCHAR(150); 
  V_LV3_PROD_CODE VARCHAR(150); 
  V_LV4_PROD_CODE VARCHAR(150);
  V_INSERT_LV0_PROD_CODE VARCHAR(150);
  V_INSERT_LV1_PROD_CODE VARCHAR(150);  
  V_INSERT_LV2_PROD_CODE VARCHAR(150); 
  V_INSERT_LV3_PROD_CODE VARCHAR(150); 
  V_INSERT_LV4_PROD_CODE VARCHAR(150);
  
  V_DIM_INSERT_LV1_PROD_CODE VARCHAR(150);  
  V_DIM_INSERT_LV2_PROD_CODE VARCHAR(150); 
  V_DIM_INSERT_LV3_PROD_CODE VARCHAR(150); 
  V_DIM_INSERT_LV4_PROD_CODE VARCHAR(150);
  
  V_FROM_TABLE VARCHAR(100); 
  V_TO_TABLE VARCHAR(100);
  V_JOIN_TABLE VARCHAR(100);
 
  V_IN_LV0_PROD_PARA VARCHAR(150);
  V_IN_LV1_PROD_PARA VARCHAR(150);  
  V_IN_LV2_PROD_PARA VARCHAR(150); 
  V_IN_LV3_PROD_PARA VARCHAR(150); 
  V_IN_LV4_PROD_PARA VARCHAR(150);
  
  
  V_DIMENSION_PARA VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_PARA VARCHAR(200); 
  V_DIMENSION_SUB_DETAIL_PARA VARCHAR(200); 
  
  
  V_DIMENSION_CODE VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(200); 
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(200); 
  
  
  V_IN_DIMENSION_PARA VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_PARA VARCHAR(200); 
  V_IN_DIMENSION_SUB_DETAIL_PARA VARCHAR(200); 
  

  
   
  V_SPART_CODE VARCHAR(20);
  V_IN_SPART_CODE VARCHAR(20);
  V_SPART_CN_NAME VARCHAR(20);
  V_SPART_CN_NAME2 VARCHAR(40);
  V_IN_SPART_CN_NAME2 VARCHAR(40);
  
  V_RMB_AMT TEXT;
  V_SUM_AMT TEXT;
  V_AVG_AMT TEXT;
  
  V_WHERE_PARA TEXT ;
  V_SOFTWARE VARCHAR(200);
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);
   

  
   --判断PBI维度和成本类型选择来源表,目标表,关联表,金额字段
  IF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_MID_MON_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_DECODE_ENTIRE_T';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_MID_MON_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_DECODE_ENTIRE_T';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_MID_MON_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_DECODE_ENTIRE_T';
	 
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_MID_MON_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_DECODE_ENTIRE_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_MID_MON_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_DECODE_ENTIRE_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_MID_MON_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_DECODE_ENTIRE_T';
	 
  ELSE 
	RETURN '入参有误';
  END IF;
  
  
  
    --判断路径，选择量纲字段
  IF F_VIEW_FLAG = 'DIMENSION' THEN 
   V_DIMENSION_PARA := 'DIMENSION_CODE,DIMENSION_CN_NAME,';
   V_DIMENSION_SUBCATEGORY_PARA := 'DIMENSION_SUBCATEGORY_CODE ,DIMENSION_SUBCATEGORY_CN_NAME,'; 
   V_DIMENSION_SUB_DETAIL_PARA := 'DIMENSION_SUB_DETAIL_CODE ,DIMENSION_SUB_DETAIL_CN_NAME,';  
   
   V_IN_DIMENSION_PARA := 'A.DIMENSION_CODE,A.DIMENSION_CN_NAME,';
   V_IN_DIMENSION_SUBCATEGORY_PARA := 'A.DIMENSION_SUBCATEGORY_CODE ,A.DIMENSION_SUBCATEGORY_CN_NAME,'; 
   V_IN_DIMENSION_SUB_DETAIL_PARA := 'A.DIMENSION_SUB_DETAIL_CODE ,A.DIMENSION_SUB_DETAIL_CN_NAME,';  
   
   V_DIMENSION_CODE := 'DIMENSION_CODE,';
   V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE ,'; 
   V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE ,'; 
   
   V_SPART_CODE := '';
   V_SPART_CN_NAME := '';
   V_SPART_CN_NAME2 := '';
   
   V_IN_SPART_CODE := '';
   V_IN_SPART_CN_NAME2 := '';
   
   
   V_WHERE_PARA := ' WHERE T.VIEW_FLAG = ''DIMENSION'' ' ;
   
   --软硬件标识
   V_SOFTWARE := 'SOFTWARE_MARK,';
   
  ELSIF F_VIEW_FLAG = 'PROD_SPART' AND F_COST_TYPE = 'PSP' THEN 
   V_DIMENSION_PARA := '';
   V_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_DIMENSION_SUB_DETAIL_PARA := '';  
   
   V_IN_DIMENSION_PARA := '';
   V_IN_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_IN_DIMENSION_SUB_DETAIL_PARA := '';  
   
   V_DIMENSION_CODE := '';
   V_DIMENSION_SUBCATEGORY_CODE := ''; 
   V_DIMENSION_SUB_DETAIL_CODE := '';
   
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME := 'SPART_CN_NAME ,';
	V_SPART_CN_NAME2 := 'SPART_CODE AS SPART_CN_NAME ,';
	
	V_IN_SPART_CODE := 'A.SPART_CODE,';
    V_IN_SPART_CN_NAME2 := 'A.SPART_CODE AS SPART_CN_NAME,';
	
	V_WHERE_PARA := ' WHERE T.VIEW_FLAG = ''PROD_SPART'' '; --PSP成本不做剔除
	
	V_SOFTWARE := 'SOFTWARE_MARK,';	
	
			
	  ELSIF F_VIEW_FLAG = 'PROD_SPART' AND F_COST_TYPE = 'STD' THEN 
   V_DIMENSION_PARA := '';
   V_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_DIMENSION_SUB_DETAIL_PARA := '';  
   
   V_IN_DIMENSION_PARA := '';
   V_IN_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_IN_DIMENSION_SUB_DETAIL_PARA := '';  
   
   V_DIMENSION_CODE := '';
   V_DIMENSION_SUBCATEGORY_CODE := ''; 
   V_DIMENSION_SUB_DETAIL_CODE := '';
   
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME := 'SPART_CN_NAME ,';
	V_SPART_CN_NAME2 := 'SPART_CODE AS SPART_CN_NAME ,';
	
	V_IN_SPART_CODE := 'A.SPART_CODE,';
    V_IN_SPART_CN_NAME2 := 'A.SPART_CODE AS SPART_CN_NAME,';
	
	V_WHERE_PARA := ' WHERE T.VIEW_FLAG = ''PROD_SPART''
			AND ONLY_SPART_FLAG = ''N'' ';		
	
	V_SOFTWARE := 'SOFTWARE_MARK,';	

  END IF;
  
  
  
  
  
  
  IF F_COST_TYPE = 'PSP' THEN 
      
	  V_RMB_AMT := 'RMB_COST_AMT,';
		
      V_SUM_AMT := ' SUM(RMB_COST_AMT) AS  RMB_COST_AMT,';
	  
	  V_AVG_AMT := ' SUM(T.RMB_COST_AMT) / SUM(T.PROD_QTY) ';
	  
  ELSIF F_COST_TYPE = 'STD' THEN 
      
	  V_RMB_AMT := 'TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')) AS RMB_COST_AMT,';
	  
	  V_SUM_AMT := ' GS_ENCRYPT(SUM(T.RMB_COST_AMT),'''||f_keystr||''',''AES128'', ''CBC'', ''SHA256'')  AS RMB_COST_AMT,';
  
	  V_AVG_AMT := ' GS_ENCRYPT(SUM(T.RMB_COST_AMT) / SUM(T.PROD_QTY),'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') ';
  
  END IF;  
 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
 
 
 
 --判断PBI维度选择PBI字段	
  IF  F_GRANULARITY_TYPE = 'IRB' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_RND_TEAM_CODE ,LV0_PROD_RD_TEAM_CN_NAME, ';
	V_LV1_PROD_PARA :='	LV1_PROD_RND_TEAM_CODE ,LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_LV2_PROD_PARA :=' LV2_PROD_RND_TEAM_CODE ,LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_LV3_PROD_PARA :=' LV3_PROD_RND_TEAM_CODE ,LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_LV4_PROD_PARA :=' LV4_PROD_RND_TEAM_CODE ,LV4_PROD_RD_TEAM_CN_NAME, '; 
    V_LV0_PROD_CODE :='LV0_PROD_RND_TEAM_CODE,';
	V_LV1_PROD_CODE :='LV1_PROD_RND_TEAM_CODE,';  
	V_LV2_PROD_CODE :='LV2_PROD_RND_TEAM_CODE,'; 
	V_LV3_PROD_CODE :='LV3_PROD_RND_TEAM_CODE,'; 
	V_LV4_PROD_CODE :='LV4_PROD_RND_TEAM_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_RND_TEAM_CODE = B.LV1_PROD_RND_TEAM_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_RND_TEAM_CODE = B.LV4_PROD_RND_TEAM_CODE';
	
	
	V_DIM_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_RND_TEAM_CODE = M.LV1_PROD_RND_TEAM_CODE';  
	V_DIM_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_RND_TEAM_CODE = M.LV2_PROD_RND_TEAM_CODE'; 
	V_DIM_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_RND_TEAM_CODE = M.LV3_PROD_RND_TEAM_CODE'; 
	V_DIM_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_RND_TEAM_CODE = M.LV4_PROD_RND_TEAM_CODE';
	
	
	V_IN_LV0_PROD_PARA :=' A.LV0_PROD_RND_TEAM_CODE ,A.LV0_PROD_RD_TEAM_CN_NAME, ';
	V_IN_LV1_PROD_PARA :=' A.LV1_PROD_RND_TEAM_CODE ,A.LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_IN_LV2_PROD_PARA :=' A.LV2_PROD_RND_TEAM_CODE ,A.LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_IN_LV3_PROD_PARA :=' A.LV3_PROD_RND_TEAM_CODE ,A.LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_IN_LV4_PROD_PARA :=' A.LV4_PROD_RND_TEAM_CODE ,A.LV4_PROD_RD_TEAM_CN_NAME, ';
	
	
  ELSIF  F_GRANULARITY_TYPE = 'INDUS' THEN 
	V_LV0_PROD_PARA :=' LV0_INDUSTRY_CATG_CODE ,LV0_INDUSTRY_CATG_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_INDUSTRY_CATG_CODE ,LV1_INDUSTRY_CATG_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_INDUSTRY_CATG_CODE ,LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_INDUSTRY_CATG_CODE ,LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_INDUSTRY_CATG_CODE ,LV4_INDUSTRY_CATG_CN_NAME,'; 
	V_LV0_PROD_CODE :='LV0_INDUSTRY_CATG_CODE,';
	V_LV1_PROD_CODE :='LV1_INDUSTRY_CATG_CODE,';  
	V_LV2_PROD_CODE :='LV2_INDUSTRY_CATG_CODE,'; 
	V_LV3_PROD_CODE :='LV3_INDUSTRY_CATG_CODE,'; 
	V_LV4_PROD_CODE :='LV4_INDUSTRY_CATG_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_INDUSTRY_CATG_CODE = B.LV0_INDUSTRY_CATG_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_INDUSTRY_CATG_CODE = B.LV1_INDUSTRY_CATG_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_INDUSTRY_CATG_CODE = B.LV2_INDUSTRY_CATG_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_INDUSTRY_CATG_CODE = B.LV3_INDUSTRY_CATG_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_INDUSTRY_CATG_CODE = B.LV4_INDUSTRY_CATG_CODE';
	
	V_DIM_INSERT_LV1_PROD_CODE :=' AND A.LV1_INDUSTRY_CATG_CODE = M.LV1_INDUSTRY_CATG_CODE';  
	V_DIM_INSERT_LV2_PROD_CODE :=' AND A.LV2_INDUSTRY_CATG_CODE = M.LV2_INDUSTRY_CATG_CODE'; 
	V_DIM_INSERT_LV3_PROD_CODE :=' AND A.LV3_INDUSTRY_CATG_CODE = M.LV3_INDUSTRY_CATG_CODE'; 
	V_DIM_INSERT_LV4_PROD_CODE :=' AND A.LV4_INDUSTRY_CATG_CODE = M.LV4_INDUSTRY_CATG_CODE';
	
	V_IN_LV0_PROD_PARA :=' A.LV0_INDUSTRY_CATG_CODE ,A.LV0_INDUSTRY_CATG_CN_NAME,';
	V_IN_LV1_PROD_PARA :=' A.LV1_INDUSTRY_CATG_CODE ,A.LV1_INDUSTRY_CATG_CN_NAME,';  
	V_IN_LV2_PROD_PARA :=' A.LV2_INDUSTRY_CATG_CODE ,A.LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_IN_LV3_PROD_PARA :=' A.LV3_INDUSTRY_CATG_CODE ,A.LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_IN_LV4_PROD_PARA :=' A.LV4_INDUSTRY_CATG_CODE ,A.LV4_INDUSTRY_CATG_CN_NAME,'; 
	
  ELSIF  F_GRANULARITY_TYPE = 'PROD' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,'; 
	V_LV0_PROD_CODE :='LV0_PROD_LIST_CODE,';
	V_LV1_PROD_CODE :='LV1_PROD_LIST_CODE,';  
	V_LV2_PROD_CODE :='LV2_PROD_LIST_CODE,'; 
	V_LV3_PROD_CODE :='LV3_PROD_LIST_CODE,'; 
	V_LV4_PROD_CODE :='LV4_PROD_LIST_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE';
	
	
	V_DIM_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_LIST_CODE = M.LV1_PROD_LIST_CODE';  
	V_DIM_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_LIST_CODE = M.LV2_PROD_LIST_CODE'; 
	V_DIM_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_LIST_CODE = M.LV3_PROD_LIST_CODE'; 
	V_DIM_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_LIST_CODE = M.LV4_PROD_LIST_CODE';
	
	V_IN_LV0_PROD_PARA :=' A.LV0_PROD_LIST_CODE,A.LV0_PROD_LIST_CN_NAME,';
	V_IN_LV1_PROD_PARA :='	A.LV1_PROD_LIST_CODE,A.LV1_PROD_LIST_CN_NAME,';  
	V_IN_LV2_PROD_PARA :=' A.LV2_PROD_LIST_CODE,A.LV2_PROD_LIST_CN_NAME,'; 
	V_IN_LV3_PROD_PARA :=' A.LV3_PROD_LIST_CODE,A.LV3_PROD_LIST_CN_NAME,'; 
	V_IN_LV4_PROD_PARA :=' A.LV4_PROD_LIST_CODE,A.LV4_PROD_LIST_CN_NAME,'; 
	
	
  ELSE 
	NULL ;
  END IF;
 

 
 
 
  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM  '||V_TO_TABLE||' WHERE VIEW_FLAG = '''||F_VIEW_FLAG||''' ';
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
 --创建临时表
            DROP TABLE IF EXISTS DECODE_TEMP;
            CREATE TEMPORARY TABLE DECODE_TEMP (
                PERIOD_ID NUMERIC,
				PERIOD_YEAR NUMERIC,
				REGION_CODE CHARACTER VARYING(50),
				REGION_CN_NAME CHARACTER VARYING(200),
				REPOFFICE_CODE CHARACTER VARYING(50),
				REPOFFICE_CN_NAME CHARACTER VARYING(200),
				BG_CODE CHARACTER VARYING(50),
				BG_CN_NAME CHARACTER VARYING(200),
				LV0_PROD_LIST_CODE CHARACTER VARYING(50),
				LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV1_PROD_LIST_CODE CHARACTER VARYING(50),
				LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV2_PROD_LIST_CODE CHARACTER VARYING(50),
				LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV3_PROD_LIST_CODE CHARACTER VARYING(50),
				LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV4_PROD_LIST_CODE CHARACTER VARYING(50),
				LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(100),
				LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
				LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV0_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV0_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV1_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV1_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV2_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV2_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV3_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV3_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV4_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV4_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				DIMENSION_CODE CHARACTER VARYING(500),
				DIMENSION_CN_NAME CHARACTER VARYING(200),
				DIMENSION_EN_NAME CHARACTER VARYING(200),
				DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
				DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(200),
				DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
				DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(200),
				PROD_QTY NUMERIC,
				RMB_COST_AMT NUMERIC,
				RMB_AVG_AMT NUMERIC,
				HW_CONTRACT_NUM CHARACTER VARYING(188),
				SPART_CODE CHARACTER VARYING(188),
				OVERSEA_FLAG CHARACTER VARYING(1),
				ONLY_SPART_FLAG VARCHAR(1),
				MAIN_FLAG VARCHAR(1),
				CODE_ATTRIBUTES VARCHAR(20),
				VIEW_FLAG VARCHAR(20),
				SOFTWARE_MARK VARCHAR(100)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE);

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '实际数补齐临时表创建完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
        

	
	
		
--插数进临时表，路径1需要剔除单SPART

IF F_VIEW_FLAG = 'PROD_SPART' THEN 
      V_SQL := 
   'INSERT INTO DECODE_TEMP(
                PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||'  
				PROD_QTY ,
				RMB_COST_AMT ,
				HW_CONTRACT_NUM,
				'||V_SPART_CODE||'
				OVERSEA_FLAG,
				ONLY_SPART_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				'||V_SOFTWARE||'
				VIEW_FLAG			  
				)
       SELECT 	PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||'
				 PROD_QTY,
				 '||V_RMB_AMT||'
				 HW_CONTRACT_NUM,
				 '||V_SPART_CODE||'
				OVERSEA_FLAG,
				ONLY_SPART_FLAG ,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				'||V_SOFTWARE||'
                VIEW_FLAG
            FROM '||V_FROM_TABLE ||' T
            '||V_WHERE_PARA||'
			AND VERSION_ID = '||V_VERSION_ID      -- 202503新增版本号取数限制
			;    
			DBMS_OUTPUT.PUT_LINE(V_SQL);
			EXECUTE IMMEDIATE V_SQL;
           
ELSIF F_VIEW_FLAG = 'DIMENSION' THEN      
	 
	    V_SQL := 
   'INSERT INTO DECODE_TEMP(
                PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||'  
				PROD_QTY ,
				RMB_COST_AMT ,
				HW_CONTRACT_NUM,
				'||V_SPART_CODE||'
				OVERSEA_FLAG,
				ONLY_SPART_FLAG,
				MAIN_FLAG,
			  CODE_ATTRIBUTES,
			  '||V_SOFTWARE||'
				VIEW_FLAG			  
				)
				
				SELECT 
				PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||'
				 SUM(PROD_QTY) AS PROD_QTY,
				 SUM(RMB_COST_AMT) AS RMB_COST_AMT,
				 HW_CONTRACT_NUM,
				 OVERSEA_FLAG,
				ONLY_SPART_FLAG ,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				'||V_SOFTWARE||'
                VIEW_FLAG
				FROM 
       (SELECT 	PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||'
				 PROD_QTY,
				 '||V_RMB_AMT||'
				 HW_CONTRACT_NUM,
				 '||V_SPART_CODE||'
				OVERSEA_FLAG,
				ONLY_SPART_FLAG ,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				'||V_SOFTWARE||'
                VIEW_FLAG
            FROM '||V_FROM_TABLE ||' T
            '||V_WHERE_PARA||'
			AND VERSION_ID = '||V_VERSION_ID||'      -- 202503新增版本号取数限制
             )
			 GROUP BY PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||'
				 HW_CONTRACT_NUM,
			     OVERSEA_FLAG,
				ONLY_SPART_FLAG ,
				MAIN_FLAG,
				'||V_SOFTWARE||'
				CODE_ATTRIBUTES,
				'||V_SPART_CODE||'
                VIEW_FLAG'
			;    
			DBMS_OUTPUT.PUT_LINE(V_SQL);
			EXECUTE IMMEDIATE V_SQL;
	 
    END IF;	
	
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入会话级临时表,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	
	
	
	
--插入均价数据到目标表
  V_SQL := 
  'INSERT INTO '|| V_TO_TABLE ||' 
		(
		VERSION_ID,
		PERIOD_ID ,
		PERIOD_YEAR ,
		REGION_CODE ,
		REGION_CN_NAME ,
		REPOFFICE_CODE ,
		REPOFFICE_CN_NAME ,
		BG_CODE ,
		BG_CN_NAME ,
		'||V_LV0_PROD_PARA 
		||V_LV1_PROD_PARA  
		||V_LV2_PROD_PARA 
		||V_LV3_PROD_PARA  
		||V_LV4_PROD_PARA
		||V_DIMENSION_PARA 
		||V_DIMENSION_SUBCATEGORY_PARA  
		||V_DIMENSION_SUB_DETAIL_PARA||'
		PROD_QTY ,
		RMB_COST_AMT,
		RMB_AVG_AMT,
		'||V_SPART_CODE
		||V_SPART_CN_NAME
		||' 
		OVERSEA_FLAG,
		ONLY_SPART_FLAG,
		MAIN_FLAG,
		CODE_ATTRIBUTES,
		VIEW_FLAG,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		'||V_SOFTWARE||'
		DEL_FLAG
		)
SELECT 	  '||V_VERSION_ID||',
		PERIOD_ID ,
		PERIOD_YEAR ,
		REGION_CODE ,
		REGION_CN_NAME ,
		REPOFFICE_CODE ,
		REPOFFICE_CN_NAME ,
		BG_CODE ,
		BG_CN_NAME ,
		'||V_LV0_PROD_PARA 
		||V_LV1_PROD_PARA  
		||V_LV2_PROD_PARA 
		||V_LV3_PROD_PARA  
		||V_LV4_PROD_PARA
		||V_DIMENSION_PARA 
		||V_DIMENSION_SUBCATEGORY_PARA  
		||V_DIMENSION_SUB_DETAIL_PARA||'
		SUM(PROD_QTY) AS PROD_QTY, 
		'||V_SUM_AMT||'
		CASE
		WHEN   SUM(T.RMB_COST_AMT) = 0 OR SUM(T.PROD_QTY) = 0 THEN
		NULL --聚合后, 有量无价或者有价无量的情况, 均价置空
		ELSE
		'||V_AVG_AMT||'
		END AS RMB_AVG_AMT,
		'||V_SPART_CODE
		||V_SPART_CN_NAME2||'  
		OVERSEA_FLAG,
		ONLY_SPART_FLAG,
		MAIN_FLAG,
		CODE_ATTRIBUTES,
		VIEW_FLAG,
		-1 AS CREATED_BY,
		CURRENT_TIMESTAMP AS CREATION_DATE,
		-1 AS LAST_UPDATED_BY,
		CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		'||V_SOFTWARE||'
		''N'' AS DEL_FLAG
FROM DECODE_TEMP T
GROUP BY 
		PERIOD_ID ,
		PERIOD_YEAR ,
		REGION_CODE ,
		REGION_CN_NAME ,
		REPOFFICE_CODE ,
		REPOFFICE_CN_NAME ,
		BG_CODE ,
		BG_CN_NAME ,	
		'||V_LV0_PROD_PARA 
		||V_LV1_PROD_PARA  
		||V_LV2_PROD_PARA 
		||V_LV3_PROD_PARA  
		||V_LV4_PROD_PARA
		||V_DIMENSION_PARA 
		||V_DIMENSION_SUBCATEGORY_PARA  
		||V_DIMENSION_SUB_DETAIL_PARA
		||V_SPART_CODE||' 
		OVERSEA_FLAG,
		ONLY_SPART_FLAG,
		MAIN_FLAG,
		CODE_ATTRIBUTES,
		'||V_SOFTWARE||'
		VIEW_FLAG'	;	  

				
	DBMS_OUTPUT.PUT_LINE(V_SQL);			
    EXECUTE IMMEDIATE V_SQL;    
	

  

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价实际数到'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

