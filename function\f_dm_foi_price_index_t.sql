-- Name: f_dm_foi_price_index_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_price_index_t(f_caliber_flag character varying, f_cate_version bigint, f_item_version bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最近更新时间:2024年6月26日15点56分
修改人:		黄心蕊
修改内容: 202407版本 新增华东采购与IAS
创建时间：2022-11-01
创建人  ：黄心蕊 hwx1187045
修改时间：2024-01-17
修改人  ：黄心蕊 hwx1187045
背景描述：JAVA控制权限需求，本版本增加专家团模块本身层级数据存储时，保留本层级的维度字段CODE和NAME
修改时间：2024-02-20
修改人  ：黄心蕊 hwx1187045
背景描述：202403版本需求，增加数字能源模块逻辑
参数描述：参数一(f_cate_version)：top品类清单表最新版本号
		  参数一(f_item_version)：导入通用版本号（规格品清单版本号）
		  参数三(x_success_flag)  ：运行状态返回值-成功或者失败
		  参数四(F_CALIBER_FLAG)：'I'为ICT，'E'为数字能源 --202403版本新增
--数字能源
来源表:  FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_WEIGHT_T --数字能源_月度页面权重表
		 FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T --数字能源_ITEM及供应商均价表
		 FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T --数字能源_品类组合页权重表
目标表:  FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T --数字能源_价格指数表
--生产采购
来源表:  FIN_DM_OPT_FOI.DM_FOI_MONTH_WEIGHT_T 	--生产采购_月度页面权重表
		 FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T  	--生产采购_ITEM及供应商均价表
		 FIN_DM_OPT_FOI.DM_FOI_CATE_GROUP_WEIGHT_T --生产采购_品类组合页权重表
目标表:  FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T  --生产采购_价格指数表

事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_PRICE_INDEX_T('E','') --初始化数字能源一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_PRICE_INDEX_T('I','') --初始化ICT一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_PRICE_INDEX_T('IAS','') --初始化IAS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_PRICE_INDEX_T('EAST_CHINA_PQC','') --初始化华东采购一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME               VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_PRICE_INDEX_T';
  V_VERSION               BIGINT;
  V_STEP_NUM              INT := 0;
  V_BASE_PERIOD_ID_SINGLE NUMBER DEFAULT( SELECT TO_NUMBER(TO_CHAR(NOW() + '-3 YEAR', 'YYYY') || '01'));
  V_YEAR                   INT DEFAULT(TO_NUMBER(TO_CHAR(NOW() + '-3 YEAR','YYYY'))); /*这里的4年的第一年的年份*/
  V_PART1_PUBLIC           TEXT := NULL; -- 配置调度的函数公共部分1
  V_PART1_PUBLIC_BAK       TEXT := NULL; -- 配置调度的函数公共部分2的备份变量
  V_VAR_PARA1              TEXT := NULL; -- 替换公共部分函数中特殊字符的变量
  V_VAR_PARA2              TEXT := NULL; -- 替换公共部分函数中特殊字符的变量
  V_VAR_PARA4              TEXT := NULL; -- 替换公共部分函数中特殊字符的变量
  V_EXECUTE_SQL            TEXT := NULL; -- 执行SQL
  V_LOOP_NUM               TEXT := NULL; -- 循环次数
  V_FILTER2                TEXT := NULL; -- 特殊筛选条件
  V_FILTER3                TEXT := NULL; -- 特殊筛选条件
  V_BASE_PERIOD_ID         TEXT := NULL; -- 基期会计期
  V_SQL_BASE_PERIOD_ID     TEXT := NULL; -- 基期会计期查询字段
  V_JOIN_TABLE3            TEXT := NULL; -- 基期指数关联条件
  V_CHILD                  TEXT := NULL; -- 子集字段
  V_BASE_RATE              TEXT := NULL; -- 子集依赖数值字段
  V_GROUP_CODE             TEXT := NULL; -- 本层级编码
  V_GROUP_NAME             TEXT := NULL; -- 本层级名称
  V_LV4                    TEXT := NULL; -- 模块层级编码名称
  V_LV3                    TEXT := NULL; -- 专家团层级编码名称
  V_LV2                    TEXT := NULL; -- LV2层级编码名称
  V_L3_CEG_CN_NAME         TEXT := NULL; -- 专家团层级名称
  V_L4_CEG_CN_NAME         TEXT := NULL; -- 模块层级名称
  V_SOURCE_TBALE           TEXT := NULL; -- 来源表
  V_TOP_TYPE               TEXT := NULL; -- 品类分类字段
  V_SQL_PARENT_CODE        TEXT := NULL; -- 上级编码及名称赋值
  V_SQL_GROUP_CODE         TEXT := NULL; -- 本层级编码赋值
  V_SQL_GROUP_CN_NAME      TEXT := NULL; -- 本层级名称赋值
  V_SQL_GROUPBY_CODE       TEXT := NULL; -- 
  V_SQL_LV4                TEXT := NULL;
  V_SQL_LV3                TEXT := NULL;
  V_SQL_LV2                TEXT := NULL;
  V_SQL_L3_CEG_CN_NAME     TEXT := NULL;
  V_SQL_L4_CEG_CN_NAME     TEXT := NULL;
  V_PRICE_INDEX            TEXT := NULL;
  V_SQL_TOP_TYPE           TEXT := NULL;
  V_CHILD_LEVEL            TEXT := NULL;
  V_GROUP_LEVEL            TEXT := NULL;
  V_JOIN_PARENT_CODE       TEXT := NULL;
  V_FILTER1                TEXT := NULL;
  V_BASE_PERIOD_AVG_TEMP   TEXT := NULL;
  V_SQL1                   TEXT := NULL;
  V_SQL2                   TEXT := NULL;
  V_SQL3                   TEXT := NULL;
  V_FROM_WEIGHT_TABLE      TEXT := NULL; --202403版本新增来源表及目标表判断
  V_FROM_AVG_TABLE         TEXT := NULL;
  V_FROM_SUP_AVG_TABLE     TEXT := NULL; --供应商下ITEM均价来源表
  V_TO_TABLE               TEXT := NULL;
  V_CALIBER_COLUMN         TEXT := NULL; --特殊字段 生产采购为是否含连续性影响,数字能源为是否含集团代采
  V_SQL_CALIBER_COLUMN     TEXT := NULL;
  V_CALIBER_COLUMN_BAC     TEXT := NULL; --特殊字段备份
  V_SQL_CALIBER_COLUMN_BAC TEXT := NULL;
  V_VERSION_TABLE          VARCHAR(200) := NULL;
  V_FROM_TYPE_WEIGHT_TABLE VARCHAR(200) := NULL; --TOP分类及品类分类权重表
  V_FILTER_WEIGHT          TEXT := NULL;
  V_BASE_RATE_BAC          TEXT := NULL;
  V_FROM_WEIGHT_TABLE_BAC  TEXT := NULL;
  V_FILTER1_BAC            TEXT := NULL;
  
  --202407版本 新增华东采购与IAS
  V_NUM_END       TEXT; --循环截止次数
  V_CALIBER_FLAG  TEXT;
  V_IN_CALIBER    TEXT;
  V_IAS_ECPQC_SQL TEXT;

BEGIN
/**********************************
  -- 将查询到的数据放到变量中的公共sql
  V_PART1_PUBLIC := '
                       SELECT VALUE 
                           FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                           WHERE ENABLE_FLAG = ''Y''
                           AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                         ';

  -- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
  IF F_CATE_VERSION IN (-1, 0) AND F_ITEM_VERSION IN (-1, 0) THEN
    V_EXECUTE_SQL := REPLACE(V_PART1_PUBLIC,
                             '$PARA_NAME$',
                             'VERSION_ID-ITEM'); -- 规格品版本号
    EXECUTE V_EXECUTE_SQL INTO V_VERSION;
    -- 业务在前台配置页面调整规格品数据时，JAVA传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号   
  ELSE
    V_VERSION := NVL(F_ITEM_VERSION, F_CATE_VERSION);
  END IF;
  
*****************************************/
  
--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  
   
  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NOT NULL THEN
    V_VERSION := F_ITEM_VERSION;
  ELSE
    IF F_CALIBER_FLAG = 'I' THEN
	V_VERSION_TABLE		:=' FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T ';
    ELSIF F_CALIBER_FLAG = 'E' THEN
	V_VERSION_TABLE		:=' FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T ';
    ELSIF F_CALIBER_FLAG = 'IAS' THEN		--202407版本 新增IAS
	V_VERSION_TABLE		:=' FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T ';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN	--202407版本 新增华东采购
	V_VERSION_TABLE		:=' FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T ';
	END IF ;
    V_EXECUTE_SQL:='
	SELECT VERSION_ID
	  FROM '||V_VERSION_TABLE||'
	 WHERE del_flag = ''N''
	   AND status = 1
	   AND UPPER(DATA_TYPE) = ''ITEM''
	   AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
	';
	EXECUTE V_EXECUTE_SQL INTO V_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
   
   
--根据不同数据源定义变量 202403版本
  IF F_CALIBER_FLAG = 'I' THEN
    V_FROM_WEIGHT_TABLE      := ' FIN_DM_OPT_FOI.DM_FOI_MONTH_WEIGHT_T '; --权重来源表
    V_FROM_TYPE_WEIGHT_TABLE := ' FIN_DM_OPT_FOI.DM_FOI_CATE_GROUP_WEIGHT_T ';
    V_FROM_AVG_TABLE         := ' FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T ';
    V_TO_TABLE               := ' FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T '; --指数目标表
    V_CALIBER_COLUMN_BAC     := ' CONTINUITY_TYPE, ';
    V_SQL_CALIBER_COLUMN_BAC := ' T2.CONTINUITY_TYPE, ';
  ELSIF F_CALIBER_FLAG = 'E' THEN
    V_FROM_WEIGHT_TABLE      := ' FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_WEIGHT_T '; --权重来源表
    V_FROM_TYPE_WEIGHT_TABLE := ' FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T ';
    V_FROM_AVG_TABLE         := ' FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T '; --均价来源表
    V_TO_TABLE               := ' FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T '; --指数目标表
    V_CALIBER_COLUMN_BAC     := ' GROUP_PUR_FLAG, ';
    V_SQL_CALIBER_COLUMN_BAC := ' T2.GROUP_PUR_FLAG, ';
  ELSE  --202407版本 新增华东采购与IAS
    V_FROM_WEIGHT_TABLE      := ' FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_WEIGHT_T '; --权重来源表
    V_FROM_TYPE_WEIGHT_TABLE := ' FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_CATE_GROUP_WEIGHT_T ';
    V_FROM_AVG_TABLE         := ' FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ITEM_SUP_AVG_T '; --均价来源表
    V_TO_TABLE               := ' FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_PRICE_INDEX_T '; --指数目标表
	V_CALIBER_FLAG			:= 'CALIBER_FLAG,';
	V_IN_CALIBER			:= ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
	V_IAS_ECPQC_SQL			:= ' AND T1.CALIBER_FLAG = '''||F_CALIBER_FLAG||''' ';
	
	IF F_CALIBER_FLAG = 'IAS' THEN		--202407版本 新增IAS
    V_CALIBER_COLUMN_BAC     := ' GROUP_PUR_FLAG, ';
    V_SQL_CALIBER_COLUMN_BAC := ' T2.GROUP_PUR_FLAG, ';
	ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN	--202407版本 新增华东采购
	V_CALIBER_COLUMN_BAC     := '';
	V_SQL_CALIBER_COLUMN_BAC := '';
	END IF ;
	
  END IF;
  
  V_FROM_WEIGHT_TABLE_BAC := V_FROM_WEIGHT_TABLE;
  V_BASE_RATE             := ' AVG_RECEIVE_AMT AS BASE_RATE,';
  V_FILTER1               := ' WHERE VERSION_ID = ' || V_VERSION || '
                             AND TOP_FLAG = ''Y'' 
                             AND UPPER(PARENT_LEVEL) = ''CATEGORY''
                             AND UPPER(GROUP_LEVEL) = ''ITEM'' '; --均价来源表取数条件
  V_FROM_SUP_AVG_TABLE    := V_FROM_AVG_TABLE;	--均价来源表备份
  V_FILTER1_BAC           := V_FILTER1;			--基础数据查询条件备份
  V_BASE_RATE_BAC         := V_BASE_RATE;		--均价字段备份
  
  
  
   
--创建指数中间表 202403
V_STEP_NUM := V_STEP_NUM + 1;
DROP TABLE IF EXISTS FOI_MID_PRICE_INDEX_TEMP;
CREATE TEMPORARY TABLE FOI_MID_PRICE_INDEX_TEMP(
	YEAR BIGINT,
	PERIOD_ID BIGINT,
	BASE_PERIOD_ID BIGINT,
	GROUP_CODE CHARACTER VARYING(500),
	GROUP_CN_NAME CHARACTER VARYING(500),
	GROUP_LEVEL CHARACTER VARYING(50),
	PARENT_CODE CHARACTER VARYING(500),
	PARENT_NAME CHARACTER VARYING(500),
	PRICE_INDEX NUMERIC,
	L4_CEG_CODE CHARACTER VARYING(500),
	L4_CEG_SHORT_CN_NAME CHARACTER VARYING(500),
	L4_CEG_CN_NAME CHARACTER VARYING(500),
	L3_CEG_CODE CHARACTER VARYING(500),
	L3_CEG_SHORT_CN_NAME CHARACTER VARYING(500),
	L3_CEG_CN_NAME CHARACTER VARYING(500),
	L2_CEG_CODE CHARACTER VARYING(500),
	L2_CEG_CN_NAME CHARACTER VARYING(500),
	APPEND_FLAG CHARACTER VARYING(2),
	TOP_TYPE CHARACTER VARYING(50),
	GROUP_PUR_FLAG 	CHARACTER VARYING(2),	 --是否含集团代采 (数字能源)
	CONTINUITY_TYPE	CHARACTER VARYING(50),  --是否含连续性影响 (生产采购)
	CALIBER_FLAG	CHARACTER VARYING(50)	--202407版本 华东采购与IAS新增CALIBER_FLAG字段
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;

  --写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '指数中间表创建成功',
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');

  FOR YEAR_NUM IN 1 .. 2 LOOP
  
    /*字段值定义*/
    V_CHILD              := 'GROUP_CODE,GROUP_CN_NAME AS GROUP_CN_NAME,';
    V_GROUP_CODE         := 'CATEGORY_CODE AS PARENT_CODE ,';
    V_GROUP_NAME         := 'CATEGORY_NAME AS PARENT_NAME ,';
    V_LV4                := 'L4_CEG_CODE , L4_CEG_SHORT_CN_NAME , ';
    V_LV3                := 'L3_CEG_CODE , L3_CEG_SHORT_CN_NAME , ';
    V_LV2                := 'L2_CEG_CODE , L2_CEG_CN_NAME , ';
    V_L3_CEG_CN_NAME     := 'L3_CEG_CN_NAME , ';
    V_L4_CEG_CN_NAME     := 'L4_CEG_CN_NAME , ';
	V_BASE_RATE			 := V_BASE_RATE_BAC;
	V_SOURCE_TBALE         := V_FROM_AVG_TABLE ; --202403版本 品类指数计算来源数据为ITEM均价
	V_FROM_WEIGHT_TABLE:=V_FROM_WEIGHT_TABLE_BAC;
	V_FILTER1			:= V_FILTER1_BAC;
	
	
    V_TOP_TYPE           := '';
    V_SQL_PARENT_CODE    := 'T1.L4_CEG_CODE AS PARENT_CODE , T1.L4_CEG_SHORT_CN_NAME AS PARENT_NAME ';
    V_SQL_GROUP_CODE     := 'T1.PARENT_CODE,';
    V_SQL_GROUP_CN_NAME  := 'T1.PARENT_NAME,';
    V_SQL_LV4            := 'T1.L4_CEG_CODE , T1.L4_CEG_SHORT_CN_NAME , ';
    V_SQL_LV3            := 'T1.L3_CEG_CODE , T1.L3_CEG_SHORT_CN_NAME , ';
    V_SQL_LV2            := 'T1.L2_CEG_CODE , T1.L2_CEG_CN_NAME , ';
    V_SQL_L3_CEG_CN_NAME := 'T1.L3_CEG_CN_NAME , ';
    V_SQL_L4_CEG_CN_NAME := 'T1.L4_CEG_CN_NAME , ';
    V_PRICE_INDEX        := 'SUM(T1.BASE_RATE / NULLIF(T3.BASE_PERIOD_AVG, 0) * T2.WEIGHT)*100 AS PRICE_INDEX,';
    V_SQL_TOP_TYPE       := '';
    V_CHILD_LEVEL        := '''ITEM''';
    V_GROUP_LEVEL        := '''CATEGORY''';
    V_JOIN_PARENT_CODE   := ' AND T1.PARENT_CODE = T2.PARENT_CODE ';
	V_FILTER_WEIGHT:= ' AND UPPER(PARENT_LEVEL) = '||V_GROUP_LEVEL||'
		                    AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
		';
	
						 
    IF YEAR_NUM = 1 THEN
      /*初始基期指数*/
      V_LOOP_NUM             := 6;
      V_BASE_PERIOD_ID       := V_BASE_PERIOD_ID_SINGLE;
      V_JOIN_TABLE3          := 'LEFT JOIN BASE_PERIOD_AVG T3
									 ON T1.GROUP_CODE = T3.GROUP_CODE
									AND T1.PARENT_CODE = T3.PARENT_CODE ';
      V_SQL_BASE_PERIOD_ID   := V_BASE_PERIOD_ID_SINGLE;
      V_FILTER2              := '';
      V_BASE_PERIOD_AVG_TEMP := '
							BASE_PERIOD_AVG AS
							 (SELECT GROUP_CODE,PERIOD_ID,PARENT_CODE,PARENT_NAME,BASE_RATE AS BASE_PERIOD_AVG
								FROM BASE_DB
							   WHERE PERIOD_ID = '||V_BASE_PERIOD_ID||'), ';
      V_SQL_GROUPBY_CODE     := 'T1.PARENT_CODE,';
    
    ELSIF YEAR_NUM = 2 THEN
      /*单年指数*/
      V_LOOP_NUM			:= 4;
      V_FILTER2  			:= ' WHERE T1.YEAR <> '||V_YEAR||'';
      V_SQL_BASE_PERIOD_ID	:= ' T3.PERIOD_ID ';
      V_JOIN_TABLE3			:= 'LEFT JOIN BASE_PERIOD_AVG T3
								   ON T1.GROUP_CODE = T3.GROUP_CODE
								  AND T1.PARENT_CODE = T3.PARENT_CODE 
								  AND T1.YEAR = T3.YEAR';
      V_BASE_PERIOD_AVG_TEMP := '
								BASE_PERIOD_AVG AS
								 (SELECT GROUP_CODE,YEAR,PERIOD_ID,PARENT_CODE,PARENT_NAME,BASE_RATE AS BASE_PERIOD_AVG
									FROM BASE_DB
								   WHERE PERIOD_ID IN ( YEAR(CURRENT_DATE)- 2||''01'', YEAR(CURRENT_DATE) - 1||''01'',YEAR(CURRENT_DATE)|| ''01'')
								 ), ';
      V_SQL_GROUPBY_CODE     := 'T1.PARENT_CODE,T3.PERIOD_ID,';
    END IF;
  
    FOR LEVEL_NUM IN 1 .. V_LOOP_NUM LOOP
    V_STEP_NUM:=V_STEP_NUM+1;
	
      IF LEVEL_NUM = 1 THEN
        --规格品到品类卷积
		NULL;
      ELSIF LEVEL_NUM = 2 THEN
        --LV4卷积
        V_CHILD                := 'GROUP_CODE AS GROUP_CODE,GROUP_CN_NAME AS GROUP_CN_NAME,BASE_PERIOD_ID,';
        V_BASE_RATE            := 'PRICE_INDEX AS BASE_RATE,';
        V_GROUP_CODE           := 'L4_CEG_CODE AS PARENT_CODE ,';
        V_GROUP_NAME           := 'L4_CEG_SHORT_CN_NAME AS PARENT_NAME ,';
        V_LV4                  := 'L4_CEG_CODE , L4_CEG_SHORT_CN_NAME , ';    --20240117 配合JAVA修改
        V_SOURCE_TBALE         := ' FOI_MID_PRICE_INDEX_TEMP ';  --除品类以上层级来源数据皆为已算出的子集指数
        V_SQL_PARENT_CODE      := 'T1.L3_CEG_CODE AS PARENT_CODE , T1.L3_CEG_SHORT_CN_NAME AS PARENT_NAME ';
        V_SQL_LV4              := 'T1.L4_CEG_CODE , T1.L4_CEG_SHORT_CN_NAME , ';  --20240117 配合JAVA修改
        V_PRICE_INDEX          := 'SUM(T1.BASE_RATE * T2.WEIGHT) AS PRICE_INDEX,';
        V_JOIN_TABLE3          := '';
        V_CHILD_LEVEL          := '''CATEGORY''';
        V_GROUP_LEVEL          := '''LV4''';
        V_FILTER1              := 'WHERE UPPER(GROUP_LEVEL) = ''CATEGORY'' ';
        V_BASE_PERIOD_AVG_TEMP := '';
		V_FILTER_WEIGHT:= ' AND UPPER(PARENT_LEVEL) = '||V_GROUP_LEVEL||'
		                    AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
		';
      
        IF YEAR_NUM = 2 THEN
          V_SQL_BASE_PERIOD_ID := ' T1.BASE_PERIOD_ID ';
          V_SQL_GROUPBY_CODE   := 'T1.PARENT_CODE,T1.BASE_PERIOD_ID,'; --替换源表后，基期字段取值变化
          V_FILTER2            := ' WHERE T1.YEAR <> '||V_YEAR||' 
                                AND T1.BASE_PERIOD_ID IN ( YEAR(CURRENT_DATE)- 2||''01'', YEAR(CURRENT_DATE) - 1||''01'',YEAR(CURRENT_DATE)|| ''01'')';
        ELSE
          NULL;
        END IF;
      
      ELSIF LEVEL_NUM = 3 THEN
        --LV3卷积
        V_GROUP_CODE         := 'L3_CEG_CODE AS PARENT_CODE ,';
        V_GROUP_NAME         := 'L3_CEG_SHORT_CN_NAME AS PARENT_NAME ,';
		V_LV4				 := '';  --20240117 配合JAVA修改
		V_SQL_LV4			 := '';  --20240117 配合JAVA修改
        V_LV3                := 'L3_CEG_CODE , L3_CEG_SHORT_CN_NAME ,';    --20240117 配合JAVA修改
        V_L4_CEG_CN_NAME     := '';
        V_SQL_PARENT_CODE    := 'T1.L2_CEG_CODE AS PARENT_CODE , T1.L2_CEG_CN_NAME AS PARENT_NAME';				
        V_SQL_LV3            := 'T1.L3_CEG_CODE , T1.L3_CEG_SHORT_CN_NAME ,';   --20240117 配合JAVA修改
        V_SQL_L4_CEG_CN_NAME := '';
        V_CHILD_LEVEL        := '''LV4''';
        V_GROUP_LEVEL        := '''LV3''';
        V_FILTER1            := 'WHERE UPPER(GROUP_LEVEL) = ''LV4'' ';
		V_FILTER_WEIGHT:= ' AND UPPER(PARENT_LEVEL) = '||V_GROUP_LEVEL||'
		                    AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
		';
		
      
      ELSIF LEVEL_NUM = 4 THEN
        --LV2卷积
        V_GROUP_CODE          := 'L2_CEG_CODE AS PARENT_CODE ,';
        V_GROUP_NAME          := 'L2_CEG_CN_NAME AS PARENT_NAME ,';
        V_L3_CEG_CN_NAME      := '';
        V_CALIBER_COLUMN     := V_CALIBER_COLUMN_BAC;  
        V_SQL_PARENT_CODE     := ''''' AS PARENT_CODE , '''' AS PARENT_NAME ';
        V_SQL_L3_CEG_CN_NAME  := '';
        V_SQL_CALIBER_COLUMN := V_SQL_CALIBER_COLUMN_BAC;
        V_CHILD_LEVEL         := '''LV3''';
        V_GROUP_LEVEL         := '''LV2''';
        V_FILTER1             := 'WHERE UPPER(GROUP_LEVEL) = ''LV3'' ';
		V_LV3				  := '';   --20240117 配合JAVA修改
		V_SQL_LV3			  := '';   --20240117 配合JAVA修改
		V_FILTER_WEIGHT:= ' AND UPPER(PARENT_LEVEL) = '||V_GROUP_LEVEL||'
		                    AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
		';
		
		
      
      ELSIF LEVEL_NUM = 5 THEN
        --品类分类卷积
        V_GROUP_CODE          := '';
        V_GROUP_NAME          := '';
        V_SQL_GROUP_CODE      := ' '''' AS GROUP_CODE,';
        V_SQL_GROUP_CN_NAME   := 'T2.PARENT_CODE,';
        V_CALIBER_COLUMN     := '';		--202403版本新增 生产采购为是否含连续性影响 数字能源为是否含集团代采
        V_SQL_PARENT_CODE     := 'T1.L2_CEG_CODE AS PARENT_CODE ,T1.L2_CEG_CN_NAME AS PARENT_NAME ';
        V_SQL_LV2             := 'T1.L2_CEG_CODE , T1.L2_CEG_CN_NAME , ';
        V_SQL_CALIBER_COLUMN := '';		--202403版本新增 生产采购为是否含连续性影响 数字能源为是否含集团代采
        V_SQL_GROUPBY_CODE    := '';
        V_CHILD_LEVEL         := '''CATEGORY''';
        V_GROUP_LEVEL         := '''CATEGORY_TYPE''';
        V_FILTER1             := 'WHERE UPPER(GROUP_LEVEL) = ''CATEGORY'' ';
        V_JOIN_PARENT_CODE    := '';
		V_FILTER_WEIGHT			:= ' AND UPPER(PARENT_TYPE) = ''CATEGORY_TYPE'' ';
		V_FROM_WEIGHT_TABLE := V_FROM_TYPE_WEIGHT_TABLE;
		
      
      ELSIF LEVEL_NUM = 6 THEN
        --TOP分类卷积
        V_SQL_GROUP_CODE      := ' '''' AS GROUP_CODE,';
        V_SQL_GROUP_CN_NAME   := 'T2.PARENT_CODE ,';
        --V_CALIBER_COLUMN     := '';
        --V_TOP_TYPE            := 'TOP_TYPE,';
        --V_SQL_PARENT_CODE     := 'T1.L2_CEG_CODE AS PARENT_CODE ';
        --V_SQL_LV2             := 'T1.L2_CEG_CODE , T1.L2_CEG_CN_NAME , ';
        --V_SQL_CALIBER_COLUMN := '';
        V_GROUP_LEVEL         := '''TOP_TYPE''';
        --V_SQL_TOP_TYPE        := ' T2.PARENT_CODE AS TOP_TYPE ,';
		V_FILTER_WEIGHT:= ' AND UPPER(PARENT_TYPE) = ''TOP_TYPE'' ';
      
      END IF;
    
		V_SQL1 := 'WITH BASE_DB AS
				 (SELECT YEAR,
						 '||V_CHILD || V_BASE_RATE || V_GROUP_CODE || V_GROUP_NAME 
						   || V_LV4 || V_LV3 || V_LV2 || V_L3_CEG_CN_NAME 
						   || V_L4_CEG_CN_NAME||'
						  PERIOD_ID
				  FROM ' ||V_SOURCE_TBALE||' T1
				  '|| V_FILTER1||V_IAS_ECPQC_SQL|| '),';	--202407版本 华东采购与IAS新增CALIBER_FLAG字段
  
		V_SQL2 := ' '|| V_BASE_PERIOD_AVG_TEMP||'
					BASE_WEIGHT AS
					 (SELECT DISTINCT GROUP_CODE, WEIGHT_RATE AS WEIGHT,'|| V_CALIBER_COLUMN ||' PARENT_CODE
					  FROM '||V_FROM_WEIGHT_TABLE||' T1
					 WHERE VERSION_ID ='||V_VERSION|| V_FILTER_WEIGHT|| V_IAS_ECPQC_SQL||' --202407版本 华东采购与IAS新增CALIBER_FLAG字段
					   --AND PERIOD_TYPE = ''S''	--202403版本权重来源表更新 条件失效
					   --AND TOP_FLAG = ''Y''
					 --  AND UPPER(PARENT_LEVEL) = '||V_GROUP_LEVEL||'
					 --  AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
					   )';
  
		V_SQL3 := '
		  INSERT INTO FOI_MID_PRICE_INDEX_TEMP(
		  YEAR,
		  PERIOD_ID,
		  BASE_PERIOD_ID,
		  GROUP_CODE,
		  GROUP_CN_NAME,
		  GROUP_LEVEL,
		  PRICE_INDEX,
		  '||V_CALIBER_FLAG||'		--202407版本 华东采购与IAS新增CALIBER_FLAG字段
		  '||V_LV4 || V_LV3 || V_LV2 || V_L3_CEG_CN_NAME 
			|| V_L4_CEG_CN_NAME || V_CALIBER_COLUMN || V_TOP_TYPE||'
		  PARENT_CODE,PARENT_NAME		--202403版本新增 PARENT_NAME字段
		  )
		  SELECT T1.YEAR,
				 T1.PERIOD_ID,
				 '||V_SQL_BASE_PERIOD_ID||' AS BASE_PERIOD_ID, 
				 '||V_SQL_GROUP_CODE|| V_SQL_GROUP_CN_NAME||'
				 '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
				 '||V_PRICE_INDEX
				  ||V_IN_CALIBER||'		--202407版本 华东采购与IAS新增CALIBER_FLAG字段
				 '||V_SQL_LV4||V_SQL_LV3||V_SQL_LV2 
				  ||V_SQL_L3_CEG_CN_NAME||V_SQL_L4_CEG_CN_NAME
				  ||V_SQL_CALIBER_COLUMN||V_SQL_TOP_TYPE||V_SQL_PARENT_CODE|| '
			FROM BASE_DB T1
			JOIN BASE_WEIGHT T2
			  ON T1.GROUP_CODE = T2.GROUP_CODE ' 
			  ||V_JOIN_PARENT_CODE||V_JOIN_TABLE3 || V_FILTER2 ||'
			GROUP BY '||V_SQL_LV4 || V_SQL_LV3 || V_SQL_LV2 
						||V_SQL_L3_CEG_CN_NAME || V_SQL_L4_CEG_CN_NAME 
						||V_SQL_CALIBER_COLUMN || V_SQL_GROUPBY_CODE 
						||V_SQL_GROUP_CN_NAME||'
					 T1.YEAR,
					 T1.PERIOD_ID; ';
    
      V_EXECUTE_SQL := V_SQL1 || V_SQL2 || V_SQL3;
      --DBMS_OUTPUT.PUT_LINE(V_GROUP_LEVEL||'层级卷积');
      --DBMS_OUTPUT.PUT_LINE(V_EXECUTE_SQL);
      EXECUTE V_EXECUTE_SQL;
	  
	--写入日志
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME => V_SP_NAME,
	 F_STEP_NUM => V_STEP_NUM,
	 F_CAL_LOG_DESC => V_GROUP_LEVEL||'层级卷积',
	 F_FORMULA_SQL_TXT => V_EXECUTE_SQL,
	 F_DML_ROW_COUNT => SQL%ROWCOUNT,
	 F_RESULT_STATUS => X_SUCCESS_FLAG,
	 F_ERRBUF => 'SUCCESS');
    END LOOP;
  
  END LOOP;

  V_STEP_NUM := V_STEP_NUM+1;
  --202311版本新增 
V_EXECUTE_SQL:='
  WITH BASE_DB AS
 (SELECT YEAR,
         SUPPLIER_CODE        AS PARENT_CODE,
         SUPPLIER_CN_NAME     AS PARENT_NAME,
         ITEM_CODE            AS GROUP_CODE,
         ITEM_NAME            AS GROUP_CN_NAME,
		 AVG_RECEIVE_AMT 	AS BASE_RATE,
         CATEGORY_CODE,
         CATEGORY_NAME,
         L4_CEG_CODE,
         L4_CEG_SHORT_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_SHORT_CN_NAME,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
         L3_CEG_CN_NAME,
         L4_CEG_CN_NAME,
         PERIOD_ID
    FROM '||V_FROM_SUP_AVG_TABLE||' T1
	WHERE VERSION_ID = '||V_VERSION||'
		  '||V_IAS_ECPQC_SQL||'		--202407版本 华东采购与IAS新增CALIBER_FLAG字段
		  AND TOP_FLAG = ''Y'' 
          AND UPPER(PARENT_LEVEL) = ''ITEM''
          AND UPPER(GROUP_LEVEL) = ''SUPPLIER'' ),
	
BASE_PERIOD_AVG AS
 (SELECT GROUP_CODE,
         YEAR,
         PERIOD_ID,
         PARENT_CODE,
         PARENT_NAME,
		 CATEGORY_CODE,
         BASE_RATE AS BASE_PERIOD_AVG
    FROM BASE_DB
   WHERE PERIOD_ID = '||V_BASE_PERIOD_ID_SINGLE||'),
   
BASE_WEIGHT AS
 (SELECT DISTINCT GROUP_CODE, CATEGORY_CODE,WEIGHT_RATE AS WEIGHT , PARENT_CODE
    FROM '||V_FROM_WEIGHT_TABLE||' T1
   WHERE VERSION_ID = '||V_VERSION||'
     '||V_IAS_ECPQC_SQL||'		--202407版本 华东采购与IAS新增CALIBER_FLAG字段
     AND UPPER(PARENT_LEVEL) = ''SUPPLIER''
     AND UPPER(GROUP_LEVEL) = ''ITEM'')
	 
 INSERT INTO FOI_MID_PRICE_INDEX_TEMP
   (YEAR,
    PERIOD_ID,
    BASE_PERIOD_ID,
    GROUP_CODE,
    GROUP_CN_NAME,
    GROUP_LEVEL,
    PRICE_INDEX,
    L4_CEG_CODE,
    L4_CEG_SHORT_CN_NAME,
    L3_CEG_CODE,
    L3_CEG_SHORT_CN_NAME,
    L2_CEG_CODE,
    L2_CEG_CN_NAME,
    L3_CEG_CN_NAME,
    L4_CEG_CN_NAME,
    PARENT_CODE,
	PARENT_NAME)
SELECT T1.YEAR,
       T1.PERIOD_ID,
       '||V_BASE_PERIOD_ID_SINGLE||' AS BASE_PERIOD_ID,
       T1.PARENT_CODE AS GROUP_CODE,
       T1.PARENT_NAME AS GROUP_CN_NAME,
       ''SUPPLIER'' AS GROUP_LEVEL,
       SUM(T1.BASE_RATE / NULLIF(T3.BASE_PERIOD_AVG, 0) * T2.WEIGHT) * 100 AS PRICE_INDEX,
       T1.L4_CEG_CODE,
       T1.L4_CEG_SHORT_CN_NAME,
       T1.L3_CEG_CODE,
       T1.L3_CEG_SHORT_CN_NAME,
       T1.L2_CEG_CODE,
       T1.L2_CEG_CN_NAME,
       T1.L3_CEG_CN_NAME,
       T1.L4_CEG_CN_NAME,
       T1.CATEGORY_CODE AS PARENT_CODE,
	   T1.CATEGORY_NAME AS PARENT_NAME
  FROM BASE_DB T1
  JOIN BASE_WEIGHT T2
    ON T1.GROUP_CODE = T2.GROUP_CODE
   AND T1.PARENT_CODE = T2.PARENT_CODE
   AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
  LEFT JOIN BASE_PERIOD_AVG T3
    ON T1.GROUP_CODE = T3.GROUP_CODE
   AND T1.PARENT_CODE = T3.PARENT_CODE
   AND T1.CATEGORY_CODE = T3.CATEGORY_CODE
 GROUP BY T1.L4_CEG_CODE,
          T1.L4_CEG_SHORT_CN_NAME,
          T1.L3_CEG_CODE,
          T1.L3_CEG_SHORT_CN_NAME,
          T1.L2_CEG_CODE,
          T1.L2_CEG_CN_NAME,
          T1.L3_CEG_CN_NAME,
          T1.L4_CEG_CN_NAME,
          T1.CATEGORY_CODE,
		  T1.CATEGORY_NAME,
          T1.PARENT_CODE,
          T1.PARENT_NAME,
          T3.PERIOD_ID,
          T1.YEAR,
          T1.PERIOD_ID;';

--DBMS_OUTPUT.PUT_LINE(V_EXECUTE_SQL);
EXECUTE V_EXECUTE_SQL;


  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '供应商指数插数完成',
   F_FORMULA_SQL_TXT => V_EXECUTE_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');  

  V_STEP_NUM:=V_STEP_NUM+1;   
V_EXECUTE_SQL:='
WITH BASE_DB AS
 (SELECT YEAR,
         GROUP_CODE           AS GROUP_CODE,
         GROUP_CN_NAME        AS GROUP_CN_NAME,
         BASE_PERIOD_ID,
         PRICE_INDEX          AS BASE_RATE,
         PARENT_CODE,
		 PARENT_NAME,
		 L4_CEG_CODE,
		 L4_CEG_SHORT_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_SHORT_CN_NAME,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
         L3_CEG_CN_NAME,
         L4_CEG_CN_NAME,
         PERIOD_ID
    FROM FOI_MID_PRICE_INDEX_TEMP
   WHERE GROUP_LEVEL = ''SUPPLIER'' ),
   
BASE_WEIGHT AS
 (SELECT DISTINCT GROUP_CODE, WEIGHT_RATE AS WEIGHT, CATEGORY_CODE AS PARENT_CODE,CATEGORY_NAME AS PARENT_NAME
    FROM '||V_FROM_WEIGHT_TABLE||' T1
   WHERE VERSION_ID = '||V_VERSION||'
     '||V_IAS_ECPQC_SQL||'		--202407版本 华东采购与IAS新增CALIBER_FLAG字段
     AND UPPER(PARENT_LEVEL) = ''CATEGORY''
     AND UPPER(GROUP_LEVEL) = ''SUPPLIER'')
	 
 INSERT INTO FOI_MID_PRICE_INDEX_TEMP
   (YEAR,
    PERIOD_ID,
    BASE_PERIOD_ID,
    GROUP_CODE,
    GROUP_CN_NAME,
    GROUP_LEVEL,
    PRICE_INDEX,
	L4_CEG_CODE,
	L4_CEG_SHORT_CN_NAME,
    L3_CEG_CODE,
    L3_CEG_SHORT_CN_NAME,
    L2_CEG_CODE,
    L2_CEG_CN_NAME,
    L3_CEG_CN_NAME,
    L4_CEG_CN_NAME,
    PARENT_CODE,
	PARENT_NAME)
SELECT T1.YEAR,
       T1.PERIOD_ID,
       '||V_BASE_PERIOD_ID_SINGLE||' AS BASE_PERIOD_ID,
       T2.PARENT_CODE,
       T2.PARENT_NAME,
       ''CATE_SUPPLIER'' AS GROUP_LEVEL,
       SUM(T1.BASE_RATE * T2.WEIGHT) AS PRICE_INDEX,
	   T1.L4_CEG_CODE,
	   T1.L4_CEG_SHORT_CN_NAME,
       T1.L3_CEG_CODE,
       T1.L3_CEG_SHORT_CN_NAME,
       T1.L2_CEG_CODE,
       T1.L2_CEG_CN_NAME,
       T1.L3_CEG_CN_NAME,
       T1.L4_CEG_CN_NAME,
       T1.L4_CEG_CODE AS PARENT_CODE,
	   T1.L4_CEG_SHORT_CN_NAME AS PARENT_NAME
  FROM BASE_DB T1
  JOIN BASE_WEIGHT T2
    ON T1.GROUP_CODE = T2.GROUP_CODE
   AND T1.PARENT_CODE = T2.PARENT_CODE
 GROUP BY T1.L3_CEG_CODE,
          T1.L3_CEG_SHORT_CN_NAME,
		  T1.L4_CEG_CODE,
		  T1.L4_CEG_SHORT_CN_NAME,
          T1.L2_CEG_CODE,
          T1.L2_CEG_CN_NAME,
          T1.L3_CEG_CN_NAME,
          T1.L4_CEG_CN_NAME,
          T2.PARENT_CODE,
          T2.PARENT_NAME,
          T1.YEAR,
          T1.PERIOD_ID;';
		  
--DBMS_OUTPUT.PUT_LINE(V_EXECUTE_SQL);
EXECUTE V_EXECUTE_SQL;
	
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '供应商到品类指数插数完成',
   F_FORMULA_SQL_TXT => V_EXECUTE_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');  
	
	
  V_STEP_NUM:=V_STEP_NUM+1;
  V_EXECUTE_SQL:='DELETE FROM '||V_TO_TABLE||' T1 WHERE T1.VERSION_ID = '||V_VERSION||V_IAS_ECPQC_SQL||';';
																			--202407版本 华东采购与IAS新增CALIBER_FLAG字段
--DBMS_OUTPUT.PUT_LINE(V_EXECUTE_SQL);
EXECUTE V_EXECUTE_SQL;
						
--.写入日志

   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '指数表同版本数据删除完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');   

--raise notice '插入数据';

V_STEP_NUM:=V_STEP_NUM+1;
V_EXECUTE_SQL:='
INSERT INTO '||V_TO_TABLE||'
  (YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PARENT_CN_NAME,
   PRICE_INDEX,
   L4_CEG_CODE,
   L4_CEG_SHORT_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   L4_CEG_CN_NAME,
   L3_CEG_CN_NAME,
   '||V_CALIBER_FLAG||'		--202407版本 华东采购与IAS新增CALIBER_FLAG字段
   '||V_CALIBER_COLUMN_BAC||'
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VERSION_ID)
  SELECT YEAR,
         PERIOD_ID,
         BASE_PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
		 PARENT_NAME,
         PRICE_INDEX,
         L4_CEG_CODE,
         L4_CEG_SHORT_CN_NAME,
         L3_CEG_CODE,
         L3_CEG_SHORT_CN_NAME,
         L2_CEG_CODE,
         L2_CEG_CN_NAME,
         L4_CEG_CN_NAME,
         L3_CEG_CN_NAME,
		 '||V_IN_CALIBER||'	--202407版本 华东采购与IAS新增CALIBER_FLAG字段
		 '||V_CALIBER_COLUMN_BAC||'
         -1,
         CURRENT_TIMESTAMP,
         -1,
         CURRENT_TIMESTAMP,
         ''N'',
         '||V_VERSION||'
    FROM FOI_MID_PRICE_INDEX_TEMP;	';	
	
--DBMS_OUTPUT.PUT_LINE(V_EXECUTE_SQL);
EXECUTE V_EXECUTE_SQL;
						
--写入日志

   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '指数表数据插入完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');  

  EXECUTE 'ANALYZE '||V_TO_TABLE;  
  
RETURN 'SUCCESS';

--处理异常信息

 EXCEPTION
   WHEN OTHERS THEN
   X_SUCCESS_FLAG := 0;
   
 	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步运行失败', 
    F_RESULT_STATUS => X_SUCCESS_FLAG, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; $$
/

