-- Name: f_dm_fcst_data_encrypt; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_data_encrypt(f_keystr character varying, f_period_id bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  背景描述：用于FCST-ICT KMS数据解密，数据库对JAVA解密数据，重新进行高斯函数解密，加密过程
  参数描述：x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.f_dm_fcst_data_encrypt()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_DATA_ENCRYPT'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_PERIOD_ID BIGINT := F_PERIOD_ID; --入参月份
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  V_FROM_TABLE VARCHAR2(500);  -- 来源表
  V_TO_TABLE VARCHAR2(500);  -- 目标表
  V_SQL_DECRYPT TEXT;
  V_IN_AMT VARCHAR(50);
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
 --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
       V_IN_AMT := 'RMB_FACT_RATE_AMT,';
       V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_JAVA_PRIMARY_ENCRYPT_T';
       V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_DATA_PRIMARY_ENCRYPT_T';
       V_SQL_DECRYPT := 'CAST(GS_DECRYPT(RMB_FACT_RATE_AMT,'''||V_KEYSTR||''',''AES128'', ''CBC'', ''SHA256'') AS NUMERIC) AS RMB_FACT_RATE_AMT';

   
  --1.清空数据侧对JAVA侧数据处理表数据:
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE PERIOD_ID = '||V_PERIOD_ID;


  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空月份为：'||V_PERIOD_ID||'的fin_dm_opt_foi.DM_FOC_DATA_PRIMARY_ENCRYPT_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
       
    -- 对JAVA侧表数据解密处理过程
    V_SQL := '
    WITH JAVA_DECRYPT_TMP AS(
          SELECT PRIMARY_ID,   
                 PERIOD_ID,
                 '||V_SQL_DECRYPT||'  -- 解密(收入口径：RMB实际汇率集团成本/发货口径：发货RMB成本)
              FROM '||V_FROM_TABLE||'
              WHERE PERIOD_ID = '||V_PERIOD_ID||'
             )
     INSERT INTO '||V_TO_TABLE||'(
                 PRIMARY_ID,   
                 PERIOD_ID,
                 '||V_IN_AMT||'
                 CREATION_DATE,
                 LAST_UPDATE_DATE
           )
          SELECT PRIMARY_ID,   
                 PERIOD_ID,
                 GS_ENCRYPT('||V_IN_AMT||''''||V_KEYSTR||''',''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,  -- 加密(收入口径：RMB实际汇率集团成本/发货口径：发货RMB成本)
                 CURRENT_TIMESTAMP AS CREATION_DATE,
                 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
              FROM JAVA_DECRYPT_TMP';
       EXECUTE IMMEDIATE V_SQL;       
     --DBMS_OUTPUT.PUT_LINE (V_SQL);
        --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入PERIOD_ID= '||V_PERIOD_ID ||' 的数据到FIN_DM_OPT_FOI.DM_FCST_DATA_PRIMARY_ENCRYPT_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                    
               
     -- 10.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --11.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FCST_DATA_PRIMARY_ENCRYPT_T!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

