-- Name: f_dm_fcst_spart_actual_append; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_spart_actual_append(f_cost_type character varying, f_granularity_type character varying, f_view_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述：对实际数SPART补齐3补齐3+1年连续月份的均价: 前向补齐、后项补齐
		  不解密直接补齐

事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_SPART_ACTUAL_APPEND()
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FCST_SPART_ACTUAL_APPEND'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 
  V_BEGIN_DATE TIMESTAMP ;  --两年前首月
  V_END_DATE   TIMESTAMP;
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑

  
  V_LV0_PROD_PARA VARCHAR(150);
  V_LV1_PROD_PARA VARCHAR(150);  
  V_LV2_PROD_PARA VARCHAR(150); 
  V_LV3_PROD_PARA VARCHAR(150); 
  V_LV4_PROD_PARA VARCHAR(150); 
  V_LV0_PROD_CODE VARCHAR(150);
  V_LV1_PROD_CODE VARCHAR(150);  
  V_LV2_PROD_CODE VARCHAR(150); 
  V_LV3_PROD_CODE VARCHAR(150); 
  V_LV4_PROD_CODE VARCHAR(150);
  V_INSERT_LV0_PROD_CODE VARCHAR(150);
  V_INSERT_LV1_PROD_CODE VARCHAR(150);  
  V_INSERT_LV2_PROD_CODE VARCHAR(150); 
  V_INSERT_LV3_PROD_CODE VARCHAR(150); 
  V_INSERT_LV4_PROD_CODE VARCHAR(150);
  
  V_IN_LV0_PROD_CODE VARCHAR(150);
  V_IN_LV1_PROD_CODE VARCHAR(150);  
  V_IN_LV2_PROD_CODE VARCHAR(150); 
  V_IN_LV3_PROD_CODE VARCHAR(150); 
  V_IN_LV4_PROD_CODE VARCHAR(150);
  
  V_FROM_TABLE VARCHAR(100); 
  V_TO_TABLE VARCHAR(100);
  V_TEMP_TABLE VARCHAR(100);
  V_JOIN_TABLE VARCHAR(100);
 
  V_IN_LV0_PROD_PARA VARCHAR(150);
  V_IN_LV1_PROD_PARA VARCHAR(150);  
  V_IN_LV2_PROD_PARA VARCHAR(150); 
  V_IN_LV3_PROD_PARA VARCHAR(150); 
  V_IN_LV4_PROD_PARA VARCHAR(150);
  
  
  V_DIMENSION_PARA VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_PARA VARCHAR(200); 
  V_DIMENSION_SUB_DETAIL_PARA VARCHAR(200); 
  
  
  V_DIMENSION_CODE VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(200); 
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(200); 
  
  
  V_IN_DIMENSION_PARA VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_PARA VARCHAR(200); 
  V_IN_DIMENSION_SUB_DETAIL_PARA VARCHAR(200); 
  
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200); 
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  
  V_INSERT_DIMENSION_CODE VARCHAR(200);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(200); 
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  
  V_WHERE_PARA VARCHAR(50); 
  V_AND_PARA VARCHAR(50); 
  V_EXISTS_PARA TEXT;  
  V_SPART_CODE VARCHAR(20);
  V_IN_SPART_CODE VARCHAR(20);
  V_SPART_CN_NAME VARCHAR(20);
  V_SPART_CN_NAME2 VARCHAR(40);
  V_IN_SPART_CN_NAME2 VARCHAR(40);
  
  V_INSERT_SPART_CODE VARCHAR(50);
  
  V_SOFTWARE  VARCHAR(50); --软硬件标识
  V_IN_SOFTWARE  VARCHAR(50); --软硬件标识
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);
   
	


  --判断年份
  	IF   MONTH(CURRENT_TIMESTAMP) = 1   THEN
	V_BEGIN_DATE  := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM');
	V_END_DATE    := CURRENT_TIMESTAMP ;
    V_AND_PARA := ' AND B.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 3 ';
	
	--如果是本年且月份不为1,则从年初跑到当前月减一
	ELSIF MONTH(CURRENT_TIMESTAMP) != 1    THEN
	V_BEGIN_DATE  := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-2)||'01','YYYYMM');
	V_END_DATE    := CURRENT_TIMESTAMP ; --当前月 	
	V_AND_PARA := ' AND B.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP) - 2 ';
	
	END IF;
  
  
   --判断PBI维度和成本类型选择来源表,目标表,关联表,金额字段
  IF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_DECODE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_T';
	 V_JOIN_TABLE := 'DM_FCST_ICT_PSP_IRB_TOP_SPART_INFO_T';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_DECODE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_BASE_DETAIL_SPART_T';
	 V_JOIN_TABLE := 'DM_FCST_ICT_PSP_INDUS_TOP_SPART_INFO_T';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_DECODE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_T';
	 V_JOIN_TABLE := 'DM_FCST_ICT_PSP_PROD_TOP_SPART_INFO_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_DECODE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_T';
	 V_JOIN_TABLE := 'DM_FCST_ICT_STD_IRB_TOP_SPART_INFO_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_DECODE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_BASE_DETAIL_SPART_T';
	 V_JOIN_TABLE := 'DM_FCST_ICT_STD_INDUS_TOP_SPART_INFO_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_DECODE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_BASE_DETAIL_SPART_T';
	 V_JOIN_TABLE := 'DM_FCST_ICT_STD_PROD_TOP_SPART_INFO_T';
  ELSE 
	RETURN '入参有误';
  END IF;

	
	   --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
 

   --1.清空目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM  '||V_TO_TABLE||' WHERE VIEW_FLAG = '''||F_VIEW_FLAG||''' ';
 
   

  
 
 
 
 --判断PBI维度选择PBI字段	
  IF  F_GRANULARITY_TYPE = 'IRB' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_RND_TEAM_CODE ,LV0_PROD_RD_TEAM_CN_NAME, ';
	V_LV1_PROD_PARA :='	LV1_PROD_RND_TEAM_CODE ,LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_LV2_PROD_PARA :=' LV2_PROD_RND_TEAM_CODE ,LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_LV3_PROD_PARA :=' LV3_PROD_RND_TEAM_CODE ,LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_LV4_PROD_PARA :=' LV4_PROD_RND_TEAM_CODE ,LV4_PROD_RD_TEAM_CN_NAME, '; 
    V_LV0_PROD_CODE :='LV0_PROD_RND_TEAM_CODE,';
	V_LV1_PROD_CODE :='LV1_PROD_RND_TEAM_CODE,';  
	V_LV2_PROD_CODE :='LV2_PROD_RND_TEAM_CODE,'; 
	V_LV3_PROD_CODE :='LV3_PROD_RND_TEAM_CODE,'; 
	V_LV4_PROD_CODE :='LV4_PROD_RND_TEAM_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_RND_TEAM_CODE = B.LV1_PROD_RND_TEAM_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_RND_TEAM_CODE = B.LV4_PROD_RND_TEAM_CODE';
	
	
	V_IN_LV0_PROD_CODE :='A.LV0_PROD_RND_TEAM_CODE,';
	V_IN_LV1_PROD_CODE :='A.LV1_PROD_RND_TEAM_CODE,';  
	V_IN_LV2_PROD_CODE :='A.LV2_PROD_RND_TEAM_CODE,'; 
	V_IN_LV3_PROD_CODE :='A.LV3_PROD_RND_TEAM_CODE,'; 
	V_IN_LV4_PROD_CODE :='A.LV4_PROD_RND_TEAM_CODE,';
	
	
	V_IN_LV0_PROD_PARA :=' A.LV0_PROD_RND_TEAM_CODE ,A.LV0_PROD_RD_TEAM_CN_NAME, ';
	V_IN_LV1_PROD_PARA :=' A.LV1_PROD_RND_TEAM_CODE ,A.LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_IN_LV2_PROD_PARA :=' A.LV2_PROD_RND_TEAM_CODE ,A.LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_IN_LV3_PROD_PARA :=' A.LV3_PROD_RND_TEAM_CODE ,A.LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_IN_LV4_PROD_PARA :=' A.LV4_PROD_RND_TEAM_CODE ,A.LV4_PROD_RD_TEAM_CN_NAME, ';
	
	
  ELSIF  F_GRANULARITY_TYPE = 'INDUS' THEN 
	V_LV0_PROD_PARA :=' LV0_INDUSTRY_CATG_CODE ,LV0_INDUSTRY_CATG_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_INDUSTRY_CATG_CODE ,LV1_INDUSTRY_CATG_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_INDUSTRY_CATG_CODE ,LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_INDUSTRY_CATG_CODE ,LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_INDUSTRY_CATG_CODE ,LV4_INDUSTRY_CATG_CN_NAME,'; 
	V_LV0_PROD_CODE :='LV0_INDUSTRY_CATG_CODE,';
	V_LV1_PROD_CODE :='LV1_INDUSTRY_CATG_CODE,';  
	V_LV2_PROD_CODE :='LV2_INDUSTRY_CATG_CODE,'; 
	V_LV3_PROD_CODE :='LV3_INDUSTRY_CATG_CODE,'; 
	V_LV4_PROD_CODE :='LV4_INDUSTRY_CATG_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_INDUSTRY_CATG_CODE = B.LV0_INDUSTRY_CATG_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_INDUSTRY_CATG_CODE = B.LV1_INDUSTRY_CATG_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_INDUSTRY_CATG_CODE = B.LV2_INDUSTRY_CATG_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_INDUSTRY_CATG_CODE = B.LV3_INDUSTRY_CATG_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_INDUSTRY_CATG_CODE = B.LV4_INDUSTRY_CATG_CODE';
	
	V_IN_LV0_PROD_CODE :='A.LV0_INDUSTRY_CATG_CODE,';
	V_IN_LV1_PROD_CODE :='A.LV1_INDUSTRY_CATG_CODE,';  
	V_IN_LV2_PROD_CODE :='A.LV2_INDUSTRY_CATG_CODE,'; 
	V_IN_LV3_PROD_CODE :='A.LV3_INDUSTRY_CATG_CODE,'; 
	V_IN_LV4_PROD_CODE :='A.LV4_INDUSTRY_CATG_CODE,';
	
	V_IN_LV0_PROD_PARA :=' A.LV0_INDUSTRY_CATG_CODE ,A.LV0_INDUSTRY_CATG_CN_NAME,';
	V_IN_LV1_PROD_PARA :=' A.LV1_INDUSTRY_CATG_CODE ,A.LV1_INDUSTRY_CATG_CN_NAME,';  
	V_IN_LV2_PROD_PARA :=' A.LV2_INDUSTRY_CATG_CODE ,A.LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_IN_LV3_PROD_PARA :=' A.LV3_INDUSTRY_CATG_CODE ,A.LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_IN_LV4_PROD_PARA :=' A.LV4_INDUSTRY_CATG_CODE ,A.LV4_INDUSTRY_CATG_CN_NAME,'; 
	
  ELSIF  F_GRANULARITY_TYPE = 'PROD' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,'; 
	V_LV0_PROD_CODE :='LV0_PROD_LIST_CODE,';
	V_LV1_PROD_CODE :='LV1_PROD_LIST_CODE,';  
	V_LV2_PROD_CODE :='LV2_PROD_LIST_CODE,'; 
	V_LV3_PROD_CODE :='LV3_PROD_LIST_CODE,'; 
	V_LV4_PROD_CODE :='LV4_PROD_LIST_CODE,';
	
	V_INSERT_LV0_PROD_CODE :=' AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE';
	V_INSERT_LV1_PROD_CODE :=' AND A.LV1_PROD_LIST_CODE = B.LV1_PROD_LIST_CODE';  
	V_INSERT_LV2_PROD_CODE :=' AND A.LV2_PROD_LIST_CODE = B.LV2_PROD_LIST_CODE'; 
	V_INSERT_LV3_PROD_CODE :=' AND A.LV3_PROD_LIST_CODE = B.LV3_PROD_LIST_CODE'; 
	V_INSERT_LV4_PROD_CODE :=' AND A.LV4_PROD_LIST_CODE = B.LV4_PROD_LIST_CODE';
	
	
	V_IN_LV0_PROD_CODE :='A.LV0_PROD_LIST_CODE,';
	V_IN_LV1_PROD_CODE :='A.LV1_PROD_LIST_CODE,';  
	V_IN_LV2_PROD_CODE :='A.LV2_PROD_LIST_CODE,'; 
	V_IN_LV3_PROD_CODE :='A.LV3_PROD_LIST_CODE,'; 
	V_IN_LV4_PROD_CODE :='A.LV4_PROD_LIST_CODE,';
	
	V_IN_LV0_PROD_PARA :=' A.LV0_PROD_LIST_CODE,A.LV0_PROD_LIST_CN_NAME,';
	V_IN_LV1_PROD_PARA :='	A.LV1_PROD_LIST_CODE,A.LV1_PROD_LIST_CN_NAME,';  
	V_IN_LV2_PROD_PARA :=' A.LV2_PROD_LIST_CODE,A.LV2_PROD_LIST_CN_NAME,'; 
	V_IN_LV3_PROD_PARA :=' A.LV3_PROD_LIST_CODE,A.LV3_PROD_LIST_CN_NAME,'; 
	V_IN_LV4_PROD_PARA :=' A.LV4_PROD_LIST_CODE,A.LV4_PROD_LIST_CN_NAME,'; 
	
	
  ELSE 
	NULL ;
  END IF;
 
     --判断路径，选择量纲字段
  IF F_VIEW_FLAG = 'DIMENSION' THEN 
   V_DIMENSION_PARA := 'DIMENSION_CODE,DIMENSION_CN_NAME,';
   V_DIMENSION_SUBCATEGORY_PARA := 'DIMENSION_SUBCATEGORY_CODE ,DIMENSION_SUBCATEGORY_CN_NAME,'; 
   V_DIMENSION_SUB_DETAIL_PARA := 'DIMENSION_SUB_DETAIL_CODE ,DIMENSION_SUB_DETAIL_CN_NAME,';  
   
   V_IN_DIMENSION_PARA := 'A.DIMENSION_CODE,A.DIMENSION_CN_NAME,';
   V_IN_DIMENSION_SUBCATEGORY_PARA := 'A.DIMENSION_SUBCATEGORY_CODE ,A.DIMENSION_SUBCATEGORY_CN_NAME,'; 
   V_IN_DIMENSION_SUB_DETAIL_PARA := 'A.DIMENSION_SUB_DETAIL_CODE ,A.DIMENSION_SUB_DETAIL_CN_NAME,';  
   
   V_DIMENSION_CODE := 'DIMENSION_CODE,';
   V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE ,'; 
   V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE ,'; 
   

   V_IN_DIMENSION_CODE := 'DIMENSION_CODE,';
   V_IN_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE ,'; 
   V_IN_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE ,'; 
   
   
   V_INSERT_DIMENSION_CODE := ' AND A.DIMENSION_CODE  = B.DIMENSION_CODE';
   V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' AND A.DIMENSION_SUBCATEGORY_CODE = B.DIMENSION_SUBCATEGORY_CODE '; 
   V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' AND A.DIMENSION_SUB_DETAIL_CODE = B.DIMENSION_SUB_DETAIL_CODE '; 
   
   
   V_SPART_CODE := '';
   V_SPART_CN_NAME := '';
   V_SPART_CN_NAME2 := '';
   
   V_IN_SPART_CODE := '';
   V_IN_SPART_CN_NAME2 := '';
   
   V_INSERT_SPART_CODE := '';
   
   V_WHERE_PARA := ' WHERE B.VIEW_FLAG = ''DIMENSION'' ' ;
   
   V_EXISTS_PARA := '';
   
    --软硬件标识
    V_SOFTWARE := ' SOFTWARE_MARK,';					
	V_IN_SOFTWARE := ' A.SOFTWARE_MARK,';	
   
   
  ELSIF F_VIEW_FLAG = 'PROD_SPART' THEN 
   V_DIMENSION_PARA := '';
   V_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_DIMENSION_SUB_DETAIL_PARA := '';  
   
   V_IN_DIMENSION_PARA := '';
   V_IN_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_IN_DIMENSION_SUB_DETAIL_PARA := '';  
   
   V_DIMENSION_CODE := '';
   V_DIMENSION_SUBCATEGORY_CODE := ''; 
   V_DIMENSION_SUB_DETAIL_CODE := ''; 
   
   V_IN_DIMENSION_CODE := '';
   V_IN_DIMENSION_SUBCATEGORY_CODE := ''; 
   V_IN_DIMENSION_SUB_DETAIL_CODE := ''; 
   
   
   V_INSERT_DIMENSION_CODE := '';
   V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' '; 
   V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' '; 
   
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME := 'SPART_CN_NAME ,';
	V_SPART_CN_NAME2 := 'SPART_CODE AS SPART_CN_NAME ,';
	
	V_IN_SPART_CODE := 'A.SPART_CODE,';
    V_IN_SPART_CN_NAME2 := 'A.SPART_CODE AS SPART_CN_NAME,';
	
	V_INSERT_SPART_CODE := ' AND A.SPART_CODE = B.SPART_CODE ';
	
	V_WHERE_PARA := ' WHERE B.VIEW_FLAG = ''PROD_SPART'' ';
	
	
	V_EXISTS_PARA := ' AND EXISTS (SELECT 1 FROM '||V_JOIN_TABLE||' A WHERE A.TOP_SPART_CODE = B.SPART_CODE AND A.IS_TOP_FLAG = ''Y'' AND A.DOUBLE_FLAG = ''Y'' 
						AND A.REGION_CODE = B.REGION_CODE                                      
						AND A.REPOFFICE_CODE = B.REPOFFICE_CODE                                  
						AND A.BG_CODE = B.BG_CODE
						AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
						'||V_INSERT_LV4_PROD_CODE||'
						AND A.IS_TOP_FLAG = ''Y'' 
						AND A.DOUBLE_FLAG = ''Y''
						AND A.VERSION_ID = '||V_VERSION_ID||') ';
						
	 --软硬件标识
    V_SOFTWARE := ' SOFTWARE_MARK,';					
	V_IN_SOFTWARE := ' A.SOFTWARE_MARK,';	
	
  END IF;
  
  
  
  
   
  IF F_COST_TYPE = 'PSP' THEN 
      
	  V_TEMP_TABLE := 'ACTUAL_PSP_APD_TEMP';
	  
  ELSIF F_COST_TYPE = 'STD' THEN 
      
	  V_TEMP_TABLE := 'ACTUAL_STD_APD_TEMP';
  
  END IF;  
 
 
 

  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
 --创建PSP临时表
            DROP TABLE IF EXISTS ACTUAL_PSP_APD_TEMP;
            CREATE TEMPORARY TABLE ACTUAL_PSP_APD_TEMP (
                PERIOD_ID NUMERIC,
				PERIOD_YEAR NUMERIC,
				REGION_CODE CHARACTER VARYING(50),
				REGION_CN_NAME CHARACTER VARYING(200),
				REGION_EN_NAME CHARACTER VARYING(200),
				REPOFFICE_CODE CHARACTER VARYING(50),
				REPOFFICE_CN_NAME CHARACTER VARYING(200),
				REPOFFICE_EN_NAME CHARACTER VARYING(200),
				BG_CODE CHARACTER VARYING(50),
				BG_CN_NAME CHARACTER VARYING(200),
				LV0_PROD_LIST_CODE CHARACTER VARYING(50),
				LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV1_PROD_LIST_CODE CHARACTER VARYING(50),
				LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV2_PROD_LIST_CODE CHARACTER VARYING(50),
				LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV3_PROD_LIST_CODE CHARACTER VARYING(50),
				LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV4_PROD_LIST_CODE CHARACTER VARYING(50),
				LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(100),
				LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
				LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV0_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV0_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV1_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV1_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV2_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV2_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV3_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV3_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV4_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV4_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				DIMENSION_CODE CHARACTER VARYING(500),
				DIMENSION_CN_NAME CHARACTER VARYING(200),
				DIMENSION_EN_NAME CHARACTER VARYING(200),
				DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
				DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(200),
				DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
				DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(200),
				PROD_QTY NUMERIC,
				RMB_COST_AMT NUMERIC,
				RMB_AVG_AMT NUMERIC,
				SPART_CODE CHARACTER VARYING(188),
				OVERSEA_FLAG CHARACTER VARYING(1),
				MAIN_FLAG VARCHAR(1),
				CODE_ATTRIBUTES VARCHAR(50),
				VIEW_FLAG VARCHAR(10),
				ONLY_SPART_FLAG VARCHAR(1),
                NULL_FLAG VARCHAR(2),
                APPEND_FLAG VARCHAR(2),
				SOFTWARE_MARK VARCHAR(50)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE);

			
 --创建STD临时表
            DROP TABLE IF EXISTS ACTUAL_STD_APD_TEMP;
            CREATE TEMPORARY TABLE ACTUAL_STD_APD_TEMP (
                PERIOD_ID NUMERIC,
				PERIOD_YEAR NUMERIC,
				REGION_CODE CHARACTER VARYING(50),
				REGION_CN_NAME CHARACTER VARYING(200),
				REGION_EN_NAME CHARACTER VARYING(200),
				REPOFFICE_CODE CHARACTER VARYING(50),
				REPOFFICE_CN_NAME CHARACTER VARYING(200),
				REPOFFICE_EN_NAME CHARACTER VARYING(200),
				BG_CODE CHARACTER VARYING(50),
				BG_CN_NAME CHARACTER VARYING(200),
				LV0_PROD_LIST_CODE CHARACTER VARYING(50),
				LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV1_PROD_LIST_CODE CHARACTER VARYING(50),
				LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV2_PROD_LIST_CODE CHARACTER VARYING(50),
				LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV3_PROD_LIST_CODE CHARACTER VARYING(50),
				LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV4_PROD_LIST_CODE CHARACTER VARYING(50),
				LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
				LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(100),
				LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
				LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
				LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
				LV0_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV0_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV1_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV1_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV2_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV2_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV3_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV3_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				LV4_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
				LV4_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
				DIMENSION_CODE CHARACTER VARYING(500),
				DIMENSION_CN_NAME CHARACTER VARYING(200),
				DIMENSION_EN_NAME CHARACTER VARYING(200),
				DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
				DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(200),
				DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
				DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(200),
				PROD_QTY NUMERIC,
				RMB_COST_AMT CHARACTER VARYING(500),
				RMB_AVG_AMT CHARACTER VARYING(500),
				SPART_CODE CHARACTER VARYING(188),
				OVERSEA_FLAG CHARACTER VARYING(1),
				MAIN_FLAG VARCHAR(1),
				CODE_ATTRIBUTES VARCHAR(50),
				VIEW_FLAG VARCHAR(10),
				ONLY_SPART_FLAG VARCHAR(1),
                NULL_FLAG VARCHAR(2),
                APPEND_FLAG VARCHAR(2),
				SOFTWARE_MARK VARCHAR(50)
            )
            ON COMMIT PRESERVE ROWS
            DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE);			
			
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '实际数补齐临时表创建完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
        

	
	
		

    V_SQL := 
       'INSERT INTO '||V_TEMP_TABLE||' ( 
             PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				PROD_QTY ,
				RMB_COST_AMT ,
				RMB_AVG_AMT ,
				'||V_SPART_CODE||' 
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				'||V_SOFTWARE||'
				VIEW_FLAG ,
                NULL_FLAG ,
                APPEND_FLAG 
				)
	
	
	WITH ACTUAL_ITEM_TEMP AS
     (
      --实际数历史表中出现的取数范围：两年前第1月至当前系统月(不含)2023,2022,2021,2020
      SELECT DISTINCT  		
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA
				 ||V_SPART_CODE||'
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				'||V_SOFTWARE||'
				VIEW_FLAG 
        FROM '||V_FROM_TABLE||'  B
        '||V_WHERE_PARA
		||V_EXISTS_PARA
		||V_AND_PARA||'
	   
         AND B.PERIOD_ID <
             CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 三年前第1月至当前系统月(不含)
      SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                      V_BEGIN_DATE||''',
                                                      '''||V_END_DATE||''')),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的发散维
      SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              A.REGION_CODE ,
			  A.REGION_CN_NAME ,
			  A.REPOFFICE_CODE ,
			  A.REPOFFICE_CN_NAME ,
			  A.BG_CODE ,
			  A.BG_CN_NAME ,
			  '||V_IN_LV0_PROD_PARA 
			   ||V_IN_LV1_PROD_PARA  
			   ||V_IN_LV2_PROD_PARA 
			   ||V_IN_LV3_PROD_PARA  
			   ||V_IN_LV4_PROD_PARA
			   ||V_IN_DIMENSION_PARA 
			   ||V_IN_DIMENSION_SUBCATEGORY_PARA  
			   ||V_IN_DIMENSION_SUB_DETAIL_PARA
			   ||V_IN_SPART_CODE||'
			  A.OVERSEA_FLAG,
			  A.MAIN_FLAG,
			  A.CODE_ATTRIBUTES,
			  '||V_IN_SOFTWARE||'
			  A.VIEW_FLAG 
        FROM ACTUAL_ITEM_TEMP A, PERIOD_DIM_TEMP B)
                
            SELECT  A.PERIOD_ID ,
				A.PERIOD_YEAR ,
				A.REGION_CODE ,
				A.REGION_CN_NAME ,
				A.REPOFFICE_CODE ,
				A.REPOFFICE_CN_NAME ,
				A.BG_CODE ,
				A.BG_CN_NAME ,
				'||V_IN_LV0_PROD_PARA 
				 ||V_IN_LV1_PROD_PARA  
				 ||V_IN_LV2_PROD_PARA 
				 ||V_IN_LV3_PROD_PARA  
				 ||V_IN_LV4_PROD_PARA
				 ||V_IN_DIMENSION_PARA 
			   ||V_IN_DIMENSION_SUBCATEGORY_PARA  
			   ||V_IN_DIMENSION_SUB_DETAIL_PARA
			   ||'
				B.PROD_QTY ,
				B.RMB_COST_AMT ,
				B.RMB_AVG_AMT ,
				'||V_IN_SPART_CODE||'
				A.OVERSEA_FLAG,
				A.MAIN_FLAG,
				A.CODE_ATTRIBUTES,
				 '||V_IN_SOFTWARE||'
				A.VIEW_FLAG ,
                 DECODE(B.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
				 DECODE(B.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APPEND_FLAG --补齐标识：Y为补齐，N为原始
        FROM CROSS_JOIN_TEMP A
        LEFT JOIN '||V_FROM_TABLE||' B
          ON A.VIEW_FLAG = B.VIEW_FLAG
		 AND A.PERIOD_ID = B.PERIOD_ID
		 AND A.REGION_CODE = B.REGION_CODE
		 AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
		 AND A.BG_CODE = B.BG_CODE
		 AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
		 AND A.MAIN_FLAG = B.MAIN_FLAG
		 AND NVL(A.CODE_ATTRIBUTES,1) = NVL(B.CODE_ATTRIBUTES,1)
		 AND NVL(A.SOFTWARE_MARK,2) = NVL(B.SOFTWARE_MARK,2)
		 '||V_INSERT_LV0_PROD_CODE
		  ||V_INSERT_LV1_PROD_CODE
		  ||V_INSERT_LV2_PROD_CODE
		  ||V_INSERT_LV3_PROD_CODE
		  ||V_INSERT_LV4_PROD_CODE
		  ||V_INSERT_DIMENSION_CODE 
		  ||V_INSERT_DIMENSION_SUBCATEGORY_CODE  
		  ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
		  ||V_INSERT_SPART_CODE
		  ;
		 DBMS_OUTPUT.PUT_LINE(V_SQL); 
		 
         EXECUTE IMMEDIATE V_SQL;
                 
 --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入数据到实际数补齐临时表,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
   

 --2.只补齐均价, 发货额和发货数量无需补齐
      V_SQL := 
           'INSERT INTO '||V_TO_TABLE||' 
                (VERSION_ID,
				 PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
			   ||V_DIMENSION_SUBCATEGORY_PARA  
			   ||V_DIMENSION_SUB_DETAIL_PARA
			   ||'
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 RMB_AVG_AMT ,
				 '||V_SPART_CODE
				 ||V_SPART_CN_NAME||'
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				 '||V_SOFTWARE||'
				 VIEW_FLAG ,
				 APPEND_FLAG,
                 CREATED_BY,
                 CREATION_DATE,
                 LAST_UPDATED_BY,
                 LAST_UPDATE_DATE,
                 DEL_FLAG
                )
                
    WITH FORWARD_FILLER_TEMP AS
     (
      --按照重量级团队, 采购信息维补齐, 前向补齐均价
      SELECT  PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
			   ||V_DIMENSION_SUBCATEGORY_PARA  
			   ||V_DIMENSION_SUB_DETAIL_PARA
			   ||'
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 FIRST_VALUE(T.RMB_COST_AMT) OVER(PARTITION BY '||V_LV0_PROD_CODE||V_LV1_PROD_CODE||V_LV2_PROD_CODE||V_LV3_PROD_CODE||V_LV4_PROD_CODE||V_DIMENSION_CODE ||V_DIMENSION_SUBCATEGORY_CODE  ||V_DIMENSION_SUB_DETAIL_CODE||V_SPART_CODE||V_SOFTWARE||'REGION_CODE,REPOFFICE_CODE,BG_CODE ,OVERSEA_FLAG,MAIN_FLAG,CODE_ATTRIBUTES,VIEW_FLAG,AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS COST_AMT_2, --新补齐的金额字段
				 RMB_AVG_AMT ,
				 FIRST_VALUE(T.RMB_AVG_AMT) OVER(PARTITION BY '||V_LV0_PROD_CODE||V_LV1_PROD_CODE||V_LV2_PROD_CODE||V_LV3_PROD_CODE||V_LV4_PROD_CODE||V_DIMENSION_CODE ||V_DIMENSION_SUBCATEGORY_CODE  ||V_DIMENSION_SUB_DETAIL_CODE||V_SPART_CODE||V_SOFTWARE||'REGION_CODE,REPOFFICE_CODE,BG_CODE ,OVERSEA_FLAG,MAIN_FLAG,CODE_ATTRIBUTES,VIEW_FLAG,AVG_AMT_FLAG ORDER BY T.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
				 '||V_SPART_CODE||'
				 APPEND_FLAG,
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				 '||V_SOFTWARE||'
				 VIEW_FLAG,
				 AVG_AMT_FLAG
        FROM (SELECT  
                      PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
			   ||V_DIMENSION_SUBCATEGORY_PARA  
			   ||V_DIMENSION_SUB_DETAIL_PARA
			   ||'
				 PROD_QTY ,
				 RMB_COST_AMT ,
				 RMB_AVG_AMT ,
				 '||V_SPART_CODE||' 
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				 SUM(A.NULL_FLAG) OVER(PARTITION BY '||V_LV0_PROD_CODE||V_LV1_PROD_CODE||V_LV2_PROD_CODE||V_LV3_PROD_CODE||V_LV4_PROD_CODE||V_DIMENSION_CODE ||V_DIMENSION_SUBCATEGORY_CODE  ||V_DIMENSION_SUB_DETAIL_CODE||V_SPART_CODE||V_SOFTWARE||'REGION_CODE,REPOFFICE_CODE,BG_CODE ,OVERSEA_FLAG,MAIN_FLAG,CODE_ATTRIBUTES,VIEW_FLAG ORDER BY PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
				 '||V_SOFTWARE||'
				  VIEW_FLAG,
				 APPEND_FLAG
                 FROM '||V_TEMP_TABLE||' A
				 ) T)
    
    --向后补齐均价
    SELECT 		 '||V_VERSION_ID||',
				 PERIOD_ID ,
				 PERIOD_YEAR ,
				 REGION_CODE ,
				 REGION_CN_NAME ,
				 REPOFFICE_CODE ,
				 REPOFFICE_CN_NAME ,
				 BG_CODE ,
				 BG_CN_NAME ,
				'||V_IN_LV0_PROD_PARA 
				 ||V_IN_LV1_PROD_PARA  
				 ||V_IN_LV2_PROD_PARA 
				 ||V_IN_LV3_PROD_PARA  
				 ||V_IN_LV4_PROD_PARA
				 ||V_IN_DIMENSION_PARA 
			   ||V_IN_DIMENSION_SUBCATEGORY_PARA  
			   ||V_IN_DIMENSION_SUB_DETAIL_PARA
			   ||'
				 PROD_QTY ,
				 (NVL(A.COST_AMT_2, A.COST_AMT_3)) AS RMB_COST_AMT,-- 202403版本新逻辑，不解密直接补齐 
				 (NVL(A.AVG_AMT_2, A.AVG_AMT_3)) AS RMB_AVG_AMT,-- 202403版本新逻辑，不解密直接补齐 ,
				 '||V_SPART_CODE
				 ||V_SPART_CN_NAME2||'
				 OVERSEA_FLAG,
				 MAIN_FLAG,
				 CODE_ATTRIBUTES,
				 '||V_SOFTWARE||'
				 VIEW_FLAG ,
				 APPEND_FLAG,
                 -1 AS CREATED_BY,
				CURRENT_TIMESTAMP AS CREATION_DATE,
				-1 AS LAST_UPDATED_BY,
				CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
				''N'' AS DEL_FLAG
				
      FROM (SELECT  A.PERIOD_ID ,
				 A.PERIOD_YEAR ,
				 A.REGION_CODE ,
				 A.REGION_CN_NAME ,
				 A.REPOFFICE_CODE ,
				 A.REPOFFICE_CN_NAME ,
				 A.BG_CODE ,
				 A.BG_CN_NAME ,
				'||V_IN_LV0_PROD_PARA 
				 ||V_IN_LV1_PROD_PARA  
				 ||V_IN_LV2_PROD_PARA 
				 ||V_IN_LV3_PROD_PARA  
				 ||V_IN_LV4_PROD_PARA
				 ||V_IN_DIMENSION_PARA 
				 ||V_IN_DIMENSION_SUBCATEGORY_PARA  
			   ||V_IN_DIMENSION_SUB_DETAIL_PARA
			   ||'
				 A.PROD_QTY ,
				 A.COST_AMT_2,
                 B.COST_AMT_3,
				 '||V_IN_SPART_CODE||'
				 A.OVERSEA_FLAG,
				 A.MAIN_FLAG,
				 A.CODE_ATTRIBUTES,
				 '||V_IN_SOFTWARE||'
				 A.VIEW_FLAG ,
				 APPEND_FLAG,
                 A.AVG_AMT_2,
                 B.AVG_AMT_3
              FROM FORWARD_FILLER_TEMP A
              LEFT JOIN (SELECT DISTINCT A.REGION_CODE,
                                        A.REPOFFICE_CODE,
                                        A.BG_CODE,
                                         '||V_IN_SPART_CODE||'
                                        A.OVERSEA_FLAG,
                                        A.MAIN_FLAG,
										A.CODE_ATTRIBUTES,
										'||V_IN_SOFTWARE||'
										A.VIEW_FLAG,
                                       '||V_IN_LV0_PROD_CODE 
										||V_IN_LV1_PROD_CODE  
										||V_IN_LV2_PROD_CODE 
										||V_IN_LV3_PROD_CODE  
										||V_IN_LV4_PROD_CODE
										||V_IN_DIMENSION_CODE
										||V_IN_DIMENSION_SUBCATEGORY_CODE  
			   ||V_IN_DIMENSION_SUB_DETAIL_CODE
			   ||'
                                        FIRST_VALUE(A.PERIOD_ID) OVER(PARTITION BY A.BG_CODE,A.VIEW_FLAG,A.OVERSEA_FLAG,A.MAIN_FLAG,A.CODE_ATTRIBUTES,'||V_IN_LV0_PROD_CODE ||V_IN_LV1_PROD_CODE  ||V_IN_LV2_PROD_CODE ||V_IN_LV3_PROD_CODE  ||V_IN_LV4_PROD_CODE||V_IN_DIMENSION_CODE ||V_IN_DIMENSION_SUBCATEGORY_CODE  ||V_IN_DIMENSION_SUB_DETAIL_CODE||V_IN_SPART_CODE||V_IN_SOFTWARE||'A.REGION_CODE,A.REPOFFICE_CODE ORDER BY A.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(A.AVG_AMT_2) OVER(PARTITION BY A.BG_CODE,A.VIEW_FLAG,A.OVERSEA_FLAG,A.MAIN_FLAG,A.CODE_ATTRIBUTES,'||V_IN_LV0_PROD_CODE ||V_IN_LV1_PROD_CODE  ||V_IN_LV2_PROD_CODE ||V_IN_LV3_PROD_CODE  ||V_IN_LV4_PROD_CODE||V_IN_DIMENSION_CODE ||V_IN_DIMENSION_SUBCATEGORY_CODE  ||V_IN_DIMENSION_SUB_DETAIL_CODE||V_IN_SPART_CODE||V_IN_SOFTWARE||'A.REGION_CODE,A.REPOFFICE_CODE ORDER BY A.PERIOD_ID ASC) AS AVG_AMT_3, --有均价的首条补齐均价
										FIRST_VALUE(A.COST_AMT_2) OVER(PARTITION BY A.BG_CODE,A.VIEW_FLAG,A.OVERSEA_FLAG,A.MAIN_FLAG,A.CODE_ATTRIBUTES,'||V_IN_LV0_PROD_CODE ||V_IN_LV1_PROD_CODE  ||V_IN_LV2_PROD_CODE ||V_IN_LV3_PROD_CODE  ||V_IN_LV4_PROD_CODE||V_IN_DIMENSION_CODE ||V_IN_DIMENSION_SUBCATEGORY_CODE  ||V_IN_DIMENSION_SUB_DETAIL_CODE||V_IN_SPART_CODE||V_IN_SOFTWARE||'A.REGION_CODE,A.REPOFFICE_CODE ORDER BY A.PERIOD_ID ASC) AS COST_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP A
                         WHERE A.AVG_AMT_FLAG > 0) B
                ON A.VIEW_FLAG = B.VIEW_FLAG   
               AND A.REGION_CODE = B.REGION_CODE
               AND A.REPOFFICE_CODE = B.REPOFFICE_CODE
               AND A.BG_CODE = B.BG_CODE
               AND A.PERIOD_ID < B.PERIOD_ID
               AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
               AND A.MAIN_FLAG = B.MAIN_FLAG
               AND NVL(A.CODE_ATTRIBUTES,1) = NVL(B.CODE_ATTRIBUTES,1)
			   AND NVL(A.SOFTWARE_MARK,2) = NVL(B.SOFTWARE_MARK,2)'
               ||V_INSERT_LV0_PROD_CODE
			   ||V_INSERT_LV1_PROD_CODE
			   ||V_INSERT_LV2_PROD_CODE
			   ||V_INSERT_LV3_PROD_CODE
			   ||V_INSERT_LV4_PROD_CODE
			   ||V_INSERT_DIMENSION_CODE
			   ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
			   ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
			   ||V_INSERT_SPART_CODE
               ||' ) A';
			   DBMS_OUTPUT.PUT_LINE(V_SQL);
               EXECUTE IMMEDIATE V_SQL;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价实际数到'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

