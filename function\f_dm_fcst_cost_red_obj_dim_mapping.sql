-- Name: f_dm_fcst_cost_red_obj_dim_mapping; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_cost_red_obj_dim_mapping(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：补齐降成本目标维度

事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_TOP_SPART_INFO()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_COST_RED_OBJ_DIM_MAPPING'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自序列:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_FROM_TABLE VARCHAR(100); 
  V_TO_TABLE VARCHAR(100);
  V_TO_MID_TABLE VARCHAR(100);
  V_PROD_PARA   VARCHAR(100);
  V_GROUP_PARA 	VARCHAR(100);			
  V_GROUP_LEVEL VARCHAR(100); 
  V_PARENT_PARA VARCHAR(100);
  V_PARENT_LEVEL VARCHAR(100);		


  
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行'
   );

  
   
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_COST_RED_OBJ_T';
	 V_TO_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_COST_RED_MID_FILL_UP_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_COST_RED_OBJ_FILL_UP_T'; 


	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 0
        AND UPPER(DATA_TYPE) = 'RED_DIM'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
  
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表降成本目标版本号信息, 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
   --删除当前会计期版数据, 支持单月重刷
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VERSION_ID = '||V_VERSION_ID||'';
  EXECUTE IMMEDIATE V_SQL ;
  
   --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP规格品清单的数据, 删除版本='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
   
  --清空中间表，中间表只留最新版本的数据
   V_SQL := 'TRUNCATE TABLE  '||V_TO_MID_TABLE;
  EXECUTE IMMEDIATE V_SQL ;
 
 
    --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '中间表清空完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	

  V_SQL := 'INSERT INTO  '||V_TO_MID_TABLE||'
   (
     PERIOD_ID,
	 LV0_PROD_RND_TEAM_CODE ,
	 LV0_PROD_RD_TEAM_CN_NAME ,
	 LV1_PROD_RND_TEAM_CODE ,
	 LV1_PROD_RD_TEAM_CN_NAME ,
	 LV2_PROD_RND_TEAM_CODE ,
	 LV2_PROD_RD_TEAM_CN_NAME ,
	 LV3_PROD_RND_TEAM_CODE ,
	 LV3_PROD_RD_TEAM_CN_NAME ,
	 LV4_PROD_RND_TEAM_CODE ,
	 LV4_PROD_RD_TEAM_CN_NAME ,
	 DIMENSION_CODE ,
	 DIMENSION_CN_NAME,
	 DIMENSION_SUBCATEGORY_CODE ,
	 DIMENSION_SUBCATEGORY_CN_NAME,
	 DIMENSION_SUB_DETAIL_CODE ,
	 DIMENSION_SUB_DETAIL_CN_NAME,
	 SPART_CODE,
	 SPART_CN_NAME,
	 OBJECTIVE,
	 SUMMARY_FLAG,
	 COST_REDUCTION_CODE,
	 COST_REDUCTION_CN_NAME,
	 COST_REDUCTION_LEVEL
	)
	WITH IRB_DIM_TEMP AS (
	--拿到PSP和STD全维度
	
	
SELECT DISTINCT 
	LV0_PROD_RND_TEAM_CODE ,
	LV0_PROD_RD_TEAM_CN_NAME ,
	 LV1_PROD_RND_TEAM_CODE ,
	 LV1_PROD_RD_TEAM_CN_NAME ,
	 LV2_PROD_RND_TEAM_CODE ,
	 LV2_PROD_RD_TEAM_CN_NAME ,
	 LV3_PROD_RND_TEAM_CODE ,
	 LV3_PROD_RD_TEAM_CN_NAME ,
	 LV4_PROD_RND_TEAM_CODE ,
	 LV4_PROD_RD_TEAM_CN_NAME ,
	 DIMENSION_CODE ,
	 DIMENSION_CN_NAME,
	 DIMENSION_SUBCATEGORY_CODE ,
	 DIMENSION_SUBCATEGORY_CN_NAME,
	 DIMENSION_SUB_DETAIL_CODE ,
	 DIMENSION_SUB_DETAIL_CN_NAME,
	 SPART_CODE
	-- MAIN_FLAG ,
	 --CODE_ATTRIBUTES 
	 FROM DM_FCST_ICT_PSP_IRB_MID_MON_SPART_T
UNION  
SELECT DISTINCT 
	 LV0_PROD_RND_TEAM_CODE ,
	 LV0_PROD_RD_TEAM_CN_NAME ,
	 LV1_PROD_RND_TEAM_CODE ,
	 LV1_PROD_RD_TEAM_CN_NAME ,
	 LV2_PROD_RND_TEAM_CODE ,
	 LV2_PROD_RD_TEAM_CN_NAME ,
	 LV3_PROD_RND_TEAM_CODE ,
	 LV3_PROD_RD_TEAM_CN_NAME ,
	 LV4_PROD_RND_TEAM_CODE ,
	 LV4_PROD_RD_TEAM_CN_NAME ,
	 DIMENSION_CODE ,
	 DIMENSION_CN_NAME,
	 DIMENSION_SUBCATEGORY_CODE ,
	 DIMENSION_SUBCATEGORY_CN_NAME,
	 DIMENSION_SUB_DETAIL_CODE ,
	 DIMENSION_SUB_DETAIL_CN_NAME,
	 SPART_CODE
	 --MAIN_FLAG ,
	 --CODE_ATTRIBUTES 
	 
	 FROM DM_FCST_ICT_STD_IRB_MID_MON_SPART_T
	 
	 
	),
	ACUTUAL_DATA_TEMP AS (
	--拿到有实际降成本的LV2
	SELECT 
	 DISTINCT 
		PERIOD_ID,
		LV1_PROD_RD_TEAM_CN_NAME,
		LV2_PROD_RD_TEAM_CN_NAME,
		OBJECTIVE,
		''N'' AS SUMMARY_FLAG
		FROM 
		'||V_FROM_TABLE||'
		WHERE VERSION_ID = '||V_VERSION_ID||'
		AND LV2_PROD_RD_TEAM_CN_NAME != ''合计''
		
	),
	SUMMARY_DATA_TEMP AS (
	--拿到有实际降成本的LV1
	SELECT 
	 DISTINCT
		PERIOD_ID,
		LV1_PROD_RD_TEAM_CN_NAME,
		LV2_PROD_RD_TEAM_CN_NAME,
		OBJECTIVE,
		''Y'' AS SUMMARY_FLAG
		FROM 
		'||V_FROM_TABLE||'
		WHERE VERSION_ID = '||V_VERSION_ID||'
		AND LV2_PROD_RD_TEAM_CN_NAME = ''合计''
	),
	
	ACTUAL_DIM AS
--从PSP和STD全维度里筛选出有实际降成本的维度


	(
SELECT  DISTINCT 
	 LV0_PROD_RND_TEAM_CODE ,
	 LV0_PROD_RD_TEAM_CN_NAME ,
	 LV1_PROD_RND_TEAM_CODE ,
	 LV1_PROD_RD_TEAM_CN_NAME ,
	 LV2_PROD_RND_TEAM_CODE ,
	 LV2_PROD_RD_TEAM_CN_NAME ,
	 LV3_PROD_RND_TEAM_CODE ,
	 LV3_PROD_RD_TEAM_CN_NAME ,
	 LV4_PROD_RND_TEAM_CODE ,
	 LV4_PROD_RD_TEAM_CN_NAME ,
	 DIMENSION_CODE ,
	 DIMENSION_CN_NAME,
	 DIMENSION_SUBCATEGORY_CODE ,
	 DIMENSION_SUBCATEGORY_CN_NAME,
	 DIMENSION_SUB_DETAIL_CODE ,
	 DIMENSION_SUB_DETAIL_CN_NAME,
	 SPART_CODE
	 --MAIN_FLAG ,
	 --CODE_ATTRIBUTES 
FROM  IRB_DIM_TEMP A
WHERE EXISTS (SELECT 1 FROM ACUTUAL_DATA_TEMP B WHERE A.LV1_PROD_RD_TEAM_CN_NAME = B.LV1_PROD_RD_TEAM_CN_NAME AND A.LV2_PROD_RD_TEAM_CN_NAME = B.LV2_PROD_RD_TEAM_CN_NAME)
	)
	SELECT 
	DISTINCT 
	 B.PERIOD_ID,
	 A.LV0_PROD_RND_TEAM_CODE ,
	 A.LV0_PROD_RD_TEAM_CN_NAME ,
	 A.LV1_PROD_RND_TEAM_CODE ,
	 A.LV1_PROD_RD_TEAM_CN_NAME ,
	 A.LV2_PROD_RND_TEAM_CODE ,
	 A.LV2_PROD_RD_TEAM_CN_NAME ,
	 A.LV3_PROD_RND_TEAM_CODE ,
	 A.LV3_PROD_RD_TEAM_CN_NAME ,
	 A.LV4_PROD_RND_TEAM_CODE ,
	 A.LV4_PROD_RD_TEAM_CN_NAME ,
	 A.DIMENSION_CODE ,
	 A.DIMENSION_CN_NAME,
	 A.DIMENSION_SUBCATEGORY_CODE ,
	 A.DIMENSION_SUBCATEGORY_CN_NAME,
	 A.DIMENSION_SUB_DETAIL_CODE ,
	 A.DIMENSION_SUB_DETAIL_CN_NAME,
	 A.SPART_CODE,
	 A.SPART_CODE AS SPART_CN_NAME,
	 B.OBJECTIVE,
	 B.SUMMARY_FLAG,
	 A.LV1_PROD_RND_TEAM_CODE  AS COST_REDUCTION_CODE,
	 A.LV1_PROD_RD_TEAM_CN_NAME AS COST_REDUCTION_CN_NAME,
	  ''LV1'' AS COST_REDUCTION_LEVEL
	 --A.MAIN_FLAG ,
	 --A.CODE_ATTRIBUTES 
	 FROM ACTUAL_DIM A
	 JOIN ACUTUAL_DATA_TEMP B
	 ON A.LV1_PROD_RD_TEAM_CN_NAME = B.LV1_PROD_RD_TEAM_CN_NAME 
	 AND A.LV2_PROD_RD_TEAM_CN_NAME = B.LV2_PROD_RD_TEAM_CN_NAME
	 
	 UNION ALL 
	 
SELECT 
	DISTINCT 
	 B.PERIOD_ID,
	 A.LV0_PROD_RND_TEAM_CODE ,
	 A.LV0_PROD_RD_TEAM_CN_NAME ,
	 A.LV1_PROD_RND_TEAM_CODE ,
	 A.LV1_PROD_RD_TEAM_CN_NAME ,
	 A.LV2_PROD_RND_TEAM_CODE ,
	 A.LV2_PROD_RD_TEAM_CN_NAME ,
	 A.LV3_PROD_RND_TEAM_CODE ,
	 A.LV3_PROD_RD_TEAM_CN_NAME ,
	 A.LV4_PROD_RND_TEAM_CODE ,
	 A.LV4_PROD_RD_TEAM_CN_NAME ,
	 A.DIMENSION_CODE ,
	 A.DIMENSION_CN_NAME,
	 A.DIMENSION_SUBCATEGORY_CODE ,
	 A.DIMENSION_SUBCATEGORY_CN_NAME,
	 A.DIMENSION_SUB_DETAIL_CODE ,
	 A.DIMENSION_SUB_DETAIL_CN_NAME,
	 A.SPART_CODE,
	 A.SPART_CODE AS SPART_CN_NAME,
	 B.OBJECTIVE,
	 B.SUMMARY_FLAG,
	 A.LV2_PROD_RND_TEAM_CODE  AS COST_REDUCTION_CODE,
	 A.LV2_PROD_RD_TEAM_CN_NAME AS COST_REDUCTION_CN_NAME,
	 ''LV2'' AS COST_REDUCTION_LEVEL
	 -- A.MAIN_FLAG ,
	 --A.CODE_ATTRIBUTES 
	 FROM 
	( SELECT * FROM IRB_DIM_TEMP  WHERE LV2_PROD_RND_TEAM_CODE NOT IN ( SELECT DISTINCT LV2_PROD_RND_TEAM_CODE FROM  ACTUAL_DIM)) A
	 JOIN SUMMARY_DATA_TEMP B
	 ON A.LV1_PROD_RD_TEAM_CN_NAME = B.LV1_PROD_RD_TEAM_CN_NAME  ';
	
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;
  

 
    --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数进临时表完成,版本号：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
 
 
--开启循环 
FOR V_NUM IN 1..8 LOOP
	IF V_NUM = 1 THEN 
		V_PROD_PARA := 'LV1_PROD_RND_TEAM_CODE ,
						LV1_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'LV1_PROD_RND_TEAM_CODE ,
							LV1_PROD_RD_TEAM_CN_NAME ,';
							
		V_GROUP_LEVEL  := '''LV1'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA := 'LV0_PROD_RND_TEAM_CODE ,
							LV0_PROD_RD_TEAM_CN_NAME ,';
							
		V_PARENT_LEVEL := '''LV0'' AS PARENT_LEVEL,' ;					
 
   ELSIF V_NUM = 2 THEN 
		V_PROD_PARA := 'LV2_PROD_RND_TEAM_CODE ,
						LV2_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'LV2_PROD_RND_TEAM_CODE ,
							LV2_PROD_RD_TEAM_CN_NAME ,';
							
		V_GROUP_LEVEL  := '''LV2'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA := 'LV1_PROD_RND_TEAM_CODE ,
							LV1_PROD_RD_TEAM_CN_NAME ,';
 
		V_PARENT_LEVEL := '''LV1'' AS PARENT_LEVEL,' ;
		
	ELSIF V_NUM = 3 THEN 
		V_PROD_PARA := 'LV3_PROD_RND_TEAM_CODE ,
						LV3_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'LV3_PROD_RND_TEAM_CODE ,
							LV3_PROD_RD_TEAM_CN_NAME ,';
							
		V_GROUP_LEVEL  := '''LV3'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA := 'LV2_PROD_RND_TEAM_CODE ,
							LV2_PROD_RD_TEAM_CN_NAME ,';
 
		V_PARENT_LEVEL := '''LV2'' AS PARENT_LEVEL,' ;

	ELSIF V_NUM = 4 THEN 
		V_PROD_PARA := 'LV4_PROD_RND_TEAM_CODE ,
						LV4_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'LV4_PROD_RND_TEAM_CODE ,
							LV4_PROD_RD_TEAM_CN_NAME ,';
							
		V_GROUP_LEVEL  := '''LV4'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA := 'LV3_PROD_RND_TEAM_CODE ,
							LV3_PROD_RD_TEAM_CN_NAME ,';
 
		V_PARENT_LEVEL := '''LV3'' AS PARENT_LEVEL,' ;
		
		
	ELSIF V_NUM = 5 THEN 
		V_PROD_PARA := 'LV4_PROD_RND_TEAM_CODE ,
						LV4_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'DIMENSION_CODE ,
							DIMENSION_CN_NAME ,';
							
		V_GROUP_LEVEL  := '''DIMENSION'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA := 'LV4_PROD_RND_TEAM_CODE ,
							LV4_PROD_RD_TEAM_CN_NAME ,';
 
		V_PARENT_LEVEL := '''LV4'' AS PARENT_LEVEL,' ;	
	
	
	ELSIF V_NUM = 6 THEN 
		V_PROD_PARA := 'LV4_PROD_RND_TEAM_CODE ,
						LV4_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'DIMENSION_SUBCATEGORY_CODE ,
							DIMENSION_SUBCATEGORY_CODE ,';
							
		V_GROUP_LEVEL  := '''SUBCATEGORY'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA :=  'DIMENSION_CODE ,
							DIMENSION_CN_NAME ,';
 
		V_PARENT_LEVEL := '''DIMENSION'' AS PARENT_LEVEL,' ;
	
	ELSIF V_NUM = 7 THEN 
		V_PROD_PARA := 'LV4_PROD_RND_TEAM_CODE ,
						LV4_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'DIMENSION_SUB_DETAIL_CODE ,
							DIMENSION_SUB_DETAIL_CODE ,';
							
		V_GROUP_LEVEL  := '''SUBDETAIL'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA :=  'DIMENSION_SUBCATEGORY_CODE ,
							DIMENSION_SUBCATEGORY_CODE ,';
 
		V_PARENT_LEVEL := '''SUBCATEGORY'' AS PARENT_LEVEL,' ;
	
	ELSIF V_NUM = 8 THEN 
		V_PROD_PARA := 'LV4_PROD_RND_TEAM_CODE ,
						LV4_PROD_RD_TEAM_CN_NAME ,';
		
		V_GROUP_PARA := 'SPART_CODE ,
							SPART_CN_NAME  ,';
							
		V_GROUP_LEVEL  := '''SPART'' AS GROUP_LEVEL,' ;
		
		V_PARENT_PARA := 'DIMENSION_SUB_DETAIL_CODE ,
							DIMENSION_SUB_DETAIL_CODE ,';
 
		V_PARENT_LEVEL := '''SUBDETAIL'' AS PARENT_LEVEL,' ;
		
	END IF ;	
	
	V_SQL := '
	INSERT INTO '||V_TO_TABLE||' (
	VERSION_ID ,
	PERIOD_ID,
	PROD_RND_TEAM_CODE,
	PROD_RD_TEAM_CN_NAME,
	GROUP_CODE ,
	GROUP_CN_NAME ,
	GROUP_LEVEL ,
	PARENT_CODE ,
	PARENT_CN_NAME ,
	PARENT_LEVEL ,
	--MAIN_FLAG ,
	--CODE_ATTRIBUTES,
	SUMMARY_FLAG ,
	OBJECTIVE ,
	COST_REDUCTION_CODE,
	COST_REDUCTION_CN_NAME,
	COST_REDUCTION_LEVEL,
	CREATED_BY ,
	CREATION_DATE ,
	LAST_UPDATED_BY ,
	LAST_UPDATE_DATE ,
	DEL_FLAG 
	)
	SELECT 
	DISTINCT '||V_VERSION_ID||',
	PERIOD_ID,
	'||V_PROD_PARA
	||V_GROUP_PARA
	||V_GROUP_LEVEL
	||V_PARENT_PARA
	||V_PARENT_LEVEL||'
	--MAIN_FLAG ,
	--CODE_ATTRIBUTES,
	SUMMARY_FLAG ,
	OBJECTIVE ,
	COST_REDUCTION_CODE,
	COST_REDUCTION_CN_NAME,
	COST_REDUCTION_LEVEL,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG
	FROM '||V_TO_MID_TABLE||'
	';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE IMMEDIATE V_SQL;
 END LOOP;
	  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

