-- Name: f_dm_fom_wip_quantity_t_init; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_wip_quantity_t_init(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023/12/22
创建人  ：许灿烽
背景描述：任务令交易量表初始化,给每个月的数据生成一个版本号，并更新 任务令交易量表 的 version_id
参数描述:x_result_status :是否成功
来源表:   FIN_DM_OPT_FOI.DM_FOM_WIP_QUANTITY_T--任务令交易量 
目标表:FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T
事例：FIN_DM_OPT_FOI.F_DM_FOM_WIP_QUANTITY_T_INIT()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_WIP_QUANTITY_T_INIT'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T'; -- 目标表
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_WIP_VERSION_ID BIGINT; --任务令交易量 的版本号
  V_MIN_PERIOD_ID  BIGINT; --任务令交易量的最小月份
  V_MAX_PERIOD_ID  BIGINT; --任务令交易量的最大月份
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--初始化：获取交易量表的最小月份和最大月份
SELECT CAST(MIN(CONCAT(SUBSTR(PERIOD_NAME,1,4), SUBSTR(PERIOD_NAME,6,2))) AS BIGINT) AS V_MIN_PERIOD_ID
,CAST(MAX(CONCAT(SUBSTR(PERIOD_NAME,1,4), SUBSTR(PERIOD_NAME,6,2))) AS BIGINT) AS V_MAX_PERIOD_ID
INTO V_MIN_PERIOD_ID,V_MAX_PERIOD_ID
FROM FIN_DM_OPT_FOI.DM_FOM_WIP_QUANTITY_T--任务令交易量 
WHERE VERSION_ID IS NULL
;

LOOP
IF V_MIN_PERIOD_ID > V_MAX_PERIOD_ID THEN 
 EXIT; 
ELSE
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_S')              
    INTO V_WIP_VERSION_ID
    FROM DUAL;
     
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_WIP_VERSION_ID,NULL,V_MIN_PERIOD_ID,1,'AUTO','DIM_WIP',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');

--更新 任务令交易量 的版本号
UPDATE FIN_DM_OPT_FOI.DM_FOM_WIP_QUANTITY_T
SET VERSION_ID = V_WIP_VERSION_ID
,UPDATED_BY = -1
,UPDATE_DATE = CURRENT_TIMESTAMP
,CREATED_BY = -1
,CREATION_DATE = CURRENT_TIMESTAMP
WHERE VERSION_ID IS NULL
AND CONCAT(SUBSTR(PERIOD_NAME,1,4), SUBSTR(PERIOD_NAME,6,2)) = V_MIN_PERIOD_ID
;

V_MIN_PERIOD_ID:=CAST(TO_CHAR(ADD_MONTHS(TO_DATE(CONCAT(V_MIN_PERIOD_ID,22),'YYYYMMDD'),1),'YYYYMM') AS BIGINT);
END IF; 
END LOOP; 


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '任务令交易量表初始化结束',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

