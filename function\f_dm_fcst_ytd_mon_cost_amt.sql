-- Name: f_dm_fcst_ytd_mon_cost_amt; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ytd_mon_cost_amt(f_cost_type character varying, f_granularity_type character varying, f_view_flag character varying, f_keystr character varying, f_customization_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述：路径一+路径二 
		  路径一 是主力Y 编码类型保持不变  
		  路径一 是主力Y 编码类型 '全选'
		 
		  虚化
		  1，实时计算虚化逻辑
		  2，预处理虚化SPART的成本分布图
		  
		  
		  3.用于月度累积成本分布图结果
		  
事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_YTD_MON_COST_AMT()
*/

DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FCST_YTD_MON_COST_AMT'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-2)||'01','YYYYMM');  --两年前首月
  
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
 
  V_LV0_PROD_PARA VARCHAR(150);
  V_LV1_PROD_PARA VARCHAR(150);  
  V_LV2_PROD_PARA VARCHAR(150); 
  V_LV3_PROD_PARA VARCHAR(150); 
  V_LV4_PROD_PARA VARCHAR(150); 
  V_LV0_PROD_CODE VARCHAR(150);
  V_LV1_PROD_CODE VARCHAR(150);  
  V_LV2_PROD_CODE VARCHAR(150); 
  V_LV3_PROD_CODE VARCHAR(150); 
  V_LV4_PROD_CODE VARCHAR(150);
  
  V_DIMENSION_PARA VARCHAR(200);
  V_DIMENSION_SUBCATEGORY_PARA VARCHAR(200); 
  V_DIMENSION_SUB_DETAIL_PARA VARCHAR(200); 
  
  
  V_NUM NUMERIC;
  V_GROUP_LEVEL VARCHAR(30);
  V_GROUP_PARA VARCHAR(100);
  V_PARENT_PARA VARCHAR(100);
  V_PROD_RND_TEAM_PARA VARCHAR(100);
  V_PROD_CODE VARCHAR(100);
  V_PROD_PARA VARCHAR(100);
  
  
  V_RMB_COST_AMT VARCHAR(200);
  V_SUM_COST_AMT VARCHAR(200);
  V_FROM_TABLE VARCHAR(100); 
  V_TO_TABLE VARCHAR(100);
  V_VIRTUAL_TABLE VARCHAR(100);
  V_TEMP_TABLE VARCHAR(30);
  V_JOIN_TABLE VARCHAR(50);
  V_MAIN_FLAG VARCHAR(10);
  V_PBI_FLAG VARCHAR(20);
  
--实时虚化的变量
  V_LV_CODE TEXT;
  V_VIEW_FLAG VARCHAR(10);
  V_GROUP_CODE  VARCHAR(30);
  V_PARENT_LEVEL VARCHAR(10);
  V_REGION_CODE VARCHAR(30);
  V_REPOFFICE_CODE   VARCHAR(30);
  V_BG_CODE  VARCHAR(30);
  V_OVERSEA_FLAG  VARCHAR(15);
  V_IN_LV_CODE  TEXT;
  
  V_DIMENSION_CODE VARCHAR(30);
	V_DIMENSION_PARAMETER VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);
   

  
   --判断PBI维度和成本类型选择来源表,目标表,关联表,金额字段
  IF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_MON_YTD_AMT_T';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_BASE_DETAIL_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_MON_YTD_AMT_T';
  ELSIF F_COST_TYPE = 'PSP' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_MON_YTD_AMT_T';
	 
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'IRB' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_MON_YTD_AMT_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'INDUS' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_BASE_DETAIL_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_MON_YTD_AMT_T';
  ELSIF F_COST_TYPE = 'STD' AND F_GRANULARITY_TYPE = 'PROD' THEN 
	 V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_BASE_DETAIL_SPART_ENTIRE_T';
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_MON_YTD_AMT_T';
	 
  ELSE 
	RETURN '入参有误';
  END IF;


  
  
    --判断路径，选择量纲字段
  IF F_VIEW_FLAG = 'DIMENSION'  THEN 
   V_DIMENSION_PARA := 'DIMENSION_CODE,DIMENSION_CN_NAME,';
   V_DIMENSION_SUBCATEGORY_PARA := 'DIMENSION_SUBCATEGORY_CODE ,DIMENSION_SUBCATEGORY_CN_NAME,'; 
   V_DIMENSION_SUB_DETAIL_PARA := 'DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,';  
   

 
 
  ELSIF F_VIEW_FLAG = 'PROD_SPART' THEN 
   V_DIMENSION_PARA := '';
   V_DIMENSION_SUBCATEGORY_PARA := ''; 
   V_DIMENSION_SUB_DETAIL_PARA := '';  

      
  END IF;
 

 
 --判断PBI维度选择PBI字段	
  IF  F_GRANULARITY_TYPE = 'IRB' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_RND_TEAM_CODE ,LV0_PROD_RD_TEAM_CN_NAME, ';
	V_LV1_PROD_PARA :='	LV1_PROD_RND_TEAM_CODE ,LV1_PROD_RD_TEAM_CN_NAME, ';  
	V_LV2_PROD_PARA :=' LV2_PROD_RND_TEAM_CODE ,LV2_PROD_RD_TEAM_CN_NAME, '; 
	V_LV3_PROD_PARA :=' LV3_PROD_RND_TEAM_CODE ,LV3_PROD_RD_TEAM_CN_NAME, '; 
	V_LV4_PROD_PARA :=' LV4_PROD_RND_TEAM_CODE ,LV4_PROD_RD_TEAM_CN_NAME, '; 
    
	V_PROD_PARA := 'PROD_RND_TEAM_CODE,PROD_RD_TEAM_CN_NAME,';

	V_LV0_PROD_CODE :='LV0_PROD_RND_TEAM_CODE ';
	V_LV1_PROD_CODE :='LV1_PROD_RND_TEAM_CODE ';  
	V_LV2_PROD_CODE :='LV2_PROD_RND_TEAM_CODE '; 
	V_LV3_PROD_CODE :='LV3_PROD_RND_TEAM_CODE '; 
	V_LV4_PROD_CODE :='LV4_PROD_RND_TEAM_CODE ';
	
  ELSIF  F_GRANULARITY_TYPE = 'INDUS' THEN 
	V_LV0_PROD_PARA :=' LV0_INDUSTRY_CATG_CODE ,LV0_INDUSTRY_CATG_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_INDUSTRY_CATG_CODE ,LV1_INDUSTRY_CATG_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_INDUSTRY_CATG_CODE ,LV2_INDUSTRY_CATG_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_INDUSTRY_CATG_CODE ,LV3_INDUSTRY_CATG_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_INDUSTRY_CATG_CODE ,LV4_INDUSTRY_CATG_CN_NAME,'; 

	
	
	
	V_PROD_PARA := 'INDUSTRY_CATG_CODE,INDUSTRY_CATG_CN_NAME,';
	
	V_LV0_PROD_CODE :='LV0_INDUSTRY_CATG_CODE ';
	V_LV1_PROD_CODE :='LV1_INDUSTRY_CATG_CODE ';  
	V_LV2_PROD_CODE :='LV2_INDUSTRY_CATG_CODE '; 
	V_LV3_PROD_CODE :='LV3_INDUSTRY_CATG_CODE '; 
	V_LV4_PROD_CODE :='LV4_INDUSTRY_CATG_CODE ';
	
  ELSIF  F_GRANULARITY_TYPE = 'PROD' THEN 
	V_LV0_PROD_PARA :=' LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,';
	V_LV1_PROD_PARA :='	LV1_PROD_LIST_CODE,LV1_PROD_LIST_CN_NAME,';  
	V_LV2_PROD_PARA :=' LV2_PROD_LIST_CODE,LV2_PROD_LIST_CN_NAME,'; 
	V_LV3_PROD_PARA :=' LV3_PROD_LIST_CODE,LV3_PROD_LIST_CN_NAME,'; 
	V_LV4_PROD_PARA :=' LV4_PROD_LIST_CODE,LV4_PROD_LIST_CN_NAME,'; 

	
	V_LV0_PROD_CODE :='LV0_PROD_LIST_CODE ';
	V_LV1_PROD_CODE :='LV1_PROD_LIST_CODE ';  
	V_LV2_PROD_CODE :='LV2_PROD_LIST_CODE '; 
	V_LV3_PROD_CODE :='LV3_PROD_LIST_CODE '; 
	V_LV4_PROD_CODE :='LV4_PROD_LIST_CODE ';
	
	
	V_PROD_PARA := 'PROD_LIST_CODE,PROD_LIST_CN_NAME,';
	
  ELSE 
	NULL ;
  END IF;
 
 
 
   --判断成本类型选择金额字段的处理方式
  IF F_COST_TYPE = 'PSP' THEN 
  
  
   V_RMB_COST_AMT := ' CASE WHEN RMB_COST_AMT IS NOT NULL  THEN RMB_COST_AMT 
					   ELSE 0  END AS RMB_COST_AMT,  ';  --对于需要补齐的数据补0 
  
   V_SUM_COST_AMT := 'SUM(RMB_COST_AMT) AS RMB_COST_AMT,';
   
   V_VIRTUAL_TABLE := 'DM_FCST_ICT_PSP_BASE_CUS_MON_YTD_COST_AMT_T';
	
	
   V_TEMP_TABLE := 'DATA_TEMP';	
   
   V_JOIN_TABLE := ' DM_FCST_ICT_PSP_BASE_CUS_DIM_T ';  --实施虚化的关联表


   
  ELSIF F_COST_TYPE = 'STD' THEN 
  
	V_RMB_COST_AMT := ' TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'')) AS RMB_COST_AMT,';
    
	V_SUM_COST_AMT := 'SUM(RMB_COST_AMT)   AS RMB_COST_AMT,';
	
    V_VIRTUAL_TABLE := 'DM_FCST_ICT_STD_BASE_CUS_MON_YTD_COST_AMT_T';
    
	V_TEMP_TABLE := 'DATA_TEMP';
	
	V_JOIN_TABLE := ' DM_FCST_ICT_STD_BASE_CUS_DIM_T '; --实施虚化的关联表
	
 END IF;
 
 
 
    

  
 
   --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
 


 
--创建临时表
--用来存储全量数据以及后续只取主力编码时的卷积
DROP TABLE IF EXISTS DATA_TEMP;
CREATE TEMPORARY TABLE DATA_TEMP (
PERIOD_ID NUMERIC,
PERIOD_YEAR NUMERIC,
REGION_CODE CHARACTER VARYING(50),
REGION_CN_NAME CHARACTER VARYING(200),
REGION_EN_NAME CHARACTER VARYING(200),
REPOFFICE_CODE CHARACTER VARYING(50),
REPOFFICE_CN_NAME CHARACTER VARYING(200),
REPOFFICE_EN_NAME CHARACTER VARYING(200),
BG_CODE CHARACTER VARYING(50),
BG_CN_NAME CHARACTER VARYING(200),
LV0_PROD_LIST_CODE CHARACTER VARYING(50),
LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
LV1_PROD_LIST_CODE CHARACTER VARYING(50),
LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
LV2_PROD_LIST_CODE CHARACTER VARYING(50),
LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
LV3_PROD_LIST_CODE CHARACTER VARYING(50),
LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
LV4_PROD_LIST_CODE CHARACTER VARYING(50),
LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
LV0_PROD_RND_TEAM_CODE CHARACTER VARYING(100),
LV0_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(200),
LV1_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
LV1_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
LV2_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
LV2_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
LV3_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
LV3_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
LV4_PROD_RND_TEAM_CODE CHARACTER VARYING(50),
LV4_PROD_RD_TEAM_CN_NAME CHARACTER VARYING(600),
LV0_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
LV0_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
LV1_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
LV1_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
LV2_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
LV2_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
LV3_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
LV3_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
LV4_INDUSTRY_CATG_CODE CHARACTER VARYING(50),
LV4_INDUSTRY_CATG_CN_NAME CHARACTER VARYING(600),
DIMENSION_CODE CHARACTER VARYING(500),
DIMENSION_CN_NAME CHARACTER VARYING(200),
DIMENSION_EN_NAME CHARACTER VARYING(200),
DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(500),
DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(200),
DIMENSION_SUB_DETAIL_CODE CHARACTER VARYING(500),
DIMENSION_SUB_DETAIL_CN_NAME CHARACTER VARYING(200),
PROD_QTY NUMERIC,
RMB_COST_AMT NUMERIC,
SPART_CODE CHARACTER VARYING(188),
SPART_CN_NAME CHARACTER VARYING(188),
OVERSEA_FLAG CHARACTER VARYING(1),
MAIN_FLAG VARCHAR(1),
CODE_ATTRIBUTES VARCHAR(50),
VIEW_FLAG VARCHAR(10),
APPEND_FLAG VARCHAR(15),
SOFTWARE_MARK VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE);

			

		
IF F_COST_TYPE = 'PSP' THEN 			
    V_SQL := 
       'INSERT INTO '||V_TEMP_TABLE||' ( 
             PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				PROD_QTY ,
				RMB_COST_AMT ,
				SPART_CODE,
				SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				)
		
		
		--对原始数据做月累计计算
		SELECT PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				SUM(PROD_QTY) OVER(PARTITION BY PERIOD_YEAR,REGION_CODE ,REPOFFICE_CODE,BG_CODE,
										'||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||V_DIMENSION_PARA ||V_DIMENSION_SUBCATEGORY_PARA  ||V_DIMENSION_SUB_DETAIL_PARA||' 
										SPART_CODE,OVERSEA_FLAG,MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK,VIEW_FLAG ORDER BY PERIOD_ID ),
				SUM(RMB_COST_AMT) OVER(PARTITION BY PERIOD_YEAR,REGION_CODE ,REPOFFICE_CODE,BG_CODE,
										'||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||V_DIMENSION_PARA ||V_DIMENSION_SUBCATEGORY_PARA  ||V_DIMENSION_SUB_DETAIL_PARA||' 
										SPART_CODE,OVERSEA_FLAG,MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK,VIEW_FLAG ORDER BY PERIOD_ID ),
				SPART_CODE,
				SPART_CODE AS SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				FROM
		
		
		(
		SELECT PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				PROD_QTY ,
				'||V_RMB_COST_AMT||'
				SPART_CODE,
				SPART_CODE AS SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				FROM '||V_FROM_TABLE||'
				WHERE VIEW_FLAG = '''||F_VIEW_FLAG||''' 
				
				
				)';
				
				DBMS_OUTPUT.PUT_LINE(V_SQL);

				EXECUTE IMMEDIATE V_SQL;

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '数据插入临时表完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
		F_FORMULA_SQL_TXT => V_SQL,
        F_ERRBUF => 'SUCCESS');  

ELSIF  F_COST_TYPE = 'STD' THEN 
	V_SQL := 
       'INSERT INTO '||V_TEMP_TABLE||' ( 
             PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				PROD_QTY ,
				RMB_COST_AMT ,
				SPART_CODE,
				SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				)
				
				
				
		--对原始数据做月累计计算
		SELECT PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				SUM(PROD_QTY) OVER(PARTITION BY PERIOD_YEAR,REGION_CODE ,REPOFFICE_CODE,BG_CODE,
										'||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||V_DIMENSION_PARA ||V_DIMENSION_SUBCATEGORY_PARA  ||V_DIMENSION_SUB_DETAIL_PARA||' 
										SPART_CODE,OVERSEA_FLAG,MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK,VIEW_FLAG ORDER BY PERIOD_ID ),
				SUM(RMB_COST_AMT) OVER(PARTITION BY PERIOD_YEAR,REGION_CODE ,REPOFFICE_CODE,BG_CODE,
										'||V_LV0_PROD_PARA ||V_LV1_PROD_PARA  ||V_LV2_PROD_PARA ||V_LV3_PROD_PARA  ||V_LV4_PROD_PARA||V_DIMENSION_PARA ||V_DIMENSION_SUBCATEGORY_PARA  ||V_DIMENSION_SUB_DETAIL_PARA||' 
										SPART_CODE,OVERSEA_FLAG,MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),SOFTWARE_MARK,VIEW_FLAG ORDER BY PERIOD_ID ),
				SPART_CODE,
				SPART_CODE AS SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				FROM		
				
				
		(SELECT PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				PROD_QTY ,
				CASE WHEN RMB_COST_AMT IS NOT NULL THEN TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))
					  ELSE ''0''  END AS RMB_COST_AMT, 
				SPART_CODE,
				SPART_CODE AS SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				FROM '||V_FROM_TABLE||'
				WHERE VIEW_FLAG = '''||F_VIEW_FLAG||''' 
				AND APPEND_FLAG = ''Y''
				UNION ALL 
		SELECT PERIOD_ID ,
				PERIOD_YEAR ,
				REGION_CODE ,
				REGION_CN_NAME ,
				REPOFFICE_CODE ,
				REPOFFICE_CN_NAME ,
				BG_CODE ,
				BG_CN_NAME ,
				'||V_LV0_PROD_PARA 
				 ||V_LV1_PROD_PARA  
				 ||V_LV2_PROD_PARA 
				 ||V_LV3_PROD_PARA  
				 ||V_LV4_PROD_PARA
				 ||V_DIMENSION_PARA 
				 ||V_DIMENSION_SUBCATEGORY_PARA  
				 ||V_DIMENSION_SUB_DETAIL_PARA||' 
				PROD_QTY ,
				'||V_RMB_COST_AMT||'
				SPART_CODE,
				SPART_CODE AS SPART_CN_NAME,
				OVERSEA_FLAG,
				MAIN_FLAG,
				CODE_ATTRIBUTES,
				SOFTWARE_MARK,
				VIEW_FLAG ,
                APPEND_FLAG 
				FROM '||V_FROM_TABLE||'
				WHERE VIEW_FLAG = '''||F_VIEW_FLAG||''' 
				AND APPEND_FLAG = ''N''				
				)'
				
				;
				
				DBMS_OUTPUT.PUT_LINE(V_SQL);

				EXECUTE IMMEDIATE V_SQL;

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '数据插入临时表完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
		F_FORMULA_SQL_TXT => V_SQL,
        F_ERRBUF => 'SUCCESS'); 
		
END IF ;	
 
 
 
 
 
 
 
--大方向区分，由 f_customization_id 判定调用函数是正常跑逻辑还是进行实时虚化

IF f_customization_id IS NULL  THEN 
 
 

 
  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'DELETE FROM  '||V_TO_TABLE||' WHERE VIEW_FLAG = '''||F_VIEW_FLAG||''' ';
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_FORMULA_SQL_TXT => V_SQL,
   F_ERRBUF => 'SUCCESS');
  


	IF F_VIEW_FLAG = 'PROD_SPART' THEN 
	V_NUM := 6;
	
	ELSIF F_VIEW_FLAG = 'DIMENSION' THEN 
	V_NUM := 3;
	
	ELSE NULL;
	
	END IF;
	
	
FOR V_RUN_NUM IN 1..V_NUM LOOP 


	IF F_VIEW_FLAG = 'DIMENSION' AND V_RUN_NUM = 1 THEN 
	
	V_GROUP_LEVEL := '''SUB_DETAIL'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := 'DIMENSION_SUB_DETAIL_CODE ,
					 DIMENSION_SUB_DETAIL_CN_NAME,';
	
	V_PARENT_PARA := 'DIMENSION_SUBCATEGORY_CODE ,
					  DIMENSION_SUBCATEGORY_CN_NAME ,';
					  
	V_PROD_RND_TEAM_PARA := V_LV4_PROD_PARA;
	
	ELSIF F_VIEW_FLAG = 'DIMENSION' AND V_RUN_NUM = 2 THEN 
	
	V_GROUP_LEVEL := '''SUBCATEGORY'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := 'DIMENSION_SUBCATEGORY_CODE ,
					 DIMENSION_SUBCATEGORY_CN_NAME ,';
	
	V_PARENT_PARA := 'DIMENSION_CODE ,
					  DIMENSION_CN_NAME ,';
					  
	V_PROD_RND_TEAM_PARA := V_LV4_PROD_PARA;
	

	V_DIMENSION_SUB_DETAIL_PARA := '';
		
		
	ELSIF F_VIEW_FLAG = 'DIMENSION' AND V_RUN_NUM = 3 THEN 
	
	V_GROUP_LEVEL := '''DIMENSION'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := 'DIMENSION_CODE ,
					 DIMENSION_CN_NAME ,';
	
	V_PARENT_PARA := V_DIMENSION_PARA;
					  
	V_PROD_RND_TEAM_PARA := V_LV4_PROD_PARA;
	
	V_DIMENSION_SUBCATEGORY_PARA := '';
	V_DIMENSION_SUB_DETAIL_PARA := '';


	ELSIF 	F_VIEW_FLAG = 'PROD_SPART' AND V_RUN_NUM = 1 THEN 
	
	V_GROUP_LEVEL := '''SPART'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := 'SPART_CODE,SPART_CN_NAME ,';
	
	V_PARENT_PARA := V_LV4_PROD_PARA;
					  
	V_PROD_RND_TEAM_PARA := V_LV4_PROD_PARA;
	
	
		
    ELSIF 	F_VIEW_FLAG = 'PROD_SPART' AND V_RUN_NUM = 2 THEN 
	
	V_GROUP_LEVEL := '''LV4'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := V_LV4_PROD_PARA ;
	
	V_PARENT_PARA := V_LV3_PROD_PARA;
					  
	V_PROD_RND_TEAM_PARA := V_LV4_PROD_PARA;
		
	
	ELSIF 	F_VIEW_FLAG = 'PROD_SPART' AND V_RUN_NUM = 3 THEN 
	
	V_GROUP_LEVEL := '''LV3'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := V_LV3_PROD_PARA ;
	
	V_PARENT_PARA := V_LV2_PROD_PARA;
					  
	V_PROD_RND_TEAM_PARA := V_LV3_PROD_PARA;
	
	
	ELSIF 	F_VIEW_FLAG = 'PROD_SPART' AND V_RUN_NUM = 4 THEN 
	
	V_GROUP_LEVEL := '''LV2'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := V_LV2_PROD_PARA ;
	
	V_PARENT_PARA := V_LV1_PROD_PARA;
					  
	V_PROD_RND_TEAM_PARA := V_LV2_PROD_PARA;
	
	
	
	ELSIF 	F_VIEW_FLAG = 'PROD_SPART' AND V_RUN_NUM = 5 THEN 
	
	V_GROUP_LEVEL := '''LV1'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := V_LV1_PROD_PARA ;
	
	V_PARENT_PARA := V_LV0_PROD_PARA;
					  
	V_PROD_RND_TEAM_PARA := V_LV1_PROD_PARA;
	
	
	
	ELSIF 	F_VIEW_FLAG = 'PROD_SPART' AND V_RUN_NUM = 6 THEN 
	
	V_GROUP_LEVEL := '''LV0'' AS GROUP_LEVEL,';
	
	V_GROUP_PARA := V_LV0_PROD_PARA ;
	
	V_PARENT_PARA := V_LV0_PROD_PARA;
					  		  
					  
	V_PROD_RND_TEAM_PARA := V_LV0_PROD_PARA;
	
	ELSE NULL;
	
	END IF;
	

	
DBMS_OUTPUT.PUT_LINE('循环处理开始');	
   
   
   
   V_SQL := '
   INSERT INTO '||V_TO_TABLE||' (
	
    VERSION_ID ,
	PERIOD_YEAR ,
	PERIOD_ID ,
	'||V_PROD_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA||'
	GROUP_CODE ,
	GROUP_CN_NAME ,
	GROUP_LEVEL ,
	RMB_COST_AMT ,
	PARENT_CODE ,
	PARENT_CN_NAME ,
	MAIN_FLAG ,
	SOFTWARE_MARK,
	VIEW_FLAG ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG ,
	CREATED_BY ,
	CREATION_DATE ,
	LAST_UPDATED_BY ,
	LAST_UPDATE_DATE ,
	DEL_FLAG ,
	CODE_ATTRIBUTES ,
	ACTUAL_QTY 
	)
	SELECT '||V_VERSION_ID||',
	PERIOD_YEAR,
	PERIOD_ID,
	'||V_PROD_RND_TEAM_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA
	||V_GROUP_PARA
	||V_GROUP_LEVEL
	||V_SUM_COST_AMT
	||V_PARENT_PARA||'
	MAIN_FLAG,
	SOFTWARE_MARK,
	VIEW_FLAG,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG,
	CODE_ATTRIBUTES,
	SUM(PROD_QTY) PROD_QTY
FROM '||V_TEMP_TABLE||'
WHERE MAIN_FLAG = ''Y''
GROUP BY 
	PERIOD_YEAR,
	PERIOD_ID,
	'||V_PROD_RND_TEAM_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA
	||V_GROUP_PARA
	||V_PARENT_PARA||'
	MAIN_FLAG,
	SOFTWARE_MARK,
	VIEW_FLAG,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG,
	CODE_ATTRIBUTES
UNION ALL
SELECT '||V_VERSION_ID||',
	PERIOD_YEAR,
	PERIOD_ID,
	'||V_PROD_RND_TEAM_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA
	||V_GROUP_PARA
	||V_GROUP_LEVEL
	||V_SUM_COST_AMT
	||V_PARENT_PARA||'
	''N'' AS MAIN_FLAG,
	SOFTWARE_MARK,
	VIEW_FLAG,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG,
	NULL AS CODE_ATTRIBUTES,
	SUM(PROD_QTY) PROD_QTY
FROM '||V_TEMP_TABLE||'
GROUP BY 
	PERIOD_YEAR,
	PERIOD_ID,
	'||V_PROD_RND_TEAM_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA
	||V_GROUP_PARA
	||V_PARENT_PARA||'
	SOFTWARE_MARK,
	VIEW_FLAG,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG
	'
	;
	
	DBMS_OUTPUT.PUT_LINE(V_SQL);		
	EXECUTE IMMEDIATE V_SQL;

	
	
	END LOOP;
	

        	
	
IF F_VIEW_FLAG = 'PROD_SPART' THEN

--如果是路径1，还要对主力编码做一次汇总
V_SQL := '  
	INSERT INTO '||V_TO_TABLE||' (
    VERSION_ID ,
	PERIOD_YEAR ,
	PERIOD_ID ,
	'||V_PROD_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA||'
	GROUP_CODE ,
	GROUP_CN_NAME ,
	GROUP_LEVEL ,
	RMB_COST_AMT ,
	PARENT_CODE ,
	PARENT_CN_NAME ,
	MAIN_FLAG ,
	SOFTWARE_MARK,
	VIEW_FLAG ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG ,
	CREATED_BY ,
	CREATION_DATE ,
	LAST_UPDATED_BY ,
	LAST_UPDATE_DATE ,
	DEL_FLAG ,
	CODE_ATTRIBUTES ,
	ACTUAL_QTY 
	)
SELECT '||V_VERSION_ID||',
	PERIOD_YEAR,
	PERIOD_ID,
	'||V_PROD_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA||'
	GROUP_CODE ,
	GROUP_CN_NAME ,
	GROUP_LEVEL ,
	'||V_SUM_COST_AMT||'
	PARENT_CODE ,
	PARENT_CN_NAME ,
	MAIN_FLAG,
	SOFTWARE_MARK,
	VIEW_FLAG,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG,
	''全选'' AS CODE_ATTRIBUTES,
	SUM(ACTUAL_QTY) AS ACTUAL_QTY
FROM '||V_TO_TABLE||'
WHERE MAIN_FLAG = ''Y''
	AND VIEW_FLAG = ''PROD_SPART''
GROUP BY 
	PERIOD_YEAR,
	PERIOD_ID,
	'||V_PROD_PARA
	||V_DIMENSION_PARA
	||V_DIMENSION_SUBCATEGORY_PARA
	||V_DIMENSION_SUB_DETAIL_PARA||'
	GROUP_CODE ,
	GROUP_CN_NAME ,
	GROUP_LEVEL ,
	PARENT_CODE ,
	PARENT_CN_NAME ,
	MAIN_FLAG,
	SOFTWARE_MARK,
	VIEW_FLAG,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	BG_CODE ,
	BG_CN_NAME ,
	OVERSEA_FLAG
	';
	
	DBMS_OUTPUT.PUT_LINE(V_SQL);		
	EXECUTE IMMEDIATE V_SQL;	

	
--2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '编码标识全选造数完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
		F_FORMULA_SQL_TXT => V_SQL,
        F_ERRBUF => 'SUCCESS'); 


--虚化预处理
--处理除LV0层下的SPART卷积

V_SQL := 'DELETE FROM '||V_VIRTUAL_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND  CUSTOM_ID IS NULL AND  PARENT_LEVEL = ''LV0'' AND GROUP_LEVEL = ''SPART'' ' ;
EXECUTE IMMEDIATE V_SQL;


  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空虚化预处理表'||V_VIRTUAL_TABLE||'数据,版本:'||V_VERSION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_FORMULA_SQL_TXT => V_SQL,
   F_ERRBUF => 'SUCCESS');


V_SQL := 'INSERT INTO '||V_VIRTUAL_TABLE||' (
		  VERSION_ID,
		  CUSTOM_ID,
		  CUSTOM_CN_NAME,
		  PERIOD_YEAR,
		  PERIOD_ID,
		  GROUP_CODE,
		  GROUP_CN_NAME,
		  GROUP_LEVEL,
		  PARENT_LEVEL,
		  RMB_COST_AMT,
		  PARENT_CODE,
		  PARENT_CN_NAME,
		  MAIN_FLAG,
		  SOFTWARE_MARK,
		  VIEW_FLAG,
		  REGION_CODE,
		  REGION_CN_NAME,
		  REPOFFICE_CODE,
		  REPOFFICE_CN_NAME,
		  BG_CODE,
		  BG_CN_NAME,
		  OVERSEA_FLAG,
		  CREATED_BY,
		  CREATION_DATE,
		  LAST_UPDATED_BY,
		  LAST_UPDATE_DATE,
		  DEL_FLAG,
		  CODE_ATTRIBUTES,
		  GRANULARITY_TYPE,
		  ACTUAL_QTY
        )
	SELECT '||V_VERSION_ID||',
		NULL AS CUSTOM_ID,
		NULL AS CUSTOM_CN_NAME,
		PERIOD_YEAR,
		PERIOD_ID,
		SPART_CODE AS GROUP_CODE,
		SPART_CODE AS GROUP_CN_NAME,
		''SPART'' AS GROUP_LEVEL,
		''LV0'' AS PARENT_LEVEL,
		'||V_SUM_COST_AMT
		 ||V_LV0_PROD_PARA||'
		 MAIN_FLAG,
		 SOFTWARE_MARK,
		 VIEW_FLAG,
		 REGION_CODE,
		 REGION_CN_NAME,
		 REPOFFICE_CODE,
		 REPOFFICE_CN_NAME,
		 BG_CODE,
		 BG_CN_NAME,
		 OVERSEA_FLAG,
		 -1 AS CREATED_BY,
	     CURRENT_TIMESTAMP AS CREATION_DATE,
	     -1 AS LAST_UPDATED_BY,
	     CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	     ''N'' AS DEL_FLAG,
		 CODE_ATTRIBUTES,
		 '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
		 SUM(PROD_QTY) AS PROD_QTY
	FROM '||V_TEMP_TABLE||'
	WHERE VIEW_FLAG = ''PROD_SPART''
		 AND CODE_ATTRIBUTES != ''全选''
	GROUP BY 
		PERIOD_YEAR,
		PERIOD_ID,
		SPART_CODE,
		'||V_LV0_PROD_PARA||'
		 MAIN_FLAG,
		 SOFTWARE_MARK,
		 VIEW_FLAG,
		 REGION_CODE,
		 REGION_CN_NAME,
		 REPOFFICE_CODE,
		 REPOFFICE_CN_NAME,
		 BG_CODE,
		 BG_CN_NAME,
		 OVERSEA_FLAG,
		 CODE_ATTRIBUTES
		';
		
	DBMS_OUTPUT.PUT_LINE(V_SQL);		
	EXECUTE IMMEDIATE V_SQL;	

--2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '虚化预处理完成,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
	
--路径2不参与虚化，也不需要造主力编码
ELSE NULL;
END IF;	
	
--走实施虚化逻辑	
ELSIF F_CUSTOMIZATION_ID IS NOT NULL THEN 
	
	
	V_SQL := 'DELETE FROM '||V_VIRTUAL_TABLE||' WHERE CUSTOM_ID = '|| F_CUSTOMIZATION_ID||' AND VERSION_ID = '||V_VERSION_ID  ;
	EXECUTE IMMEDIATE V_SQL;


  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空虚化表'||V_VIRTUAL_TABLE||'的实时虚化数据,版本:'||V_VERSION_ID||',虚化ID：'|| F_CUSTOMIZATION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_FORMULA_SQL_TXT => V_SQL,
   F_ERRBUF => 'SUCCESS');

	 V_SQL := '
		SELECT DISTINCT LV_CODE,VIEW_FLAG, GROUP_LEVEL,GROUP_CODE,PARENT_LEVEL,
		REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
		REPLACE(LV_CODE,'','','''''','''''') AS IN_LV_CODE
		  FROM ' || V_JOIN_TABLE || '
		WHERE CUSTOM_ID = ' || F_CUSTOMIZATION_ID || ';';

		DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE V_SQL INTO V_LV_CODE, V_VIEW_FLAG, V_GROUP_LEVEL, V_GROUP_CODE, V_PARENT_LEVEL,
  V_REGION_CODE,V_REPOFFICE_CODE,V_BG_CODE,V_OVERSEA_FLAG,V_IN_LV_CODE;
	
	
	
	
	--通过V_PARENT_LEVEL判断选定的PBI层级
	IF V_PARENT_LEVEL = 'LV0' THEN 
		V_PROD_CODE := V_LV0_PROD_CODE ;
		V_PROD_PARA := V_LV0_PROD_PARA ;
	ELSIF V_PARENT_LEVEL = 'LV1' THEN 
		V_PROD_CODE := V_LV1_PROD_CODE ;
		V_PROD_PARA := V_LV1_PROD_PARA ;
	ELSIF V_PARENT_LEVEL = 'LV2' THEN 
		V_PROD_CODE := V_LV2_PROD_CODE ;
		V_PROD_PARA := V_LV2_PROD_PARA ;
		
	ELSIF V_PARENT_LEVEL = 'LV3' THEN 
		V_PROD_CODE := V_LV3_PROD_CODE ;
		V_PROD_PARA := V_LV3_PROD_PARA ;
		
	ELSIF V_PARENT_LEVEL = 'LV4' THEN 
		V_PROD_CODE := V_LV4_PROD_CODE ;
		V_PROD_PARA := V_LV4_PROD_PARA ;
		
	END IF ;	
	
	
	IF V_VIEW_FLAG = 'PROD_SPART' THEN 
	
		IF F_COST_TYPE = 'PSP' THEN 
			SELECT MAIN_FLAG INTO V_MAIN_FLAG FROM DM_FCST_ICT_PSP_BASE_CUS_DIM_T WHERE CUSTOM_ID = F_CUSTOMIZATION_ID
				LIMIT 1;
		
		ELSIF F_COST_TYPE = 'STD' THEN 
			SELECT MAIN_FLAG INTO V_MAIN_FLAG FROM DM_FCST_ICT_STD_BASE_CUS_DIM_T WHERE CUSTOM_ID = F_CUSTOMIZATION_ID
				LIMIT 1;
		END IF;
	
IF V_MAIN_FLAG = 'Y' THEN 
	
	V_SQL := 'INSERT INTO '||V_VIRTUAL_TABLE||' (
		  VERSION_ID,
		  CUSTOM_ID,
		  CUSTOM_CN_NAME,
		  PERIOD_YEAR,
		  PERIOD_ID,
		  GROUP_CODE,
		  GROUP_CN_NAME,
		  GROUP_LEVEL,
		  PARENT_LEVEL,
		  RMB_COST_AMT,
		  PARENT_CODE,
		  PARENT_CN_NAME,
		  MAIN_FLAG,
		  SOFTWARE_MARK,
		  VIEW_FLAG,
		  REGION_CODE,
		  REGION_CN_NAME,
		  REPOFFICE_CODE,
		  REPOFFICE_CN_NAME,
		  BG_CODE,
		  BG_CN_NAME,
		  OVERSEA_FLAG,
		  CREATED_BY,
		  CREATION_DATE,
		  LAST_UPDATED_BY,
		  LAST_UPDATE_DATE,
		  DEL_FLAG,
		  CODE_ATTRIBUTES,
		  GRANULARITY_TYPE,
		  ACTUAL_QTY
        )
		SELECT 
			'||V_VERSION_ID||',
			'|| F_CUSTOMIZATION_ID ||',
			'|| F_CUSTOMIZATION_ID ||',
			PERIOD_YEAR,
			PERIOD_ID,
			SPART_CODE,
			SPART_CODE AS SPART_CN_NAME,
			'''||V_GROUP_LEVEL||''' AS GROUP_LEVEL,
			'''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			'||V_PROD_PARA||'
			MAIN_FLAG,
			SOFTWARE_MARK,
			''PROD_SPART'' AS VIEW_FLAG,
			REGION_CODE,
		    REGION_CN_NAME,
		    REPOFFICE_CODE,
		    REPOFFICE_CN_NAME,
		    BG_CODE,
		    BG_CN_NAME,
		    OVERSEA_FLAG,
		    -1 AS CREATED_BY,
			CURRENT_TIMESTAMP AS CREATION_DATE,
			-1 AS LAST_UPDATED_BY,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			''N'' AS DEL_FLAG,
		    CODE_ATTRIBUTES,
		    '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
		    SUM(PROD_QTY) AS PROD_QTY
	FROM '||V_TEMP_TABLE||'
		WHERE '||V_PROD_CODE||' IN ('||V_IN_LV_CODE||')
		AND SPART_CODE = '''||V_GROUP_CODE||'''
		AND REGION_CODE = '''||V_REGION_CODE||'''
		AND REPOFFICE_CODE = '''||V_REPOFFICE_CODE||'''
		AND BG_CODE = '''||V_BG_CODE||'''
	GROUP BY 
		PERIOD_YEAR,
		PERIOD_ID,
		SPART_CODE,
		GROUP_LEVEL,
		PARENT_LEVEL,
		'||V_PROD_PARA||'
		MAIN_FLAG,
		SOFTWARE_MARK,
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		CODE_ATTRIBUTES
UNION ALL 	
			SELECT 
			'||V_VERSION_ID||',
			'|| F_CUSTOMIZATION_ID ||',
			'|| F_CUSTOMIZATION_ID ||',
			PERIOD_YEAR,
			PERIOD_ID,
			SPART_CODE,
			SPART_CODE AS SPART_CN_NAME,
			'''||V_GROUP_LEVEL||''' AS GROUP_LEVEL,
			'''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			'||V_PROD_PARA||'
			''N'' AS MAIN_FLAG,
			SOFTWARE_MARK,
			''PROD_SPART'' AS VIEW_FLAG,
			REGION_CODE,
		    REGION_CN_NAME,
		    REPOFFICE_CODE,
		    REPOFFICE_CN_NAME,
		    BG_CODE,
		    BG_CN_NAME,
		    OVERSEA_FLAG,
		    -1 AS CREATED_BY,
			CURRENT_TIMESTAMP AS CREATION_DATE,
			-1 AS LAST_UPDATED_BY,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			''N'' AS DEL_FLAG,
		    CODE_ATTRIBUTES,
		    '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
		    SUM(PROD_QTY) AS PROD_QTY
	FROM '||V_TEMP_TABLE||'
		WHERE '||V_PROD_CODE||' IN ('||V_IN_LV_CODE||')
		AND SPART_CODE = '''||V_GROUP_CODE||'''
		AND REGION_CODE = '''||V_REGION_CODE||'''
		AND REPOFFICE_CODE = '''||V_REPOFFICE_CODE||'''
		AND BG_CODE = '''||V_BG_CODE||'''
	GROUP BY 
		PERIOD_YEAR,
		PERIOD_ID,
		SPART_CODE,
		GROUP_LEVEL,
		PARENT_LEVEL,
		'||V_PROD_PARA||'
		MAIN_FLAG,
		SOFTWARE_MARK,
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		CODE_ATTRIBUTES
		';
		

	
	---DBMS_OUTPUT.PUT_LINE(V_SQL);		
	EXECUTE IMMEDIATE V_SQL;	

	
ELSIF  V_MAIN_FLAG = 'N' THEN 	
		V_SQL := 'INSERT INTO '||V_VIRTUAL_TABLE||' (
		  VERSION_ID,
		  CUSTOM_ID,
		  CUSTOM_CN_NAME,
		  PERIOD_YEAR,
		  PERIOD_ID,
		  GROUP_CODE,
		  GROUP_CN_NAME,
		  GROUP_LEVEL,
		  PARENT_LEVEL,
		  RMB_COST_AMT,
		  PARENT_CODE,
		  PARENT_CN_NAME,
		  MAIN_FLAG,
		  SOFTWARE_MARK,
		  VIEW_FLAG,
		  REGION_CODE,
		  REGION_CN_NAME,
		  REPOFFICE_CODE,
		  REPOFFICE_CN_NAME,
		  BG_CODE,
		  BG_CN_NAME,
		  OVERSEA_FLAG,
		  CREATED_BY,
		  CREATION_DATE,
		  LAST_UPDATED_BY,
		  LAST_UPDATE_DATE,
		  DEL_FLAG,
		  CODE_ATTRIBUTES,
		  GRANULARITY_TYPE,
		  ACTUAL_QTY
        )
		SELECT 
			'||V_VERSION_ID||',
			'|| F_CUSTOMIZATION_ID ||',
			'|| F_CUSTOMIZATION_ID ||',
			PERIOD_YEAR,
			PERIOD_ID,
			SPART_CODE,
			SPART_CODE AS SPART_CN_NAME,
			'''||V_GROUP_LEVEL||''' AS GROUP_LEVEL,
			'''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			'||V_PROD_PARA||'
			MAIN_FLAG,
			SOFTWARE_MARK,
			''PROD_SPART'' AS VIEW_FLAG,
			REGION_CODE,
		    REGION_CN_NAME,
		    REPOFFICE_CODE,
		    REPOFFICE_CN_NAME,
		    BG_CODE,
		    BG_CN_NAME,
		    OVERSEA_FLAG,
		    -1 AS CREATED_BY,
			CURRENT_TIMESTAMP AS CREATION_DATE,
			-1 AS LAST_UPDATED_BY,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			''N'' AS DEL_FLAG,
		    CODE_ATTRIBUTES,
		    '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
		    SUM(PROD_QTY) AS PROD_QTY
	FROM '||V_TEMP_TABLE||'
		WHERE '||V_PROD_CODE||' IN ('||V_IN_LV_CODE||')
		AND SPART_CODE = '''||V_GROUP_CODE||'''
		AND REGION_CODE = '''||V_REGION_CODE||'''
		AND REPOFFICE_CODE = '''||V_REPOFFICE_CODE||'''
		AND BG_CODE = '''||V_BG_CODE||'''
	GROUP BY 
		PERIOD_YEAR,
		PERIOD_ID,
		SPART_CODE,
		GROUP_LEVEL,
		PARENT_LEVEL,
		'||V_PROD_PARA||'
		MAIN_FLAG,
		SOFTWARE_MARK,
		REGION_CODE,
		REGION_CN_NAME,
		REPOFFICE_CODE,
		REPOFFICE_CN_NAME,
		BG_CODE,
		BG_CN_NAME,
		OVERSEA_FLAG,
		CODE_ATTRIBUTES
		';
		
	--DBMS_OUTPUT.PUT_LINE(V_SQL);		
	EXECUTE IMMEDIATE V_SQL;	
	
	END IF;
	
--2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '虚化实时处理完成,版本:'||V_VERSION_ID||',虚化ID：'|| F_CUSTOMIZATION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
		F_FORMULA_SQL_TXT => V_SQL,
        F_ERRBUF => 'SUCCESS'); 
	
	ELSIF V_VIEW_FLAG = 'DIMENSION' THEN 
	
		
		V_DIMENSION_PARA := 'D.DIMENSION_CODE,D.DIMENSION_CN_NAME,';
		V_DIMENSION_SUBCATEGORY_PARA := 'D.DIMENSION_SUBCATEGORY_CODE ,D.DIMENSION_SUBCATEGORY_CN_NAME,'; 	
		V_DIMENSION_SUB_DETAIL_PARA := 'D.DIMENSION_SUB_DETAIL_CODE,D.DIMENSION_SUB_DETAIL_CN_NAME,';
		
		IF V_GROUP_LEVEL = 'DIMENSION' THEN 
			 V_DIMENSION_CODE := 'DIMENSION_CODE';
			 V_DIMENSION_PARAMETER := 'DIMENSION_CODE AS GROUP_CODE,DIMENSION_CN_NAME AS GROUP_CN_NAME,';
		ELSIF V_GROUP_LEVEL = 'SUBCATEGORY' THEN 
			 V_DIMENSION_CODE := 'DIMENSION_SUBCATEGORY_CODE';
			 V_DIMENSION_PARAMETER := 'DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,';
		ELSIF V_GROUP_LEVEL = 'SUB_DETAIL' THEN 
		
			  V_DIMENSION_CODE = 'DIMENSION_SUB_DETAIL_CODE';
			  V_DIMENSION_PARAMETER := 'DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,';
		END IF;
		
	V_SQL:= 'INSERT INTO '||V_VIRTUAL_TABLE||' (
		  VERSION_ID,
		  CUSTOM_ID,
		  CUSTOM_CN_NAME,
		  PERIOD_YEAR,
		  PERIOD_ID,
		  GROUP_CODE,
		  GROUP_CN_NAME,
		  GROUP_LEVEL,
		  PARENT_LEVEL,
		  RMB_COST_AMT,
		  PARENT_CODE,
		  PARENT_CN_NAME,
		  SOFTWARE_MARK,
		  MAIN_FLAG,  
		  VIEW_FLAG,
		  REGION_CODE,
		  REGION_CN_NAME,
		  REPOFFICE_CODE,
		  REPOFFICE_CN_NAME,
		  BG_CODE,
		  BG_CN_NAME,
		  OVERSEA_FLAG,
		  CREATED_BY,
		  CREATION_DATE,
		  LAST_UPDATED_BY,
		  LAST_UPDATE_DATE,
		  DEL_FLAG,
		  CODE_ATTRIBUTES,
		  GRANULARITY_TYPE,
		  ACTUAL_QTY
        )
		SELECT 
			'||V_VERSION_ID||',
			'|| F_CUSTOMIZATION_ID ||',
			'|| F_CUSTOMIZATION_ID ||',
			PERIOD_YEAR,
			PERIOD_ID,
			'||V_DIMENSION_PARAMETER||'
			'''||V_GROUP_LEVEL||''' AS GROUP_LEVEL,
			'''||V_PARENT_LEVEL||''' AS PARENT_LEVEL,
			RMB_COST_AMT,
			'||V_PROD_PARA||'
			SOFTWARE_MARK,
			MAIN_FLAG,
			''DIMENSION'' AS VIEW_FLAG,
			REGION_CODE,
		    REGION_CN_NAME,
		    REPOFFICE_CODE,
		    REPOFFICE_CN_NAME,
		    BG_CODE,
		    BG_CN_NAME,
		    OVERSEA_FLAG,
		    -1 AS CREATED_BY,
			CURRENT_TIMESTAMP AS CREATION_DATE,
			-1 AS LAST_UPDATED_BY,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			''N'' AS DEL_FLAG,
		    CODE_ATTRIBUTES,
		    '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
		    PROD_QTY
		FROM
		(
			SELECT 
			D.PERIOD_YEAR,
			D.PERIOD_ID,
			SUM(D.RMB_COST_AMT) AS RMB_COST_AMT,
			'||V_PROD_PARA
			||V_DIMENSION_PARA 
			||V_DIMENSION_SUBCATEGORY_PARA  
			||V_DIMENSION_SUB_DETAIL_PARA||'
			D.SOFTWARE_MARK,
			D.MAIN_FLAG,
			D.REGION_CODE,
		    D.REGION_CN_NAME,
		    D.REPOFFICE_CODE,
		    D.REPOFFICE_CN_NAME,
		    D.BG_CODE,
		    D.BG_CN_NAME,
		    D.OVERSEA_FLAG,
		    D.CODE_ATTRIBUTES,
		    SUM(D.PROD_QTY) AS PROD_QTY
			FROM	
		(SELECT * FROM '||V_TEMP_TABLE||'
		WHERE '||V_PROD_CODE||' IN ('||V_IN_LV_CODE||')
		AND '||V_DIMENSION_CODE||' = '''||V_GROUP_CODE||'''
		AND REGION_CODE = '''||V_REGION_CODE||'''
		AND REPOFFICE_CODE = '''||V_REPOFFICE_CODE||'''
		AND BG_CODE = '''||V_BG_CODE||'''
		) D
		JOIN (SELECT * FROM '||V_JOIN_TABLE||' WHERE   CUSTOM_ID = '||F_CUSTOMIZATION_ID||') T1
		ON   
		D.MAIN_FLAG = T1.MAIN_FLAG
					   AND NVL(D.CODE_ATTRIBUTES, ''CA'') = NVL(T1.CODE_ATTRIBUTES, ''CA'')
					   AND D.REGION_CODE = T1.REGION_CODE
					   AND D.REPOFFICE_CODE = T1.REPOFFICE_CODE
					   AND D.BG_CODE = T1.BG_CODE
					   AND D.OVERSEA_FLAG = T1.OVERSEA_FLAG
					   AND NVL(D.SOFTWARE_MARK,1) = NVL(T1.SOFTWARE_MARK,1) 
					   AND NVL(T1.DIMENSION_CODE,''DC'') = DECODE(T1.DIMENSION_CODE,'''',''DC'',D.DIMENSION_CODE)
					   AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''',''DSC'',D.DIMENSION_SUBCATEGORY_CODE)
					   AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''',''DSDC'',D.DIMENSION_SUB_DETAIL_CODE)
					   AND D.VIEW_FLAG = T1.VIEW_FLAG
		GROUP BY 
			D.PERIOD_YEAR,
			D.PERIOD_ID,
			'||V_PROD_PARA
			||V_DIMENSION_PARA 
			||V_DIMENSION_SUBCATEGORY_PARA  
			||V_DIMENSION_SUB_DETAIL_PARA||'
			D.MAIN_FLAG,
			D.SOFTWARE_MARK,
			D.REGION_CODE,
		    D.REGION_CN_NAME,
		    D.REPOFFICE_CODE,
		    D.REPOFFICE_CN_NAME,
		    D.BG_CODE,
		    D.BG_CN_NAME,
		    D.OVERSEA_FLAG,
		    D.CODE_ATTRIBUTES
		)';
		

	--DBMS_OUTPUT.PUT_LINE(V_SQL);		
	EXECUTE IMMEDIATE V_SQL;	

--2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '虚化实时处理完成,版本:'||V_VERSION_ID||',虚化ID：'|| F_CUSTOMIZATION_ID||',成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG,
        F_RESULT_STATUS => X_RESULT_STATUS,
		F_FORMULA_SQL_TXT => V_SQL,
        F_ERRBUF => 'SUCCESS'); 
	
	END IF;
	
	

NULL;

ELSE NULL;

END IF;
	
	
	
  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG);

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,成本类型:'||F_COST_TYPE||',PBI维度:'||F_GRANULARITY_TYPE||',路径：'||F_VIEW_FLAG, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

