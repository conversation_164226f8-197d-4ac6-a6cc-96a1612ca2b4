-- Name: f_dm_fom_month_mid_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_mid_cost_idx_t(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间： 2023-12-11
创建人  ： 黄心蕊 HWX1187045
背景描述： 月度分析-ITEM层级指数计算
创建时间： 2024-02-02
创建人  ： 黄心蕊 HWX1187045
修改内容： 202403版本新增是否补齐判断
参数描述： 参数一(F_KEYSTR)：绝密数据解密密钥串
		   参数二(F_VERSION_ID)：运行版本号
		   参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T ITEM均价表
目标表：FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T 指数中间表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_MID_COST_IDX_T('M','密钥串',''); --自制一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_MID_COST_IDX_T('E','',''); --EMS一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_MONTH_MID_COST_IDX_T';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_KEYSTR       VARCHAR(100) := F_KEYSTR; --解密密钥串
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;	
  V_BASE_PERIOD_ID INT4 := TO_NUMBER(YEAR(CURRENT_DATE)-1||'01');

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T ;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '数据删除成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  V_STEP_NUM := V_STEP_NUM + 1;
--1.建临时表，承载规格品金额
DROP TABLE IF EXISTS DM_DECRYP_TOP_AMT_TEMP;
CREATE TEMPORARY TABLE DM_DECRYP_TOP_AMT_TEMP(
	PERIOD_YEAR CHARACTER VARYING(50),
	PERIOD_ID CHARACTER VARYING(50),
	LV0_CODE CHARACTER VARYING(50),
	LV0_CN_NAME CHARACTER VARYING(200),
	LV1_CODE CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CODE CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
	SHIPPING_OBJECT_CODE CHARACTER VARYING(200),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CODE CHARACTER VARYING(200),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(200),
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_CN_NAME CHARACTER VARYING(1000),
	RMB_COST_AVG  NUMERIC,
	CALIBER_FLAG CHARACTER VARYING(2) ,
	TOP_FLAG CHARACTER VARYING(2),
	APPEND_FLAG   CHARACTER VARYING(2)
	)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  IF V_CALIBER_FLAG = 'E' THEN
    --2.1 根据业务口径入参为'E'，将EMS金额落表，无需解密
    V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_TOP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AVG,
	   TOP_FLAG,
	   APPEND_FLAG)		--202403版本新增
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
             RMB_EMS_AVG,
			 TOP_FLAG,
			 APPEND_FLAG			--202403版本新增
        FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T
       WHERE CALIBER_FLAG = V_CALIBER_FLAG ;
  
    --写入日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME       => V_SP_NAME,
     F_STEP_NUM      => V_STEP_NUM,
     F_CAL_LOG_DESC  => 'EMS金额落表',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_SUCCESS_FLAG,
     F_ERRBUF        => 'SUCCESS');
  
  ELSIF V_CALIBER_FLAG = 'M' THEN
    --2.1 根据业务口径入参为'M'，将自制金额落表，需解密
    V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_TOP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AVG,
	   TOP_FLAG,
	   APPEND_FLAG)   --202403版本新增
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
             TO_NUMBER(GS_DECRYPT(RMB_MADE_AVG,
                                  V_KEYSTR,
                                  'aes128',
                                  'cbc',
                                  'sha256')) AS RMB_COST_AVG,
			TOP_FLAG,
			APPEND_FLAG			--202403版本新增
        FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T
       WHERE CALIBER_FLAG = V_CALIBER_FLAG ;
  
    --写入日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME       => V_SP_NAME,
     F_STEP_NUM      => V_STEP_NUM,
     F_CAL_LOG_DESC  => '自制金额落表',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_SUCCESS_FLAG,
     F_ERRBUF        => 'SUCCESS');
  
  END IF;
  
	--2.2 计算ITEM指数
	V_STEP_NUM := V_STEP_NUM + 1;	
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MID_COST_IDX_T
    (VERSION_ID,						
     BASE_PERIOD_ID,
     PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_IDX,
     PARENT_CODE,
     PARENT_CN_NAME,
     CALIBER_FLAG,
     TOP_FLAG,
	 APPEND_FLAG,		--202403版本新增
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT V_VERSION AS VERSION_ID,
           V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
           T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           T1.SHIPPING_OBJECT_CODE,
           T1.SHIPPING_OBJECT_CN_NAME,
           T1.MANUFACTURE_OBJECT_CODE,
           T1.MANUFACTURE_OBJECT_CN_NAME,
           T1.ITEM_CODE AS GROUP_CODE,
           T1.ITEM_CN_NAME AS GROUP_CN_NAME,
           'ITEM' AS GROUP_LEVEL,
           T1.RMB_COST_AVG / NULLIF(T2.RMB_COST_AVG, 0) * 100 AS COST_IDX,
           T1.MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
           T1.MANUFACTURE_OBJECT_CN_NAME AS PARENT_CN_NAME,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           T1.TOP_FLAG,
		   T1.APPEND_FLAG,    --202403版本新增
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_DECRYP_TOP_AMT_TEMP T1
      LEFT JOIN (SELECT PERIOD_ID,
                        LV0_CODE,
                        LV1_CODE,
                        BUSSINESS_OBJECT_CODE,
                        SHIPPING_OBJECT_CODE,
                        MANUFACTURE_OBJECT_CODE,
                        ITEM_CODE,
                        RMB_COST_AVG,
                        TOP_FLAG
                   FROM DM_DECRYP_TOP_AMT_TEMP
                  WHERE PERIOD_ID = V_BASE_PERIOD_ID) T2
        ON T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
       AND T1.SHIPPING_OBJECT_CODE = T2.SHIPPING_OBJECT_CODE
       AND T1.MANUFACTURE_OBJECT_CODE = T2.MANUFACTURE_OBJECT_CODE
       AND T1.ITEM_CODE = T2.ITEM_CODE
       AND T1.TOP_FLAG = T2.TOP_FLAG
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') <> 'SNULL'
	 
UNION ALL
--海思与云核心网下ITEM单独处理
    SELECT V_VERSION AS VERSION_ID,
           V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
           T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           '',
           '',
           '',
           '',
           T1.ITEM_CODE AS GROUP_CODE,
           T1.ITEM_CN_NAME AS GROUP_CN_NAME,
           'ITEM' AS GROUP_LEVEL,
           T1.RMB_COST_AVG / NULLIF(T2.RMB_COST_AVG, 0) * 100 AS COST_IDX,
           T1.BUSSINESS_OBJECT_CODE AS PARENT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           T1.TOP_FLAG,
		   T1.APPEND_FLAG,		--202403版本新增
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_DECRYP_TOP_AMT_TEMP T1
      LEFT JOIN (SELECT PERIOD_ID,
                        LV0_CODE,
                        LV1_CODE,
                        BUSSINESS_OBJECT_CODE,
                        ITEM_CODE,
                        RMB_COST_AVG,
                        TOP_FLAG
                   FROM DM_DECRYP_TOP_AMT_TEMP
                  WHERE PERIOD_ID = V_BASE_PERIOD_ID
				    AND NVL(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL') T2
        ON T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.BUSSINESS_OBJECT_CODE = T2.BUSSINESS_OBJECT_CODE
       AND T1.ITEM_CODE = T2.ITEM_CODE
       AND T1.TOP_FLAG = T2.TOP_FLAG
	 WHERE NVL(T1.MANUFACTURE_OBJECT_CODE||T1.SHIPPING_OBJECT_CODE,'SNULL') = 'SNULL';
		 
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => 'ITEM指数落表成功',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	
	
 RETURN 'SUCCESS';


EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

