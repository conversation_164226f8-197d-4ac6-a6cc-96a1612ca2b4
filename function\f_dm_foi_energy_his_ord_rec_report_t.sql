-- Name: f_dm_foi_energy_his_ord_rec_report_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_his_ord_rec_report_t(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*

创建人  ：罗若文
背景描述：1. 数字能源实际数历史数表关联维表
专家团维表	 DM_DIM_CEG_D
库存组织维表 DWR_DIM_INVENTORY_ORG_D
物料维表	 DWR_DIM_MATERIAL_CODE_D
供应商维表	 DWR_DIM_SUPPLIER_D


参数描述：x_result_status ：是否成功
事例：SELECT FIN_DM_OPT_FOI.f_dm_foi_energy_his_ord_rec_report_t()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T'; --存储过程名称
  --V_PERIOD_YEAR  TIMESTAMP := YEAR(CURRENT_TIMESTAMP)-3;
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_ITEM_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(100) ; --新的版本中文名称
  V_ITEM_VERSION_NAME VARCHAR2(100) ; --新的版本中文名称
  V_PARENT_VERSION BIGINT;
  V_DIM_VERSION_ID BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识//
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --清空目标表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空 FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
   -- 查询品类该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-TOP品类-Auto'
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND DATA_TYPE = 'category';
		
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-TOP品类-Auto'
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND DATA_TYPE = 'category';
		
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
	V_VERSION_NAME := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')||'-TOP品类-Auto';
	
  --往版本信息表记录本次CATEGORY版本号, 版本号为V_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_VERSION_ID,V_PARENT_VERSION,V_VERSION_NAME,1,'auto','category',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');
   END IF;
   
   
   
   
   
   

 
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录CATEGORY版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 
    -- 查询ITEM该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND DATA_TYPE = 'item';
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG = 0 THEN 
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_S')
    INTO V_ITEM_VERSION_ID
    FROM DUAL;
     
	V_ITEM_VERSION_NAME := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')||'-ITEM-Auto';
	V_PARENT_VERSION := V_VERSION_ID;
  --往版本信息表记录本次TOP品类版本号, 版本号为V_VERSION_ID, 依赖的品类专家维V_DIM_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_ITEM_VERSION_ID,V_PARENT_VERSION,V_ITEM_VERSION_NAME,1,'auto','item',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');
   END IF;
   
 
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录ITEM版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 
  --查询品类专家团映射关系的最新版本号; 
 
 SELECT
        VERSION_ID INTO V_DIM_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        CREATION_DATE =
         (SELECT MAX(CREATION_DATE)
            FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T
           WHERE UPPER(DATA_TYPE) = 'DIMENSION'
             AND STATUS =1
        AND UPPER( DEL_FLAG ) = 'N');
 
  --往目标表里插数
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T
    (
		 VERSION_ID,
		 YEAR,
		 PERIOD_ID,
		 ITEM_ID,
		 ITEM_CODE,
		 ITEM_NAME,
		 RECEIVE_QTY,
		 RECEIVE_AMT_USD,
		 RECEIVE_AMT_CNY,
		 CATEGORY_CODE,
		 CATEGORY_NAME,
		 CATEGORY_EN_NAME,
		 SUPPLIER_CODE,
		 SUPPLIER_CN_NAME,
		 L4_CEG_CODE,
		 L4_CEG_SHORT_CN_NAME,
		 L4_CEG_CN_NAME,
		 L3_CEG_CODE,
		 L3_CEG_SHORT_CN_NAME,
		 L3_CEG_CN_NAME,
		 L2_CEG_CODE,
		 L2_CEG_CN_NAME,
		 CREATED_BY,
		 CREATION_DATE,
		 LAST_UPDATED_BY,
		 LAST_UPDATE_DATE,
		 DEL_FLAG,
		 GROUP_PUR_FLAG

     )
	WITH BASE_DATA_TEMP AS (

		SELECT 
			S.YEAR ,
			S.PERIOD_ID ,
			S.ITEM_ID ,
			S.ITEM_CODE ,
			S.ITEM_DESCRIPTION ,
			S.CURRENCY_CODE ,
			S.RECEIVE_QTY ,
			S.RECEIVE_AMT ,
			S.RECEIVE_AMT_USD ,
			S.RECEIVE_AMT_CNY ,
			M.ITEM_SUBTYPE_CODE ,
			S.ITEM_SUBTYPE_NAME ,
			S.ITEM_SUBTYPE_EN_NAME ,
			S.SUPPLIER_KEY ,
			S.SUPPLIER_CODE ,
			S.COUNTRY_ID ,
			S.COUNTRY_CODE ,
			S.PROJECT_KEY ,
			S.PROJECT_CODE ,
			S.EMS_STRATEGY ,
			S.PO_PURCHASE_TARGET_TYPE ,
			S.INTERCOMPANY_TRANSACTION_FLAG ,
			S.PROCUREMENT_PP_FLAG ,
			S.PO_ID ,
			S.PO_NUM ,
			S.PO_SHIPMENT_ID ,
			S.COMPANY_KEY ,
			S.CEG_KEY ,
			S.TO_INVENTORY_ORG_KEY ,
			S.L2_CEG_SHORT_CN_NAME ,
			S.L3_CEG_SHORT_CN_NAME ,
			S.L4_CEG_SHORT_CN_NAME ,
			S.L3_CODE ,
			S.L3_CN_NAME ,
			S.TOP_CATEGORY ,
			S.ITEM_SUBTYPE_CATEGORY ,
			S.ITEM_SUBTYPE_CHARACTER ,
			S.DEL_FLAG ,
			S.CRT_CYCLE_ID ,
			S.LAST_UPD_CYCLE_ID ,
			S.PRODUCT_CODE ,
			S.PRODUCT_LINE_CODE
			FROM FIN_DM_OPT_FOI.FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I S
			LEFT JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D M
			ON S.ITEM_CODE = M.ITEM_CODE
			LEFT JOIN DWRDIM.DWR_DIM_SUPPLIER_D SUP
			ON    S.SUPPLIER_KEY = SUP.SUPPLIER_KEY
			WHERE S.ITEM_CODE IS NOT NULL
			AND S.YEAR >= YEAR(CURRENT_TIMESTAMP)-3
			AND M.ITEM_SUBTYPE_CODE IS NOT NULL 
			AND     NOT EXISTS (SELECT S.ITEM_CODE 
                  FROM(SELECT S.ITEM_CODE 
                       FROM FIN_DM_OPT_FOI.FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I S
                       WHERE SUBSTR(S.ITEM_CODE,1,2)='88' 
                       OR    SUBSTR(S.ITEM_CODE,1,2)='99')S1   --剔除以88，99开头的item_code
                  WHERE  S.ITEM_CODE=S1.ITEM_CODE)      
AND   NOT EXISTS (SELECT S.ITEM_CODE
                  FROM(SELECT S.ITEM_CODE 
                       FROM FIN_DM_OPT_FOI.FOI_DWL_PRO_PO_SHIPMENT_RECEIPT_I S
                       WHERE SUBSTR(S.ITEM_CODE,1,1)='W' 
                       OR    SUBSTR(S.ITEM_CODE,1,1)='Z')S1	   --剔除以W，Z开头的item_code
                       WHERE  S.ITEM_CODE=S1.ITEM_CODE)
			
			
	)	,
	
	UNION_DATA_TEMP AS (
	    
		SELECT T.* FROM 
		(
		SELECT T.* 
			FROM BASE_DATA_TEMP T
			LEFT JOIN DWRDIM.DWR_DIM_COMPANY_D D
			ON T.COMPANY_KEY  = D.COMPANY_KEY
			WHERE D.COMPANY_CODE = '4761'   /*数字能源直采：通过COMPANY_KEY 字段带出DWR_DIM_COMPANY_D维表中的COMPANY_CODE，筛选COMPANY_CODE=4761，即为数字能源直采。*/
			UNION 
		SELECT T.*  
			FROM BASE_DATA_TEMP T 
			LEFT JOIN DWRDIM.DWR_DIM_INVENTORY_ORG_D D
			ON T.TO_INVENTORY_ORG_KEY  = D.INVENTORY_ORG_KEY
			WHERE D.INVENTORY_ORG_CODE = 'H89' /*2、	华计代采：通过TO_INVENTORY_ORG_KEY字段带出DWR_DIM_INVENTORY_ORG_D表中的INVENTORY_ORG_CODE，筛选为H89的，即为华技代采。*/
			UNION 
		SELECT T.*  
			FROM BASE_DATA_TEMP T
			LEFT JOIN DWRDIM.DWR_DIM_CEG_D D
			ON T.CEG_KEY  = D.CEG_KEY
			WHERE D.L2_CEG_CODE = '50047'  /*4、	数字能源采购认证部：通过CEG_KEY字段带出维表DWR_DIM_CEG_D中的L2_CEG_CN_NAME=数字能源采购认证部。*/
			UNION 
		SELECT T.*  
			FROM BASE_DATA_TEMP T	
			WHERE  LEFT(PO_NUM,6) = 'HK4761'
			OR LEFT(PO_NUM,4) = 'HKNY'  /*3、	香港华为代采：通过限定PO_NUM字段前六位为HK4761或HKNY，为香港华为代采。*/
			) T
			LEFT JOIN DWRDIM.DWR_DIM_CEG_D D
			ON T.CEG_KEY  = D.CEG_KEY
			WHERE D.L3_CEG_CODE IN ('12251','12252','12253','12254','12256','12257','12274','16349','19382','16449','14029','19462','19484',
										'50050','50051','50053','50873','50874','15809','12321','13289')
			
		),
		 OPT_DIM_SUPPLIER_TMP AS (
      SELECT * FROM(
      SELECT SUPPLIER_KEY,SUPPLIER_CODE,SUPPLIER_NAME,
			ROW_NUMBER() OVER(PARTITION BY SUPPLIER_CODE ORDER BY SCD_ACTIVE_BEGIN_DATE DESC) RN
		  FROM DWRDIM.DWR_DIM_SUPPLIER_D
		  WHERE SCD_ACTIVE_IND = 1
      ) WHERE RN = 1
)	
			
			SELECT  
			V_VERSION_ID,
			TT.YEAR AS  YEAR,
			TT.PERIOD_ID,
			M.ITEM_ID,
			M.ITEM_CODE,
			M.ITEM_NAME,
			SUM(TT.RECEIVE_QTY) AS RECEIVE_QTY,
			SUM(TT.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
			SUM(TT.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
			CEG.CATEGORY_CODE,
			CEG.CATEGORY_NAME,
			NULL AS CATEGORY_EN_NAME,
			S.SUPPLIER_CODE,
			S.SUPPLIER_NAME AS SUPPLIER_CN_NAME,
			CEG.L4_CEG_CODE,
			CEG.L4_CEG_SHORT_CN_NAME,
			CEG.L4_CEG_CN_NAME,
			CEG.L3_CEG_CODE,
			CEG.L3_CEG_SHORT_CN_NAME,
			CEG.L3_CEG_CN_NAME,
			'50047' AS L2_CEG_CODE,
			'数字能源采购认证部' AS L2_CEG_CN_NAME,
			'-1' AS CREATED_BY,
			CURRENT_TIMESTAMP AS CREATION_DATE,
			'-1' AS  LAST_UPDATED_BY,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			TT.DEL_FLAG,
			'Y' AS GROUP_PUR_FLAG
			FROM UNION_DATA_TEMP TT   
			LEFT JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D M
			ON TT.ITEM_CODE = M.ITEM_CODE
			--AND M.ITEM_SUBTYPE_CODE IS NOT NULL 
			--AND LENGTH(M.ITEM_SUBTYPE_CODE) >= 4 
			--对于所取得“业财联接_采购业务采购PO采购验收_PO发运行粒度”，进行ITEM与品类关系得重新刷新，将所有ITEM都以最新的物料维表DWR_DIM_MATERIAL_CODE_D 关联取出品类信息。“物料小类编码” 字段剔除空值（4位+）
			
			LEFT JOIN OPT_DIM_SUPPLIER_TMP S
			ON TT.SUPPLIER_CODE = S.SUPPLIER_CODE   --通过SUPPLIER KEY关联DWR_DIM_SUPPLIER_D带出供应商信息
			INNER JOIN FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T CEG
			ON TT.ITEM_SUBTYPE_CODE = CEG.CATEGORY_CODE  --通过“品类编码”与数字能源品类-模块-专家团映射表带出模块和专家团的信息（内关联）
			AND TT.ITEM_CODE = CEG.ITEM_CODE
			GROUP BY 
			TT.YEAR ,
			TT.PERIOD_ID,
			M.ITEM_ID,
			M.ITEM_CODE,
			M.ITEM_NAME,
			CEG.CATEGORY_CODE,
			CEG.CATEGORY_NAME,
			S.SUPPLIER_CODE,
			S.SUPPLIER_NAME ,
			CEG.L4_CEG_CODE,
			CEG.L4_CEG_SHORT_CN_NAME,
			CEG.L4_CEG_CN_NAME,
			CEG.L3_CEG_CODE,
			CEG.L3_CEG_SHORT_CN_NAME,
			CEG.L3_CEG_CN_NAME,
			CEG.L2_CEG_CODE,
			CEG.L2_CEG_CN_NAME,
			TT.DEL_FLAG
			HAVING 
			SUM(TT.RECEIVE_QTY) >= 10
			AND SUM(TT.RECEIVE_AMT_CNY) > 1 
			AND TT.DEL_FLAG = 'N'   --“到货金额（10K CNY）”按月卷积后剔除小于1的数据“,到货数量”按月卷积剔除小于10的数据
			AND L3_CEG_CODE IN ('12251','12252','12253','12254','12256','12257','12274','16349','19382','16449','14029','19462','19484')
			
			UNION ALL 
			
			SELECT  
			V_VERSION_ID,
			TT.YEAR AS  YEAR,
			TT.PERIOD_ID,
			M.ITEM_ID,
			M.ITEM_CODE,
			M.ITEM_NAME,
			SUM(TT.RECEIVE_QTY) AS RECEIVE_QTY,
			SUM(TT.RECEIVE_AMT_USD) AS RECEIVE_AMT_USD,
			SUM(TT.RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
			CEG.CATEGORY_CODE,
			CEG.CATEGORY_NAME,
			NULL AS CATEGORY_EN_NAME,
			S.SUPPLIER_CODE,
			S.SUPPLIER_NAME AS SUPPLIER_CN_NAME,
			CEG.L4_CEG_CODE,
			CEG.L4_CEG_SHORT_CN_NAME,
			CEG.L4_CEG_CN_NAME,
			CEG.L3_CEG_CODE,
			CEG.L3_CEG_SHORT_CN_NAME,
			CEG.L3_CEG_CN_NAME,
			CEG.L2_CEG_CODE,
			CEG.L2_CEG_CN_NAME,
			'-1' AS CREATED_BY,
			CURRENT_TIMESTAMP AS CREATION_DATE,
			'-1' AS  LAST_UPDATED_BY,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			TT.DEL_FLAG,
			'N' AS GROUP_PUR_FLAG
			FROM UNION_DATA_TEMP TT   
			LEFT JOIN DWRDIM.DWR_DIM_MATERIAL_CODE_D M
			ON TT.ITEM_CODE = M.ITEM_CODE
			--AND M.ITEM_SUBTYPE_CODE IS NOT NULL 
			--AND LENGTH(M.ITEM_SUBTYPE_CODE) >= 4
			--AND TT.ITEM_CODE IS NOT NULL  
			--对于所取得“业财联接_采购业务采购PO采购验收_PO发运行粒度”，进行ITEM与品类关系得重新刷新，将所有ITEM都以最新的物料维表DWR_DIM_MATERIAL_CODE_D 关联取出品类信息。“物料小类编码” 字段剔除空值（4位+）
			LEFT JOIN OPT_DIM_SUPPLIER_TMP S
			ON TT.SUPPLIER_CODE = S.SUPPLIER_CODE   --通过SUPPLIER KEY关联DWR_DIM_SUPPLIER_D带出供应商信息
			INNER JOIN FIN_DM_OPT_FOI.DM_DIM_ITEM_CATG_MODL_CEG_ENERGY_T CEG
			ON TT.ITEM_SUBTYPE_CODE = CEG.CATEGORY_CODE  --通过“品类编码”与数字能源品类-模块-专家团映射表带出模块和专家团的信息（内关联）
			AND TT.ITEM_CODE = CEG.ITEM_CODE
			GROUP BY 
			TT.YEAR ,
			TT.PERIOD_ID,
			M.ITEM_ID,
			M.ITEM_CODE,
			M.ITEM_NAME,
			CEG.CATEGORY_CODE,
			CEG.CATEGORY_NAME,
			S.SUPPLIER_CODE,
			S.SUPPLIER_NAME ,
			CEG.L4_CEG_CODE,
			CEG.L4_CEG_SHORT_CN_NAME,
			CEG.L4_CEG_CN_NAME,
			CEG.L3_CEG_CODE,
			CEG.L3_CEG_SHORT_CN_NAME,
			CEG.L3_CEG_CN_NAME,
			CEG.L2_CEG_CODE,
			CEG.L2_CEG_CN_NAME,
			TT.DEL_FLAG
			HAVING 
			SUM(TT.RECEIVE_QTY) >= 10
			AND SUM(TT.RECEIVE_AMT_CNY) > 1 
			AND TT.DEL_FLAG = 'N'   --“到货金额（10K CNY）”按月卷积后剔除小于1的数据“,到货数量”按月卷积剔除小于10的数据
			AND L3_CEG_CODE IN ('50050','50051','50053','50873','50874','15809','12321','13289')
			;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T表, 新版本号='||V_VERSION_ID||', 所依赖的品类-专家团的版本号='||V_DIM_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T表统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

