-- Name: f_dm_fcst_ict_sum_detail_spart_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_sum_detail_spart_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text DEFAULT NULL::text, f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间: 2024年7月5日
创建人  : 黄心蕊 hwx1187045
背景描述: 月累计均本表数据计算
参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二: F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录
		参数三: F_KEYSTR	密钥
		参数四: F_VERSION_ID 版本号
		参数五: X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败
--------------------来源表
--金额表
重量级团队目录： 	DM_FCST_ICT_PSP_IRB_MON_YTD_AVG_T	--PSP IRB
产业目录： 			DM_FCST_ICT_PSP_INDUS_MON_YTD_AVG_T	--PSP INDUS
销售目录： 			DM_FCST_ICT_PSP_PROD_MON_YTD_AVG_T	--PSP PROD
重量级团队目录：	DM_FCST_ICT_STD_IRB_MON_YTD_AVG_T	--STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_MON_YTD_AVG_T	--STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_MON_YTD_AVG_T	--STD PROD

--维表
重量级团队目录：	DM_FCST_ICT_PSP_IRB_TOP_SPART_INFO_T	--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_TOP_SPART_INFO_T  --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_TOP_SPART_INFO_T   --PSP PROD
重量级团队目录：	DM_FCST_ICT_STD_IRB_TOP_SPART_INFO_T    --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_TOP_SPART_INFO_T  --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_TOP_SPART_INFO_T   --STD PROD
--------------------目标表
重量级团队目录：	DM_FCST_ICT_PSP_IRB_SUM_DETAIL_SPART_T	--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_SUM_DETAIL_SPART_T  --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_SUM_DETAIL_SPART_T  --PSP PROD
重量级团队目录：	DM_FCST_ICT_STD_IRB_SUM_DETAIL_SPART_T    --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_SUM_DETAIL_SPART_T  --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_SUM_DETAIL_SPART_T  --STD PROD

--------------------编码替代页面用的目标表
重量级团队目录：	DM_FCST_ICT_PSP_IRB_REPL_SUM_DETAIL_SPART_T	--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_REPL_SUM_DETAIL_SPART_T  --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_REPL_SUM_DETAIL_SPART_T  --PSP PROD
重量级团队目录：	DM_FCST_ICT_STD_IRB_REPL_SUM_DETAIL_SPART_T    --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_REPL_SUM_DETAIL_SPART_T  --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_REPL_SUM_DETAIL_SPART_T  --STD PROD


事例    ：	SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T('PSP','IRB','',''); 		--PSP成本 重量级团队目录一个版本数据
			SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T('PSP','INDUS','',''); 	--PSP成本 产业目录一个版本数据
			SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T('PSP','PROD','',''); 	--PSP成本 销售目录一个版本数据
			SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T('STD''IRB','密钥',''); 	--标准成本 重量级团队目录一个版本数据
			SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T('STD''INDUS','密钥',''); --标准成本 产业目录一个版本数据
			SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T('STD''PROD','密钥','');  --标准成本 销售目录一个版本数据

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME             VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_SUM_DETAIL_SPART_T';
  V_VERSION             BIGINT; --版本号
  V_FROM_AMT_TABLE      VARCHAR(100);
  V_FROM_REPL_AMT_TABLE VARCHAR(100);  -- 编码替代页面中用到的来源表
  V_FROM_DIM_TABLE      VARCHAR(100);
  V_TO_TABLE            VARCHAR(100);
  V_TO_REPL_TABLE       VARCHAR(200);  -- 编码替代页面中用到的月累积目标表
  V_FROM_TEMP_TABLE     VARCHAR(100);
  V_BEGIN_DATE          TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP) - 2) || '01',
                                             'YYYYMM');
  V_PBI_PART            TEXT; --PBI层级字段
  V_SQL_PBI_PART        TEXT; --PBI层级查询字段
  V_JOIN_PB1_PART       TEXT; --PBI层级关联条件
  V_COST_AMT            TEXT; --不同成本类型下,金额字段的数据类型不同
  V_RMB_AVG_AMT         TEXT; --STD金额落表加密处理,PSP不加密
  V_RMB_COST_AMT        TEXT; --STD金额落表加密处理,PSP不加密
  V_SQL                 TEXT; --执行逻辑
  V_EXCEPTION_FLAG      INT; --异常步骤
  V_DIMENSION_PART      TEXT;
  V_SQL_DIMENSION_PART  TEXT;
  V_JOIN_DIMENSION_CODE TEXT;
  V_SQL_CONDITION       TEXT;


BEGIN
X_RESULT_STATUS := '1';

--写入日志
V_EXCEPTION_FLAG	:= 0;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--1.变量定义
--1.1 表名定义
V_FROM_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_YTD_AVG_T';
V_FROM_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_TOP_SPART_INFO_T';
V_TO_TABLE		 := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_SUM_DETAIL_SPART_T';
V_FROM_REPL_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_REPL_MON_YTD_AVG_T';  -- 编码替代页面用到来源表
V_TO_REPL_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_REPL_SUM_DETAIL_SPART_T';  -- 编码替代页面用到月累计目标表

V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';

V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';

--1.2 PBI层级定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN

    V_PBI_PART := '
      LV0_PROD_RND_TEAM_CODE,
      LV1_PROD_RND_TEAM_CODE,
      LV2_PROD_RND_TEAM_CODE,
      LV3_PROD_RND_TEAM_CODE,
      LV4_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RD_TEAM_CN_NAME,
      LV3_PROD_RD_TEAM_CN_NAME,
      LV4_PROD_RD_TEAM_CN_NAME,
      ';

    V_SQL_PBI_PART  := '
      T1.LV0_PROD_RND_TEAM_CODE,
      T1.LV1_PROD_RND_TEAM_CODE,
      T1.LV2_PROD_RND_TEAM_CODE,
      T1.LV3_PROD_RND_TEAM_CODE,
      T1.LV4_PROD_RND_TEAM_CODE,
      T1.LV0_PROD_RD_TEAM_CN_NAME,
      T1.LV1_PROD_RD_TEAM_CN_NAME,
      T1.LV2_PROD_RD_TEAM_CN_NAME,
      T1.LV3_PROD_RD_TEAM_CN_NAME,
      T1.LV4_PROD_RD_TEAM_CN_NAME,
      ';

    V_JOIN_PB1_PART := '
      AND T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE
      AND T1.LV1_PROD_RND_TEAM_CODE = T2.LV1_PROD_RND_TEAM_CODE
      AND T1.LV2_PROD_RND_TEAM_CODE = T2.LV2_PROD_RND_TEAM_CODE
      AND T1.LV3_PROD_RND_TEAM_CODE = T2.LV3_PROD_RND_TEAM_CODE
      AND T1.LV4_PROD_RND_TEAM_CODE = T2.LV4_PROD_RND_TEAM_CODE
      ';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_PBI_PART := '
      LV0_INDUSTRY_CATG_CODE    ,
      LV1_INDUSTRY_CATG_CODE      ,
      LV2_INDUSTRY_CATG_CODE      ,
      LV3_INDUSTRY_CATG_CODE      ,
      LV4_INDUSTRY_CATG_CODE      ,
      LV0_INDUSTRY_CATG_CN_NAME,
      LV1_INDUSTRY_CATG_CN_NAME,
      LV2_INDUSTRY_CATG_CN_NAME,
      LV3_INDUSTRY_CATG_CN_NAME,
      LV4_INDUSTRY_CATG_CN_NAME,
      ';

    V_SQL_PBI_PART := '
      T1.LV0_INDUSTRY_CATG_CODE,
      T1.LV1_INDUSTRY_CATG_CODE,
      T1.LV2_INDUSTRY_CATG_CODE,
      T1.LV3_INDUSTRY_CATG_CODE,
      T1.LV4_INDUSTRY_CATG_CODE,
      T1.LV0_INDUSTRY_CATG_CN_NAME,
      T1.LV1_INDUSTRY_CATG_CN_NAME,
      T1.LV2_INDUSTRY_CATG_CN_NAME,
      T1.LV3_INDUSTRY_CATG_CN_NAME,
      T1.LV4_INDUSTRY_CATG_CN_NAME,
      ';

    V_JOIN_PB1_PART := '
      AND T1.LV0_INDUSTRY_CATG_CODE = T2.LV0_INDUSTRY_CATG_CODE
      AND T1.LV1_INDUSTRY_CATG_CODE = T2.LV1_INDUSTRY_CATG_CODE
      AND T1.LV2_INDUSTRY_CATG_CODE = T2.LV2_INDUSTRY_CATG_CODE
      AND T1.LV3_INDUSTRY_CATG_CODE = T2.LV3_INDUSTRY_CATG_CODE
      AND T1.LV4_INDUSTRY_CATG_CODE = T2.LV4_INDUSTRY_CATG_CODE
      ';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_PBI_PART      := '
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      ';
    V_SQL_PBI_PART  := '
      T1.LV0_PROD_LIST_CODE,
      T1.LV1_PROD_LIST_CODE,
      T1.LV2_PROD_LIST_CODE,
      T1.LV3_PROD_LIST_CODE,
      T1.LV4_PROD_LIST_CODE,
      T1.LV0_PROD_LIST_CN_NAME,
      T1.LV1_PROD_LIST_CN_NAME,
      T1.LV2_PROD_LIST_CN_NAME,
      T1.LV3_PROD_LIST_CN_NAME,
      T1.LV4_PROD_LIST_CN_NAME,
      ';
    V_JOIN_PB1_PART := '
      AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
      AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
      ';

  END IF;

----1.3 不同成本类型差别定义
  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_AVG_AMT     := 'RMB_AVG_AMT,';
    V_RMB_COST_AMT    := 'RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_AVG_AMT     := 'STD_RMB_AVG_AMT,';
    V_RMB_COST_AMT    := 'STD_RMB_COST_AMT,';
  END IF;

  V_EXCEPTION_FLAG	:= 1;
--2.落表版本号定义赋值
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，目录树：'||F_GRANULARITY_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');


--3.基础数据关联处理
--3.1 TOP金额临时表创建
V_EXCEPTION_FLAG	:= 2;
DROP TABLE IF EXISTS DM_TOP_AMT_TEMP;
CREATE TEMPORARY TABLE DM_TOP_AMT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200),
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) ,
 STD_RMB_AVG_AMT			VARCHAR(2000) ,
 RMB_AVG_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 APPEND_FLAG		VARCHAR(5)  ,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) ,
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) ,
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) ,
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20) ,    --是否主力编码
 SOFTWARE_MARK		VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--3.2 金额表与TOP表关联筛选出TOP金额
V_SQL:='
  INSERT INTO DM_TOP_AMT_TEMP
    (PERIOD_ID,
     PERIOD_YEAR,
     LV0_CODE,
     LV1_CODE,
     LV2_CODE,
     LV3_CODE,
     LV4_CODE,
     LV0_CN_NAME,
     LV1_CN_NAME,
     LV2_CN_NAME,
     LV3_CN_NAME,
     LV4_CN_NAME,
	 '||V_DIMENSION_PART||'
	 SPART_CODE,
	 SPART_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 APPEND_FLAG,
     '||V_RMB_AVG_AMT||'
	 ACTUAL_QTY,
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 VIEW_FLAG,
	 SOFTWARE_MARK)
    SELECT T1.PERIOD_ID,
           T1.PERIOD_YEAR,
           '||V_SQL_PBI_PART||'
		   '||V_SQL_DIMENSION_PART||'
		   T1.SPART_CODE,
		   T1.SPART_CN_NAME,
		   T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
		   T1.APPEND_FLAG,
           T1.RMB_AVG_AMT,
           T1.ACTUAL_QTY,
		   T2.MAIN_FLAG,
           T2.CODE_ATTRIBUTES,
		   T1.VIEW_FLAG,
		   T1.SOFTWARE_MARK
      FROM '||V_FROM_AMT_TABLE||' T1
      LEFT JOIN '||V_FROM_DIM_TABLE||' T2
        ON T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
	   AND NVL(T1.SPART_CODE,''SC'') = NVL(T2.TOP_SPART_CODE,''SC'')
       AND T1.BG_CODE = T2.BG_CODE
	   AND T1.VIEW_FLAG = T2.VIEW_FLAG
	   AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG '||V_JOIN_PB1_PART||'
     WHERE T1.PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE)
       AND T2.VERSION_ID = '||V_VERSION||'
	   '||V_SQL_CONDITION||'
	   AND T1.VIEW_FLAG = ''PROD_SPART''
	   AND T2.DOUBLE_FLAG = ''Y''
       AND T2.IS_TOP_FLAG = ''Y''
  UNION ALL
    SELECT T1.PERIOD_ID,
           T1.PERIOD_YEAR,
           '||V_SQL_PBI_PART||'
		   '||V_SQL_DIMENSION_PART||'
		   T1.SPART_CODE,
		   T1.SPART_CN_NAME,
		   T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
		   T1.APPEND_FLAG,
           T1.RMB_AVG_AMT,
           T1.ACTUAL_QTY,
		   ''N'' AS MAIN_FLAG,
           '''' AS CODE_ATTRIBUTES,
		   T1.VIEW_FLAG,
		   T1.SOFTWARE_MARK
      FROM '||V_FROM_AMT_TABLE||' T1
     WHERE T1.VIEW_FLAG = ''DIMENSION''
	   AND T1.PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE)
	   '||V_SQL_CONDITION||'
	   ';

	DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE V_SQL;

--2.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => 'TOP金额关联完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');


--3.基础数据关联处理
--3.1 TOP金额临时表创建
V_EXCEPTION_FLAG	:= 2;
DROP TABLE IF EXISTS DM_TOP_REPL_AMT_TEMP;
CREATE TEMPORARY TABLE DM_TOP_REPL_AMT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200),
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) ,
 STD_RMB_AVG_AMT  VARCHAR(2000) ,
 RMB_AVG_AMT			NUMERIC,
 STD_RMB_COST_AMT	VARCHAR(2000) ,
 RMB_COST_AMT			NUMERIC,
 ACTUAL_QTY				NUMERIC,
 APPEND_FLAG		VARCHAR(5)  ,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) ,
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) ,
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) ,
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20) ,    --是否主力编码
 SOFTWARE_MARK      VARCHAR(30)
 )
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--3.2 金额表与TOP表关联筛选出TOP金额
V_SQL:='
  INSERT INTO DM_TOP_REPL_AMT_TEMP
    (PERIOD_ID,
     PERIOD_YEAR,
     LV0_CODE,
     LV1_CODE,
     LV2_CODE,
     LV3_CODE,
     LV4_CODE,
     LV0_CN_NAME,
     LV1_CN_NAME,
     LV2_CN_NAME,
     LV3_CN_NAME,
     LV4_CN_NAME,
	 '||V_DIMENSION_PART||'
	 SPART_CODE,
	 SPART_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 APPEND_FLAG,
     '||V_RMB_AVG_AMT||'
     '||V_RMB_COST_AMT||'
	 ACTUAL_QTY,
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 VIEW_FLAG,
	 SOFTWARE_MARK)
    SELECT T1.PERIOD_ID,
           T1.PERIOD_YEAR,
           '||V_SQL_PBI_PART||'
		   '||V_SQL_DIMENSION_PART||'
		   T1.SPART_CODE,
		   T1.SPART_CN_NAME,
		   T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
		   T1.APPEND_FLAG,
           T1.RMB_AVG_AMT,
           T1.RMB_COST_AMT,
           T1.ACTUAL_QTY,
		   T2.MAIN_FLAG,
           T2.CODE_ATTRIBUTES,
		   T1.VIEW_FLAG,
		   T1.SOFTWARE_MARK
      FROM '||V_FROM_REPL_AMT_TABLE||' T1
      LEFT JOIN '||V_FROM_DIM_TABLE||' T2
        ON T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
	   AND NVL(T1.SPART_CODE,''SC'') = NVL(T2.TOP_SPART_CODE,''SC'')
       AND T1.BG_CODE = T2.BG_CODE
	   AND T1.VIEW_FLAG = T2.VIEW_FLAG
	   AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG '||V_JOIN_PB1_PART||'
     WHERE T1.PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE)
       AND T2.VERSION_ID = '||V_VERSION||'
	   '||V_SQL_CONDITION||'
	   AND T1.VIEW_FLAG = ''PROD_SPART''
	   AND T2.DOUBLE_FLAG = ''Y''
       AND T2.IS_TOP_FLAG = ''Y''
  UNION ALL
    SELECT T1.PERIOD_ID,
           T1.PERIOD_YEAR,
           '||V_SQL_PBI_PART||'
		   '||V_SQL_DIMENSION_PART||'
		   T1.SPART_CODE,
		   T1.SPART_CN_NAME,
		   T1.REGION_CODE,
           T1.REGION_CN_NAME,
           T1.REPOFFICE_CODE,
           T1.REPOFFICE_CN_NAME,
           T1.BG_CODE,
           T1.BG_CN_NAME,
           T1.OVERSEA_FLAG,
		   T1.APPEND_FLAG,
           T1.RMB_AVG_AMT,
           T1.RMB_COST_AMT,
           T1.ACTUAL_QTY,
		   ''N'' AS MAIN_FLAG,
           '''' AS CODE_ATTRIBUTES,
		   T1.VIEW_FLAG,
		   T1.SOFTWARE_MARK
      FROM '||V_FROM_REPL_AMT_TABLE||' T1
     WHERE T1.VIEW_FLAG = ''DIMENSION''
	   AND T1.PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE)
	   '||V_SQL_CONDITION||'
	   ';

	DBMS_OUTPUT.PUT_LINE(V_SQL);
	EXECUTE V_SQL;

  raise notice'11111111111111111';

--2.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '编码替代页面的TOP金额关联完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');


--3.1 删数
  V_SQL := 'TRUNCATE TABLE ' || V_TO_TABLE;
  EXECUTE V_SQL;
  
  raise notice'2222222222222';
  
--3.2 插数
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_PBI_PART||V_DIMENSION_PART||'
     SPART_CODE,
     SPART_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,
     MAIN_FLAG,
	 VIEW_FLAG,
     RMB_AVG_AMT,
     ACTUAL_QTY,
     APPEND_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV1_CODE,
           LV2_CODE,
           LV3_CODE,
           LV4_CODE,
           LV0_CN_NAME,
           LV1_CN_NAME,
           LV2_CN_NAME,
           LV3_CN_NAME,
           LV4_CN_NAME,
		   '||V_DIMENSION_PART||'
           SPART_CODE,
           SPART_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,
           MAIN_FLAG,
		   VIEW_FLAG,
           '||V_RMB_AVG_AMT||'
           ACTUAL_QTY,
           APPEND_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_TOP_AMT_TEMP;';
  EXECUTE V_SQL;


--4 写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => '月累积均本结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');


--3.1 删数
  V_SQL := 'TRUNCATE TABLE ' || V_TO_REPL_TABLE;
  EXECUTE V_SQL;
  
  raise notice'33333333333333';
  
  
--3.2 插数
  V_SQL := '
  INSERT INTO '||V_TO_REPL_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_PBI_PART||V_DIMENSION_PART||'
     SPART_CODE,
     SPART_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,
     MAIN_FLAG,
	 VIEW_FLAG,
     RMB_AVG_AMT,
     RMB_COST_AMT,
     ACTUAL_QTY,
     APPEND_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV1_CODE,
           LV2_CODE,
           LV3_CODE,
           LV4_CODE,
           LV0_CN_NAME,
           LV1_CN_NAME,
           LV2_CN_NAME,
           LV3_CN_NAME,
           LV4_CN_NAME,
		   '||V_DIMENSION_PART||'
           SPART_CODE,
           SPART_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,
           MAIN_FLAG,
		   VIEW_FLAG,
           '||V_RMB_AVG_AMT||'
           '||V_RMB_COST_AMT||'
           ACTUAL_QTY,
           APPEND_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_TOP_REPL_AMT_TEMP;';
  EXECUTE V_SQL;

  raise notice'44444444444444';
  
--4 写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '编码替代页面的月累积均本结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');


RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; $$
/

