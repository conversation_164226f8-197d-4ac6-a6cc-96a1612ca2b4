-- 分区管理优化脚本
-- 检查和优化表分区，删除2025年之前的冗余分区，确保分区策略合理
-- 优化策略6：函数中间关联的表要考虑分区和数据倾斜情况，截止2025分区的表及时调整，并删除冗余分区

-- =====================================================
-- 1. 分区信息查询和分析
-- =====================================================

-- 1.1 查询当前所有分区表的分区信息
SELECT 
    schemaname,
    tablename,
    partitionname,
    partitionboundary,
    partitiontablespace,
    partitionsize_pretty
FROM pg_partitions 
WHERE schemaname IN ('FIN_DM_OPT_FOI', 'DWRDIM', 'DMDIM')
ORDER BY schemaname, tablename, partitionname;

-- 1.2 查询主要事实表的分区分布
SELECT 
    schemaname,
    tablename,
    COUNT(*) as partition_count,
    MIN(partitionboundary) as min_partition,
    MAX(partitionboundary) as max_partition
FROM pg_partitions 
WHERE tablename IN (
    'DWL_PROD_BOM_ITEM_SHIP_DIM_I',
    'DM_FOC_BOM_ITEM_SHIP_DTL_T',
    'DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T',
    'DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T'
)
GROUP BY schemaname, tablename
ORDER BY schemaname, tablename;

-- =====================================================
-- 2. 删除2023年之前的冗余分区
-- =====================================================

-- 2.1 删除DWL_PROD_BOM_ITEM_SHIP_DIM_I表的旧分区
-- 注意：请根据实际分区命名规则调整分区名称

-- 删除2022年及之前的分区（保留最近3年数据）
DO $$
DECLARE
    partition_name TEXT;
    partition_cursor CURSOR FOR
        SELECT partitionname
        FROM pg_partitions 
        WHERE schemaname = 'FIN_DM_OPT_FOI'
          AND tablename = 'DWL_PROD_BOM_ITEM_SHIP_DIM_I'
          AND partitionboundary::INTEGER < 202100  -- 2023年之前的分区
        ORDER BY partitionname;
BEGIN
    FOR partition_name IN partition_cursor LOOP
        EXECUTE 'DROP TABLE IF EXISTS FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I_' || partition_name || ' CASCADE';
        RAISE NOTICE '已删除分区: DWL_PROD_BOM_ITEM_SHIP_DIM_I_%', partition_name;
    END LOOP;
END $$;

-- 2.2 删除目标表的旧分区
-- ICT目标表
DO $$
DECLARE
    partition_name TEXT;
    partition_cursor CURSOR FOR
        SELECT partitionname
        FROM pg_partitions 
        WHERE schemaname = 'FIN_DM_OPT_FOI'
          AND tablename = 'DM_FOC_BOM_ITEM_SHIP_DTL_T'
          AND partitionboundary::INTEGER < 202300  -- 2023年之前的分区
        ORDER BY partitionname;
BEGIN
    FOR partition_name IN partition_cursor LOOP
        EXECUTE 'DROP TABLE IF EXISTS FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T_' || partition_name || ' CASCADE';
        RAISE NOTICE '已删除ICT目标表分区: DM_FOC_BOM_ITEM_SHIP_DTL_T_%', partition_name;
    END LOOP;
END $$;

-- 数字能源目标表
DO $$
DECLARE
    partition_name TEXT;
    partition_cursor CURSOR FOR
        SELECT partitionname
        FROM pg_partitions 
        WHERE schemaname = 'FIN_DM_OPT_FOI'
          AND tablename = 'DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T'
          AND partitionboundary::INTEGER < 202300  -- 2023年之前的分区
        ORDER BY partitionname;
BEGIN
    FOR partition_name IN partition_cursor LOOP
        EXECUTE 'DROP TABLE IF EXISTS FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T_' || partition_name || ' CASCADE';
        RAISE NOTICE '已删除数字能源目标表分区: DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T_%', partition_name;
    END LOOP;
END $$;

-- IAS目标表
DO $$
DECLARE
    partition_name TEXT;
    partition_cursor CURSOR FOR
        SELECT partitionname
        FROM pg_partitions 
        WHERE schemaname = 'FIN_DM_OPT_FOI'
          AND tablename = 'DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T'
          AND partitionboundary::INTEGER < 202300  -- 2023年之前的分区
        ORDER BY partitionname;
BEGIN
    FOR partition_name IN partition_cursor LOOP
        EXECUTE 'DROP TABLE IF EXISTS FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T_' || partition_name || ' CASCADE';
        RAISE NOTICE '已删除IAS目标表分区: DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T_%', partition_name;
    END LOOP;
END $$;

-- =====================================================
-- 3. 创建2025年及未来的分区
-- =====================================================

-- 3.1 为主要事实表创建2025年分区
-- DWL_PROD_BOM_ITEM_SHIP_DIM_I表分区
DO $$
DECLARE
    year_month INTEGER;
    partition_name TEXT;
    start_boundary INTEGER;
    end_boundary INTEGER;
BEGIN
    -- 创建2025年1月到12月的分区
    FOR i IN 1..12 LOOP
        year_month := 202500 + i;
        partition_name := 'p' || year_month::TEXT;
        start_boundary := year_month;
        end_boundary := CASE WHEN i = 12 THEN 202601 ELSE year_month + 1 END;
        
        -- 检查分区是否已存在
        IF NOT EXISTS (
            SELECT 1 FROM pg_partitions 
            WHERE schemaname = 'FIN_DM_OPT_FOI'
              AND tablename = 'DWL_PROD_BOM_ITEM_SHIP_DIM_I'
              AND partitionname = partition_name
        ) THEN
            EXECUTE format('
                CREATE TABLE FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I_%s 
                PARTITION OF FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I 
                FOR VALUES FROM (%s) TO (%s)',
                partition_name, start_boundary, end_boundary);
            RAISE NOTICE '已创建分区: DWL_PROD_BOM_ITEM_SHIP_DIM_I_%', partition_name;
        END IF;
    END LOOP;
END $$;

-- 3.2 为目标表创建2025年分区
-- ICT目标表分区
DO $$
DECLARE
    year_month INTEGER;
    partition_name TEXT;
    start_boundary INTEGER;
    end_boundary INTEGER;
BEGIN
    FOR i IN 1..12 LOOP
        year_month := 202500 + i;
        partition_name := 'p' || year_month::TEXT;
        start_boundary := year_month;
        end_boundary := CASE WHEN i = 12 THEN 202601 ELSE year_month + 1 END;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_partitions 
            WHERE schemaname = 'FIN_DM_OPT_FOI'
              AND tablename = 'DM_FOC_BOM_ITEM_SHIP_DTL_T'
              AND partitionname = partition_name
        ) THEN
            EXECUTE format('
                CREATE TABLE FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T_%s 
                PARTITION OF FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T 
                FOR VALUES FROM (%s) TO (%s)',
                partition_name, start_boundary, end_boundary);
            RAISE NOTICE '已创建ICT目标表分区: DM_FOC_BOM_ITEM_SHIP_DTL_T_%', partition_name;
        END IF;
    END LOOP;
END $$;

-- 数字能源目标表分区
DO $$
DECLARE
    year_month INTEGER;
    partition_name TEXT;
    start_boundary INTEGER;
    end_boundary INTEGER;
BEGIN
    FOR i IN 1..12 LOOP
        year_month := 202500 + i;
        partition_name := 'p' || year_month::TEXT;
        start_boundary := year_month;
        end_boundary := CASE WHEN i = 12 THEN 202601 ELSE year_month + 1 END;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_partitions 
            WHERE schemaname = 'FIN_DM_OPT_FOI'
              AND tablename = 'DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T'
              AND partitionname = partition_name
        ) THEN
            EXECUTE format('
                CREATE TABLE FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T_%s 
                PARTITION OF FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T 
                FOR VALUES FROM (%s) TO (%s)',
                partition_name, start_boundary, end_boundary);
            RAISE NOTICE '已创建数字能源目标表分区: DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T_%', partition_name;
        END IF;
    END LOOP;
END $$;

-- IAS目标表分区
DO $$
DECLARE
    year_month INTEGER;
    partition_name TEXT;
    start_boundary INTEGER;
    end_boundary INTEGER;
BEGIN
    FOR i IN 1..12 LOOP
        year_month := 202500 + i;
        partition_name := 'p' || year_month::TEXT;
        start_boundary := year_month;
        end_boundary := CASE WHEN i = 12 THEN 202601 ELSE year_month + 1 END;
        
        IF NOT EXISTS (
            SELECT 1 FROM pg_partitions 
            WHERE schemaname = 'FIN_DM_OPT_FOI'
              AND tablename = 'DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T'
              AND partitionname = partition_name
        ) THEN
            EXECUTE format('
                CREATE TABLE FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T_%s 
                PARTITION OF FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T 
                FOR VALUES FROM (%s) TO (%s)',
                partition_name, start_boundary, end_boundary);
            RAISE NOTICE '已创建IAS目标表分区: DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T_%', partition_name;
        END IF;
    END LOOP;
END $$;

-- =====================================================
-- 4. 分区统计信息收集
-- =====================================================

-- 4.1 收集主要表的统计信息
ANALYZE FIN_DM_OPT_FOI.DWL_PROD_BOM_ITEM_SHIP_DIM_I;
ANALYZE FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T;
ANALYZE FIN_DM_OPT_FOI.DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T;
ANALYZE FIN_DM_OPT_FOI.DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T;

-- 4.2 查看分区优化后的效果
SELECT 
    schemaname,
    tablename,
    COUNT(*) as current_partition_count,
    MIN(partitionboundary::INTEGER) as min_period,
    MAX(partitionboundary::INTEGER) as max_period
FROM pg_partitions 
WHERE schemaname = 'FIN_DM_OPT_FOI'
  AND tablename IN (
    'DWL_PROD_BOM_ITEM_SHIP_DIM_I',
    'DM_FOC_BOM_ITEM_SHIP_DTL_T',
    'DM_FOC_ENERGY_BOM_ITEM_SHIP_DTL_T',
    'DM_FOC_IAS_BOM_ITEM_SHIP_DTL_T'
  )
GROUP BY schemaname, tablename
ORDER BY tablename;

-- =====================================================
-- 5. 分区维护建议
-- =====================================================

/*
分区维护建议：

1. 定期清理策略：
   - 建议每季度清理超过3年的历史分区
   - 保留最近36个月的数据分区

2. 自动分区创建：
   - 建议创建定时任务，每月自动创建未来3个月的分区
   - 避免因分区不存在导致的数据插入失败

3. 分区监控：
   - 定期检查分区大小，避免单个分区过大
   - 监控分区数量，避免分区过多影响查询性能

4. 数据倾斜处理：
   - 定期检查各分区的数据分布
   - 对于数据倾斜严重的分区，考虑进一步子分区

5. 执行建议：
   - 在业务低峰期执行分区维护操作
   - 执行前备份重要数据
   - 分批执行，避免长时间锁表
*/

-- =====================================================
-- 分区管理脚本执行完成
-- =====================================================
