-- Name: f_dm_foi_item_fcst_sum_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_item_fcst_sum_t(f_cate_version bigint, f_item_version bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2022-10-29
创建人  ：唐钦
背景描述：全品类item预测汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：x_success_flag ：是否成功
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_item_fcst_sum_t(p_cate_version,p_item_version)

*/

declare
	v_sp_name varchar(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ITEM_FCST_SUM_T';
	v_dml_row_count  number default 0 ;
	v_cate_version bigint;
	v_version bigint;
	v_execute_sql TEXT := NULL;   -- 执行SQL
	v_part1_public TEXT := NULL;   -- 公共部分函数 

begin
	x_success_flag := '1';
	
-- 将查询到的数据放到变量中的公共sql
 V_PART1_PUBLIC := '
                    SELECT VALUE 
                        FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                        WHERE ENABLE_FLAG = ''Y''
                        AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                      ';  
                      
 -- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
	IF f_cate_version IN (-1,0)
  AND f_item_version IN (-1,0) THEN
  V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-CATEGORY');  -- TOP品类版本号
  EXECUTE V_EXECUTE_SQL INTO V_CATE_VERSION;
  V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-ITEM');  -- 规格品版本号
  EXECUTE V_EXECUTE_SQL INTO V_VERSION;                        
 	
-- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号		
		ELSE v_cate_version := f_cate_version;
		     v_version := f_item_version;	
END IF;
	
	 --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => v_sp_name,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => v_sp_name||'开始执行');
		 
 -- 支持重跑，清除目标表要插入预测数据
--  raise notice 'truncate table ,now!';
 truncate table FIN_DM_OPT_FOI.dm_foi_item_fcst_sum_t;
--  raise notice 'truncate table ,completed!';
 --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => v_sp_name,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '清空FIN_DM_OPT_FOI.dm_foi_item_fcst_sum_t表的数据，并取到version_id='||v_version,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
 
-- 插入目标表数据
insert into FIN_DM_OPT_FOI.dm_foi_item_fcst_sum_t (
     	  id	                      
				 , year                   
				 , period_id              
				 , item_code							
				 , item_name							
				 , category_code	        
				 , category_name	        
				 , l4_ceg_code						
				 , l4_ceg_short_cn_name		
				 , l4_ceg_cn_name					
				 , l3_ceg_code						
				 , l3_ceg_short_cn_name		
				 , l3_ceg_cn_name					
				 , l2_ceg_code						
				 , l2_ceg_cn_name					
				 , receive_qty            
				 , receive_amt_usd        
				 , receive_amt_cny   
				 , top_flag
				 , created_by	            
				 , creation_date	        
				 , last_updated_by	      
				 , last_update_date	      
				 , del_flag
				 , append_flag
				 , avg_price_cny
				 , avg_price_usd
		)
select  t1.id	                      
			, t1.year                   
			, t1.period_id              
			, t1.item_code							
			, t1.item_name							
			, t1.category_code	        
			, t1.category_name	        
			, t1.l4_ceg_code						
			, t1.l4_ceg_short_cn_name		
			, t1.l4_ceg_cn_name					
			, t1.l3_ceg_code						
			, t1.l3_ceg_short_cn_name		
			, t1.l3_ceg_cn_name					
			, t1.l2_ceg_code						
			, t1.l2_ceg_cn_name					
			, t1.receive_qty            
			, t1.receive_amt_usd        
			, t1.receive_amt_cny   
 			, case when t1.item_code = t2.item_code then 'Y'
					else 'N' end as top_flag
		 	, t1.created_by
		 	, t1.creation_date
		 	, t1.last_updated_by
		 	, t1.last_update_date
		 	, t1.del_flag
			, t1.append_flag
			, t1.avg_price_cny
			, t1.avg_price_usd
		from FIN_DM_OPT_FOI.dm_foi_fcst_sum_t t1
		left join FIN_DM_OPT_FOI.dm_foi_top_item_info_t t2
		on t1.item_code = t2.item_code
		and t2.version_id = v_version
		and t1.category_code = t2.category_code;

--2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 2,
   F_CAL_LOG_DESC => '插入带TOP标识的预测数到FIN_DM_OPT_FOI.dm_foi_item_fcst_sum_t表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');
   
-- 收集信息
  ANALYSE FIN_DM_OPT_FOI.dm_foi_item_fcst_sum_t;

  --3.日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 3,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束');

return 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  x_success_flag := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => x_success_flag, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
	 
end;
$$
/

