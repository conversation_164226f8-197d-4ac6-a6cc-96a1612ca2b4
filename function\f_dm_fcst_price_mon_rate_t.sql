-- Name: f_dm_fcst_price_mon_rate_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mon_rate_t(f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间: 20241111
创建人  : 黄心蕊 hwx1187045
背景描述: 定价指数-指数计算 T-2年到T年指数
参数描述: 参数一:  F_VERSION_ID 版本号
		  参数二:  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败
--来源表
指数 DM_FCST_PRICE_MON_COST_IDX_T

--目标表
同环比 DM_FCST_PRICE_MON_RATE_T

SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MON_RATE_T('');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MON_RATE_T';
  V_VERSION                 BIGINT; --版本号
  V_BASE_PERIOD_ID          INT ; --基期会计期
  V_EXCEPTION_FLAG			INT:=0; --异常步骤
  
BEGIN
X_RESULT_STATUS := '1';

 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
   if MONTH(CURRENT_TIMESTAMP) = 1
  then V_BASE_PERIOD_ID = TO_NUMBER((YEAR(CURRENT_DATE) - 2) || '12');
  ELSE
  V_BASE_PERIOD_ID = TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '12');
  END IF ;
  
  
 --版本号取值
  V_EXCEPTION_FLAG	:= 0;
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

 V_EXCEPTION_FLAG := 1; 
DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_RATE_T WHERE VERSION_ID = V_VERSION;

 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION||' ，此版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  V_EXCEPTION_FLAG := 2; 
  WITH LEV_INDEX AS
   (SELECT PERIOD_ID,
           SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,	--取相同月
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           BG_CODE,
           BG_CN_NAME
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_COST_IDX_T
     WHERE VERSION_ID = V_VERSION
       AND BASE_PERIOD_ID = V_BASE_PERIOD_ID),
  BASE_YOY AS
   (SELECT PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           BG_CODE,
           BG_CN_NAME,
           PARENT_CODE,
           PARENT_CN_NAME,
           OVERSEA_FLAG,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY GROUP_CODE, GROUP_LEVEL, REGION_CODE, REPOFFICE_CODE, SIGN_TOP_CUST_CATEGORY_CODE, SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, VIEW_FLAG, BG_CODE, PARENT_CODE, OVERSEA_FLAG, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY GROUP_CODE, GROUP_LEVEL, REGION_CODE, REPOFFICE_CODE, SIGN_TOP_CUST_CATEGORY_CODE, SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, VIEW_FLAG, BG_CODE, PARENT_CODE, OVERSEA_FLAG, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY GROUP_CODE, GROUP_LEVEL, REGION_CODE, REPOFFICE_CODE, SIGN_TOP_CUST_CATEGORY_CODE, SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, VIEW_FLAG, BG_CODE, PARENT_CODE, OVERSEA_FLAG ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY GROUP_CODE, GROUP_LEVEL, REGION_CODE, REPOFFICE_CODE, SIGN_TOP_CUST_CATEGORY_CODE, SIGN_SUBSIDIARY_CUSTCATG_CN_NAME, VIEW_FLAG, BG_CODE, PARENT_CODE, OVERSEA_FLAG ORDER BY PERIOD_ID) AS POP_COST_INDEX
      FROM LEV_INDEX)
   INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_RATE_T
     (VERSION_ID,
      PERIOD_YEAR,
      PERIOD_ID,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      RATE,
      RATE_FLAG,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG,
      BG_CODE,
      BG_CN_NAME)
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
         'YOY' AS RATE_FLAG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT V_VERSION AS VERSION_ID,
         LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
         'POP' AS RATE_FLAG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL;
   
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '同环比表第 '||V_VERSION||' 版本数据插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
	  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

