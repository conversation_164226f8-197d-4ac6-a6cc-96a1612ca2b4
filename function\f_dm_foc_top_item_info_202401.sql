-- Name: f_dm_foc_top_item_info_202401; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_top_item_info_202401(f_dimension_type character varying, f_keystr character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023/03/21
创建人  ：刘必华
最后修改时间:2023/06/07
最后修改人:曹昆
背景描述：分视角统计TOP品类下ITEM的权重, 以及给前95%的ITEM打上TOP标识
修改： 量纲增加 SPART_CODE 和 SPART_CN_NAME 字段 许灿烽 20231220
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T+FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_CATE_INFO_T+FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_CATE_INFO_T+FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOP_ITEM_INFO()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOP_ITEM_INFO_202401'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自序列:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_TOP_CATE_ID BIGINT; --TOP品类清单最新的版本号
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_FROM_TABLE1 VARCHAR(50); -- 来源表1
  V_FROM_TABLE2 VARCHAR(50); -- 来源表2
  V_TO_TABLE VARCHAR(50); -- 目标表
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(500);
  V_DIMENSION_CN_NAME VARCHAR(2000);
  V_DIMENSION_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(2000);
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  V_INSERT_DIMENSION_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);

--202401月版本需求新增量纲
  V_SPART_CODE VARCHAR(50);
  V_SPART_CN_NAME VARCHAR(50);
  V_IN_SPART_CODE VARCHAR(50);
  V_IN_SPART_CN_NAME VARCHAR(50);
  V_INSERT_SPART_CODE VARCHAR(100);

BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
  IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--来源表2
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_CATE_INFO_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T';--来源表2
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_CATE_INFO_T_202401';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_202401';--来源表2
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T_202401'; --目标表
  ELSE
    NULL;
  END IF;
  
  --删除当前会计期版本的TOP规格品数据, 支持单月重刷
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VERSION_NAME = '''||V_VERSION_NAME||'''';
  EXECUTE IMMEDIATE V_SQL ;
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP规格品清单的数据, 删除版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --查询TOP品类清单中最新的版本号, 作为TOP规格品的父版本ID
  SELECT MAX(T.VERSION_ID) 
    INTO V_TOP_CATE_ID 
    FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'CATEGORY'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;

 -- 查询该月版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
	FROM
		FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM';
	-- FLAG 不等于0，说明已有版本号，沿用	
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM';
  ELSE
  --每次刷新TOP规格品清单, 赋予新的版本号
  SELECT FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S.NEXTVAL
    INTO V_VERSION_ID
    FROM DUAL;

  --往版本信息表记录本次TOP规格品信息, 版本号为V_VERSION_ID, 依赖的品类专家维V_TOP_CATE_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
   VALUES
   (V_VERSION_ID,V_TOP_CATE_ID,V_VERSION_NAME,1,'AUTO','ITEM',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N');
   END IF;
     
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录TOP规格品版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --分视角总地区(全球)总BG(集团)计算TOP品类下的ITEM权重 
  DROP TABLE IF EXISTS DM_FOC_TOP_ITEM_INFO_TEMP ;
  CREATE TEMPORARY TABLE DM_FOC_TOP_ITEM_INFO_TEMP
  (
    VERSION_ID BIGINT,
    VERSION_NAME VARCHAR2(50),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR	VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(200),
    L2_NAME VARCHAR(200),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(500),
    DIMENSION_CN_NAME    VARCHAR(2000),
    DIMENSION_EN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
--202401月版本需求新增量纲
    SPART_CODE VARCHAR(50),
    SPART_CN_NAME VARCHAR(50),
    L3_CEG_CODE    VARCHAR(50),
    L3_CEG_CN_NAME    VARCHAR(200),
    L3_CEG_SHORT_CN_NAME    VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE    VARCHAR(50),
    CATEGORY_CN_NAME    VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    WEIGHT_RATE    NUMERIC,
    PARENT_CODE VARCHAR(50),
    PARENT_WEIGHT_RATE NUMERIC,
    DOUBLE_FLAG VARCHAR2(2),
    CALIBER_FLAG VARCHAR2(2)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  
  
  --创建分视角收敛ITEM的年发货额临时表 
  DROP TABLE IF EXISTS RMB_COST_TEMP ;
  CREATE TEMPORARY TABLE RMB_COST_TEMP
  (
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200),
    CALIBER_FLAG VARCHAR2(2),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR VARCHAR(50),
    YEAR_FLAG VARCHAR2(2),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(200),
    L2_NAME VARCHAR(200),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(500),
    DIMENSION_CN_NAME    VARCHAR(2000),
    DIMENSION_EN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
--202401月版本需求新增量纲
    SPART_CODE VARCHAR(50),
    SPART_CN_NAME VARCHAR(50),
    L3_CEG_CODE VARCHAR(50),
    L3_CEG_CN_NAME VARCHAR(200),
    L3_CEG_SHORT_CN_NAME VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE VARCHAR(50),
    CATEGORY_CN_NAME VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    RMB_COST_AMT NUMERIC
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;

    --创建汇总分视角收敛ITEM的年发货额临时表 
  DROP TABLE IF EXISTS SUM_COST_TEMP ;
  CREATE TEMPORARY TABLE SUM_COST_TEMP
  (
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200),
    CALIBER_FLAG VARCHAR2(2),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR VARCHAR(50),
    YEAR_FLAG VARCHAR2(2),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(200),
    L2_NAME VARCHAR(200),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(500),
    DIMENSION_CN_NAME    VARCHAR(2000),
    DIMENSION_EN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
--202401月版本需求新增量纲
    SPART_CODE VARCHAR(50),
    SPART_CN_NAME VARCHAR(50),
    L3_CEG_CODE VARCHAR(50),
    L3_CEG_CN_NAME VARCHAR(200),
    L3_CEG_SHORT_CN_NAME VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE VARCHAR(50),
    CATEGORY_CN_NAME VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    RMB_COST_AMT NUMERIC
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;

  
  
  --分视角分地区(全球、国内、海外)分BG去计算TOP品类下的ITEM权重 (9月版本需求新增)
  DROP TABLE IF EXISTS DM_FOC_WEIGHT_ITEM_INFO_TEMP ;
  CREATE TEMPORARY TABLE DM_FOC_WEIGHT_ITEM_INFO_TEMP
  (
    VERSION_ID BIGINT,
    VERSION_NAME VARCHAR2(50),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR	VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(200),
    L2_NAME VARCHAR(200),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(500),
    DIMENSION_CN_NAME    VARCHAR(2000),
    DIMENSION_EN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
--202401月版本需求新增量纲
    SPART_CODE VARCHAR(50),
    SPART_CN_NAME VARCHAR(50),
    L3_CEG_CODE    VARCHAR(50),
    L3_CEG_CN_NAME    VARCHAR(200),
    L3_CEG_SHORT_CN_NAME    VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE    VARCHAR(50),
    CATEGORY_CN_NAME    VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    WEIGHT_RATE    NUMERIC,
    PARENT_CODE VARCHAR(50),
    PARENT_WEIGHT_RATE NUMERIC,
    DOUBLE_FLAG VARCHAR2(2),
    CALIBER_FLAG VARCHAR2(2),
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  

    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'A.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='A.LV3_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'A.L1_NAME,';
    V_IN_L2_NAME := 'A.L2_NAME,';
    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND NVL(A.LV3_PROD_RND_TEAM_CODE, 3) = NVL(B.LV3_PROD_RND_TEAM_CODE, 3)';
    V_INSERT_L1_NAME := ' AND NVL(A.L1_NAME, 1) = NVL(B.L1_NAME, 1)';
    V_INSERT_L2_NAME := ' AND NVL(A.L2_NAME, 2) = NVL(B.L2_NAME, 2)';
    
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'A.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'A.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'A.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'A.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'A.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'A.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'A.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'A.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'A.DIMENSION_SUB_DETAIL_EN_NAME,';
    V_INSERT_DIMENSION_CODE := ' AND NVL(A.DIMENSION_CODE, 1) = NVL(B.DIMENSION_CODE, 1)';
    V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(A.DIMENSION_SUBCATEGORY_CODE, 2) = NVL(B.DIMENSION_SUBCATEGORY_CODE, 2)';
    V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' AND NVL(A.DIMENSION_SUB_DETAIL_CODE, 3) = NVL(B.DIMENSION_SUB_DETAIL_CODE, 3)';

--202401月版本需求新增量纲
    V_SPART_CODE :='SPART_CODE,';
    V_SPART_CN_NAME := 'SPART_CN_NAME,';
    V_IN_SPART_CODE := 'A.SPART_CODE,';
    V_IN_SPART_CN_NAME := 'A.SPART_CN_NAME,';
    V_INSERT_SPART_CODE := ' AND NVL(A.SPART_CODE, 4) = NVL(B.SPART_CODE, 4)';

    --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
--202401月版本需求新增量纲
    V_SPART_CODE :='';
    V_SPART_CN_NAME := '';
    V_IN_SPART_CODE := '';
    V_IN_SPART_CN_NAME := '';
    V_INSERT_SPART_CODE := '';
       
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
   ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
--202401月版本需求新增量纲
    V_SPART_CODE :='';
    V_SPART_CN_NAME := '';
    V_IN_SPART_CODE := '';
    V_IN_SPART_CN_NAME := '';
    V_INSERT_SPART_CODE := '';
       
    --量纲颗粒度的维度时，不需要L1、L2字段
   ELSIF F_DIMENSION_TYPE = 'D' THEN
        V_L1_NAME := '';
        V_L2_NAME := '';
        V_IN_L1_NAME := '';
        V_IN_L2_NAME := '';
        V_INSERT_L1_NAME := '';
        V_INSERT_L2_NAME := '';
    
    ELSE
      NULL;
    END IF;
    
    
      --第一步先处理按照分地区(全球、国内、海外)分BG计算得到一份权重数据存入临时表
      --数据插入分视角收敛ITEM的年发货额
      V_SQL := 
      'INSERT INTO RMB_COST_TEMP
        ( CALIBER_FLAG
         ,VIEW_FLAG
         ,PERIOD_YEAR
         ,YEAR_FLAG
         ,LV0_PROD_RND_TEAM_CODE
         ,LV0_PROD_RD_TEAM_CN_NAME
         ,LV1_PROD_RND_TEAM_CODE
         ,LV1_PROD_RD_TEAM_CN_NAME
         ,LV2_PROD_RND_TEAM_CODE
         ,LV2_PROD_RD_TEAM_CN_NAME,'||
          V_LV3_PROD_RND_TEAM_CODE ||
          V_LV3_PROD_RD_TEAM_CN_NAME ||
          V_L1_NAME ||
          V_L2_NAME ||
          V_DIMENSION_CODE ||
          V_DIMENSION_CN_NAME ||
          V_DIMENSION_EN_NAME||
          V_DIMENSION_SUBCATEGORY_CODE ||
          V_DIMENSION_SUBCATEGORY_CN_NAME ||
          V_DIMENSION_SUBCATEGORY_EN_NAME||
          V_DIMENSION_SUB_DETAIL_CODE ||
          V_DIMENSION_SUB_DETAIL_CN_NAME ||
          V_DIMENSION_SUB_DETAIL_EN_NAME ||
          V_SPART_CODE ||
          V_SPART_CN_NAME ||'
          L3_CEG_CODE
         ,L3_CEG_CN_NAME
         ,L3_CEG_SHORT_CN_NAME
         ,L4_CEG_CODE
         ,L4_CEG_CN_NAME
         ,L4_CEG_SHORT_CN_NAME
         ,CATEGORY_CODE
         ,CATEGORY_CN_NAME
         ,ITEM_CODE
         ,ITEM_CN_NAME
         ,RMB_COST_AMT
         ,OVERSEA_FLAG
         ,LV0_PROD_LIST_CODE
         ,LV0_PROD_LIST_CN_NAME
         ,LV0_PROD_LIST_EN_NAME
        )
      --分视角收敛ITEM的年发货额
      SELECT  T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              CASE
                WHEN T.PERIOD_YEAR BETWEEN YEAR(CURRENT_TIMESTAMP) - 1 AND
                     YEAR(CURRENT_TIMESTAMP) THEN
                 1
                ELSE
                 0
              END AS YEAR_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              --GS_DECRYPT(T.RMB_COST_AMT,'''|| F_KEYSTR ||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,
              T.RMB_COST_AMT AS RMB_COST_AMT,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE2 ||' T 
        WHERE REVIEW_ITEM_FLAG = 0 '; 

    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入分视角分地区(全球、国内、海外)分BG收敛ITEM的年发货额, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

      --第一步先处理按照分地区(全球、国内、海外)分BG计算得到一份权重数据存入临时表
      --汇总数据插入汇总分视角收敛ITEM的年发货额
      V_SQL := 
      'INSERT INTO SUM_COST_TEMP
        ( CALIBER_FLAG
         ,VIEW_FLAG
         ,PERIOD_YEAR
         ,YEAR_FLAG
         ,LV0_PROD_RND_TEAM_CODE
         ,LV0_PROD_RD_TEAM_CN_NAME
         ,LV1_PROD_RND_TEAM_CODE
         ,LV1_PROD_RD_TEAM_CN_NAME
         ,LV2_PROD_RND_TEAM_CODE
         ,LV2_PROD_RD_TEAM_CN_NAME,'||
          V_LV3_PROD_RND_TEAM_CODE ||
          V_LV3_PROD_RD_TEAM_CN_NAME ||
          V_L1_NAME ||
          V_L2_NAME ||
          V_DIMENSION_CODE ||
          V_DIMENSION_CN_NAME ||
          V_DIMENSION_EN_NAME||
          V_DIMENSION_SUBCATEGORY_CODE ||
          V_DIMENSION_SUBCATEGORY_CN_NAME ||
          V_DIMENSION_SUBCATEGORY_EN_NAME||
          V_DIMENSION_SUB_DETAIL_CODE ||
          V_DIMENSION_SUB_DETAIL_CN_NAME ||
          V_DIMENSION_SUB_DETAIL_EN_NAME ||
          V_SPART_CODE ||
          V_SPART_CN_NAME ||'
          L3_CEG_CODE
         ,L3_CEG_CN_NAME
         ,L3_CEG_SHORT_CN_NAME
         ,L4_CEG_CODE
         ,L4_CEG_CN_NAME
         ,L4_CEG_SHORT_CN_NAME
         ,CATEGORY_CODE
         ,CATEGORY_CN_NAME
         ,ITEM_CODE
         ,ITEM_CN_NAME
         ,RMB_COST_AMT
         ,OVERSEA_FLAG
         ,LV0_PROD_LIST_CODE
         ,LV0_PROD_LIST_CN_NAME
         ,LV0_PROD_LIST_EN_NAME
        )
      --分视角收敛ITEM的年发货额
      SELECT  T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.YEAR_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              SUM(T.RMB_COST_AMT) AS RMB_COST_AMT,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM RMB_COST_TEMP T
       GROUP BY  T.CALIBER_FLAG,
                 T.VIEW_FLAG,
                 T.YEAR_FLAG,
                 T.PERIOD_YEAR,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_LV3_PROD_RND_TEAM_CODE ||
                 V_LV3_PROD_RD_TEAM_CN_NAME ||
                 V_L1_NAME ||
                 V_L2_NAME ||
                 V_DIMENSION_CODE ||
                 V_DIMENSION_CN_NAME ||
                 V_DIMENSION_EN_NAME||
                 V_DIMENSION_SUBCATEGORY_CODE ||
                 V_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_DIMENSION_SUB_DETAIL_CODE ||
                 V_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_DIMENSION_SUB_DETAIL_EN_NAME ||
                 V_SPART_CODE ||
                 V_SPART_CN_NAME ||'
                 T.L3_CEG_CODE,
                 T.L3_CEG_CN_NAME,
                 T.L3_CEG_SHORT_CN_NAME,
                 T.L4_CEG_CODE,
                 T.L4_CEG_CN_NAME,
                 T.L4_CEG_SHORT_CN_NAME,
                 T.CATEGORY_CODE,
                 T.CATEGORY_CN_NAME,
                 T.ITEM_CODE,
                 T.ITEM_CN_NAME,
                 T.OVERSEA_FLAG,
                 T.LV0_PROD_LIST_CODE,
                 T.LV0_PROD_LIST_CN_NAME,
                 T.LV0_PROD_LIST_EN_NAME';

    EXECUTE IMMEDIATE V_SQL;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '汇总数据插入汇总分视角分地区(全球、国内、海外)分BG收敛ITEM的年发货额, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


  --第一步先处理按照分地区(全球、国内、海外)分BG计算得到一份权重数据存入临时表
  V_SQL :=
  'INSERT INTO DM_FOC_WEIGHT_ITEM_INFO_TEMP
    (VERSION_ID,
     VERSION_NAME,
     VIEW_FLAG,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
     V_SPART_CODE ||
     V_SPART_CN_NAME ||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_WEIGHT_RATE,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
    WITH TOP_CATE_TEMP AS
     (
      --取最新版本号的TOP品类
      SELECT DISTINCT  T.LV0_PROD_RND_TEAM_CODE,
                       T.LV0_PROD_RD_TEAM_CN_NAME,
                       T.LV1_PROD_RND_TEAM_CODE,
                       T.LV1_PROD_RD_TEAM_CN_NAME,
                       T.LV2_PROD_RND_TEAM_CODE,
                       T.LV2_PROD_RD_TEAM_CN_NAME,'||
                       V_LV3_PROD_RND_TEAM_CODE ||
                       V_LV3_PROD_RD_TEAM_CN_NAME ||
                       V_L1_NAME ||
                       V_L2_NAME ||
                       V_DIMENSION_CODE ||
                       V_DIMENSION_CN_NAME ||
                       V_DIMENSION_EN_NAME||
                       V_DIMENSION_SUBCATEGORY_CODE ||
                       V_DIMENSION_SUBCATEGORY_CN_NAME ||
                       V_DIMENSION_SUBCATEGORY_EN_NAME||
                       V_DIMENSION_SUB_DETAIL_CODE ||
                       V_DIMENSION_SUB_DETAIL_CN_NAME ||
                       V_DIMENSION_SUB_DETAIL_EN_NAME ||
                       V_SPART_CODE ||
                       V_SPART_CN_NAME ||'
                       T.TOP_L3_CEG_CODE          AS L3_CEG_CODE,
                       T.TOP_L3_CEG_CN_NAME       AS L3_CEG_CN_NAME,
                       T.TOP_L3_CEG_SHORT_CN_NAME AS L3_CEG_SHORT_CN_NAME,
                       T.TOP_L4_CEG_CODE          AS L4_CEG_CODE,
                       T.TOP_L4_CEG_CN_NAME       AS L4_CEG_CN_NAME,
                       T.TOP_L4_CEG_SHORT_CN_NAME AS L4_CEG_SHORT_CN_NAME,
                       T.TOP_CATEGORY_CODE        AS CATEGORY_CODE,
                       T.TOP_CATEGORY_CN_NAME     AS CATEGORY_CN_NAME,
                       T.VIEW_FLAG,
                       T.WEIGHT_RATE,
                       T.CALIBER_FLAG,
                       T.OVERSEA_FLAG,
                       T.LV0_PROD_LIST_CODE,
                       T.LV0_PROD_LIST_CN_NAME,
                       T.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE1 ||' T
       WHERE T.IS_TOP_FLAG = ''Y''
         AND T.VERSION_ID = '|| V_TOP_CATE_ID ||'),
    
    TOP_ITEM_TEMP AS
     (
      --分视角下分地区(全球、国内、海外)分BG计算筛选TOP品类的ITEM
      SELECT  A.CALIBER_FLAG,
              A.VIEW_FLAG,
              TO_CHAR(A.PERIOD_YEAR) AS PERIOD_YEAR,
              A.YEAR_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME ||
              V_IN_L2_NAME ||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_IN_SPART_CODE ||
              V_IN_SPART_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              A.ITEM_CODE,
              A.ITEM_CN_NAME,
              A.RMB_COST_AMT,
              SUM(A.RMB_COST_AMT) OVER(PARTITION BY A.OVERSEA_FLAG, A.LV0_PROD_LIST_CODE, A.CALIBER_FLAG, A.VIEW_FLAG, A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_IN_DIMENSION_CODE || V_IN_DIMENSION_SUBCATEGORY_CODE || V_IN_DIMENSION_SUB_DETAIL_CODE || V_IN_SPART_CODE ||'  A.L3_CEG_CODE, A.L4_CEG_CODE, A.CATEGORY_CODE, A.PERIOD_YEAR) AS TOTAL_CATE_AMT,
              B.CATEGORY_CODE AS PARENT_CODE,
              B.WEIGHT_RATE AS PARENT_WEIGHT_RATE,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME
        FROM SUM_COST_TEMP A
       INNER JOIN TOP_CATE_TEMP B
          ON A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
         AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) =
             NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
         AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) =
             NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
       '||V_INSERT_LV3_PROD_RND_TEAM_CODE
        ||V_INSERT_L1_NAME
        ||V_INSERT_L2_NAME
        ||V_INSERT_DIMENSION_CODE
        ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
        ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
        ||V_INSERT_SPART_CODE||'
         AND A.L3_CEG_CODE = B.L3_CEG_CODE
         AND A.L4_CEG_CODE = B.L4_CEG_CODE
         AND A.CATEGORY_CODE = B.CATEGORY_CODE
         AND A.VIEW_FLAG = B.VIEW_FLAG
         AND A.CALIBER_FLAG = B.CALIBER_FLAG
         AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
         AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE)
    
    --分视角计算各年的权重值
    SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
           '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           M.VIEW_FLAG,
           DECODE(M.PERIOD_YEAR,
                  YEAR(CURRENT_TIMESTAMP),
                  M.PERIOD_YEAR || ''YTD'',
                  M.PERIOD_YEAR) AS PERIOD_YEAR,
           M.LV0_PROD_RND_TEAM_CODE,
           M.LV0_PROD_RD_TEAM_CN_NAME,
           M.LV1_PROD_RND_TEAM_CODE,
           M.LV1_PROD_RD_TEAM_CN_NAME,
           M.LV2_PROD_RND_TEAM_CODE,
           M.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
           V_SPART_CODE ||
           V_SPART_CN_NAME ||'
           M.L3_CEG_CODE,
           M.L3_CEG_CN_NAME,
           M.L3_CEG_SHORT_CN_NAME,
           M.L4_CEG_CODE,
           M.L4_CEG_CN_NAME,
           M.L4_CEG_SHORT_CN_NAME,
           M.CATEGORY_CODE,
           M.CATEGORY_CN_NAME,
           M.ITEM_CODE,
           M.ITEM_CN_NAME,
           M.RMB_COST_AMT / NULLIF(M.TOTAL_CATE_AMT,0) AS WEIGHT_RATE,
           M.PARENT_CODE,
           M.PARENT_WEIGHT_RATE,
           ''N'' AS DOUBLE_FLAG,
           M.CALIBER_FLAG,
           M.OVERSEA_FLAG,
           M.LV0_PROD_LIST_CODE,
           M.LV0_PROD_LIST_CN_NAME,
           M.LV0_PROD_LIST_EN_NAME
      FROM TOP_ITEM_TEMP M
    UNION ALL
    --分视角计算上一年到今年YTD的TOP品类下item的权重值
    SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
           '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           S.VIEW_FLAG,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' ||
           YEAR(CURRENT_TIMESTAMP) || ''YTD'' AS PERIOD_YEAR,
           S.LV0_PROD_RND_TEAM_CODE,
           S.LV0_PROD_RD_TEAM_CN_NAME,
           S.LV1_PROD_RND_TEAM_CODE,
           S.LV1_PROD_RD_TEAM_CN_NAME,
           S.LV2_PROD_RND_TEAM_CODE,
           S.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
           V_SPART_CODE ||
           V_SPART_CN_NAME ||'
           S.L3_CEG_CODE,
           S.L3_CEG_CN_NAME,
           S.L3_CEG_SHORT_CN_NAME,
           S.L4_CEG_CODE,
           S.L4_CEG_CN_NAME,
           S.L4_CEG_SHORT_CN_NAME,
           S.CATEGORY_CODE,
           S.CATEGORY_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_CN_NAME,
           S.RMB_COST_AMT / NULLIF(S.TOTAL_CATE_AMT,0) AS WEIGHT_RATE,
           S.PARENT_CODE,
           S.PARENT_WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG,
           S.CALIBER_FLAG,
           S.OVERSEA_FLAG,
           S.LV0_PROD_LIST_CODE,
           S.LV0_PROD_LIST_CN_NAME,
           S.LV0_PROD_LIST_EN_NAME
      FROM (SELECT P.CALIBER_FLAG,
                   P.VIEW_FLAG,
                   P.LV0_PROD_RND_TEAM_CODE,
                   P.LV0_PROD_RD_TEAM_CN_NAME,
                   P.LV1_PROD_RND_TEAM_CODE,
                   P.LV1_PROD_RD_TEAM_CN_NAME,
                   P.LV2_PROD_RND_TEAM_CODE,
                   P.LV2_PROD_RD_TEAM_CN_NAME,'||
                   V_LV3_PROD_RND_TEAM_CODE ||
                   V_LV3_PROD_RD_TEAM_CN_NAME ||
                   V_L1_NAME ||
                   V_L2_NAME ||
                   V_DIMENSION_CODE ||
                   V_DIMENSION_CN_NAME ||
                   V_DIMENSION_EN_NAME||
                   V_DIMENSION_SUBCATEGORY_CODE ||
                   V_DIMENSION_SUBCATEGORY_CN_NAME ||
                   V_DIMENSION_SUBCATEGORY_EN_NAME||
                   V_DIMENSION_SUB_DETAIL_CODE ||
                   V_DIMENSION_SUB_DETAIL_CN_NAME ||
                   V_DIMENSION_SUB_DETAIL_EN_NAME ||
                   V_SPART_CODE ||
                   V_SPART_CN_NAME ||'
                   P.L3_CEG_CODE,
                   P.L3_CEG_CN_NAME,
                   P.L3_CEG_SHORT_CN_NAME,
                   P.L4_CEG_CODE,
                   P.L4_CEG_CN_NAME,
                   P.L4_CEG_SHORT_CN_NAME,
                   P.CATEGORY_CODE,
                   P.CATEGORY_CN_NAME,
                   P.ITEM_CODE,
                   P.ITEM_CN_NAME,
                   SUM(P.RMB_COST_AMT) AS RMB_COST_AMT,
                   SUM(SUM(P.RMB_COST_AMT)) OVER(PARTITION BY P.OVERSEA_FLAG, P.LV0_PROD_LIST_CODE, P.CALIBER_FLAG, P.VIEW_FLAG, P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' P.L3_CEG_CODE, P.L4_CEG_CODE, P.CATEGORY_CODE) AS TOTAL_CATE_AMT,
                   P.PARENT_CODE AS PARENT_CODE,
                   MIN(P.PARENT_WEIGHT_RATE) AS PARENT_WEIGHT_RATE,
                   P.OVERSEA_FLAG,
                   P.LV0_PROD_LIST_CODE,
                   P.LV0_PROD_LIST_CN_NAME,
                   P.LV0_PROD_LIST_EN_NAME
              FROM TOP_ITEM_TEMP P
             WHERE P.YEAR_FLAG = 1
             GROUP BY P.CALIBER_FLAG,
                      P.VIEW_FLAG,
                      P.LV0_PROD_RND_TEAM_CODE,
                      P.LV0_PROD_RD_TEAM_CN_NAME,
                      P.LV1_PROD_RND_TEAM_CODE,
                      P.LV1_PROD_RD_TEAM_CN_NAME,
                      P.LV2_PROD_RND_TEAM_CODE,
                      P.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_LV3_PROD_RND_TEAM_CODE ||
                      V_LV3_PROD_RD_TEAM_CN_NAME ||
                      V_L1_NAME ||
                      V_L2_NAME ||
                      V_DIMENSION_CODE ||
                      V_DIMENSION_CN_NAME ||
                      V_DIMENSION_EN_NAME||
                      V_DIMENSION_SUBCATEGORY_CODE ||
                      V_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_DIMENSION_SUB_DETAIL_CODE ||
                      V_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_DIMENSION_SUB_DETAIL_EN_NAME ||
                      V_SPART_CODE ||
                      V_SPART_CN_NAME ||'
                      P.L3_CEG_CODE,
                      P.L3_CEG_CN_NAME,
                      P.L3_CEG_SHORT_CN_NAME,
                      P.L4_CEG_CODE,
                      P.L4_CEG_CN_NAME,
                      P.L4_CEG_SHORT_CN_NAME,
                      P.CATEGORY_CODE,
                      P.CATEGORY_CN_NAME,
                      P.ITEM_CODE,
                      P.ITEM_CN_NAME,
                      P.PARENT_CODE,
                      P.OVERSEA_FLAG,
                      P.LV0_PROD_LIST_CODE,
                      P.LV0_PROD_LIST_CN_NAME,
                      P.LV0_PROD_LIST_EN_NAME) S';

    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角分视角分地区(全球、国内、海外)分BG各年TOP规格品的权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    
----------------------------------------------------------------------以上第一步先处理按照分地区(全球、国内、海外)分BG的权重数据-------------

----------------------------------------------------------------------以下第二步重新计算按照总地区(全球)总BG(集团)的角度去打TOP标签数据------------------------


--清空临时表数据，需重新按照总地区总BG(集团)进行计算
EXECUTE IMMEDIATE 'TRUNCATE TABLE RMB_COST_TEMP';
EXECUTE IMMEDIATE 'TRUNCATE TABLE SUM_COST_TEMP';


      --数据插入分视角收敛ITEM的年发货额
      V_SQL := 
      'INSERT INTO RMB_COST_TEMP
        ( CALIBER_FLAG
         ,VIEW_FLAG
         ,PERIOD_YEAR
         ,YEAR_FLAG
         ,LV0_PROD_RND_TEAM_CODE
         ,LV0_PROD_RD_TEAM_CN_NAME
         ,LV1_PROD_RND_TEAM_CODE
         ,LV1_PROD_RD_TEAM_CN_NAME
         ,LV2_PROD_RND_TEAM_CODE
         ,LV2_PROD_RD_TEAM_CN_NAME,'||
          V_LV3_PROD_RND_TEAM_CODE ||
          V_LV3_PROD_RD_TEAM_CN_NAME ||
          V_L1_NAME ||
          V_L2_NAME ||
          V_DIMENSION_CODE ||
          V_DIMENSION_CN_NAME ||
          V_DIMENSION_EN_NAME||
          V_DIMENSION_SUBCATEGORY_CODE ||
          V_DIMENSION_SUBCATEGORY_CN_NAME ||
          V_DIMENSION_SUBCATEGORY_EN_NAME||
          V_DIMENSION_SUB_DETAIL_CODE ||
          V_DIMENSION_SUB_DETAIL_CN_NAME ||
          V_DIMENSION_SUB_DETAIL_EN_NAME ||
          V_SPART_CODE ||
          V_SPART_CN_NAME ||'
          L3_CEG_CODE
         ,L3_CEG_CN_NAME
         ,L3_CEG_SHORT_CN_NAME
         ,L4_CEG_CODE
         ,L4_CEG_CN_NAME
         ,L4_CEG_SHORT_CN_NAME
         ,CATEGORY_CODE
         ,CATEGORY_CN_NAME
         ,ITEM_CODE
         ,ITEM_CN_NAME
         ,RMB_COST_AMT
        )
      --分视角收敛ITEM的年发货额
      SELECT  T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              CASE
                WHEN T.PERIOD_YEAR BETWEEN YEAR(CURRENT_TIMESTAMP) - 1 AND
                     YEAR(CURRENT_TIMESTAMP) THEN
                 1
                ELSE
                 0
              END AS YEAR_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              --GS_DECRYPT(T.RMB_COST_AMT,'''|| F_KEYSTR ||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT
              T.RMB_COST_AMT  AS RMB_COST_AMT
        FROM '||V_FROM_TABLE2 ||' T
        WHERE T.OVERSEA_FLAG = ''G''  -- 第二步 从总地区(全球)和总BG(集团)的角度去打TOP标签
		  AND T.LV0_PROD_LIST_CODE = ''GR''
          AND T.REVIEW_ITEM_FLAG = 0 
		'; 

    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入分视角收敛ITEM的年发货额, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

      --汇总数据插入汇总分视角收敛ITEM的年发货额
      V_SQL := 
      'INSERT INTO SUM_COST_TEMP
        ( CALIBER_FLAG
         ,VIEW_FLAG
         ,PERIOD_YEAR
         ,YEAR_FLAG
         ,LV0_PROD_RND_TEAM_CODE
         ,LV0_PROD_RD_TEAM_CN_NAME
         ,LV1_PROD_RND_TEAM_CODE
         ,LV1_PROD_RD_TEAM_CN_NAME
         ,LV2_PROD_RND_TEAM_CODE
         ,LV2_PROD_RD_TEAM_CN_NAME,'||
          V_LV3_PROD_RND_TEAM_CODE ||
          V_LV3_PROD_RD_TEAM_CN_NAME ||
          V_L1_NAME ||
          V_L2_NAME ||
          V_DIMENSION_CODE ||
          V_DIMENSION_CN_NAME ||
          V_DIMENSION_EN_NAME||
          V_DIMENSION_SUBCATEGORY_CODE ||
          V_DIMENSION_SUBCATEGORY_CN_NAME ||
          V_DIMENSION_SUBCATEGORY_EN_NAME||
          V_DIMENSION_SUB_DETAIL_CODE ||
          V_DIMENSION_SUB_DETAIL_CN_NAME ||
          V_DIMENSION_SUB_DETAIL_EN_NAME ||
          V_SPART_CODE ||
          V_SPART_CN_NAME ||'
          L3_CEG_CODE
         ,L3_CEG_CN_NAME
         ,L3_CEG_SHORT_CN_NAME
         ,L4_CEG_CODE
         ,L4_CEG_CN_NAME
         ,L4_CEG_SHORT_CN_NAME
         ,CATEGORY_CODE
         ,CATEGORY_CN_NAME
         ,ITEM_CODE
         ,ITEM_CN_NAME
         ,RMB_COST_AMT
        )
      --分视角收敛ITEM的年发货额
      SELECT  T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.YEAR_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              SUM(T.RMB_COST_AMT) AS RMB_COST_AMT
        FROM RMB_COST_TEMP T
       GROUP BY  T.CALIBER_FLAG,
                 T.VIEW_FLAG,
                 T.YEAR_FLAG,
                 T.PERIOD_YEAR,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_LV3_PROD_RND_TEAM_CODE ||
                 V_LV3_PROD_RD_TEAM_CN_NAME ||
                 V_L1_NAME ||
                 V_L2_NAME ||
                 V_DIMENSION_CODE ||
                 V_DIMENSION_CN_NAME ||
                 V_DIMENSION_EN_NAME||
                 V_DIMENSION_SUBCATEGORY_CODE ||
                 V_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_DIMENSION_SUB_DETAIL_CODE ||
                 V_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_DIMENSION_SUB_DETAIL_EN_NAME ||
                 V_SPART_CODE ||
                 V_SPART_CN_NAME ||'
                 T.L3_CEG_CODE,
                 T.L3_CEG_CN_NAME,
                 T.L3_CEG_SHORT_CN_NAME,
                 T.L4_CEG_CODE,
                 T.L4_CEG_CN_NAME,
                 T.L4_CEG_SHORT_CN_NAME,
                 T.CATEGORY_CODE,
                 T.CATEGORY_CN_NAME,
                 T.ITEM_CODE,
                 T.ITEM_CN_NAME ';

    EXECUTE IMMEDIATE V_SQL;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '汇总数据插入汇总分视角收敛ITEM的年发货额, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');



  V_SQL :=
  'INSERT INTO DM_FOC_TOP_ITEM_INFO_TEMP
    (VERSION_ID,
     VERSION_NAME,
     VIEW_FLAG,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
     V_SPART_CODE ||
     V_SPART_CN_NAME ||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_WEIGHT_RATE,
     DOUBLE_FLAG,
     CALIBER_FLAG)
    WITH TOP_CATE_TEMP AS
     (
      --取最新版本号的TOP品类
      SELECT DISTINCT  T.LV0_PROD_RND_TEAM_CODE,
                       T.LV0_PROD_RD_TEAM_CN_NAME,
                       T.LV1_PROD_RND_TEAM_CODE,
                       T.LV1_PROD_RD_TEAM_CN_NAME,
                       T.LV2_PROD_RND_TEAM_CODE,
                       T.LV2_PROD_RD_TEAM_CN_NAME,'||
                       V_LV3_PROD_RND_TEAM_CODE ||
                       V_LV3_PROD_RD_TEAM_CN_NAME ||
                       V_L1_NAME ||
                       V_L2_NAME ||
                       V_DIMENSION_CODE ||
                       V_DIMENSION_CN_NAME ||
                       V_DIMENSION_EN_NAME||
                       V_DIMENSION_SUBCATEGORY_CODE ||
                       V_DIMENSION_SUBCATEGORY_CN_NAME ||
                       V_DIMENSION_SUBCATEGORY_EN_NAME||
                       V_DIMENSION_SUB_DETAIL_CODE ||
                       V_DIMENSION_SUB_DETAIL_CN_NAME ||
                       V_DIMENSION_SUB_DETAIL_EN_NAME ||
                       V_SPART_CODE ||
                       V_SPART_CN_NAME ||'
                       T.TOP_L3_CEG_CODE          AS L3_CEG_CODE,
                       T.TOP_L3_CEG_CN_NAME       AS L3_CEG_CN_NAME,
                       T.TOP_L3_CEG_SHORT_CN_NAME AS L3_CEG_SHORT_CN_NAME,
                       T.TOP_L4_CEG_CODE          AS L4_CEG_CODE,
                       T.TOP_L4_CEG_CN_NAME       AS L4_CEG_CN_NAME,
                       T.TOP_L4_CEG_SHORT_CN_NAME AS L4_CEG_SHORT_CN_NAME,
                       T.TOP_CATEGORY_CODE        AS CATEGORY_CODE,
                       T.TOP_CATEGORY_CN_NAME     AS CATEGORY_CN_NAME,
                       T.VIEW_FLAG,
                       T.WEIGHT_RATE,
                       T.CALIBER_FLAG
        FROM '||V_FROM_TABLE1 ||' T
       WHERE T.IS_TOP_FLAG = ''Y''
         AND T.OVERSEA_FLAG = ''G'' -- 第二步 从总地区(全球)和总BG(集团)的角度去打TOP标签
		 AND T.LV0_PROD_LIST_CODE = ''GR''
         AND T.VERSION_ID = '|| V_TOP_CATE_ID ||'),
    
    TOP_ITEM_TEMP AS
     (
      --分视角下筛选TOP品类的ITEM
      SELECT  A.CALIBER_FLAG,
              A.VIEW_FLAG,
              TO_CHAR(A.PERIOD_YEAR) AS PERIOD_YEAR,
              A.YEAR_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME ||
              V_IN_L2_NAME ||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_IN_SPART_CODE ||
              V_IN_SPART_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              A.ITEM_CODE,
              A.ITEM_CN_NAME,
              A.RMB_COST_AMT,
              SUM(A.RMB_COST_AMT) OVER(PARTITION BY A.CALIBER_FLAG, A.VIEW_FLAG, A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE,'|| V_IN_LV3_PROD_RND_TEAM_CODE || V_IN_L1_NAME || V_IN_L2_NAME || V_IN_DIMENSION_CODE || V_IN_DIMENSION_SUBCATEGORY_CODE || V_IN_DIMENSION_SUB_DETAIL_CODE || V_IN_SPART_CODE ||'  A.L3_CEG_CODE, A.L4_CEG_CODE, A.CATEGORY_CODE, A.PERIOD_YEAR) AS TOTAL_CATE_AMT,
              B.CATEGORY_CODE AS PARENT_CODE,
              B.WEIGHT_RATE AS PARENT_WEIGHT_RATE
        FROM SUM_COST_TEMP A
       INNER JOIN TOP_CATE_TEMP B
          ON A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
         AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) =
             NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
         AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) =
             NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
       '||V_INSERT_LV3_PROD_RND_TEAM_CODE
        ||V_INSERT_L1_NAME
        ||V_INSERT_L2_NAME
        ||V_INSERT_DIMENSION_CODE
        ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
        ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
        ||V_INSERT_SPART_CODE||'
         AND A.L3_CEG_CODE = B.L3_CEG_CODE
         AND A.L4_CEG_CODE = B.L4_CEG_CODE
         AND A.CATEGORY_CODE = B.CATEGORY_CODE
         AND A.VIEW_FLAG = B.VIEW_FLAG
         AND A.CALIBER_FLAG = B.CALIBER_FLAG)
    
    --分视角计算各年的权重值
    SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
           '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           M.VIEW_FLAG,
           DECODE(M.PERIOD_YEAR,
                  YEAR(CURRENT_TIMESTAMP),
                  M.PERIOD_YEAR || ''YTD'',
                  M.PERIOD_YEAR) AS PERIOD_YEAR,
           M.LV0_PROD_RND_TEAM_CODE,
           M.LV0_PROD_RD_TEAM_CN_NAME,
           M.LV1_PROD_RND_TEAM_CODE,
           M.LV1_PROD_RD_TEAM_CN_NAME,
           M.LV2_PROD_RND_TEAM_CODE,
           M.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
           V_SPART_CODE ||
           V_SPART_CN_NAME ||'
           M.L3_CEG_CODE,
           M.L3_CEG_CN_NAME,
           M.L3_CEG_SHORT_CN_NAME,
           M.L4_CEG_CODE,
           M.L4_CEG_CN_NAME,
           M.L4_CEG_SHORT_CN_NAME,
           M.CATEGORY_CODE,
           M.CATEGORY_CN_NAME,
           M.ITEM_CODE,
           M.ITEM_CN_NAME,
           M.RMB_COST_AMT / NULLIF(M.TOTAL_CATE_AMT,0) AS WEIGHT_RATE,
           M.PARENT_CODE,
           M.PARENT_WEIGHT_RATE,
           ''N'' AS DOUBLE_FLAG,
           M.CALIBER_FLAG
      FROM TOP_ITEM_TEMP M
    UNION ALL
    --分视角计算上一年到今年YTD的TOP品类下item的权重值
    SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
           '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           S.VIEW_FLAG,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' ||
           YEAR(CURRENT_TIMESTAMP) || ''YTD'' AS PERIOD_YEAR,
           S.LV0_PROD_RND_TEAM_CODE,
           S.LV0_PROD_RD_TEAM_CN_NAME,
           S.LV1_PROD_RND_TEAM_CODE,
           S.LV1_PROD_RD_TEAM_CN_NAME,
           S.LV2_PROD_RND_TEAM_CODE,
           S.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
           V_SPART_CODE ||
           V_SPART_CN_NAME ||'
           S.L3_CEG_CODE,
           S.L3_CEG_CN_NAME,
           S.L3_CEG_SHORT_CN_NAME,
           S.L4_CEG_CODE,
           S.L4_CEG_CN_NAME,
           S.L4_CEG_SHORT_CN_NAME,
           S.CATEGORY_CODE,
           S.CATEGORY_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_CN_NAME,
           S.RMB_COST_AMT / NULLIF(S.TOTAL_CATE_AMT,0) AS WEIGHT_RATE,
           S.PARENT_CODE,
           S.PARENT_WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG,
           S.CALIBER_FLAG
      FROM (SELECT P.CALIBER_FLAG,
                   P.VIEW_FLAG,
                   P.LV0_PROD_RND_TEAM_CODE,
                   P.LV0_PROD_RD_TEAM_CN_NAME,
                   P.LV1_PROD_RND_TEAM_CODE,
                   P.LV1_PROD_RD_TEAM_CN_NAME,
                   P.LV2_PROD_RND_TEAM_CODE,
                   P.LV2_PROD_RD_TEAM_CN_NAME,'||
                   V_LV3_PROD_RND_TEAM_CODE ||
                   V_LV3_PROD_RD_TEAM_CN_NAME ||
                   V_L1_NAME ||
                   V_L2_NAME ||
                   V_DIMENSION_CODE ||
                   V_DIMENSION_CN_NAME ||
                   V_DIMENSION_EN_NAME||
                   V_DIMENSION_SUBCATEGORY_CODE ||
                   V_DIMENSION_SUBCATEGORY_CN_NAME ||
                   V_DIMENSION_SUBCATEGORY_EN_NAME||
                   V_DIMENSION_SUB_DETAIL_CODE ||
                   V_DIMENSION_SUB_DETAIL_CN_NAME ||
                   V_DIMENSION_SUB_DETAIL_EN_NAME ||
                   V_SPART_CODE ||
                   V_SPART_CN_NAME ||'
                   P.L3_CEG_CODE,
                   P.L3_CEG_CN_NAME,
                   P.L3_CEG_SHORT_CN_NAME,
                   P.L4_CEG_CODE,
                   P.L4_CEG_CN_NAME,
                   P.L4_CEG_SHORT_CN_NAME,
                   P.CATEGORY_CODE,
                   P.CATEGORY_CN_NAME,
                   P.ITEM_CODE,
                   P.ITEM_CN_NAME,
                   SUM(P.RMB_COST_AMT) AS RMB_COST_AMT,
                   SUM(SUM(P.RMB_COST_AMT)) OVER(PARTITION BY P.CALIBER_FLAG, P.VIEW_FLAG, P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' P.L3_CEG_CODE, P.L4_CEG_CODE, P.CATEGORY_CODE) AS TOTAL_CATE_AMT,
                   P.PARENT_CODE AS PARENT_CODE,
                   MIN(P.PARENT_WEIGHT_RATE) AS PARENT_WEIGHT_RATE
              FROM TOP_ITEM_TEMP P
             WHERE P.YEAR_FLAG = 1
             GROUP BY P.CALIBER_FLAG,
                      P.VIEW_FLAG,
                      P.LV0_PROD_RND_TEAM_CODE,
                      P.LV0_PROD_RD_TEAM_CN_NAME,
                      P.LV1_PROD_RND_TEAM_CODE,
                      P.LV1_PROD_RD_TEAM_CN_NAME,
                      P.LV2_PROD_RND_TEAM_CODE,
                      P.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_LV3_PROD_RND_TEAM_CODE ||
                      V_LV3_PROD_RD_TEAM_CN_NAME ||
                      V_L1_NAME ||
                      V_L2_NAME ||
                      V_DIMENSION_CODE ||
                      V_DIMENSION_CN_NAME ||
                      V_DIMENSION_EN_NAME||
                      V_DIMENSION_SUBCATEGORY_CODE ||
                      V_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_DIMENSION_SUB_DETAIL_CODE ||
                      V_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_DIMENSION_SUB_DETAIL_EN_NAME ||
                      V_SPART_CODE ||
                      V_SPART_CN_NAME ||'
                      P.L3_CEG_CODE,
                      P.L3_CEG_CN_NAME,
                      P.L3_CEG_SHORT_CN_NAME,
                      P.L4_CEG_CODE,
                      P.L4_CEG_CN_NAME,
                      P.L4_CEG_SHORT_CN_NAME,
                      P.CATEGORY_CODE,
                      P.CATEGORY_CN_NAME,
                      P.ITEM_CODE,
                      P.ITEM_CN_NAME,
                      P.PARENT_CODE) S';

    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角各年TOP规格品的权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
  --第一步往TOP规格品清单插数, 打上前95%TOP的标识
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     VERSION_NAME,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
     V_SPART_CODE ||
     V_SPART_CN_NAME ||'
     TOP_L3_CEG_CODE,
     TOP_L3_CEG_CN_NAME,
     TOP_L3_CEG_SHORT_CN_NAME,
     TOP_L4_CEG_CODE,
     TOP_L4_CEG_CN_NAME,
     TOP_L4_CEG_SHORT_CN_NAME,
     TOP_CATEGORY_CODE,
     TOP_CATEGORY_CN_NAME,
     TOP_ITEM_CODE,
     TOP_ITEM_CN_NAME,
     WEIGHT_RATE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     IS_TOP_FLAG,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
    WITH BASE_FLAG_TEMP AS
     (
      --如果品类下所有ITEM的权重值相同, 或第二位权重值到末尾权重值相同, 则最大排序值标记为1或2, 反之为一般数据情况
      SELECT  T.CALIBER_FLAG,
              T.VERSION_ID,
              T.VERSION_NAME,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.WEIGHT_RATE,
              T.DRANK_SORT,
              MAX(T.DRANK_SORT) OVER(PARTITION BY T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' T.L3_CEG_CODE, T.L4_CEG_CODE, T.CATEGORY_CODE) AS MAX_DRANK_SORT
        FROM (SELECT P.VERSION_ID,
                      P.VERSION_NAME,
                      P.VIEW_FLAG,
                      P.PERIOD_YEAR,
                      P.LV0_PROD_RND_TEAM_CODE,
                      P.LV0_PROD_RD_TEAM_CN_NAME,
                      P.LV1_PROD_RND_TEAM_CODE,
                      P.LV1_PROD_RD_TEAM_CN_NAME,
                      P.LV2_PROD_RND_TEAM_CODE,
                      P.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_LV3_PROD_RND_TEAM_CODE ||
                      V_LV3_PROD_RD_TEAM_CN_NAME ||
                      V_L1_NAME ||
                      V_L2_NAME ||
                      V_DIMENSION_CODE ||
                      V_DIMENSION_CN_NAME ||
                      V_DIMENSION_EN_NAME||
                      V_DIMENSION_SUBCATEGORY_CODE ||
                      V_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_DIMENSION_SUB_DETAIL_CODE ||
                      V_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_DIMENSION_SUB_DETAIL_EN_NAME ||
                      V_SPART_CODE ||
                      V_SPART_CN_NAME ||'
                      P.L3_CEG_CODE,
                      P.L3_CEG_CN_NAME,
                      P.L3_CEG_SHORT_CN_NAME,
                      P.L4_CEG_CODE,
                      P.L4_CEG_CN_NAME,
                      P.L4_CEG_SHORT_CN_NAME,
                      P.CATEGORY_CODE,
                      P.CATEGORY_CN_NAME,
                      P.ITEM_CODE,
                      P.ITEM_CN_NAME,
                      P.WEIGHT_RATE,
                      P.CALIBER_FLAG,
                      DENSE_RANK() OVER(PARTITION BY P.CALIBER_FLAG, P.VIEW_FLAG, P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' P.L3_CEG_CODE, P.L4_CEG_CODE, P.CATEGORY_CODE ORDER BY P.WEIGHT_RATE DESC) AS DRANK_SORT
                 FROM DM_FOC_TOP_ITEM_INFO_TEMP P
                WHERE P.DOUBLE_FLAG = ''Y'') T),
        
    NORMAL_DATA_TEMP AS
     (
      --筛选一般数据情况: 最大排序值标识大于2的数据, 并带出ROW_NUMBER排序字段
      SELECT T.VERSION_ID,
              T.VERSION_NAME,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.WEIGHT_RATE,
              T.CALIBER_FLAG,
              ROW_NUMBER() OVER(PARTITION BY T.CALIBER_FLAG, T.VIEW_FLAG, T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' T.L3_CEG_CODE, T.L4_CEG_CODE, T.CATEGORY_CODE ORDER BY T.WEIGHT_RATE DESC) AS RRANK_SORT,
              T.DRANK_SORT,
              T.MAX_DRANK_SORT
        FROM BASE_FLAG_TEMP T
       WHERE T.MAX_DRANK_SORT > 2),
        
    FLAG_DATA_TEMP AS
     (
      --对NORMAL_DATA_TEMP临时表打上辅助列标识:(ACCU_WEIGHT_RATE->ACCU_FLAG->CAL_FLAG)
      SELECT S.VERSION_ID,
              S.VERSION_NAME,
              S.VIEW_FLAG,
              S.PERIOD_YEAR,
              S.LV0_PROD_RND_TEAM_CODE,
              S.LV0_PROD_RD_TEAM_CN_NAME,
              S.LV1_PROD_RND_TEAM_CODE,
              S.LV1_PROD_RD_TEAM_CN_NAME,
              S.LV2_PROD_RND_TEAM_CODE,
              S.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              S.L3_CEG_CODE,
              S.L3_CEG_CN_NAME,
              S.L3_CEG_SHORT_CN_NAME,
              S.L4_CEG_CODE,
              S.L4_CEG_CN_NAME,
              S.L4_CEG_SHORT_CN_NAME,
              S.CATEGORY_CODE,
              S.CATEGORY_CN_NAME,
              S.ITEM_CODE,
              S.ITEM_CN_NAME,
              S.WEIGHT_RATE,
              S.ACCU_WEIGHT_RATE,
              S.ACCU_FLAG,
              S.CALIBER_FLAG,
              SUM(S.ACCU_FLAG) OVER(PARTITION BY S.CALIBER_FLAG, S.VIEW_FLAG, S.LV0_PROD_RND_TEAM_CODE, S.LV1_PROD_RND_TEAM_CODE, S.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' S.L3_CEG_CODE, S.L4_CEG_CODE, S.CATEGORY_CODE ORDER BY S.RRANK_SORT) AS CAL_FLAG --根据ROW_NUMBER累计求和ACCU_FLAG标识
        FROM (SELECT R.VERSION_ID,
                      R.VERSION_NAME,
                      R.VIEW_FLAG,
                      R.PERIOD_YEAR,
                      R.LV0_PROD_RND_TEAM_CODE,
                      R.LV0_PROD_RD_TEAM_CN_NAME,
                      R.LV1_PROD_RND_TEAM_CODE,
                      R.LV1_PROD_RD_TEAM_CN_NAME,
                      R.LV2_PROD_RND_TEAM_CODE,
                      R.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_LV3_PROD_RND_TEAM_CODE ||
                      V_LV3_PROD_RD_TEAM_CN_NAME ||
                      V_L1_NAME ||
                      V_L2_NAME ||
                      V_DIMENSION_CODE ||
                      V_DIMENSION_CN_NAME ||
                      V_DIMENSION_EN_NAME||
                      V_DIMENSION_SUBCATEGORY_CODE ||
                      V_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_DIMENSION_SUB_DETAIL_CODE ||
                      V_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_DIMENSION_SUB_DETAIL_EN_NAME ||
                      V_SPART_CODE ||
                      V_SPART_CN_NAME ||'
                      R.L3_CEG_CODE,
                      R.L3_CEG_CN_NAME,
                      R.L3_CEG_SHORT_CN_NAME,
                      R.L4_CEG_CODE,
                      R.L4_CEG_CN_NAME,
                      R.L4_CEG_SHORT_CN_NAME,
                      R.CATEGORY_CODE,
                      R.CATEGORY_CN_NAME,
                      R.ITEM_CODE,
                      R.ITEM_CN_NAME,
                      R.WEIGHT_RATE,
                      R.ACCU_WEIGHT_RATE,
                      R.RRANK_SORT,
                      CASE
                        WHEN R.ACCU_WEIGHT_RATE >= 0.95 THEN
                         1
                        ELSE
                         0
                      END AS ACCU_FLAG, --累计权重大于95,标识为1,反之为0
                      R.CALIBER_FLAG
                 FROM (SELECT Z.VERSION_ID,
                              Z.VERSION_NAME,
                              Z.VIEW_FLAG,
                              Z.PERIOD_YEAR,
                              Z.LV0_PROD_RND_TEAM_CODE,
                              Z.LV0_PROD_RD_TEAM_CN_NAME,
                              Z.LV1_PROD_RND_TEAM_CODE,
                              Z.LV1_PROD_RD_TEAM_CN_NAME,
                              Z.LV2_PROD_RND_TEAM_CODE,
                              Z.LV2_PROD_RD_TEAM_CN_NAME,'||
                              V_LV3_PROD_RND_TEAM_CODE ||
                              V_LV3_PROD_RD_TEAM_CN_NAME ||
                              V_L1_NAME ||
                              V_L2_NAME ||
                              V_DIMENSION_CODE ||
                              V_DIMENSION_CN_NAME ||
                              V_DIMENSION_EN_NAME||
                              V_DIMENSION_SUBCATEGORY_CODE ||
                              V_DIMENSION_SUBCATEGORY_CN_NAME ||
                              V_DIMENSION_SUBCATEGORY_EN_NAME||
                              V_DIMENSION_SUB_DETAIL_CODE ||
                              V_DIMENSION_SUB_DETAIL_CN_NAME ||
                              V_DIMENSION_SUB_DETAIL_EN_NAME ||
                              V_SPART_CODE ||
                              V_SPART_CN_NAME ||'
                              Z.L3_CEG_CODE,
                              Z.L3_CEG_CN_NAME,
                              Z.L3_CEG_SHORT_CN_NAME,
                              Z.L4_CEG_CODE,
                              Z.L4_CEG_CN_NAME,
                              Z.L4_CEG_SHORT_CN_NAME,
                              Z.CATEGORY_CODE,
                              Z.CATEGORY_CN_NAME,
                              Z.ITEM_CODE,
                              Z.ITEM_CN_NAME,
                              Z.WEIGHT_RATE,
                              SUM(Z.WEIGHT_RATE) OVER(PARTITION BY Z.CALIBER_FLAG, Z.VIEW_FLAG, Z.LV0_PROD_RND_TEAM_CODE, Z.LV1_PROD_RND_TEAM_CODE, Z.LV2_PROD_RND_TEAM_CODE,'|| V_LV3_PROD_RND_TEAM_CODE || V_L1_NAME || V_L2_NAME || V_DIMENSION_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_SPART_CODE ||' Z.L3_CEG_CODE, Z.L4_CEG_CODE, Z.CATEGORY_CODE ORDER BY Z.RRANK_SORT) AS ACCU_WEIGHT_RATE, --根据ROW_NUMBER累计求和权重值
                              Z.RRANK_SORT,
                              Z.CALIBER_FLAG
                         FROM NORMAL_DATA_TEMP Z) R) S),
    TOP_DATA_TEMP AS
     ( 
      --TOP品类的数据
      SELECT A.VERSION_ID,
              A.VERSION_NAME,
              A.VIEW_FLAG,
              A.PERIOD_YEAR,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              A.ITEM_CODE,
              A.ITEM_CN_NAME,
              A.WEIGHT_RATE,
              A.CALIBER_FLAG
        FROM BASE_FLAG_TEMP A
       WHERE MAX_DRANK_SORT <= 2
      UNION ALL
      SELECT B.VERSION_ID,
              B.VERSION_NAME,
              B.VIEW_FLAG,
              B.PERIOD_YEAR,
              B.LV0_PROD_RND_TEAM_CODE,
              B.LV0_PROD_RD_TEAM_CN_NAME,
              B.LV1_PROD_RND_TEAM_CODE,
              B.LV1_PROD_RD_TEAM_CN_NAME,
              B.LV2_PROD_RND_TEAM_CODE,
              B.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||'
              B.L3_CEG_CODE,
              B.L3_CEG_CN_NAME,
              B.L3_CEG_SHORT_CN_NAME,
              B.L4_CEG_CODE,
              B.L4_CEG_CN_NAME,
              B.L4_CEG_SHORT_CN_NAME,
              B.CATEGORY_CODE,
              B.CATEGORY_CN_NAME,
              B.ITEM_CODE,
              B.ITEM_CN_NAME,
              B.WEIGHT_RATE,
              B.CALIBER_FLAG
        FROM FLAG_DATA_TEMP B
       WHERE CAL_FLAG <= 2)
        
    --临时表左关联TOP规格品临时表, 打上是否为TOP类标识
    SELECT A.VERSION_ID,
           A.VERSION_NAME,
           A.PERIOD_YEAR,
           A.LV0_PROD_RND_TEAM_CODE,
           A.LV0_PROD_RD_TEAM_CN_NAME,
           A.LV1_PROD_RND_TEAM_CODE,
           A.LV1_PROD_RD_TEAM_CN_NAME,
           A.LV2_PROD_RND_TEAM_CODE,
           A.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
           V_IN_L1_NAME ||
           V_IN_L2_NAME ||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
           V_IN_SPART_CODE ||
           V_IN_SPART_CN_NAME ||'
           A.L3_CEG_CODE,
           A.L3_CEG_CN_NAME,
           A.L3_CEG_SHORT_CN_NAME,
           A.L4_CEG_CODE,
           A.L4_CEG_CN_NAME,
           A.L4_CEG_SHORT_CN_NAME,
           A.CATEGORY_CODE,
           A.CATEGORY_CN_NAME,
           A.ITEM_CODE,
           A.ITEM_CN_NAME,
           A.WEIGHT_RATE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           A.VIEW_FLAG,
           DECODE(B.ITEM_CODE, NULL, ''N'', ''Y'') AS IS_TOP_FLAG,
           A.DOUBLE_FLAG,
           A.CALIBER_FLAG,
           A.OVERSEA_FLAG,
           A.LV0_PROD_LIST_CODE,
           A.LV0_PROD_LIST_CN_NAME,
           A.LV0_PROD_LIST_EN_NAME
      FROM DM_FOC_WEIGHT_ITEM_INFO_TEMP A
      LEFT JOIN TOP_DATA_TEMP B
        ON A.VIEW_FLAG = B.VIEW_FLAG
       AND A.PERIOD_YEAR = B.PERIOD_YEAR
       AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
       AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) = NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
       AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) = NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
       '||V_INSERT_LV3_PROD_RND_TEAM_CODE
        ||V_INSERT_L1_NAME
        ||V_INSERT_L2_NAME
        ||V_INSERT_DIMENSION_CODE
        ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
        ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
        ||V_INSERT_SPART_CODE||'
       AND A.L3_CEG_CODE = B.L3_CEG_CODE
       AND A.L4_CEG_CODE = B.L4_CEG_CODE
       AND A.CATEGORY_CODE = B.CATEGORY_CODE
       AND A.ITEM_CODE = B.ITEM_CODE
       AND A.CALIBER_FLAG = B.CALIBER_FLAG';

    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往TOP规格品表插数: 前95%的ITEM打上TOP标识(兜底逻辑), 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
   
   
   
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

