-- Name: f_dm_foi_energy_point_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_point_amp(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建人  ：罗若文
  背景描述：点降图数据计算
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_POINT_AMP()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_POINT_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
  V_BASE_PERIOD BIGINT :=  CAST(YEAR(CURRENT_TIMESTAMP)-3||'01' AS BIGINT);  -- 基期月份
  V_FROM_TABLE1 VARCHAR(100);
  V_FROM_TABLE2 VARCHAR(100);
  V_TO_TABLE    VARCHAR(100);
  V_PARAMETER   VARCHAR(30);
  V_AMT_PARA    VARCHAR(30);
  V_INSERT_ID   VARCHAR(10);
  V_ID   TEXT ;
  V_JOIN_PARAMETER  TEXT;
  V_SQL  TEXT;
  V_INSERT_SQL TEXT ;
  V_CURRENT_FLAG INT;
  
  
  BEGIN
  
  
  	  --判断入参是ICT还是数字能源
  IF f_caliber_flag = 'I' THEN 
	V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T';
	V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T';
	V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_T';
	V_INSERT_ID := ' ID,';
	V_ID := ' FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_S.NEXTVAL AS ID, ';
	V_PARAMETER := ' CONTINUITY_TYPE ';
	V_AMT_PARA := 'AVG_RECEIVE_AMT ';
	V_JOIN_PARAMETER := ' AND NVL(T1.CONTINUITY_TYPE,''SNULL'') = NVL(T2.CONTINUITY_TYPE,''SNULL'') ';
	IF F_VERSION_ID IS NULL THEN 
   SELECT VALUE 
     INTO V_VERSION_ID
   FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
   WHERE ENABLE_FLAG = 'Y'
   AND UPPER(PARA_NAME) = 'VERSION_ID-ITEM';
 ELSE V_VERSION_ID := F_VERSION_ID;
 END IF;
	
	--判断入参是ICT还是数字能源
  ELSIF f_caliber_flag = 'E' THEN 
	V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T';
	V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T';
	V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_POINT_AMP_T';
	V_INSERT_ID := '';
	V_ID := '';
	V_PARAMETER := ' GROUP_PUR_FLAG ';
	V_AMT_PARA := 'AVG_RECEIVE_AMT ';
	V_JOIN_PARAMETER := ' AND NVL(T1.GROUP_PUR_FLAG,''SNULL'') = NVL(T2.GROUP_PUR_FLAG,''SNULL'') ';
	
	
	-- 取到对应的规格品版本号
	  IF F_VERSION_ID IS  NULL  THEN 
	  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
    WHERE
        VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ITEM';
		
    -- FLAG 不等于0，说明已有版本号，沿用        
		IF V_CURRENT_FLAG <> 0 THEN 
			SELECT VERSION_ID INTO V_VERSION_ID
			FROM
				FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T 
				WHERE
					VERSION = TO_CHAR(CURRENT_DATE, 'YYYYMM')||'-ITEM-Auto'
					AND DEL_FLAG = 'N'
					AND STATUS = 1
					AND UPPER(DATA_TYPE) = 'ITEM';
						
		ELSE 
			RETURN '没有找到版本号';
		END IF;
	  ELSE  
				V_VERSION_ID = f_version_id;
		END IF;
	
ELSE 
	RETURN '输入的入参有误';
END IF;
  
  
  X_RESULT_STATUS = '1';
  
  
    --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 删除本版本数据
   V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
   EXECUTE IMMEDIATE V_SQL;
   
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除点降表：'||V_TO_TABLE||' 版本号为：'||V_VERSION_ID||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 点降结果数据计算
 V_SQL := ' INSERT INTO '||V_TO_TABLE|| '( 
                '||V_INSERT_ID||'
                VERSION_ID,
                YEAR,
                PERIOD_ID,
                BASE_PERIOD_ID,
				CATEGORY_CODE,
                CATEGORY_NAME,
				L4_CEG_CODE,
                L4_CEG_SHORT_CN_NAME,
                L4_CEG_CN_NAME,
                L3_CEG_CODE,
                L3_CEG_SHORT_CN_NAME,
                L3_CEG_CN_NAME,
                L2_CEG_CODE,
                L2_CEG_CN_NAME,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                POINT_AMP,
                PARENT_CODE,
				PARENT_CN_NAME ,				
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
				'||V_PARAMETER||'
                ) 
 WITH FOI_COMPUTE_TMP AS(
 
 -- ITEM层级的月均本数据  
   SELECT PERIOD_ID,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          '||V_AMT_PARA||' AS COMPUTE_VALUE,  -- 作为计算值
          CATEGORY_CODE,
          CATEGORY_NAME,
          L4_CEG_CODE,
          L4_CEG_SHORT_CN_NAME,
          L4_CEG_CN_NAME,
          L3_CEG_CODE,
          L3_CEG_SHORT_CN_NAME,
          L3_CEG_CN_NAME,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          PARENT_CODE,
		  PARENT_CN_NAME,
		  NULL AS '||V_PARAMETER||'
       FROM '||V_FROM_TABLE1||'
       WHERE VERSION_ID = '||V_VERSION_ID||
       ' AND GROUP_LEVEL = ''ITEM''
   UNION ALL
 -- 除ITEM层级之外其余层级的指数数据
   SELECT PERIOD_ID,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          PRICE_INDEX AS COMPUTE_VALUE,  -- 作为计算值
          NULL AS CATEGORY_CODE,
          NULL AS CATEGORY_NAME,
          L4_CEG_CODE,
          L4_CEG_SHORT_CN_NAME,
          L4_CEG_CN_NAME,
          L3_CEG_CODE,
          L3_CEG_SHORT_CN_NAME,
          L3_CEG_CN_NAME,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          PARENT_CODE,
		  PARENT_CN_NAME,
		  '||V_PARAMETER||'
       FROM '||V_FROM_TABLE2||'
       WHERE VERSION_ID = '||V_VERSION_ID||'
       AND BASE_PERIOD_ID = CAST(YEAR(CURRENT_TIMESTAMP)-3||''01'' AS BIGINT)  
       AND GROUP_LEVEL IN (''CATEGORY'',''LV4'',''LV3'',''LV2'')  -- 限制层级为：品类/模块/专家团/ICT
         )
   SELECT 
		 '||V_ID||
          V_VERSION_ID||' AS VERSION_ID,
          SUBSTR(T1.PERIOD_ID,1,4) AS YEAR,
          T1.PERIOD_ID,
          '||V_BASE_PERIOD||' AS BASE_PERIOD_ID,
		  T1.CATEGORY_CODE,
          T1.CATEGORY_NAME,
		  T1.L4_CEG_CODE,
          T1.L4_CEG_SHORT_CN_NAME,
          T1.L4_CEG_CN_NAME,
          T1.L3_CEG_CODE,
          T1.L3_CEG_SHORT_CN_NAME,
          T1.L3_CEG_CN_NAME,
          T1.L2_CEG_CODE,
          T1.L2_CEG_CN_NAME,
          T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
          T1.GROUP_LEVEL, 
          NVL((T1.COMPUTE_VALUE/NULLIF(T2.BASE_COMPUTE_VALUE,0)-1)*100,0) AS POINT_AMP ,  -- 计算点降结果值
          T1.PARENT_CODE,
		  T1.PARENT_CN_NAME,
          -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
		 T1.'||V_PARAMETER||'
       FROM FOI_COMPUTE_TMP T1
       LEFT JOIN (
                  SELECT PERIOD_ID,
                         GROUP_CODE,
                         GROUP_LEVEL,
                         COMPUTE_VALUE AS BASE_COMPUTE_VALUE,  -- 取默认基期的数据
                         PARENT_CODE,
						 PARENT_CN_NAME,
                         '||V_PARAMETER||'
                      FROM FOI_COMPUTE_TMP 
                      WHERE PERIOD_ID = CAST(YEAR(CURRENT_TIMESTAMP)-3||''01'' AS BIGINT)  
                 ) T2
       ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       AND NVL(T1.PARENT_CODE,''SNULL0'') = NVL(T2.PARENT_CODE,''SNULL0'')
	   '||V_JOIN_PARAMETER;
	   EXECUTE IMMEDIATE V_SQL;

   
     --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||' 的权重数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');     
     
     --收集统计信息
 V_SQL := ' ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

END$$
/

