-- Name: f_dm_foi_item_fcst_sum_append_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_item_fcst_sum_append_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建人  ：罗若文
修改人：唐钦
最后修改时间：2024年4月16日09点37分
背景；ICT/数字能源预测数补齐
参数描述：x_result_status ：是否成功
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOI_ITEM_FCST_SUM_APPEND_T()
*/

DECLARE
  V_SP_NAME VARCHAR2(500):= 'FIN_DM_OPT_FOI.F_DM_FOI_ITEM_FCST_SUM_APPEND_T';
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(100);
  V_FROM1_TABLE VARCHAR(100);
  V_JOIN_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_VERSION_ID INT;
  V_CURRENT_FLAG INT;
  V_STEP_NUM  INT := 0;
  V_TMP_TABLE VARCHAR(50);
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(200);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_VERSION_TABLE VARCHAR(200);
  V_REL_CONDITION VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS:= 1;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM + 1,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --判断入参是ICT还是数字能源
  IF F_CALIBER_FLAG = 'I' THEN 
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T';
    V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T';
    V_TMP_TABLE := 'CROSS_FLAG_TMP';
  ELSIF F_CALIBER_FLAG = 'E' THEN 
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_FCST_SUM_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T';
    V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T';
    V_TMP_TABLE := 'ENERGY_CROSS_FLAG_TMP';
  ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') THEN  -- IAS/华东采购
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ITEM_SUP_AVG_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_FCST_SUM_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ITEM_SUP_AVG_T';
    V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_TOP_ITEM_INFO_T';
    V_TMP_TABLE := 'IAS_ECPQC_CROSS_FLAG_TMP';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := ' AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    V_REL_CONDITION := 'AND T1.CALIBER_FLAG = T2.CALIBER_FLAG';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
  END IF;
   
  -- 查询版本号   
  IF F_VERSION_ID IS NULL THEN 
     V_SQL := '
        SELECT VERSION_ID 
           FROM '||V_VERSION_TABLE||'
           WHERE DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = UPPER(''ITEM'')
           AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
           ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
   EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE 
        V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  V_SQL := 'DELETE FROM  '||V_TO_TABLE||' WHERE SCENARIO_FLAG = ''Y'' '||V_SQL_CONDITION;
  EXECUTE IMMEDIATE V_SQL;

  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM + 1,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，删除'||V_TO_TABLE||'预测表中SCENARIO_FLAG为Y的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表
  V_SQL := '
    DROP TABLE IF EXISTS '||V_TMP_TABLE||';
    CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
           YEAR BIGINT,
           PERIOD_ID BIGINT,
           ITEM_CODE VARCHAR(50),
           ITEM_NAME VARCHAR(1000),
           CATEGORY_CODE VARCHAR(50),
           CATEGORY_NAME VARCHAR(200),
           L4_CEG_CODE VARCHAR(50),
           L4_CEG_SHORT_CN_NAME VARCHAR(200),
           L4_CEG_CN_NAME VARCHAR(200),
           L3_CEG_CODE VARCHAR(50),
           L3_CEG_SHORT_CN_NAME VARCHAR(200),
           L3_CEG_CN_NAME VARCHAR(200),
           L2_CEG_CODE VARCHAR(50),
           L2_CEG_CN_NAME VARCHAR(200),
           GROUP_CODE VARCHAR(50),
           GROUP_CN_NAME VARCHAR(1000),
           GROUP_LEVEL VARCHAR(50),
           AVG_PRICE_CNY NUMERIC,
           NULL_FLAG VARCHAR(2),
           PARENT_LEVEL VARCHAR(50),
           PARENT_CODE VARCHAR(50),
           PARENT_CN_NAME VARCHAR(200),
           TOP_FLAG VARCHAR(2),
           APPEND_FLAG VARCHAR(2)
         )
      ON COMMIT PRESERVE ROWS
      DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;

  -- 写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM + 1,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，创建临时表'||V_TMP_TABLE||',表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
    --2.插入新补齐的均价预测数
  V_SQL := 'INSERT INTO '||V_TMP_TABLE||'
    (
    YEAR ,
    PERIOD_ID ,
    ITEM_CODE ,
    ITEM_NAME ,
    CATEGORY_CODE ,
    CATEGORY_NAME ,
    L4_CEG_CODE ,
    L4_CEG_SHORT_CN_NAME ,
    L4_CEG_CN_NAME ,
    L3_CEG_CODE ,
    L3_CEG_SHORT_CN_NAME ,
    L3_CEG_CN_NAME ,
    L2_CEG_CODE ,
    L2_CEG_CN_NAME ,
    GROUP_CODE ,
    GROUP_CN_NAME ,
    GROUP_LEVEL ,
    AVG_PRICE_CNY ,
    NULL_FLAG,
    PARENT_LEVEL ,
    PARENT_CODE ,
    PARENT_CN_NAME ,
    TOP_FLAG ,
    APPEND_FLAG 
    )
    WITH RECENT_ACTUAL_TEMP AS
     (
      --从实际数补齐表取最近一次实际月(即当前预测月-1)的品类信息,item信息和均价信息
      SELECT  DISTINCT T.PERIOD_ID,
              T.CATEGORY_CODE,
              T.CATEGORY_NAME,
              T.ITEM_CODE,
              T.ITEM_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L2_CEG_CODE,
              T.L2_CEG_CN_NAME,
              T.GROUP_CODE ,
              T.GROUP_CN_NAME ,
              T.GROUP_LEVEL ,
              T.AVG_RECEIVE_AMT ,
              T.PARENT_LEVEL ,
              T.PARENT_CODE ,
              T.PARENT_CN_NAME ,
              T.TOP_FLAG ,
              T.APPEND_FLAG 
        FROM '||V_FROM_TABLE||' T
       WHERE T.PERIOD_ID =
             TO_NUMBER(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1), ''YYYYMM''))
             AND PARENT_LEVEL = ''CATEGORY''
             '||V_SQL_CONDITION||'
             ), 
    CROSS_JOIN_TEMP AS
     (
      --生成今年预测月份及预测月份之后, 所有的发散维
      SELECT  A.CATEGORY_CODE,
              A.CATEGORY_NAME,
              A.ITEM_CODE,
              A.ITEM_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L2_CEG_CODE,
              A.L2_CEG_CN_NAME,
              A.GROUP_CODE ,
              A.GROUP_CN_NAME ,
              A.GROUP_LEVEL ,
              A.PARENT_LEVEL ,
              A.PARENT_CODE ,
              A.PARENT_CN_NAME ,
              A.TOP_FLAG ,
              A.APPEND_FLAG ,
              CAST(SUBSTR(TO_CHAR(B.PERIOD_ID), 1, 4) AS BIGINT) AS YEAR,
              B.PERIOD_ID
            FROM RECENT_ACTUAL_TEMP A,
               ( SELECT CAST(TO_CHAR(ADD_MONTHS(ADD_MONTHS(CURRENT_TIMESTAMP, -1),NUM.VAL ),''YYYYMM'') AS BIGINT)
                      AS PERIOD_ID
                    FROM GENERATE_SERIES(0,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                      ADD_MONTHS(CURRENT_TIMESTAMP, -1),
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)+1)||''01'',''YYYYMM''))),
                              1) NUM(VAL)) B),   -- 取当前-1月至当年12月的所有月份
    FCST_ITEM_TEMP AS
     (
      SELECT I.PERIOD_ID, I.ITEM_CODE, I.AVG_PRICE_CNY
        FROM '||V_FROM1_TABLE||' I
        WHERE DEL_FLAG = ''N'' 
        '||V_SQL_CONDITION||'
      UNION ALL 
      SELECT PERIOD_ID, ITEM_CODE, AVG_RECEIVE_AMT AS AVG_PRICE_CNY
         FROM RECENT_ACTUAL_TEMP    -- 历史数补齐表上个月数据
         ) 

    SELECT T1.YEAR ,
           T1.PERIOD_ID ,
           T1.ITEM_CODE ,
           T1.ITEM_NAME ,
           T1.CATEGORY_CODE ,
           T1.CATEGORY_NAME ,
           T1.L4_CEG_CODE ,
           T1.L4_CEG_SHORT_CN_NAME ,
           T1.L4_CEG_CN_NAME ,
           T1.L3_CEG_CODE ,
           T1.L3_CEG_SHORT_CN_NAME ,
           T1.L3_CEG_CN_NAME ,
           T1.L2_CEG_CODE ,
           T1.L2_CEG_CN_NAME ,
           T1.GROUP_CODE ,
           T1.GROUP_CN_NAME ,
           T1.GROUP_LEVEL ,
           T2.AVG_PRICE_CNY ,
           DECODE(T2.AVG_PRICE_CNY, NULL, 0, 1) AS NULL_FLAG,
           T1.PARENT_LEVEL ,
           T1.PARENT_CODE ,
           T1.PARENT_CN_NAME ,
           T1.TOP_FLAG ,
           DECODE(T2.AVG_PRICE_CNY, NULL, ''Y'', ''N'') AS APPEND_FLAG
        FROM CROSS_JOIN_TEMP T1
        LEFT JOIN FCST_ITEM_TEMP T2
        ON T1.PERIOD_ID = T2.PERIOD_ID
        AND T1.ITEM_CODE = T2.ITEM_CODE';
      EXECUTE IMMEDIATE V_SQL;
         
  -- 写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM + 1,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，预测数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 插入结果表数据
    V_SQL := '
  INSERT INTO '||V_TO_TABLE||'
    (
    VERSION_ID ,
    YEAR ,
    PERIOD_ID ,
    ITEM_CODE ,
    ITEM_NAME ,
    CATEGORY_CODE ,
    CATEGORY_NAME ,
    L4_CEG_CODE ,
    L4_CEG_SHORT_CN_NAME ,
    L4_CEG_CN_NAME ,
    L3_CEG_CODE ,
    L3_CEG_SHORT_CN_NAME ,
    L3_CEG_CN_NAME ,
    L2_CEG_CODE ,
    L2_CEG_CN_NAME ,
    GROUP_CODE ,
    GROUP_CN_NAME ,
    GROUP_LEVEL ,
    AVG_RECEIVE_AMT ,
    PARENT_LEVEL ,
    PARENT_CODE ,
    PARENT_CN_NAME ,
    TOP_FLAG ,
    APPEND_FLAG ,
    SCENARIO_FLAG ,
    '||V_CALIBER||'
    CREATED_BY ,
    CREATION_DATE ,
    LAST_UPDATED_BY ,
    LAST_UPDATE_DATE ,
    DEL_FLAG 
    )
   WITH NULL_FLAG_TMP AS(
        SELECT T1.YEAR ,
               T1.PERIOD_ID ,
               T1.ITEM_CODE ,
               T1.ITEM_NAME ,
               T1.CATEGORY_CODE ,
               T1.CATEGORY_NAME ,
               T1.L4_CEG_CODE ,
               T1.L4_CEG_SHORT_CN_NAME ,
               T1.L4_CEG_CN_NAME ,
               T1.L3_CEG_CODE ,
               T1.L3_CEG_SHORT_CN_NAME ,
               T1.L3_CEG_CN_NAME ,
               T1.L2_CEG_CODE ,
               T1.L2_CEG_CN_NAME ,
               T1.GROUP_CODE ,
               T1.GROUP_CN_NAME ,
               T1.GROUP_LEVEL ,
               T1.AVG_PRICE_CNY ,
               SUM(T1.NULL_FLAG) OVER(PARTITION BY T1.ITEM_CODE,T1.CATEGORY_CODE ,T1.L4_CEG_CODE ,T1.L3_CEG_CODE,T1.L2_CEG_CODE ORDER BY T1.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
               T1.PARENT_LEVEL ,
               T1.PARENT_CODE ,
               T1.PARENT_CN_NAME ,
               T1.TOP_FLAG ,
               T1.APPEND_FLAG
            FROM '||V_TMP_TABLE||' T1
             ),
   DATA_APPEND_TMP AS(
    SELECT 
        T1.YEAR ,
        T1.PERIOD_ID ,
        T1.ITEM_CODE ,
        T1.ITEM_NAME ,
        T1.CATEGORY_CODE ,
        T1.CATEGORY_NAME ,
        T1.L4_CEG_CODE ,
        T1.L4_CEG_SHORT_CN_NAME ,
        T1.L4_CEG_CN_NAME ,
        T1.L3_CEG_CODE ,
        T1.L3_CEG_SHORT_CN_NAME ,
        T1.L3_CEG_CN_NAME ,
        T1.L2_CEG_CODE ,
        T1.L2_CEG_CN_NAME ,
        T1.GROUP_CODE ,
        T1.GROUP_CN_NAME ,
        T1.GROUP_LEVEL ,
        FIRST_VALUE(T1.AVG_PRICE_CNY) OVER(PARTITION BY T1.AVG_AMT_FLAG,T1.ITEM_CODE,T1.CATEGORY_CODE ,T1.L4_CEG_CODE ,T1.L3_CEG_CODE,T1.L2_CEG_CODE ORDER BY T1.PERIOD_ID) AS AVG_RECEIVE_AMT,
        T1.PARENT_LEVEL ,
        T1.PARENT_CODE ,
        T1.PARENT_CN_NAME ,
        T1.TOP_FLAG ,
        T1.APPEND_FLAG 
     FROM NULL_FLAG_TMP T1
   )
    SELECT 
        '||V_VERSION_ID||',
        T1.YEAR ,
        T1.PERIOD_ID ,
        T1.ITEM_CODE ,
        T1.ITEM_NAME ,
        T1.CATEGORY_CODE ,
        T1.CATEGORY_NAME ,
        T1.L4_CEG_CODE ,
        T1.L4_CEG_SHORT_CN_NAME ,
        T1.L4_CEG_CN_NAME ,
        T1.L3_CEG_CODE ,
        T1.L3_CEG_SHORT_CN_NAME ,
        T1.L3_CEG_CN_NAME ,
        T1.L2_CEG_CODE ,
        T1.L2_CEG_CN_NAME ,
        T1.GROUP_CODE ,
        T1.GROUP_CN_NAME ,
        T1.GROUP_LEVEL ,
        T1.AVG_RECEIVE_AMT,
        T1.PARENT_LEVEL ,
        T1.PARENT_CODE ,
        T1.PARENT_CN_NAME ,
        T1.TOP_FLAG ,
        T1.APPEND_FLAG ,
        ''Y'' AS SCENARIO_FLAG ,
        '||V_IN_CALIBER||'
        -1 AS CREATED_BY,
        CURRENT_TIMESTAMP AS CREATION_DATE,
        -1 AS LAST_UPDATED_BY,
        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
        ''N'' AS DEL_FLAG
    FROM DATA_APPEND_TMP T1
    WHERE T1.PERIOD_ID >= CAST(TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'') AS BIGINT)';
    EXECUTE IMMEDIATE V_SQL;
     
     
  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM + 1,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，插入新补齐的均价预测数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --当计算采购ICT数据时，将补齐表数据修改为最新版本号
  IF F_CALIBER_FLAG = 'I' THEN 
    V_SQL := 'UPDATE '||V_TO_TABLE||' T1 SET VERSION_ID = '||V_VERSION_ID||' WHERE SCENARIO_FLAG = ''S'' ';
    EXECUTE IMMEDIATE V_SQL;
  ELSE NULL;
  END IF;
   
       --打给补齐表打TOP_ITEM标签
    V_SQL := 'UPDATE '||V_TO_TABLE||' T1 SET TOP_FLAG = ''Y'' 
                WHERE EXISTS ( SELECT 1 FROM '||V_JOIN_TABLE||' T2 WHERE T1.ITEM_CODE = T2.ITEM_CODE AND T1.VERSION_ID = T2.VERSION_ID '||V_REL_CONDITION||') 
                '||V_SQL_CONDITION;
    EXECUTE IMMEDIATE V_SQL;
   
   --写入日志
PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，TOP标签已插入'||V_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --3.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM + 1,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 领域标识为：'||F_CALIBER_FLAG||'，收集'||V_TO_TABLE||'统计信息完成!');

    return 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

