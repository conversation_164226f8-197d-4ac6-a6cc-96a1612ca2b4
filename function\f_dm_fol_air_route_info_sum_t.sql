-- Name: f_dm_fol_air_route_info_sum_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_route_info_sum_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 	/*
创建人  ：zwx1275798
创建时间：2024年8月27日
背景描述：物流空运航线量价汇总表，航线量集成表 关联 物流空运框招及燃油价格补录表、TAC价格集成表
参数描述： p_version_id   逻辑：1、自动调度，取航线量汇总表的最大版本ID；2、刷新（页面的刷新价格表）：取java传版本ID；
           p_version_code 逻辑：1、自动调度，取价格补录头表的最大版本code；2、刷新（页面的刷新价格表）：取java传版本code；
           p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表）：取java传的值（1_刷新价格表 ）
		   x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_route_info_sum_t()
变更记录：202503 zwx1275798 代码优化(代码由808行缩减至650行)：
                                         1、将所有with as 临时表修改为temporary会话临时表
                                         2、将所有temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_fol_air_route_info_sum_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_fol_air_route_info_sum_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_apd_max_version_code varchar(30);


begin

	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '物流空运航线量价汇总表'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;	
	

        --从 物流空运航线量价汇总表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_route_info_sum_t t1 
		 where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001' and t2.transport_mode = '精品空运');		 		 
		 
		  -- 如果JAVA传入的p_version_id为空，则为自动调度，取版本表的最大版本ID（版本表无数据则赋值1） ，如果JAVA传入的p_version_id有值则用JAVA传入的p_version_id
      if (p_version_id is null or p_version_id = '') then 	  
	    select  nvl(max(version_id)+1,1) into v_max_version_id
		from fin_dm_opt_foi.dm_fol_air_version_info_t t1
		where upper(del_flag) = 'N';
		else 
		select  p_version_id into v_max_version_id ;
		end if
          ; 
		
		-- 如果p_version_code为空，则取 物流空运价格补录表头表中 的最大版本编码，如果p_version_code不为空，则取传入的p_version_code   
		if (p_version_code is null or p_version_code = '') then
        select max(version_code) as max_version_code into v_apd_max_version_code 
		from fin_dm_opt_foi.apd_fol_air_route_price_heaer_t 
		where upper(status)='FINAL';
		else 
		select  p_version_code into v_apd_max_version_code ;
		end if
          ; 

       v_dml_row_count := sql%rowcount;	-- 收集数据量

       -- 写入日志
       perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => 2,
         p_log_cal_log_desc => '版本ID：'||v_max_version_id||'，传入刷新类型：'||p_refresh_type||'，价格补录表最大编码：'||v_apd_max_version_code,--日志描述
         p_log_formula_sql_txt => null,--错误信息,--日志描述
         p_log_row_count => v_dml_row_count,
         p_log_errbuf => null  --错误编码
       ) ;

	
		  -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 2 
		 and source_en_name = 'f_dm_fol_air_route_info_sum_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');
		 
		 -- 将执行步骤：2  执行中 插入版本信息表中的
        insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
                    version_id           -- 版本ID（自动生成）
                  , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
                  , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
                  , source_en_name       -- 来源英文描述（可以是表名、函数名等）
                  , source_cn_name       -- 来源中文描述
                  , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
				  , transport_mode       -- 运输方式（精品空运、精品海运）
                  , remark               -- 备注
                  , created_by           -- 创建人
                  , creation_date        -- 创建时间
                  , last_updated_by      -- 修改人
                  , last_update_date     -- 修改时间
                  , del_flag             -- 是否删除
                  )       
             select v_max_version_id   as version_id
                  , v_apd_max_version_code as version_code
                  , 2 as step
                  , 'f_dm_fol_air_route_info_sum_t' as source_en_name
                  , '物流空运航线量价汇总表' as source_cn_name
                  , nvl(p_refresh_type,'4_AUTO') as refresh_type
				  , '精品空运' as transport_mode
                  , 'version_code为物流空运框招及燃油价格补录表的最大版本编码' as remark
  	              , -1 as created_by
  	              , current_timestamp as creation_date
  	              , -1 as last_updated_by
  	              , current_timestamp as last_update_date
  	              , 'N' as del_flag
                 ;
				 

		  

               -- 取航线量集成表的数据入到临时表
             drop table if exists air_scm_lg_shipment_f_tmp;
	         create temporary table air_scm_lg_shipment_f_tmp
	         as
             select substr(asd_utc,1,4)         as year                        -- 年份
                  , substr(asd_utc,1,4)||substr(asd_utc,6,2)||substr(asd_utc,9,2) as  period_id -- 会计期				 
                  , high_quality_flag          as transport_mode  -- 运输方式（精品空运、TAC）
                  , substr(rate_geo_gid,position('.' in rate_geo_gid)+1)                 as price_id                -- 价格ID，对应集成表字段 rate_geo_gid
                  , sum(total_volume) as total_volume  -- 总体积
                  , sum(total_weight) as total_weight  -- 总毛重
                  , case when High_quality_flag = '精品空运' and  Carrier_name = '3U' 
                         then '3U'
             	         when High_quality_flag = '精品空运' and  (Carrier_name ='CZ' or Carrier_name ='CZ;CZ')
             	         then 'CZ'
             	         when High_quality_flag = '精品空运' and  (Carrier_name != '3U' or Carrier_name !='CZ' or Carrier_name !='CZ;CZ' )
             	         then LSP
             	         when High_quality_flag is null or  High_quality_flag= ''
             	         then LSP 
             		     end as  supplier_short_name
               from fin_dm_opt_foi.dwr_scm_lg_shipment_f
              where  substr(asd_utc,1,4) || substr(asd_utc,6,2) >= '202001'
                and (high_quality_flag = '精品空运' or  High_quality_flag is null or  High_quality_flag= '')
                and asd_utc is not null
				group by  substr(asd_utc,1,4)    -- 年份
                  , substr(asd_utc,1,4)||substr(asd_utc,6,2)||substr(asd_utc,9,2)   -- 会计期				 
                  , high_quality_flag    -- 运输方式（精品空运、TAC）
                  , substr(rate_geo_gid,position('.' in rate_geo_gid)+1)               -- 价格ID，对应集成表字段 rate_geo_gid
                  , case when High_quality_flag = '精品空运' and  Carrier_name = '3U' 
                         then '3U'
             	         when High_quality_flag = '精品空运' and  (Carrier_name ='CZ' or Carrier_name ='CZ;CZ')
             	         then 'CZ'
             	         when High_quality_flag = '精品空运' and  (Carrier_name != '3U' or Carrier_name !='CZ' or Carrier_name !='CZ;CZ' )
             	         then LSP
             	         when High_quality_flag is null or  High_quality_flag= ''
             	         then LSP 
             		     end 
                  ;
                 
			  v_dml_row_count := sql%rowcount;	-- 收集数据量
			  
			  -- 写入日志
               perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 3,
                   p_log_cal_log_desc => '航线量集成数据入到临时表 air_scm_lg_shipment_f_tmp， 数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
               ) ;
			   
			   -- 创建从2020年1月1日至今每天的时间临时表
			    drop table if exists time_tmp;
	            create temporary table time_tmp
	            as 
              select to_char(generate_series,'YYYYMM')  as period_id
                     ,to_char(generate_series,'YYYYMMDD')  as active_period
              from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 day')
              ;
			  
			   -- 从 物流空运框招及燃油价格补录表 中取 燃油价格+框招价格
			    drop table if exists price_tmp;
	            create temporary table price_tmp
	            as 		  
			  select version_code
			   , to_char(ful_begin_date,'yyyyMM') as period_id
               , price_id
               , source_port_name
               , dest_port_name
			   , substr(dest_port_name,instr(dest_port_name,'_')+1) as dest_country_name
               , region_cn_name
               , supplier_short_name
               , 'PTP价格+FUL价格'   as price_type
			   , sum(ptp_price+ful_price) as hw_price
               , currency
               , to_char(ful_begin_date,'yyyyMMdd') as begin_date
               , to_char(ful_end_date,'yyyyMMdd') as end_date
               , Huawei_group
               , service_level
               , is_high_quality
               , path
               , billing_mode
				 from fin_dm_opt_foi.apd_fol_air_route_price_info_t 
				 where version_code = v_apd_max_version_code
				   and source_port_name= 'GUANGDONG HONGKONG ZONE'	
				  group by version_code
				  ,to_char(ful_begin_date,'yyyyMM')
				  , price_id
                  , source_port_name
                  , dest_port_name
			      , substr(dest_port_name,instr(dest_port_name,'_')+1)
			      , region_cn_name
                  , supplier_short_name
			      , currency
                  , to_char(ful_begin_date,'yyyyMMdd')  
                  , to_char(ful_end_date,'yyyyMMdd')    
                  , Huawei_group
                  , service_level
                  , is_high_quality
                  , path
                  , billing_mode
				  ;
			  
			  			    
			   -- 先将 燃油价格+框招价格临时表与每天的时间临时表关联，发散到日粒度
			  drop table if exists air_route_price_info_tmp;
	          create temporary table air_route_price_info_tmp
	              as               
				  select t1.version_code
			      , t1.period_id
				  , t2.active_period
                  , t1.price_id
                  , t1.source_port_name
                  , t1.dest_port_name
			      , t1.dest_country_name
                  , t1.region_cn_name
                  , t1.supplier_short_name
                  , t1.price_type
			      , t1.hw_price
                  , t1.currency                  
                  , t1.Huawei_group
                  , t1.service_level
                  , t1.is_high_quality
                  , t1.path
                  , t1.billing_mode
				 from price_tmp t1
				 left join time_tmp t2
				 on t1.period_id = t2.period_id
				 and t1.begin_date <= t2.active_period
				 and t2.active_period <= t1.end_date
			   ;
				 
			    v_dml_row_count := sql%rowcount;	-- 收集数据量
			  
			  -- 写入日志
               perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 4,
                   p_log_cal_log_desc => '将燃油+框招价格入到临时表 air_route_price_info_tmp， 数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
               ) ;
			   					  
              -- 取外部价格表的城市信息的数据
              drop table if exists tac_city_tmp;
	          create temporary table tac_city_tmp
	              as  
                select substr(price_effective_time,1,4)||substr(price_effective_time,6,2)||substr(price_effective_time,9,2) as price_effective_time
                     ,substr(price_expiration_time,1,4)||substr(price_expiration_time,6,2)||substr(price_expiration_time,9,2) as price_expiration_time
					 ,date
					 ,origin_name
					 ,destination_name
					 ,destination_type
					 ,to_number(usd) as tac_price
                 from fin_dm_opt_foi.dwb_ed_tac_interntl_transportmkt_f
				 where destination_type = 'city'
				 and  origin_name = 'Hong Kong'
				 ;

				-- 关联外部价格表，先匹配城市信息
				drop table if exists air_tac_hw_price_tmp1;
	            create temporary table air_tac_hw_price_tmp1
	              as  							 
				 select t1.period_id
				       ,t1.active_period
                       ,t1.price_id
                       ,t1.source_port_name
                       ,t1.dest_port_name
					   ,t1.dest_country_name
                       ,t1.region_cn_name
                       ,t1.supplier_short_name
					   ,t1.currency
                       ,sum(t1.hw_price)  as hw_price        
					   ,sum(t2.tac_price) as tac_price	           
                       ,t1.Huawei_group
                       ,t1.service_level
                       ,t1.is_high_quality
                       ,t1.path
                       ,t1.billing_mode
				 from air_route_price_info_tmp t1
		         left join tac_city_tmp t2
				   on upper(substr(t1.dest_port_name,1,instr(dest_port_name,'_')-1))= upper(t2.destination_name)
				   and t2.price_effective_time <= t1.active_period
				   and t1.active_period <= t2.price_expiration_time
				  group by t1.period_id
				       ,t1.active_period
                       ,t1.price_id
                       ,t1.source_port_name
                       ,t1.dest_port_name
					   ,t1.dest_country_name
                       ,t1.region_cn_name
                       ,t1.supplier_short_name
					   ,t1.currency            
                       ,t1.Huawei_group
                       ,t1.service_level
                       ,t1.is_high_quality
                       ,t1.path
                       ,t1.billing_mode
                 ;
           
		         v_dml_row_count := sql%rowcount;	-- 收集数据量
			  
			  -- 写入日志
               perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 5,
                   p_log_cal_log_desc => '当TAC价格生效与失效时间在同一个月时，匹配city信息 air_tac_hw_price_tmp1， 数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
               ) ;

                 -- 取外部价格表的国家信息的数据
                 drop table if exists tac_country_tmp;
	            create temporary table tac_country_tmp
	              as  
                select substr(price_effective_time,1,4)||substr(price_effective_time,6,2)||substr(price_effective_time,9,2) as price_effective_time
                     ,substr(price_expiration_time,1,4)||substr(price_expiration_time,6,2)||substr(price_expiration_time,9,2) as price_expiration_time
					 ,date
					 ,origin_name
					 ,destination_name
					 ,destination_type
					 ,to_number(usd) as tac_price
                 from fin_dm_opt_foi.dwb_ed_tac_interntl_transportmkt_f
				 where destination_type = 'country'
				 and  origin_name = 'Hong Kong'
                      ;				 

				  -- 再匹配国家
				  drop table if exists air_tac_hw_price_tmp2;
	            create temporary table air_tac_hw_price_tmp2
	              as                    			  		 				 
				 select t1.period_id
				       ,t1.active_period
                       ,t1.price_id
                       ,t1.source_port_name
                       ,t1.dest_port_name
					   ,t1.dest_country_name
                       ,t1.region_cn_name
                       ,t1.supplier_short_name
					   ,t1.currency
					   ,sum(t1.hw_price) as hw_price                       					   
                       ,sum(case when t1.tac_price is null 
					         then t2.tac_price 
							 else t1.tac_price
                             end) as tac_price							                       
                       ,t1.Huawei_group
                       ,t1.service_level
                       ,t1.is_high_quality
                       ,t1.path
                       ,t1.billing_mode
				 from air_tac_hw_price_tmp1 t1
		         left join tac_country_tmp t2
				   on upper(t1.dest_country_name)= upper(t2.destination_name)
				   and t2.price_effective_time <= t1.active_period
				   and t1.active_period <= t2.price_expiration_time
				  group by t1.period_id
				       ,t1.active_period
                       ,t1.price_id
                       ,t1.source_port_name
                       ,t1.dest_port_name
					   ,t1.dest_country_name
                       ,t1.region_cn_name
                       ,t1.supplier_short_name
					   ,t1.currency                     					   
                       ,t1.Huawei_group
                       ,t1.service_level
                       ,t1.is_high_quality
                       ,t1.path
                       ,t1.billing_mode
                 ;
				 
				 v_dml_row_count := sql%rowcount;	-- 收集数据量
			  
			  -- 写入日志
               perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 6,
                   p_log_cal_log_desc => '当TAC价格生效与失效时间在同一个月时，匹配country信息 air_tac_hw_price_tmp2， 数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
               ) ;             
			  
			  -- 将华为价格和TAC价格列转行
			  drop table if exists air_tac_hw_price_tmp3;
	            create temporary table air_tac_hw_price_tmp3
	              as  			  
              select  period_id
			         ,active_period
					 ,price_id					 
					 ,source_port_name
					 ,dest_port_name
					 ,dest_country_name
					 ,'精品空运' as transport_mode
					 ,region_cn_name
					 ,supplier_short_name					 
					 ,currency
					 ,'PTP价格+FUL价格' as price_type 
					 ,hw_price as price         			   
					 ,Huawei_group
					 ,service_level
					 ,is_high_quality
					 ,path
					 ,billing_mode	
			    from air_tac_hw_price_tmp2
				union all
				 select  period_id
				     ,active_period
					 ,price_id
					 ,source_port_name
					 ,dest_port_name
					 ,dest_country_name
					 ,'TAC' as transport_mode
					 ,region_cn_name
					 ,supplier_short_name					 
					 ,currency
					 ,'TAC价格' as price_type 
					 ,tac_price as price         					   
					 ,Huawei_group
					 ,service_level
					 ,is_high_quality
					 ,path
					 ,billing_mode	
			    from air_tac_hw_price_tmp2
				where tac_price is not null
			         ;
					 
					 v_dml_row_count := sql%rowcount;	-- 收集数据量
					 
					 -- 写入日志
               perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 7,
                   p_log_cal_log_desc => '将TAC价格和华为价格列转行 air_tac_hw_price_tmp3， 数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
               ) ;
					 
					 -- 清理目标表中数据
					 delete from fin_dm_opt_foi.dm_fol_air_route_info_sum_t where version_id = v_max_version_id ;
					 										 
				-- 关联航线量临时表获取货量，将数据入到目标表
				  insert into fin_dm_opt_foi.dm_fol_air_route_info_sum_t(
				     version_id
                    ,year
                    ,period_id
					,active_period
                    ,source_port_name
                    ,dest_port_name
                    ,dest_country_name
                    ,route
                    ,region_cn_name
                    ,transport_mode
                    ,price_id
                    ,supplier_short_name
                    ,price_type
                    ,currency
                    ,price
                    ,container_qty
                    ,billing_mode
                    ,Huawei_group
                    ,service_level
                    ,is_high_quality
                    ,path
                    ,remark
                    ,created_by
                    ,creation_date
                    ,last_updated_by
                    ,last_update_date
                    ,del_flag
                      )
					select v_max_version_id as version_id
                    ,substr(t1.period_id,1,4) as year
                    ,cast(t1.period_id as int) as period_id
					,t1.active_period
                    ,t1.source_port_name
                    ,t1.dest_port_name
                    ,t1.dest_country_name
                    ,t1.source_port_name||'-'||t1.dest_port_name as route
                    ,t1.region_cn_name
                    ,t1.transport_mode
                    ,t1.price_id
                    ,t1.supplier_short_name
                    ,t1.price_type
                    ,t1.currency
                    ,sum(t1.price) as price
                    ,sum(case when upper(t1.billing_mode) = 'SHIPMENT.WEIGHT' 
					      then total_weight
						  when upper(t1.billing_mode) = 'SHIPMENT.DIMWEIGHT'
						  then GREATEST(t2.total_volume*166.67,t2.total_weight)
						  end) as container_qty
                    ,t1.billing_mode
                    ,t1.Huawei_group
                    ,t1.service_level
                    ,t1.is_high_quality
                    ,t1.path
                    , '' as remark
 	                , -1 as created_by
 	                , current_timestamp as creation_date
 	                , -1 as last_updated_by
 	                , current_timestamp as last_update_date
 	                , 'N' as del_flag
					from air_tac_hw_price_tmp3 t1
					left join air_scm_lg_shipment_f_tmp t2
					on t1.price_id = t2.price_id
					and t1.active_period = t2.period_id
					group by substr(t1.period_id,1,4) 
                    ,t1.period_id
					,t1.active_period
                    ,t1.source_port_name
                    ,t1.dest_port_name
                    ,t1.dest_country_name
                    ,t1.region_cn_name
                    ,t1.transport_mode
                    ,t1.price_id
                    ,t1.supplier_short_name
                    ,t1.price_type
                    ,t1.currency
                    ,t1.billing_mode
                    ,t1.Huawei_group
                    ,t1.service_level
                    ,t1.is_high_quality
                    ,t1.path
			         ;
	
	            v_dml_row_count := sql%rowcount;	-- 收集数据量
				
			  -- 写入日志
               perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 8,
                   p_log_cal_log_desc => '将结果入到目标表 dm_fol_air_route_info_sum_t， 数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
               ) ;
			   			   
			   -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
               update fin_dm_opt_foi.dm_fol_air_version_info_t set step = 1
                where version_id = v_max_version_id
                  and version_code = v_apd_max_version_code
                  and source_en_name = 'f_dm_fol_air_route_info_sum_t'
                  and refresh_type = nvl(p_refresh_type,'4_AUTO')
                  and upper(del_flag) = 'N'
               ;

                v_dml_row_count := sql%rowcount;	-- 收集数据量

            -- 写入日志
            perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 13,
                p_log_cal_log_desc => '【 dm_fol_air_version_info_t  成功数据写入到版本信息表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;

            --收集统计信息
            analyse fin_dm_opt_foi.dm_fol_air_route_info_sum_t;
            analyse fin_dm_opt_foi.dm_fol_air_version_info_t;

            exception
            	when others then          
                perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
                  p_log_version_id => null,                 --版本
                  p_log_sp_name => v_sp_name,    --sp名称
                  p_log_para_list => '',--参数
                  p_log_step_num  => null,
                  p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
                  p_log_formula_sql_txt => sqlerrm,--错误信息
          		    p_log_row_count => null,
          		    p_log_errbuf => sqlstate  --错误编码
                ) ;
          	x_success_flag := '2001';	         --2001表示失败
          
            -- 失败信息写入到版本信息表
            insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
                   version_id           -- 版本ID（自动生成）
                 , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
                 , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
                 , source_en_name       -- 来源英文描述（可以是表名、函数名等）
                 , source_cn_name       -- 来源中文描述
                 , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
          	     , transport_mode       -- 运输方式（精品海运、精品空运）
                 , remark               -- 备注
                 , created_by           -- 创建人
                 , creation_date        -- 创建时间
                 , last_updated_by      -- 修改人
                 , last_update_date     -- 修改时间
                 , del_flag             -- 是否删除
            )            
            select v_max_version_id as version_id
                 , v_apd_max_version_code as version_code
                 , 2001 as step
                 , 'f_dm_fol_air_route_info_sum_t' as source_en_name
                 , '物流空运航线量价汇总表' as source_cn_name
                 , nvl(p_refresh_type,'4_AUTO') as refresh_type
          	     , '精品空运' as transport_mode
                 , 'version_code 是物流空运框招及燃油价格补录表的最大版本编码' as remark
          	     , -1 as created_by
          	     , current_timestamp as creation_date
          	     , -1 as last_updated_by
          	     , current_timestamp as last_update_date
          	     , 'N' as del_flag
              ;                  
end;
$$
/

