-- Name: f_dm_foi_energy_base_item_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_base_item_amt_t(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/1/30
创建人  ：罗若文
最后修改时间: 2023/1/30
最后修改人: 罗若文
背景描述：实际数金额卷积至ITEM层
参数描述：x_result_status ：是否成功
事例：SELECT FIN_DM_OPT_FOI.f_dm_foi_energy_base_item_amt_t()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_BASE_ITEM_AMT_T'; --存储过程名称
  V_VERSION_ID BIGINT ; --版本号ID
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_FROM_TABLE VARCHAR(200);
  V_TO_TABLE 	VARCHAR(200);
  V_SQL   TEXT;
BEGIN
  X_RESULT_STATUS = '1';
  
  V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';
  V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T';
  
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --清空目标表数据:
 TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T ;
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空 FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 

  --版本号赋值.
SELECT VERSION_ID  INTO V_VERSION_ID FROM DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T  LIMIT 1 ;
 
  
 
 
  --往目标表里插数
INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T
    (	VERSION_ID,
		YEAR,
		PERIOD_ID,
		ITEM_CODE,
		ITEM_NAME,
		RECEIVE_QTY,
		RECEIVE_AMT_USD,
		RECEIVE_AMT_CNY,
		CATEGORY_CODE,
		CATEGORY_NAME,
		L4_CEG_CODE,
		L4_CEG_SHORT_CN_NAME,
		L4_CEG_CN_NAME,
		L3_CEG_CODE,
		L3_CEG_SHORT_CN_NAME,
		L3_CEG_CN_NAME,
		L2_CEG_CODE,
		L2_CEG_CN_NAME,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		DEL_FLAG
     )
	 
	 SELECT V_VERSION_ID AS VERSION_ID,
		T.YEAR,
		T.PERIOD_ID,
		T.ITEM_CODE,
		T.ITEM_NAME,
		T.RECEIVE_QTY,
		T.RECEIVE_AMT_USD,
		T.RECEIVE_AMT_CNY,
		T.CATEGORY_CODE,
		T.CATEGORY_NAME,
		T.L4_CEG_CODE,
		T.L4_CEG_SHORT_CN_NAME,
		T.L4_CEG_CN_NAME,
		T.L3_CEG_CODE,
		T.L3_CEG_SHORT_CN_NAME,
		T.L3_CEG_CN_NAME,
		T.L2_CEG_CODE,
		T.L2_CEG_CN_NAME,
		'-1' AS CREATED_BY,
		CURRENT_TIMESTAMP AS CREATION_DATE,
		'-1' AS  LAST_UPDATED_BY,
		CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		'N' AS DEL_FLAG
		FROM 
	 (
		SELECT 
		YEAR,
		PERIOD_ID,
		ITEM_CODE,
		ITEM_NAME,
		SUM(RECEIVE_QTY) RECEIVE_QTY,
		SUM(RECEIVE_AMT_USD) RECEIVE_AMT_USD,
		SUM(RECEIVE_AMT_CNY) RECEIVE_AMT_CNY,
		CATEGORY_CODE,
		CATEGORY_NAME,
		L4_CEG_CODE,
		L4_CEG_SHORT_CN_NAME,
		L4_CEG_CN_NAME,
		L3_CEG_CODE,
		L3_CEG_SHORT_CN_NAME,
		L3_CEG_CN_NAME,
		L2_CEG_CODE,
		L2_CEG_CN_NAME
		FROM FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T
		WHERE DEL_FLAG = 'N'
		GROUP BY YEAR,
		PERIOD_ID,
		ITEM_CODE,
		ITEM_NAME,
		CATEGORY_CODE,
		CATEGORY_NAME,
		L4_CEG_CODE,
		L4_CEG_SHORT_CN_NAME,
		L4_CEG_CN_NAME,
		L3_CEG_CODE,
		L3_CEG_SHORT_CN_NAME,
		L3_CEG_CN_NAME,
		L2_CEG_CODE,
		L2_CEG_CN_NAME
		)T;

		
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T表, 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  

  
  --3.收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T表统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

