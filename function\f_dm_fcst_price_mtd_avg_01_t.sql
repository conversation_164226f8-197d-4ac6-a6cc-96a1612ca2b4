-- Name: f_dm_fcst_price_mtd_avg_01_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mtd_avg_01_t(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：20241106
创建人  ：qwx1110218
背景描述：月累计均本补齐表：从SPART层级月累计收敛表（DM_FCST_PRICE_MID_DECODE_DTL_T）直接取数补齐均价，向前、向后补齐；

事例：SELECT FIN_DM_OPT_FOI.f_dm_fcst_price_mtd_avg_01_t()
*/

DECLARE
  V_SP_NAME     VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MTD_AVG_01_T';
  V_FROM_TABLE  VARCHAR(100);  -- 来源表
	V_TO_TABLE    VARCHAR(100);  -- 目标表
	V_VERSION_ID  BIGINT;        -- 版本号
	V_SOURCE_VERSION_ID BIGINT;  -- 目标表的版本号
  V_PERIOD_YEAR BIGINT; -- 年份
  V_SQL         TEXT;   -- SQL逻辑
  V_STEP_MUM    BIGINT; --步骤号
  V_TEMP_TABLE  VARCHAR(100);



BEGIN
  X_RESULT_STATUS = 'SUCCESS';

  --0.开始日志
  V_STEP_MUM := 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => V_SP_NAME||'开始执行'
  );

  V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_DECODE_DTL_T';
  V_TO_TABLE   := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_MTD_AVG_01_T';
  V_TEMP_TABLE := 'ACTUAL_APD_TEMP';
  
  -- 从版本表取最新版本
  IF(F_VERSION_ID IS NULL) THEN
    SELECT VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'  -- 用月度版本号
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1
    ;

  ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;

  RAISE NOTICE'V_VERSION_ID：%',V_VERSION_ID;

  -- 从来源表取数入到临时表
  DROP TABLE IF EXISTS ALL_ACTUAL_ITEM_TEMP;
  CREATE TEMPORARY TABLE ALL_ACTUAL_ITEM_TEMP(
			   PERIOD_YEAR                        BIGINT
       , PERIOD_ID                          BIGINT
       , BG_CODE                            VARCHAR(50)
       , BG_CN_NAME                         VARCHAR(200)
       , LV0_PROD_LIST_CODE                 VARCHAR(50)
       , LV1_PROD_LIST_CODE                 VARCHAR(50)
       , LV2_PROD_LIST_CODE                 VARCHAR(50)
       , LV3_PROD_LIST_CODE                 VARCHAR(50)
       , LV4_PROD_LIST_CODE                 VARCHAR(50)
       , LV0_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV1_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV2_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV3_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV4_PROD_LIST_CN_NAME              VARCHAR(200)
       , OVERSEA_FLAG                       VARCHAR(10)
       , REGION_CODE                        VARCHAR(50)
       , REGION_CN_NAME                     VARCHAR(200)
       , REPOFFICE_CODE                     VARCHAR(50)
       , REPOFFICE_CN_NAME                  VARCHAR(200)
       , SIGN_TOP_CUST_CATEGORY_CODE        VARCHAR(50)
       , SIGN_TOP_CUST_CATEGORY_CN_NAME     VARCHAR(200)
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME   VARCHAR(200)
       , SPART_CODE                         VARCHAR(50)
       , SPART_CN_NAME                      VARCHAR(2000)
       , VIEW_FLAG                          VARCHAR(50)
       , USD_PNP_AMT                        NUMERIC
       , RMB_PNP_AMT                        NUMERIC
       , USD_PNP_AVG                        NUMERIC
       , RMB_PNP_AVG                        NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;
  
  -- 创建临时表
  DROP TABLE IF EXISTS PERIOD_DIM_TEMP;
  CREATE TEMPORARY TABLE PERIOD_DIM_TEMP(
         PERIOD_ID BIGINT
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY REPLICATION
  ;

	RAISE NOTICE'11111111111111';


 if MONTH(CURRENT_TIMESTAMP) = 1
  then V_PERIOD_YEAR := YEAR(CURRENT_TIMESTAMP) - 4;-- 取4年的数据
  ELSE
  V_PERIOD_YEAR := YEAR(CURRENT_TIMESTAMP) - 3;-- 取4年的数据（包括当年）
  END IF ;
	

  -- 生成连续月份, 三年前第1月至当前系统月(不含)    
  V_SQL := '
  INSERT INTO PERIOD_DIM_TEMP(PERIOD_ID)
  SELECT CAST(TO_CHAR(ADD_MONTHS(TO_DATE('''||V_PERIOD_YEAR||''',''YYYYMMDD''),NUM.VAL - 1),''YYYYMM'') AS BIGINT) AS PERIOD_ID
    FROM GENERATE_SERIES(1,TO_NUMBER(TIMESTAMPDIFF(MONTH,TO_DATE('''||V_PERIOD_YEAR||''',''YYYYMMDD''),CURRENT_TIMESTAMP)),1) NUM(VAL)
  ';

  RAISE NOTICE'33333333333333';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '生成连续月份：'||V_PERIOD_YEAR||'01,'||CURRENT_TIMESTAMP,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );

  -- 从来源表取数入到临时表
  V_SQL := '
  INSERT INTO ALL_ACTUAL_ITEM_TEMP(
         PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , USD_PNP_AVG
       , RMB_PNP_AVG
  )
    WITH PRICE_TOP_SPART_INFO_TMP AS(
  SELECT DISTINCT T1.PERIOD_YEAR
       , T1.BG_CODE                           -- BG编码
       , T1.BG_CN_NAME                        -- BG中文名称
       , T1.LV0_PROD_LIST_CODE                -- 产品LV0编码
       , T1.LV1_PROD_LIST_CODE                -- 产品LV1编码
       , T1.LV2_PROD_LIST_CODE                -- 产品LV2编码
       , T1.LV3_PROD_LIST_CODE                -- 产品LV3编码
       , T1.LV4_PROD_LIST_CODE                -- 产品LV4编码
       , T1.LV0_PROD_LIST_CN_NAME             -- 产品LV0中文名称
       , T1.LV1_PROD_LIST_CN_NAME             -- 产品LV1中文名称
       , T1.LV2_PROD_LIST_CN_NAME             -- 产品LV2中文名称
       , T1.LV3_PROD_LIST_CN_NAME             -- 产品LV3中文名称
       , T1.LV4_PROD_LIST_CN_NAME             -- 产品LV4中文名称
       , T1.TOP_SPART_CODE                    -- TOP_SPART编码
       , T1.TOP_SPART_CN_NAME                 -- TOP_SPART中文名称
       , T1.OVERSEA_FLAG                      -- 国内海外标识
       , T1.REGION_CODE                       -- 地区部编码
       , T1.REGION_CN_NAME                    -- 地区部名称
       , T1.REPOFFICE_CODE                    -- 代表处编码
       , T1.REPOFFICE_CN_NAME                 -- 代表处名称
       , T1.SIGN_TOP_CUST_CATEGORY_CODE       -- 签约客户_大T系统部编码
       , T1.SIGN_TOP_CUST_CATEGORY_CN_NAME    -- 签约客户_大T系统部名称
       , T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME  -- 签约客户_子网系统部名称
       , T1.VIEW_FLAG                         -- 视角标识，用于区分不同视角下的数据()
    FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_TOP_SPART_INFO_T T1  -- 月累计TOP_SPART表（月度版本号）   
   WHERE UPPER(T1.IS_TOP_FLAG)  = ''Y''  -- 是否TOP标识（Y：TOP、N：非TOP）
    AND  UPPER(T1.YTD_TOP_FLAG) = ''Y''      -- 取在TOP_SPART筛选范围内
     AND T1.VERSION_ID = '||V_VERSION_ID||'
     AND T1.DEL_FLAG = ''N''
  )
  SELECT T1.PERIOD_YEAR
       , T1.PERIOD_ID
       , T1.BG_CODE
       , T1.BG_CN_NAME
       , T1.LV0_PROD_LIST_CODE
       , T1.LV1_PROD_LIST_CODE
       , T1.LV2_PROD_LIST_CODE
       , T1.LV3_PROD_LIST_CODE
       , T1.LV4_PROD_LIST_CODE
       , T1.LV0_PROD_LIST_CN_NAME
       , T1.LV1_PROD_LIST_CN_NAME
       , T1.LV2_PROD_LIST_CN_NAME
       , T1.LV3_PROD_LIST_CN_NAME
       , T1.LV4_PROD_LIST_CN_NAME
       , T1.OVERSEA_FLAG
       , T1.REGION_CODE
       , T1.REGION_CN_NAME
       , T1.REPOFFICE_CODE
       , T1.REPOFFICE_CN_NAME
       , T1.SIGN_TOP_CUST_CATEGORY_CODE
       , T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
       , T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , T1.SPART_CODE
       , T1.SPART_CN_NAME
       , T1.VIEW_FLAG
       , T1.USD_PNP_AMT
       , T1.RMB_PNP_AMT
       , T1.USD_PNP_AVG
       , T1.RMB_PNP_AVG
    FROM '||V_FROM_TABLE||'  t1
	JOIN PRICE_TOP_SPART_INFO_TMP T2   
      ON T1.BG_CODE = T2.BG_CODE
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
     AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
     AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
     AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
     AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
     AND T1.SPART_CODE = T2.TOP_SPART_CODE
	 AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND NVL(T1.OVERSEA_FLAG,''A'') = NVL(T2.OVERSEA_FLAG,''A'')
     AND NVL(T1.REGION_CODE,''A'') = NVL(T2.REGION_CODE,''A'')
     AND NVL(T1.REPOFFICE_CODE,''A'') = NVL(T2.REPOFFICE_CODE,''A'')
     AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,''A'') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,''A'')
     AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''A'') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''A'')
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
   WHERE T1.DEL_FLAG = ''N''
     AND T1.PERIOD_YEAR >= '||V_PERIOD_YEAR||'
     AND T1.PERIOD_ID < CAST(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM'') AS BIGINT)
     AND T1.VERSION_ID = '||V_VERSION_ID||'
  ';

  RAISE NOTICE'22222222222';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '从来源表取数入到临时表',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );

  -- 创建合同层临时表,用于接受全会计期结果
  DROP TABLE IF EXISTS ACTUAL_APD_TEMP;
  CREATE TEMPORARY TABLE ACTUAL_APD_TEMP(
			   PERIOD_YEAR                        BIGINT
       , PERIOD_ID                          BIGINT
       , BG_CODE                            VARCHAR(50)
       , BG_CN_NAME                         VARCHAR(200)
       , LV0_PROD_LIST_CODE                 VARCHAR(50)
       , LV1_PROD_LIST_CODE                 VARCHAR(50)
       , LV2_PROD_LIST_CODE                 VARCHAR(50)
       , LV3_PROD_LIST_CODE                 VARCHAR(50)
       , LV4_PROD_LIST_CODE                 VARCHAR(50)
       , LV0_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV1_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV2_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV3_PROD_LIST_CN_NAME              VARCHAR(200)
       , LV4_PROD_LIST_CN_NAME              VARCHAR(200)
       , OVERSEA_FLAG                       VARCHAR(10)
       , REGION_CODE                        VARCHAR(50)
       , REGION_CN_NAME                     VARCHAR(200)
       , REPOFFICE_CODE                     VARCHAR(50)
       , REPOFFICE_CN_NAME                  VARCHAR(200)
       , SIGN_TOP_CUST_CATEGORY_CODE        VARCHAR(50)
       , SIGN_TOP_CUST_CATEGORY_CN_NAME     VARCHAR(200)
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME   VARCHAR(200)
       , SPART_CODE                         VARCHAR(50)
       , SPART_CN_NAME                      VARCHAR(2000)
       , VIEW_FLAG                          VARCHAR(50)
       , APPEND_FLAG                        VARCHAR(5)
       , NULL_FLAG                          VARCHAR(2)
       , USD_PNP_AMT                        NUMERIC
       , RMB_PNP_AMT                        NUMERIC
       , USD_PNP_AVG                        NUMERIC
       , RMB_PNP_AVG                        NUMERIC
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE)
  ;

	RAISE NOTICE'88888888888';

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '合同层临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );


  V_SQL := '
  INSERT INTO ACTUAL_APD_TEMP(
         PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
       , APPEND_FLAG
       , NULL_FLAG
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , USD_PNP_AVG
       , RMB_PNP_AVG
  )
  WITH ACTUAL_ITEM_TEMP AS(
  SELECT DISTINCT BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
    FROM ALL_ACTUAL_ITEM_TEMP
  ),
  -- 生成连续年月的发散维
  CROSS_JOIN_TEMP AS(
  SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR
       , B.PERIOD_ID
       , A.BG_CODE
       , A.BG_CN_NAME
       , A.lv0_prod_list_code
       , A.LV1_PROD_LIST_CODE
       , A.LV2_PROD_LIST_CODE
       , A.LV3_PROD_LIST_CODE
       , A.LV4_PROD_LIST_CODE
       , A.LV0_PROD_LIST_CN_NAME
       , A.LV1_PROD_LIST_CN_NAME
       , A.LV2_PROD_LIST_CN_NAME
       , A.LV3_PROD_LIST_CN_NAME
       , A.LV4_PROD_LIST_CN_NAME
       , A.OVERSEA_FLAG
       , A.REGION_CODE
       , A.REGION_CN_NAME
       , A.REPOFFICE_CODE
       , A.REPOFFICE_CN_NAME
       , A.SIGN_TOP_CUST_CATEGORY_CODE
       , A.SIGN_TOP_CUST_CATEGORY_CN_NAME
       , A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , A.SPART_CODE
       , A.SPART_CN_NAME
       , A.VIEW_FLAG
    FROM ACTUAL_ITEM_TEMP A, PERIOD_DIM_TEMP B
  )
  SELECT A.PERIOD_YEAR
       , A.PERIOD_ID
       , A.BG_CODE
       , A.BG_CN_NAME
       , A.LV0_PROD_LIST_CODE
       , A.LV1_PROD_LIST_CODE
       , A.LV2_PROD_LIST_CODE
       , A.LV3_PROD_LIST_CODE
       , A.LV4_PROD_LIST_CODE
       , A.LV0_PROD_LIST_CN_NAME
       , A.LV1_PROD_LIST_CN_NAME
       , A.LV2_PROD_LIST_CN_NAME
       , A.LV3_PROD_LIST_CN_NAME
       , A.LV4_PROD_LIST_CN_NAME
       , A.OVERSEA_FLAG
       , A.REGION_CODE
       , A.REGION_CN_NAME
       , A.REPOFFICE_CODE
       , A.REPOFFICE_CN_NAME
       , A.SIGN_TOP_CUST_CATEGORY_CODE
       , A.SIGN_TOP_CUST_CATEGORY_CN_NAME
       , A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , A.SPART_CODE
       , A.SPART_CN_NAME
       , A.VIEW_FLAG
       , DECODE(B.RMB_PNP_AMT, NULL, ''Y'', ''N'') AS APPEND_FLAG --补齐标识：Y为补齐，N为原始
			 , DECODE(B.RMB_PNP_AMT, NULL, 0, 1) AS NULL_FLAG --空标识, 用于sum开窗累计
			 , B.USD_PNP_AMT
       , B.RMB_PNP_AMT
       , B.USD_PNP_AVG
       , B.RMB_PNP_AVG
    FROM CROSS_JOIN_TEMP A
    LEFT JOIN ALL_ACTUAL_ITEM_TEMP B
      ON NVL(A.LV0_PROD_LIST_CODE,''A'' ) = NVL(B.LV0_PROD_LIST_CODE,''A'')
     AND A.PERIOD_ID = B.PERIOD_ID
     AND NVL(A.BG_CODE,''A'' ) = NVL(B.BG_CODE,''A'')
     AND NVL(A.LV0_PROD_LIST_CODE,''A'' ) = NVL(B.LV0_PROD_LIST_CODE,''A'')
     AND NVL(A.LV1_PROD_LIST_CODE,''A'' ) = NVL(B.LV1_PROD_LIST_CODE,''A'')
     AND NVL(A.LV2_PROD_LIST_CODE,''A'' ) = NVL(B.LV2_PROD_LIST_CODE,''A'')
     AND NVL(A.LV3_PROD_LIST_CODE,''A'' ) = NVL(B.LV3_PROD_LIST_CODE,''A'')
     AND NVL(A.LV4_PROD_LIST_CODE,''A'' ) = NVL(B.LV4_PROD_LIST_CODE,''A'')
     AND NVL(A.OVERSEA_FLAG,''A'' ) = NVL(B.OVERSEA_FLAG,''A'')
     AND NVL(A.REGION_CODE,''A'')  = NVL(B.REGION_CODE,''A'')
     AND NVL(A.REPOFFICE_CODE,''A'')  = NVL(B.REPOFFICE_CODE,''A'')
     AND NVL(A.SIGN_TOP_CUST_CATEGORY_CODE,''A'')  = NVL(B.SIGN_TOP_CUST_CATEGORY_CODE,''A'')
     AND NVL(A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''A'')  = NVL(B.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''A'')
     AND NVL(A.SPART_CODE,''A'')  = NVL(B.SPART_CODE,''A'')
     AND NVL(A.VIEW_FLAG,''A'')  = NVL(B.VIEW_FLAG,''A'')
  ';
  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  
  -- 清理目标表数据
  V_SQL := 'TRUNCATE TABLE '||V_TO_TABLE||' ;';

  DBMS_OUTPUT.PUT_LINE(V_SQL);

  EXECUTE IMMEDIATE V_SQL;

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
     F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_MUM,
     F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS'
  );
  
  -- 2.只补齐均价 发货额和发货数量无需补齐
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID                         -- 版本ID
       , PERIOD_YEAR                        -- 会计年
       , PERIOD_ID                          -- 会计月
       , BG_CODE                            -- BG编码
       , BG_CN_NAME                         -- BG中文名称
       , LV0_PROD_LIST_CODE                 -- 产品LV0编码
       , LV1_PROD_LIST_CODE                 -- 产品LV1编码
       , LV2_PROD_LIST_CODE                 -- 产品LV2编码
       , LV3_PROD_LIST_CODE                 -- 产品LV3编码
       , LV4_PROD_LIST_CODE                 -- 产品LV4编码
       , LV0_PROD_LIST_CN_NAME              -- 产品LV0中文名称
       , LV1_PROD_LIST_CN_NAME              -- 产品LV1中文名称
       , LV2_PROD_LIST_CN_NAME              -- 产品LV2中文名称
       , LV3_PROD_LIST_CN_NAME              -- 产品LV3中文名称
       , LV4_PROD_LIST_CN_NAME              -- 产品LV4中文名称
       , OVERSEA_FLAG                       -- 国内海外标识
       , REGION_CODE                        -- 地区部编码
       , REGION_CN_NAME                     -- 地区部名称
       , REPOFFICE_CODE                     -- 代表处编码
       , REPOFFICE_CN_NAME                  -- 代表处名称
       , SIGN_TOP_CUST_CATEGORY_CODE        -- 签约客户_大T系统部编码
       , SIGN_TOP_CUST_CATEGORY_CN_NAME     -- 签约客户_大T系统部名称
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME   -- 签约客户_子网系统部名称
       , SPART_CODE                         -- SPART编码
       , SPART_CN_NAME                      -- SPART中文名称
       , VIEW_FLAG                          -- 视角标识，用于区分不同视角下的数据()
       , APPEND_FLAG                        -- 补齐标识（Y：补齐数据、N：真实数据）
       , APPEND_PERIOD                      -- 补齐月份
       , USD_PNP_AMT                        -- PNP(CNP)_美元
       , RMB_PNP_AMT                        -- PNP(CNP)_人民币
       , USD_PNP_AVG                        -- PNP(CNP)均价_美元
       , RMB_PNP_AVG                        -- PNP(CNP)均价_人民币
       , CREATED_BY                         -- 创建人
       , CREATION_DATE                      -- 创建时间
       , LAST_UPDATED_BY                    -- 修改人
       , LAST_UPDATE_DATE                   -- 修改时间
       , DEL_FLAG                           -- 删除标识(未删除：N，已删除：Y)
  )
  -- 按照重量级团队 采购信息维补齐 前向补齐均价
  WITH FORWARD_FILLER_TEMP1 AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , USD_PNP_AVG
       , RMB_PNP_AVG
			 , SUM(A.NULL_FLAG) OVER(PARTITION BY PERIOD_YEAR,BG_CODE,LV0_PROD_LIST_CODE ,LV1_PROD_LIST_CODE ,LV2_PROD_LIST_CODE ,LV3_PROD_LIST_CODE ,LV4_PROD_LIST_CODE ,OVERSEA_FLAG,REGION_CODE,
						REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,SPART_CODE,VIEW_FLAG ORDER BY PERIOD_ID) AS AVG_AMT_FLAG  --均价标识: 为空不参与累计加1
       , VIEW_FLAG
       , APPEND_FLAG
    FROM ACTUAL_APD_TEMP A
  ),
  FORWARD_FILLER_TEMP AS(
  SELECT PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , USD_PNP_AMT
       , RMB_PNP_AMT
       , USD_PNP_AVG
       , RMB_PNP_AVG
			 , FIRST_VALUE(T.USD_PNP_AVG) OVER(PARTITION BY PERIOD_YEAR,BG_CODE,lv0_prod_list_code ,LV1_PROD_LIST_CODE ,LV2_PROD_LIST_CODE ,LV3_PROD_LIST_CODE ,LV4_PROD_LIST_CODE ,OVERSEA_FLAG,REGION_CODE,
						REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,SPART_CODE, AVG_AMT_FLAG, VIEW_FLAG ORDER BY T.PERIOD_ID) AS USD_PNP_AVG_2 --新补齐的均价字段
			 , FIRST_VALUE(T.RMB_PNP_AVG) OVER(PARTITION BY PERIOD_YEAR,BG_CODE,lv0_prod_list_code ,LV1_PROD_LIST_CODE ,LV2_PROD_LIST_CODE ,LV3_PROD_LIST_CODE ,LV4_PROD_LIST_CODE ,OVERSEA_FLAG,REGION_CODE,
						REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,SPART_CODE, AVG_AMT_FLAG, VIEW_FLAG ORDER BY T.PERIOD_ID) AS RMB_PNP_AVG_2 --新补齐的均价字段
			 , APPEND_FLAG
			 , AVG_AMT_FLAG
			 , VIEW_FLAG
    FROM FORWARD_FILLER_TEMP1 T
  ),
  FORWARD_FILLER_TEMP2 as(
  SELECT DISTINCT A.BG_CODE,
							A.LV0_PROD_LIST_CODE ,
							A.LV1_PROD_LIST_CODE ,
							A.LV2_PROD_LIST_CODE ,
							A.LV3_PROD_LIST_CODE ,
							A.LV4_PROD_LIST_CODE ,
							A.OVERSEA_FLAG ,
							A.REGION_CODE ,
							A.REPOFFICE_CODE ,
							A.SIGN_TOP_CUST_CATEGORY_CODE ,
							A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME ,
							A.SPART_CODE ,
							A.VIEW_FLAG,
							A.PERIOD_YEAR,
              FIRST_VALUE(A.PERIOD_ID) OVER(PARTITION BY A.PERIOD_YEAR,A.BG_CODE,A.LV0_PROD_LIST_CODE ,A.LV1_PROD_LIST_CODE ,A.LV2_PROD_LIST_CODE ,A.LV3_PROD_LIST_CODE ,A.LV4_PROD_LIST_CODE ,A.OVERSEA_FLAG,A.REGION_CODE,
													A.REPOFFICE_CODE,A.SIGN_TOP_CUST_CATEGORY_CODE,A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,A.SPART_CODE,A.VIEW_FLAG ORDER BY A.PERIOD_ID ASC) AS APD_PERIOD_ID, --有均价的首条会计期
						  FIRST_VALUE(A.USD_PNP_AVG_2) OVER(PARTITION BY A.PERIOD_YEAR,A.BG_CODE,A.LV0_PROD_LIST_CODE ,A.LV1_PROD_LIST_CODE ,A.LV2_PROD_LIST_CODE ,A.LV3_PROD_LIST_CODE ,A.LV4_PROD_LIST_CODE ,A.OVERSEA_FLAG,A.REGION_CODE,
													A.REPOFFICE_CODE,A.SIGN_TOP_CUST_CATEGORY_CODE,A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,A.SPART_CODE,A.VIEW_FLAG ORDER BY A.PERIOD_ID ASC) AS USD_PNP_AVG_3, --有均价的首条补齐均价
              FIRST_VALUE(A.RMB_PNP_AVG_2) OVER(PARTITION BY A.PERIOD_YEAR,A.BG_CODE,A.LV0_PROD_LIST_CODE ,A.LV1_PROD_LIST_CODE ,A.LV2_PROD_LIST_CODE ,A.LV3_PROD_LIST_CODE ,A.LV4_PROD_LIST_CODE ,A.OVERSEA_FLAG,A.REGION_CODE,
												  A.REPOFFICE_CODE,A.SIGN_TOP_CUST_CATEGORY_CODE,A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,A.SPART_CODE,A.VIEW_FLAG ORDER BY A.PERIOD_ID ASC) AS RMB_PNP_AVG_3 --有均价的首条补齐均价
         FROM FORWARD_FILLER_TEMP A
        WHERE A.AVG_AMT_FLAG > 0
  ),
  FORWARD_FILLER_TEMP3 as(
  SELECT
				A.PERIOD_YEAR ,
				A.PERIOD_ID ,
				A.BG_CODE,
        A.BG_CN_NAME,
				A.LV0_PROD_LIST_CODE ,
				A.LV1_PROD_LIST_CODE ,
				A.LV2_PROD_LIST_CODE ,
				A.LV3_PROD_LIST_CODE ,
				A.LV4_PROD_LIST_CODE ,
				A.LV0_PROD_LIST_CN_NAME ,
				A.LV1_PROD_LIST_CN_NAME ,
				A.LV2_PROD_LIST_CN_NAME ,
				A.LV3_PROD_LIST_CN_NAME ,
				A.LV4_PROD_LIST_CN_NAME ,
				A.OVERSEA_FLAG ,
				A.REGION_CODE ,
				A.REGION_CN_NAME ,
				A.REPOFFICE_CODE ,
				A.REPOFFICE_CN_NAME ,
				A.SIGN_TOP_CUST_CATEGORY_CODE ,
				A.SIGN_TOP_CUST_CATEGORY_CN_NAME ,
				A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME ,
				A.SPART_CODE ,
				A.SPART_CN_NAME ,
				A.VIEW_FLAG,
				A.USD_PNP_AMT,
				A.RMB_PNP_AMT,
				A.USD_PNP_AVG_2,
				B.USD_PNP_AVG_3,
				APPEND_FLAG,
        A.RMB_PNP_AVG_2,
        B.RMB_PNP_AVG_3,
        B.APD_PERIOD_ID
   FROM FORWARD_FILLER_TEMP A
   LEFT JOIN FORWARD_FILLER_TEMP2 B
     ON NVL(A.lv0_prod_list_code,''A'' ) = NVL(B.lv0_prod_list_code,''A'')
					AND A.PERIOD_YEAR = B.PERIOD_YEAR
					AND A.PERIOD_ID < B.APD_PERIOD_ID
					AND NVL(A.BG_CODE,''A'' ) = NVL(B.BG_CODE,''A'')
					AND NVL(A.LV0_PROD_LIST_CODE,''A'' ) = NVL(B.LV0_PROD_LIST_CODE,''A'')
					AND NVL(A.LV1_PROD_LIST_CODE,''A'' ) = NVL(B.LV1_PROD_LIST_CODE,''A'')
					AND NVL(A.LV2_PROD_LIST_CODE,''A'' ) = NVL(B.LV2_PROD_LIST_CODE,''A'')
					AND NVL(A.LV3_PROD_LIST_CODE,''A'' ) = NVL(B.LV3_PROD_LIST_CODE,''A'')
					AND NVL(A.LV4_PROD_LIST_CODE,''A'' ) = NVL(B.LV4_PROD_LIST_CODE,''A'')
					AND NVL(A.OVERSEA_FLAG,''A'' ) = NVL(B.OVERSEA_FLAG,''A'')
					AND NVL(A.REGION_CODE,''A'')  = NVL(B.REGION_CODE,''A'')
					AND NVL(A.REPOFFICE_CODE,''A'')  = NVL(B.REPOFFICE_CODE,''A'')
					AND NVL(A.SIGN_TOP_CUST_CATEGORY_CODE,''A'')  = NVL(B.SIGN_TOP_CUST_CATEGORY_CODE,''A'')
					AND NVL(A.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''A'')  = NVL(B.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''A'')
					AND NVL(A.SPART_CODE,''A'')  = NVL(B.SPART_CODE,''A'')
					AND NVL(A.VIEW_FLAG,''A'')  = NVL(B.VIEW_FLAG,''A'')
  )
  -- 向后补齐均价
  SELECT '||V_VERSION_ID||' AS VERSION_ID
       , PERIOD_YEAR
       , PERIOD_ID
       , BG_CODE
       , BG_CN_NAME
       , LV0_PROD_LIST_CODE
       , LV1_PROD_LIST_CODE
       , LV2_PROD_LIST_CODE
       , LV3_PROD_LIST_CODE
       , LV4_PROD_LIST_CODE
       , LV0_PROD_LIST_CN_NAME
       , LV1_PROD_LIST_CN_NAME
       , LV2_PROD_LIST_CN_NAME
       , LV3_PROD_LIST_CN_NAME
       , LV4_PROD_LIST_CN_NAME
       , OVERSEA_FLAG
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SIGN_TOP_CUST_CATEGORY_CODE
       , SIGN_TOP_CUST_CATEGORY_CN_NAME
       , SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
       , SPART_CODE
       , SPART_CN_NAME
       , VIEW_FLAG
       , APPEND_FLAG
       , APD_PERIOD_ID AS APPEND_PERIOD
       , USD_PNP_AMT
       , RMB_PNP_AMT
			 , NVL(A.USD_PNP_AVG_2,A.USD_PNP_AVG_3) AS USD_PNP_AVG
		   , NVL(A.RMB_PNP_AVG_2,A.RMB_PNP_AVG_3) AS RMB_PNP_AVG
       , -1 AS CREATED_BY
			 , CURRENT_TIMESTAMP AS CREATION_DATE
			 , -1 AS LAST_UPDATED_BY
			 , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
			 , ''N'' AS DEL_FLAG
    FROM FORWARD_FILLER_TEMP3 A';

   RAISE NOTICE'33333333333';

   DBMS_OUTPUT.PUT_LINE(V_SQL);

   EXECUTE IMMEDIATE V_SQL;


  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价实际数到'||V_TO_TABLE||'表，数据量：'||SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

