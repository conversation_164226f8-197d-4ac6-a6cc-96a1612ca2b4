-- Name: f_dm_fcst_prod_unit_i_insert_total; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_prod_unit_i_insert_total(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2024年7月29日
创建人  ：李志勇
最后修改时间:
最后修改人:
背景描述：1.psp成本增量集成数据插入历史汇总表和正式表
参数描述：x_result_status ：是否成功
事例：select fin_dm_opt_foi.f_dm_fcst_prod_unit_i_insert_total()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_PROD_UNIT_I_INSERT_TOTAL'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_LAST_MONTH BIGINT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1),'YYYYMM') AS BIGINT); --当前系统月份的上一个月
--  V_TIMESTAMP  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISSFF'); --当前current_timestamp的时间戳

BEGIN
  X_RESULT_STATUS = 'SUCCESS';

  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --1.删除目标表会计期等于psp集成表会计期的数据
 -- DELETE FROM fin_dm_opt_foi.fcst_dwl_prod_prod_unit_i WHERE PERIOD_ID IN (SELECT  DISTINCT period_id FROM fcst_dwl_prod_prod_unit_i_temp ) ;
  --DELETE FROM fin_dm_opt_foi.dwl_prod_prod_unit_i WHERE PERIOD_ID IN (SELECT  DISTINCT period_id FROM fcst_dwl_prod_prod_unit_i_temp ) ;
    EXECUTE IMMEDIATE   'TRUNCATE TABLE fin_dm_opt_foi.dwl_prod_prod_unit_i';

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除psp成本正式表的数据完成',
   --F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --2.从临时表增量刷数到历史汇总表,方案变化，可删除
--  INSERT INTO fin_dm_opt_foi.fcst_dwl_prod_prod_unit_i
--  (
--      p_flag ,
--      period_id ,
--      scenario ,
--      prod_key ,
--      prod_code ,
--      contract_key ,
--      hw_contract_num ,
--      geo_pc_key ,
--      end_cust_key ,
--      spart_code ,
--      rmb_fact_rate_amt ,
--      usd_fact_rate_amt ,
--      part_qty ,
--      prod_unit_qty ,
--      dimension_key ,
--      main_dimension_flag ,
--      self_prod_and_sales_flag ,
--      dw_last_update_date
--  )
--  SELECT
--      p_flag,
--      period_id,
--      scenario,
--      prod_key,
--      prod_code,
--      contract_key,
--      hw_contract_num,
--      geo_pc_key,
--      end_cust_key,
--      spart_code,
--      rmb_fact_rate_amt,
--      usd_fact_rate_amt,
--      part_qty,
--      prod_unit_qty,
--      dimension_key,
--      main_dimension_flag,
--      self_prod_and_sales_flag,
--      dw_last_update_date
--  FROM
--      fin_dm_opt_foi.fcst_dwl_prod_prod_unit_i_temp;

  --3、正式表
    FOR CUR IN (SELECT DISTINCT PERIOD_ID
  				 FROM fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_I
  				 ORDER BY PERIOD_ID ASC) LOOP
  INSERT INTO fin_dm_opt_foi.dwl_prod_prod_unit_i
  (
      p_flag ,
      period_id ,
      scenario ,
      prod_key ,
      prod_code ,
      contract_key ,
      hw_contract_num ,
      geo_pc_key ,
      end_cust_key ,
      spart_code ,
      rmb_fact_rate_amt ,
      usd_fact_rate_amt ,
      part_qty ,
      prod_unit_qty ,
      dimension_key ,
      main_dimension_flag ,
      self_prod_and_sales_flag ,
      dw_last_update_date
  )

  SELECT
      p_flag,
      period_id,
      scenario,
      prod_key,
      prod_code,
      contract_key,
      hw_contract_num,
      geo_pc_key,
      end_cust_key,
      spart_code,
      rmb_fact_rate_amt,
      usd_fact_rate_amt,
      part_qty,
      prod_unit_qty,
      dimension_key,
      main_dimension_flag,
      self_prod_and_sales_flag,
      dw_last_update_date
  FROM
      fin_dm_opt_foi.fcst_dwl_prod_prod_unit_i p
          WHERE P.PERIOD_ID = CUR.PERIOD_ID;


    --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '从fin_dm_opt_foi.fcst_dwl_prod_prod_unit_i 取月份为: ' || CUR.PERIOD_ID ||'的数据到全量表fin_dm_opt_foi.dwl_prod_prod_unit_i完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  END LOOP;

  --3.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入完成',
   --F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE fin_dm_opt_foi.fcst_dwl_prod_prod_unit_i';
  EXECUTE IMMEDIATE 'ANALYZE fin_dm_opt_foi.dwl_prod_prod_unit_i';

  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集dwl_prod_prod_unit_i统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';

  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END


$$
/

