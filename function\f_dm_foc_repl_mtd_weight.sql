-- Name: f_dm_foc_repl_mtd_weight; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_mtd_weight(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建人  ：唐钦
  背景描述：根据年均本计算得到替代指数的所有层级权重值
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_MTD_WEIGHT();
*/
DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_MTD_WEIGHT'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_LAST_VERSION_ID BIGINT;
  V_SQL TEXT; --执行语句
  V_PARENT_AMT VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(200);
  V_NOW_PERIOD     INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT);
  V_NOW_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT);     -- 取当前年份（若当月为1月时，即取去年年份）
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY')||'01','YYYYMM');  --当年首月（若当月是1月的时候，即为去年1月）
  V_END_DATE INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY')||'12' AS INT);  --当年12月（若当月是1月的时候，即为去年12月）
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  -- 取上个版本的年度版本号
      SELECT VERSION_ID
      INTO V_LAST_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'CATEGORY'
       AND CAST(REPLACE(VERSION,'-','') AS INT) = V_NOW_PERIOD
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID||'，取得最大年份：'||V_NOW_YEAR||'取得上个月版本号为：'||V_LAST_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -------------------------------------------------------------------------------- 计算研发替代-月度累计权重值 -------------------------------------------------------------------------------
   
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T WHERE VERSION_ID = '||V_VERSION_ID;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表
     DROP TABLE IF EXISTS FOC_REPL_MTD_BIND_COST_TMP;
     CREATE TEMPORARY TABLE FOC_REPL_MTD_BIND_COST_TMP (
         PERIOD_YEAR          INT,
         PERIOD_ID            INT,
         GROUP_CODE           VARCHAR(100),
         GROUP_CN_NAME        VARCHAR(500),
         GROUP_LEVEL          VARCHAR(50),
         PARENT_CODE          VARCHAR(100),
         PARENT_CN_NAME       VARCHAR(500),
         SHIP_QTY             NUMERIC,
         COST_AMT             NUMERIC,
         VIEW_FLAG            VARCHAR(50),
         CALIBER_FLAG         VARCHAR(2)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 取出BINDING组层级的金额数据，进行逻辑处理
  INSERT INTO FOC_REPL_MTD_BIND_COST_TMP(
         PERIOD_YEAR,   
         PERIOD_ID,
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
--         SHIP_QTY,
         COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
    )
  WITH DECODE_AMT_TMP AS(
  SELECT T1.VERSION_ID,
         T1.PERIOD_YEAR, 
         T1.PERIOD_ID,
         T1.REPLACEMENT_GROUP_ID,
         T1.REPLACEMENT_DESCRIPTION,
         T1.VIEW_FLAG,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV3_PROD_RND_TEAM_CODE,
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV3_PROD_RD_TEAM_CN_NAME,
--         T1.REPLACING_CY_SHIP_QTY,
--         T1.RMB_AAA_BINDING_CUR_COST_AMT,
		 T1.RMB_COST_AMT,
         T1.CALIBER_FLAG,
         T1.DEL_FLAG
  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_DECODE_T T1
  WHERE T1.DEL_FLAG = 'N'
  AND PERIOD_YEAR = V_NOW_YEAR
  ),
  FOC_GROUP_AMT_TMP AS(
  SELECT PERIOD_YEAR,   
         PERIOD_ID,
         REPLACEMENT_GROUP_ID AS GROUP_CODE,    
         REPLACEMENT_DESCRIPTION AS GROUP_CN_NAME, 
         'BIND' AS GROUP_LEVEL,   
         DECODE(VIEW_FLAG,0,LV0_PROD_RND_TEAM_CODE,1,LV1_PROD_RND_TEAM_CODE,2,LV2_PROD_RND_TEAM_CODE,3,LV3_PROD_RND_TEAM_CODE) AS PARENT_CODE,   
         DECODE(VIEW_FLAG,0,LV0_PROD_RD_TEAM_CN_NAME,1,LV1_PROD_RD_TEAM_CN_NAME,2,LV2_PROD_RD_TEAM_CN_NAME,3,LV3_PROD_RD_TEAM_CN_NAME) AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
  UNION ALL 
  -- LV3层级
  SELECT PERIOD_YEAR,   
         PERIOD_ID,
         LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV3' AS GROUP_LEVEL,   
         LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
	  AND VIEW_FLAG IN ('3')   -- 视角4时，才需要LV3层级数据
  UNION ALL 
  -- LV2层级
  SELECT PERIOD_YEAR,
         PERIOD_ID,  
         LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV2' AS GROUP_LEVEL,   
         LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
	  AND VIEW_FLAG IN ('3','2')   -- 视角4/3时，才需要LV2层级数据
  UNION ALL 
  -- LV1层级
  SELECT PERIOD_YEAR,   
         PERIOD_ID,
         LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV1' AS GROUP_LEVEL,   
         LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N'
	  AND VIEW_FLAG IN ('3','2','1')   -- 视角4/3/2时，才需要LV1层级数据
  UNION ALL 
  -- LV0层级
  SELECT PERIOD_YEAR,   
         PERIOD_ID,
         LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,    
         LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME, 
         'LV0' AS GROUP_LEVEL,   
         LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,   
         LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
--         REPLACING_CY_SHIP_QTY AS SHIP_QTY,
--         REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS COST_AMT,
         RMB_COST_AMT AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
      FROM DECODE_AMT_TMP
      WHERE DEL_FLAG = 'N')
  SELECT PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL, 
         PARENT_CODE,
         PARENT_CN_NAME,
--         SUM(SHIP_QTY) AS SHIP_QTY,
         SUM(COST_AMT) AS COST_AMT,
         VIEW_FLAG,
         CALIBER_FLAG
     FROM FOC_GROUP_AMT_TMP
     GROUP BY PERIOD_YEAR,
              PERIOD_ID,
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL, 
              PARENT_CODE,
              PARENT_CN_NAME,
              VIEW_FLAG,
              CALIBER_FLAG;
      
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将来源表数据按层级卷积汇总后，保存到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 分为2类不同层级，进行循环：1：（'BIND'）、2：（'LV3','LV2','LV1','LV0'）
  FOR LOOP_NUM IN 0 .. 1 LOOP
  IF LOOP_NUM = 0 THEN
      V_PARENT_AMT := '
      SUM(COST_AMT) OVER(PARTITION BY PERIOD_YEAR,PERIOD_ID,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG) AS PARENT_AMT, ';
      V_GROUP_LEVEL := ' 
      WHERE GROUP_LEVEL IN (''BIND'') ';  
  ELSIF LOOP_NUM = 1 THEN
      V_PARENT_AMT := '
      SUM(COST_AMT) OVER(PARTITION BY PERIOD_YEAR,PERIOD_ID,GROUP_LEVEL,PARENT_CODE,VIEW_FLAG,CALIBER_FLAG) AS PARENT_AMT, ';
      V_GROUP_LEVEL := ' 
      WHERE GROUP_LEVEL IN (''LV3'',''LV2'',''LV1'',''LV0'') ';   
  END IF;     
  
  -- 计算权重数据
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T(
         VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         CALIBER_FLAG,
         VIEW_FLAG,
         APPEND_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH PARENT_AMT_TMP AS(
  SELECT PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL, 
         PARENT_CODE,
         PARENT_CN_NAME,
         COST_AMT,
         '||V_PARENT_AMT||'
         VIEW_FLAG,
         CALIBER_FLAG
      FROM FOC_REPL_MTD_BIND_COST_TMP
      '||V_GROUP_LEVEL||'
  ),
  -- 生成连续年份, 当年第1月至当前系统月（若当前为1月时，即去年1月至12月）   
  PERIOD_YEAR_TMP AS(     
  --生成连续月份, 当年第1月至当前系统月(不含)
  SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
      AS PERIOD_ID
    FROM GENERATE_SERIES(1,
                          TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                  V_BEGIN_DATE||''',
                                                  CURRENT_TIMESTAMP)),
                          1) NUM(VAL)
               ),    
  -- 生成连续年的发散维
  CONTIN_DIM_TMP AS(
  SELECT DISTINCT SUBSTR(T2.PERIOD_ID,1,4) AS PERIOD_YEAR,
         T2.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL, 
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG
      FROM PARENT_AMT_TMP T1,PERIOD_YEAR_TMP T2
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.PERIOD_ID,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         DECODE(T2.COST_AMT,NULL,0,T2.COST_AMT / NULLIF(T2.PARENT_AMT,0)) AS WEIGHT_RATE,
         T2.COST_AMT AS RMB_COST_AMT,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         DECODE(T2.COST_AMT,NULL,''Y'',''N'') AS APPEND_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM CONTIN_DIM_TMP T1
      LEFT JOIN PARENT_AMT_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
	  AND T1.PERIOD_ID = T2.PERIOD_ID 
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      ';

     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;   
     DBMS_OUTPUT.PUT_LINE('第'||LOOP_NUM||'次循环，权重计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||LOOP_NUM||'次循环，插入版本号为：'||V_VERSION_ID||' 的权重数据到FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
   END LOOP;

  -------------------------------------------------------------------------------- 取同编码-月度累计权重值 -------------------------------------------------------------------------------
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MTD_WEIGHT_T WHERE VERSION_ID = '||V_VERSION_ID;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MTD_WEIGHT_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 同编码-总成本
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MTD_WEIGHT_T(
         VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         RMB_COST_AMT,
         PERCENTAGE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CALIBER_FLAG,
         COST_TYPE,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         APPEND_FLAG
    )
  -- 取最新版本的同编码年度权重，作为上个月的月度累计权重值
  SELECT V_VERSION_ID AS VERSION_ID,
         CAST(PERIOD_YEAR AS INT) AS PERIOD_YEAR,
         CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT) AS PERIOD_ID,  
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         RMB_COST_AMT,
         PERCENTAGE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CALIBER_FLAG,
         COST_TYPE,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         APPEND_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_WEIGHT_T
	  WHERE VERSION_ID = V_VERSION_ID
        AND OVERSEA_FLAG = 'G'
        AND LV0_PROD_LIST_CODE = 'GR'
        AND PERIOD_YEAR = V_NOW_YEAR
        AND GROUP_LEVEL IN ('LV0', 'LV1', 'LV2', 'LV3')
  UNION ALL 
  -- 取上一个版本的月度累计权重表数据，作为历史月份的权重值
  SELECT V_VERSION_ID AS VERSION_ID,
         CAST(PERIOD_YEAR AS INT) AS PERIOD_YEAR,
         CAST(PERIOD_ID AS INT) AS PERIOD_ID,  
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         RMB_COST_AMT,
         PERCENTAGE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CALIBER_FLAG,
         COST_TYPE,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         APPEND_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MTD_WEIGHT_T
	  WHERE VERSION_ID = V_LAST_VERSION_ID
        AND PERIOD_YEAR = V_NOW_YEAR
        AND GROUP_LEVEL IN ('LV0', 'LV1', 'LV2', 'LV3');

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '总成本：插入版本号为：'||V_VERSION_ID||'的数据到DM_FOC_REPL_SAME_MTD_WEIGHT_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -------------------------------------------------------------------------- 标准成本-替代金额占比逻辑 -------------------------------------------------------------------------------------
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_COST_PER_T WHERE VERSION_ID = '||V_VERSION_ID;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_COST_PER_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 创建临时表
     DROP TABLE IF EXISTS FOC_REPL_SAME_MTD_COST_TMP;
     CREATE TEMPORARY TABLE FOC_REPL_SAME_MTD_COST_TMP (
         PERIOD_YEAR          INT,
         PERIOD_ID            INT,
         GROUP_CODE           VARCHAR(100),
         GROUP_CN_NAME        VARCHAR(500),
         GROUP_LEVEL          VARCHAR(50),
         PARENT_CODE          VARCHAR(100),
         PARENT_CN_NAME       VARCHAR(500),
         SAME_COST_AMT        NUMERIC,
         REPL_COST_AMT        NUMERIC,
         VIEW_FLAG            VARCHAR(50),
         CALIBER_FLAG         VARCHAR(2)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建金额占比临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 取出同编码金额的数据和替代指数金额的数据
  INSERT INTO FOC_REPL_SAME_MTD_COST_TMP(
         PERIOD_YEAR,   
         PERIOD_ID,
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
         SAME_COST_AMT,  
         REPL_COST_AMT,
         VIEW_FLAG,     
         CALIBER_FLAG
    )
  WITH SAME_COST_TMP AS(
  -- 取同编码金额整年数据(换表取月度累计权重-20250103)
  SELECT PERIOD_YEAR,   
         PERIOD_ID,
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
         RMB_COST_AMT,  
         VIEW_FLAG,     
         CALIBER_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MTD_WEIGHT_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND COST_TYPE = 'T'   -- 只取总成本的数据
	  AND GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')
	  AND PERIOD_ID BETWEEN V_BEGIN_DATE AND V_END_DATE
	),   -- 限制层级
  REPL_COST_TMP AS(
  -- 取替代指数金额整年数据
  SELECT PERIOD_YEAR,   
         PERIOD_ID,
         GROUP_CODE,    
         GROUP_CN_NAME, 
         GROUP_LEVEL,   
         PARENT_CODE,   
         PARENT_CN_NAME,
         RMB_COST_AMT,  
         VIEW_FLAG,     
         CALIBER_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T   -- 替代指数金额
	  WHERE VERSION_ID = V_VERSION_ID
	  AND GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')   -- 限制层级
	  AND PERIOD_ID BETWEEN V_BEGIN_DATE AND V_END_DATE
  )
  SELECT DECODE(T1.PERIOD_YEAR,NULL,T2.PERIOD_YEAR,T1.PERIOD_YEAR) AS PERIOD_YEAR,   
         DECODE(T1.PERIOD_ID,NULL,T2.PERIOD_ID,T1.PERIOD_ID) AS PERIOD_ID,
         DECODE(T1.GROUP_CODE,NULL,T2.GROUP_CODE,T1.GROUP_CODE) AS GROUP_CODE,    
         DECODE(T1.GROUP_CN_NAME,NULL,T2.GROUP_CN_NAME,T1.GROUP_CN_NAME) AS GROUP_CN_NAME, 
         DECODE(T1.GROUP_LEVEL,NULL,T2.GROUP_LEVEL,T1.GROUP_LEVEL) AS GROUP_LEVEL,   
         DECODE(T1.PARENT_CODE,NULL,T2.PARENT_CODE,T1.PARENT_CODE) AS PARENT_CODE,   
         DECODE(T1.PARENT_CN_NAME,NULL,T2.PARENT_CN_NAME,T1.PARENT_CN_NAME) AS PARENT_CN_NAME,
         T1.RMB_COST_AMT AS SAME_COST_AMT,
		 T2.RMB_COST_AMT AS REPL_COST_AMT,
         DECODE(T1.VIEW_FLAG,NULL,T2.VIEW_FLAG,T1.VIEW_FLAG) AS VIEW_FLAG,     
         DECODE(T1.CALIBER_FLAG,NULL,T2.CALIBER_FLAG,T1.CALIBER_FLAG) AS CALIBER_FLAG 
	  FROM SAME_COST_TMP T1  -- 同编码金额
	  FULL JOIN REPL_COST_TMP T2  -- 替代指数金额
	  ON  T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.GROUP_CODE = T2.GROUP_CODE
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
	  AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	  AND T1.PERIOD_ID = T2.PERIOD_ID;
	  
 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取出同编码金额的数据和替代指数金额的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	 
  -- 计算金额占比数据
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_COST_PER_T(
         VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SAME_COST_AMT,
         REPL_COST_AMT,
         RMB_COST_PER,
         CALIBER_FLAG,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         NVL(SAME_COST_AMT,0) AS SAME_COST_AMT,
         NVL(REPL_COST_AMT,0) AS REPL_COST_AMT,
         NVL(DECODE(SAME_COST_AMT,0,0,REPL_COST_AMT/SAME_COST_AMT),0) AS RMB_COST_PER,
         CALIBER_FLAG,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FOC_REPL_SAME_MTD_COST_TMP;
	  
 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算得到对应编码的金额及金额占比，插入数据到结果表：FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_COST_PER_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T';
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MTD_WEIGHT_T';
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_COST_PER_T';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T/DM_FOC_REPL_SAME_MTD_WEIGHT_T/DM_FOC_REPL_MTD_COST_PER_T表统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

