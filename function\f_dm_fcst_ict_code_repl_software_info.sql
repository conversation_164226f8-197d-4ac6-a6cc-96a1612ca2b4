-- Name: f_dm_fcst_ict_code_repl_software_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_code_repl_software_info(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年9月14日
  创建人  ：唐钦
  背景描述：根据年均本和权重值，计算研发替代指数逻辑
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO();
*/
DECLARE
  V_SP_NAME    VARCHAR2(100) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; -- 年度版本号ID
  V_REPL_VERSION_ID BIGINT; -- 新旧编码替换维表版本号ID
  V_SQL TEXT; --执行语句
  V_PARENT_AMT VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的年度版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  -- 取出对应的年度版本号
  SELECT MAX(VERSION_ID) INTO V_REPL_VERSION_ID   -- 新旧编码替换维表版本号
  FROM
      FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID||'，'||'新旧编码替换维表版本号为：'||V_REPL_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空编码替换新旧编码维表与软硬件标识关联维表的数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T';

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表
  DROP TABLE IF EXISTS FOC_REPL_SOFTWARE_DIM_TMP;
  CREATE TEMPORARY TABLE FOC_REPL_SOFTWARE_DIM_TMP (
         BG_CODE               VARCHAR(50),
         BG_CN_NAME            VARCHAR(200),
         GTS_TYPE              VARCHAR(50),
         LV1_CODE              VARCHAR(50),
         LV1_CN_NAME           VARCHAR(200),
         LV2_CODE              VARCHAR(50),
         LV2_CN_NAME           VARCHAR(200),
         LV3_CODE              VARCHAR(50),
         LV3_CN_NAME           VARCHAR(200),
         LV4_CODE              VARCHAR(50),
         LV4_CN_NAME           VARCHAR(200),
         PROD_CODE             VARCHAR(50),
         PROD_CN_NAME          VARCHAR(200),
         REPLACE_RELATION_NAME VARCHAR(200),
         REPLACE_RELATION_TYPE VARCHAR(200),
         SPART_CODE            VARCHAR(50),
         SPART_DESC            VARCHAR(200),
         CODE_TYPE             VARCHAR(50),
         RELATION_TYPE         VARCHAR(50),
         SOFTWARE_MARK         VARCHAR(50)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 配置管理页面-编码替换新旧编码维表与软硬件标识关联
  INSERT INTO FOC_REPL_SOFTWARE_DIM_TMP (
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         SOFTWARE_MARK
  )
  WITH OLD_NEW_REPL_TMP AS (
  -- 将新旧编码分为多行，并将同一字段内多个编码处理成多行
  SELECT VERSION_ID,
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         REGEXP_SPLIT_TO_TABLE(OLD_SPART_CODE,',') AS SPART_CODE,
         OLD_SPART_DESC AS SPART_DESC,
         'OLD' AS CODE_TYPE,
         RELATION_TYPE
     FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T  /* 新旧编码替换关系表（24年7月版只有销售目录树的）*/
     WHERE DEL_FLAG = 'N'
     AND VERSION_ID = V_REPL_VERSION_ID
     AND GTS_TYPE = 'PROD'    -- （24年7月版只有销售目录树的）
  UNION ALL
  SELECT VERSION_ID,
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         REGEXP_SPLIT_TO_TABLE(NEW_SPART_CODE,',') AS SPART_CODE,
         NEW_SPART_DESC AS SPART_DESC,
         'NEW' AS CODE_TYPE,
         RELATION_TYPE
     FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T  /* 新旧编码替换关系表（24年7月版只有销售目录树的）*/
     WHERE DEL_FLAG = 'N'
     AND VERSION_ID = V_REPL_VERSION_ID
     AND GTS_TYPE = 'PROD'    -- （24年7月版只有销售目录树的）
  ),
  OTHERS_REPL_TMP AS(
  SELECT VERSION_ID,
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         SPART_CODE,
         SPART_DESC,
         CODE_TYPE,
         RELATION_TYPE,
		 CASE WHEN SPART_CODE = 'SNULL' THEN 'OTHERS'
		      WHEN SPART_CODE LIKE '9904%' THEN 'OTHERS'
		 ELSE NULL END AS SOFTWARE_MARK
	  FROM OLD_NEW_REPL_TMP
  )
  -- 用SPART编码关联得到软硬件标识
  SELECT DISTINCT T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.GTS_TYPE,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV2_CODE,
         T1.LV2_CN_NAME,
         T1.LV3_CODE,
         T1.LV3_CN_NAME,
         T1.LV4_CODE,
         T1.LV4_CN_NAME,
         T1.PROD_CODE,
         T1.PROD_CN_NAME,
         T1.REPLACE_RELATION_NAME,
         T1.REPLACE_RELATION_TYPE,
         T1.RELATION_TYPE,
         CASE WHEN UPPER(T2.SOFTWARE_MARK) IN ('SOFTWARE','HARDWARE LICENSE') THEN 'SOFTWARE' 
              WHEN UPPER(T2.SOFTWARE_MARK) IN ('HARDWARE') THEN 'HARDWARE' 
              WHEN T2.SOFTWARE_MARK IS NULL THEN 'HARDWARE' 
         END AS SOFTWARE_MARK
      FROM OTHERS_REPL_TMP T1
      LEFT JOIN DWL_PROD_SPART_ATTR_EXT_I T2
      ON T1.SPART_CODE = T2.PART_CODE
	  WHERE T1.SOFTWARE_MARK IS NULL
  UNION ALL 
  SELECT DISTINCT T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.GTS_TYPE,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV2_CODE,
         T1.LV2_CN_NAME,
         T1.LV3_CODE,
         T1.LV3_CN_NAME,
         T1.LV4_CODE,
         T1.LV4_CN_NAME,
         T1.PROD_CODE,
         T1.PROD_CN_NAME,
         T1.REPLACE_RELATION_NAME,
         T1.REPLACE_RELATION_TYPE,
         T1.RELATION_TYPE,
		 T1.SOFTWARE_MARK
	  FROM OTHERS_REPL_TMP T1
	  WHERE T1.SOFTWARE_MARK IS NOT NULL;
      
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将与软硬件标识关联后的维表数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 将替代ID打上软硬件标识的标签后，插入结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T(
         VERSION_ID,
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         SOFTWARE_MARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH DIS_FLAG_TMP AS(
  -- 按替代ID为最细粒度去重
  SELECT DISTINCT BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         SOFTWARE_MARK,
         COUNT(1) OVER(PARTITION BY BG_CODE,GTS_TYPE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,PROD_CODE,REPLACE_RELATION_NAME,REPLACE_RELATION_TYPE,RELATION_TYPE,SOFTWARE_MARK) AS CNT
      FROM FOC_REPL_SOFTWARE_DIM_TMP
  )
  -- 按替代ID层级去重后，只有一条的数据，保留
  SELECT V_VERSION_ID AS VERSION_ID,
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         SOFTWARE_MARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM DIS_FLAG_TMP
      WHERE CNT = 1
  UNION ALL 
  -- 所有数据都造一份全选
  SELECT V_VERSION_ID AS VERSION_ID,
         BG_CODE,
         BG_CN_NAME,
         GTS_TYPE,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         PROD_CODE,
         PROD_CN_NAME,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         'ALL' AS SOFTWARE_MARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM DIS_FLAG_TMP;
      
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将替代ID打上软硬件标识的标签后，插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T表统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

