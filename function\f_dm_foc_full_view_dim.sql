-- Name: f_dm_foc_full_view_dim; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_full_view_dim(f_industry_flag character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2023-03-26
创建人  ：黄心蕊 hwx1187045
背景描述：页面下拉框国内海外_视角_集团维表刷新
参数描述：参数一(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_FULL_VIEW_DIM(); --一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_FULL_VIEW_DIM';
  V_TABLE_MID_PART VARCHAR(20);
  V_SQL            TEXT;

BEGIN
  
  X_RESULT_STATUS:='1';
  
  IF F_INDUSTRY_FLAG = 'I' THEN
  
    V_TABLE_MID_PART := '';
  
    --日志开始
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME      => V_SP_NAME,
	F_STEP_NUM     => 0,
	F_CAL_LOG_DESC => V_SP_NAME||'开始执行，ICT表开始刷新');
  
  ELSIF F_INDUSTRY_FLAG = 'E' THEN
  
    V_TABLE_MID_PART := 'ENERGY_';
  
    --日志开始
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME      => V_SP_NAME,
	F_STEP_NUM     => 0,
	F_CAL_LOG_DESC => V_SP_NAME||'开始执行，数字能源表开始刷新');
	
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
  
    V_TABLE_MID_PART := 'IAS_';
  
    --日志开始
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	(F_SP_NAME      => V_SP_NAME,
	F_STEP_NUM     => 0,
	F_CAL_LOG_DESC => V_SP_NAME||'开始执行，IAS表开始刷新');
  
  END IF;
   
   
  V_SQL:=' TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'FULL_VIEW_DIM;';
  EXECUTE V_SQL;
  
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '表数据清空完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
   
--总成本
V_SQL:='
INSERT INTO FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'FULL_VIEW_DIM
SELECT DISTINCT ''T'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,
				LV0_PROD_RND_TEAM_CODE,
				LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_VIEW_INFO_D
UNION ALL
SELECT DISTINCT ''T'' AS COST_TYPE,
                ''P'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_PFT_VIEW_INFO_D
UNION ALL
SELECT DISTINCT ''T'' AS COST_TYPE,
                ''D'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'TOTAL_DMS_VIEW_INFO_D
	UNION ALL
	
--采购
	SELECT DISTINCT ''P'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D
	UNION ALL
--采购反向视角4
	SELECT DISTINCT ''P'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                ''4'' AS VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D
 WHERE VIEW_FLAG = 3
	UNION ALL
--采购反向视角5
	SELECT DISTINCT ''P'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                ''5'' AS VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D
 WHERE VIEW_FLAG = 3
 	UNION ALL
--采购反向视角6
	SELECT DISTINCT ''P'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                ''6'' AS VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'VIEW_INFO_D
 WHERE VIEW_FLAG = 3
  
UNION ALL
SELECT DISTINCT ''P'' AS COST_TYPE,
                ''P'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'PFT_VIEW_INFO_D
UNION ALL
SELECT DISTINCT ''P'' AS COST_TYPE,
                ''D'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'DMS_VIEW_INFO_D

UNION ALL
--制造
SELECT DISTINCT ''M'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D
UNION ALL
--制造反向视角5
SELECT DISTINCT ''M'' AS COST_TYPE,
                ''U'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                ''5'' AS VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_VIEW_INFO_D
 WHERE VIEW_FLAG = ''3''
  
UNION ALL
SELECT DISTINCT ''M'' AS COST_TYPE,
                ''P'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_PFT_VIEW_INFO_D
UNION ALL
SELECT DISTINCT ''M'' AS COST_TYPE,
                ''D'' AS DIMENSION_TYPE,
                CALIBER_FLAG,
                VIEW_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME,LV0_PROD_RND_TEAM_CODE,LV0_PROD_RD_TEAM_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOC_'||V_TABLE_MID_PART||'MADE_DMS_VIEW_INFO_D;
';
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '插数完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  
  RETURN 'SUCCESS';

  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

