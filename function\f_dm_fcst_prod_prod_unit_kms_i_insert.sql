-- Name: f_dm_fcst_prod_prod_unit_kms_i_insert; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_prod_prod_unit_kms_i_insert(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：1.从贴源层的临时表增量插入到全量历史汇总表，以及将数据并插入正式表并添加主键
参数描述：x_result_status ：是否成功
事例：select fin_dm_opt_foi.f_dm_fcst_prod_prod_unit_kms_i_insert()
全量历史汇总表 : DWL_PROD_PROD_UNIT_KMS_I
正式表: FCST_DWL_PROD_PROD_UNIT_KMS_I
集成表: FCSt_DWL_PROD_PROD_UNIT_KMS_I_temp
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_PROD_PROD_UNIT_KMS_I_INSERT'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_LAST_MONTH BIGINT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1),'YYYYMM') AS BIGINT); --当前系统月份的上一个月
--  V_TIMESTAMP  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISSFF'); --当前current_timestamp的时间戳\
 -- V_TIMESTAMP  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISSFF'); --当前current_timestamp的时间戳

BEGIN
  X_RESULT_STATUS = 'SUCCESS';

  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --1.删除目标表会计期等于上集成表会计期的数据
  DELETE FROM FIN_DM_OPT_FOI.FCST_DWL_PROD_PROD_UNIT_KMS_I WHERE PERIOD_ID = V_LAST_MONTH;
  DELETE FROM FIN_DM_OPT_FOI.DWL_PROD_PROD_UNIT_KMS_I WHERE PERIOD_ID = V_LAST_MONTH;

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除目标表数据完成',
   --F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


  --2.1 将集成表数据插入汇总表数据（汇总表没有主键）
  INSERT INTO fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_KMS_I
  (
      p_flag ,
      period_id ,
      scenario ,
      prod_key ,
      prod_code ,
      contract_key ,
      hw_contract_num ,
      geo_pc_key ,
      end_cust_key ,
      spart_code ,
      rmb_fact_rate_amt ,
      usd_fact_rate_amt ,
      part_qty ,
      prod_unit_qty ,
      dimension_key ,
      main_dimension_flag ,
      self_prod_and_sales_flag ,
      dw_last_update_date
  )

  SELECT
      p_flag,
      period_id,
      scenario,
      prod_key,
      prod_code,
      contract_key,
      hw_contract_num,
      geo_pc_key,
      end_cust_key,
      spart_code,
      rmb_fact_rate_amt,
      usd_fact_rate_amt,
      part_qty,
      prod_unit_qty,
      dimension_key,
      main_dimension_flag,
      self_prod_and_sales_flag,
      dw_last_update_date
  FROM
      fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_KMS_I_temp
           WHERE PERIOD_ID = V_LAST_MONTH;



  --2.从临时表增量刷数到全量表, 并生成主键
  INSERT INTO fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I
  (
      p_flag ,
      period_id ,
      scenario ,
      prod_key ,
      prod_code ,
      contract_key ,
      hw_contract_num ,
      geo_pc_key ,
      end_cust_key ,
      spart_code ,
      rmb_fact_rate_amt ,
      usd_fact_rate_amt ,
      part_qty ,
      prod_unit_qty ,
      dimension_key ,
      main_dimension_flag ,
      self_prod_and_sales_flag ,
      dw_last_update_date
  )

  SELECT
      p_flag,
      period_id,
      scenario,
      prod_key,
      prod_code,
      contract_key,
      hw_contract_num,
      geo_pc_key,
      end_cust_key,
      spart_code,
      rmb_fact_rate_amt,
      usd_fact_rate_amt,
      part_qty,
      prod_unit_qty,
      dimension_key,
      main_dimension_flag,
      self_prod_and_sales_flag,
      dw_last_update_date
  --NEXTVAL('fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_S') || V_TIMESTAMP --主键值=序列+时间戳
  FROM
      fin_dm_opt_foi.fcst_dwl_prod_prod_unit_kms_i_temp
                     WHERE PERIOD_ID = V_LAST_MONTH;


  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  ANALYZE fin_dm_opt_foi.FCST_DWL_PROD_PROD_UNIT_KMS_I;
  ANALYZE fin_dm_opt_foi.DWL_PROD_PROD_UNIT_KMS_I;

  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';

  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

