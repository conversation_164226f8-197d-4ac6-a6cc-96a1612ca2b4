-- Name: f_dm_foi_point_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_point_amp(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建人  ：唐钦
  背景描述：点降图数据计算
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_POINT_AMP()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_POINT_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
  V_FROM_TABLE1 VARCHAR(100);
  V_FROM_TABLE2 VARCHAR(100);
  V_TO_TABLE    VARCHAR(100);
  V_PARAMETER   VARCHAR(30);
  V_AMT_PARA    VARCHAR(30);
  V_JOIN_PARAMETER  TEXT;
  V_SQL  TEXT;
  V_INSERT_SQL TEXT ;
  V_CURRENT_FLAG INT;
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(200);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_VERSION_TABLE VARCHAR(200);
  V_BASE_PERIOD_ID INT;
  
  BEGIN
    X_RESULT_STATUS = '1';

  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --判断入参是ICT还/数字能源/IAS
  IF f_caliber_flag = 'I' THEN   -- ICT
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
    V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T';
    V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_T';
    V_PARAMETER := ' CONTINUITY_TYPE ';
    V_AMT_PARA := 'AVG_RECEIVE_AMT ';
    V_JOIN_PARAMETER := ' AND NVL(T1.CONTINUITY_TYPE,''SNULL'') = NVL(T2.CONTINUITY_TYPE,''SNULL'') ';
  ELSIF f_caliber_flag = 'E' THEN   --202403版本新增数字能源
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
    V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T';
    V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_POINT_AMP_T';
    V_PARAMETER := ' GROUP_PUR_FLAG ';
    V_AMT_PARA := 'AVG_RECEIVE_AMT ';
    V_JOIN_PARAMETER := ' AND NVL(T1.GROUP_PUR_FLAG,''SNULL'') = NVL(T2.GROUP_PUR_FLAG,''SNULL'') ';
  ELSIF f_caliber_flag IN ('IAS','EAST_CHINA_PQC') THEN   -- 202407版本新增IAS/华东采购
    V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ITEM_SUP_AVG_T';
    V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_PRICE_INDEX_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_POINT_AMP_T';
    V_PARAMETER := ' GROUP_PUR_FLAG ';
    V_AMT_PARA := 'AVG_RECEIVE_AMT ';
    V_JOIN_PARAMETER := ' AND NVL(T1.GROUP_PUR_FLAG,''SNULL'') = NVL(T2.GROUP_PUR_FLAG,''SNULL'') ';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := ' AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
  END IF;
  
  -- 取到对应的规格品版本号
 IF F_VERSION_ID IS  NULL  THEN 
    V_SQL := '   
        SELECT VERSION_ID
           FROM '||V_VERSION_TABLE||'
           WHERE
            DEL_FLAG = ''N''
            AND STATUS = 1
            AND UPPER(DATA_TYPE) = ''ITEM''
            AND UPPER(VERSION_TYPE) IN (''AUTO'',''FINAL'')
            ORDER BY LAST_UPDATE_DATE DESC
            LIMIT 1';
     EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
 ELSE V_VERSION_ID := F_VERSION_ID;
 END IF; 
 
  -- 默认基期取值（取价格指数表里本版本最小月份，即是默认基期）
  V_SQL := '
  SELECT MIN(PERIOD_ID)
     FROM '||V_FROM_TABLE2||'
     WHERE VERSION_ID = '||V_VERSION_ID||'
     '||V_SQL_CONDITION;
  EXECUTE IMMEDIATE V_SQL INTO V_BASE_PERIOD_ID;
  
  -- 删除本版本数据
   V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
   EXECUTE IMMEDIATE V_SQL;
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除点降表：'||V_TO_TABLE||' 版本号为：'||V_VERSION_ID||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 点降结果数据计算
 V_SQL := ' INSERT INTO '||V_TO_TABLE|| '( 
                VERSION_ID,
                YEAR,
                PERIOD_ID,
                BASE_PERIOD_ID,
                CATEGORY_CODE,
                CATEGORY_NAME,
                L4_CEG_CODE,
                L4_CEG_SHORT_CN_NAME,
                L4_CEG_CN_NAME,
                L3_CEG_CODE,
                L3_CEG_SHORT_CN_NAME,
                L3_CEG_CN_NAME,
                L2_CEG_CODE,
                L2_CEG_CN_NAME,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                POINT_AMP,
                PARENT_CODE,
                PARENT_CN_NAME ,                
                '||V_CALIBER||'
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                '||V_PARAMETER||'
                ) 
 WITH FOI_COMPUTE_TMP AS(
 
 -- ITEM层级的月均本数据  
   SELECT PERIOD_ID,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          '||V_AMT_PARA||' AS COMPUTE_VALUE,  -- 作为计算值
          CATEGORY_CODE,
          CATEGORY_NAME,
          L4_CEG_CODE,
          L4_CEG_SHORT_CN_NAME,
          L4_CEG_CN_NAME,
          L3_CEG_CODE,
          L3_CEG_SHORT_CN_NAME,
          L3_CEG_CN_NAME,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          PARENT_CODE,
          PARENT_CN_NAME,
          NULL AS '||V_PARAMETER||'
       FROM '||V_FROM_TABLE1||'
       WHERE VERSION_ID = '||V_VERSION_ID||
       ' AND GROUP_LEVEL = ''ITEM''
       '||V_SQL_CONDITION||'
   UNION ALL
 -- 除ITEM层级之外其余层级的指数数据
   SELECT PERIOD_ID,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          PRICE_INDEX AS COMPUTE_VALUE,  -- 作为计算值
          NULL AS CATEGORY_CODE,
          NULL AS CATEGORY_NAME,
          L4_CEG_CODE,
          L4_CEG_SHORT_CN_NAME,
          L4_CEG_CN_NAME,
          L3_CEG_CODE,
          L3_CEG_SHORT_CN_NAME,
          L3_CEG_CN_NAME,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          PARENT_CODE,
          PARENT_CN_NAME,
          '||V_PARAMETER||'
       FROM '||V_FROM_TABLE2||'
       WHERE VERSION_ID = '||V_VERSION_ID||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
       AND GROUP_LEVEL IN (''CATEGORY'',''LV4'',''LV3'',''LV2'')  -- 限制层级为：品类/模块/专家团/ICT
       '||V_SQL_CONDITION||'
         )
   SELECT '||V_VERSION_ID||' AS VERSION_ID,
          SUBSTR(T1.PERIOD_ID,1,4) AS YEAR,
          T1.PERIOD_ID,
          '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
          T1.CATEGORY_CODE,
          T1.CATEGORY_NAME,
          T1.L4_CEG_CODE,
          T1.L4_CEG_SHORT_CN_NAME,
          T1.L4_CEG_CN_NAME,
          T1.L3_CEG_CODE,
          T1.L3_CEG_SHORT_CN_NAME,
          T1.L3_CEG_CN_NAME,
          T1.L2_CEG_CODE,
          T1.L2_CEG_CN_NAME,
          T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
          T1.GROUP_LEVEL, 
          NVL((T1.COMPUTE_VALUE/NULLIF(T2.BASE_COMPUTE_VALUE,0)-1)*100,0) AS POINT_AMP ,  -- 计算点降结果值
          T1.PARENT_CODE,
          T1.PARENT_CN_NAME,
          '||V_IN_CALIBER||'
          -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         T1.'||V_PARAMETER||'
       FROM FOI_COMPUTE_TMP T1
       LEFT JOIN (
                  SELECT PERIOD_ID,
                         GROUP_CODE,
                         GROUP_LEVEL,
                         COMPUTE_VALUE AS BASE_COMPUTE_VALUE,  -- 取默认基期的数据
                         PARENT_CODE,
                         PARENT_CN_NAME,
                         '||V_PARAMETER||'
                      FROM FOI_COMPUTE_TMP 
                      WHERE PERIOD_ID = '||V_BASE_PERIOD_ID||'  
                 ) T2
       ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       AND NVL(T1.PARENT_CODE,''SNULL0'') = NVL(T2.PARENT_CODE,''SNULL0'')
       '||V_JOIN_PARAMETER;
       EXECUTE IMMEDIATE V_SQL;

   
     --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||' 的权重数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');     
     
     --收集统计信息
 V_SQL := ' ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

END$$
/

