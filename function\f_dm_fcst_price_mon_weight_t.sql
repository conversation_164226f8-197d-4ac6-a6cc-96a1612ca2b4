-- Name: f_dm_fcst_price_mon_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mon_weight_t(f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 
/***************************************************************************************************************************************************************
创建时间: 20241108
创建人  : 黄心蕊 hwx1187045
背景描述: 定价指数-权重计算 T年YTD金额占比
参数描述: 参数一:  F_VERSION_ID 版本号
		  参数二:  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败
--来源表
年均本表 DM_FCST_PRICE_ANNL_AVG_T
--目标表
月权重表 DM_FCST_PRICE_MON_WEIGHT_T

SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MON_WEIGHT_T('');
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MON_WEIGHT_T';
  V_VERSION                 BIGINT; --版本号
  V_YEAR					INT;
  V_EXCEPTION_FLAG			INT; --异常步骤
  
BEGIN 

X_RESULT_STATUS := '1';

 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
 V_EXCEPTION_FLAG	:= 0;
--月度版本号取值
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  V_EXCEPTION_FLAG	:= 1;
  
  DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T WHERE VERSION_ID = V_VERSION;
  
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '权重表第 '||V_VERSION||' 版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  V_EXCEPTION_FLAG	:= 2;
  
 if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
  
  WITH BASE_AMT AS
   (SELECT PERIOD_YEAR,
           LV0_PROD_LIST_CODE,
           LV1_PROD_LIST_CODE,
           LV2_PROD_LIST_CODE,
           LV3_PROD_LIST_CODE,
           LV4_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           LV1_PROD_LIST_CN_NAME,
           LV2_PROD_LIST_CN_NAME,
           LV3_PROD_LIST_CN_NAME,
           LV4_PROD_LIST_CN_NAME,
           SPART_CODE,
           SPART_CN_NAME,
           USD_PNP_AMT,
           OVERSEA_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SIGN_TOP_CUST_CATEGORY_CODE,
           SIGN_TOP_CUST_CATEGORY_CN_NAME,
           SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
           VIEW_FLAG,
           BG_CODE,
           BG_CN_NAME
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T
     WHERE PERIOD_YEAR = V_YEAR
	   AND ENABLE_FLAG = 'Y' 
	   ) --取最新月份累积金额,即本年金额
   INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
     (VERSION_ID,
      PERIOD_YEAR,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
	  USD_PNP_AMT,
      WEIGHT_RATE,
	  ABSOLUTE_WEIGHT,
      OVERSEA_FLAG,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      SIGN_TOP_CUST_CATEGORY_CODE,
      SIGN_TOP_CUST_CATEGORY_CN_NAME,
      SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG,
      BG_CODE,
      BG_CN_NAME)
	--SPART/LV4
	SELECT V_VERSION AS VERSION_ID,
		   V_YEAR AS PERIOD_YEAR,
		   SPART_CODE AS GROUP_CODE,
		   SPART_CN_NAME AS GROUP_CN_NAME,
		   'SPART' AS GROUP_LEVEL,
		   USD_PNP_AMT,
		   USD_PNP_AMT /
		   NULLIF(SUM(USD_PNP_AMT) OVER(PARTITION BY LV4_PROD_LIST_CODE,
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS WEIGHT_RATE,
		   USD_PNP_AMT /
		   NULLIF(SUM(USD_PNP_AMT) OVER(PARTITION BY OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS ABSOLUTE_WEIGHT,	--绝对权重
		   OVERSEA_FLAG,
		   REGION_CODE,
		   REGION_CN_NAME,
		   REPOFFICE_CODE,
		   REPOFFICE_CN_NAME,
		   SIGN_TOP_CUST_CATEGORY_CODE,
		   SIGN_TOP_CUST_CATEGORY_CN_NAME,
		   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		   VIEW_FLAG,
		   LV4_PROD_LIST_CODE AS PARENT_CODE,
		   LV4_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
		   -1 AS CREATED_BY,
		   CURRENT_TIMESTAMP AS CREATION_DATE,
		   -1 AS LAST_UPDATED_BY,
		   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		   'N' AS DEL_FLAG,
		   BG_CODE,
		   BG_CN_NAME
	  FROM BASE_AMT
	UNION ALL
	--LV4/LV3
	SELECT V_VERSION AS VERSION_ID,
		   V_YEAR AS PERIOD_YEAR,
		   LV4_PROD_LIST_CODE AS GROUP_CODE,
		   LV4_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
		   'LV4' AS GROUP_LEVEL,
		   SUM(USD_PNP_AMT) AS USD_PNP_AMT,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY LV3_PROD_LIST_CODE,
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS WEIGHT_RATE,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY 
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS ABSOLUTE_WEIGHT,
		   OVERSEA_FLAG,
		   REGION_CODE,
		   REGION_CN_NAME,
		   REPOFFICE_CODE,
		   REPOFFICE_CN_NAME,
		   SIGN_TOP_CUST_CATEGORY_CODE,
		   SIGN_TOP_CUST_CATEGORY_CN_NAME,
		   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		   VIEW_FLAG,
		   LV3_PROD_LIST_CODE AS PARENT_CODE,
		   LV3_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
		   -1 AS CREATED_BY,
		   CURRENT_TIMESTAMP AS CREATION_DATE,
		   -1 AS LAST_UPDATED_BY,
		   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		   'N' AS DEL_FLAG,
		   BG_CODE,
		   BG_CN_NAME
	  FROM BASE_AMT
	 GROUP BY LV4_PROD_LIST_CODE,
			  LV4_PROD_LIST_CN_NAME,
			  OVERSEA_FLAG,
			  REGION_CODE,
			  REGION_CN_NAME,
			  REPOFFICE_CODE,
			  REPOFFICE_CN_NAME,
			  SIGN_TOP_CUST_CATEGORY_CODE,
			  SIGN_TOP_CUST_CATEGORY_CN_NAME,
			  SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
			  VIEW_FLAG,
			  LV3_PROD_LIST_CODE,
			  LV3_PROD_LIST_CN_NAME,
			  BG_CODE,
			  BG_CN_NAME
	UNION ALL
	--LV3/LV2
	SELECT V_VERSION AS VERSION_ID,
		   V_YEAR AS PERIOD_YEAR,
		   LV3_PROD_LIST_CODE AS GROUP_CODE,
		   LV3_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
		   'LV3' AS GROUP_LEVEL,
		   SUM(USD_PNP_AMT) AS USD_PNP_AMT,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY LV2_PROD_LIST_CODE,
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS WEIGHT_RATE,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY 
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS ABSOLUTE_WEIGHT,
		   OVERSEA_FLAG,
		   REGION_CODE,
		   REGION_CN_NAME,
		   REPOFFICE_CODE,
		   REPOFFICE_CN_NAME,
		   SIGN_TOP_CUST_CATEGORY_CODE,
		   SIGN_TOP_CUST_CATEGORY_CN_NAME,
		   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		   VIEW_FLAG,
		   LV2_PROD_LIST_CODE AS PARENT_CODE,
		   LV2_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
		   -1 AS CREATED_BY,
		   CURRENT_TIMESTAMP AS CREATION_DATE,
		   -1 AS LAST_UPDATED_BY,
		   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		   'N' AS DEL_FLAG,
		   BG_CODE,
		   BG_CN_NAME
	  FROM BASE_AMT
	 GROUP BY LV2_PROD_LIST_CODE,
			  LV2_PROD_LIST_CN_NAME,
			  OVERSEA_FLAG,
			  REGION_CODE,
			  REGION_CN_NAME,
			  REPOFFICE_CODE,
			  REPOFFICE_CN_NAME,
			  SIGN_TOP_CUST_CATEGORY_CODE,
			  SIGN_TOP_CUST_CATEGORY_CN_NAME,
			  SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
			  VIEW_FLAG,
			  LV3_PROD_LIST_CODE,
			  LV3_PROD_LIST_CN_NAME,
			  BG_CODE,
			  BG_CN_NAME
	UNION ALL
	--LV2/LV1
	SELECT V_VERSION AS VERSION_ID,
		   V_YEAR AS PERIOD_YEAR,
		   LV2_PROD_LIST_CODE AS GROUP_CODE,
		   LV2_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
		   'LV2' AS GROUP_LEVEL,
		   SUM(USD_PNP_AMT) AS USD_PNP_AMT,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY LV1_PROD_LIST_CODE,
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS WEIGHT_RATE,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY 
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS ABSOLUTE_WEIGHT,
		   OVERSEA_FLAG,
		   REGION_CODE,
		   REGION_CN_NAME,
		   REPOFFICE_CODE,
		   REPOFFICE_CN_NAME,
		   SIGN_TOP_CUST_CATEGORY_CODE,
		   SIGN_TOP_CUST_CATEGORY_CN_NAME,
		   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		   VIEW_FLAG,
		   LV1_PROD_LIST_CODE AS PARENT_CODE,
		   LV1_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
		   -1 AS CREATED_BY,
		   CURRENT_TIMESTAMP AS CREATION_DATE,
		   -1 AS LAST_UPDATED_BY,
		   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		   'N' AS DEL_FLAG,
		   BG_CODE,
		   BG_CN_NAME
	  FROM BASE_AMT
	 GROUP BY LV2_PROD_LIST_CODE,
			  LV2_PROD_LIST_CN_NAME,
			  OVERSEA_FLAG,
			  REGION_CODE,
			  REGION_CN_NAME,
			  REPOFFICE_CODE,
			  REPOFFICE_CN_NAME,
			  SIGN_TOP_CUST_CATEGORY_CODE,
			  SIGN_TOP_CUST_CATEGORY_CN_NAME,
			  SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
			  VIEW_FLAG,
			  LV1_PROD_LIST_CODE,
			  LV1_PROD_LIST_CN_NAME,
			  BG_CODE,
			  BG_CN_NAME
	UNION ALL
	--LV1/LV0
	SELECT V_VERSION AS VERSION_ID,
		   V_YEAR AS PERIOD_YEAR,
		   LV1_PROD_LIST_CODE AS GROUP_CODE,
		   LV1_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
		   'LV1' AS GROUP_LEVEL,
		   SUM(USD_PNP_AMT) AS USD_PNP_AMT,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY LV0_PROD_LIST_CODE,
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS WEIGHT_RATE,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY 
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS ABSOLUTE_WEIGHT,
		   OVERSEA_FLAG,
		   REGION_CODE,
		   REGION_CN_NAME,
		   REPOFFICE_CODE,
		   REPOFFICE_CN_NAME,
		   SIGN_TOP_CUST_CATEGORY_CODE,
		   SIGN_TOP_CUST_CATEGORY_CN_NAME,
		   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		   VIEW_FLAG,
		   LV0_PROD_LIST_CODE AS PARENT_CODE,
		   LV0_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
		   -1 AS CREATED_BY,
		   CURRENT_TIMESTAMP AS CREATION_DATE,
		   -1 AS LAST_UPDATED_BY,
		   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		   'N' AS DEL_FLAG,
		   BG_CODE,
		   BG_CN_NAME
	  FROM BASE_AMT
	 GROUP BY LV1_PROD_LIST_CODE,
			  LV1_PROD_LIST_CN_NAME,
			  OVERSEA_FLAG,
			  REGION_CODE,
			  REGION_CN_NAME,
			  REPOFFICE_CODE,
			  REPOFFICE_CN_NAME,
			  SIGN_TOP_CUST_CATEGORY_CODE,
			  SIGN_TOP_CUST_CATEGORY_CN_NAME,
			  SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
			  VIEW_FLAG,
			  LV0_PROD_LIST_CODE,
			  LV0_PROD_LIST_CN_NAME,
			  BG_CODE,
			  BG_CN_NAME
	UNION ALL
	--LV0
	SELECT V_VERSION AS VERSION_ID,
		   V_YEAR AS PERIOD_YEAR,
		   LV0_PROD_LIST_CODE AS GROUP_CODE,
		   LV0_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
		   'LV0' AS GROUP_LEVEL,
		   SUM(USD_PNP_AMT) AS USD_PNP_AMT,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY LV0_PROD_LIST_CODE,
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS WEIGHT_RATE,
		   SUM(USD_PNP_AMT) /
		   NULLIF(SUM(SUM(USD_PNP_AMT)) OVER(PARTITION BY 
					   OVERSEA_FLAG,
					   REGION_CODE,
					   REPOFFICE_CODE,
					   SIGN_TOP_CUST_CATEGORY_CODE,
					   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
					   VIEW_FLAG,
					   BG_CODE),
				  0) AS ABSOLUTE_WEIGHT,
		   OVERSEA_FLAG,
		   REGION_CODE,
		   REGION_CN_NAME,
		   REPOFFICE_CODE,
		   REPOFFICE_CN_NAME,
		   SIGN_TOP_CUST_CATEGORY_CODE,
		   SIGN_TOP_CUST_CATEGORY_CN_NAME,
		   SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		   VIEW_FLAG,
		   NULL AS PARENT_CODE,
		   NULL AS PARENT_CN_NAME,
		   -1 AS CREATED_BY,
		   CURRENT_TIMESTAMP AS CREATION_DATE,
		   -1 AS LAST_UPDATED_BY,
		   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		   'N' AS DEL_FLAG,
		   BG_CODE,
		   BG_CN_NAME
	  FROM BASE_AMT
	 GROUP BY OVERSEA_FLAG,
			  REGION_CODE,
			  REGION_CN_NAME,
			  REPOFFICE_CODE,
			  REPOFFICE_CN_NAME,
			  SIGN_TOP_CUST_CATEGORY_CODE,
			  SIGN_TOP_CUST_CATEGORY_CN_NAME,
			  SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
			  VIEW_FLAG,
			  LV0_PROD_LIST_CODE,
			  LV0_PROD_LIST_CN_NAME,
			  BG_CODE,
			  BG_CN_NAME;
	
 --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '权重表第 '||V_VERSION||' 版本数据插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

