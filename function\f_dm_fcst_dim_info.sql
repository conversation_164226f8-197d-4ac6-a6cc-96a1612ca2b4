-- Name: f_dm_fcst_dim_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_dim_info(f_cost_type character varying, f_granularity_type character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年10月4日
  创建人  ：唐钦
  背景描述：根据维度关联后的表，取得不同路径下的维度关系数据，并在得到的这版数据的基础上，关联主力编码维表，对数据打上主力编码标签
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_DIM_INFO('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_DIM_INFO'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_BEGIN_NUM INT;
  V_END_NUM INT;
  V_GROUP_LEVEL VARCHAR(50);
  V_VIEW_FLAG VARCHAR(50);
  V_LV0_CODE VARCHAR(50);
  V_LV0_NAME VARCHAR(50);
  V_LV1_CODE VARCHAR(50);
  V_LV1_NAME VARCHAR(50);
  V_LV2_CODE VARCHAR(50);
  V_LV2_NAME VARCHAR(50);
  V_LV3_CODE VARCHAR(50);
  V_LV3_NAME VARCHAR(50);
  V_LV4_CODE VARCHAR(50);
  V_LV4_NAME VARCHAR(50);
  V_DIMENSION_CODE VARCHAR(50);
  V_DIMENSION_NAME VARCHAR(50);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(50);
  V_DIMENSION_SUBCATEGORY_NAME VARCHAR(50);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(50);
  V_DIMENSION_SUB_DETAIL_NAME VARCHAR(50);
  V_SPART_CODE VARCHAR(50);
  V_SPART_NAME VARCHAR(50);
  V_MAIN_FLAG VARCHAR(50);
  V_MAIN_CODE VARCHAR(50);
  V_SQL_JOIN VARCHAR(500);
  V_IN_CODE_ATTR VARCHAR(50);
  V_CODE_ATTR VARCHAR(50);
  V_PARA VARCHAR(10);
  V_IN_LV4 VARCHAR(100);
  V_IN_LV_CODE VARCHAR(100);
  V_INTO_LV_CODE VARCHAR(100);
  V_SQL_CONDITION VARCHAR(100);
  V_IN_SOFTWARE VARCHAR(50);
  V_INTO_SOFTWARE VARCHAR(50);
  V_REL_SOFTWARE VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 根据入参，对变量进行不同定义
     V_BEGIN_NUM := 0;
     V_END_NUM := 8;
     V_DIMENSION_CODE := 'DIMENSION_CODE,';
     V_DIMENSION_NAME := 'DIMENSION_CN_NAME,';
     V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
     V_DIMENSION_SUBCATEGORY_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
     V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
     V_DIMENSION_SUB_DETAIL_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
     V_SPART_CODE := 'SPART_CODE,';
     V_SPART_NAME := 'SPART_CN_NAME,';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MID_MON_SPART_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DIM_INFO_T';
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV0_CODE := 'LV0_PROD_RND_TEAM_CODE,';
     V_LV0_NAME := 'LV0_PROD_RD_TEAM_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_RND_TEAM_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_RND_TEAM_CODE,';
     V_LV2_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_RND_TEAM_CODE,';
     V_LV3_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE,';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
     V_IN_LV4 := 'LV4_PROD_RND_TEAM_CODE';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV0_CODE := 'LV0_INDUSTRY_CATG_CODE,'; 
     V_LV0_NAME := 'LV0_INDUSTRY_CATG_CN_NAME,';
     V_LV1_CODE := 'LV1_INDUSTRY_CATG_CODE,'; 
     V_LV1_NAME := 'LV1_INDUSTRY_CATG_CN_NAME,';
     V_LV2_CODE := 'LV2_INDUSTRY_CATG_CODE,';
     V_LV2_NAME := 'LV2_INDUSTRY_CATG_CN_NAME,';
     V_LV3_CODE := 'LV3_INDUSTRY_CATG_CODE,';
     V_LV3_NAME := 'LV3_INDUSTRY_CATG_CN_NAME,';
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE,';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME,';
     V_IN_LV4 := 'LV4_INDUSTRY_CATG_CODE';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV0_CODE := 'LV0_PROD_LIST_CODE,'; 
     V_LV0_NAME := 'LV0_PROD_LIST_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_LIST_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_LIST_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_LIST_CODE,';
     V_LV2_NAME := 'LV2_PROD_LIST_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_LIST_CODE,';
     V_LV3_NAME := 'LV3_PROD_LIST_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_LIST_CODE,';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME,';
     V_IN_LV4 := 'LV4_PROD_LIST_CODE';
  END IF;
  
  IF F_VERSION_ID IS NULL THEN 
  -- 取出对应的版本号
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空维表数据
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的结果表：'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  V_IN_SOFTWARE := 'SOFTWARE_MARK,';
  V_INTO_SOFTWARE := 'T1.SOFTWARE_MARK,';
  V_REL_SOFTWARE := 'AND NVL(T1.SOFTWARE_MARK,''SS'') = NVL(T2.SOFTWARE_MARK,''SS'')';
  IF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_CONDITION := 'AND T1.ONLY_SPART_FLAG = ''N''   ';    -- 取LV3.5下非单SPART的数据进行计算
  ELSIF F_COST_TYPE = 'PSP' THEN   -- PSP成本类型
     V_SQL_CONDITION := '';
  END IF;
  
  -- 对视角和层级进行逐层循环插入逻辑
  FOR LOOP_NUM IN V_BEGIN_NUM .. V_END_NUM LOOP 
  -- 对循环到不同层级、不同路径时，变量赋予不同值
     IF LOOP_NUM = 0 THEN   -- 路径2-子类明细层级
        V_GROUP_LEVEL := '''SUB_DETAIL''';
        V_VIEW_FLAG := '''DIMENSION''';
        V_MAIN_FLAG := '''N''';
        V_MAIN_CODE := '''N''';
        V_IN_CODE_ATTR := 'NULL';
     ELSIF LOOP_NUM = 1 THEN   -- 路径2-量纲子类层级
        V_GROUP_LEVEL := '''SUBCATEGORY''';
        V_DIMENSION_SUB_DETAIL_CODE := '';
        V_DIMENSION_SUB_DETAIL_NAME := '';
     ELSIF LOOP_NUM = 2 THEN   -- 路径2-量纲层级
        V_GROUP_LEVEL := '''DIMENSION''';
        V_DIMENSION_SUBCATEGORY_CODE := '';
        V_DIMENSION_SUBCATEGORY_NAME := '';
     ELSIF LOOP_NUM = 3 THEN   -- 路径1-SPART层级
        V_PARA := 'T1.';
        V_GROUP_LEVEL := '''SPART''';
        V_CODE_ATTR := 'T2.CODE_ATTRIBUTES,';
        V_VIEW_FLAG := '''PROD_SPART''';
        V_DIMENSION_CODE := '';
        V_DIMENSION_NAME := '';
        V_MAIN_CODE := 'T2.SPART_CODE';
        V_MAIN_FLAG := 'DECODE(MAIN_CODE,NULL,''N'',''Y'')';
        V_IN_CODE_ATTR := 'DECODE(MAIN_CODE,NULL,NULL,CODE_ATTRIBUTES)';
        V_SQL_JOIN := 'LEFT JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_PBI_MAIN_CODE_DIM_T T2
                       ON T1.SPART_CODE = T2.SPART_CODE
                       AND T1.'||V_IN_LV4||' = T2.'||V_IN_LV4||'
                       AND T1.BG_CODE = T2.BG_CODE
                       AND T2.VERSION_ID = '||V_VERSION_ID;
     ELSIF LOOP_NUM = 4 THEN   -- 路径1-LV4层级
        V_PARA := '';
        V_MAIN_CODE := 'NULL';
        V_MAIN_FLAG := 'NULL';
        V_CODE_ATTR := 'NULL,';
        V_IN_CODE_ATTR := 'NULL';
        V_SQL_JOIN := '';
        V_GROUP_LEVEL := '''LV4''';
        V_SPART_CODE := '';
        V_SPART_NAME := '';
     ELSIF LOOP_NUM = 5 THEN   -- 路径1-LV3层级
        V_GROUP_LEVEL := '''LV3''';
        V_LV4_CODE := '';
        V_LV4_NAME := '';
     ELSIF LOOP_NUM = 6 THEN   -- 路径1-LV2层级
        V_GROUP_LEVEL := '''LV2''';
        V_LV3_CODE := '';
        V_LV3_NAME := '';
     ELSIF LOOP_NUM = 7 THEN   -- 路径1-LV1层级
        V_GROUP_LEVEL := '''LV1''';
        V_LV2_CODE := '';
        V_LV2_NAME := '';
     ELSIF LOOP_NUM = 8 THEN   -- 路径1-LV0层级
        V_GROUP_LEVEL := '''LV0''';
        V_LV1_CODE := '';
        V_LV1_NAME := '';
     END IF;
  
  -- 取到对应的层级维度，插入到结果表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID,
         GROUP_LEVEL,
         '||V_LV0_CODE||'
         '||V_LV0_NAME||'
         '||V_LV1_CODE||'
         '||V_LV1_NAME||'
         '||V_LV2_CODE||'
         '||V_LV2_NAME||'
         '||V_LV3_CODE||'
         '||V_LV3_NAME||'
         '||V_LV4_CODE||'
         '||V_LV4_NAME||'
         '||V_DIMENSION_CODE||'
         '||V_DIMENSION_NAME||'
         '||V_DIMENSION_SUBCATEGORY_CODE||'
         '||V_DIMENSION_SUBCATEGORY_NAME||'
         '||V_DIMENSION_SUB_DETAIL_CODE||'
         '||V_DIMENSION_SUB_DETAIL_NAME||'
         '||V_SPART_CODE||'
         '||V_SPART_NAME||'
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH MAIN_FLAG_DIM_TMP AS(
  SELECT DISTINCT '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
         '||V_PARA||V_LV0_CODE||'
         '||V_PARA||V_LV0_NAME||'
         '||V_PARA||V_LV1_CODE||'
         '||V_PARA||V_LV1_NAME||'
         '||V_PARA||V_LV2_CODE||'
         '||V_PARA||V_LV2_NAME||'
         '||V_PARA||V_LV3_CODE||'
         '||V_PARA||V_LV3_NAME||'
         '||V_PARA||V_LV4_CODE||'
         '||V_PARA||V_LV4_NAME||'
         '||V_DIMENSION_CODE||'
         '||V_DIMENSION_NAME||'
         '||V_DIMENSION_SUBCATEGORY_CODE||'
         '||V_DIMENSION_SUBCATEGORY_NAME||'
         '||V_DIMENSION_SUB_DETAIL_CODE||'
         '||V_DIMENSION_SUB_DETAIL_NAME||'    -- 路径2：不需要关联主力编码表，所以不需要带表别名
         '||V_PARA||V_SPART_CODE||'
         '||V_PARA||V_SPART_NAME||'
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         '||V_INTO_SOFTWARE||'
         T1.VIEW_FLAG,
         '||V_CODE_ATTR||'
         '||V_MAIN_CODE||' AS MAIN_CODE
      FROM '||V_FROM_TABLE||' T1
      '||V_SQL_JOIN||'
      WHERE T1.VIEW_FLAG = '||V_VIEW_FLAG||'
      AND T1.VERSION_ID = '||V_VERSION_ID||'
      '||V_SQL_CONDITION||'
  )
  
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
         '||V_LV0_CODE||'
         '||V_LV0_NAME||'
         '||V_LV1_CODE||'
         '||V_LV1_NAME||'
         '||V_LV2_CODE||'
         '||V_LV2_NAME||'
         '||V_LV3_CODE||'
         '||V_LV3_NAME||'
         '||V_LV4_CODE||'
         '||V_LV4_NAME||'
         '||V_DIMENSION_CODE||'
         '||V_DIMENSION_NAME||'
         '||V_DIMENSION_SUBCATEGORY_CODE||'
         '||V_DIMENSION_SUBCATEGORY_NAME||'
         '||V_DIMENSION_SUB_DETAIL_CODE||'
         '||V_DIMENSION_SUB_DETAIL_NAME||'
         '||V_SPART_CODE||'
         '||V_SPART_NAME||'
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         '||V_MAIN_FLAG||' AS MAIN_FLAG,
         '||V_IN_CODE_ATTR||' AS CODE_ATTRIBUTES,
         '||V_IN_SOFTWARE||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM MAIN_FLAG_DIM_TMP';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将打好主力编码标签的所有维度数据插入'||V_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   END LOOP;   -- 结束循环
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

