-- Name: f_dm_foc_repl_actual_append_flag; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_actual_append_flag(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最后修改人:罗若文
背景描述：对通用实际数本年进行补齐，用于同编码指数
参数描述: x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T(通用颗粒度--采购成本),
		FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T(通用颗粒度--制造成本)
目标表:
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_ACTUAL_APPEND_FLAG()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_ACTUAL_APPEND_FLAG'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --新的版本号, 取自
  
  
 
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_FROM_TABLE VARCHAR(100); -- 来源表
  V_TO_TABLE VARCHAR(100); -- 目标表
  

  V_RMB_COST_AMT TEXT;
  V_FINAL_RMB_AMT TEXT;
  V_FINAL_AVG_AMT TEXT;
  V_REPLACE_PARA TEXT;
  V_IN_REPLACE_PARA TEXT;
  V_IN_REPLACE_CODE TEXT;
  V_INSERT_REPLACE_PARA TEXT;
  
  V_WHERE_PARA TEXT;
  

  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  
	V_TO_TABLE := 'DM_FOC_REPL_SAME_PUR_MTD_AVG_T';
  
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE ;
	
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  

  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_C_0 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_C_1 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_C_2 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_C_3 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_R_0 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_R_1 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_R_2 WHERE 1 = 1';
  EXECUTE IMMEDIATE 'INSERT INTO DM_FOC_REPL_SAME_PUR_MTD_AVG_T SELECT * FROM DM_FOC_REPL_SAME_PUR_MTD_AVG_T_R_3 WHERE 1 = 1';
 
        
    
   

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '子表数据'||V_TO_TABLE||'表完成',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

