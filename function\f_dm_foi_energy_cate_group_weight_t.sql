-- Name: f_dm_foi_energy_cate_group_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_energy_cate_group_weight_t(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2024-02-04
创建人  ：黄心蕊 HWX1187045
背景描述：品类组合页面-品类分类及TOP分类权重初始化
参数描述：参数一(F_CALIBER_FLAG)：'I'为ICT，'E'为数字能源
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
--数字能源
来源表 ： FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_CATE_INFO_T 数字能源_TOP品类清单 --取品类分类标签
		  FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_ITEM_WEIGHT_T 数字能源_品类及ITEM清单权重表(月度分析) --取TOP分类标签
		  FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T 数字能源_ITEM基础金额表 --取底层数据金额
		  FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T  数字能源_TOP品类下规格品清单 --取版本号
目标表：  FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T 数字能源_品类组合页权重表
--采购ICT
来源表 ： FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T 采购ICT_TOP品类清单 --取品类分类标签
		  FIN_DM_OPT_FOI.DM_FOI_CATE_ITEM_WEIGHT_T 采购ICT_品类及ITEM清单权重表(月度分析) --取TOP分类标签
		  FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T 采购ICT_ITEM基础金额表 --取底层数据金额
		  FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T  采购ICT_TOP品类下规格品清单 --取版本号
目标表：  FIN_DM_OPT_FOI.DM_FOI_CATE_GROUP_WEIGHT_T 采购ICT_品类组合页权重表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T('I',''); --ICT一个版本数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T('E',''); --数字能源一个版本数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME              VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T';
  V_VERSION              INT; --版本号
  V_CATE_VERSION		INT; --TOP品类清单版本号
  V_STEP_NUM             INT := 0; --函数步骤号
  V_CALIBER_FLAG         VARCHAR(2) := F_CALIBER_FLAG;
  V_TO_TABLE             VARCHAR(50); --目标表
  V_FROM_TOP_TYPE_TABLE  VARCHAR(50); --TOP分类标签来源表
  V_FROM_CATE_TYPE_TABLE VARCHAR(50); --品类分类来源表
  V_FROM_AMT_TABLE       VARCHAR(50); --金额来源表
  V_VERSION_TABLE        VARCHAR(50); --版本号取数表
  V_SUM_AMT              NUMERIC; --四年到货总额
  V_SQL                  TEXT; --执行语句
  V_PERIOD_YEAR          VARCHAR(200) := YEAR(NOW()) - 3 || '-' ||
                                         YEAR(NOW());

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  IF V_CALIBER_FLAG = 'I' THEN
	V_FROM_TOP_TYPE_TABLE 	:='FIN_DM_OPT_FOI.DM_FOI_CATE_ITEM_WEIGHT_T'; --202403版本新增
	V_FROM_CATE_TYPE_TABLE	:='FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T';  
	V_FROM_AMT_TABLE      	:='FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_AMT_T';  --202403版本新增
	V_VERSION_TABLE			:='FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T'; 
	V_TO_TABLE 				:='FIN_DM_OPT_FOI.DM_FOI_CATE_GROUP_WEIGHT_T'; --202403版本新增
    
  ELSIF V_CALIBER_FLAG = 'E' THEN
	V_FROM_TOP_TYPE_TABLE 	:='FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_ITEM_WEIGHT_T';
	V_FROM_CATE_TYPE_TABLE	:='FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_CATE_INFO_T';
	V_FROM_AMT_TABLE      	:='FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_AMT_T';
	V_VERSION_TABLE			:='FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T';
    V_TO_TABLE      		:='FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T';
    
  END IF;
   
  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_VERSION_ID IS NOT NULL THEN
    V_VERSION := F_VERSION_ID;
  ELSE
    V_SQL:='SELECT VERSION_ID FROM '||V_VERSION_TABLE||' WHERE LAST_UPDATE_DATE IS NOT NULL ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
	EXECUTE V_SQL INTO V_VERSION;
  END IF;
      
	V_SQL:='SELECT VERSION_ID FROM '||V_FROM_CATE_TYPE_TABLE ||' WHERE LAST_UPDATE_DATE IS NOT NULL ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
	EXECUTE V_SQL INTO V_CATE_VERSION;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
--删除结果表同版本数据
V_SQL:='DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION;
EXECUTE V_SQL;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'||V_VERSION||' 数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--品类分类及TOP分类权重计算
V_STEP_NUM := V_STEP_NUM + 1;
V_SQL:= ' 
INSERT INTO '||V_TO_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   GROUP_CODE,
   GROUP_CN_NAME,
   WEIGHT_RATE,
   PARENT_CODE,
   PARENT_CN_NAME,
   PARENT_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG)
  WITH BASE_AMT AS
  --取TOP品类四年总额并
   (SELECT CATEGORY_CODE,
           CATEGORY_NAME,
           SUM(RECEIVE_AMT_CNY) AS RMB_RECEIVE_AMT
      FROM '||V_FROM_AMT_TABLE||'
     WHERE PERIOD_YEAR BETWEEN (YEAR(NOW()) - 3) AND (YEAR(NOW())) --实际逻辑仅需要4年实际数
       AND VERSION_ID = '||V_VERSION||'
       AND TOP_FLAG = ''Y''
     GROUP BY CATEGORY_CODE, CATEGORY_NAME)
  --品类分类权重计算
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         T1.CATEGORY_CODE AS GROUP_CODE,
         T1.CATEGORY_NAME AS GROUP_CN_NAME,
         T1.RMB_RECEIVE_AMT /
         NULLIF(SUM(T1.RMB_RECEIVE_AMT) OVER(PARTITION BY T2.CATEGORY_TYPE),
                0) WEIGHT_RATE,
         T2.CATEGORY_TYPE AS PARENT_CODE,
         T2.CATEGORY_TYPE AS PARENT_CN_NAME,
         ''CATEGORY_TYPE'' AS PARENT_TYPE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
    FROM BASE_AMT T1
   INNER JOIN '||V_FROM_CATE_TYPE_TABLE||' T2
      ON (T1.CATEGORY_CODE = T2.CATEGORY_CODE AND
         T1.CATEGORY_NAME = T2.CATEGORY_NAME)
   WHERE T2.VERSION_ID = '||V_CATE_VERSION||'
     AND T2.CATEGORY_TYPE IS NOT NULL
   GROUP BY T1.CATEGORY_CODE,
            T1.CATEGORY_NAME,
            T2.CATEGORY_TYPE,
            T1.RMB_RECEIVE_AMT
  
  --TOP分类权重计算
  UNION ALL
  SELECT '||V_VERSION||' AS VERSION_ID,
         '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
         T1.CATEGORY_CODE AS GROUP_CODE,
         T1.CATEGORY_NAME AS GROUP_CN_NAME,
         T1.RMB_RECEIVE_AMT /
         NULLIF(SUM(RMB_RECEIVE_AMT) OVER(PARTITION BY T2.TOP_TYPE), 0) WEIGHT_RATE,
         T2.TOP_TYPE AS PARENT_CODE,
         T2.TOP_TYPE AS PARENT_CN_NAME,
         ''TOP_TYPE'' AS PARENT_TYPE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
    FROM BASE_AMT T1
   INNER JOIN (SELECT GROUP_CODE,''TOP50'' AS TOP_TYPE
                 FROM '||V_FROM_TOP_TYPE_TABLE||'
                WHERE VERSION_ID = '||V_CATE_VERSION||'
                  AND TOP_TYPE =''TOP50''
               UNION ALL
               SELECT GROUP_CODE,''TOP100'' AS TOP_TYPE
                 FROM '||V_FROM_TOP_TYPE_TABLE||'
                WHERE VERSION_ID = '||V_CATE_VERSION||'
                  AND TOP_TYPE <>''TOP300''
               UNION ALL
               SELECT GROUP_CODE,''TOP300'' AS TOP_TYPE
                 FROM '||V_FROM_TOP_TYPE_TABLE||'
                WHERE VERSION_ID = '||V_CATE_VERSION||'
                  AND TOP_TYPE IS NOT NULL) T2
      ON (T1.CATEGORY_CODE = T2.GROUP_CODE)
   GROUP BY T1.CATEGORY_CODE,
            T1.CATEGORY_NAME,
            T1.RMB_RECEIVE_AMT,
            T2.TOP_TYPE;
';
			  
EXECUTE V_SQL;
			  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => 'TOP分类及品类分类插数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

EXECUTE 'ANALYZE '||V_TO_TABLE||';';

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

