-- Name: f_dm_fcst_price_base_related; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_base_related(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
最后修改人: 罗若文
背景描述：1.定价源表收敛
参数描述：X_RESULT_STATUS ：是否成功
事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_BASE_RELATED()
变更示例-202503:
调整调度跑数日期时间为每月9号
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_BASE_RELATED'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_ID_MONTH BIGINT ; --月度版本也一并创建
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMM')||'-001'; --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_CURRENT_FLAG_MONTH BIGINT;
  V_DIM_VERSION_ID BIGINT ; 
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_COUNT 	 INT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
   
	V_FROM_TABLE  :=  'FIN_DM_OPT_FOI.DM_PRICE_GROUP_ORD_SPART_SRAN_I' ;
	V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T';



--清空目标表数据:
V_SQL := 'TRUNCATE '||V_TO_TABLE ;

EXECUTE IMMEDIATE V_SQL;
  

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 

 
  --首先判断底层数据审视有没有待刷数的版本
  SELECT COUNT(1) INTO V_COUNT 
  FROM DM_FCST_PRICE_VERSION_INFO_T
	WHERE DATA_TYPE = 'DATA_REVIEW'
		AND STATUS = 0 
		AND IS_RUNNING = 'Y';
  

  IF V_COUNT = 0 AND DAY(CURRENT_TIMESTAMP) != 9 THEN 
	RETURN '0' ;  --如果没有待跑数的底层数据审视版本,且不等于9号则不跑数（202503修改）
	
  ELSIF  (DAY(CURRENT_TIMESTAMP) < 1) or (DAY(CURRENT_TIMESTAMP) > 31)  THEN
	RETURN '0';   --8号前和25号后不跑数
	
  ELSIF (V_COUNT <> 0   ) or (V_COUNT = 0 AND DAY(CURRENT_TIMESTAMP) = 9) THEN 
  --如果有待跑数的底层数据审视版本，或者时间是9号则跑数  （202503修改）
   
   
-- 查询该月版本是否已存在，若存在，沿用，否则新建 
SELECT COUNT(1) INTO V_CURRENT_FLAG
FROM
	FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T 
WHERE
	SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
	AND DEL_FLAG = 'N'
	AND STATUS = 1
	AND UPPER(DATA_TYPE) = 'ANNUAL';
	
 -- FLAG 不等于0，说明已有版本号，沿用        
IF V_CURRENT_FLAG <> 0 THEN 
	SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T 
	WHERE
		SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ANNUAL';
ELSE
	--新版本号赋值
	SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_S')
	INTO V_VERSION_ID
	FROM DUAL;
     
  --往版本信息表记录本次版本号, 版本号为V_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','ANNUAL',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');

   


END IF;
	

	
 
-- 查询该月月度版本是否已存在，若存在，沿用，否则新建 
SELECT COUNT(1) INTO V_CURRENT_FLAG_MONTH
FROM
	FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T 
WHERE
	SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
	AND DEL_FLAG = 'N'
	AND STATUS = 1
	AND UPPER(DATA_TYPE) = 'MONTH';	
	
--月度版本号判断	
 -- FLAG 不等于0，说明已有版本号，沿用        
IF V_CURRENT_FLAG_MONTH  <> 0 THEN 
	SELECT VERSION_ID INTO V_VERSION_ID_MONTH
	FROM
		FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T 
	WHERE
		SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'MONTH';
ELSE
	--新版本号赋值
	SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_S')
	INTO V_VERSION_ID_MONTH
	FROM DUAL;
     
  --往版本信息表记录本次版本号, 版本号为V_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID_MONTH,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','MONTH',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   
   
   
END IF;
 


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
   

   
   
  --往目标表里插数
V_SQL := '
		INSERT INTO DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T
(
		VERSION_ID,
		PERIOD_YEAR,
		PERIOD_ID,
		LV0_PROD_LIST_CODE,
		LV1_PROD_LIST_CODE,
		LV2_PROD_LIST_CODE,
		LV3_PROD_LIST_CODE,
		LV4_PROD_LIST_CODE,
		LV0_PROD_LIST_CN_NAME,
		LV1_PROD_LIST_CN_NAME,
		LV2_PROD_LIST_CN_NAME,
		LV3_PROD_LIST_CN_NAME,
		LV4_PROD_LIST_CN_NAME,
		OVERSEAS_FLAG,
		REGION_HRMS_ORG_CODE,
		REGION_HRMS_ORG_CN_NAME,
		REGION_NAME_ABBR,
		REPOFFICE_ORG_CODE,
		REPOFFICE_ORG_CN_NAME,
		REPOFFICE_NAME_ABBR,
		SIGN_TOP_CUST_CATEGORY_CODE,
		SIGN_TOP_CUST_CATEGORY_CN_NAME,
		SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		HW_CONTRACT_NUM,
		SPART_CODE,
		SPART_DESC,
		QTY,
		USD_PNP_AMT,
		RMB_PNP_AMT,
		NEGATIVE_TAG,
		SPECIAL_TAG,
		CREATED_BY,
		CREATION_DATE,
		LAST_UPDATED_BY,
		LAST_UPDATE_DATE,
		DEL_FLAG,
		EXAMINE_RESULT
)
SELECT 
		'||V_VERSION_ID||',
		YYYY AS PERIOD_YEAR,
		PERIOD_ID,
		LV0_PROD_LIST_CODE,
		LV1_PROD_LIST_CODE,
		LV2_PROD_LIST_CODE,
		LV3_PROD_LIST_CODE,
		LV4_PROD_LIST_CODE,
		LV0_PROD_LIST_CN_NAME,
		LV1_PROD_LIST_CN_NAME,
		LV2_PROD_LIST_CN_NAME,
		LV3_PROD_LIST_CN_NAME,
		LV4_PROD_LIST_CN_NAME,
		CASE WHEN OVERSEAS_FLAG = ''国内'' then ''N'' 
		WHEN OVERSEAS_FLAG = ''海外''  then ''Y''
		ELSE NULL END AS OVERSEAS_FLAG,
		REGION_HRMS_ORG_CODE,
		REGION_HRMS_ORG_CN_NAME,
		REGION_NAME_ABBR,
		REPOFFICE_ORG_CODE,
		REPOFFICE_ORG_CN_NAME,
		REPOFFICE_NAME_ABBR,
		SIGN_TOP_CUST_CATEGORY_CODE,
		SIGN_TOP_CUST_CATEGORY_CN_NAME,
		SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		HW_CONTRACT_NUM,
		SPART_CODE,
		SPART_DESC,
		SUM(QUANTITY) AS QTY,
		SUM(USD_FACT_EX_RATE_AMT_PNP) AS USD_PNP_AMT,
		SUM(RMB_FACT_EX_RATE_AMT_PNP) AS RMB_PNP_AMT,
		CASE WHEN SUM(RMB_FACT_EX_RATE_AMT_PNP) < 0 THEN ''Y''
		ELSE ''N'' END AS NEGATIVE_TAG,
		CASE WHEN SPART_CODE IN (''CA999999'',''00000064'',''00000065'') THEN ''Y''
		ELSE ''N'' END AS SPECIAL_TAG,
		''-1'' AS CREATED_BY,
        CURRENT_TIMESTAMP AS CREATION_DATE,
        ''-1'' AS LAST_UPDATED_BY,
        CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
        ''N'' AS DEL_FLAG,
		''N'' AS EXAMINE_RESULT
		
		
	FROM  DM_PRICE_GROUP_ORD_SPART_SRAN_I
	WHERE PROD_CLASS_TAG = ''设备''
		--AND  LV0_PROD_RND_TEAM_CODE = ''104364''
	GROUP BY
		YYYY ,
		PERIOD_ID,
		LV0_PROD_LIST_CODE,
		LV1_PROD_LIST_CODE,
		LV2_PROD_LIST_CODE,
		LV3_PROD_LIST_CODE,
		LV4_PROD_LIST_CODE,
		LV0_PROD_LIST_CN_NAME,
		LV1_PROD_LIST_CN_NAME,
		LV2_PROD_LIST_CN_NAME,
		LV3_PROD_LIST_CN_NAME,
		LV4_PROD_LIST_CN_NAME,
		OVERSEAS_FLAG,
		REGION_HRMS_ORG_CODE,
		REGION_HRMS_ORG_CN_NAME,
		REGION_NAME_ABBR,
		REPOFFICE_ORG_CODE,
		REPOFFICE_ORG_CN_NAME,
		REPOFFICE_NAME_ABBR,
		SIGN_TOP_CUST_CATEGORY_CODE,
		SIGN_TOP_CUST_CATEGORY_CN_NAME,
		SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
		HW_CONTRACT_NUM,
		SPART_CODE,
		SPART_DESC
				';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
	  EXECUTE IMMEDIATE V_SQL;
 
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 '||V_TO_TABLE||'表, 新版本号='||V_VERSION_ID,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 '||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

  END IF;
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

