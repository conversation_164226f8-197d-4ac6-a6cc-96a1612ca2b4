-- Name: f_dm_fom_actual_cost_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_actual_cost_t(f_caliber_flag character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-09
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-热力图表数据初始化
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_VERSION_ID)：运行版本号
		  参数三(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T 月度分析制造对象金额表
		FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 制造单领域金额表 
		FIN_DM_OPT_FOI.DM_FOM_ITEM_FORECAST_T --计划量表
目标表：FIN_DM_OPT_FOI.DM_FOM_ACTUAL_COST_T 月度分析热力图表
事例  ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_ACTUAL_COST_T('E',''); --EMS一个版本的数据
		SELECT FIN_DM_OPT_FOI.F_DM_FOM_ACTUAL_COST_T('M',''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_ACTUAL_COST_T';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;
  V_HOMEMADE_EMS VARCHAR(50);
  V_SQL			TEXT;

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_ACTUAL_COST_T WHERE VERSION_ID = V_VERSION AND CALIBER_FLAG = V_CALIBER_FLAG;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'''||V_CALIBER_FLAG||''' 口径数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
  V_STEP_NUM := V_STEP_NUM + 1;
  --2.分层金额卷积  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ACTUAL_COST_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     ACTUAL_COST_AMT,
     ACTUAL_QTY,
     CALIBER_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
  --制造对象层级金额插数
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL, --原始数据分为两个层级：普通数据的制造对象层级,以及海思与云核心网的经营对象层级
           RMB_COST_AMT,
           ACTUAL_QTY,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           DECODE(SHIPPING_OBJECT_CODE||MANUFACTURE_OBJECT_CODE,'',LV1_CODE,SHIPPING_OBJECT_CODE) AS PARENT_CODE,
           DECODE(SHIPPING_OBJECT_CODE||MANUFACTURE_OBJECT_CODE,'',LV1_CN_NAME,SHIPPING_OBJECT_CN_NAME) AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
    
    UNION ALL
    
    --发货对象层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           '',
           '',
           SHIPPING_OBJECT_CODE AS GROUP_CODE,
           SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'SHIPPING_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT) AS ACTUAL_COST_AMT,
           SUM(ACTUAL_QTY) AS ACTUAL_QTY,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           BUSSINESS_OBJECT_CODE AS PARENT_CODE,
           BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'MANUFACTURE_OBJECT'
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              SHIPPING_OBJECT_CODE,
              SHIPPING_OBJECT_CN_NAME,
              PERIOD_YEAR,
              PERIOD_ID
    
    UNION ALL
    --经营对象层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           '',
           '',
           '',
           '',
           BUSSINESS_OBJECT_CODE AS GROUP_CODE,
           BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'BUSSINESS_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           SUM(ACTUAL_QTY) AS ACTUAL_QTY,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           LV1_CODE AS PARENT_CODE,
           LV1_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
	   AND GROUP_LEVEL = 'MANUFACTURE_OBJECT'
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              PERIOD_YEAR,
              PERIOD_ID
    
    UNION ALL
    --LV1层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           '',
           '',
           '',
           '',
           '',
           '',
           LV1_CODE AS GROUP_CODE,
           LV1_CN_NAME AS GROUP_CN_NAME,
           'LV1' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           SUM(ACTUAL_QTY) AS ACTUAL_QTY,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           LV0_CODE AS PARENT_CODE,
           LV0_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T 
     WHERE CALIBER_FLAG = V_CALIBER_FLAG		--取原始普通制造对象层级数据及海思云核心网经营对象层级数，卷积至LV1层级，无需另外筛选控制
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              CALIBER_FLAG,
              PERIOD_YEAR,
              PERIOD_ID
    
    UNION ALL
    --LV0层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           '',
           '',
           '',
           '',
           '',
           '',
           '',
           '',
           LV0_CODE AS GROUP_CODE,
           LV0_CN_NAME AS GROUP_CN_NAME,
           'LV0' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           SUM(ACTUAL_QTY) AS ACTUAL_QTY,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           '' AS PARENT_CODE,
           '' AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_MANUFACTURE_DTL_T
     WHERE CALIBER_FLAG = V_CALIBER_FLAG
     GROUP BY LV0_CODE, LV0_CN_NAME, CALIBER_FLAG, PERIOD_YEAR, PERIOD_ID
	 
    UNION ALL
    --ITEM层级数量插数
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           ITEM_CODE AS GROUP_CODE,
           ITEM_CN_NAME AS GROUP_CN_NAME,
           'ITEM' AS GROUP_LEVEL,
           DECODE(CALIBER_FLAG,'E',RMB_EMS_AMT,NULL) AS ACTUAL_COST_AMT, --EMS展示金额，自制不展示
           TRANSACTION_QUANTITY AS ACTUAL_QTY,
           V_CALIBER_FLAG AS CALIBER_FLAG,
           DECODE(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'',BUSSINESS_OBJECT_CODE,MANUFACTURE_OBJECT_CODE) AS PARENT_CODE,
           DECODE(MANUFACTURE_OBJECT_CODE||SHIPPING_OBJECT_CODE,'',BUSSINESS_OBJECT_CN_NAME,MANUFACTURE_OBJECT_CN_NAME) AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
     WHERE ONLY_ITEM_FLAG = 'N'
       AND CALIBER_FLAG = V_CALIBER_FLAG;
		
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '金额表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
 
--入参取值判断
IF V_CALIBER_FLAG = 'E' THEN 
  V_HOMEMADE_EMS := ' EMS_SR <> ''自制'' ';
ELSIF V_CALIBER_FLAG = 'M' THEN
  V_HOMEMADE_EMS := ' EMS_SR = ''自制'' ';
END IF;
 
  --ITEM与测量插数
  V_SQL:='
  WITH BASE_ITEM_QTY AS
   (
	 
    --计划量表取数
    SELECT SUBSTR(FORECAST_DATE,1,6) AS PERIOD_ID, ITEM_CODE AS ITEM, SUM(PCS_NUMBER) AS ACTUAL_QTY --计划加工量
      FROM FIN_DM_OPT_FOI.DWK_GRP_PROCESS_FCST_I
     WHERE TO_NUMBER(REPLACE(PLAN_DATE,''-'','''')) = (SELECT  MAX(TO_NUMBER(REPLACE(PLAN_DATE,''-'',''''))) 
                           FROM FIN_DM_OPT_FOI.DWK_GRP_PROCESS_FCST_I
                         )
	   AND '||V_HOMEMADE_EMS||'
       AND SUBSTR(FORECAST_DATE,1,6) BETWEEN TO_NUMBER(TO_CHAR(CURRENT_DATE, ''YYYYMM'')) AND
           TO_NUMBER(TO_CHAR(ADD_MONTHS(CURRENT_DATE, 5), ''YYYYMM''))		--取未来6个月的预测量
     GROUP BY SUBSTR(FORECAST_DATE,1,6), ITEM_CODE
	 ) 	--202403版本 预测数来源表逻辑修改

   INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ACTUAL_COST_T
     (VERSION_ID,
      PERIOD_YEAR,
      PERIOD_ID,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      ACTUAL_COST_AMT,
      ACTUAL_QTY,
      CALIBER_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG)
  --ITEM预测量插数
  SELECT '||V_VERSION||' AS VERSION_ID,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T2.LV0_CODE,
         T2.LV0_CN_NAME,
         T2.LV1_CODE,
         T2.LV1_CN_NAME,
         T2.BUSSINESS_OBJECT_CODE,
         T2.BUSSINESS_OBJECT_CN_NAME,
         T2.SHIPPING_OBJECT_CODE,
         T2.SHIPPING_OBJECT_CN_NAME,
         T2.MANUFACTURE_OBJECT_CODE,
         T2.MANUFACTURE_OBJECT_CN_NAME,
         T2.TOP_ITEM_CODE AS GROUP_CODE,
         T2.TOP_ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         '''' AS RMB_COST_AMT,
         T1.ACTUAL_QTY,
         '''||V_CALIBER_FLAG||''' AS CALIBER_FLAG,
         DECODE(T2.MANUFACTURE_OBJECT_CODE||T2.SHIPPING_OBJECT_CODE,'''',T2.BUSSINESS_OBJECT_CODE,T2.MANUFACTURE_OBJECT_CODE) AS PARENT_CODE,
         DECODE(T2.MANUFACTURE_OBJECT_CODE||T2.SHIPPING_OBJECT_CODE,'''',T2.BUSSINESS_OBJECT_CN_NAME,T2.MANUFACTURE_OBJECT_CN_NAME) AS PARENT_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
    FROM BASE_ITEM_QTY T1
    JOIN FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T T2
      ON T1.ITEM = T2.TOP_ITEM_CODE
   WHERE T2.VERSION_ID = '||V_VERSION||'
     AND T2.CALIBER_FLAG = '''||V_CALIBER_FLAG||'''
     AND NVL(T2.MANUFACTURE_OBJECT_CODE||T2.SHIPPING_OBJECT_CODE,''SNULL'') <> ''SNULL''
	 
UNION ALL

  --海思与云核心网ITEM预测量插数
  SELECT '||V_VERSION||' AS VERSION_ID,
         LEFT(T1.PERIOD_ID, 4) AS PERIOD_YEAR,
         T1.PERIOD_ID,
         T2.LV0_CODE,
         T2.LV0_CN_NAME,
         T2.LV1_CODE,
         T2.LV1_CN_NAME,
         T2.BUSSINESS_OBJECT_CODE,
         T2.BUSSINESS_OBJECT_CN_NAME,
         '''',
         '''',
         '''',
         '''',
         T2.TOP_ITEM_CODE AS GROUP_CODE,
         T2.TOP_ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         '''' AS RMB_COST_AMT,
         T1.ACTUAL_QTY,
         '''||V_CALIBER_FLAG||''' AS CALIBER_FLAG,
         T2.BUSSINESS_OBJECT_CODE AS PARENT_CODE,
         T2.BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
    FROM BASE_ITEM_QTY T1
    JOIN FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T T2
      ON T1.ITEM = T2.TOP_ITEM_CODE
   WHERE T2.VERSION_ID = '||V_VERSION||'
     AND T2.CALIBER_FLAG = '''||V_CALIBER_FLAG||'''
     AND NVL(T2.MANUFACTURE_OBJECT_CODE||T2.SHIPPING_OBJECT_CODE,''SNULL'') = ''SNULL'';';
	 
EXECUTE V_SQL;
  
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => 'ITEM预测量插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 

  
 ANALYZE FIN_DM_OPT_FOI.DM_FOM_ACTUAL_COST_T;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

