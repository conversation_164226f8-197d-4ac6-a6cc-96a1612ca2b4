-- Name: f_dm_fcst_price_point_base_cus_annl; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_point_base_cus_annl(f_cus_id bigint, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年12月版本
  创建人  ：唐钦
  背景描述：组合虚化-年度分析页面
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_POINT_BASE_CUS_ANNL('');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_POINT_BASE_CUS_ANNL'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_SQL_CUSTOM_ID VARCHAR(200); --筛选条件
  V_CUS_ID BIGINT := F_CUS_ID;
  V_CUS_LV_CODE VARCHAR(200);
  V_CUS_LV_NAME VARCHAR(10) := 'NULL';
  V_INTO_CUS_LV_CODE VARCHAR(200);
  V_CUS_PARENT_LEVEL VARCHAR(200);
  V_CUS_SPART_CODE VARCHAR(200);
  V_LEVEL_CODE VARCHAR(200);
  V_YEAR       INT ;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
     if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
  
  -- 将组合维表值插入变量
   V_SQL := '
   SELECT REPLACE(LV_CODE,'','','''''',''''''),LV_CODE,PARENT_LEVEL,SPART_CODE
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T
      WHERE CUSTOM_ID = '||V_CUS_ID;
   DBMS_OUTPUT.PUT_LINE(V_SQL);
   EXECUTE IMMEDIATE V_SQL INTO V_CUS_LV_CODE,V_INTO_CUS_LV_CODE,V_CUS_PARENT_LEVEL,V_CUS_SPART_CODE;
   DBMS_OUTPUT.PUT_LINE(V_CUS_LV_CODE||','||V_CUS_PARENT_LEVEL||','||V_CUS_SPART_CODE);
     
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到虚化数据为：LV_CODE = '||V_INTO_CUS_LV_CODE||',PARENT_LEVEL = '||V_CUS_PARENT_LEVEL||',SPART_CODE = '||V_CUS_SPART_CODE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'，组合ID为：'||V_CUS_ID||'的DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T、DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T、DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表
  DROP TABLE IF EXISTS FCST_PRICE_AVG_TMP;
  CREATE TEMPORARY TABLE FCST_PRICE_AVG_TMP(
         PERIOD_YEAR INT,
         LV0_PROD_LIST_CODE CHARACTER VARYING(50),
         LV1_PROD_LIST_CODE CHARACTER VARYING(50),
         LV2_PROD_LIST_CODE CHARACTER VARYING(50),
         LV3_PROD_LIST_CODE CHARACTER VARYING(50),
         LV4_PROD_LIST_CODE CHARACTER VARYING(50),
         LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         OVERSEA_FLAG CHARACTER VARYING(20),
         REGION_CODE CHARACTER VARYING(50),
         REGION_CN_NAME CHARACTER VARYING(200),
         REPOFFICE_CODE CHARACTER VARYING(50),
         REPOFFICE_CN_NAME CHARACTER VARYING(200),
         SIGN_TOP_CUST_CATEGORY_CODE CHARACTER VARYING(50),
         SIGN_TOP_CUST_CATEGORY_CN_NAME CHARACTER VARYING(200),
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME CHARACTER VARYING(100),
         SPART_CODE CHARACTER VARYING(40),
         SPART_CN_NAME CHARACTER VARYING(2000),
         USD_PNP_AMT NUMERIC,
         VIEW_FLAG CHARACTER VARYING(20),
         APD_FLAG CHARACTER VARYING(5),
         APPEND_YEAR INT,
         BG_CODE CHARACTER VARYING(50),
         BG_CN_NAME CHARACTER VARYING(200)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE, LV4_PROD_LIST_CODE, REPOFFICE_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 判断用户选择的LV0-LV4最细粒度层级，再用哪个层级字段
  IF V_CUS_PARENT_LEVEL = 'LV0' THEN 
     V_LEVEL_CODE := 'LV0_PROD_LIST_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV1' THEN 
     V_LEVEL_CODE := 'LV1_PROD_LIST_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV2' THEN 
     V_LEVEL_CODE := 'LV2_PROD_LIST_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV3' THEN 
     V_LEVEL_CODE := 'LV3_PROD_LIST_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV4' THEN 
     V_LEVEL_CODE := 'LV4_PROD_LIST_CODE';   -- 一定是多选，卷积时不加
  END IF;
   
  -- 取ENABLE_FLAG = 'Y'，PERIOD_YEAR = YTD年的数据，用于重新计算权重数据，且判断状态码逻辑
  V_SQL := '
  INSERT INTO FCST_PRICE_AVG_TMP(
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         VIEW_FLAG,
         APD_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME
  )
  SELECT PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         VIEW_FLAG,
         APPEND_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T
	  WHERE VERSION_ID = '||V_VERSION_ID||'
	  AND ENABLE_FLAG = ''Y''    -- 取有效数据
	  AND SPART_CODE = '''||V_CUS_SPART_CODE||'''
	  AND PERIOD_YEAR = '||V_YEAR||'
	  AND '||V_LEVEL_CODE||' IN ('||V_CUS_LV_CODE||')'; 
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将'||V_LEVEL_CODE||'：'||V_CUS_LV_CODE||'，SPART：'||V_CUS_SPART_CODE||'的年均本-有效数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  ------------------------------------------------------------------------------- 虚化逻辑处理年度权重数据 -----------------------------------------------------------------------------------------
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 LOGIC_NUM
  )
  WITH CUS_DIM_AMT_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T1.SPART_CODE AS GROUP_CODE,
         T1.SPART_CN_NAME AS GROUP_CN_NAME,
         T2.GROUP_LEVEL,
         SUM(NVL(T1.USD_PNP_AMT,0)) AS USD_PNP_AMT,
         SUM(SUM(NVL(T1.USD_PNP_AMT,0))) OVER(PARTITION BY T1.PERIOD_YEAR,T1.SPART_CODE,T1.OVERSEA_FLAG,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.SIGN_TOP_CUST_CATEGORY_CODE,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,T1.VIEW_FLAG,T1.BG_CODE) AS PARENT_AMT,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.LV4_PROD_LIST_CODE AS PARENT_CODE,
         T1.LV4_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
	  FROM FCST_PRICE_AVG_TMP T1
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T T2
	  ON T1.SPART_CODE = T2.SPART_CODE 
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
	  AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.PERIOD_YEAR = V_YEAR
	  AND T2.CUSTOM_ID = V_CUS_ID
	  GROUP BY T1.PERIOD_YEAR,
               T2.CUSTOM_ID,
               T2.CUSTOM_CN_NAME,
               T1.SPART_CODE,
               T1.SPART_CN_NAME,
               T2.GROUP_LEVEL,
               T1.OVERSEA_FLAG,
               T1.REGION_CODE,
               T1.REGION_CN_NAME,
               T1.REPOFFICE_CODE,
               T1.REPOFFICE_CN_NAME,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.VIEW_FLAG,
               T1.LV4_PROD_LIST_CODE,
               T1.LV4_PROD_LIST_CN_NAME,
               T1.BG_CODE,
               T1.BG_CN_NAME
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         DECODE(PARENT_AMT,0,0,USD_PNP_AMT/PARENT_AMT) AS WEIGHT_RATE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 1 AS LOGIC_NUM
	  FROM CUS_DIM_AMT_TMP;
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将虚化后，在不同LV4下SPART层级的权重数据插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 LOGIC_NUM
  )
  WITH CUS_DIM_AMT_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T1.SPART_CODE AS GROUP_CODE,
         T1.SPART_CN_NAME AS GROUP_CN_NAME,
         T2.GROUP_LEVEL,
         SUM(NVL(T1.USD_PNP_AMT,0)) AS USD_PNP_AMT,
         SUM(SUM(NVL(T1.USD_PNP_AMT,0))) OVER(PARTITION BY T1.PERIOD_YEAR,T1.SPART_CODE,T1.OVERSEA_FLAG,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.SIGN_TOP_CUST_CATEGORY_CODE,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,T1.VIEW_FLAG,T1.BG_CODE) AS PARENT_AMT,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T2.LV_CODE AS PARENT_CODE,
         T2.LV_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
	  FROM FCST_PRICE_AVG_TMP T1
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T T2
	  ON T1.SPART_CODE = T2.SPART_CODE 
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
	  AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.PERIOD_YEAR = V_YEAR
	  AND T2.CUSTOM_ID = V_CUS_ID
	  GROUP BY T1.PERIOD_YEAR,
               T2.CUSTOM_ID,
               T2.CUSTOM_CN_NAME,
               T1.SPART_CODE,
               T1.SPART_CN_NAME,
               T2.LV_CODE,
               T2.LV_CN_NAME,
               T2.GROUP_LEVEL,
               T1.OVERSEA_FLAG,
               T1.REGION_CODE,
               T1.REGION_CN_NAME,
               T1.REPOFFICE_CODE,
               T1.REPOFFICE_CN_NAME,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.VIEW_FLAG,
               T1.BG_CODE,
               T1.BG_CN_NAME
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         DECODE(PARENT_AMT,0,0,USD_PNP_AMT/PARENT_AMT) AS WEIGHT_RATE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 2 AS LOGIC_NUM
	  FROM CUS_DIM_AMT_TMP;
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将虚化后，在CUSTOM_ID下SPART层级的权重数据插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  ------------------------------------------------------------------------- 虚化后年度涨跌幅逻辑 ---------------------------------------------------------------------------------------------
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH SPART_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T2.CUSTOM_ID,
         T2.CUSTOM_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.LV4_PROD_LIST_CODE,
         T1.ANNUAL_AMP,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T2.LV_CODE AS PARENT_CODE,
         T2.LV_CN_NAME AS PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T T1
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T T2
	  ON T1.GROUP_CODE = T2.SPART_CODE 
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
	  AND NVL(T1.OVERSEA_FLAG,''S1'') = NVL(T2.OVERSEA_FLAG,''S1'')
      AND NVL(T1.REGION_CODE,''S2'') = NVL(T2.REGION_CODE,''S2'')
      AND NVL(T1.REPOFFICE_CODE,''S3'') = NVL(T2.REPOFFICE_CODE,''S3'')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,''S4'') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,''S4'')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'')
	  WHERE T1.VERSION_ID = '||V_VERSION_ID||'
	  AND T1.GROUP_LEVEL = ''SPART''
	  AND T1.ENABLE_FLAG = ''Y''
	  AND T1.'||V_LEVEL_CODE||' IN ('||V_CUS_LV_CODE||')
	  AND T2.CUSTOM_ID = '||V_CUS_ID||'
  ),
  WEIGHT_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP * T2.WEIGHT_RATE AS WEIGHT_AMP,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME
	  FROM SPART_AMP_TMP T1
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T T2
	  ON T1.CUSTOM_ID = T2.CUSTOM_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE 
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.LV4_PROD_LIST_CODE = T2.PARENT_CODE
	  AND T1.BG_CODE = T2.BG_CODE 
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
	  AND NVL(T1.OVERSEA_FLAG,''S1'') = NVL(T2.OVERSEA_FLAG,''S1'')
      AND NVL(T1.REGION_CODE,''S2'') = NVL(T2.REGION_CODE,''S2'')
      AND NVL(T1.REPOFFICE_CODE,''S3'') = NVL(T2.REPOFFICE_CODE,''S3'')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,''S4'') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,''S4'')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'')
	  WHERE T2.LOGIC_NUM = 1   -- 取第一次计算的权重数据
	  AND T2.VERSION_ID = '||V_VERSION_ID||'
  )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SUM(WEIGHT_AMP) AS ANNUAL_AMP,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
	  FROM WEIGHT_AMP_TMP
	  GROUP BY PERIOD_YEAR,
               CUSTOM_ID,
               CUSTOM_CN_NAME,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               OVERSEA_FLAG,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               SIGN_TOP_CUST_CATEGORY_CODE,
               SIGN_TOP_CUST_CATEGORY_CN_NAME,
               SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               VIEW_FLAG,
               PARENT_CODE,
               PARENT_CN_NAME,
               BG_CODE,
               BG_CN_NAME';

     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将虚化后的SPART层级的涨跌幅数据,插入结果表：DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --------------------------------------------------------------------------- 虚化后状态码逻辑处理 -----------------------------------------------------------------------------------------
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  WITH BASE_SPART_STATUS_TMP AS (
  SELECT T1.PERIOD_YEAR,
         T2.CUSTOM_ID,
         T1.SPART_CODE AS GROUP_CODE,
         ''SPART'' AS GROUP_LEVEL,
         T1.STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T2.LV_CODE AS PARENT_CODE,
         T1.BG_CODE
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_BASE_STATUS_T T1 
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_DIM_T T2
	  ON T1.SPART_CODE = T2.SPART_CODE 
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
	  AND NVL(T1.OVERSEA_FLAG,''S1'') = NVL(T2.OVERSEA_FLAG,''S1'')
      AND NVL(T1.REGION_CODE,''S2'') = NVL(T2.REGION_CODE,''S2'')
      AND NVL(T1.REPOFFICE_CODE,''S3'') = NVL(T2.REPOFFICE_CODE,''S3'')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,''S4'') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,''S4'')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'')
	  WHERE T1.VERSION_ID = '||V_VERSION_ID||'
	  AND T1.'||V_LEVEL_CODE||' IN ('||V_CUS_LV_CODE||')
	  AND T2.CUSTOM_ID = '||V_CUS_ID||'
  ),
  BASEUP_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.CUSTOM_ID,
         T1.GROUP_CODE,   
         T1.PARENT_CODE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,
         T1.VIEW_FLAG,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,4,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2     -- 值=0，说明子级都为2，赋2
      FROM BASE_SPART_STATUS_TMP T1
      GROUP BY T1.GROUP_CODE,
               T1.CUSTOM_ID,
               T1.PARENT_CODE,
               T1.REGION_CODE,
               T1.REPOFFICE_CODE,
               T1.BG_CODE,
               T1.OVERSEA_FLAG,
               T1.VIEW_FLAG,
               T1.SIGN_TOP_CUST_CATEGORY_CODE,
               T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               T1.PERIOD_YEAR
    )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         END AS STATUS_CODE,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T T1
	  INNER JOIN BASEUP_STATUS_TMP T2
	  ON  T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.CUSTOM_ID = T2.CUSTOM_ID
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,''S1'') = NVL(T2.OVERSEA_FLAG,''S1'')
      AND NVL(T1.REGION_CODE,''S2'') = NVL(T2.REGION_CODE,''S2'')
      AND NVL(T1.REPOFFICE_CODE,''S3'') = NVL(T2.REPOFFICE_CODE,''S3'')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,''S4'') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,''S4'')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,''S5'')
      WHERE T1.VERSION_ID = '||V_VERSION_ID ;  

     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，虚化后的SPART层级状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
	
  EXECUTE IMMEDIATE 'ANALYSE FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_WEIGHT_T';
  EXECUTE IMMEDIATE 'ANALYSE FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_AMP_T';
  EXECUTE IMMEDIATE 'ANALYSE FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_ANNL_STATUS_T';
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
    
END$$
/

