-- Name: f_dm_foc_total_annual_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_total_annual_amp(f_industry_flag character varying, f_dimension_type character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-10-26
  创建人  ：唐钦
  背景描述：总成本-年度涨跌幅表(年度分析-柱状图)
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ANNUAL_AMP()
*/
DECLARE
  V_SP_NAME    VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ANNUAL_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(500);
  V_FROM1_TABLE VARCHAR(500);
  V_TO_TABLE VARCHAR(500);
  V_L1_NAME VARCHAR(500);
  V_L2_NAME VARCHAR(500);
  V_DIMENSION VARCHAR(500);
  V_DIMENSION_SUBCATEGORY VARCHAR(500);
  V_DIMENSION_SUB_DETAIL VARCHAR(500);
  V_IN_L1_NAME VARCHAR(500);
  V_IN_L2_NAME VARCHAR(500);
  V_IN_DIMENSION VARCHAR(500);
  V_IN_DIMENSION_SUBCATEGORY VARCHAR(500);
  V_IN_DIMENSION_SUB_DETAIL VARCHAR(500);
  V_SQL_CONDITION TEXT;     -- 关联条件逻辑
  V_REL_L1_NAME VARCHAR(500);
  V_REL_L2_NAME VARCHAR(500);
  V_REL_DIMENSION VARCHAR(500);
  V_REL_DIMENSION_SUBCATEGORY VARCHAR(500);
  V_REL_DIMENSION_SUB_DETAIL VARCHAR(500);
  V_FROM2_TABLE VARCHAR(500);
  V_SPART_CODE VARCHAR(200);
  V_SPART_NAME VARCHAR(200);
  V_REL_SPART VARCHAR(500);
  V_IN_SPART VARCHAR(200);
  
  -- 202405版本新增
  V_DIFF_COLUMN_CODE VARCHAR(200);
  V_DIFF_COLUMN_NAME VARCHAR(200);
  V_REL_DIFF_COLUMN VARCHAR(500);
  V_IN_DIFF_COLUMN VARCHAR(200);
  V_VERSION_TABLE VARCHAR(100);
  V_IN_PUBLIC VARCHAR(500);
  V_INTO_PUBLIC VARCHAR(500);
  V_REL_PUBLIC VARCHAR(500);
  V_LV_CODE VARCHAR(100);
  V_INTO_LV_CODE VARCHAR(100);
  V_REL_LV_CODE VARCHAR(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
  -- 判断入参为何颗粒度，赋予变量不同值
   IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_SQL_CONDITION := ' AND T1.GROUP_LEVEL IN (''LV0'', ''LV1'', ''LV2'', ''LV3'', ''LV4'') ';   -- 202407版本新增LV4层级
   ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_L1_NAME := 'L1_NAME,';
      V_L2_NAME := 'L2_NAME,';
      V_IN_L1_NAME := 'T1.L1_NAME,';
      V_IN_L2_NAME := 'T1.L2_NAME,';
      V_REL_L1_NAME := ' AND NVL(T1.L1_NAME,''SNULL'') = NVL(T2.L1_NAME,''SNULL'') ';
      V_REL_L2_NAME := ' AND NVL(T1.L2_NAME,''SNULL1'') = NVL(T2.L2_NAME,''SNULL1'') ';
      V_SQL_CONDITION := ' AND T1.GROUP_LEVEL IN (''LV0'', ''LV1'', ''LV2'', ''L1'', ''L2'') ';
   ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_SPART_CODE := 'SPART_CODE,';
      V_SPART_NAME := 'SPART_CN_NAME,';
      V_IN_SPART := 'T1.SPART_CODE,T1.SPART_CN_NAME,';
      V_REL_SPART := ' AND NVL(T1.SPART_CODE,''S1'') = NVL(T2.SPART_CODE,''S1'') ';
      V_DIMENSION := 'DIMENSION_CODE,
                      DIMENSION_CN_NAME,';
      V_DIMENSION_SUBCATEGORY := 'DIMENSION_SUBCATEGORY_CODE,
                                  DIMENSION_SUBCATEGORY_CN_NAME,';
      V_DIMENSION_SUB_DETAIL := 'DIMENSION_SUB_DETAIL_CODE,
                                 DIMENSION_SUB_DETAIL_CN_NAME,';
      V_IN_DIMENSION := 'T1.DIMENSION_CODE,
                         T1.DIMENSION_CN_NAME,';
      V_IN_DIMENSION_SUBCATEGORY := 'T1.DIMENSION_SUBCATEGORY_CODE,
                                     T1.DIMENSION_SUBCATEGORY_CN_NAME,';
      V_IN_DIMENSION_SUB_DETAIL := 'T1.DIMENSION_SUB_DETAIL_CODE,
                                    T1.DIMENSION_SUB_DETAIL_CN_NAME,';
      V_REL_DIMENSION := ' AND NVL(T1.DIMENSION_CODE,''SNULL0'') = NVL(T2.DIMENSION_CODE,''SNULL0'') ';
      V_REL_DIMENSION_SUBCATEGORY := ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') ';
      V_REL_DIMENSION_SUB_DETAIL := ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') ';
       V_SQL_CONDITION := ' AND T1.GROUP_LEVEL IN (''LV0'',''LV1'',''LV2'',''LV3'',''LV4'',''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'',''COA'') ';   -- 202405版本新增COA层级
  -- 当产业项目标识为：E时，加COA层级变量
    IF F_INDUSTRY_FLAG = 'E' THEN 
       V_DIFF_COLUMN_CODE := 'COA_CODE,';
       V_DIFF_COLUMN_NAME := 'COA_CN_NAME,';
       V_IN_DIFF_COLUMN := 'T1.COA_CODE,T1.COA_CN_NAME,';
       V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''S2'') = NVL(T2.COA_CODE,''S2'') ';
    END IF;
   END IF;
  
  -- 202410版本新增：替代指数字段代码兼容
  V_LV_CODE := 'PROD_RND_TEAM_CODE,
                PROD_RD_TEAM_CN_NAME,';
  V_INTO_LV_CODE := 'T1.PROD_RND_TEAM_CODE,
                   T1.PROD_RD_TEAM_CN_NAME,';
  V_REL_LV_CODE := 'AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE';
  V_IN_PUBLIC := 'OVERSEA_FLAG,
                   LV0_PROD_LIST_CODE,
                   LV0_PROD_LIST_CN_NAME,';
  V_INTO_PUBLIC := 'T1.OVERSEA_FLAG,
                   T1.LV0_PROD_LIST_CODE,
                   T1.LV0_PROD_LIST_CN_NAME,';
  V_REL_PUBLIC := 'AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE';
  
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG IN ('I','REPL')  THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
  -- 判断入参为何颗粒度，赋予变量不同值
   IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN   -- 通用颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_AMP_T';
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'REPL' THEN   -- 通用颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_YTD_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_YTD_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_YTD_AMP_T';
	  V_IN_PUBLIC := 'DATA_TYPE,';
	  V_INTO_PUBLIC := 'T1.DATA_TYPE,';
	  V_REL_PUBLIC := '';
	  V_LV_CODE := '';
	  V_INTO_LV_CODE := '';
	  V_REL_LV_CODE := '';
   ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ANNUAL_AMP_T';
   ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ANNUAL_AMP_T';
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
  -- 判断入参为何颗粒度，赋予变量不同值
   IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ANNUAL_AMP_T';
   ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ANNUAL_AMP_T';
   ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ANNUAL_AMP_T';
   END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
  -- 判断入参为何颗粒度，赋予变量不同值
   IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ANNUAL_AMP_T';
   ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ANNUAL_AMP_T';
   ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ANNUAL_AMP_T';  -- 采购成本
      V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_ANNUAL_AMP_T';  -- 制造成本
      V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ANNUAL_WEIGHT_T ';   --总成本权重表
      V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ANNUAL_AMP_T';
   END IF;
  END IF;
   
  --版本号赋值
  IF F_VERSION_ID IS NULL THEN   -- 版本号未入参，取版本信息表版本号
     V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE   -- 否则，以入参版本为主
     V_VERSION_ID := F_VERSION_ID;
  END IF;
  
 -- 删除总成本年度涨跌幅表同版本的数据
   EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
 
 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除VERSION_ID= '||V_VERSION_ID ||' 的总成本年度涨跌幅（'||V_TO_TABLE||'）数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 取出采购成本的数据计算
  V_SQL := '
   INSERT INTO '||V_TO_TABLE||'(
          VERSION_ID,
          PERIOD_YEAR,
          '||V_LV_CODE||'
          '||V_L1_NAME
          ||V_L2_NAME
          ||V_DIMENSION
          ||V_DIMENSION_SUBCATEGORY
          ||V_DIMENSION_SUB_DETAIL
          ||V_SPART_CODE
          ||V_SPART_NAME
          ||V_DIFF_COLUMN_CODE
          ||V_DIFF_COLUMN_NAME||'
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          ANNUAL_AMP,
          PARENT_CODE,
          PARENT_CN_NAME,
          CREATED_BY,
          CREATION_DATE,
          LAST_UPDATED_BY,
          LAST_UPDATE_DATE,
          DEL_FLAG,
		  '||V_IN_PUBLIC||'
          VIEW_FLAG,
          CALIBER_FLAG
          )  
   WITH AMP_WEIGHT_TMP AS(
        SELECT T1.PERIOD_YEAR,
               '||V_INTO_LV_CODE||'
               '||V_IN_L1_NAME
               ||V_IN_L2_NAME
               ||V_IN_DIMENSION
               ||V_IN_DIMENSION_SUBCATEGORY
               ||V_IN_DIMENSION_SUB_DETAIL
               ||V_IN_SPART
               ||V_IN_DIFF_COLUMN||'
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               T1.ANNUAL_AMP * T2.PERCENTAGE AS AMP_PER,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
			   '||V_INTO_PUBLIC||'
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG
            FROM '||V_FROM_TABLE||' T1
            INNER JOIN '||V_FROM2_TABLE||' T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            '||V_REL_LV_CODE||'
            '||V_REL_L1_NAME
            ||V_REL_L2_NAME
            ||V_REL_DIMENSION
            ||V_REL_DIMENSION_SUBCATEGORY
            ||V_REL_DIMENSION_SUB_DETAIL
            ||V_REL_SPART
            ||V_REL_DIFF_COLUMN||'
            AND T1.GROUP_CODE = T2.GROUP_CODE
            AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
            AND T1.PARENT_CODE = T2.PARENT_CODE
            AND T1.VIEW_FLAG = T2.VIEW_FLAG
            AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
            '||V_REL_PUBLIC||'
            WHERE T1.VERSION_ID = '||V_VERSION_ID||'
            AND T2.VERSION_ID = '||V_VERSION_ID
            ||V_SQL_CONDITION||'
            AND T2.COST_TYPE = ''P''   -- 限制总成本表里的数据为采购成本数据
        UNION ALL
        SELECT T1.PERIOD_YEAR,
               '||V_INTO_LV_CODE||'
               '||V_IN_L1_NAME
               ||V_IN_L2_NAME
               ||V_IN_DIMENSION
               ||V_IN_DIMENSION_SUBCATEGORY
               ||V_IN_DIMENSION_SUB_DETAIL
               ||V_IN_SPART
               ||V_IN_DIFF_COLUMN||'
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               T1.ANNUAL_AMP * T2.PERCENTAGE AS AMP_PER,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
			   '||V_INTO_PUBLIC||'
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG
            FROM '||V_FROM1_TABLE||' T1
            INNER JOIN '||V_FROM2_TABLE||' T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            '||V_REL_LV_CODE||'
            '||V_REL_L1_NAME
            ||V_REL_L2_NAME
            ||V_REL_DIMENSION
            ||V_REL_DIMENSION_SUBCATEGORY
            ||V_REL_DIMENSION_SUB_DETAIL
            ||V_REL_SPART
            ||V_REL_DIFF_COLUMN||'
            AND T1.GROUP_CODE = T2.GROUP_CODE
            AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
            AND T1.PARENT_CODE = T2.PARENT_CODE
            AND T1.VIEW_FLAG = T2.VIEW_FLAG
            AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
            '||V_REL_PUBLIC||'
            WHERE T1.VERSION_ID = '||V_VERSION_ID||'
            AND T2.VERSION_ID = '||V_VERSION_ID
            ||V_SQL_CONDITION||'
            AND T2.COST_TYPE = ''M''   -- 限制总成本表里的数据为制造成本数据
        )
        SELECT '||V_VERSION_ID||' AS VERSION_ID,
               PERIOD_YEAR,
               '||V_LV_CODE||'
               '||V_L1_NAME
               ||V_L2_NAME
               ||V_DIMENSION
               ||V_DIMENSION_SUBCATEGORY
               ||V_DIMENSION_SUB_DETAIL
               ||V_SPART_CODE
               ||V_SPART_NAME
               ||V_DIFF_COLUMN_CODE
               ||V_DIFF_COLUMN_NAME||'
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               SUM(NVL(AMP_PER,0)) AS ANNUAL_AMP,
               PARENT_CODE,
               PARENT_CN_NAME,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,
			   '||V_IN_PUBLIC||'
               VIEW_FLAG,
               CALIBER_FLAG
            FROM AMP_WEIGHT_TMP
            GROUP BY PERIOD_YEAR,
                     '||V_LV_CODE
                     ||V_L1_NAME
                     ||V_L2_NAME
                     ||V_DIMENSION
                     ||V_DIMENSION_SUBCATEGORY
                     ||V_DIMENSION_SUB_DETAIL
                     ||V_SPART_CODE
                     ||V_SPART_NAME
                     ||V_DIFF_COLUMN_CODE
                     ||V_DIFF_COLUMN_NAME||'
                     GROUP_CODE,
                     GROUP_CN_NAME,
                     GROUP_LEVEL,
                     PARENT_CODE,
                     PARENT_CN_NAME,
					 '||V_IN_PUBLIC||'
                     VIEW_FLAG,
                     CALIBER_FLAG';
        DBMS_OUTPUT.PUT_LINE(V_SQL); 
        EXECUTE IMMEDIATE V_SQL;      
       
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||' 的总成本涨跌幅数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  DBMS_OUTPUT.PUT_LINE('收集统计信息成功'); 
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

