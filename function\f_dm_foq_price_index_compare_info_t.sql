-- Name: f_dm_foq_price_index_compare_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_price_index_compare_info_t(p_period_id bigint DEFAULT NULL::bigint, p_base_period bigint DEFAULT NULL::bigint, p_l3_ceg_code character varying DEFAULT NULL::character varying, p_l4_ceg_code character varying DEFAULT NULL::character varying, p_category_code character varying DEFAULT NULL::character varying, p_conduction_phase bigint DEFAULT NULL::bigint, p_metal_percent character varying DEFAULT NULL::character varying, p_query_key character varying DEFAULT NULL::character varying, p_business character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$ 
	/*
创建时间：2023-8-09
创建人  ：qwx1110218
背景描述：价格指数对比信息表，包括原材料价格指数、外部价格指数以及内外部价格指数
参数描述：参数一(p_period)：传入会计期(年月)
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foq_price_index_compare_info_t(202306)
修改记录：20240517    zwx1275798  
          1、新增“business”字段的逻辑
		  2、新增内部价格指数数字能源取数逻辑，来源表“数字能源指数表”
		  3、新增外部价格指数数字能源取数逻辑
*/


declare
	v_sp_name varchar(500) := 'fin_dm_opt_foi.f_dm_foq_price_index_compare_info_t('||p_period_id||','||p_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||','||p_conduction_phase||','||p_metal_percent||','||p_query_key||','||p_business||')';
	v_tbl_name varchar(500) := 'fin_dm_opt_foi.dm_foq_price_index_compare_info_t';
	v_dml_row_count number default 0 ;
	v_desc varchar(100);
	v_count numeric;
	v_remark varchar(100);
	v_def_base_period numeric;  -- 默认基期
	v_query_key varchar(2000);
	v_base_query_key varchar(2000);
	v_max_version_id varchar(100); -- 采购指数项目组的最大版本
	v_max_version_code varchar(100);



begin
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => ''||v_tbl_name||'，开始运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
 
  -- 创建 grp_outs_act_fcst_tmp 临时表
drop table if exists  grp_outs_act_fcst_tmp;
create temporary table grp_outs_act_fcst_tmp(
         business                 varchar(600)    -- 业务（比如ICT、终端、数字能源等）
       , query_key              varchar(2000)
       , data_date              numeric         -- 数据日期
       , base_period            numeric         -- 基期
       , data_type              varchar(100)    -- 数据类别（ACT 实际数、FCST 预测数）
       , l3_ceg_code            varchar(200)    -- 专家团编码
       , l3_ceg_cn_name         varchar(600)    -- 专家团中文名称
       , l3_ceg_short_cn_name   varchar(600)    -- 专家团（Group Lv3简称）
       , l4_ceg_code            varchar(200)    -- 模块编码
       , l4_ceg_cn_name         varchar(600)    -- 模块中文名称
       , l4_ceg_short_cn_name   varchar(600)    -- 模块（Group Lv4简称）
       , category_code          varchar(200)    -- 品类编码
       , category_name          varchar(600)    -- 品类名称
       , index_code             varchar(600)    -- 指标编码
       , index_name             varchar(1000)   -- 指标名称
       , index_short_name       varchar(1000)   -- 指标简称
       , metal_short_cn_name    varchar(1000)   -- 金属简称
       , conduction_phase       numeric         -- 传导期
       , source_name            varchar(1000)   -- 数据源
       , source_table           varchar(1000)   -- 来源表
       , percent                numeric(38,10)  -- 占比
       , data_value             numeric(38,10)  -- 数据值
       , init_base_period_flag  varchar(10)     -- 初始默认基期标识（Y 是、N 否）
  )on commit preserve rows distribute by  hash(query_key,data_date,base_period,category_code,index_code);

-- 创建 raw_materials_category_rel_tmp 临时表
drop table if exists raw_materials_category_rel_tmp;
create temporary table raw_materials_category_rel_tmp(
         business               varchar(600)  -- 业务（比如ICT、终端、数字能源等）
       , query_key              varchar(2000)
       , index_code             varchar(600)  -- 指数编码
       , l3_ceg_code            varchar(200) -- 专家团编码
       , l3_ceg_cn_name         varchar(600) -- 专家团中文名称
       , l3_ceg_short_cn_name   varchar(600) -- 专家团（Group Lv3简称）
       , l4_ceg_code            varchar(200) -- 模块编码
       , l4_ceg_cn_name         varchar(600) -- 模块中文名称
       , l4_ceg_short_cn_name   varchar(600) -- 模块（Group Lv4简称）
       , category_code          varchar(200) -- 品类编码
       , category_name          varchar(600) -- 品类名称
       , metal_short_cn_name    varchar(600) -- 金属简称
       , conduction_phase       numeric      -- 传导期
       , percent                numeric(38,10)  -- 占比
       , init_base_period_flag  varchar(10)     -- 初始默认基期标识（Y 是、N 否）
  )on commit preserve rows distribute by  hash(query_key,index_code,category_code);

-- 创建 metal_percent_tmp1 临时表
  drop table if exists metal_percent_tmp1;
	create temporary table metal_percent_tmp1(
         query_key        varchar(2000)
	   , business         varchar(600)  -- 业务（比如ICT、终端、数字能源等）
       , base_period      numeric      -- 基期
       , month            numeric
       , index_code       varchar(600) -- 指数编码
       , conduction_phase numeric      -- 传导期
       , l3_ceg_code      varchar(200) -- 专家团编码
       , l4_ceg_code      varchar(200) -- 模块编码
       , category_code    varchar(200) -- 品类编码
       , percent          numeric(38,10)  -- 占比
       , remark           varchar(200)
       , last_update_date timestamp
  )on commit preserve rows distribute by  hash(query_key,category_code,index_code,conduction_phase);
    

-- 创建 make_up_data_month_tmp1 临时表
  drop table if exists  make_up_data_month_tmp1;
  create temporary table  make_up_data_month_tmp1(
         query_key   varchar(2000)
       , period_id   numeric
  		 , data_date   numeric
  		 , index_code  varchar(100)
  		 , index_short_name varchar(1000) 
  		 , data_value  numeric(38,10)
  )on commit preserve rows distribute by  hash(query_key,period_id,data_date,index_code);
  
  -- 创建 make_up_data_month_tmp2 临时表
  drop table if exists  make_up_data_month_tmp2;
  create temporary table  make_up_data_month_tmp2(
         query_key varchar(2000)
       , year   numeric
  		 , month   numeric
  		 , index_code  varchar(100)
  		 , index_short_name varchar(1000) 
  )on commit preserve rows distribute by  hash(query_key);
  
  -- 创建 make_up_data_month_tmp9 临时表
  drop table if exists  make_up_data_month_tmp9;
	create temporary table make_up_data_month_tmp9(
         query_key        varchar(2000)
       , index_code       varchar(600)   -- 指数编码
       , index_short_name varchar(1000)  -- 指标简称
       , period_id        numeric        -- 数据日期（包括补录的）
       , data_date        numeric        -- 数据日期（集成表中的）
       , data_value       numeric(38,10) -- 数据值（集成表中的）
       , copy_data_value  numeric(38,10) -- 数据值（用的集成表的哪个数据值补）
       , copy_data_date   numeric        -- 数据日期（用的集成表的哪个数据日期补）
  )on commit preserve rows distribute by  hash(query_key,index_code,period_id);

-- 创建 grp_outs_act_fcst_i_tmp 临时表
  drop table if exists  grp_outs_act_fcst_i_tmp;
	create temporary table  grp_outs_act_fcst_i_tmp(
         query_key        varchar(2000)
       , data_date        numeric        -- 数据日期
       , data_type        varchar(100)   -- 数据类别（ACT 实际数、FCST 预测数）
       , index_code       varchar(600)   -- 指标编码
       , index_name       varchar(1000)  -- 指标名称
       , index_short_name varchar(1000)  -- 指标简称 
       , source_name      varchar(600)   -- 数据源
       , source_table     varchar(600)   -- 来源表
       , data_value       numeric(38,10) -- 数据值
  )on commit preserve rows distribute by  hash(query_key,data_date,index_code);

-- 创建 all_info_tmp2 临时表
  drop table if exists  all_info_tmp2;
	create temporary table  all_info_tmp2(
         query_key                varchar(2000)
	   , business               varchar(600)  -- 业务（比如ICT、终端、数字能源等）
       , data_date                numeric        -- 数据日期
       , data_type                varchar(100)   -- 数据类别（ACT 实际数、FCST 预测数）
       , l3_ceg_code              varchar(200)   -- 专家团编码
       , l3_ceg_cn_name           varchar(600)   -- 专家团中文名称
       , l3_ceg_short_cn_name     varchar(600)   -- 专家团（Group Lv3简称）
       , l4_ceg_code              varchar(200)   -- 模块编码
       , l4_ceg_cn_name           varchar(600)   -- 模块中文名称
       , l4_ceg_short_cn_name     varchar(600)   -- 模块（Group Lv4简称）
       , category_code            varchar(200)   -- 品类编码
       , category_name            varchar(600)   -- 品类名称
       , index_code               varchar(600)   -- 指标编码
       , index_name               varchar(1000)  -- 指标名称
       , index_short_name         varchar(1000)  -- 指标简称
       , metal_short_cn_name      varchar(600)   -- 金属简称
       , conduction_phase         numeric        -- 传导期
       , source_name              varchar(1000)  -- 数据源
       , source_table             varchar(1000)  -- 来源表
       , index_type               varchar(600)   -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
       , percent                  numeric(38,10) -- 占比
       , data_value               numeric(38,10) -- 数据值
       , price_index              numeric(38,10) -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
       , price_index_pp_ptd	      numeric(38,10) -- 环比
   )on commit preserve rows distribute by  hash(query_key,data_date,category_code,index_code);


  -- 如果 传入 p_business = 'ICT' 时，则以ICT价格指数表的基期保持一致
  if p_business = 'ICT' then
  
  select max(version_id) as max_version_id into v_max_version_id
    from fin_dm_opt_foi.dm_foi_plan_version_t
   where upper(data_type) = 'ITEM'
     and upper(version_type) in ('AUTO','FINAL')
     and status = 1
     and del_flag = 'N'
     ;
	 
  select min(base_period_id) as base_period into v_def_base_period 
    from fin_dm_opt_foi.dm_foi_price_index_t
   where version_id = v_max_version_id
  ;
  -- 如果 传入 p_business = '数字能源' 时，则以数字能源价格指数表的基期保持一致
  else
  select max(version_id) as max_version_id into v_max_version_id
    from fin_dm_opt_foi.dm_foi_energy_plan_version_t
   where upper(data_type) = 'ITEM'
     and upper(version_type) in ('AUTO','FINAL')
     and status = 1
     and del_flag = 'N'
  ;
  
  select min(base_period_id) as base_period into v_def_base_period 
    from fin_dm_opt_foi.dm_foi_energy_month_price_index_t
   where version_id = v_max_version_id
  ;
    end if 
  ;	
  
  if not exists(select query_key from metal_percent_tmp1 where query_key = p_query_key) then
    for i in 1..10 loop
      v_desc = split_part(p_metal_percent,',',i);
      insert into metal_percent_tmp1(
             remark
           , query_key
           , conduction_phase
           , l3_ceg_code
           , l4_ceg_code
           , category_code
           , base_period
		   , business
      )
      values(v_desc
           , p_query_key
           , p_conduction_phase
           , p_l3_ceg_code
           , p_l4_ceg_code
           , p_category_code
           , p_base_period
		   , p_business
      );
    end loop;

    delete from metal_percent_tmp1 where remark is null;

    -- 传入金属及占比临时表
    insert into metal_percent_tmp1(
           query_key
		 , business
         , base_period
         , month
         , conduction_phase
         , l3_ceg_code
         , l4_ceg_code
         , category_code
         , index_code
         , percent
         , remark
         , last_update_date
    )
    select distinct t1.query_key
	     , t1.business
         , t1.base_period
         , t2.month
         , t1.conduction_phase
         , t1.l3_ceg_code
         , t1.l4_ceg_code
         , t1.category_code
         , substr(t1.remark,1,position(':' in t1.remark)-1) as index_code
         , (substr(t1.remark,position(':' in t1.remark)+1))::numeric as percent
         , t1.remark
         , CURRENT_TIMESTAMP
      from metal_percent_tmp1 t1
      left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month) t2
        on 1=1
     where t1.query_key = p_query_key
       and t1.conduction_phase = p_conduction_phase
       and t1.l3_ceg_code = p_l3_ceg_code
       and t1.l4_ceg_code = p_l4_ceg_code
       and t1.category_code = p_category_code
       and t1.base_period = p_base_period
	   and t1.business = p_business
    ;

    delete from metal_percent_tmp1 where index_code is null;

  end if;

  if not exists(select query_key from make_up_data_month_tmp1 where query_key = p_query_key) then
    insert into make_up_data_month_tmp1(
           query_key
         , period_id
    		 , data_date
    		 , index_code
    		 , data_value
    )
    -- 只取预测数的金属
    with fcst_metal_tmp2 as(
    select distinct index_code, index_name, index_short_name
      from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i  -- 外购大宗商品与有色金属信息
     where data_category = '预测数'
       and data_date >= v_def_base_period
    )
    -- 预测金属对应的数据日期
    select distinct p_query_key as query_key, t1.period_id, t1.data_date, t1.index_code, t1.data_value
      from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i t1  -- 外购大宗商品与有色金属信息
      join fcst_metal_tmp2 t2
        on t1.index_code = t2.index_code
     where t1.data_category = '实际数'
       and t1.period_id < p_period_id
       and t1.data_date >= v_def_base_period
    union all
    select distinct p_query_key as query_key, t1.period_id, t1.data_date, t1.index_code, t1.data_value
      from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i t1  -- 外购大宗商品与有色金属信息
      join fcst_metal_tmp2 t2
        on t1.index_code = t2.index_code
     where t1.data_category = '预测数'
       and t1.period_id >= p_period_id
       and t1.data_date >= v_def_base_period
    ;
  end if;

  if not exists(select query_key from make_up_data_month_tmp2 where query_key = p_query_key) then
	  insert into make_up_data_month_tmp2(
         query_key
       , year
	  	 , month
	  	 , index_code
    )
    -- 预测金属对应全的数据日期（包括补齐的数据日期）
    select distinct t1.query_key, t3.year, t2.month, t1.index_code
      from make_up_data_month_tmp1 t1
      left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month)t2
        on 1=1
      left join (select distinct substr(data_date,1,4) as year from make_up_data_month_tmp1 where query_key = p_query_key) t3
        on 1=1
     where t1.query_key = p_query_key
    ;
  end if;

  if not exists(select query_key from make_up_data_month_tmp9 where query_key = p_query_key) then
    -- 补齐的数据
	  insert into make_up_data_month_tmp9(
           query_key
         , index_code        -- 指数编码
         , period_id         -- 数据日期（包括补录的）
         , data_date         -- 数据日期（集成表中的）
         , data_value        -- 数据值（集成表中的）
         , copy_data_value   -- 数据值（用的集成表的哪个数据值补）
         , copy_data_date    -- 数据日期（用的集成表的哪个数据日期补）
    )
    -- 带出已经存在的数据日期的信息
    with make_up_data_month_tmp33 as(
    select t1.index_code, t1.year, t1.month, t2.period_id, t2.data_date, t2.data_value
      from make_up_data_month_tmp2 t1
      left join make_up_data_month_tmp1 t2
        on t1.index_code = t2.index_code
       and t1.year = substr(t2.data_date,1,4)
       and t1.month = substr(t2.data_date,5,2)
       and t1.query_key = t2.query_key
     where t1.query_key = p_query_key
    ),
    -- 取数据日期不为空的每年最小日期，用于补每年一开始就缺失的日期数据
    make_up_data_month_tmp4 as(
    select index_code, year, min(data_date) as min_data_date
      from make_up_data_month_tmp33
     where data_date is not null
     group by index_code, year
    ),
    -- 每年最小日期的金额
    make_up_data_month_tmp5 as(
    select t1.index_code, t1.data_date, t1.data_value
      from make_up_data_month_tmp33 t1
      join make_up_data_month_tmp4 t2
        on t1.index_code = t2.index_code
       and t1.data_date = t2.min_data_date
    ),
    make_up_data_month_tmp6 as(
    select index_code, year, month, data_date, decode(data_value,0,null,data_value) as data_value
         , last_value(decode(data_value,0,null,data_value)) over(partition by index_code order by year||lpad(month,2,0)) as amt_c
         , count(decode(data_value,0,null,data_value)) over(partition by index_code,year order by year,month) as cnt
      from make_up_data_month_tmp33
    ),
    make_up_data_month_tmp7 as(
    select t1.index_code, t1.year, t1.month, t1.year||lpad(t1.month,2,0) as period_id, t1.data_date, t1.data_value
         , (case when (max(t1.amt_c) over(partition by t1.index_code, t1.year, t1.cnt)) is null
                 then t2.data_value
                 else (min(t1.amt_c) over(partition by t1.index_code, t1.year, t1.cnt))
            end) as copy_data_value
         , (case when (max(t1.data_date) over(partition by t1.index_code, t1.year, t1.cnt)) is null
                 then t2.data_date
                 else (min(t1.data_date) over(partition by t1.index_code, t1.year, t1.cnt))
            end) as copy_data_date
      from make_up_data_month_tmp6 t1
      left join make_up_data_month_tmp5 t2
        on t1.index_code = t2.index_code
       and t1.year = substr(t2.data_date,1,4)
    ),
    make_up_data_month_tmp8 as(
    select index_code, year||lpad(month,2,0) as period_id, data_date, data_value, copy_data_value, copy_data_date
         , count(decode(copy_data_value,0,null,copy_data_value)) over(partition by index_code order by index_code, period_id desc) as cnt2
      from make_up_data_month_tmp7
    )
    select distinct p_query_key as query_key, index_code, period_id, data_date, data_value
         , (case when copy_data_value is null
                 then first_value(copy_data_value) over(partition by index_code, cnt2 order by period_id desc)
                 else copy_data_value
            end) as copy_data_value
         , (case when copy_data_value is null
                 then first_value(copy_data_date) over(partition by index_code, cnt2 order by period_id desc)
                 else copy_data_date
            end) as copy_data_date
      from make_up_data_month_tmp8
    ;
  end if;

  -- 传入参数有值，则根据传入参数 query_key 查询目标表是否有数据，如有则跳出，否则根据传入参数计算
  if(p_query_key is not null or p_query_key <> '') then
    -- 判断已存在的query_key值指数类型是否有4种（原材料价格指数、外部价格指数、内部价格指数、GAP），如否，则需要删除此query_key值
    if((select count(distinct index_type) from fin_dm_opt_foi.dm_foq_price_index_compare_info_t where query_key = p_query_key) = 4) then
       perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '会计期：'||p_period_id||'，基期：'||p_base_period||'，专家团编码：'||p_l3_ceg_code||'，模块编码：'||p_l4_ceg_code||'，品类编码：'||p_category_code||'，传导期：'||p_conduction_phase||'，金属及占比：'||p_metal_percent||'， query_key 值：'||p_query_key||'，目标表中此 query_key 的指数类型有4种！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => null,
            p_log_errbuf => null  --错误编码
          ) ;
        x_success_flag := 2001;
        return;
       
    else
      -- 已存在的query_key值指数类型没有4种（原材料价格指数、外部价格指数、内部价格指数、GAP），需要删除，然后再计算
      delete from fin_dm_opt_foi.dm_foq_price_index_compare_info_t where query_key = p_query_key;
        -- 【默认基期 start】
        -- 传入基期是默认基期
        if(p_base_period = v_def_base_period) then
          -- 判断默认基期是否已存在计算的数据，如存在则不计算，否则计算
          if exists(select query_key from fin_dm_opt_foi.dm_foq_price_index_compare_info_t where query_key = p_query_key) then
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 2,
                p_log_cal_log_desc => '传入 query_key 值：'||p_query_key||'，目标表中已存在此数据！',--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => null,
                p_log_errbuf => null  --错误编码
              ) ;
            x_success_flag := 2001;
            return;

          -- 默认基期不存在，需要计算；
          else
            if not exists(select query_key from raw_materials_category_rel_tmp where query_key = p_query_key) then            
              -- 根据传入参数取对应的最新版本专家团、模型信息，如果传入的是初始默认基期，则能取到对应的数据，否则取不到对应的数据
              insert into raw_materials_category_rel_tmp(
                     query_key
				   , business               -- 业务（目前是ICT、数字能源）
                   , index_code             -- 指数编码
                   , l3_ceg_code            -- 专家团编码
                   , l3_ceg_cn_name         -- 专家团中文名称
                   , l3_ceg_short_cn_name   -- 专家团（Group Lv3简称）
                   , l4_ceg_code            -- 模块编码
                   , l4_ceg_cn_name         -- 模块中文名称
                   , l4_ceg_short_cn_name   -- 模块（Group Lv4简称）
                   , category_code          -- 品类编码
                   , category_name          -- 品类名称
                   , metal_short_cn_name    -- 金属简称
                   , conduction_phase       -- 传导期
                   , percent                -- 占比
                   , init_base_period_flag  -- 初始默认基期标识（Y 是、N 否）
              )
              with metal_percent_tmp2 as(
              select distinct business,index_code, l3_ceg_code, l4_ceg_code, category_code, conduction_phase, percent 
                from metal_percent_tmp1 
               where query_key = p_query_key
              )
              select distinct p_query_key
			       , t1.business        -- 业务（目前是ICT、数字能源）
                   , t1.index_code      -- 指数编码
                   , (case when t1.l3_ceg_code = t2.l3_ceg_code then t1.l3_ceg_code else t2.l3_ceg_code end) as l3_ceg_code  -- 专家团编码
                   , t1.l3_ceg_cn_name           -- 专家团中文名称
                   , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                   , (case when t1.l4_ceg_code = t2.l4_ceg_code then t1.l4_ceg_code else t2.l4_ceg_code end) as l4_ceg_code  -- 模块编码
                   , t1.l4_ceg_cn_name           -- 模块中文名称
                   , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                   , (case when t1.category_code = t2.category_code then t1.category_code else t2.category_code end) as category_code -- 品类编码
                   , t1.category_name            -- 品类名称
                   , t1.metal_short_cn_name      -- 金属简称
                   , (case when t1.conduction_phase = t2.conduction_phase then t1.conduction_phase else t2.conduction_phase end) as conduction_phase -- 传导期
                   , (case when t1.percent = t2.percent then t1.percent else t2.percent end) as percent  -- 占比
                   , (case when t1.l3_ceg_code = t2.l3_ceg_code
                            and t1.l4_ceg_code = t2.l4_ceg_code
                            and t1.category_code = t2.category_code
                            and t1.conduction_phase = t2.conduction_phase
                           then 'Y'
                           else 'N'
                      end) as init_base_period_flag  -- 初始默认基期标识（Y 是、N 否）
                from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t t1  -- 原材料与品类量化建模关系表
                left join metal_percent_tmp2 t2  -- 传入金属及占比
                  on t1.index_code = t2.index_code
				 and t1.business = t2.business
               where t1.del_flag = 'N'
                 and upper(t1.status) = 'SUBMIT'
                 and t1.version_code = (select max(version_code) from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t where del_flag = 'N' and upper(status) = 'SUBMIT' and business = p_business)  -- 取最大版本
                 and t1.category_code = p_category_code  -- 传入品类
				 and t1.business = p_business
              ;
            
              v_dml_row_count := sql%rowcount;  -- 收集数据量
            
              perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                  p_log_version_id => null,                 --版本
                  p_log_sp_name => v_sp_name,    --sp名称
                  p_log_para_list => '',--参数
                  p_log_step_num  => 2,           
                  p_log_cal_log_desc => '【默认基期计算】传入参数：'||p_period_id||','||p_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||','||p_conduction_phase||','||p_metal_percent||','||p_query_key||'，根据传入参数取对应的最新版本专家团、模型信息，入到 raw_materials_category_rel_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
                  p_log_formula_sql_txt => null,--错误信息
                  p_log_row_count => v_dml_row_count,
                  p_log_errbuf => null  --错误编码
              ) ;
            end if;
            
            -- 清理 grp_outs_act_fcst_i_tmp 表数据
            truncate table grp_outs_act_fcst_i_tmp;
            
            -- 实际数取<p_period_id；预测数取>=p_period_id
            insert into grp_outs_act_fcst_i_tmp(
                   query_key
                 , data_date        -- 数据日期
                 , data_type        -- 数据类别（ACT 实际数、FCST 预测数）
                 , index_code       -- 指标编码
                 , index_name       -- 指标名称
                 , source_name      -- 数据源
                 , source_table     -- 来源表
                 , data_value       -- 数据值
            )
            select p_query_key as query_key
                 , data_date::numeric           -- 数据日期
                 , data_category as data_type   -- 数据类别（ACT 实际数、FCST 预测数）
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , data_value               -- 数据值
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i -- 外购大宗商品与有色金属信息
             where data_date >= v_def_base_period
               and data_category = '实际数'
               and period_id < p_period_id
            union all
            select p_query_key as query_key
                 , data_date::numeric           -- 数据日期
                 , data_category as data_type   -- 数据类别（ACT 实际数、FCST 预测数）
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , data_value               -- 数据值
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i -- 外购大宗商品与有色金属信息
             where data_date >= v_def_base_period
               and data_category = '预测数'
               and period_id >= p_period_id
            ;
            
            v_dml_row_count := sql%rowcount;  -- 收集数据量
            
              perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                  p_log_version_id => null,                 --版本
                  p_log_sp_name => v_sp_name,    --sp名称
                  p_log_para_list => '',--参数
                  p_log_step_num  => 3,
                  p_log_cal_log_desc => '【默认基期计算】价格指数总览表的默认基期：'||v_def_base_period||'，传入参数：'||p_period_id||'，'||p_query_key||'，从外购大宗商品表取数，入到 grp_outs_act_fcst_i_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
                  p_log_formula_sql_txt => null,--错误信息
                  p_log_row_count => v_dml_row_count,
                  p_log_errbuf => null  --错误编码
              ) ;

            -- 判断临时表中"初始默认基期标识"是否有N，如有，则"初始默认基期标识"赋值为"N"
            if exists(select index_code from raw_materials_category_rel_tmp where init_base_period_flag = 'N' and query_key = p_query_key ) then
              
              -- 数据清理
              delete from grp_outs_act_fcst_tmp where query_key = p_query_key;
              
              -- 关联出专家团、模块等信息的数据
              insert into grp_outs_act_fcst_tmp(
                     query_key
				   , business                -- 业务（目前是ICT、数字能源）
                   , data_date               -- 数据日期
                   , base_period             -- 基期
                   , data_type               -- 数据类别（ACT 实际数、FCST 预测数）
                   , l3_ceg_code             -- 专家团编码
                   , l3_ceg_cn_name          -- 专家团中文名称
                   , l3_ceg_short_cn_name    -- 专家团（Group Lv3简称）
                   , l4_ceg_code             -- 模块编码
                   , l4_ceg_cn_name          -- 模块中文名称
                   , l4_ceg_short_cn_name    -- 模块（Group Lv4简称）
                   , category_code           -- 品类编码
                   , category_name           -- 品类名称
                   , index_code              -- 指标编码
                   , index_name              -- 指标名称
                   , index_short_name        -- 指标简称
                   , metal_short_cn_name     -- 金属简称
                   , conduction_phase        -- 传导期
                   , source_name             -- 数据源
                   , source_table            -- 来源表
                   , percent                 -- 占比
                   , data_value              -- 数据值
                   , init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
              )
              -- 只取预测数的金属
              with fcst_metal_tmp as(
              select distinct index_code, index_name, index_short_name
                from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i  -- 外购大宗商品与有色金属信息
               where data_category = '预测数'
                 and data_date >= v_def_base_period
              )
              select p_query_key as query_key
			       , t2.business                -- 业务（目前是ICT、数字能源）
                   , t1.data_date                -- 数据日期
                   , p_base_period as base_period -- 基期
                   , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                   , t2.l3_ceg_code              -- 专家团编码
                   , t2.l3_ceg_cn_name           -- 专家团中文名称
                   , t2.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                   , t2.l4_ceg_code              -- 模块编码
                   , t2.l4_ceg_cn_name           -- 模块中文名称
                   , t2.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                   , t2.category_code            -- 品类编码
                   , t2.category_name            -- 品类名称
                   , t1.index_code               -- 指标编码
                   , t1.index_name               -- 指标名称
                   , t3.index_short_name         -- 指标简称
                   , t2.metal_short_cn_name      -- 金属简称
                   , t2.conduction_phase         -- 传导期
                   , t1.source_name              -- 数据源
                   , t1.source_table             -- 来源表
                   , t2.percent                  -- 占比
                   , t1.data_value               -- 数据值
                   , 'N' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
                from grp_outs_act_fcst_i_tmp t1  -- 外购大宗商品与有色金属信息临时表
                left join raw_materials_category_rel_tmp t2  -- 原材料与品类量化建模关系临时表
                  on t1.index_code = t2.index_code
                 and t1.query_key = t2.query_key
                join fcst_metal_tmp t3  -- 预测数金属临时表
                  on t1.index_code = t3.index_code
               where t2.l3_ceg_code = p_l3_ceg_code  -- 传入参数
                 and t2.l4_ceg_code = p_l4_ceg_code  -- 传入参数
                 and t2.category_code = p_category_code  -- 传入参数
                 and t1.query_key = p_query_key
				 and t2.business = p_business
              union all
              -- 补齐的数据
              select p_query_key as query_key
			       , t4.business                 -- 业务（目前是ICT、数字能源）
                   , t1.period_id as data_date   -- 数据日期
                   , p_base_period as base_period -- 基期
                   , '' as data_type             -- 数据类别（ACT 实际数、FCST 预测数）
                   , t4.l3_ceg_code              -- 专家团编码
                   , t4.l3_ceg_cn_name           -- 专家团中文名称
                   , t4.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                   , t4.l4_ceg_code              -- 模块编码
                   , t4.l4_ceg_cn_name           -- 模块中文名称
                   , t4.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                   , t4.category_code            -- 品类编码
                   , t4.category_name            -- 品类名称
                   , t2.index_code               -- 指标编码
                   , t2.index_name               -- 指标名称
                   , t3.index_short_name         -- 指标简称
                   , t4.metal_short_cn_name      -- 金属简称
                   , t4.conduction_phase         -- 传导期
                   , t2.source_name              -- 数据源
                   , t2.source_table             -- 来源表
                   , t4.percent                  -- 占比
                   , t2.data_value               -- 数据值
                   , 'N' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
                from make_up_data_month_tmp9 t1
                join grp_outs_act_fcst_i_tmp t2
                  on t1.index_code = t2.index_code
                 and t1.copy_data_date = t2.data_date
                 and t2.query_key = p_query_key
                join fcst_metal_tmp t3  -- 预测数金属临时表
                  on t1.index_code = t3.index_code
                left join raw_materials_category_rel_tmp t4  -- 原材料与品类量化建模关系临时表
                  on t1.index_code = t4.index_code
                 and t4.query_key = p_query_key
               where t1.data_date is null
                 and t4.l3_ceg_code = p_l3_ceg_code
                 and t4.l4_ceg_code = p_l4_ceg_code
                 and t4.category_code = p_category_code
                 and t1.query_key = p_query_key
				 and t4.business = p_business
              ;
  
              v_dml_row_count := sql%rowcount;  -- 收集数据量
  
              perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                 p_log_version_id => null,                 --版本
                 p_log_sp_name => v_sp_name,    --sp名称
                 p_log_para_list => '',--参数
                 p_log_step_num  => 4,
                 p_log_cal_log_desc => '【默认基期计算】入到 grp_outs_act_fcst_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
                 p_log_formula_sql_txt => null,--错误信息
                 p_log_row_count => v_dml_row_count,
                 p_log_errbuf => null  --错误编码
               ) ;

            -- 判断临时表中"初始默认基期标识"是否有N，如没有，则"初始默认基期标识"赋值为"Y"
            else
              
              -- 数据清理
              delete from grp_outs_act_fcst_tmp where query_key = p_query_key;
              
              -- 关联出专家团、模块等信息的数据
              insert into grp_outs_act_fcst_tmp(
                     query_key
					 , business                -- 业务（目前是ICT、数字能源）
                     , data_date               -- 数据日期
                     , base_period             -- 基期
                     , data_type               -- 数据类别（ACT 实际数、FCST 预测数）
                     , l3_ceg_code             -- 专家团编码
                     , l3_ceg_cn_name          -- 专家团中文名称
                     , l3_ceg_short_cn_name    -- 专家团（Group Lv3简称）
                     , l4_ceg_code             -- 模块编码
                     , l4_ceg_cn_name          -- 模块中文名称
                     , l4_ceg_short_cn_name    -- 模块（Group Lv4简称）
                     , category_code           -- 品类编码
                     , category_name           -- 品类名称
                     , index_code              -- 指标编码
                     , index_name              -- 指标名称
                     , index_short_name        -- 指标简称
                     , metal_short_cn_name     -- 金属简称
                     , conduction_phase        -- 传导期
                     , source_name             -- 数据源
                     , source_table            -- 来源表
                     , percent                 -- 占比
                     , data_value              -- 数据值
                     , init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
              )-- 只取预测数的金属
              with fcst_metal_tmp as(
              select distinct index_code, index_name, index_short_name
                from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i  -- 外购大宗商品与有色金属信息
               where data_category = '预测数'
                 and data_date >= v_def_base_period
              )
              select p_query_key as query_key
			       , t2.business                 -- 业务（目前是ICT、数字能源）
                   , t1.data_date                -- 数据日期
                   , p_base_period as base_period -- 基期
                   , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                   , t2.l3_ceg_code              -- 专家团编码
                   , t2.l3_ceg_cn_name           -- 专家团中文名称
                   , t2.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                   , t2.l4_ceg_code              -- 模块编码
                   , t2.l4_ceg_cn_name           -- 模块中文名称
                   , t2.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                   , t2.category_code            -- 品类编码
                   , t2.category_name            -- 品类名称
                   , t1.index_code               -- 指标编码
                   , t1.index_name               -- 指标名称
                   , t3.index_short_name         -- 指标简称
                   , t2.metal_short_cn_name      -- 金属简称
                   , t2.conduction_phase         -- 传导期
                   , t1.source_name              -- 数据源
                   , t1.source_table             -- 来源表
                   , t2.percent                  -- 占比
                   , t1.data_value               -- 数据值
                   , 'Y' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
                from grp_outs_act_fcst_i_tmp t1  -- 外购大宗商品与有色金属信息临时表
                left join raw_materials_category_rel_tmp t2  -- 原材料与品类量化建模关系临时表
                  on t1.index_code = t2.index_code
                 and t1.query_key = t2.query_key
                join fcst_metal_tmp t3  -- 预测数金属临时表
                  on t1.index_code = t3.index_code
               where t2.l3_ceg_code = p_l3_ceg_code  -- 传入参数
                 and t2.l4_ceg_code = p_l4_ceg_code  -- 传入参数
                 and t2.category_code = p_category_code  -- 传入参数
                 and t1.query_key = p_query_key
				 and t2.business = p_business
              union all
              -- 补齐的数据
              select p_query_key as query_key
			       , t4.business                 -- 业务（目前是ICT、数字能源）
                   , t1.period_id as data_date   -- 数据日期
                   , p_base_period as base_period -- 基期
                   , '' as data_type             -- 数据类别（ACT 实际数、FCST 预测数）
                   , t4.l3_ceg_code              -- 专家团编码
                   , t4.l3_ceg_cn_name           -- 专家团中文名称
                   , t4.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                   , t4.l4_ceg_code              -- 模块编码
                   , t4.l4_ceg_cn_name           -- 模块中文名称
                   , t4.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                   , t4.category_code            -- 品类编码
                   , t4.category_name            -- 品类名称
                   , t2.index_code               -- 指标编码
                     , t2.index_name               -- 指标名称
                     , t3.index_short_name         -- 指标简称
                     , t4.metal_short_cn_name      -- 金属简称
                     , t4.conduction_phase         -- 传导期
                     , t2.source_name              -- 数据源
                     , t2.source_table             -- 来源表
                     , t4.percent                  -- 占比
                     , t2.data_value               -- 数据值
                     , 'Y' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
                  from make_up_data_month_tmp9 t1
                  join grp_outs_act_fcst_i_tmp t2
                    on t1.index_code = t2.index_code
                   and t2.query_key = p_query_key
                   and t1.copy_data_date = t2.data_date
                  join fcst_metal_tmp t3  -- 预测数金属临时表
                    on t1.index_code = t3.index_code
                  left join raw_materials_category_rel_tmp t4  -- 原材料与品类量化建模关系临时表
                    on t1.index_code = t4.index_code
                   and t4.query_key = p_query_key
                 where t1.data_date is null
                   and t4.l3_ceg_code = p_l3_ceg_code
                   and t4.l4_ceg_code = p_l4_ceg_code
                   and t4.category_code = p_category_code
                   and t1.query_key = p_query_key
				   and t4.business = p_business
                ;
  
                v_dml_row_count := sql%rowcount;  -- 收集数据量
  
                perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                   p_log_version_id => null,                 --版本
                   p_log_sp_name => v_sp_name,    --sp名称
                   p_log_para_list => '',--参数
                   p_log_step_num  => 4,
                   p_log_cal_log_desc => '【默认基期计算】入到 grp_outs_act_fcst_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
                   p_log_formula_sql_txt => null,--错误信息
                   p_log_row_count => v_dml_row_count,
                   p_log_errbuf => null  --错误编码
                 ) ;
  
            end if;

            -- 原材料价格指数、外部价格指数入到目标表
            insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                   query_key                  -- 查询键
				 , business
                 , period_id                  -- 会计期
                 , data_date                  -- 数据日期
                 , base_period                -- 基期
                 , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                 , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code                -- 专家团编码
                 , l3_ceg_cn_name             -- 专家团中文名称
                 , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                 , l4_ceg_code                -- 模块编码
                 , l4_ceg_cn_name             -- 模块中文名称
                 , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                 , category_code              -- 品类编码
                 , category_name              -- 品类名称
                 , index_code                 -- 指标编码
                 , index_name                 -- 指标名称
                 , index_short_name           -- 指标简称
                 , metal_short_cn_name        -- 金属简称
                 , conduction_phase           -- 传导期
                 , source_name                -- 数据源
                 , source_table               -- 来源表
                 , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                    -- 占比
                 , data_value                 -- 数据值
                 , price_index                -- 价格指数
                 , price_index_pp_ptd         -- 环比
                 , price_index_gap            -- 差异额
                 , remark                     -- 备注
                 , created_by                 -- 创建人
                 , creation_date              -- 创建时间
                 , last_updated_by            -- 修改人
                 , last_update_date           -- 修改时间
                 , del_flag                   -- 是否删除
            )
           -- 只取Top品类清单表的品类
           with detail_tmp as(
           select t1.query_key
		        , 'ICT' as business           -- 业务（目前是ICT、数字能源）
                , t1.data_date                -- 数据日期
                , t1.base_period              -- 基期
                , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                , t1.l3_ceg_code              -- 专家团编码
                , t1.l3_ceg_cn_name           -- 专家团中文名称
                , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , t1.l4_ceg_code              -- 模块编码
                , t1.l4_ceg_cn_name           -- 模块中文名称
                , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , t1.category_code            -- 品类编码
                , t1.category_name            -- 品类名称
                , t1.index_code               -- 指标编码
                , t1.index_name               -- 指标名称
                , t1.index_short_name         -- 指标简称
                , t1.metal_short_cn_name      -- 金属简称
                , t1.conduction_phase         -- 传导期
                , t1.source_name              -- 数据源
                , t1.source_table             -- 来源表
                , t1.percent                  -- 占比
                , t1.data_value               -- 数据值
                , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
             from grp_outs_act_fcst_tmp t1  -- 外购大宗商品与有色金属信息
             join fin_dm_opt_foi.dm_foq_top_cate_info_v t2  -- 最大版本的Top品类清单视图
               on t1.category_code = t2.category_code
            where t1.query_key = p_query_key
			  and t1.business = 'ICT'
			  union all
			   select t1.query_key
		        , '数字能源' as business      -- 业务（目前是ICT、数字能源）
                , t1.data_date                -- 数据日期
                , t1.base_period              -- 基期
                , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                , t1.l3_ceg_code              -- 专家团编码
                , t1.l3_ceg_cn_name           -- 专家团中文名称
                , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , t1.l4_ceg_code              -- 模块编码
                , t1.l4_ceg_cn_name           -- 模块中文名称
                , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , t1.category_code            -- 品类编码
                , t1.category_name            -- 品类名称
                , t1.index_code               -- 指标编码
                , t1.index_name               -- 指标名称
                , t1.index_short_name         -- 指标简称
                , t1.metal_short_cn_name      -- 金属简称
                , t1.conduction_phase         -- 传导期
                , t1.source_name              -- 数据源
                , t1.source_table             -- 来源表
                , t1.percent                  -- 占比
                , t1.data_value               -- 数据值
                , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
             from grp_outs_act_fcst_tmp t1  -- 外购大宗商品与有色金属信息
             join fin_dm_opt_foi.dm_foq_energy_top_cate_info_t_v t2  -- 最大版本的Top品类清单视图
               on t1.category_code = t2.category_code
            where t1.query_key = p_query_key
			  and t1.business = '数字能源'
           ),
           -- 根据传入参数取默认基期数据
           default_base_period_tmp as(
           select data_date as base_period, business,category_code, l3_ceg_code, l4_ceg_code, index_code, data_value
             from detail_tmp
            where data_date = p_base_period
			  and business = p_business
           ),
           -- 原材料价格指数临时表（默认基期）
           def_rm_price_index_tmp as(
           select t1.data_date, t1.business,t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code, t1.index_code
                , (case when coalesce(t2.data_value,0) = 0 then 0
                        else round(coalesce(t1.data_value,0)/coalesce(t2.data_value,0),10)*100
                   end) as rm_price_index  -- 原材料价格指数
             from detail_tmp t1
             left join default_base_period_tmp t2
               on t1.category_code = t2.category_code
              and t1.l3_ceg_code = t2.l3_ceg_code
              and t1.l4_ceg_code = t2.l4_ceg_code
              and t1.index_code = t2.index_code
			  and t1.business = t2.business
			 where t1.business = p_business
           ),
           -- 固定占比
           fa_percent_tmp as(
           select data_date, business,category_code, l3_ceg_code, l4_ceg_code, (1 - sum(coalesce(percent,0))) as fa_percent  -- 固定占比
             from (select distinct data_date, business,category_code, l3_ceg_code, l4_ceg_code, index_code,percent from detail_tmp) T
			 where business = p_business
            group by data_date, business,category_code, l3_ceg_code, l4_ceg_code
           ),
           -- 外部价格指数临时表（默认基期）
           def_exter_price_index_tmp as(
           select t1.data_date, t1.business,t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code, t1.index_code
                , (case when coalesce(t2.data_value,0) = 0 then 0
                        else round(coalesce(t1.data_value,0)/coalesce(t2.data_value,0)*coalesce(t1.percent,0),10)  -- 报告期价格指数/默认基期价格指数*金属占比
                   end) as exter_price_index  -- 外部价格指数
             from detail_tmp t1
             left join default_base_period_tmp t2
               on t1.category_code = t2.category_code
              and t1.index_code = t2.index_code
              and t1.l3_ceg_code = t2.l3_ceg_code
              and t1.l4_ceg_code = t2.l4_ceg_code
			  and t1.business = t2.business
			where t1.business = p_business
           union all
           select data_date, business,category_code, l3_ceg_code, l4_ceg_code, '' as index_code, fa_percent as exter_price_index -- 外部价格指数
             from fa_percent_tmp
           ),
           -- 外部价格指数按品类汇总
           def_exter_price_index_sum_tmp as(
           select data_date, business,category_code, l3_ceg_code, l4_ceg_code, sum(exter_price_index)*100 as exter_price_index -- 外部价格指数
             from def_exter_price_index_tmp
            group by data_date, business,category_code, l3_ceg_code, l4_ceg_code
           ),
           -- 上月外部价格指数数据
           last_def_exter_price_index_tmp as(
           select to_char(to_date(data_date,'yyyymm') + interval'1 month','yyyymm')::numeric as last_data_date  -- 上月日期
                , data_date, business,category_code, l3_ceg_code, l4_ceg_code, exter_price_index -- 外部价格指数
             from def_exter_price_index_sum_tmp t1
           ),
           -- 外部价格指数环比
           exter_price_index_pp_ptd_tmp as(
           select t1.data_date, t1.business,t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code
                , (case when coalesce(t2.exter_price_index,0) = 0 then 0
                        else (coalesce(t1.exter_price_index,0)-coalesce(t2.exter_price_index,0))/coalesce(t2.exter_price_index,0)
                   end) as exter_price_index_pp_ptd  -- 外部价格指数环比
             from def_exter_price_index_sum_tmp t1  -- 当月数据
             left join last_def_exter_price_index_tmp t2 -- 上月数据
               on t1.data_date = t2.last_data_date
              and t1.category_code = t2.category_code
              and t1.l3_ceg_code = t2.l3_ceg_code
              and t1.l4_ceg_code = t2.l4_ceg_code
			  and t1.business = t2.business
           ),
           -- 原材料价格指数（默认基期）
           all_info_tmp1 as(
           select t1.query_key
		        , t1.business
                , t1.data_date                -- 数据日期
                , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                , t1.l3_ceg_code              -- 专家团编码
                , t1.l3_ceg_cn_name           -- 专家团中文名称
                , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , t1.l4_ceg_code              -- 模块编码
                , t1.l4_ceg_cn_name           -- 模块中文名称
                , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , t1.category_code            -- 品类编码
                , t1.category_name            -- 品类名称
                , t1.index_code               -- 指标编码
                , t1.index_name               -- 指标名称
                , t1.index_short_name         -- 指标简称
                , t1.metal_short_cn_name      -- 金属简称
                , t1.conduction_phase         -- 传导期
                , t1.source_name              -- 数据源
                , t1.source_table             -- 来源表
                , '原材料价格指数' as index_type  -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                , t1.percent                  -- 占比
                , t1.data_value               -- 数据值
                , t2.rm_price_index as price_index  -- 价格指数
                , null as price_index_pp_ptd	 -- 环比
                , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
             from detail_tmp t1
             left join def_rm_price_index_tmp t2
               on t1.data_date = t2.data_date
              and t1.category_code = t2.category_code
              and t1.l3_ceg_code = t2.l3_ceg_code
              and t1.l4_ceg_code = t2.l4_ceg_code
              and t1.index_code = t2.index_code
			  and t1.business = t2.business
			where t1.business = p_business
           union all
           -- 外部材料价格指数（默认基期）
           select t1.query_key
		        , t1.business
                , (case when t1.conduction_phase = t4.conduction_phase then to_char(add_months(to_date(t1.data_date,'yyyymm'),t1.conduction_phase),'yyyymm')::numeric
                        else to_char(add_months(to_date(t1.data_date,'yyyymm'),@(t4.conduction_phase - t1.conduction_phase)),'yyyymm')::numeric   -- @ 绝对值
                   end) as data_date -- 数据日期
                , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                , t1.l3_ceg_code              -- 专家团编码
                , t1.l3_ceg_cn_name           -- 专家团中文名称
                , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , t1.l4_ceg_code              -- 模块编码
                , t1.l4_ceg_cn_name           -- 模块中文名称
                , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , t1.category_code            -- 品类编码
                , t1.category_name            -- 品类名称
                , t1.index_code               -- 指标编码
                , t1.index_name               -- 指标名称
                , t1.index_short_name         -- 指标简称
                , t1.metal_short_cn_name      -- 金属简称
                , t1.conduction_phase         -- 传导期
                , t1.source_name              -- 数据源
                , t1.source_table             -- 来源表
                , '外部价格指数' as index_type  -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                , t1.percent                  -- 占比
                , t1.data_value               -- 数据值
                , t2.exter_price_index        as price_index  -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
                , t3.exter_price_index_pp_ptd as price_index_pp_ptd	 -- 环比
                , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
             from detail_tmp t1
             left join def_exter_price_index_sum_tmp t2
               on t1.data_date = t2.data_date
              and t1.category_code = t2.category_code
              and t1.l3_ceg_code = t2.l3_ceg_code
              and t1.l4_ceg_code = t2.l4_ceg_code
			  and t1.business = t2.business
             left join exter_price_index_pp_ptd_tmp t3
               on t1.data_date = t3.data_date
              and t1.category_code = t3.category_code
              and t1.l3_ceg_code = t3.l3_ceg_code
              and t1.l4_ceg_code = t3.l4_ceg_code
			  and t1.business = t3.business
             left join (select distinct query_key, conduction_phase from metal_percent_tmp1) t4
               on t1.query_key = t4.query_key
			where t1.business = p_business
           )
           select query_key -- 查询键
		        , business
                , p_period_id as period_id -- 会计期
                , data_date                -- 数据日期
                , p_base_period as base_period  -- 基期
                , init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
                , data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                , l3_ceg_code              -- 专家团编码
                , l3_ceg_cn_name           -- 专家团中文名称
                , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , l4_ceg_code              -- 模块编码
                , l4_ceg_cn_name           -- 模块中文名称
                , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , category_code            -- 品类编码
                , category_name            -- 品类名称
                , index_code               -- 指标编码
                , index_name               -- 指标名称
                , index_short_name         -- 指标简称
                , metal_short_cn_name      -- 金属简称
                , conduction_phase         -- 传导期
                , source_name              -- 数据源
                , source_table             -- 来源表
                , index_type               -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                , percent                  -- 占比
                , data_value               -- 数据值
                , price_index              -- 价格指数
                , price_index_pp_ptd	      -- 环比
                , null as price_index_gap	        -- 差异额
                , '' as remark                          -- 备注
          	   , -1 as created_by                      -- 创建人
          	   , current_timestamp as creation_date    -- 创建时间
          	   , -1 as last_updated_by                 -- 修改人
          	   , current_timestamp as last_update_date -- 修改时间
          	   , 'N' as del_flag                       -- 是否删除
             from all_info_tmp1
            where data_date >= p_base_period
           ;
   
           v_dml_row_count := sql%rowcount;  -- 收集数据量
   
           perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
               p_log_version_id => null,                 --版本
               p_log_sp_name => v_sp_name,    --sp名称
               p_log_para_list => '',--参数
               p_log_step_num  => 5,
               p_log_cal_log_desc => '【默认基期计算】原材料价格指数（默认基期）、外部价格指数（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count,--日志描述
               p_log_formula_sql_txt => null,--错误信息
               p_log_row_count => v_dml_row_count,
               p_log_errbuf => null  --错误编码
            ) ;


          -- 取ICT内部价格指数的最大版本
          with foi_max_plan_version_tmp as(
          select max(version_id) as max_version_id
            from fin_dm_opt_foi.dm_foi_plan_version_t
           where upper(data_type) = 'ITEM'
		     and upper(version_type) in('AUTO','FINAL') 
             and status = 1
             and del_flag = 'N'
          ),
		  -- 取数字能源内部价格指数的最大版本
		   foi_max_energy_version_tmp as(
          select max(version_id) as max_version_id
            from fin_dm_opt_foi.dm_foi_energy_plan_version_t
           where upper(data_type) = 'ITEM'
		     and upper(version_type) in('AUTO','FINAL') 
             and status = 1
             and del_flag = 'N'
          ),
           -- 内部价格指数临时表（默认基期）
           foi_price_index_tmp as(
           select 'ICT'          as business
		        , period_id      as data_date    -- 数据日期
                , base_period_id as base_period  -- 默认基期（T-3年）
                , l3_ceg_code              -- 专家团编码
                , l3_ceg_cn_name           -- 专家团中文名称
                , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , l4_ceg_code              -- 模块编码
                , l4_ceg_cn_name           -- 模块中文名称
                , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , group_code    as category_code -- 品类编码
                , group_cn_name as category_name -- 品类名称
                , coalesce(price_index,0) as price_index  -- 价格指数
             from fin_dm_opt_foi.dm_foi_price_index_t t1  -- 价格指数总览表
            where version_id = (select max_version_id from foi_max_plan_version_tmp)
              and group_level = 'CATEGORY'  -- 品类
              and base_period_id = p_base_period  -- 取T-3年的基期
              and period_id between p_base_period and ((substr(p_base_period::varchar(10),1,4)+3)||'12')::numeric  -- 取T+3年的数据
              and l3_ceg_code = p_l3_ceg_code
              and l4_ceg_code = p_l4_ceg_code
              and group_code = p_category_code
              and del_flag = 'N'
			  union all
			  select '数字能源'  as business
			    , period_id      as data_date    -- 数据日期
                , base_period_id as base_period  -- 默认基期（T-3年）
                , l3_ceg_code              -- 专家团编码
                , l3_ceg_cn_name           -- 专家团中文名称
                , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                , l4_ceg_code              -- 模块编码
                , l4_ceg_cn_name           -- 模块中文名称
                , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                , group_code    as category_code -- 品类编码
                , group_cn_name as category_name -- 品类名称
                , coalesce(price_index,0) as price_index  -- 价格指数
             from fin_dm_opt_foi.dm_foi_energy_month_price_index_t t1  -- 数字能源价格指数表
            where version_id = (select max_version_id from foi_max_energy_version_tmp)
              and group_level = 'CATEGORY'  -- 品类
              and base_period_id = p_base_period  -- 取T-3年的基期
              and period_id between p_base_period and ((substr(p_base_period::varchar(10),1,4)+3)||'12')::numeric  -- 取T+3年的数据
              and l3_ceg_code = p_l3_ceg_code
              and l4_ceg_code = p_l4_ceg_code
              and group_code = p_category_code
              and del_flag = 'N'
           ),
            -- 上月内部价格指数数据
            last_foi_price_index_tmp as(
            select business
			     , to_char(to_date(data_date,'yyyymm') + interval'1 month','yyyymm')::numeric as last_data_date  -- 上月日期
                 , data_date                -- 数据日期
                 , base_period              -- 默认基期（T-3年）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , category_code -- 品类编码
                 , category_name -- 品类名称
                 , price_index                 -- 价格指数
              from foi_price_index_tmp
			 where business = p_business
            ),
            -- 内部价格指数环比
            foi_price_index_pp_ptd_tmp as(
            select t1.business
			     , t1.data_date    -- 数据日期
                 , t1.base_period  -- 默认基期（T-3年）
                 , t1.l3_ceg_code              -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code              -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t1.category_code -- 品类编码
                 , t1.category_name -- 品类名称
                 , (case when coalesce(t2.price_index,0) = 0 then 0
                         else (coalesce(t1.price_index,0)-coalesce(t2.price_index,0))/coalesce(t2.price_index,0)
                    end) as foi_price_index_pp_ptd  -- 内部价格指数环比
              from foi_price_index_tmp t1  -- 当月数据
              left join last_foi_price_index_tmp t2  -- 上月数据
                on t1.data_date = t2.last_data_date
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
               and t1.category_code = t2.category_code
			   and t1.business = t2.business
			   where t1.business = p_business
            )
            -- 内部价格指数（默认基期），从指数项目组的“价格指数总览表”和“数字能源内部价格指数”直接取
            insert into all_info_tmp2(
                   query_key
				 , business
                 , data_date                 -- 数据日期
                 , data_type                 -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code               -- 专家团编码
                 , l3_ceg_cn_name            -- 专家团中文名称
                 , l3_ceg_short_cn_name      -- 专家团（Group Lv3简称）
                 , l4_ceg_code               -- 模块编码
                 , l4_ceg_cn_name            -- 模块中文名称
                 , l4_ceg_short_cn_name      -- 模块（Group Lv4简称）
                 , category_code             -- 品类编码
                 , category_name             -- 品类名称
                 , index_code                -- 指标编码
                 , index_name                -- 指标名称
                 , index_short_name          -- 指标简称
                 , metal_short_cn_name       -- 金属简称
                 , conduction_phase          -- 传导期
                 , source_name               -- 数据源
                 , source_table              -- 来源表
                 , index_type                -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                   -- 占比
                 , data_value                -- 数据值
                 , price_index               -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
                 , price_index_pp_ptd	       -- 环比
             )
            select p_query_key as query_key
			     , t1.business
                 , t1.data_date                   -- 数据日期
                 , '' as data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code                 -- 专家团编码
                 , t1.l3_ceg_cn_name              -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code                 -- 模块编码
                 , t1.l4_ceg_cn_name              -- 模块中文名称
                 , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
                 , t1.category_code               -- 品类编码
                 , t1.category_name               -- 品类名称
                 , '' as index_code               -- 指标编码
                 , '' as index_name               -- 指标名称
                 , '' as index_short_name         -- 指标简称
                 , '' as metal_short_cn_name      -- 金属简称
                 , null as conduction_phase       -- 传导期
                 , '' as source_name              -- 数据源
                 , '' as source_table             -- 来源表
                 , '内部价格指数' as index_type   -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , null as percent                -- 占比
                 , null as data_value             -- 数据值
                 , t1.price_index                 -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
                 , t2.foi_price_index_pp_ptd as price_index_pp_ptd	 -- 环比
              from foi_price_index_tmp t1  -- 内部价格指数临时表
              left join foi_price_index_pp_ptd_tmp t2  -- 内部价格指数环比
                on t1.data_date     = t2.data_date
               and t1.l3_ceg_code   = t2.l3_ceg_code
               and t1.l4_ceg_code   = t2.l4_ceg_code
               and t1.category_code = t2.category_code
			   and t1.business = t2.business
			   where t1.business = p_business
          ;

        -- 判断临时表中"初始默认基期标识"是否有N，如有，则"初始默认基期标识"赋值为"N"
        if exists(select index_code from raw_materials_category_rel_tmp where init_base_period_flag = 'N' and query_key = p_query_key ) then
          -- 内部价格指数入到目标表
          insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                 query_key                  -- 查询键
			   , business
               , period_id                  -- 会计期
               , data_date                  -- 数据日期
               , base_period                -- 基期
               , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
               , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
               , l3_ceg_code                -- 专家团编码
               , l3_ceg_cn_name             -- 专家团中文名称
               , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
               , l4_ceg_code                -- 模块编码
               , l4_ceg_cn_name             -- 模块中文名称
               , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
               , category_code              -- 品类编码
               , category_name              -- 品类名称
               , index_code                 -- 指标编码
               , index_name                 -- 指标名称
               , index_short_name           -- 指标简称
               , metal_short_cn_name        -- 金属简称
               , conduction_phase           -- 传导期
               , source_name                -- 数据源
               , source_table               -- 来源表
               , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , percent                    -- 占比
               , data_value                 -- 数据值
               , price_index                -- 价格指数
               , price_index_pp_ptd         -- 环比
               , price_index_gap            -- 差异额
               , remark                     -- 备注
               , created_by                 -- 创建人
               , creation_date              -- 创建时间
               , last_updated_by            -- 修改人
               , last_update_date           -- 修改时间
               , del_flag                   -- 是否删除
          )
          select query_key -- 查询键
		       , business
               , p_period_id as period_id -- 会计期
               , data_date                -- 数据日期
               , p_base_period as base_period -- 基期
               , 'N' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
               , data_type                -- 数据类别（ACT 实际数、FCST 预测数）
               , l3_ceg_code              -- 专家团编码
               , l3_ceg_cn_name           -- 专家团中文名称
               , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
               , l4_ceg_code              -- 模块编码
               , l4_ceg_cn_name           -- 模块中文名称
               , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
               , category_code            -- 品类编码
               , category_name            -- 品类名称
               , index_code               -- 指标编码
               , index_name               -- 指标名称
               , index_short_name         -- 指标简称
               , metal_short_cn_name      -- 金属简称
               , conduction_phase         -- 传导期
               , source_name              -- 数据源
               , source_table             -- 来源表
               , index_type               -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , percent                  -- 占比
               , data_value               -- 数据值
               , price_index              -- 价格指数
               , price_index_pp_ptd	      -- 环比
               , null as price_index_gap	        -- 差异额
               , '' as remark                          -- 备注
         	   , -1 as created_by                      -- 创建人
         	   , current_timestamp as creation_date    -- 创建时间
         	   , -1 as last_updated_by                 -- 修改人
         	   , current_timestamp as last_update_date -- 修改时间
         	   , 'N' as del_flag                       -- 是否删除
            from all_info_tmp2
           where query_key = p_query_key
          ;

          v_dml_row_count := sql%rowcount;  -- 收集数据量

          perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
              p_log_version_id => null,                 --版本
              p_log_sp_name => v_sp_name,    --sp名称
              p_log_para_list => '',--参数
              p_log_step_num  => 6,
              p_log_cal_log_desc => '【默认基期计算】内部价格指数（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count,--日志描述
              p_log_formula_sql_txt => null,--错误信息
              p_log_row_count => v_dml_row_count,
              p_log_errbuf => null  --错误编码
          ) ;

          -- 差异额数据入到目标表
          insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                 query_key                  -- 查询键
			   , business
               , period_id                  -- 会计期
               , data_date                  -- 数据日期
               , base_period                -- 基期
               , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
               , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
               , l3_ceg_code                -- 专家团编码
               , l3_ceg_cn_name             -- 专家团中文名称
               , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
               , l4_ceg_code                -- 模块编码
               , l4_ceg_cn_name             -- 模块中文名称
               , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
               , category_code              -- 品类编码
               , category_name              -- 品类名称
               , index_code                 -- 指标编码
               , index_name                 -- 指标名称
               , index_short_name           -- 指标简称
               , metal_short_cn_name        -- 金属简称
               , conduction_phase           -- 传导期
               , source_name                -- 数据源
               , source_table               -- 来源表
               , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , percent                    -- 占比
               , data_value                 -- 数据值
               , price_index                -- 价格指数
               , price_index_pp_ptd         -- 环比
               , price_index_gap            -- 差异额
               , remark                     -- 备注
               , created_by                 -- 创建人
               , creation_date              -- 创建时间
               , last_updated_by            -- 修改人
               , last_update_date           -- 修改时间
               , del_flag                   -- 是否删除
          )
          with out_price_index_compare_info_tmp as(
          select distinct query_key
		       , business
               , data_date
               , l3_ceg_code
               , l3_ceg_cn_name
               , l3_ceg_short_cn_name
               , l4_ceg_code
               , l4_ceg_cn_name
               , l4_ceg_short_cn_name
               , category_code
               , category_name
               , conduction_phase
               , price_index
            from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
           where period_id = p_period_id
             and l3_ceg_code = p_l3_ceg_code
             and l4_ceg_code = p_l4_ceg_code
             and category_code = p_category_code
             and index_type = '外部价格指数'
             and query_key = p_query_key
			 and business = p_business
          ),
          in_price_index_compare_info_tmp as(
          select distinct query_key
		       , business
               , data_date
               , l3_ceg_code
               , l3_ceg_cn_name
               , l3_ceg_short_cn_name
               , l4_ceg_code
               , l4_ceg_cn_name
               , l4_ceg_short_cn_name
               , category_code
               , category_name
               , price_index
            from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
           where period_id = p_period_id
             and index_type = '内部价格指数'
             and query_key = p_query_key
			 and business = p_business
          )
          -- 差异额=外部价格指数-品类采购价格指数
          select t1.query_key -- 查询键
		       , t1.business
               , p_period_id as period_id -- 会计期
               , t1.data_date                   -- 数据日期
               , p_base_period as base_period   -- 默认基期（T-3年）
               , 'N' as init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
               , '' as data_type                -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code                 -- 专家团编码
               , t1.l3_ceg_cn_name              -- 专家团中文名称
               , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code                 -- 模块编码
               , t1.l4_ceg_cn_name              -- 模块中文名称
               , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
               , t1.category_code               -- 品类编码
               , t1.category_name               -- 品类名称
               , '' as index_code               -- 指标编码
               , '' as index_name               -- 指标名称
               , '' as index_short_name         -- 指标简称
               , '' as metal_short_cn_name      -- 金属简称
               , null as conduction_phase       -- 传导期
               , '' as source_name              -- 数据源
               , '' as source_table             -- 来源表
               , 'GAP' as index_type            -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , null as percent                -- 占比
               , null as data_value             -- 数据值
               , null as price_index            -- 价格指数
               , null as price_index_pp_ptd	    -- 环比
               , (coalesce(t1.price_index,0) - coalesce(t2.price_index,0)) as price_index_gap 	        -- 差异额
               , '' as remark                          -- 备注
	        	   , -1 as created_by                      -- 创建人
	        	   , current_timestamp as creation_date    -- 创建时间
	        	   , -1 as last_updated_by                 -- 修改人
	        	   , current_timestamp as last_update_date -- 修改时间
	        	   , 'N' as del_flag                       -- 是否删除
            from out_price_index_compare_info_tmp t1  -- 外部价格指数临时表
            join in_price_index_compare_info_tmp t2   -- 内部价格指数临时表
              on t1.data_date = t2.data_date
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
             and t1.category_code = t2.category_code
             and t1.query_key = t2.query_key
			 and t1.business = t2.business
          ;

          v_dml_row_count := sql%rowcount;  -- 收集数据量

          perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
              p_log_version_id => null,                 --版本
              p_log_sp_name => v_sp_name,    --sp名称
              p_log_para_list => '',--参数
              p_log_step_num  => 7,
              p_log_cal_log_desc => '【默认基期计算】差异额（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
              p_log_formula_sql_txt => null,--错误信息
              p_log_row_count => v_dml_row_count,
              p_log_errbuf => null  --错误编码
           ) ;

          -- 判断临时表中"初始默认基期标识"是否有N，如没有，则"初始默认基期标识"赋值为"Y"
          else
            -- 内部价格指数入到目标表
            insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                   query_key                  -- 查询键
				 , business
                 , period_id                  -- 会计期
                 , data_date                  -- 数据日期
                 , base_period                -- 基期
                 , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                 , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code                -- 专家团编码
                 , l3_ceg_cn_name             -- 专家团中文名称
                 , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                 , l4_ceg_code                -- 模块编码
                 , l4_ceg_cn_name             -- 模块中文名称
                 , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                 , category_code              -- 品类编码
                 , category_name              -- 品类名称
                 , index_code                 -- 指标编码
                 , index_name                 -- 指标名称
                 , index_short_name           -- 指标简称
                 , metal_short_cn_name        -- 金属简称
                 , conduction_phase           -- 传导期
                 , source_name                -- 数据源
                 , source_table               -- 来源表
                 , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                    -- 占比
                 , data_value                 -- 数据值
                 , price_index                -- 价格指数
                 , price_index_pp_ptd         -- 环比
                 , price_index_gap            -- 差异额
                 , remark                     -- 备注
                 , created_by                 -- 创建人
                 , creation_date              -- 创建时间
                 , last_updated_by            -- 修改人
                 , last_update_date           -- 修改时间
                 , del_flag                   -- 是否删除
            )
            select query_key -- 查询键
			     , business
                 , p_period_id as period_id -- 会计期
                 , data_date                -- 数据日期
                 , p_base_period as base_period -- 基期
                 , 'Y' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
                 , data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , category_code            -- 品类编码
                 , category_name            -- 品类名称
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , index_short_name         -- 指标简称
                 , metal_short_cn_name      -- 金属简称
                 , conduction_phase         -- 传导期
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , index_type               -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                  -- 占比
                 , data_value               -- 数据值
                 , price_index              -- 价格指数
                 , price_index_pp_ptd	      -- 环比
                 , null as price_index_gap	        -- 差异额
                 , '' as remark                          -- 备注
	          	   , -1 as created_by                      -- 创建人
	          	   , current_timestamp as creation_date    -- 创建时间
	          	   , -1 as last_updated_by                 -- 修改人
	          	   , current_timestamp as last_update_date -- 修改时间
	          	   , 'N' as del_flag                       -- 是否删除
              from all_info_tmp2
             where query_key = p_query_key
            ;

            v_dml_row_count := sql%rowcount;  -- 收集数据量

            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 6,
                p_log_cal_log_desc => '【默认基期计算】内部价格指数（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
             ) ;

            -- 差异额数据入到目标表
            insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                   query_key                  -- 查询键
				 , business
                 , period_id                  -- 会计期
                 , data_date                  -- 数据日期
                 , base_period                -- 基期
                 , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                 , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code                -- 专家团编码
                 , l3_ceg_cn_name             -- 专家团中文名称
                 , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                 , l4_ceg_code                -- 模块编码
                 , l4_ceg_cn_name             -- 模块中文名称
                 , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                 , category_code              -- 品类编码
                 , category_name              -- 品类名称
                 , index_code                 -- 指标编码
                 , index_name                 -- 指标名称
                 , index_short_name           -- 指标简称
                 , metal_short_cn_name        -- 金属简称
                 , conduction_phase           -- 传导期
                 , source_name                -- 数据源
                 , source_table               -- 来源表
                 , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                    -- 占比
                 , data_value                 -- 数据值
                 , price_index                -- 价格指数
                 , price_index_pp_ptd         -- 环比
                 , price_index_gap            -- 差异额
                 , remark                     -- 备注
                 , created_by                 -- 创建人
                 , creation_date              -- 创建时间
                 , last_updated_by            -- 修改人
                 , last_update_date           -- 修改时间
                 , del_flag                   -- 是否删除
            )
            with out_price_index_compare_info_tmp as(
            select distinct query_key
			     , business
                 , data_date
                 , l3_ceg_code
                 , l3_ceg_cn_name
                 , l3_ceg_short_cn_name
                 , l4_ceg_code
                 , l4_ceg_cn_name
                 , l4_ceg_short_cn_name
                 , category_code
                 , category_name
                 , price_index
              from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
             where period_id = p_period_id
               and l3_ceg_code = p_l3_ceg_code
               and l4_ceg_code = p_l4_ceg_code
               and category_code = p_category_code
               and index_type = '外部价格指数'
               and query_key = p_query_key
			   and business = p_business
            ),
            in_price_index_compare_info_tmp as(
            select distinct query_key
			     , business
                 , data_date
                 , l3_ceg_code
                 , l3_ceg_cn_name
                 , l3_ceg_short_cn_name
                 , l4_ceg_code
                 , l4_ceg_cn_name
                 , l4_ceg_short_cn_name
                 , category_code
                 , category_name
                 , price_index
              from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
             where period_id = p_period_id
               and index_type = '内部价格指数'
               and query_key = p_query_key
			   and business = p_business
            )
            -- 差异额=外部价格指数-品类采购价格指数
            select t1.query_key -- 查询键
			     , t1.business
                 , p_period_id as period_id -- 会计期
                 , t1.data_date                   -- 数据日期
                 , p_base_period as base_period   -- 默认基期（T-3年）
                 , 'Y' as init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
                 , '' as data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code                 -- 专家团编码
                 , t1.l3_ceg_cn_name              -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code                 -- 模块编码
                 , t1.l4_ceg_cn_name              -- 模块中文名称
                 , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
                 , t1.category_code               -- 品类编码
                 , t1.category_name               -- 品类名称
                 , '' as index_code               -- 指标编码
                 , '' as index_name               -- 指标名称
                 , '' as index_short_name         -- 指标简称
                 , '' as metal_short_cn_name      -- 金属简称
                 , null as conduction_phase       -- 传导期
                 , '' as source_name              -- 数据源
                 , '' as source_table             -- 来源表
                 , 'GAP' as index_type            -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , null as percent                -- 占比
                 , null as data_value             -- 数据值
                 , null as price_index            -- 价格指数
                 , null as price_index_pp_ptd	    -- 环比
                 , (coalesce(t1.price_index,0) - coalesce(t2.price_index,0)) as price_index_gap 	        -- 差异额
                 , '' as remark                          -- 备注
  	        	   , -1 as created_by                      -- 创建人
  	        	   , current_timestamp as creation_date    -- 创建时间
  	        	   , -1 as last_updated_by                 -- 修改人
  	        	   , current_timestamp as last_update_date -- 修改时间
  	        	   , 'N' as del_flag                       -- 是否删除
              from out_price_index_compare_info_tmp t1  -- 外部价格指数临时表
              join in_price_index_compare_info_tmp t2   -- 内部价格指数临时表
                on t1.data_date = t2.data_date
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
               and t1.category_code = t2.category_code
               and t1.query_key = t2.query_key
			   and t1.business = t2.business
            ;

            v_dml_row_count := sql%rowcount;  -- 收集数据量

            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 7,
                p_log_cal_log_desc => '【默认基期计算】差异额（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
             ) ;

           end if;

          end if;
        -- 【默认基期 end】
          
        -- 【切换基期 start】
        -- 传入基期不是默认基期，需要先判断是否有对应的默认基期
        -- p_base_period 外购大宗商品的默认基期（最大会计期年份-3）；v_def_base_period 采购价格指数项目组的默认基期
        elseif(p_base_period <> v_def_base_period) then
            -- query_key值拼接，找到切换基期对应的默认基期
            select gs_hash((select distinct period_id||max(to_char(dw_last_update_date,'yyyymmddhh24mi')) over()  
                     from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i
                    where del_flag = 'N'
                      and period_id = p_period_id
                   )||v_max_version_id||','||v_def_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||','||
                  (select distinct conduction_phase from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t
                    where del_flag = 'N'
                      and upper(status) = 'SUBMIT'
                      and version_code = (select max(version_code) from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t where del_flag = 'N' and upper(status) = 'SUBMIT' and business=p_business )  -- 取最大版本
                      and category_code = p_category_code  -- 传入品类
                      and l3_ceg_code = p_l3_ceg_code
                      and l4_ceg_code = p_l4_ceg_code
					  and business=p_business
                  )||','||p_metal_percent||','||p_business,'sha256') into v_query_key
            ;

          if not exists(select query_key from metal_percent_tmp1 where query_key = v_query_key) then
    for i in 1..10 loop
      v_desc = split_part(p_metal_percent,',',i);
      insert into metal_percent_tmp1(
             remark
           , query_key
           , conduction_phase
           , l3_ceg_code
           , l4_ceg_code
           , category_code
           , base_period
		   , business
      )
      values(v_desc
           , v_query_key
           , p_conduction_phase
           , p_l3_ceg_code
           , p_l4_ceg_code
           , p_category_code
           , v_def_base_period
		   , p_business
      );
    end loop;

    delete from metal_percent_tmp1 where remark is null;

    -- 传入金属及占比临时表
    insert into metal_percent_tmp1(
           query_key
		 , business
         , base_period
         , month
         , conduction_phase
         , l3_ceg_code
         , l4_ceg_code
         , category_code
         , index_code
         , percent
         , remark
         , last_update_date
    )
    select distinct t1.query_key
	     , t1.business
         , t1.base_period
         , t2.month
         , t1.conduction_phase
         , t1.l3_ceg_code
         , t1.l4_ceg_code
         , t1.category_code
         , substr(t1.remark,1,position(':' in t1.remark)-1) as index_code
         , (substr(t1.remark,position(':' in t1.remark)+1))::numeric as percent
         , t1.remark
         , CURRENT_TIMESTAMP
      from metal_percent_tmp1 t1
      left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month) t2
        on 1=1
     where t1.query_key = v_query_key
       and t1.conduction_phase = p_conduction_phase
       and t1.l3_ceg_code = p_l3_ceg_code
       and t1.l4_ceg_code = p_l4_ceg_code
       and t1.category_code = p_category_code
       and t1.base_period = v_def_base_period
	   and t1.business = p_business
    ;

    delete from metal_percent_tmp1 where index_code is null;

  end if;
          if not exists(select query_key from make_up_data_month_tmp1 where query_key = v_query_key) then
            insert into make_up_data_month_tmp1(
                   query_key
                 , period_id
            		 , data_date
            		 , index_code
            		 , data_value
            )
            -- 只取预测数的金属
            with fcst_metal_tmp2 as(
            select distinct index_code, index_name, index_short_name
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i  -- 外购大宗商品与有色金属信息
             where data_category = '预测数'
               and data_date >= v_def_base_period
            )
            -- 预测金属对应的数据日期
            select distinct v_query_key as query_key, t1.period_id, t1.data_date, t1.index_code, t1.data_value
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i t1  -- 外购大宗商品与有色金属信息
              join fcst_metal_tmp2 t2
                on t1.index_code = t2.index_code
             where t1.data_category = '实际数'
               and t1.period_id < p_period_id
               and t1.data_date >= v_def_base_period
            union all
            select distinct v_query_key as query_key, t1.period_id, t1.data_date, t1.index_code, t1.data_value
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i t1  -- 外购大宗商品与有色金属信息
              join fcst_metal_tmp2 t2
                on t1.index_code = t2.index_code
             where t1.data_category = '预测数'
               and t1.period_id >= p_period_id
               and t1.data_date >= v_def_base_period
            ;
          end if;
        
          if not exists(select query_key from make_up_data_month_tmp2 where query_key = v_query_key) then
        	  insert into make_up_data_month_tmp2(
                 query_key
               , year
        	  	 , month
        	  	 , index_code
            )
            -- 预测金属对应全的数据日期（包括补齐的数据日期）
            select distinct t1.query_key, t3.year, t2.month, t1.index_code
              from make_up_data_month_tmp1 t1
              left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month)t2
                on 1=1
              left join (select distinct substr(data_date,1,4) as year from make_up_data_month_tmp1 where query_key = v_query_key) t3
                on 1=1
             where t1.query_key = v_query_key
            ;
          end if;
          
          if not exists(select query_key from make_up_data_month_tmp9 where query_key = v_query_key) then
        
            -- 补齐的数据
        	  insert into make_up_data_month_tmp9(
                   query_key
                 , index_code        -- 指数编码
                 , period_id         -- 数据日期（包括补录的）
                 , data_date         -- 数据日期（集成表中的）
                 , data_value        -- 数据值（集成表中的）
                 , copy_data_value   -- 数据值（用的集成表的哪个数据值补）
                 , copy_data_date    -- 数据日期（用的集成表的哪个数据日期补）
            )
            -- 带出已经存在的数据日期的信息
            with make_up_data_month_tmp33 as(
            select t1.index_code, t1.year, t1.month, t2.period_id, t2.data_date, t2.data_value
              from make_up_data_month_tmp2 t1
              left join make_up_data_month_tmp1 t2
                on t1.index_code = t2.index_code
               and t1.year = substr(t2.data_date,1,4)
               and t1.month = substr(t2.data_date,5,2)
               and t1.query_key = t2.query_key
             where t1.query_key = v_query_key
            ),
            -- 取数据日期不为空的每年最小日期，用于补每年一开始就缺失的日期数据
            make_up_data_month_tmp4 as(
            select index_code, year, min(data_date) as min_data_date
              from make_up_data_month_tmp33
             where data_date is not null
             group by index_code, year
            ),
            -- 每年最小日期的金额
            make_up_data_month_tmp5 as(
            select t1.index_code, t1.data_date, t1.data_value
              from make_up_data_month_tmp33 t1
              join make_up_data_month_tmp4 t2
                on t1.index_code = t2.index_code
               and t1.data_date = t2.min_data_date
            ),
            make_up_data_month_tmp6 as(
            select index_code, year, month, data_date, decode(data_value,0,null,data_value) as data_value
                 , last_value(decode(data_value,0,null,data_value)) over(partition by index_code order by year||lpad(month,2,0)) as amt_c
                 , count(decode(data_value,0,null,data_value)) over(partition by index_code,year order by year,month) as cnt
              from make_up_data_month_tmp33
            ),
            make_up_data_month_tmp7 as(
            select t1.index_code, t1.year, t1.month, t1.year||lpad(t1.month,2,0) as period_id, t1.data_date, t1.data_value--, t1.cnt, t1.amt_c
                 , (case when (max(t1.amt_c) over(partition by t1.index_code, t1.year, t1.cnt)) is null
                         then t2.data_value
                         else (min(t1.amt_c) over(partition by t1.index_code, t1.year, t1.cnt))
                    end) as copy_data_value
                 , (case when (max(t1.data_date) over(partition by t1.index_code, t1.year, t1.cnt)) is null
                         then t2.data_date
                         else (min(t1.data_date) over(partition by t1.index_code, t1.year, t1.cnt))
                    end) as copy_data_date
              from make_up_data_month_tmp6 t1
              left join make_up_data_month_tmp5 t2
                on t1.index_code = t2.index_code
               and t1.year = substr(t2.data_date,1,4)
            ),
            make_up_data_month_tmp8 as(
            select index_code, year||lpad(month,2,0) as period_id, data_date, data_value, copy_data_value, copy_data_date
                 , count(decode(copy_data_value,0,null,copy_data_value)) over(partition by index_code order by index_code, period_id desc) as cnt2
              from make_up_data_month_tmp7
            )
            select distinct v_query_key as query_key, index_code, period_id, data_date, data_value
                 , (case when copy_data_value is null
                         then first_value(copy_data_value) over(partition by index_code, cnt2 order by period_id desc)
                         else copy_data_value
                    end) as copy_data_value
                 , (case when copy_data_value is null
                         then first_value(copy_data_date) over(partition by index_code, cnt2 order by period_id desc)
                         else copy_data_date
                    end) as copy_data_date
              from make_up_data_month_tmp8
            ;
          end if;
          
          if not exists(select query_key from raw_materials_category_rel_tmp where query_key = v_query_key) then
            
            -- 根据传入业务（比如ICT、终端、数字能源等）取关系表中的最大版本
            select max(version_code) as max_version_code into v_max_version_code 
              from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t
             where del_flag = 'N'
               and upper(status) = 'SUBMIT'
               and category_code = p_category_code  -- 传入品类
               and l3_ceg_code = p_l3_ceg_code
               and l4_ceg_code = p_l4_ceg_code
               and business = p_business
            ;
            
            -- 根据传入参数取对应的最新版本专家团、模型信息，如果传入的是初始默认基期，则能取到对应的数据，否则取不到对应的数据
            insert into raw_materials_category_rel_tmp(
                   query_key
				         , business
                 , index_code              -- 指数编码
                 , l3_ceg_code            -- 专家团编码
                 , l3_ceg_cn_name         -- 专家团中文名称
                 , l3_ceg_short_cn_name   -- 专家团（Group Lv3简称）
                 , l4_ceg_code            -- 模块编码
                 , l4_ceg_cn_name         -- 模块中文名称
                 , l4_ceg_short_cn_name   -- 模块（Group Lv4简称）
                 , category_code          -- 品类编码
                 , category_name          -- 品类名称
                 , metal_short_cn_name    -- 金属简称
                 , conduction_phase       -- 传导期
                 , percent                -- 占比
                 , init_base_period_flag  -- 初始默认基期标识（Y 是、N 否）
            )
			with metal_percent_tmp2 as(
              select distinct business,index_code, l3_ceg_code, l4_ceg_code, category_code, conduction_phase, percent 
                from metal_percent_tmp1 
               where query_key = v_query_key
              )
            select distinct v_query_key
			           , t1.business
                 , t1.index_code      -- 指数编码
                 , (case when t1.l3_ceg_code = t2.l3_ceg_code then t1.l3_ceg_code else t2.l3_ceg_code end) as l3_ceg_code  -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , (case when t1.l4_ceg_code = t2.l4_ceg_code then t1.l4_ceg_code else t2.l4_ceg_code end) as l4_ceg_code  -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 ,  (case when t1.category_code = t2.category_code then t1.category_code else t2.category_code end) as category_code -- 品类编码
                 , t1.category_name            -- 品类名称
                 , t1.metal_short_cn_name      -- 金属简称
                 , (case when t1.conduction_phase = t2.conduction_phase then t1.conduction_phase else t2.conduction_phase end) as  conduction_phase -- 传导期
                 ,  (case when t1.percent = t2.percent then t1.percent else t2.percent end) as  percent  -- 占比
                 , (case when t1.l3_ceg_code = t2.l3_ceg_code
                            and t1.l4_ceg_code = t2.l4_ceg_code
                            and t1.category_code = t2.category_code
                            and t1.conduction_phase = t2.conduction_phase
                           then 'Y'
                           else 'N'
                      end) as init_base_period_flag  -- 初始默认基期标识（Y 是、N 否）
              from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t t1  -- 原材料与品类量化建模关系表
			  left join metal_percent_tmp2 t2  -- 传入金属及占比
                  on t1.index_code = t2.index_code
				 and t1.business = t2.business
             where t1.del_flag = 'N'
               and upper(t1.status) = 'SUBMIT'
               and t1.version_code = v_max_version_code  -- 取最大版本
               and t1.category_code = p_category_code  -- 传入品类
               and t1.l3_ceg_code = p_l3_ceg_code
               and t1.l4_ceg_code = p_l4_ceg_code
			   and t1.business = p_business
            ;
          
            v_dml_row_count := sql%rowcount;  -- 收集数据量
          
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 2,
                p_log_cal_log_desc => '【切换基期对应的默认基期计算】传入参数：'||p_period_id||','||p_base_period||','||v_def_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||'，拼接的 query_key 值：'||v_query_key||'，入到 raw_materials_category_rel_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
            
          end if;

          -- 根据传入的切换会计期信息判断是否存在对应的默认基期，如不存在则计算
          if not exists(select query_key from fin_dm_opt_foi.dm_foq_price_index_compare_info_t where query_key = v_query_key) then
            -- 【切换基期的默认基期 start】
            
            -- 清理 grp_outs_act_fcst_i_tmp 表数据
            truncate table grp_outs_act_fcst_i_tmp;
            
            -- 实际数取<p_period_id；预测数取>=p_period_id
            insert into grp_outs_act_fcst_i_tmp(
                   query_key
                 , data_date        -- 数据日期
                 , data_type        -- 数据类别（ACT 实际数、FCST 预测数）
                 , index_code       -- 指标编码
                 , index_name       -- 指标名称
                 , source_name      -- 数据源
                 , source_table     -- 来源表
                 , data_value       -- 数据值
            )
            select v_query_key as query_key
                 , data_date::numeric           -- 数据日期
                 , data_category as data_type   -- 数据类别（ACT 实际数、FCST 预测数）
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , data_value               -- 数据值
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i -- 外购大宗商品与有色金属信息
             where data_date >= v_def_base_period
               and data_category = '实际数'
               and period_id < p_period_id
            union all
            select v_query_key as query_key
                 , data_date::numeric           -- 数据日期
                 , data_category as data_type   -- 数据类别（ACT 实际数、FCST 预测数）
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , data_value               -- 数据值
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i -- 外购大宗商品与有色金属信息
             where data_date >= v_def_base_period
               and data_category = '预测数'
               and period_id >= p_period_id
            ;
            
            -- 数据清理
            delete from grp_outs_act_fcst_tmp where query_key = v_query_key;
            
            -- 关联出专家团、模块等信息的数据
            insert into grp_outs_act_fcst_tmp(
                   query_key
				   , business
                   , data_date               -- 数据日期
                   , base_period             -- 基期
                   , data_type               -- 数据类别（ACT 实际数、FCST 预测数）
                   , l3_ceg_code             -- 专家团编码
                   , l3_ceg_cn_name          -- 专家团中文名称
                   , l3_ceg_short_cn_name    -- 专家团（Group Lv3简称）
                   , l4_ceg_code             -- 模块编码
                   , l4_ceg_cn_name          -- 模块中文名称
                   , l4_ceg_short_cn_name    -- 模块（Group Lv4简称）
                   , category_code           -- 品类编码
                   , category_name           -- 品类名称
                   , index_code              -- 指标编码
                   , index_name              -- 指标名称
                   , index_short_name        -- 指标简称
                   , metal_short_cn_name     -- 金属简称
                   , conduction_phase        -- 传导期
                   , source_name             -- 数据源
                   , source_table            -- 来源表
                   , percent                 -- 占比
                   , data_value              -- 数据值
                   , init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
            )-- 只取预测数的金属
            with fcst_metal_tmp as(
            select distinct index_code, index_name, index_short_name
              from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i  -- 外购大宗商品与有色金属信息
             where data_category = '预测数'
               and data_date >= v_def_base_period
            )
            select v_query_key as query_key
			     , t2.business
                 , t1.data_date                -- 数据日期
                 , v_def_base_period as base_period -- 默认基期
                 , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t2.l3_ceg_code              -- 专家团编码
                 , t2.l3_ceg_cn_name           -- 专家团中文名称
                 , t2.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t2.l4_ceg_code              -- 模块编码
                 , t2.l4_ceg_cn_name           -- 模块中文名称
                 , t2.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t2.category_code            -- 品类编码
                 , t2.category_name            -- 品类名称
                 , t1.index_code               -- 指标编码
                 , t1.index_name               -- 指标名称
                 , t3.index_short_name         -- 指标简称
                 , t2.metal_short_cn_name      -- 金属简称
                 , t2.conduction_phase         -- 传导期
                 , t1.source_name              -- 数据源
                 , t1.source_table             -- 来源表
                 , t2.percent                  -- 占比
                 , t1.data_value               -- 数据值
                 , 'Y' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
              from grp_outs_act_fcst_i_tmp t1  -- 外购大宗商品与有色金属信息临时表
              left join raw_materials_category_rel_tmp t2  -- 原材料与品类量化建模关系临时表
                on t1.index_code = t2.index_code
               and t1.query_key = t2.query_key
              join fcst_metal_tmp t3  -- 预测数金属临时表
                on t1.index_code = t3.index_code
             where t2.l3_ceg_code = p_l3_ceg_code  -- 传入参数
               and t2.l4_ceg_code = p_l4_ceg_code  -- 传入参数
               and t2.category_code = p_category_code  -- 传入参数
               and t1.query_key = v_query_key
			   and t2.business = p_business
            union all
            -- 补齐的数据
            select v_query_key as query_key
			     , t4.business
                 , t1.period_id as data_date   -- 数据日期
                 , v_def_base_period as base_period -- 默认基期
                 , '' as data_type             -- 数据类别（ACT 实际数、FCST 预测数）
                 , t4.l3_ceg_code              -- 专家团编码
                 , t4.l3_ceg_cn_name           -- 专家团中文名称
                 , t4.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t4.l4_ceg_code              -- 模块编码
                 , t4.l4_ceg_cn_name           -- 模块中文名称
                 , t4.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t4.category_code            -- 品类编码
                 , t4.category_name            -- 品类名称
                 , t2.index_code               -- 指标编码
                 , t2.index_name               -- 指标名称
                 , t3.index_short_name         -- 指标简称
                 , t4.metal_short_cn_name      -- 金属简称
                 , t4.conduction_phase         -- 传导期
                 , t2.source_name              -- 数据源
                 , t2.source_table             -- 来源表
                 , t4.percent                  -- 占比
                 , t2.data_value               -- 数据值
                 , 'Y' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
              from make_up_data_month_tmp9 t1
              join grp_outs_act_fcst_i_tmp t2
                on t1.index_code = t2.index_code
               and t2.query_key = v_query_key
               and t1.copy_data_date = t2.data_date
              join fcst_metal_tmp t3  -- 预测数金属临时表
                on t1.index_code = t3.index_code
              left join raw_materials_category_rel_tmp t4  -- 原材料与品类量化建模关系临时表
                on t1.index_code = t4.index_code
               and t4.query_key = v_query_key
             where t1.data_date is null
               and t4.l3_ceg_code = p_l3_ceg_code
               and t4.l4_ceg_code = p_l4_ceg_code
               and t4.category_code = p_category_code
               and t1.query_key = v_query_key
			   and t4.business = p_business
            ;

            v_dml_row_count := sql%rowcount;  -- 收集数据量

            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
               p_log_version_id => null,                 --版本
               p_log_sp_name => v_sp_name,    --sp名称
               p_log_para_list => '',--参数
               p_log_step_num  => 3,
               p_log_cal_log_desc => '【切换基期对应的默认基期计算】价格指数总览表的默认基期：'||v_def_base_period||'，拼接的 query_key 值：'||v_query_key||'，入到 grp_outs_act_fcst_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
               p_log_formula_sql_txt => null,--错误信息
               p_log_row_count => v_dml_row_count,
               p_log_errbuf => null  --错误编码
             ) ;

            -- 原材料价格指数、外部价格指数入到目标表
            insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                   query_key                  -- 查询键
				 , business
                 , period_id                  -- 会计期
                 , data_date                  -- 数据日期
                 , base_period                -- 基期
                 , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                 , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code                -- 专家团编码
                 , l3_ceg_cn_name             -- 专家团中文名称
                 , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                 , l4_ceg_code                -- 模块编码
                 , l4_ceg_cn_name             -- 模块中文名称
                 , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                 , category_code              -- 品类编码
                 , category_name              -- 品类名称
                 , index_code                 -- 指标编码
                 , index_name                 -- 指标名称
                 , index_short_name           -- 指标简称
                 , metal_short_cn_name        -- 金属简称
                 , conduction_phase           -- 传导期
                 , source_name                -- 数据源
                 , source_table               -- 来源表
                 , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                    -- 占比
                 , data_value                 -- 数据值
                 , price_index                -- 价格指数
                 , price_index_pp_ptd         -- 环比
                 , price_index_gap            -- 差异额
                 , remark                     -- 备注
                 , created_by                 -- 创建人
                 , creation_date              -- 创建时间
                 , last_updated_by            -- 修改人
                 , last_update_date           -- 修改时间
                 , del_flag                   -- 是否删除
            )
            -- 只取Top品类清单表的品类
            with detail_tmp as(
            select t1.query_key
			     , 'ICT'  as business
                 , t1.data_date                -- 数据日期
                 , t1.base_period              -- 基期
                 , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code              -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code              -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t1.category_code            -- 品类编码
                 , t1.category_name            -- 品类名称
                 , t1.index_code               -- 指标编码
                 , t1.index_name               -- 指标名称
                 , t1.index_short_name         -- 指标简称
                 , t1.metal_short_cn_name      -- 金属简称
                 , t1.conduction_phase         -- 传导期
                 , t1.source_name              -- 数据源
                 , t1.source_table             -- 来源表
                 , t1.percent                  -- 占比
                 , t1.data_value               -- 数据值
                 , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
              from grp_outs_act_fcst_tmp t1  -- 外购大宗商品与有色金属信息
              join fin_dm_opt_foi.dm_foq_top_cate_info_v t2  -- ICT最大版本的Top品类清单视图
                on t1.category_code = t2.category_code
             where t1.query_key = v_query_key
			 union all
			 select t1.query_key
			     , '数字能源'     as business
                 , t1.data_date                -- 数据日期
                 , t1.base_period              -- 基期
                 , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code              -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code              -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t1.category_code            -- 品类编码
                 , t1.category_name            -- 品类名称
                 , t1.index_code               -- 指标编码
                 , t1.index_name               -- 指标名称
                 , t1.index_short_name         -- 指标简称
                 , t1.metal_short_cn_name      -- 金属简称
                 , t1.conduction_phase         -- 传导期
                 , t1.source_name              -- 数据源
                 , t1.source_table             -- 来源表
                 , t1.percent                  -- 占比
                 , t1.data_value               -- 数据值
                 , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
              from grp_outs_act_fcst_tmp t1  -- 外购大宗商品与有色金属信息
              join fin_dm_opt_foi.dm_foq_energy_top_cate_info_t_v t2  -- 数字能源最大版本的Top品类清单视图
                on t1.category_code = t2.category_code
             where t1.query_key = v_query_key
            ),
            -- 根据传入参数取默认基期数据
            default_base_period_tmp as(
            select data_date as base_period, business,category_code, l3_ceg_code, l4_ceg_code, index_code, data_value
              from detail_tmp
             where data_date = base_period
			   and business = p_business
            ),
            -- 原材料价格指数临时表（默认基期）
            def_rm_price_index_tmp as(
            select t1.data_date, t1.business,t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code, t1.index_code
                 , (case when coalesce(t2.data_value,0) = 0 then 0
                         else round(coalesce(t1.data_value,0)/coalesce(t2.data_value,0),10)*100
                    end) as rm_price_index  -- 原材料价格指数
              from detail_tmp t1
              left join default_base_period_tmp t2
                on t1.category_code = t2.category_code
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
               and t1.index_code = t2.index_code
			   and t1.business = t2.business
			 where t1.business = p_business
            ),
            -- 固定占比
            fa_percent_tmp as(
            select data_date,business, category_code, l3_ceg_code, l4_ceg_code, (1 - sum(coalesce(percent,0))) as fa_percent  -- 固定占比
              from (select distinct data_date, business,category_code, l3_ceg_code, l4_ceg_code, index_code,percent from detail_tmp where business = p_business) T
             group by data_date, business,category_code, l3_ceg_code, l4_ceg_code
            ),
            -- 外部价格指数临时表（默认基期）
            def_exter_price_index_tmp as(
            select t1.data_date,t1.business, t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code, t1.index_code
                 , (case when coalesce(t2.data_value,0) = 0 then 0
                         else round(coalesce(t1.data_value,0)/coalesce(t2.data_value,0)*coalesce(t1.percent,0),10)  -- 报告期价格指数/默认基期价格指数*金属占比
                    end) as exter_price_index  -- 外部价格指数
              from detail_tmp t1
              left join default_base_period_tmp t2
                on t1.category_code = t2.category_code
               and t1.index_code = t2.index_code
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
			   and t1.business = t2.business
			 where t1.business = p_business
            union all
            select data_date, business,category_code, l3_ceg_code, l4_ceg_code, '' as index_code, fa_percent as exter_price_index -- 外部价格指数
              from fa_percent_tmp
            ),
            -- 外部价格指数按品类汇总
            def_exter_price_index_sum_tmp as(
            select data_date, business,category_code, l3_ceg_code, l4_ceg_code, sum(exter_price_index)*100 as exter_price_index -- 外部价格指数
              from def_exter_price_index_tmp
             group by data_date,business, category_code, l3_ceg_code, l4_ceg_code
            ),
            -- 上月外部价格指数数据
            last_def_exter_price_index_tmp as(
            select to_char(to_date(data_date,'yyyymm') + interval'1 month','yyyymm')::numeric as last_data_date  -- 上月日期
                 , data_date, business,category_code, l3_ceg_code, l4_ceg_code, exter_price_index -- 外部价格指数
              from def_exter_price_index_sum_tmp t1
            ),
            -- 外部价格指数环比
            exter_price_index_pp_ptd_tmp as(
            select t1.data_date,t1.business, t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code
                 , (case when coalesce(t2.exter_price_index,0) = 0 then 0
                         else (coalesce(t1.exter_price_index,0)-coalesce(t2.exter_price_index,0))/coalesce(t2.exter_price_index,0)
                    end) as exter_price_index_pp_ptd  -- 外部价格指数环比
              from def_exter_price_index_sum_tmp t1  -- 当月数据
              left join last_def_exter_price_index_tmp t2 -- 上月数据
                on t1.data_date = t2.last_data_date
               and t1.category_code = t2.category_code
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
			   and t1.business = t2.business
            ),
            -- 原材料价格指数（默认基期）
            all_info_tmp1 as(
            select t1.query_key
			     , t1.business
                 , t1.data_date                -- 数据日期
                 , t1.base_period              -- 默认基期
                 , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code              -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code              -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t1.category_code            -- 品类编码
                 , t1.category_name            -- 品类名称
                 , t1.index_code               -- 指标编码
                 , t1.index_name               -- 指标名称
                 , t1.index_short_name         -- 指标简称
                 , t1.metal_short_cn_name      -- 金属简称
                 , t1.conduction_phase         -- 传导期
                 , t1.source_name              -- 数据源
                 , t1.source_table             -- 来源表
                 , '原材料价格指数' as index_type  -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , t1.percent                  -- 占比
                 , t1.data_value               -- 数据值
                 , t2.rm_price_index as price_index  -- 价格指数
                 , null as price_index_pp_ptd	 -- 环比
                 , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
              from detail_tmp t1
              left join def_rm_price_index_tmp t2
                on t1.data_date = t2.data_date
               and t1.category_code = t2.category_code
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
               and t1.index_code = t2.index_code
			   and t1.business = t2.business
			 where t1.business = p_business
            union all
            -- 外部材料价格指数（默认基期）
            select t1.query_key
			     , t1.business
                 , (case when t1.conduction_phase = t4.conduction_phase then to_char(add_months(to_date(t1.data_date,'yyyymm'),t1.conduction_phase),'yyyymm')::numeric
                         else to_char(add_months(to_date(t1.data_date,'yyyymm'),@(t4.conduction_phase - t1.conduction_phase)),'yyyymm')::numeric   -- @ 绝对值
                    end) as data_date -- 数据日期
                 , t1.base_period              -- 默认基期
                 , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code              -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code              -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t1.category_code            -- 品类编码
                 , t1.category_name            -- 品类名称
                 , t1.index_code               -- 指标编码
                 , t1.index_name               -- 指标名称
                 , t1.index_short_name         -- 指标简称
                 , t1.metal_short_cn_name      -- 金属简称
                 , t1.conduction_phase         -- 传导期
                 , t1.source_name              -- 数据源
                 , t1.source_table             -- 来源表
                 , '外部价格指数' as index_type  -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , t1.percent                  -- 占比
                 , t1.data_value               -- 数据值
                 , t2.exter_price_index        as price_index  -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
                 , t3.exter_price_index_pp_ptd as price_index_pp_ptd	 -- 环比
                 , t1.init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
              from detail_tmp t1
              left join def_exter_price_index_sum_tmp t2
                on t1.data_date = t2.data_date
               and t1.category_code = t2.category_code
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
			   and t1.business = t2.business
              left join exter_price_index_pp_ptd_tmp t3
                on t1.data_date = t3.data_date
               and t1.category_code = t3.category_code
               and t1.l3_ceg_code = t3.l3_ceg_code
               and t1.l4_ceg_code = t3.l4_ceg_code
			   and t1.business = t3.business
              left join (select distinct query_key,business, conduction_phase from raw_materials_category_rel_tmp where query_key = v_query_key) t4
                on t1.query_key = t4.query_key
				and t1.business = t4.business
			  where t1.business = p_business
            )
            select query_key -- 查询键
			     , business
                 , p_period_id as period_id -- 会计期
                 , data_date                -- 数据日期
                 , base_period              -- 默认基期
                 , init_base_period_flag    -- 初始默认基期标识（Y 是、N 否）
                 , data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , category_code            -- 品类编码
                 , category_name            -- 品类名称
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , index_short_name         -- 指标简称
                 , metal_short_cn_name      -- 金属简称
                 , conduction_phase         -- 传导期
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , index_type               -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                  -- 占比
                 , data_value               -- 数据值
                 , price_index              -- 价格指数
                 , price_index_pp_ptd	      -- 环比
                 , null as price_index_gap	        -- 差异额
                 , '' as remark                          -- 备注
  	        	   , -1 as created_by                      -- 创建人
  	        	   , current_timestamp as creation_date    -- 创建时间
  	        	   , -1 as last_updated_by                 -- 修改人
  	        	   , current_timestamp as last_update_date -- 修改时间
  	        	   , 'N' as del_flag                       -- 是否删除
              from all_info_tmp1
             where data_date >= base_period
            ;
  
            v_dml_row_count := sql%rowcount;  -- 收集数据量
  
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 4,
                p_log_cal_log_desc => '【切换基期对应的默认基期计算】拼接 query_key 值：'||v_query_key||'，原材料价格指数（默认基期）、外部价格指数（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
             ) ;


            -- 取ICT内部价格指数的最大版本
            with foi_max_plan_version_tmp as(
            select max(version_id) as max_version_id
              from fin_dm_opt_foi.dm_foi_plan_version_t
             where upper(data_type) = 'ITEM'
			   and upper(version_type) in('AUTO','FINAL')
               and status = 1
               and del_flag = 'N'
            ),
			 -- 取数字能源的内部价格指数的最大版本
		    foi_max_energy_version_tmp as(
            select max(version_id) as max_version_id
              from fin_dm_opt_foi.dm_foi_energy_plan_version_t
             where upper(data_type) = 'ITEM'
			   and upper(version_type) in('AUTO','FINAL')
               and status = 1
               and del_flag = 'N'
            ),
            -- 内部价格指数临时表（默认基期）
            foi_price_index_tmp as(
            select 'ICT'          as business
			     , period_id      as data_date    -- 数据日期
                 , base_period_id as base_period  -- 默认基期（T-3年）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , group_code    as category_code -- 品类编码
                 , group_cn_name as category_name -- 品类名称
                 , coalesce(price_index,0) as price_index  -- 价格指数
              from fin_dm_opt_foi.dm_foi_price_index_t t1  -- 价格指数总览表
             where version_id = (select max_version_id from foi_max_plan_version_tmp)
               and group_level = 'CATEGORY'  -- 品类
               and base_period_id = v_def_base_period  -- 内部指数的默认基期
               and period_id between v_def_base_period and ((substr(v_def_base_period::varchar(10),1,4)+3)||'12')::numeric  -- 取T+3年的数据
               and l3_ceg_code = p_l3_ceg_code
               and l4_ceg_code = p_l4_ceg_code
               and group_code = p_category_code
               and del_flag = 'N'
			   union all
			    select '数字能源' as business
			     , period_id      as data_date    -- 数据日期
                 , base_period_id as base_period  -- 默认基期（T-3年）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , group_code    as category_code -- 品类编码
                 , group_cn_name as category_name -- 品类名称
                 , coalesce(price_index,0) as price_index  -- 价格指数
              from fin_dm_opt_foi.dm_foi_energy_month_price_index_t t1  -- 数字能源价格指数表
             where version_id = (select max_version_id from foi_max_energy_version_tmp)
               and group_level = 'CATEGORY'  -- 品类
               and base_period_id = v_def_base_period  -- 内部指数的默认基期
               and period_id between v_def_base_period and ((substr(v_def_base_period::varchar(10),1,4)+3)||'12')::numeric  -- 取T+3年的数据
               and l3_ceg_code = p_l3_ceg_code
               and l4_ceg_code = p_l4_ceg_code
               and group_code = p_category_code
               and del_flag = 'N'
            ),
            -- 上月内部价格指数数据
            last_foi_price_index_tmp as(
            select to_char(to_date(data_date,'yyyymm') + interval'1 month','yyyymm')::numeric as last_data_date  -- 上月日期
                 , data_date                -- 数据日期
				 , business
                 , base_period              -- 默认基期（T-3年）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , category_code -- 品类编码
                 , category_name -- 品类名称
                 , price_index                 -- 价格指数
              from foi_price_index_tmp
			  where business = p_business
            ),
            -- 内部价格指数环比
            foi_price_index_pp_ptd_tmp as(
            select t1.data_date    -- 数据日期
			     , t1.business
                 , t1.base_period  -- 默认基期（T-3年）
                 , t1.l3_ceg_code              -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code              -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , t1.category_code -- 品类编码
                 , t1.category_name -- 品类名称
                 , (case when coalesce(t2.price_index,0) = 0 then 0
                         else (coalesce(t1.price_index,0)-coalesce(t2.price_index,0))/coalesce(t2.price_index,0)
                    end) as foi_price_index_pp_ptd  -- 内部价格指数环比
              from foi_price_index_tmp t1  -- 当月数据
              left join last_foi_price_index_tmp t2  -- 上月数据
                on t1.data_date = t2.last_data_date
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
               and t1.category_code = t2.category_code
			   and t1.business = t2.business
			   where t1.business = p_business
            )
            -- 内部价格指数（默认基期），从指数项目组的“价格指数总览表”和“数字能源内部价格指数”直接取
            insert into all_info_tmp2(
                   query_key
				 , business
                 , data_date                 -- 数据日期
                 , data_type                 -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code               -- 专家团编码
                 , l3_ceg_cn_name            -- 专家团中文名称
                 , l3_ceg_short_cn_name      -- 专家团（Group Lv3简称）
                 , l4_ceg_code               -- 模块编码
                 , l4_ceg_cn_name            -- 模块中文名称
                 , l4_ceg_short_cn_name      -- 模块（Group Lv4简称）
                 , category_code             -- 品类编码
                 , category_name             -- 品类名称
                 , index_code                -- 指标编码
                 , index_name                -- 指标名称
                 , index_short_name          -- 指标简称
                 , metal_short_cn_name       -- 金属简称
                 , conduction_phase          -- 传导期
                 , source_name               -- 数据源
                 , source_table              -- 来源表
                 , index_type                -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                   -- 占比
                 , data_value                -- 数据值
                 , price_index               -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
                 , price_index_pp_ptd	       -- 环比
             )
            select v_query_key as query_key
			     , t1.business
                 , t1.data_date                   -- 数据日期
                 , '' as data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code                 -- 专家团编码
                 , t1.l3_ceg_cn_name              -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code                 -- 模块编码
                 , t1.l4_ceg_cn_name              -- 模块中文名称
                 , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
                 , t1.category_code               -- 品类编码
                 , t1.category_name               -- 品类名称
                 , '' as index_code               -- 指标编码
                 , '' as index_name               -- 指标名称
                 , '' as index_short_name         -- 指标简称
                 , '' as metal_short_cn_name      -- 金属简称
                 , null as conduction_phase       -- 传导期
                 , '' as source_name              -- 数据源
                 , '' as source_table             -- 来源表
                 , '内部价格指数' as index_type   -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , null as percent                -- 占比
                 , null as data_value             -- 数据值
                 , t1.price_index                 -- 价格指数（原材料报告期价格/原材料基期价格（默认基期））
                 , t2.foi_price_index_pp_ptd as price_index_pp_ptd	 -- 环比
              from foi_price_index_tmp t1  -- 内部价格指数临时表
              left join foi_price_index_pp_ptd_tmp t2  -- 内部价格指数环比
                on t1.data_date     = t2.data_date
               and t1.l3_ceg_code   = t2.l3_ceg_code
               and t1.l4_ceg_code   = t2.l4_ceg_code
               and t1.category_code = t2.category_code
			   and t1.business = t2.business
			 where t1.business = p_business
            ;

            -- 内部价格指数入到目标表
            insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                   query_key                  -- 查询键
				 , business
                 , period_id                  -- 会计期
                 , data_date                  -- 数据日期
                 , base_period                -- 基期
                 , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                 , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code                -- 专家团编码
                 , l3_ceg_cn_name             -- 专家团中文名称
                 , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                 , l4_ceg_code                -- 模块编码
                 , l4_ceg_cn_name             -- 模块中文名称
                 , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                 , category_code              -- 品类编码
                 , category_name              -- 品类名称
                 , index_code                 -- 指标编码
                 , index_name                 -- 指标名称
                 , index_short_name           -- 指标简称
                 , metal_short_cn_name        -- 金属简称
                 , conduction_phase           -- 传导期
                 , source_name                -- 数据源
                 , source_table               -- 来源表
                 , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                    -- 占比
                 , data_value                 -- 数据值
                 , price_index                -- 价格指数
                 , price_index_pp_ptd         -- 环比
                 , price_index_gap            -- 差异额
                 , remark                     -- 备注
                 , created_by                 -- 创建人
                 , creation_date              -- 创建时间
                 , last_updated_by            -- 修改人
                 , last_update_date           -- 修改时间
                 , del_flag                   -- 是否删除
            )
            select query_key -- 查询键
			     , business
                 , p_period_id as period_id -- 会计期
                 , data_date                -- 数据日期
                 , v_def_base_period as base_period -- 基期
                 , 'Y' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
                 , data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code              -- 专家团编码
                 , l3_ceg_cn_name           -- 专家团中文名称
                 , l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , l4_ceg_code              -- 模块编码
                 , l4_ceg_cn_name           -- 模块中文名称
                 , l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , category_code            -- 品类编码
                 , category_name            -- 品类名称
                 , index_code               -- 指标编码
                 , index_name               -- 指标名称
                 , index_short_name         -- 指标简称
                 , metal_short_cn_name      -- 金属简称
                 , conduction_phase         -- 传导期
                 , source_name              -- 数据源
                 , source_table             -- 来源表
                 , index_type               -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                  -- 占比
                 , data_value               -- 数据值
                 , price_index              -- 价格指数
                 , price_index_pp_ptd	      -- 环比
                 , null as price_index_gap	        -- 差异额
                 , '' as remark                          -- 备注
           	   , -1 as created_by                      -- 创建人
           	   , current_timestamp as creation_date    -- 创建时间
           	   , -1 as last_updated_by                 -- 修改人
           	   , current_timestamp as last_update_date -- 修改时间
           	   , 'N' as del_flag                       -- 是否删除
              from all_info_tmp2
             where query_key = v_query_key
            ;
  
            v_dml_row_count := sql%rowcount;  -- 收集数据量
    
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 5,
                p_log_cal_log_desc => '【切换基期对应的默认基期计算】内部价格指数（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
             ) ;
  
            -- 差异额数据入到目标表
            insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                   query_key                  -- 查询键
				 , business
                 , period_id                  -- 会计期
                 , data_date                  -- 数据日期
                 , base_period                -- 基期
                 , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                 , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                 , l3_ceg_code                -- 专家团编码
                 , l3_ceg_cn_name             -- 专家团中文名称
                 , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                 , l4_ceg_code                -- 模块编码
                 , l4_ceg_cn_name             -- 模块中文名称
                 , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                 , category_code              -- 品类编码
                 , category_name              -- 品类名称
                 , index_code                 -- 指标编码
                 , index_name                 -- 指标名称
                 , index_short_name           -- 指标简称
                 , metal_short_cn_name        -- 金属简称
                 , conduction_phase           -- 传导期
                 , source_name                -- 数据源
                 , source_table               -- 来源表
                 , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , percent                    -- 占比
                 , data_value                 -- 数据值
                 , price_index                -- 价格指数
                 , price_index_pp_ptd         -- 环比
                 , price_index_gap            -- 差异额
                 , remark                     -- 备注
                 , created_by                 -- 创建人
                 , creation_date              -- 创建时间
                 , last_updated_by            -- 修改人
                 , last_update_date           -- 修改时间
                 , del_flag                   -- 是否删除
            )
            with out_price_index_compare_info_tmp as(
            select distinct query_key
			     , business
                 , data_date
                 , l3_ceg_code
                 , l3_ceg_cn_name
                 , l3_ceg_short_cn_name
                 , l4_ceg_code
                 , l4_ceg_cn_name
                 , l4_ceg_short_cn_name
                 , category_code
                 , category_name
                 , price_index
              from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
             where period_id = p_period_id
               and l3_ceg_code = p_l3_ceg_code
               and l4_ceg_code = p_l4_ceg_code
               and category_code = p_category_code
               and index_type = '外部价格指数'
               and query_key = v_query_key
			   and business = p_business
            ),
            in_price_index_compare_info_tmp as(
            select distinct query_key
			     , business
                 , data_date
                 , l3_ceg_code
                 , l3_ceg_cn_name
                 , l3_ceg_short_cn_name
                 , l4_ceg_code
                 , l4_ceg_cn_name
                 , l4_ceg_short_cn_name
                 , category_code
                 , category_name
                 , price_index
              from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
             where period_id = p_period_id
               and index_type = '内部价格指数'
               and query_key = v_query_key
			   and business = p_business
            )
            -- 差异额=外部价格指数-品类采购价格指数
            select t1.query_key -- 查询键
			     , t1.business
                 , p_period_id as period_id -- 会计期
                 , t1.data_date                   -- 数据日期
                 , v_def_base_period as base_period   -- 默认基期（T-3年）
                 , 'Y' as init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
                 , '' as data_type                -- 数据类别（ACT 实际数、FCST 预测数）
                 , t1.l3_ceg_code                 -- 专家团编码
                 , t1.l3_ceg_cn_name              -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
                 , t1.l4_ceg_code                 -- 模块编码
                 , t1.l4_ceg_cn_name              -- 模块中文名称
                 , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
                 , t1.category_code               -- 品类编码
                 , t1.category_name               -- 品类名称
                 , '' as index_code               -- 指标编码
                 , '' as index_name               -- 指标名称
                 , '' as index_short_name         -- 指标简称
                 , '' as metal_short_cn_name      -- 金属简称
                 , null as conduction_phase       -- 传导期
                 , '' as source_name              -- 数据源
                 , '' as source_table             -- 来源表
                 , 'GAP' as index_type            -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                 , null as percent                -- 占比
                 , null as data_value             -- 数据值
                 , null as price_index            -- 价格指数
                 , null as price_index_pp_ptd	    -- 环比
                 , (coalesce(t1.price_index,0) - coalesce(t2.price_index,0)) as price_index_gap 	        -- 差异额
                 , '' as remark                          -- 备注
           	   , -1 as created_by                      -- 创建人
           	   , current_timestamp as creation_date    -- 创建时间
           	   , -1 as last_updated_by                 -- 修改人
           	   , current_timestamp as last_update_date -- 修改时间
           	   , 'N' as del_flag                       -- 是否删除
              from out_price_index_compare_info_tmp t1  -- 外部价格指数临时表
              join in_price_index_compare_info_tmp t2   -- 内部价格指数临时表
                on t1.data_date = t2.data_date
               and t1.l3_ceg_code = t2.l3_ceg_code
               and t1.l4_ceg_code = t2.l4_ceg_code
               and t1.category_code = t2.category_code
               and t1.query_key = t2.query_key
			   and t1.business = t2.business
            ;
  
            v_dml_row_count := sql%rowcount;  -- 收集数据量
    
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 6,
                p_log_cal_log_desc => '【切换基期对应的默认基期计算】差异额（默认基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
             ) ;

          end if;
          -- 【切换基期的默认基期 end】
          
          if not exists(select query_key from raw_materials_category_rel_tmp where query_key = p_query_key) then
          
            -- 根据传入切换基期参数取对应的最新版本专家团、模型信息
            insert into raw_materials_category_rel_tmp(
                   query_key
				 , business
                 , index_code              -- 指数编码
                 , l3_ceg_code            -- 专家团编码
                 , l3_ceg_cn_name         -- 专家团中文名称
                 , l3_ceg_short_cn_name   -- 专家团（Group Lv3简称）
                 , l4_ceg_code            -- 模块编码
                 , l4_ceg_cn_name         -- 模块中文名称
                 , l4_ceg_short_cn_name   -- 模块（Group Lv4简称）
                 , category_code          -- 品类编码
                 , category_name          -- 品类名称
                 , metal_short_cn_name    -- 金属简称
                 , conduction_phase       -- 传导期
                 , percent                -- 占比
                 , init_base_period_flag  -- 初始默认基期标识（Y 是、N 否）
            )
            with metal_percent_tmp2 as(
            select distinct business,index_code, l3_ceg_code, l4_ceg_code, category_code, conduction_phase, percent 
              from metal_percent_tmp1 
             where query_key = p_query_key
            )
            select distinct p_query_key
			     , t1.business
                 , t1.index_code      -- 指数编码
                 , (case when t1.l3_ceg_code = t2.l3_ceg_code then t1.l3_ceg_code else t2.l3_ceg_code end) as l3_ceg_code  -- 专家团编码
                 , t1.l3_ceg_cn_name           -- 专家团中文名称
                 , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
                 , (case when t1.l4_ceg_code = t2.l4_ceg_code then t1.l4_ceg_code else t2.l4_ceg_code end) as l4_ceg_code  -- 模块编码
                 , t1.l4_ceg_cn_name           -- 模块中文名称
                 , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
                 , (case when t1.category_code = t2.category_code then t1.category_code else t2.category_code end) as category_code -- 品类编码
                 , t1.category_name            -- 品类名称
                 , t1.metal_short_cn_name      -- 金属简称
                 , (case when t1.conduction_phase = t2.conduction_phase then t1.conduction_phase else t2.conduction_phase end) as conduction_phase -- 传导期
                 , (case when t1.percent = t2.percent then t1.percent else t2.percent end) as percent  -- 占比
                 , 'N' as init_base_period_flag  -- 初始默认基期标识（Y 是、N 否）
              from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t t1  -- 原材料与品类量化建模关系表
              left join metal_percent_tmp2 t2  -- 传入金属及占比
                on t1.index_code = t2.index_code
			   and t1.business = t2.business
             where t1.del_flag = 'N'
               and upper(t1.status) = 'SUBMIT'
               and t1.version_code = (select max(version_code) from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t where del_flag = 'N' and upper(status) = 'SUBMIT' and business=p_business)  -- 取最大版本
               and t1.category_code = p_category_code  -- 传入品类
			   and t1.business = p_business
            ;
          
            v_dml_row_count := sql%rowcount;  -- 收集数据量
          
            perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
                p_log_version_id => null,                 --版本
                p_log_sp_name => v_sp_name,    --sp名称
                p_log_para_list => '',--参数
                p_log_step_num  => 2,
                p_log_cal_log_desc => '【切换基期计算】传入参数：'||p_period_id||','||p_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||','||p_conduction_phase||','||p_query_key||'，入到 raw_materials_category_rel_tmp 临时表的数据量：'||v_dml_row_count,--日志描述
                p_log_formula_sql_txt => null,--错误信息
                p_log_row_count => v_dml_row_count,
                p_log_errbuf => null  --错误编码
            ) ;
          end if;
          
           -- query_key值拼接，找到切换基期对应的默认基期
            select gs_hash((select distinct period_id||max(to_char(dw_last_update_date,'yyyymmddhh24mi')) over()  
                     from fin_dm_opt_foi.foq_dwk_grp_outs_act_fcst_i
                    where del_flag = 'N'
                      and period_id = p_period_id
                   )||v_max_version_id||','||v_def_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||','||
                  (select distinct conduction_phase from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t
                    where del_flag = 'N'
                      and upper(status) = 'SUBMIT'
                      and version_code = (select max(version_code) from fin_dm_opt_foi.dm_foq_raw_materials_category_rel_t where del_flag = 'N' and upper(status) = 'SUBMIT' and business=p_business )  -- 取最大版本
                      and category_code = p_category_code  -- 传入品类
                      and l3_ceg_code = p_l3_ceg_code
                      and l4_ceg_code = p_l4_ceg_code
					  and business=p_business
                  )||','||p_metal_percent||','||p_business,'sha256') into v_base_query_key
            ;
			
          -- 原材料价格指数（切换基期）、外部价格指数（切换基期）、内部价格指数（切换基期）入到目标表
          insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                  query_key                  -- 查询键
				, business
                , period_id                  -- 会计期
                , data_date                  -- 数据日期
                , base_period                -- 基期
                , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
                , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
                , l3_ceg_code                -- 专家团编码
                , l3_ceg_cn_name             -- 专家团中文名称
                , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
                , l4_ceg_code                -- 模块编码
                , l4_ceg_cn_name             -- 模块中文名称
                , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
                , category_code              -- 品类编码
                , category_name              -- 品类名称
                , index_code                 -- 指标编码
                , index_name                 -- 指标名称
                , index_short_name           -- 指标简称
                , metal_short_cn_name        -- 金属简称
                , conduction_phase           -- 传导期
                , source_name                -- 数据源
                , source_table               -- 来源表
                , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
                , percent                    -- 占比
                , data_value                 -- 数据值
                , price_index                -- 价格指数
                , price_index_pp_ptd         -- 环比
                , price_index_gap            -- 差异额
                , remark                     -- 备注
                , created_by                 -- 创建人
                , creation_date              -- 创建时间
                , last_updated_by            -- 修改人
                , last_update_date           -- 修改时间
                , del_flag                   -- 是否删除
          )
          -- 取默认基期
          with min_base_period_tmp as(
          select min(base_period) as min_base_period, init_base_period_flag,business
           from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
          where period_id = p_period_id
            and l3_ceg_code = p_l3_ceg_code
            and l4_ceg_code = p_l4_ceg_code
            and category_code = p_category_code
			and business = p_business
            and init_base_period_flag = 'Y'        -- 初始默认基期标识（Y 是、N 否）
			and query_key = v_base_query_key
          group by init_base_period_flag,business
          ),
          -- 取默认价格指数的数据
          base_price_index_info_tmp as(
          select distinct t1.business
		       , t1.period_id         -- 会计期
               , (case when t1.index_type = '外部价格指数' then to_char(add_months(to_date(t1.data_date,'yyyymm'),-t1.conduction_phase),'yyyymm')::numeric
                       else t1.data_date
                  end) as data_date            -- 数据日期
               , t1.base_period                -- 基期
               , t1.init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
               , t1.data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code                -- 专家团编码
               , t1.l3_ceg_cn_name             -- 专家团中文名称
               , t1.l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code                -- 模块编码
               , t1.l4_ceg_cn_name             -- 模块中文名称
               , t1.l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
               , t1.category_code              -- 品类编码
               , t1.category_name              -- 品类名称
               , t1.index_code                 -- 指标编码
               , t1.index_name                 -- 指标名称
               , t1.index_short_name           -- 指标简称
               , t1.metal_short_cn_name        -- 金属简称
               , t1.conduction_phase           -- 传导期
               , t1.source_name                -- 数据源
               , t1.source_table               -- 来源表
               , t1.index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , t1.percent                    -- 占比
               , t1.data_value                 -- 数据值
               , t1.price_index                -- 价格指数
               , t1.price_index_pp_ptd         -- 环比
               , t1.price_index_gap            -- 差异额
            from fin_dm_opt_foi.dm_foq_price_index_compare_info_t t1
            join min_base_period_tmp t2
              on t1.base_period = t2.min_base_period
             and t1.init_base_period_flag = t2.init_base_period_flag
			 and t1.business = t2.business
           where t1.period_id = p_period_id
             and t1.l3_ceg_code = p_l3_ceg_code
             and t1.l4_ceg_code = p_l4_ceg_code
             and t1.category_code = p_category_code
			 and t1.business = p_business
			 and t1.query_key = v_base_query_key
          ),
          -- 取切换基期的默认价格指数
          change_base_period_info_tmp as(
          select business
		       , period_id                  -- 会计期
               , data_date                  -- 数据日期
               , base_period                -- 基期
               , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
               , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
               , l3_ceg_code                -- 专家团编码
               , l3_ceg_cn_name             -- 专家团中文名称
               , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
               , l4_ceg_code                -- 模块编码
               , l4_ceg_cn_name             -- 模块中文名称
               , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
               , category_code              -- 品类编码
               , category_name              -- 品类名称
               , index_code                 -- 指标编码
               , index_name                 -- 指标名称
               , index_short_name           -- 指标简称
               , metal_short_cn_name        -- 金属简称
               , conduction_phase           -- 传导期
               , source_name                -- 数据源
               , source_table               -- 来源表
               , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , percent                    -- 占比
               , data_value                 -- 数据值
               , price_index                -- 价格指数
               , price_index_pp_ptd         -- 环比
               , price_index_gap            -- 差异额
            from base_price_index_info_tmp
           where data_date = p_base_period  -- 传入参数（切换基期）
          ),
          /*-- 固定占比
          change_fa_percent_tmp as(
          select data_date,business, category_code, l3_ceg_code, l4_ceg_code, conduction_phase, (1 - sum(coalesce(percent,0))) as fa_percent  -- 固定占比
            from (select distinct t1.data_date, t1.business,t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code, t1.index_code, t2.percent, t1.conduction_phase
                    from base_price_index_info_tmp t1
                    left join fin_dm_opt_foi.metal_percent_tmp1 t2  -- 传入的金属占比
                      on t1.index_code = t2.index_code
                     and t2.query_key = p_query_key
					 and t1.business = t2.business
                   where t1.index_type = '外部价格指数'
                 ) T
           group by data_date, business,category_code, l3_ceg_code, l4_ceg_code, conduction_phase
          ),*/
          metal_percent_tmp3 as(
          select distinct month, business,index_code, l3_ceg_code, l4_ceg_code, category_code, conduction_phase, percent 
            from metal_percent_tmp1 
           where query_key = p_query_key
		     and business = p_business
          ),
          -- 外部价格指数临时表（切换基期，占比也可能会改变）
          change_def_exter_price_index_tmp as(
          select t1.data_date,t1.business, t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code, t1.index_code, t1.conduction_phase
               , (case when coalesce(t2.price_index,0) = 0 then 0
                       else round(coalesce(t1.price_index,0)/coalesce(t2.price_index,0),10)  -- 默认基期价格指数/报告期价格指数
                  end) as exter_price_index  -- 外部价格指数
            from base_price_index_info_tmp t1
            left join change_base_period_info_tmp t2  -- 切换基期的默认价格指数
              on t1.category_code = t2.category_code
             and t1.index_code = t2.index_code
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
             and t1.index_type = t2.index_type
			 and t1.business = t2.business
           /* left join metal_percent_tmp3 t3
              on t1.index_code = t3.index_code
             and t1.business = t3.business
             and substr(t1.data_date,5,2) = lpad(t3.month,2,0)*/
           where t1.index_type = '外部价格指数'
          /*union all
          select data_date, business,category_code, l3_ceg_code, l4_ceg_code, '' as index_code, conduction_phase, fa_percent as exter_price_index -- 外部价格指数
            from change_fa_percent_tmp*/
          ),
          -- 外部价格指数按品类汇总
          change_exter_price_index_sum_tmp as(
          select data_date, business,category_code, l3_ceg_code, l4_ceg_code, conduction_phase, exter_price_index*100 as exter_price_index -- 外部价格指数
            from change_def_exter_price_index_tmp
           group by data_date, business,category_code, l3_ceg_code, l4_ceg_code, conduction_phase,exter_price_index
          ),
          -- 上月外部价格指数数据
          change_last_def_exter_price_index_tmp as(
          select to_char(to_date(data_date,'yyyymm') + interval'1 month','yyyymm')::numeric as last_data_date  -- 上月日期
               , data_date,business, category_code, l3_ceg_code, l4_ceg_code, exter_price_index -- 外部价格指数
            from change_exter_price_index_sum_tmp t1
          ),
          -- 外部价格指数环比
          change_exter_price_index_pp_ptd_tmp as(
          select t1.data_date,t1.business, t1.category_code, t1.l3_ceg_code, t1.l4_ceg_code
               , (case when coalesce(t2.exter_price_index,0) = 0 then 0
                       else (coalesce(t1.exter_price_index,0)-coalesce(t2.exter_price_index,0))/coalesce(t2.exter_price_index,0)
                  end) as exter_price_index_pp_ptd  -- 外部价格指数环比
            from change_exter_price_index_sum_tmp t1  -- 当月数据
            left join change_last_def_exter_price_index_tmp t2 -- 上月数据
              on t1.data_date = t2.last_data_date
             and t1.category_code = t2.category_code
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
			 and t1.business = t2.business
          ),
          change_all_info_tmp1 as(
          -- 原材料价格指数（切换基期）
          select t1.period_id                  -- 会计期
		       , t1.business
               , t1.data_date                  -- 数据日期
               , t1.data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code                -- 专家团编码
               , t1.l3_ceg_cn_name             -- 专家团中文名称
               , t1.l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code                -- 模块编码
               , t1.l4_ceg_cn_name             -- 模块中文名称
               , t1.l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
               , t1.category_code              -- 品类编码
               , t1.category_name              -- 品类名称
               , t1.index_code                 -- 指标编码
               , t1.index_name                 -- 指标名称
               , t1.index_short_name           -- 指标简称
               , t1.metal_short_cn_name        -- 金属简称
               , t1.conduction_phase           -- 传导期
               , t1.source_name                -- 数据源
               , t1.source_table               -- 来源表
               , '原材料价格指数' as index_type    -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , t1.percent                    -- 占比
               , t1.data_value                 -- 数据值
               , (case when t2.price_index = 0 then 0
                       else (t1.price_index/t2.price_index)*100
                  end) as price_index          -- 价格指数
               , null as price_index_pp_ptd    -- 环比
            from base_price_index_info_tmp t1
            left join change_base_period_info_tmp t2
              on t1.period_id = t2.period_id
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
             and t1.category_code = t2.category_code
             and t1.index_type = t2.index_type
             and t1.index_code = t2.index_code
			 and t1.business = t2.business 
           where t1.index_type = '原材料价格指数'
          union all
          -- 外部价格指数（切换基期）
          select t1.period_id                  -- 会计期
		       , t1.business
               , (case when t1.conduction_phase = t5.conduction_phase then to_char(add_months(to_date(t1.data_date,'yyyymm'),t1.conduction_phase),'yyyymm')::numeric
                       else to_char(add_months(to_date(t1.data_date,'yyyymm'),t5.conduction_phase),'yyyymm')::numeric   -- @ 绝对值
                  end) as data_date -- 数据日期
               , t1.data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code                -- 专家团编码
               , t1.l3_ceg_cn_name             -- 专家团中文名称
               , t1.l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code                -- 模块编码
               , t1.l4_ceg_cn_name             -- 模块中文名称
               , t1.l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
               , t1.category_code              -- 品类编码
               , t1.category_name              -- 品类名称
               , t1.index_code                 -- 指标编码
               , t1.index_name                 -- 指标名称
               , t1.index_short_name           -- 指标简称
               , t1.metal_short_cn_name        -- 金属简称
               , t1.conduction_phase           -- 传导期
               , t1.source_name                -- 数据源
               , t1.source_table               -- 来源表
               , '外部价格指数' as index_type  -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , t5.percent                    -- 占比
               , t1.data_value                 -- 数据值
               , t2.exter_price_index        as price_index        -- 价格指数
               , t3.exter_price_index_pp_ptd as price_index_pp_ptd -- 环比
            from base_price_index_info_tmp t1
            left join change_exter_price_index_sum_tmp t2
              on t1.data_date = t2.data_date
             and t1.category_code = t2.category_code
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
			 and t1.business = t2.business
            left join change_exter_price_index_pp_ptd_tmp t3  -- 外部价格指数环比
              on t1.data_date = t3.data_date
             and t1.category_code = t3.category_code
             and t1.l3_ceg_code = t3.l3_ceg_code
             and t1.l4_ceg_code = t3.l4_ceg_code
			 and t1.business = t3.business
            left join min_base_period_tmp t4
              on t1.business = t4.business
            left join metal_percent_tmp3 t5
              on t1.index_code = t5.index_code
             and t1.category_code = t5.category_code
             and t1.l3_ceg_code = t5.l3_ceg_code
             and t1.l4_ceg_code = t5.l4_ceg_code
			 and t1.business = t5.business
             and substr(t1.data_date,5,2) = lpad(t5.month,2,0)
           where t1.index_type = '外部价格指数'
          union all
          -- 内部价格指数（切换基期）
          select t1.period_id                   -- 会计期
		       , t1.business
               , t1.data_date                   -- 数据日期
               , t1.data_type                   -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code                 -- 专家团编码
               , t1.l3_ceg_cn_name              -- 专家团中文名称
               , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code                 -- 模块编码
               , t1.l4_ceg_cn_name              -- 模块中文名称
               , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
               , t1.category_code               -- 品类编码
               , t1.category_name               -- 品类名称
               , '' as index_code               -- 指标编码
               , '' as index_name               -- 指标名称
               , '' as index_short_name         -- 指标简称
               , '' as metal_short_cn_name      -- 金属简称
               , null as conduction_phase       -- 传导期
               , '' as source_name              -- 数据源
               , '' as source_table             -- 来源表
               , '内部价格指数' as index_type   -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , null as percent                -- 占比
               , null as data_value             -- 数据值
               , (case when t2.price_index = 0 then 0
                       else (t1.price_index/t2.price_index)*100
                  end) as price_index           -- 价格指数
               , t1.price_index_pp_ptd	        -- 环比
            from base_price_index_info_tmp t1
            left join change_base_period_info_tmp t2
              on t1.period_id = t2.period_id
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
             and t1.category_code = t2.category_code
             and t1.index_type = t2.index_type
			 and t1.business = t2.business
           where t1.index_type = '内部价格指数'
          )
          select p_query_key as query_key   -- 查询键
			   , t1.business		    
               , p_period_id as period_id   -- 会计期
               , t1.data_date                -- 数据日期
               , p_base_period as base_period -- 基期
               , 'N' as init_base_period_flag -- 初始默认基期标识（Y 是、N 否）
               , t1.data_type                -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code              -- 专家团编码
               , t1.l3_ceg_cn_name           -- 专家团中文名称
               , t1.l3_ceg_short_cn_name     -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code              -- 模块编码
               , t1.l4_ceg_cn_name           -- 模块中文名称
               , t1.l4_ceg_short_cn_name     -- 模块（Group Lv4简称）
               , t1.category_code            -- 品类编码
               , t1.category_name            -- 品类名称
               , t1.index_code               -- 指标编码
               , t1.index_name               -- 指标名称
               , t1.index_short_name         -- 指标简称
               , t1.metal_short_cn_name      -- 金属简称
               , t1.conduction_phase         -- 传导期
               , t1.source_name              -- 数据源
               , t1.source_table             -- 来源表
               , t1.index_type               -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , t1.percent                  -- 占比
               , t1.data_value               -- 数据值
               , t1.price_index              -- 价格指数
               , t1.price_index_pp_ptd	      -- 环比
               , null as price_index_gap	-- 差异额
               , '' as remark                          -- 备注
	        	   , -1 as created_by                      -- 创建人
	        	   , current_timestamp as creation_date    -- 创建时间
	        	   , -1 as last_updated_by                 -- 修改人
	        	   , current_timestamp as last_update_date -- 修改时间
	        	   , 'N' as del_flag                       -- 是否删除
            from change_all_info_tmp1 t1
           where t1.data_date >= (select min(data_date) as min_data_date from base_price_index_info_tmp)
             and t1.data_date <= (select max(data_date) as max_data_date from base_price_index_info_tmp)
          ;

          v_dml_row_count := sql%rowcount;  -- 收集数据量

          perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
              p_log_version_id => null,                 --版本
              p_log_sp_name => v_sp_name,    --sp名称
              p_log_para_list => '',--参数
              p_log_step_num  => 3,
              p_log_cal_log_desc => '【切换基期计算】传入参数：'||p_period_id||','||p_base_period||','||p_l3_ceg_code||','||p_l4_ceg_code||','||p_category_code||','||p_conduction_phase||','||p_metal_percent||','||p_query_key||'，原材料价格指数（切换基期）、外部价格指数（切换基期）、内部价格指数（切换基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count,--日志描述
              p_log_formula_sql_txt => null,--错误信息
              p_log_row_count => v_dml_row_count,
              p_log_errbuf => null  --错误编码
           ) ;

          -- 差异额数据入到目标表
          insert into fin_dm_opt_foi.dm_foq_price_index_compare_info_t(
                query_key                  -- 查询键
			  , business
              , period_id                  -- 会计期
              , data_date                  -- 数据日期
              , base_period                -- 基期
              , init_base_period_flag      -- 初始默认基期标识（Y 是、N 否）
              , data_type                  -- 数据类别（ACT 实际数、FCST 预测数）
              , l3_ceg_code                -- 专家团编码
              , l3_ceg_cn_name             -- 专家团中文名称
              , l3_ceg_short_cn_name       -- 专家团（Group Lv3简称）
              , l4_ceg_code                -- 模块编码
              , l4_ceg_cn_name             -- 模块中文名称
              , l4_ceg_short_cn_name       -- 模块（Group Lv4简称）
              , category_code              -- 品类编码
              , category_name              -- 品类名称
              , index_code                 -- 指标编码
              , index_name                 -- 指标名称
              , index_short_name           -- 指标简称
              , metal_short_cn_name        -- 金属简称
              , conduction_phase           -- 传导期
              , source_name                -- 数据源
              , source_table               -- 来源表
              , index_type                 -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
              , percent                    -- 占比
              , data_value                 -- 数据值
              , price_index                -- 价格指数
              , price_index_pp_ptd         -- 环比
              , price_index_gap            -- 差异额
              , remark                     -- 备注
              , created_by                 -- 创建人
              , creation_date              -- 创建时间
              , last_updated_by            -- 修改人
              , last_update_date           -- 修改时间
              , del_flag                   -- 是否删除
          )
          with out_price_index_compare_info_tmp as(
          select distinct query_key
		       , business
               , data_date
               , l3_ceg_code
               , l3_ceg_cn_name
               , l3_ceg_short_cn_name
               , l4_ceg_code
               , l4_ceg_cn_name
               , l4_ceg_short_cn_name
               , category_code
               , category_name
               , price_index
            from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
           where period_id = p_period_id
             and l3_ceg_code = p_l3_ceg_code
             and l4_ceg_code = p_l4_ceg_code
             and category_code = p_category_code
             and index_type = '外部价格指数'
             and query_key = p_query_key
			 and business = p_business
          ),
          in_price_index_compare_info_tmp as(
          select distinct query_key
		       , business
               , data_date
               , l3_ceg_code
               , l3_ceg_cn_name
               , l3_ceg_short_cn_name
               , l4_ceg_code
               , l4_ceg_cn_name
               , l4_ceg_short_cn_name
               , category_code
               , category_name
               , price_index
            from fin_dm_opt_foi.dm_foq_price_index_compare_info_t
           where period_id = p_period_id
             and index_type = '内部价格指数'
             and query_key = p_query_key
			 and business = p_business
          )
          -- 差异额=外部价格指数（切换基期）-品类采购价格指数（切换基期）
          select t1.query_key   -- 查询键
		       , t1.business
               , p_period_id as period_id   -- 会计期
               , t1.data_date                   -- 数据日期
               , p_base_period as base_period   -- 基期
               , 'N' as init_base_period_flag   -- 初始默认基期标识（Y 是、N 否）
               , '' as data_type                -- 数据类别（ACT 实际数、FCST 预测数）
               , t1.l3_ceg_code                 -- 专家团编码
               , t1.l3_ceg_cn_name              -- 专家团中文名称
               , t1.l3_ceg_short_cn_name        -- 专家团（Group Lv3简称）
               , t1.l4_ceg_code                 -- 模块编码
               , t1.l4_ceg_cn_name              -- 模块中文名称
               , t1.l4_ceg_short_cn_name        -- 模块（Group Lv4简称）
               , t1.category_code               -- 品类编码
               , t1.category_name               -- 品类名称
               , '' as index_code               -- 指标编码
               , '' as index_name               -- 指标名称
               , '' as index_short_name         -- 指标简称
               , '' as metal_short_cn_name      -- 金属简称
               , null as conduction_phase       -- 传导期
               , '' as source_name              -- 数据源
               , '' as source_table             -- 来源表
               , 'GAP' as index_type            -- 指数类型（原材料价格指数、外部价格指数、内部价格指数、GAP）
               , null as percent                -- 占比
               , null as data_value             -- 数据值
               , null as price_index            -- 价格指数
               , null as price_index_pp_ptd	    -- 环比
               , (coalesce(t1.price_index,0) - coalesce(t2.price_index,0)) as price_index_gap 	        -- 差异额
               , '' as remark                          -- 备注
	        	   , -1 as created_by                      -- 创建人
	        	   , current_timestamp as creation_date    -- 创建时间
	        	   , -1 as last_updated_by                 -- 修改人
	        	   , current_timestamp as last_update_date -- 修改时间
	        	   , 'N' as del_flag                       -- 是否删除
            from out_price_index_compare_info_tmp t1  -- 外部价格指数临时表
            join in_price_index_compare_info_tmp t2   -- 内部价格指数临时表
              on t1.data_date = t2.data_date
             and t1.l3_ceg_code = t2.l3_ceg_code
             and t1.l4_ceg_code = t2.l4_ceg_code
             and t1.category_code = t2.category_code
             and t1.query_key = t2.query_key
			 and t1.business = t2.business
        ;

        v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 4,
            p_log_cal_log_desc => '【切换基期计算】差异额（切换基期）数据入到 dm_foq_price_index_compare_info_t 目标表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

          end if;
          -- 【切换基期 end】
        end if;
      end if;

  exception
    when others then
       perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败

  --收集统计信息
  analyse fin_dm_opt_foi.dm_foq_price_index_compare_info_t;

end;
$$
/

