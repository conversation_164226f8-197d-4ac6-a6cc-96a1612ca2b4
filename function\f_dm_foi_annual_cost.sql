-- Name: f_dm_foi_annual_cost; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_annual_cost(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间:2024年2月18日17点55分
  创建人  :唐钦
  背景描述:ITEM的年均本基础表
  参数描述:f_caliber_flag ：I：采购价格指数、E：数字能源指数
           x_result_status ：是否成功
  事例    :select fin_dm_opt_foi.f_dm_foi_annual_cost('I')
*/
DECLARE
  V_SP_NAME    VARCHAR(100) := 'FIN_DM_OPT_FOI.F_DM_FOI_ANNUAL_COST'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; -- 新的版本号
  V_SQL        TEXT; 
  V_FROM_TABLE VARCHAR(200);
  V_FROM1_TABLE VARCHAR(200);
  V_TO_TABLE   VARCHAR(200);
  V_COLUMN     VARCHAR(50);
  -- 202407版本新增
  V_VERSION_TABLE VARCHAR(100);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(100);
  V_SQL_CONDITION VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
  
-- 判断不同入参，对应不同变量、参数
IF F_CALIBER_FLAG = 'I' THEN  -- ICT价格指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T';  -- 预测数
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ANNUAL_COST_T';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
    V_COLUMN := 'CONTINUITY_TYPE';
ELSIF F_CALIBER_FLAG = 'E' THEN  -- 数字能源指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_FCST_SUM_T';  -- ITEM层级数据（预测）
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MID_ANNUAL_COST_T';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
    V_COLUMN := 'GROUP_PUR_FLAG';
ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') THEN  -- IAS/华东采购
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_FCST_SUM_T';  -- ITEM层级数据（预测）
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MID_ANNUAL_COST_T';
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := 'AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
END IF;
  
-- 版本号取值
IF F_VERSION_ID IS NOT NULL THEN
     V_VERSION_ID := F_VERSION_ID;
  ELSE
  V_SQL := '
        SELECT VERSION_ID 
           FROM '||V_VERSION_TABLE||'
           WHERE DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = UPPER(''CATEGORY'')
           AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
           ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
 END IF;
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --1.清空目标表数据:
  IF F_CALIBER_FLAG IN ('I','E') THEN
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  ELSE 
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  END IF;
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   --创建年均本临时表
   DROP TABLE IF EXISTS AVG_APD_TMP;
   CREATE TEMPORARY TABLE AVG_APD_TMP (
          PERIOD_YEAR VARCHAR(50) ,
          L2_CEG_CODE VARCHAR(50) ,
          L2_CEG_CN_NAME VARCHAR(200) ,
          L3_CEG_CODE VARCHAR(50) ,
          L3_CEG_CN_NAME VARCHAR(200) ,
          L3_CEG_SHORT_CN_NAME VARCHAR(200) ,
          L4_CEG_CODE VARCHAR(50) ,
          L4_CEG_CN_NAME VARCHAR(200) ,
          L4_CEG_SHORT_CN_NAME VARCHAR(200) ,
          CATEGORY_CODE VARCHAR(50) ,
          CATEGORY_CN_NAME VARCHAR(200) ,
          ITEM_CODE VARCHAR(50) ,
          ITEM_CN_NAME VARCHAR(1000) ,
          SUPPLIER_CODE VARCHAR(100) ,
          SUPPLIER_CN_NAME VARCHAR(500) ,
          RECEIVE_QTY NUMERIC,
          RECEIVE_AMT_CNY NUMERIC,
          AVG_AMT NUMERIC,
          NULL_FLAG VARCHAR(2),
          APPEND_FLAG VARCHAR(2),
          GROUP_LEVEL VARCHAR(50),
          LEVEL_TYPE VARCHAR(50)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(SUPPLIER_CODE,ITEM_CODE);
    
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => '年均本临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');  
        
--插入临时表数据
V_SQL := '
  INSERT INTO AVG_APD_TMP(
  PERIOD_YEAR,
  L2_CEG_CODE,
  L2_CEG_CN_NAME,
  L3_CEG_CODE,
  L3_CEG_CN_NAME,
  L3_CEG_SHORT_CN_NAME,
  L4_CEG_CODE,
  L4_CEG_CN_NAME,
  L4_CEG_SHORT_CN_NAME,
  CATEGORY_CODE,
  CATEGORY_CN_NAME,
  ITEM_CODE,
  ITEM_CN_NAME,
  SUPPLIER_CODE,
  SUPPLIER_CN_NAME,
  RECEIVE_QTY,
  RECEIVE_AMT_CNY,
  AVG_AMT,
  NULL_FLAG,
  APPEND_FLAG,
  GROUP_LEVEL,
  LEVEL_TYPE)         

--SUPPLIER层级实际数
WITH ACTUALITY_SUP_TMP AS(
    SELECT
      SUBSTR(PERIOD_ID,1,4) AS PERIOD_YEAR ,
      L2_CEG_CODE,
      L2_CEG_CN_NAME,
      L3_CEG_CODE  ,
      L3_CEG_CN_NAME  ,
      L3_CEG_SHORT_CN_NAME ,
      L4_CEG_CODE  ,
      L4_CEG_CN_NAME  ,
      L4_CEG_SHORT_CN_NAME ,
      CATEGORY_CODE ,
      CATEGORY_NAME AS CATEGORY_CN_NAME ,
      ITEM_CODE  ,
      ITEM_NAME AS ITEM_CN_NAME ,
      SUPPLIER_CODE ,
      SUPPLIER_CN_NAME,
      ''SUPPLIER'' AS GROUP_LEVEL, --SUPPLIER层
      ''YTD'' AS LEVEL_TYPE, --实际数
      SUM(RECEIVE_QTY) AS RECEIVE_QTY,
      SUM(RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
      NULLIF(SUM(RECEIVE_AMT_CNY),0)/NULLIF(SUM(RECEIVE_QTY),0) AS RMB_AVG_AMT
    FROM '||V_FROM_TABLE||' T --实际数表
    WHERE SUBSTR(PERIOD_ID,1,4) >=YEAR(CURRENT_TIMESTAMP) - 3    -- 共四年数据(今年、去年、前年、大前年)
    '||V_SQL_CONDITION||'
 GROUP BY  SUBSTR(PERIOD_ID,1,4),
           L2_CEG_CODE,
           L2_CEG_CN_NAME,
           L3_CEG_CODE  ,
           L3_CEG_CN_NAME  ,
           L3_CEG_SHORT_CN_NAME ,
           L4_CEG_CODE  ,
           L4_CEG_CN_NAME  ,
           L4_CEG_SHORT_CN_NAME ,
           CATEGORY_CODE ,
           CATEGORY_NAME,
           ITEM_CODE  ,
           ITEM_NAME ,
           SUPPLIER_CODE ,
           SUPPLIER_CN_NAME
),
--ITEM层级实际数
ACTUALITY_ITEM_TMP AS(
    SELECT
      PERIOD_YEAR ,
      L2_CEG_CODE,
      L2_CEG_CN_NAME,
      L3_CEG_CODE  ,
      L3_CEG_CN_NAME  ,
      L3_CEG_SHORT_CN_NAME ,
      L4_CEG_CODE  ,
      L4_CEG_CN_NAME  ,
      L4_CEG_SHORT_CN_NAME ,
      CATEGORY_CODE ,
      CATEGORY_CN_NAME ,
      ITEM_CODE ,
      ITEM_CN_NAME ,
      ''ITEM'' AS GROUP_LEVEL, --ITEM层
      ''YTD'' AS LEVEL_TYPE, --实际数
      SUM(RECEIVE_QTY) AS RECEIVE_QTY,
      SUM(RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
      NULLIF(SUM(RECEIVE_AMT_CNY),0)/NULLIF(SUM(RECEIVE_QTY),0) AS RMB_AVG_AMT
    FROM ACTUALITY_SUP_TMP T --实际数
 GROUP BY  PERIOD_YEAR,
           L2_CEG_CODE,
           L2_CEG_CN_NAME,
           L3_CEG_CODE  ,
           L3_CEG_CN_NAME  ,
           L3_CEG_SHORT_CN_NAME ,
           L4_CEG_CODE  ,
           L4_CEG_CN_NAME  ,
           L4_CEG_SHORT_CN_NAME ,
           CATEGORY_CODE ,
           CATEGORY_CN_NAME,
           ITEM_CODE  ,
           ITEM_CN_NAME 
),
--ITEM层级预测数
PREDICT_ITEM_TMP AS(
 SELECT
      SUBSTR(PERIOD_ID,1,4) AS PERIOD_YEAR,
      L2_CEG_CODE,
      L2_CEG_CN_NAME,
      L3_CEG_CODE  ,
      L3_CEG_CN_NAME  ,
      L3_CEG_SHORT_CN_NAME ,
      L4_CEG_CODE  ,
      L4_CEG_CN_NAME  ,
      L4_CEG_SHORT_CN_NAME ,
      CATEGORY_CODE ,
      CATEGORY_NAME AS CATEGORY_CN_NAME ,
      ITEM_CODE ,
      ITEM_NAME AS ITEM_CN_NAME ,
      ''ITEM'' AS GROUP_LEVEL,
      SUM(RECEIVE_QTY) AS RECEIVE_QTY,
      SUM(RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
      NULLIF(SUM(RECEIVE_AMT_CNY),0)/NULLIF(SUM(RECEIVE_QTY),0) AS RMB_AVG_AMT
    FROM '||V_FROM1_TABLE||' T  --预测数表
    WHERE T.PERIOD_ID >= TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')    -- 今年预测
    '||V_SQL_CONDITION||'
 GROUP BY SUBSTR(PERIOD_ID,1,4),
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          L3_CEG_CODE  ,
          L3_CEG_CN_NAME  ,
          L3_CEG_SHORT_CN_NAME ,
          L4_CEG_CODE  ,
          L4_CEG_CN_NAME  ,
          L4_CEG_SHORT_CN_NAME ,
          CATEGORY_CODE ,
          CATEGORY_NAME ,
          ITEM_CODE  ,
          ITEM_NAME 
),
--ITEM层级的四年YTD+预测(实际数+预测数)
YTD_ACT_PREDICT_TMP AS(
   SELECT PERIOD_YEAR ,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          L3_CEG_CODE  ,
          L3_CEG_CN_NAME  ,
          L3_CEG_SHORT_CN_NAME ,
          L4_CEG_CODE  ,
          L4_CEG_CN_NAME  ,
          L4_CEG_SHORT_CN_NAME ,
          CATEGORY_CODE ,
          CATEGORY_CN_NAME ,
          ITEM_CODE ,
          ITEM_CN_NAME ,
          ''ITEM'' AS GROUP_LEVEL, --ITEM层级的(实际数+预测数)
          ''YTD_PREDICT'' AS LEVEL_TYPE, --实际数+预测数
          SUM(RECEIVE_QTY) AS RECEIVE_QTY,
          SUM(RECEIVE_AMT_CNY) AS RECEIVE_AMT_CNY,
          NULLIF(SUM(RECEIVE_AMT_CNY),0)/NULLIF(SUM(RECEIVE_QTY),0) AS RMB_AVG_AMT
   FROM (  -- 当年预测是指：当年YTD+当年预测
          SELECT
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE  ,
              L3_CEG_CN_NAME  ,
              L3_CEG_SHORT_CN_NAME ,
              L4_CEG_CODE  ,
              L4_CEG_CN_NAME  ,
              L4_CEG_SHORT_CN_NAME ,
              CATEGORY_CODE ,
              CATEGORY_CN_NAME ,
              ITEM_CODE  ,
              ITEM_CN_NAME ,
              RECEIVE_QTY,
              RECEIVE_AMT_CNY
         FROM ACTUALITY_ITEM_TMP T --ITEM层 实际数 (当年、去年、前年、大前年)用于补齐预测各年份金额数据
        UNION ALL 
        SELECT
              PERIOD_YEAR,
              L2_CEG_CODE,
              L2_CEG_CN_NAME,
              L3_CEG_CODE ,
              L3_CEG_CN_NAME  ,
              L3_CEG_SHORT_CN_NAME ,
              L4_CEG_CODE  ,
              L4_CEG_CN_NAME  ,
              L4_CEG_SHORT_CN_NAME ,
              CATEGORY_CODE ,
              CATEGORY_CN_NAME ,
              ITEM_CODE  ,
              ITEM_CN_NAME ,
              RECEIVE_QTY,
              RECEIVE_AMT_CNY
            FROM PREDICT_ITEM_TMP T --ITEM层 当年预测数
        )
        GROUP BY 
          PERIOD_YEAR ,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          L3_CEG_CODE ,
          L3_CEG_CN_NAME  ,
          L3_CEG_SHORT_CN_NAME ,
          L4_CEG_CODE  ,
          L4_CEG_CN_NAME  ,
          L4_CEG_SHORT_CN_NAME ,
          CATEGORY_CODE ,
          CATEGORY_CN_NAME ,
          ITEM_CODE  ,
          ITEM_CN_NAME
),

  --汇总数据
  ALL_AVG_TMP AS(
    --SUPPLIER层级实际数
    SELECT
          PERIOD_YEAR,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          L3_CEG_CODE  ,
          L3_CEG_CN_NAME  ,
          L3_CEG_SHORT_CN_NAME ,
          L4_CEG_CODE  ,
          L4_CEG_CN_NAME  ,
          L4_CEG_SHORT_CN_NAME ,
          CATEGORY_CODE ,
          CATEGORY_CN_NAME ,
          ITEM_CODE  ,
          ITEM_CN_NAME ,
          SUPPLIER_CODE ,
          SUPPLIER_CN_NAME,
          GROUP_LEVEL,
          LEVEL_TYPE,
          RECEIVE_QTY,
          RECEIVE_AMT_CNY,
          RMB_AVG_AMT
     FROM ACTUALITY_SUP_TMP
    UNION ALL
    --ITEM层级实际数
    SELECT
          PERIOD_YEAR,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          L3_CEG_CODE  ,
          L3_CEG_CN_NAME  ,
          L3_CEG_SHORT_CN_NAME ,
          L4_CEG_CODE  ,
          L4_CEG_CN_NAME  ,
          L4_CEG_SHORT_CN_NAME ,
          CATEGORY_CODE ,
          CATEGORY_CN_NAME ,
          ITEM_CODE  ,
          ITEM_CN_NAME ,
          NULL AS SUPPLIER_CODE ,
          NULL AS SUPPLIER_CN_NAME,
          GROUP_LEVEL,
          LEVEL_TYPE,
          RECEIVE_QTY,
          RECEIVE_AMT_CNY,
          RMB_AVG_AMT
     FROM ACTUALITY_ITEM_TMP
    UNION ALL
    --ITEM层级的四年YTD+预测(实际数+预测数)
    SELECT
          PERIOD_YEAR ,
          L2_CEG_CODE,
          L2_CEG_CN_NAME,
          L3_CEG_CODE  ,
          L3_CEG_CN_NAME  ,
          L3_CEG_SHORT_CN_NAME ,
          L4_CEG_CODE  ,
          L4_CEG_CN_NAME  ,
          L4_CEG_SHORT_CN_NAME ,
          CATEGORY_CODE ,
          CATEGORY_CN_NAME ,
          ITEM_CODE  ,
          ITEM_CN_NAME ,
          NULL AS SUPPLIER_CODE ,
          NULL AS SUPPLIER_CN_NAME,
          GROUP_LEVEL,
          LEVEL_TYPE,
          RECEIVE_QTY,
          RECEIVE_AMT_CNY,
          RMB_AVG_AMT
     FROM YTD_ACT_PREDICT_TMP
)
 
    -- 实际数均价临时表中出现的ITEM维、供应商维等，取数范围：三年前至当前系统年(若当年为1月时，当年年份不含)
  , DIM_TEAM_TMP AS(
        SELECT DISTINCT L2_CEG_CODE,
                        L2_CEG_CN_NAME,
                        L3_CEG_CODE,
                        L3_CEG_CN_NAME,
                        L3_CEG_SHORT_CN_NAME,
                        L4_CEG_CODE  ,
                        L4_CEG_CN_NAME  ,
                        L4_CEG_SHORT_CN_NAME ,
                        CATEGORY_CODE,
                        CATEGORY_CN_NAME,
                        ITEM_CODE,
                        ITEM_CN_NAME,
                        SUPPLIER_CODE,
                        SUPPLIER_CN_NAME,
                        GROUP_LEVEL,
                        LEVEL_TYPE
                   FROM ALL_AVG_TMP
  )                        
    -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  , PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )
    -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT T2.PERIOD_YEAR,
              T1.L2_CEG_CODE,
              T1.L2_CEG_CN_NAME,
              T1.L3_CEG_CODE,
              T1.L3_CEG_CN_NAME,
              T1.L3_CEG_SHORT_CN_NAME,
              T1.L4_CEG_CODE,
              T1.L4_CEG_CN_NAME,
              T1.L4_CEG_SHORT_CN_NAME,
              T1.CATEGORY_CODE,
              T1.CATEGORY_CN_NAME,
              T1.ITEM_CODE,
              T1.ITEM_CN_NAME,
              T1.SUPPLIER_CODE,
              T1.SUPPLIER_CN_NAME,
              T1.GROUP_LEVEL,
              T1.LEVEL_TYPE
           FROM DIM_TEAM_TMP T1,PERIOD_YEAR_TMP T2
  )
  SELECT T1.PERIOD_YEAR,
         T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,
         T1.L3_CEG_CODE,
         T1.L3_CEG_CN_NAME,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L4_CEG_CODE,
         T1.L4_CEG_CN_NAME,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.CATEGORY_CODE,
         T1.CATEGORY_CN_NAME,
         T1.ITEM_CODE,
         T1.ITEM_CN_NAME,
         T1.SUPPLIER_CODE,
         T1.SUPPLIER_CN_NAME,
         T2.RECEIVE_QTY,
         T2.RECEIVE_AMT_CNY,
         T2.RMB_AVG_AMT,
         DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
         DECODE(T2.RMB_AVG_AMT, NULL, ''Y'', ''N'') AS APPEND_FLAG, --补齐标识：Y为补齐，N为原始
         T1.GROUP_LEVEL,
         T1.LEVEL_TYPE
    FROM CONTIN_DIM_TMP T1
    LEFT JOIN ALL_AVG_TMP T2
      ON T1.L2_CEG_CODE = T2.L2_CEG_CODE
     AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
     AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
     AND T1.CATEGORY_CODE = T2.CATEGORY_CODE
     AND T1.ITEM_CODE = T2.ITEM_CODE
     AND NVL(T1.SUPPLIER_CODE,''SNULL'') = NVL(T2.SUPPLIER_CODE,''SNULL'')
     AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
     AND T1.LEVEL_TYPE = T2.LEVEL_TYPE';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
         
     --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '计算年均本数据插入到临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
        
    -- 插入补齐后的分层级下年均本数据
V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
            PERIOD_YEAR,
            L2_CEG_CODE,
            L2_CEG_CN_NAME,
            L3_CEG_CODE,
            L3_CEG_CN_NAME,
            L3_CEG_SHORT_CN_NAME,
            L4_CEG_CODE,
            L4_CEG_CN_NAME,
            L4_CEG_SHORT_CN_NAME,
            CATEGORY_CODE,
            CATEGORY_CN_NAME,
            ITEM_CODE,
            ITEM_CN_NAME,
            SUPPLIER_CODE,
            SUPPLIER_CN_NAME,
            RMB_AVG_AMT,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            DEL_FLAG,
            APPEND_FLAG,
            '||V_COLUMN||',
            '||V_CALIBER||'
            VERSION_ID,
            APPEND_YEAR,
            GROUP_LEVEL,
            LEVEL_TYPE
           )
 -- 按不同层级，补齐对应的ITEM维、供应商维等，前项补齐年均本
 WITH FORWARD_FILLER_TEMP AS
     (
      SELECT SS.L2_CEG_CODE,
             SS.L2_CEG_CN_NAME,
             SS.L3_CEG_CODE,
             SS.L3_CEG_CN_NAME,
             SS.L3_CEG_SHORT_CN_NAME,
             SS.L4_CEG_CODE,
             SS.L4_CEG_CN_NAME,
             SS.L4_CEG_SHORT_CN_NAME,
             SS.CATEGORY_CODE,
             SS.CATEGORY_CN_NAME,
             SS.ITEM_CODE,
             SS.ITEM_CN_NAME,
             SS.SUPPLIER_CODE,
             SS.SUPPLIER_CN_NAME,
             SS.PERIOD_YEAR,
             SS.AVG_AMT,
             FIRST_VALUE(SS.AVG_AMT)     OVER(PARTITION BY SS.GROUP_LEVEL, SS.LEVEL_TYPE, SS.L2_CEG_CODE, SS.L3_CEG_CODE, SS.L4_CEG_CODE, SS.CATEGORY_CODE, SS.ITEM_CODE, SS.SUPPLIER_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS AVG_AMT_2,   --新补齐的均价字段
             FIRST_VALUE(SS.PERIOD_YEAR) OVER(PARTITION BY SS.GROUP_LEVEL, SS.LEVEL_TYPE, SS.L2_CEG_CODE, SS.L3_CEG_CODE, SS.L4_CEG_CODE, SS.CATEGORY_CODE, SS.ITEM_CODE, SS.SUPPLIER_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS APPEND_YEAR, --新补齐的年份字段
             SS.AVG_AMT_FLAG,
             SS.APPEND_FLAG,
             SS.GROUP_LEVEL,
             SS.LEVEL_TYPE
        FROM ( SELECT S.L2_CEG_CODE,
                      S.L2_CEG_CN_NAME,
                      S.L3_CEG_CODE,
                      S.L3_CEG_CN_NAME,
                      S.L3_CEG_SHORT_CN_NAME,
                      S.L4_CEG_CODE,
                      S.L4_CEG_CN_NAME,
                      S.L4_CEG_SHORT_CN_NAME,
                      S.CATEGORY_CODE,
                      S.CATEGORY_CN_NAME,
                      S.ITEM_CODE,
                      S.ITEM_CN_NAME,
                      S.SUPPLIER_CODE,
                      S.SUPPLIER_CN_NAME,
                      S.PERIOD_YEAR,
                      S.AVG_AMT,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.GROUP_LEVEL, S.LEVEL_TYPE, S.L2_CEG_CODE, S.L3_CEG_CODE, S.L4_CEG_CODE, S.CATEGORY_CODE, S.ITEM_CODE, S.SUPPLIER_CODE ORDER BY S.PERIOD_YEAR) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      S.APPEND_FLAG,
                      S.GROUP_LEVEL,
                      S.LEVEL_TYPE
                 FROM AVG_APD_TMP S) SS)
                              
     SELECT S.PERIOD_YEAR,
            S.L2_CEG_CODE,
            S.L2_CEG_CN_NAME,
            S.L3_CEG_CODE,
            S.L3_CEG_CN_NAME,
            S.L3_CEG_SHORT_CN_NAME,
            S.L4_CEG_CODE,
            S.L4_CEG_CN_NAME,
            S.L4_CEG_SHORT_CN_NAME,
            S.CATEGORY_CODE,
            S.CATEGORY_CN_NAME,
            S.ITEM_CODE,
            S.ITEM_CN_NAME,
            S.SUPPLIER_CODE,
            S.SUPPLIER_CN_NAME,
            S.AVG_AMT_2 AS RMB_AVG_AMT,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           S.APPEND_FLAG,
           NULL AS '||V_COLUMN||',
           '||V_IN_CALIBER||'
           '||V_VERSION_ID||' AS VERSION_ID,
           CASE WHEN S.APPEND_FLAG= ''Y'' AND S.AVG_AMT_2 IS NOT NULL
                THEN S.APPEND_YEAR ELSE NULL END AS APPEND_YEAR,
           S.GROUP_LEVEL,
           S.LEVEL_TYPE
      FROM FORWARD_FILLER_TEMP S 
     WHERE S.LEVEL_TYPE = ''YTD'' OR (S.LEVEL_TYPE = ''YTD_PREDICT'' AND S.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-1)' ; --只保留(一般年今年的实际数)以及(去年今年的实际数+预测数)
               
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

 --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '补齐年均本数据，并插入版本号为：'||V_VERSION_ID||'的全量数据到'||V_TO_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                
   
   --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

