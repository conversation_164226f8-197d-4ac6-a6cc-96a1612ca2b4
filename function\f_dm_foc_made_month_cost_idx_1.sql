-- Name: f_dm_foc_made_month_cost_idx; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_month_cost_idx(f_industry_flag character varying, f_dimension_type character varying, f_same_flag character varying DEFAULT NULL::character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最近修改时间:2025年1月8日15点22分
修改人   ：唐钦 twx1139790
修改内容： 月度累计权重值换表取数

----同编码指数 202410版本
---来源表
指数中间表	DM_FOC_REPL_SAME_MID_MADE_MTD_INDEX_T
权重表		DM_FOC_MADE_ANNUAL_WEIGHT_T
--目标表
采购同编码指数表 DM_FOC_REPL_SAME_MADE_MTD_INDEX_T
SELECT F_DM_FOC_MADE_MONTH_COST_IDX('I','U','SAME','');

修改时间:2024年10月12日16点24分
修改人   ：黄心蕊 hwx1187045
修改内容： 202410版本 新增同编码指数计算 新增入参 F_SAME_FLAG 'SAME' 同编码, '' 非同编码
修改时间: 2024年6月18日15点47分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间： 2024年4月17日14点25分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023-10-24
创建人  ：黄心蕊 hwx1187045
修改时间：2023-12-18
修改人  ：黄心蕊 hwx1187045
修改内容：202401版本新增SPART层级
背景描述：月度分析-指数表数据初始化 
参数描述：参数一(F_ITEM_VERSION)：通用版本号
		  参数二(F_DIMENSION_TYPE)：维度入参，入参值为'U'为通用颗粒度，入参值为'U'则为盈利颗粒度
		  参数三(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
通用颗粒度
来源表: FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_IDX_T --指数中间表
        FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_WEIGHT_T  --权重表
目标表: FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_T  --指数表
盈利颗粒度
来源表: FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_IDX_T --指数中间表
        FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_WEIGHT_T  --权重表
目标表: FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_T  --指数表
量纲颗粒度
来源表: FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_IDX_T --指数中间表
        FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_WEIGHT_T  --权重表
目标表: FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_T  --指数表
		  
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_COST_IDX('U',''); --通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_COST_IDX('P',''); --盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_COST_IDX('D',''); --盈利颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                              VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_COST_IDX';
  V_VERSION                              BIGINT;
  V_STEP_NUM                             BIGINT := 0; --函数步骤号
  V_BASE_PERIOD_ID                       INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01');
  V_SQL                                  TEXT; --执行语句
  V_PROD_RND_TEAM_CODE                   TEXT; --字段重量级团队CODE
  V_PROD_RND_TEAM_CN_NAME                TEXT; --字段重量级团队NAME
  V_PROFITS_NAME                         TEXT; --字段盈利颗粒度
  V_LV0_PROD_RND_TEAM_CODE               TEXT; --字段LV0CODE
  V_LV0_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV0NAME
  V_LV1_PROD_RND_TEAM_CODE               TEXT; --字段LV1CODE 
  V_LV1_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV1NAME
  V_LV2_PROD_RND_TEAM_CODE               TEXT; --字段LV2CODE
  V_LV2_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV2NAME
  V_LV3_PROD_RND_TEAM_CODE               TEXT; --字段LV3CODE
  V_LV3_PROD_RD_TEAM_CN_NAME             TEXT; --字段LV3NAME
  V_L1_NAME                              TEXT; --盈利颗粒度L1字段
  V_L2_NAME                              TEXT; --盈利颗粒度L2字段
  V_INSERT_L1_NAME                       TEXT; --盈利颗粒度备份L1字段
  V_INSERT_L2_NAME                       TEXT; --盈利颗粒度备份L2字段
  V_SHIPPING_OBJECT_CODE             TEXT; --字段发货对象
  V_SHIPPING_OBJECT_CN_NAME             TEXT; 
  V_GROUP_CODE                           TEXT; --本层级CODE
  V_GROUP_NAME                           TEXT; --本层级CODE
  V_CHILD_LEVEL                          TEXT; --取数层级所在LEVEL
  V_SQL_LV0_PROD_RND_TEAM_CODE           TEXT; --取数字段LV0CODE
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV0NAME
  V_SQL_LV1_PROD_RND_TEAM_CODE           TEXT; --取数字段LV1CODE 
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV1NAME
  V_SQL_LV2_PROD_RND_TEAM_CODE           TEXT; --取数字段LV2CODE
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV2NAME
  V_SQL_LV3_PROD_RND_TEAM_CODE           TEXT; --取数字段LV3CODE
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME         TEXT; --取数字段LV3NAME
  V_SQL_L1_NAME                          TEXT; --取数盈利颗粒度L1字段
  V_SQL_L2_NAME                          TEXT; --取数盈利颗粒度L2字段
  V_SQL_SHIPPING_OBJECT_CODE         TEXT; --取数字段发货对象
  V_SQL_SHIPPING_OBJECT_CN_NAME         TEXT; 
  V_SQL_PROD_RND_TEAM_CODE               TEXT; --本层级重量级团队CODE逻辑
  V_SQL_PROD_RND_TEAM_CN_NAME            TEXT; --本层级重量级团队NAME逻辑
  V_SQL_PROFITS_NAME                     TEXT; --本层级盈利颗粒度取数逻辑
  V_JOIN_PROD_RND_TEAM_CODE              TEXT; --重量级团队关联条件
  V_JOIN_PROFITS_NAME                    TEXT; --盈利颗粒度关联条件
  V_JOIN_L1NAME                          TEXT; --盈利颗粒度L1NAME关联条件
  V_JOIN_L2NAME                          TEXT; --盈利颗粒度L2NAME关联条件
  V_SQL_PARENT_CODE                      TEXT; --上层级取数逻辑
  V_GROUP_LEVEL                          TEXT; --本层级所在LEVEL值
  V_LEVEL_NUM                            NUMERIC; --循环层级
  V_MID_TABLE                            VARCHAR(200); --中间表名称
  V_WEIGHT_TABLE                         VARCHAR(200); --权重取数表名称
  V_TARGET_TABLE                         VARCHAR(200); --结果表名称
  V_DIMENSION_TYPE                       VARCHAR(200) := F_DIMENSION_TYPE;
  V_INSERT_PROFITS_NAME                  VARCHAR(200); --查询盈利颗粒度字段
  V_SEQUENCE                             VARCHAR(200); --序列作为结果表主键
  V_MANUFACTURE_OBJECT_CODE             TEXT; --字段制造对象
  V_MANUFACTURE_OBJECT_CN_NAME             TEXT; 
  V_SQL_MANUFACTURE_OBJECT_CODE         TEXT; --取数字段制造对象
  V_SQL_MANUFACTURE_OBJECT_CN_NAME        TEXT; 
  V_DIMENSION_CODE                       TEXT; --字段量纲CODE
  V_DIMENSION_CN_NAME                    TEXT; --字段量纲NAME
  V_DIMENSION_SUBCATEGORY_CODE           TEXT; --字段量纲子类CODE
  V_DIMENSION_SUBCATEGORY_CN_NAME        TEXT; --字段量纲子类NAME
  V_DIMENSION_SUB_DETAIL_CODE            TEXT; --字段量纲子类明细CODE
  V_DIMENSION_SUB_DETAIL_CN_NAME         TEXT; --字段量纲子类明细NAME
  V_DMS_CODE                             TEXT; --字段量纲颗粒度CODE
  V_DMS_CN_NAME                          TEXT; --字段量纲颗粒度NAME
  V_SQL_DIMENSION_CODE                   TEXT; --取数字段量纲CODE
  V_SQL_DIMENSION_CN_NAME                TEXT; --取数字段量纲NAME
  V_SQL_DIMENSION_SUBCATEGORY_CODE       TEXT; --取数字段量纲子类CODE
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME    TEXT; --取数字段量纲子类NAME
  V_SQL_DIMENSION_SUB_DETAIL_CODE        TEXT; --取数字段量纲子类明细CODE
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME     TEXT; --取数字段量纲子类明细NAME
  V_SQL_DMS_CODE                         TEXT; --取数字段量纲颗粒度CODE
  V_SQL_DMS_CN_NAME                      TEXT; --取数字段量纲颗粒度NAME
  V_JOIN_DMS_CODE                        TEXT; --量纲颗粒度关联条件
  V_JOIN_DIMENSION_CODE                  TEXT; --量纲CODE关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE      TEXT; --量纲子类CODE关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE       TEXT; --量纲子类明细CODE关联条件
  V_INSERT_DIMENSION_CODE                TEXT; --备份插数字段量纲CODE
  V_INSERT_DIMENSION_CN_NAME             TEXT; --备份插数字段量纲NAME
  V_INSERT_DIMENSION_SUBCATEGORY_CODE    TEXT; --备份插数字段量纲子类CODE
  V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME TEXT; --备份插数字段量纲子类NAME
  V_INSERT_DIMENSION_SUB_DETAIL_CODE     TEXT; --备份插数字段量纲子类明细CODE
  V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME  TEXT; --备份插数字段量纲子类明细NAME
  V_INSERT_DMS_CODE                      TEXT; --备份插数量纲颗粒度CODE
  V_INSERT_DMS_CN_NAME                   TEXT; --备份插数量纲颗粒度NAME
  V_RESERVE_SQL                          TEXT; --反向视角筛选条件
  
  --202401版本新增SPART层级
  V_SPART_CODE                     	   TEXT; --量纲颗粒度
  V_SPART_CN_NAME                  	   TEXT;
  V_JOIN_SPART_CODE                	   TEXT; --量纲关联条件
  V_SQL_SPART_CODE                 	   TEXT; --量纲查询表字段
  V_SQL_SPART_CN_NAME				   TEXT; --量纲查询表字段
  V_INSERT_SPART_CODE                    TEXT;
  V_INSERT_SPART_CN_NAME                 TEXT;
  
    --202405版本 数字能源新增COA层级
  V_COA_PART                    	   TEXT; 
  V_JOIN_COA_CODE                	   TEXT; 
  V_SQL_COA_PART                 	   TEXT; 
  V_INSERT_COA_PART                    TEXT;
  
  --202407版本 IAS新增LV4层级
  V_LV4_PART        TEXT;
  V_JOIN_LV4_CODE   TEXT;
  V_SQL_LV4_PART    TEXT;
  V_INSERT_LV4_PART TEXT;
  
  --202410版本 新增同编码指数
  V_SAME_SQL             TEXT;
  V_NO_SAME_COLUMNS      TEXT;
  V_NO_SAME_SQL_COLUMNS  TEXT;
  V_NO_SAME_JOIN_COLUMNS TEXT;
  V_WEIGHT_VERSION 		INT;
  V_ANNL_VERSION 		INT;
  V_SCENARIO_FLAG		TEXT;
  V_SQL_SCENARIO_FLAG	TEXT;
  V_SQL_BASE_PERIOD_ID	TEXT;
  V_NOW_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT);     -- 取当前年份（若当月为1月时，即取去年年份）
  
BEGIN

  X_RESULT_STATUS:='1';
  
  --日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
 /* SELECT VERSION_ID INTO V_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;*/
   
	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑	
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
		 
	END IF ;
   
  --入参不为空，则以入参为版本号
  ELSE 
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
--年度版本号取值 
	SELECT VERSION_ID
	  INTO V_ANNL_VERSION
	  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
	 WHERE DEL_FLAG = 'N'
	   AND STATUS = 1
	   AND UPPER(DATA_TYPE) = 'CATEGORY'
	 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||V_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  /*字段值定义*/
  V_PROD_RND_TEAM_CODE       := 'PROD_RND_TEAM_CODE,';
  V_PROD_RND_TEAM_CN_NAME    := 'PROD_RND_TEAM_CN_NAME,';
  V_LV0_PROD_RND_TEAM_CODE   := 'LV0_PROD_RND_TEAM_CODE,';
  V_LV0_PROD_RD_TEAM_CN_NAME := 'LV0_PROD_RD_TEAM_CN_NAME,';
  V_LV1_PROD_RND_TEAM_CODE   := 'LV1_PROD_RND_TEAM_CODE,';
  V_LV1_PROD_RD_TEAM_CN_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
  V_LV2_PROD_RND_TEAM_CODE   := 'LV2_PROD_RND_TEAM_CODE,';
  V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
  V_LV3_PROD_RND_TEAM_CODE   := 'LV3_PROD_RND_TEAM_CODE,';
  V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
  V_SHIPPING_OBJECT_CODE 	:= 'SHIPPING_OBJECT_CODE, ';
  V_SHIPPING_OBJECT_CN_NAME := 'SHIPPING_OBJECT_CN_NAME,';
  V_MANUFACTURE_OBJECT_CODE := 'MANUFACTURE_OBJECT_CODE,'; 
  V_MANUFACTURE_OBJECT_CN_NAME := 'MANUFACTURE_OBJECT_CN_NAME,';
  V_GROUP_CODE               := 'MANUFACTURE_OBJECT_CODE AS PARENT_CODE,';
  V_GROUP_NAME               := 'MANUFACTURE_OBJECT_CN_NAME AS PARENT_NAME,';
  V_CHILD_LEVEL              := '''ITEM''';

  V_SQL_PROD_RND_TEAM_CODE       := 'T1.PROD_RND_TEAM_CODE,';
  V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PROD_RND_TEAM_CN_NAME,';
  V_SQL_LV0_PROD_RND_TEAM_CODE   := 'T1.LV0_PROD_RND_TEAM_CODE,';
  V_SQL_LV0_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV1_PROD_RND_TEAM_CODE   := 'T1.LV1_PROD_RND_TEAM_CODE,';
  V_SQL_LV1_PROD_RD_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV2_PROD_RND_TEAM_CODE   := 'T1.LV2_PROD_RND_TEAM_CODE,';
  V_SQL_LV2_PROD_RD_TEAM_CN_NAME := 'T1.LV2_PROD_RD_TEAM_CN_NAME,';
  V_SQL_LV3_PROD_RND_TEAM_CODE   := 'T1.LV3_PROD_RND_TEAM_CODE,';
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME := 'T1.LV3_PROD_RD_TEAM_CN_NAME,';
  V_SQL_SHIPPING_OBJECT_CODE 	:= 'T1.SHIPPING_OBJECT_CODE,';
  V_SQL_SHIPPING_OBJECT_CN_NAME := 'T1.SHIPPING_OBJECT_CN_NAME,';
  V_SQL_MANUFACTURE_OBJECT_CODE := 'T1.MANUFACTURE_OBJECT_CODE,'; 
  V_SQL_MANUFACTURE_OBJECT_CN_NAME := 'T1.MANUFACTURE_OBJECT_CN_NAME,';
  V_SQL_PARENT_CODE              := 'T1.SHIPPING_OBJECT_CODE AS PARENT_CODE,T1.SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,'; 
  V_GROUP_LEVEL                  := '''MANUFACTURE_OBJECT''';

  /*条件定义*/
  V_JOIN_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE 
								AND T1.SHIPPING_OBJECT_CODE = T2.SHIPPING_OBJECT_CODE 
								AND T1.MANUFACTURE_OBJECT_CODE = T2.MANUFACTURE_OBJECT_CODE ';
  V_RESERVE_SQL :='';
  
  --202410版本 同编码指数不含 OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME 字段
  V_NO_SAME_COLUMNS         := ' OVERSEA_FLAG , LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,';
  V_NO_SAME_SQL_COLUMNS     := ' T1.OVERSEA_FLAG , T1.LV0_PROD_LIST_CODE,T1.LV0_PROD_LIST_CN_NAME, ';
  V_NO_SAME_JOIN_COLUMNS    := ' AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE ';
  V_WEIGHT_VERSION			:= V_VERSION;
  V_SCENARIO_FLAG			:= 'SCENARIO_FLAG ,';
  V_SQL_SCENARIO_FLAG		:= 'T1.SCENARIO_FLAG,';
  V_SQL_BASE_PERIOD_ID		:= 'BASE_PERIOD_ID,';

  IF V_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
	-- V_SEQUENCE     := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_S.NEXTVAL';
	V_RESERVE_SQL :=' AND VIEW_FLAG <> ''4'' ';
    V_LEVEL_NUM    := 7; --202407版本 IAS新增LV4层级 循环新增由LV4卷积至LV3层级 
	IF F_INDUSTRY_FLAG = 'I' THEN
	  IF F_SAME_FLAG = 'SAME' THEN
	  --202410版本 新增同编码指数
	    V_MID_TABLE    	:= 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MID_MADE_MTD_INDEX_T'; 
	    V_WEIGHT_TABLE 	:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_WEIGHT_T';
	    V_TARGET_TABLE 	:= 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_MTD_INDEX_T';
		
	  --202410版本 同编码指数不含 OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME 字段
	    V_SAME_SQL				:= ' AND OVERSEA_FLAG = ''G'' AND LV0_PROD_LIST_CODE = ''GR'' AND PERIOD_YEAR = '||V_NOW_YEAR||' ';	--202410版本 同编码取全球,集团数据
	    V_WEIGHT_VERSION		:= V_ANNL_VERSION;		--仅同编码指数权重取年度分年权重,且为全量ITEM金额卷积所得
		V_NO_SAME_COLUMNS		:= '';
	    V_NO_SAME_SQL_COLUMNS	:= '';
	    V_NO_SAME_JOIN_COLUMNS	:= '';  
		V_SCENARIO_FLAG			:= '';
		V_SQL_SCENARIO_FLAG		:= '';
		V_SQL_BASE_PERIOD_ID	:= '';
	  
	  ELSIF F_SAME_FLAG IS NULL THEN 
	    V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_IDX_T'; --中间表
	    V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_WEIGHT_T';
	    V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_T';
	  END IF;
	ELSIF F_INDUSTRY_FLAG = 'E' THEN --202405版本 新增数字能源部分
	  V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_IDX_T'; --中间表
	  V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MONTH_WEIGHT_T';
	  V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MONTH_COST_IDX_T';
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN --202407版本 新增IAS数据
	  V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_IDX_T'; --中间表
	  V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MONTH_WEIGHT_T';
	  V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MONTH_COST_IDX_T';
		
      V_LV4_PART        := 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,'; --202407版本 IAS新增LV4层级
      V_SQL_LV4_PART    := 'T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
		
	END IF ;
  
  ELSIF V_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
	/*表定义*/
    --V_SEQUENCE                     := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_S.NEXTVAL';
	
	IF F_INDUSTRY_FLAG = 'I' THEN
		V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_IDX_T '; --中间表
		V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_WEIGHT_T ';
		V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_T ';
	ELSIF F_INDUSTRY_FLAG = 'E' THEN --202405版本 新增数字能源部分
		V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MID_MONTH_IDX_T '; --中间表
		V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MONTH_WEIGHT_T ';
		V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MONTH_COST_IDX_T ';
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN --202407版本 新增IAS数据
		V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MID_MONTH_IDX_T '; --中间表
		V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MONTH_WEIGHT_T ';
		V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MONTH_COST_IDX_T ';
	END IF ;
	
    V_LEVEL_NUM                    := 7; --LOOP次数
	
	/*字段值定义*/
    V_L1_NAME                      := 'L1_NAME,';
    V_L2_NAME                      := 'L2_NAME,';
    V_PROFITS_NAME                 := 'PROFITS_NAME,';
	
    V_SQL_L1_NAME                  := 'T1.L1_NAME,';
    V_SQL_L2_NAME                  := 'T1.L2_NAME,';
    V_SQL_PROFITS_NAME             := 'T1.PROFITS_NAME,';
    V_LV3_PROD_RND_TEAM_CODE       := '';
    V_LV3_PROD_RD_TEAM_CN_NAME     := '';
    V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
	
	/*条件定义*/
    V_JOIN_PROFITS_NAME            := ' AND NVL(T1.PROFITS_NAME,2)= NVL(T2.PROFITS_NAME,2)';
    V_JOIN_L1NAME                  := ' AND NVL(T1.L1_NAME,3)= NVL(T2.L1_NAME,3)';
    V_JOIN_L2NAME                  := ' AND NVL(T1.L2_NAME,4)= NVL(T2.L2_NAME,4)';
  
  ELSIF V_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
	/*表定义*/
    --V_SEQUENCE                          := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_S.NEXTVAL';
	IF F_INDUSTRY_FLAG = 'I' THEN
		V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_IDX_T '; --中间表
		V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_WEIGHT_T ';
		V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_T ';
	ELSIF F_INDUSTRY_FLAG = 'E' THEN --202405版本 新增数字能源部分
		V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MID_MONTH_IDX_T '; --中间表
		V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MONTH_WEIGHT_T ';
		V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MONTH_COST_IDX_T ';
		
		V_COA_PART			:= 'COA_CODE,COA_CN_NAME,'; 
		V_SQL_COA_PART		:= 'T1.COA_CODE,T1.COA_CN_NAME,'; 
		V_JOIN_COA_CODE		:= 'AND NVL(T1.COA_CODE,''D5'') = NVL(T2.COA_CODE,''D5'') ';
		V_INSERT_COA_PART	:= V_COA_PART	; 
		
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN --202407版本 新增IAS数据
		V_MID_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MID_MONTH_IDX_T '; --中间表
		V_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MONTH_WEIGHT_T ';
		V_TARGET_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MONTH_COST_IDX_T ';
		
		V_LV4_PART        := 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,'; --202407版本 IAS新增LV4层级
		V_SQL_LV4_PART    := 'T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
		
	END IF ;

    V_LEVEL_NUM                         := 11; --LOOP次数 202401版本新增SPART层级
												--202405版本 数字能源新增COA层级,循环新增由COA卷积至LV3层级计算
	
	/*字段值定义*/
    V_DIMENSION_CODE                    := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME                 := 'DIMENSION_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE        := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME     := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE         := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME      := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DMS_CODE                          := 'DMS_CODE,';
    V_DMS_CN_NAME                       := 'DMS_CN_NAME,';
	--202401版本新增SPART层级
	V_SPART_CODE	:='SPART_CODE,'; 
	V_SPART_CN_NAME :='SPART_CN_NAME,';
	
	
    V_SQL_DIMENSION_CODE                := 'T1.DIMENSION_CODE,';
    V_SQL_DIMENSION_CN_NAME             := 'T1.DIMENSION_CN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T1.DIMENSION_SUBCATEGORY_CODE,';
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T1.DIMENSION_SUB_DETAIL_CODE,';
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_SQL_DMS_CODE                      := 'T1.DMS_CODE,';
    V_SQL_DMS_CN_NAME                   := 'T1.DMS_CN_NAME,';
	--202401版本新增SPART层级
	V_SQL_SPART_CODE	:='T1.SPART_CODE,'; 
	V_SQL_SPART_CN_NAME :='T1.SPART_CN_NAME,';
	
    /*条件定义*/
    V_JOIN_DIMENSION_CODE             	:= ' AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'') ';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE 	:= ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'') ';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE  	:= ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'') ';
    V_JOIN_DMS_CODE                   	:= ' AND NVL(T1.DMS_CODE,''DD'') = NVL(T2.DMS_CODE,''DD'') ';
	--202401版本新增SPART层级
	V_JOIN_SPART_CODE :='AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') ';
	
  END IF;
  
  --备份插数字段
  V_INSERT_PROFITS_NAME                  := V_PROFITS_NAME;
  V_INSERT_L1_NAME                       := V_L1_NAME;
  V_INSERT_L2_NAME                       := V_L2_NAME;
  V_INSERT_DIMENSION_CODE                := V_DIMENSION_CODE;
  V_INSERT_DIMENSION_CN_NAME             := V_DIMENSION_CN_NAME;
  V_INSERT_DIMENSION_SUBCATEGORY_CODE    := V_DIMENSION_SUBCATEGORY_CODE;
  V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME := V_DIMENSION_SUBCATEGORY_CN_NAME;
  V_INSERT_DIMENSION_SUB_DETAIL_CODE     := V_DIMENSION_SUB_DETAIL_CODE;
  V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME  := V_DIMENSION_SUB_DETAIL_CN_NAME;
  V_INSERT_DMS_CODE                      := V_DMS_CODE;
  V_INSERT_DMS_CN_NAME                   := V_DMS_CN_NAME;
  --202401版本新增SPART层级
  V_INSERT_SPART_CODE	 :=V_SPART_CODE	; 
  V_INSERT_SPART_CN_NAME :=V_SPART_CN_NAME;
  
			
  FOR LEVEL_FLAG IN 1 .. V_LEVEL_NUM LOOP

    IF LEVEL_FLAG = 1 THEN
      NULL;
      
    ELSIF LEVEL_FLAG = 2 THEN
	--卷积发货对象
      V_MANUFACTURE_OBJECT_CODE := ''; 
      V_MANUFACTURE_OBJECT_CN_NAME := '';
	  V_SQL_MANUFACTURE_OBJECT_CODE := ''; 
	  V_SQL_MANUFACTURE_OBJECT_CN_NAME := '';
	  V_JOIN_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE 
								AND T1.SHIPPING_OBJECT_CODE = T2.SHIPPING_OBJECT_CODE  ';
   
      V_GROUP_CODE                   := 'SHIPPING_OBJECT_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'SHIPPING_OBJECT_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''MANUFACTURE_OBJECT''';
      V_GROUP_LEVEL                  :='''SHIPPING_OBJECT''';
	  
      IF V_DIMENSION_TYPE = 'U' THEN
        V_SQL_PARENT_CODE := 'T1.PROD_RND_TEAM_CODE AS PARENT_CODE , T1.PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,'; --通用颗粒，上层级直达重量级团队
      ELSIF V_DIMENSION_TYPE = 'P' THEN
        V_SQL_PARENT_CODE := '
         CASE
           WHEN T1.VIEW_FLAG IN (0, 1, 2) THEN T1.PROD_RND_TEAM_CODE
           ELSE T1.PROFITS_NAME
         END AS PARENT_CODE,
         CASE
           WHEN T1.VIEW_FLAG IN (0, 1, 2) THEN T1.PROD_RND_TEAM_CN_NAME
           ELSE T1.PROFITS_NAME
         END AS PARENT_CN_NAME,
      '; --盈利颗粒，012视角为重量级团队，34视角为L1L2
	  ELSIF V_DIMENSION_TYPE = 'D' THEN --量纲颗粒度专家团上层级为量纲
	   V_SQL_PARENT_CODE := 'T1.DMS_CODE AS PARENT_CODE , T1.DMS_CN_NAME AS PARENT_CN_NAME,';
      END IF;
	  
    ELSIF LEVEL_FLAG = 3 AND V_DIMENSION_TYPE = 'U' THEN
      --从发货对象层级之上开始分情况处理
	  V_SHIPPING_OBJECT_CODE 	:= '';
	  V_SHIPPING_OBJECT_CN_NAME := '';
	  V_SQL_SHIPPING_OBJECT_CODE :='';
	  V_SQL_SHIPPING_OBJECT_CN_NAME :='';
      --V_LV3_PROD_RND_TEAM_CODE       :='';
      --V_LV3_PROD_RD_TEAM_CN_NAME     :='';
      --V_SQL_LV3_PROD_RND_TEAM_CODE   :='';
      --V_SQL_LV3_PROD_RD_TEAM_CN_NAME :='';
	  
	  V_LV4_PART			:= '';		--202407版本 IAS新增LV4层级
	  V_SQL_LV4_PART		:= '';
	  
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,'; --从重量级团队维中字段重量级团队都为其本身
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_JOIN_PROD_RND_TEAM_CODE      := '';
      V_GROUP_CODE                   := 'PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'PROD_RND_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''SHIPPING_OBJECT''';
      V_GROUP_LEVEL                  := 
      'CASE T1.VIEW_FLAG 
            WHEN ''0'' THEN ''LV0''		--202405版本 ICT正式修改为LV0
            WHEN ''1'' THEN ''LV1''
            WHEN ''2'' THEN ''LV2''
            WHEN ''3'' THEN ''LV3''
			WHEN ''7'' THEN ''LV4''		--202407版本 IAS新增LV4层级 IAS新增视角7
        END';
        
      V_SQL_PARENT_CODE              := '
       CASE T1.VIEW_FLAG
            WHEN ''0'' THEN ''''
            WHEN ''1'' THEN T1.LV0_PROD_RND_TEAM_CODE
            WHEN ''2'' THEN T1.LV1_PROD_RND_TEAM_CODE
            WHEN ''3'' THEN T1.LV2_PROD_RND_TEAM_CODE
			WHEN ''7'' THEN T1.LV3_PROD_RND_TEAM_CODE	--202407版本 IAS新增LV4层级 IAS新增视角7
        END AS PARENT_CODE,
       CASE T1.VIEW_FLAG
            WHEN ''0'' THEN ''''
            WHEN ''1'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
            WHEN ''2'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
            WHEN ''3'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
			WHEN ''7'' THEN T1.LV3_PROD_RD_TEAM_CN_NAME	--202407版本 IAS新增LV4层级 IAS新增视角7
        END AS PARENT_CN_NAME,';
		
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'U' THEN
	--卷积LV3
      V_LV3_PROD_RND_TEAM_CODE       :='';
      V_LV3_PROD_RD_TEAM_CN_NAME     :='';
      V_SQL_LV3_PROD_RND_TEAM_CODE   :='';
      V_SQL_LV3_PROD_RD_TEAM_CN_NAME :='';
      V_GROUP_CODE                   := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''LV4''';
      V_GROUP_LEVEL                  :='''LV3''';
      V_SQL_PARENT_CODE              := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
      
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'U' THEN
	--卷积LV2
      V_LV2_PROD_RND_TEAM_CODE       :='';
      V_LV2_PROD_RD_TEAM_CN_NAME     :='';
      V_SQL_LV2_PROD_RND_TEAM_CODE   :='';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME :='';
      V_GROUP_CODE                   := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''LV3''';
      V_GROUP_LEVEL                  :='''LV2''';
      V_SQL_PARENT_CODE              := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'U' THEN
	--卷积LV1
      V_LV1_PROD_RND_TEAM_CODE       :='';
      V_LV1_PROD_RD_TEAM_CN_NAME     :='';
      V_SQL_LV1_PROD_RND_TEAM_CODE   :='';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME :='';
      V_GROUP_CODE                   := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  :='''LV2''';
      V_GROUP_LEVEL                  :='''LV1''';
      V_SQL_PARENT_CODE              := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'U' THEN
	--卷积LV0
      V_GROUP_CODE      := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,';
      V_GROUP_NAME      := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL     :='''LV1''';
      V_GROUP_LEVEL     :='''LV0''';		--202405版本 ICT正式修改为LV0
      V_SQL_PARENT_CODE :=' '''' AS PARENT_CODE, '''' AS PARENT_CN_NAME,';
	
      /*盈利颗粒度卷积*/
    ELSIF LEVEL_FLAG = 3 AND V_DIMENSION_TYPE = 'P' THEN
	  V_SHIPPING_OBJECT_CODE 	:= '';
	  V_SHIPPING_OBJECT_CN_NAME := '';
	  V_SQL_SHIPPING_OBJECT_CODE :='';
	  V_SQL_SHIPPING_OBJECT_CN_NAME :='';
	  V_JOIN_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE  ';
	  
      V_GROUP_CODE      := 'PARENT_CODE,';
      V_GROUP_NAME      := '
       CASE
         WHEN VIEW_FLAG IN (0, 1, 2) THEN
          PROD_RND_TEAM_CN_NAME
         ELSE
          PROFITS_NAME
       END AS PARENT_NAME,';
      V_CHILD_LEVEL     := '''SHIPPING_OBJECT''';
      V_GROUP_LEVEL     := '
      CASE T1.VIEW_FLAG 
           WHEN ''0'' THEN ''LV0''	--202405版本 ICT正式修改为LV0
           WHEN ''1'' THEN ''LV1''
           WHEN ''2'' THEN ''LV2''
           WHEN ''3'' THEN ''L1''
           WHEN ''4'' THEN ''L2''
       END';
      V_SQL_PARENT_CODE := '
      CASE T1.VIEW_FLAG
           WHEN ''0'' THEN ''''
           WHEN ''1'' THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN ''2'' THEN T1.LV1_PROD_RND_TEAM_CODE
           WHEN ''3'' THEN T1.LV2_PROD_RND_TEAM_CODE
           WHEN ''4'' THEN T1.L1_NAME
      END AS PARENT_CODE,
      CASE T1.VIEW_FLAG
           WHEN ''0'' THEN ''''
           WHEN ''1'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
           WHEN ''2'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
           WHEN ''3'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
           WHEN ''4'' THEN T1.L1_NAME
      END AS PARENT_CN_NAME,';
    
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积L1
      V_L2_NAME          := '';
      V_SQL_L2_NAME      := '';
      V_JOIN_L2NAME      := '';
      V_CHILD_LEVEL      := '''L2''';
      V_GROUP_CODE       := 'PARENT_CODE,';
      V_GROUP_NAME       := 'L1_NAME AS PARENT_NAME,';
      V_SQL_PROFITS_NAME := 'T1.PARENT_CODE,';
      V_GROUP_LEVEL      := '''L1''';
      V_SQL_PARENT_CODE  := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME, ';
    
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积LV2
      V_L1_NAME                      := '';
      V_SQL_L1_NAME                  := '';
      V_JOIN_L1NAME                  := '';
      V_LV2_PROD_RND_TEAM_CODE       := '';
      V_LV2_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV2_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME := '';
	  
      V_PROFITS_NAME                 := '';
      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''L1''';
      V_SQL_PROFITS_NAME             := '';
      V_GROUP_LEVEL                  := '''LV2''';
      V_SQL_PARENT_CODE              := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_JOIN_PROFITS_NAME            := '';
    
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积LV1
      V_LV1_PROD_RND_TEAM_CODE       := '';
      V_LV1_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV1_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_PROFITS_NAME                 := '';
      V_CHILD_LEVEL                  := '''LV2''';
      V_SQL_PROFITS_NAME             := '';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,'; --直取取不到LV1CODE
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_GROUP_LEVEL                  := '''LV1''';
      V_SQL_PARENT_CODE              := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
    
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'P' THEN
      --卷积LV0
      V_LV0_PROD_RND_TEAM_CODE       := '';
      V_LV0_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV0_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV0_PROD_RD_TEAM_CN_NAME := '';
      V_GROUP_CODE                   := 'PARENT_CODE,';
      V_GROUP_NAME                   := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV1''';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,'; --直取取不到LV1CODE
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
      V_GROUP_LEVEL                  := '''LV0''';		--202405版本 ICT正式修改为LV0
      V_SQL_PARENT_CODE              := ''''' AS PARENT_CODE, '''' AS PARENT_CN_NAME,';
	  
      /*量纲颗粒度卷积*/
    ELSIF LEVEL_FLAG = 3 AND V_DIMENSION_TYPE = 'D' THEN
      --从专家团层级之上开始分情况处理
	  V_SHIPPING_OBJECT_CODE 	:= '';
	  V_SHIPPING_OBJECT_CN_NAME := '';
	  V_SQL_SHIPPING_OBJECT_CODE :='';
	  V_SQL_SHIPPING_OBJECT_CN_NAME :='';
	  V_JOIN_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE  ';
	  
      V_GROUP_CODE      := 'DMS_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME      := 'DMS_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL     := '''SHIPPING_OBJECT''';
      V_GROUP_LEVEL     := '
       CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN ''DIMENSION''
            WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN ''SUBCATEGORY''
			WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN ''SUB_DETAIL''
            ELSE ''SPART''
        END '; --不同视角专家团父层级值不同，需定义 202401版本新增SPART层级
      V_SQL_PARENT_CODE := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CODE
		   WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN T1.DIMENSION_SUBCATEGORY_CODE
           ELSE T1.DIMENSION_SUB_DETAIL_CODE
       END AS PARENT_CODE,
      CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CN_NAME
           WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CN_NAME
           WHEN T1.VIEW_FLAG IN (''2'',''5'',''8'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
		   ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
       END AS PARENT_CN_NAME,'; --不同视角量纲颗粒度CODE父层级不同，需定义 202401版本新增SPART层级
	   
	   
    ELSIF LEVEL_FLAG = 4 AND V_DIMENSION_TYPE = 'D' THEN
      --量纲子类明细卷积 202401版本新增SPART层级 上一次循环中已完成对SPART的卷积 
      V_SPART_CODE        := '';
      V_SPART_CN_NAME     := '';
      V_SQL_SPART_CODE    := '';
      V_SQL_SPART_CN_NAME := '';
      V_JOIN_SPART_CODE   := '';
	  
      V_GROUP_CODE                       := 'DIMENSION_SUB_DETAIL_CODE    AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                       := 'DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                      := '''SPART''';
      V_GROUP_LEVEL                      := '''SUB_DETAIL''';
      V_SQL_PARENT_CODE                  := ' T1.DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE , T1.DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';
	  V_SQL_DMS_CODE                      := 'T1.DIMENSION_SUB_DETAIL_CODE,';
	  V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
	   
    
    ELSIF LEVEL_FLAG = 5 AND V_DIMENSION_TYPE = 'D' THEN
      --量纲子类卷积
      V_DIMENSION_SUB_DETAIL_CODE        := '';
      V_DIMENSION_SUB_DETAIL_CN_NAME     := '';
      V_SQL_DIMENSION_SUB_DETAIL_CODE    := '';
      V_SQL_DIMENSION_SUB_DETAIL_CN_NAME := '';
      V_JOIN_DIMENSION_SUB_DETAIL_CODE   := '';
	  
      V_GROUP_CODE                       := 'DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                       := 'DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                      := '''SUB_DETAIL''';
      V_GROUP_LEVEL                      := '''SUBCATEGORY''';
      V_SQL_PARENT_CODE                  := ' T1.DIMENSION_CODE AS PARENT_CODE , T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
	  V_SQL_DMS_CODE                      := 'T1.DIMENSION_SUBCATEGORY_CODE,';
	  V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
	  
    
    ELSIF LEVEL_FLAG = 6 AND V_DIMENSION_TYPE = 'D' THEN
      --量纲卷积
      V_DIMENSION_SUBCATEGORY_CODE        := '';
      V_DIMENSION_SUBCATEGORY_CN_NAME     := '';
      V_SQL_DIMENSION_SUBCATEGORY_CODE    := '';
      V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := '';
      V_JOIN_DIMENSION_SUBCATEGORY_CODE   := '';
	  
      V_GROUP_CODE                        := 'DIMENSION_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                        := 'DIMENSION_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                       := '''SUBCATEGORY''';
      V_GROUP_LEVEL                       := '''DIMENSION'''; --不同视角专家团父层级值不同，需定义  
      --V_SQL_PARENT_CODE                   := 'T1.PROD_RND_TEAM_CODE AS PARENT_CODE , T1.PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,'; --不同视角量纲颗粒度CODE父层级不同，需定义
	  
	  IF F_INDUSTRY_FLAG = 'I' THEN 
		V_SQL_PARENT_CODE                   := 'T1.PROD_RND_TEAM_CODE AS PARENT_CODE,
												T1.PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,'; 
										--202403版本 新增PARENT_CN_NAME
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
		V_SQL_PARENT_CODE                   := '
										DECODE(T1.VIEW_FLAG,''12'',T1.COA_CODE,T1.PROD_RND_TEAM_CODE) AS PARENT_CODE,
										DECODE(T1.VIEW_FLAG,''12'',T1.COA_CN_NAME,T1.PROD_RND_TEAM_CN_NAME) AS PARENT_CN_NAME,
											';	
										--202405版本 数字能源下12视角量纲父级为COA,其他视角均为其上重量级团队
										
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
		V_SQL_PARENT_CODE                   := 'T1.PROD_RND_TEAM_CODE AS PARENT_CODE,
												T1.PROD_RND_TEAM_CN_NAME AS PARENT_CN_NAME,'; 
											
										--202407版本  IAS新增LV4层级,量纲父级为重量级团队
										
	  END IF;
	  
	  V_SQL_DMS_CODE                      := 'T1.DIMENSION_CODE,';
	  V_SQL_DMS_CN_NAME                   := 'T1.DIMENSION_CN_NAME,';
    
    ELSIF LEVEL_FLAG = 7 AND V_DIMENSION_TYPE = 'D' THEN
      --分视角卷积量纲父级-重量级团队
      V_DIMENSION_CODE               := '';
      V_DIMENSION_CN_NAME            := '';
      V_SQL_DIMENSION_CODE           := '';
      V_SQL_DIMENSION_CN_NAME        := '';
      V_JOIN_DIMENSION_CODE          := '';
	  
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	   V_SQL_DMS_CODE                 := '';
	   V_SQL_DMS_CN_NAME              := '';
	   V_DMS_CODE                     := '';
	   V_DMS_CN_NAME                  := '';
	   V_JOIN_DMS_CODE                := '';
	   V_LV3_PROD_RND_TEAM_CODE       := '';
	   V_LV3_PROD_RD_TEAM_CN_NAME     := '';
	   V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
	   V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';	   
	   V_SQL_PARENT_CODE              := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RND_TEAM_CODE
           ELSE T1.LV2_PROD_RND_TEAM_CODE			
      END AS PARENT_CODE,
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RD_TEAM_CN_NAME
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
           ELSE T1.LV2_PROD_RD_TEAM_CN_NAME
      END AS PARENT_CN_NAME,
	  ';  --202401版本新增9，10，11视角，对应重量级团队分为别，LV1，LV2，LV3
	  --202403版本 新增PARENT_CN_NAME
	  V_GROUP_LEVEL                  := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN ''LV1''
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
           ELSE ''LV3''
      END '; --不同视角量纲父级值不同，需定义  202401版本新增9，10，11视角，对应重量级团队分为别，LV1，LV2，LV3

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	   V_SQL_DMS_CODE                      := 'T1.COA_CODE,';
	   V_SQL_DMS_CN_NAME                   := 'T1.COA_CN_NAME,';
	   
      V_SQL_PARENT_CODE              := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RND_TEAM_CODE
		   WHEN T1.VIEW_FLAG = ''12'' THEN T1.LV3_PROD_RND_TEAM_CODE	--202405版本 数字能源新增COA层级 父级为LV3
           ELSE T1.LV2_PROD_RND_TEAM_CODE			
      END AS PARENT_CODE,
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RD_TEAM_CN_NAME
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
		   WHEN T1.VIEW_FLAG = ''12'' THEN T1.LV3_PROD_RD_TEAM_CN_NAME
           ELSE T1.LV2_PROD_RD_TEAM_CN_NAME
      END AS PARENT_CN_NAME,
	  ';  --202401版本新增9，10，11视角，对应重量级团队分为别，LV1，LV2，LV3
	  --202403版本 新增PARENT_CN_NAME
	  
	  V_GROUP_LEVEL                  := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN ''LV1''
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
		   WHEN T1.VIEW_FLAG = ''12'' THEN ''COA''			--202405版本 数字能源新增COA层级 新增12视角
           ELSE ''LV3''
      END '; --不同视角量纲父级值不同，需定义  202401版本新增9，10，11视角，对应重量级团队分为别，LV1，LV2，LV3

	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 	--202407版本 IAS新增LV4层级 增加LV4卷积至LV3
	   V_SQL_DMS_CODE                 := '';
	   V_SQL_DMS_CN_NAME              := '';
	   V_DMS_CODE                     := '';
	   V_DMS_CN_NAME                  := '';
	   V_JOIN_DMS_CODE                := '';
	   V_LV4_PART					  := '';
	   V_SQL_LV4_PART				  := '';
	   
      V_SQL_PARENT_CODE              := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RND_TEAM_CODE
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RND_TEAM_CODE
		   WHEN T1.VIEW_FLAG = ''12'' THEN T1.LV3_PROD_RND_TEAM_CODE	--202407版本 IAS新增视角12 IAS新增LV4层级 父级为LV3
           ELSE T1.LV2_PROD_RND_TEAM_CODE			
      END AS PARENT_CODE,
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T1.LV0_PROD_RD_TEAM_CN_NAME
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
		   WHEN T1.VIEW_FLAG = ''12'' THEN T1.LV3_PROD_RD_TEAM_CN_NAME	--202407版本 IAS新增视角12 IAS新增LV4层级 父级为LV3
           ELSE T1.LV2_PROD_RD_TEAM_CN_NAME
      END AS PARENT_CN_NAME,
	  ';  
	  
	  V_GROUP_LEVEL                  := '
      CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN ''LV1''
           WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
		   WHEN T1.VIEW_FLAG = ''12'' THEN ''LV4''			--202407版本 IAS新增视角12 IAS新增LV4层级
           ELSE ''LV3''
      END '; 
	   
	  END IF; 
	  
	  
     /* V_GROUP_CODE                   := 'PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'PROD_RND_TEAM_CN_NAME AS PARENT_NAME,';*/
	  
	  V_GROUP_CODE                   := 'PARENT_CODE ,'; --202405版本 数字能源12视角,量纲父级为COA,其他视角为重量级团队
	  V_GROUP_NAME                   := 'PARENT_CN_NAME AS PARENT_NAME,'; --前一层级已完成分情况定义
	  
      V_CHILD_LEVEL                  := '''DIMENSION''';
	  
    ELSIF LEVEL_FLAG = 8 AND V_DIMENSION_TYPE = 'D' THEN	 --202405版本 新增COA到LV3层级卷积
      --卷积LV3层级	仅有数字能源有COA层级数据,ICT跑空
	  V_SQL_DMS_CODE                 := '';
	  V_SQL_DMS_CN_NAME              := '';
	  V_DMS_CODE                     := '';
	  V_DMS_CN_NAME                  := '';
	  V_JOIN_DMS_CODE                := '';
	  V_LV3_PROD_RND_TEAM_CODE       := '';
	  V_LV3_PROD_RD_TEAM_CN_NAME     := '';
	  V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
	  V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --
      V_GROUP_NAME                   := 'LV3_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_GROUP_LEVEL                  := '''LV3''';
      V_SQL_PARENT_CODE              := ' T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
											T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,';
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
	  
	  IF F_INDUSTRY_FLAG = 'E' THEN 
	    V_COA_PART					 := '';
	    V_SQL_COA_PART				 := '';
	    V_JOIN_COA_CODE				 := '';

        V_CHILD_LEVEL                  := '''COA''';

	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 

	    V_CHILD_LEVEL                  := '''LV4''';
	  END IF ;

    
    ELSIF LEVEL_FLAG = 9 AND V_DIMENSION_TYPE = 'D' THEN
      --卷积LV2层级
      V_LV2_PROD_RND_TEAM_CODE       := '';
      V_LV2_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV2_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV2_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'LV2_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV3''';
      V_GROUP_LEVEL                  := '''LV2''';
      V_SQL_PARENT_CODE              := ' T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE , T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_PROD_RND_TEAM_CODE       := 'T1.PARENT_CODE,';
      V_SQL_PROD_RND_TEAM_CN_NAME    := 'T1.PARENT_NAME,';
    
    ELSIF LEVEL_FLAG = 10 AND V_DIMENSION_TYPE = 'D' THEN
      --卷积LV1层级
	  
      V_LV1_PROD_RND_TEAM_CODE       := '';
      V_LV1_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV1_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV1_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'LV1_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV2''';
      V_GROUP_LEVEL                  := '''LV1''';
      V_SQL_PARENT_CODE              := ' T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
    
    ELSIF LEVEL_FLAG = 11 AND V_DIMENSION_TYPE = 'D' THEN
      --卷积LV0层级
      V_LV0_PROD_RND_TEAM_CODE       := '';
      V_LV0_PROD_RD_TEAM_CN_NAME     := '';
      V_SQL_LV0_PROD_RND_TEAM_CODE   := '';
      V_SQL_LV0_PROD_RD_TEAM_CN_NAME := '';
	  
      V_GROUP_CODE                   := 'LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,'; --所有视角专家团父层级皆为量纲颗粒度CODE
      V_GROUP_NAME                   := 'LV0_PROD_RD_TEAM_CN_NAME AS PARENT_NAME,';
      V_CHILD_LEVEL                  := '''LV1''';
      V_GROUP_LEVEL                  := '''LV0'''; --202405版本 LV0的层级值统一由ICT更新为LV0
      V_SQL_PARENT_CODE              := ' '''' AS PARENT_CODE, '''' AS PARENT_CN_NAME, ';
    
    END IF;
    
  /*主逻辑定义*/
  V_SQL := '
  WITH BASE_INDEX AS
   (SELECT PERIOD_YEAR,
           PERIOD_ID,
           '||V_PROD_RND_TEAM_CODE ||V_PROD_RND_TEAM_CN_NAME 
			||V_PROFITS_NAME
			||V_DMS_CODE ||V_DMS_CN_NAME
			||V_LV0_PROD_RND_TEAM_CODE ||V_LV0_PROD_RD_TEAM_CN_NAME 
			||V_LV1_PROD_RND_TEAM_CODE ||V_LV1_PROD_RD_TEAM_CN_NAME
			||V_LV2_PROD_RND_TEAM_CODE ||V_LV2_PROD_RD_TEAM_CN_NAME
			||V_LV3_PROD_RND_TEAM_CODE ||V_LV3_PROD_RD_TEAM_CN_NAME
			||V_LV4_PART			--202407版本 IAS新增LV4层级
			||V_L1_NAME ||V_L2_NAME
			||V_COA_PART			--202405版本 数字能源新增COA层级
			||V_DIMENSION_CODE||V_DIMENSION_CN_NAME
			||V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_SUBCATEGORY_CN_NAME 
			||V_DIMENSION_SUB_DETAIL_CODE ||V_DIMENSION_SUB_DETAIL_CN_NAME 
			||V_SPART_CODE||V_SPART_CN_NAME
			||V_SHIPPING_OBJECT_CODE ||V_SHIPPING_OBJECT_CN_NAME
			||V_MANUFACTURE_OBJECT_CODE ||V_MANUFACTURE_OBJECT_CN_NAME
			||V_GROUP_CODE ||V_GROUP_NAME||'    --202401版本新增SPART层级
           GROUP_CODE,
           COST_INDEX,
           VIEW_FLAG,
           '||V_SCENARIO_FLAG||'
           CALIBER_FLAG,
		   /*OVERSEA_FLAG,  
		   LV0_PROD_LIST_CODE, 
		   LV0_PROD_LIST_CN_NAME,*/
		   '||V_NO_SAME_COLUMNS||'	--202410版本 新增同编码指数计算
		   GROUP_LEVEL
      FROM '||V_MID_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
       AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||V_RESERVE_SQL||'),
       
  LEV_WEIGHT AS
   (SELECT PROD_RND_TEAM_CODE, 
           '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
		    ||V_COA_PART			--202405版本 数字能源新增COA层级
		    ||V_DIMENSION_CODE ||V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_SUB_DETAIL_CODE
			||V_SPART_CODE
			||V_DMS_CODE ||V_DMS_CN_NAME
			||V_SHIPPING_OBJECT_CODE ||V_MANUFACTURE_OBJECT_CODE||'   --202401版本新增SPART层级
           GROUP_CODE, 
           WEIGHT_RATE,
           PARENT_CODE, 
           VIEW_FLAG,
           CALIBER_FLAG,
		   /*OVERSEA_FLAG,  
		   LV0_PROD_LIST_CODE, 
		   LV0_PROD_LIST_CN_NAME,*/
		   '||V_NO_SAME_COLUMNS||'	--202410版本 新增同编码指数计算
		   GROUP_LEVEL
      FROM '||V_WEIGHT_TABLE||'
     WHERE VERSION_ID = '||V_WEIGHT_VERSION||'
       AND UPPER(GROUP_LEVEL) = '||V_CHILD_LEVEL||V_RESERVE_SQL||V_SAME_SQL||' ) 
       
  INSERT INTO '||V_MID_TABLE||'
    (VERSION_ID,
     BASE_PERIOD_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_LV0_PROD_RND_TEAM_CODE||V_LV0_PROD_RD_TEAM_CN_NAME 
  	  ||V_LV1_PROD_RND_TEAM_CODE||V_LV1_PROD_RD_TEAM_CN_NAME
  	  ||V_LV2_PROD_RND_TEAM_CODE||V_LV2_PROD_RD_TEAM_CN_NAME
  	  ||V_LV3_PROD_RND_TEAM_CODE||V_LV3_PROD_RD_TEAM_CN_NAME
	  ||V_LV4_PART			--202407版本 IAS新增LV4层级
  	  ||V_L1_NAME||V_L2_NAME
	  ||V_COA_PART			--202405版本 数字能源新增COA层级
	  ||V_DIMENSION_CODE||V_DIMENSION_CN_NAME 
	  ||V_DIMENSION_SUBCATEGORY_CODE ||V_DIMENSION_SUBCATEGORY_CN_NAME 
	  ||V_DIMENSION_SUB_DETAIL_CODE ||V_DIMENSION_SUB_DETAIL_CN_NAME 
	  ||V_SPART_CODE||V_SPART_CN_NAME
  	  ||V_SHIPPING_OBJECT_CODE ||V_SHIPPING_OBJECT_CN_NAME
	  ||V_MANUFACTURE_OBJECT_CODE ||V_MANUFACTURE_OBJECT_CN_NAME||'  --202401版本新增SPART层级
     VIEW_FLAG, 
     PROD_RND_TEAM_CODE,
     PROD_RND_TEAM_CN_NAME,
     '||V_PROFITS_NAME||V_DMS_CODE ||V_DMS_CN_NAME||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
	 PARENT_CN_NAME,
     '||V_SCENARIO_FLAG||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
	 '||V_NO_SAME_COLUMNS||'	--202410版本 新增同编码指数计算
     CALIBER_FLAG
	 /*OVERSEA_FLAG,  
	 LV0_PROD_LIST_CODE, 
	 LV0_PROD_LIST_CN_NAME*/
	 )
  SELECT '||V_VERSION||' AS VERSION_ID,
         '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.PERIOD_ID,
         '||V_SQL_LV0_PROD_RND_TEAM_CODE
          ||V_SQL_LV0_PROD_RD_TEAM_CN_NAME
          ||V_SQL_LV1_PROD_RND_TEAM_CODE
          ||V_SQL_LV1_PROD_RD_TEAM_CN_NAME
          ||V_SQL_LV2_PROD_RND_TEAM_CODE
          ||V_SQL_LV2_PROD_RD_TEAM_CN_NAME
          ||V_SQL_LV3_PROD_RND_TEAM_CODE
          ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
		  ||V_SQL_LV4_PART			--202407版本 IAS新增LV4层级
          ||V_SQL_L1_NAME
          ||V_SQL_L2_NAME
		  ||V_SQL_COA_PART			--202405版本 数字能源新增COA层级
		  ||V_SQL_DIMENSION_CODE||V_SQL_DIMENSION_CN_NAME
		  ||V_SQL_DIMENSION_SUBCATEGORY_CODE ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME 
		  ||V_SQL_DIMENSION_SUB_DETAIL_CODE ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME 
		  ||V_SQL_SPART_CODE||V_SQL_SPART_CN_NAME
          ||V_SQL_SHIPPING_OBJECT_CODE
          ||V_SQL_SHIPPING_OBJECT_CN_NAME
		  ||V_SQL_MANUFACTURE_OBJECT_CODE
		  ||V_SQL_MANUFACTURE_OBJECT_CN_NAME||'  --202401版本新增SPART层级
         T1.VIEW_FLAG,
         '||V_SQL_PROD_RND_TEAM_CODE
          ||V_SQL_PROD_RND_TEAM_CN_NAME
          ||V_SQL_PROFITS_NAME
		  ||V_SQL_DMS_CODE ||V_SQL_DMS_CN_NAME||'
         T1.PARENT_CODE AS GROUP_CODE,
         T1.PARENT_NAME AS GROUP_CN_NAME,
         '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         '||V_SQL_PARENT_CODE||'
         '||V_SQL_SCENARIO_FLAG||'	--202410版本 新增同编码指数
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
		 '||V_NO_SAME_SQL_COLUMNS||'	--202410版本 新增同编码指数计算
         T1.CALIBER_FLAG
		 /*T1.OVERSEA_FLAG,  
		 T1.LV0_PROD_LIST_CODE, 
		 T1.LV0_PROD_LIST_CN_NAME*/
    FROM BASE_INDEX T1
    JOIN LEV_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
	 AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 '||V_NO_SAME_JOIN_COLUMNS||'	--202410版本 新增同编码指数计算
	 /*AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE*/
     '||V_JOIN_PROD_RND_TEAM_CODE|| V_JOIN_PROFITS_NAME|| V_JOIN_L1NAME|| V_JOIN_L2NAME
	  ||V_JOIN_DMS_CODE || V_JOIN_DIMENSION_CODE || V_JOIN_DIMENSION_SUBCATEGORY_CODE ||V_JOIN_DIMENSION_SUB_DETAIL_CODE ||V_JOIN_SPART_CODE
	  ||V_JOIN_COA_CODE|| ' --202405版本 数字能源新增COA层级
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG 		--202401版本新增SPART层级
   GROUP BY '||V_SQL_LV0_PROD_RND_TEAM_CODE
             ||V_SQL_LV0_PROD_RD_TEAM_CN_NAME
             ||V_SQL_LV1_PROD_RND_TEAM_CODE
             ||V_SQL_LV1_PROD_RD_TEAM_CN_NAME
             ||V_SQL_LV2_PROD_RND_TEAM_CODE
             ||V_SQL_LV2_PROD_RD_TEAM_CN_NAME
             ||V_SQL_LV3_PROD_RND_TEAM_CODE
             ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
			 ||V_SQL_LV4_PART			--202407版本 IAS新增LV4层级
             ||V_SQL_L1_NAME
             ||V_SQL_L2_NAME
			 ||V_SQL_COA_PART			--202405版本 数字能源新增COA层级
			 ||V_SQL_DIMENSION_CODE||V_SQL_DIMENSION_CN_NAME
			 ||V_SQL_DIMENSION_SUBCATEGORY_CODE ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
			 ||V_SQL_DIMENSION_SUB_DETAIL_CODE ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
			 ||V_SQL_SPART_CODE||V_SQL_SPART_CN_NAME
             ||V_SQL_SHIPPING_OBJECT_CODE
             ||V_SQL_SHIPPING_OBJECT_CN_NAME
			 ||V_SQL_MANUFACTURE_OBJECT_CODE
			 ||V_SQL_MANUFACTURE_OBJECT_CN_NAME
             ||V_SQL_PROD_RND_TEAM_CODE
             ||V_SQL_PROD_RND_TEAM_CN_NAME
             ||V_SQL_PROFITS_NAME||V_SQL_DMS_CODE ||V_SQL_DMS_CN_NAME||' --202401版本新增SPART层级
			 '||V_SQL_SCENARIO_FLAG||' --202410版本 新增同编码指数
			 T1.PERIOD_YEAR,
			 T1.PERIOD_ID,
			 T1.PARENT_NAME,
			 T1.PARENT_CODE,
			 T1.VIEW_FLAG,
			 T1.GROUP_LEVEL,
			 '||V_NO_SAME_SQL_COLUMNS||'	--202410版本 新增同编码指数计算
			 T1.CALIBER_FLAG
			 /*T1.OVERSEA_FLAG,  
			 T1.LV0_PROD_LIST_CODE, 
			 T1.LV0_PROD_LIST_CN_NAME*/
			 ;'; 
  EXECUTE IMMEDIATE V_SQL;
  
   --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '第'||LEVEL_FLAG||'次循环开始'||V_GROUP_LEVEL||'层级指数收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  
  
  END LOOP;


 

  V_SQL := 'DELETE FROM '||V_TARGET_TABLE||' WHERE VERSION_ID = '||V_VERSION||';';
  EXECUTE IMMEDIATE V_SQL;	
  
   --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数表同版本数据删除完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  

  V_SQL:='
  INSERT INTO '||V_TARGET_TABLE||'
    (--ID,
     VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_SQL_BASE_PERIOD_ID||'
     '||V_PROD_RND_TEAM_CODE||V_PROD_RND_TEAM_CN_NAME||
      V_INSERT_PROFITS_NAME||V_INSERT_L1_NAME||V_INSERT_L2_NAME||
	  V_INSERT_COA_PART||	--202405版本 数字能源新增COA层级
	  V_INSERT_DIMENSION_CODE|| V_INSERT_DIMENSION_CN_NAME||
	  V_INSERT_DIMENSION_SUBCATEGORY_CODE|| V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME||
	  V_INSERT_DIMENSION_SUB_DETAIL_CODE|| V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME||
	  V_INSERT_SPART_CODE||V_INSERT_SPART_CN_NAME||
	  V_INSERT_DMS_CODE|| V_INSERT_DMS_CN_NAME||'
	 SHIPPING_OBJECT_CODE,
	 SHIPPING_OBJECT_CN_NAME, 
	 MANUFACTURE_OBJECT_CODE, 
	 MANUFACTURE_OBJECT_CN_NAME, 
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
	 PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     --APPEND_FLAG,
     '||V_SCENARIO_FLAG||'
	 '||V_NO_SAME_COLUMNS||'	--202410版本 新增同编码指数计算
     CALIBER_FLAG
	 /*OVERSEA_FLAG,  
	 LV0_PROD_LIST_CODE, 
	 LV0_PROD_LIST_CN_NAME*/
	 )
  SELECT -- '||V_SEQUENCE||' AS ID,
         '||V_VERSION||' AS VERSION_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         '||V_SQL_BASE_PERIOD_ID||'
         '||V_PROD_RND_TEAM_CODE||V_PROD_RND_TEAM_CN_NAME||
		  V_INSERT_PROFITS_NAME||
		  V_INSERT_L1_NAME||
		  V_INSERT_L2_NAME||
		  V_INSERT_COA_PART||	--202405版本 数字能源新增COA层级
		  V_INSERT_DIMENSION_CODE|| V_INSERT_DIMENSION_CN_NAME||
		  V_INSERT_DIMENSION_SUBCATEGORY_CODE|| V_INSERT_DIMENSION_SUBCATEGORY_CN_NAME||
		  V_INSERT_DIMENSION_SUB_DETAIL_CODE|| V_INSERT_DIMENSION_SUB_DETAIL_CN_NAME||
		  V_INSERT_SPART_CODE||V_INSERT_SPART_CN_NAME||
		  V_INSERT_DMS_CODE|| V_INSERT_DMS_CN_NAME||'
		 SHIPPING_OBJECT_CODE,
		 SHIPPING_OBJECT_CN_NAME, 
		 MANUFACTURE_OBJECT_CODE, 
		 MANUFACTURE_OBJECT_CN_NAME, 
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         COST_INDEX,
         PARENT_CODE,
		 PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         VIEW_FLAG,
         --APPEND_FLAG,
         '||V_SCENARIO_FLAG||'
		 '||V_NO_SAME_COLUMNS||'	--202410版本 新增同编码指数计算
         CALIBER_FLAG
		 /*OVERSEA_FLAG,  
		 LV0_PROD_LIST_CODE, 
		 LV0_PROD_LIST_CN_NAME*/
    FROM '||V_MID_TABLE||';';
    
  EXECUTE IMMEDIATE V_SQL;	
  
 --写入日志
 V_STEP_NUM:=V_STEP_NUM+1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '指数表插数完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');  
    
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
 	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 



$$
/

