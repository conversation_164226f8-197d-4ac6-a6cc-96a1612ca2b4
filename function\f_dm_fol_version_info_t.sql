-- Name: f_dm_fol_version_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_version_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：版本函数的逻辑：主要用于“刷新价格表”、“刷新系统”补齐未刷新的数据；根据传入参数（p_refresh_type）判断需要补齐哪些表的版本ID数据；
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；2、刷新（页面的刷新价格表、刷新系统）：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_version_info_t()
变更记录：

*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_fol_version_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_fol_version_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_rote_version_code  varchar(30);
	v_price_version_code varchar(30);


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'版本信息表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  --  “1_刷新价格表”时调用所有函数，所以无需补齐数据
  -- 如果传入刷新类型是“2_刷新系统”，则需要补齐“1_刷新价格表”的数据
  if(p_refresh_type = '2_刷新系统') then
    -- 从版本信息表取数据上个版本ID
    select max(version_id) as max_version_id into v_max_version_id
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id < p_version_id
       and not exists(select distinct version_id
                        from fin_dm_opt_foi.dm_fol_version_info_t t2
                       where t1.version_id = t2.version_id
                         and t2.step in(2001,0,2)
                     )
    ;
    
    -- 物流价格指数年度趋势表需要取上个版本ID的数据入到目标表
    insert into fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t(
           version_id
         , year
         , base_year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , level_code
         , level_desc
         , container_type
         , currency
         , price
         , container_qty
         , avg_price
         , weight
         , price_index
         , year_miss_flag
         , apd_year
         , year_avg_miss_code
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select p_version_id as version_id
         , year
         , base_year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , level_code
         , level_desc
         , container_type
         , currency
         , price
         , container_qty
         , avg_price
         , weight
         , price_index
         , year_miss_flag
         , apd_year
         , year_avg_miss_code
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_foi.dm_fol_annual_price_index_trend_info_t
     where version_id = v_max_version_id
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '物流价格指数年度趋势表需要取上个版本ID的数据入到目标表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    
    -- “2_刷新系统”涉及的表需要取上个版本ID的数据入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id
         , version_code
         , step
         , source_en_name
         , source_cn_name
         , refresh_type
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
    select p_version_id as version_id
         , version_code
         , step
         , source_en_name
         , source_cn_name
         , '2_刷新系统' as refresh_type
         , '版本ID为：'||v_max_version_id||' 的数据，'||substr(remark,position('，' in remark)+1) as remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_route_info_sum_t'
       and instr(remark,'价格补录表')>0
     union all
    select p_version_id as version_id
         , version_code
         , step
         , source_en_name
         , source_cn_name
         , '2_刷新系统' as refresh_type
         , '版本ID为：'||v_max_version_id||' 的数据，'||substr(remark,position('，' in remark)+1) as remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_annual_price_index_trend_info_t'
       and instr(remark,'价格补录表')>0
     union all
    select p_version_id as version_id
         , version_code
         , step
         , source_en_name
         , source_cn_name
         , '2_刷新系统' as refresh_type
         , '版本ID为：'||v_max_version_id||' 的数据，'||substr(remark,position('，' in remark)+1) as remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
      from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_weight_info_t'
       and instr(remark,'价格补录表')>0
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '“2_刷新系统”涉及的表需要取上个版本ID的数据入到版本信息表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
  
  else
    
    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '未传参数，需要传入正确的参数，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
   
  end if;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

