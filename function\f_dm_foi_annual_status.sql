-- Name: f_dm_foi_annual_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_annual_status(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年2月21日17点48分
  创建人  ：唐钦
  背景描述：年度涨跌幅状态码表
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_foi_annual_status()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_ANNUAL_STATUS'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
    
-- 202307月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_FROM_TABLE VARCHAR(200);
  V_FROM1_TABLE VARCHAR(200);
  V_FROM2_TABLE VARCHAR(200);
  V_TO_TABLE VARCHAR(200);
  V_TMP_TABLE VARCHAR(200);
  V_TMP1_TABLE VARCHAR(200);
  V_LAST_YEAR_FLAG VARCHAR(200);
  V_YEAR_FLAG VARCHAR(200);
  V_YEAR_APPEND VARCHAR(200);
  V_SEQUENCE VARCHAR(200);
  V_COLUMN VARCHAR(50);
  V_IN_COLUMN VARCHAR(50);
  V_FOI_VARA VARCHAR(500);
  V_FOI_LEVEL VARCHAR(200);
  V_PARENT_NAME VARCHAR(50);
  V_IN_PARENT_NAME VARCHAR(200);
  V_IN_PARENT_CODE VARCHAR(200);
  V_REL_COLUMN VARCHAR(200);
  V_GROUP VARCHAR(300);
  V_PARTITION_DIM VARCHAR(300);
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(200);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_VERSION_TABLE VARCHAR(200);
  V_REL_CONDITION VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
    
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

-- 判断不同入参，对应不同变量、参数
IF F_CALIBER_FLAG = 'I' THEN  -- 采购价格指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ANNUAL_COST_T';  -- 年均本表
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_AMP_T';  -- 涨跌幅
    V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_WEIGHT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ANNUAL_LEV_STATUS_T';
    V_TMP_TABLE := 'FOI_ITEM_LACK_STATUS_TMP';  
    V_TMP1_TABLE := 'FOI_DIM_ITEM_STATUS_TMP';  
    V_COLUMN := 'CONTINUITY_TYPE';
    V_PARENT_NAME := '';
    V_IN_PARENT_NAME := '';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T';
ELSIF F_CALIBER_FLAG = 'E' THEN  -- 数字能源指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MID_ANNUAL_COST_T';  -- 年均本表
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_AMP_T';  -- 涨跌幅
    V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_WEIGHT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ANNUAL_LEV_STATUS_T';
    V_TMP_TABLE := 'ENERGY_ITEM_LACK_STATUS_TMP';  
    V_TMP1_TABLE := 'ENERGY_DIM_ITEM_STATUS_TMP';  
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_PARENT_NAME := 'PARENT_CN_NAME,';
    V_IN_PARENT_NAME := 'DECODE(T1.SUPPLIER_CODE, NULL, T1.CATEGORY_CN_NAME, T1.ITEM_CN_NAME) AS PARENT_CN_NAME,';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') THEN  -- IAS/华东采购
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MID_ANNUAL_COST_T';  -- 年均本表
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_LEV_ANNUAL_AMP_T';  -- 涨跌幅
    V_FROM2_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_LEV_ANNUAL_WEIGHT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ANNUAL_LEV_STATUS_T';
    V_TMP_TABLE := 'IAS_ECPQC_ITEM_LACK_STATUS_TMP';  
    V_TMP1_TABLE := 'IAS_ECPQC_DIM_ITEM_STATUS_TMP';  
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_PARENT_NAME := 'PARENT_CN_NAME,';
    V_IN_PARENT_NAME := 'DECODE(T1.SUPPLIER_CODE, NULL, T1.CATEGORY_CN_NAME, T1.ITEM_CN_NAME) AS PARENT_CN_NAME,';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := ' AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    V_REL_CONDITION := ' AND T1.CALIBER_FLAG = T2.CALIBER_FLAG ';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
END IF;
   
  --版本号赋值
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '   
         SELECT VERSION_ID
            FROM '||V_VERSION_TABLE||'
            WHERE
             DEL_FLAG = ''N''
             AND STATUS = 1
             AND UPPER(DATA_TYPE) = ''CATEGORY''
             AND UPPER(VERSION_TYPE) IN (''AUTO'',''FINAL'')
             ORDER BY LAST_UPDATE_DATE DESC
             LIMIT 1';
   EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF; 
   
  --1.删除年度分析状态码表数据:
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||V_SQL_CONDITION;
    
     --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 -- 创建ITEM层级缺失情况临时表
    V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP_TABLE ||';
    CREATE TEMPORARY TABLE ' || V_TMP_TABLE ||' (
        PERIOD_YEAR BIGINT,
        L2_CEG_CODE    VARCHAR(50),
        L2_CEG_CN_NAME    VARCHAR(200),
        L3_CEG_CODE    VARCHAR(50),
        L3_CEG_CN_NAME    VARCHAR(200),
        L3_CEG_SHORT_CN_NAME    VARCHAR(200),
        L4_CEG_CODE    VARCHAR(50),
        L4_CEG_CN_NAME    VARCHAR(200),
        L4_CEG_SHORT_CN_NAME    VARCHAR(200),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(200),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(2000),
        SUPPLIER_CODE VARCHAR(50),
        SUPPLIER_CN_NAME VARCHAR(200),
        '||V_COLUMN||' VARCHAR(50),
        LEVEL_TYPE VARCHAR(50),
        GROUP_LEVEL VARCHAR(50),
        LAST_THREE_YEAR_FLAG INT,
        LAST_THREE_APPEND_YEAR INT,
        LAST_TWO_YEAR_FLAG INT,
        LAST_TWO_APPEND_YEAR INT,
        LAST_YEAR_FLAG INT,
        LAST_APPEND_YEAR INT,
        CURRENT_YEAR_FLAG INT,
        CURRENT_APPEND_YEAR INT
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(CATEGORY_CODE,ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;
    
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => 'ITEM层级缺失情况临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS, 
     F_ERRBUF => 'SUCCESS');   
        
     -- 创建ITEM层级全维度状态码临时表
    V_SQL := '
    DROP TABLE IF EXISTS ' || V_TMP1_TABLE ||';
    CREATE TEMPORARY TABLE ' || V_TMP1_TABLE ||' (
        PERIOD_YEAR BIGINT,
        L2_CEG_CODE    VARCHAR(50),
        L2_CEG_CN_NAME    VARCHAR(200),
        L3_CEG_CODE    VARCHAR(50),
        L3_CEG_CN_NAME    VARCHAR(200),
        L3_CEG_SHORT_CN_NAME    VARCHAR(200),
        L4_CEG_CODE    VARCHAR(50),
        L4_CEG_CN_NAME    VARCHAR(200),
        L4_CEG_SHORT_CN_NAME    VARCHAR(200),
        CATEGORY_CODE CHARACTER VARYING(50),
        CATEGORY_CN_NAME CHARACTER VARYING(200),
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_CN_NAME CHARACTER VARYING(2000),
        SUPPLIER_CODE VARCHAR(50),
        SUPPLIER_CN_NAME VARCHAR(200),
        '||V_COLUMN||' VARCHAR(50),
        LEVEL_TYPE VARCHAR(50),
        GROUP_LEVEL VARCHAR(50),
        STATUS_CODE CHARACTER VARYING(50),
        APPEND_YEAR CHARACTER VARYING(50)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(CATEGORY_CODE,ITEM_CODE)';
    EXECUTE IMMEDIATE V_SQL;

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_MUM,
     F_CAL_LOG_DESC => 'ITEM层级全维度状态码临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS, 
     F_ERRBUF => 'SUCCESS');   
        
    -- 当入参=FOI时，走计算采购价格指数的逻辑，需要计算供应商和ITEM2个层级，否则只计算ITEM1个层级
    FOR BASE_LEVEL IN 0 .. 1 LOOP
    IF BASE_LEVEL = 0 THEN  
        V_FOI_VARA := ' AND GROUP_LEVEL = ''SUPPLIER''';
        V_FOI_LEVEL := 'SUPPLIER_CODE,
                        SUPPLIER_CN_NAME,';
    ELSIF BASE_LEVEL = 1 THEN  
        V_FOI_VARA := ' AND GROUP_LEVEL = ''ITEM''';
        V_FOI_LEVEL := '';
    ELSE NULL;
    END IF;    
    
    -- ITEM层级缺失数据情况插入临时表
    V_SQL := '
         INSERT INTO '||V_TMP_TABLE||'(            
                     L2_CEG_CODE,
                     L2_CEG_CN_NAME,
                     L3_CEG_CODE,
                     L3_CEG_CN_NAME,
                     L3_CEG_SHORT_CN_NAME,
                     L4_CEG_CODE,
                     L4_CEG_CN_NAME,
                     L4_CEG_SHORT_CN_NAME,
                     CATEGORY_CODE,
                     CATEGORY_CN_NAME,
                     ITEM_CODE,
                     ITEM_CN_NAME,
                     '||V_FOI_LEVEL||'
                     LEVEL_TYPE,
                     GROUP_LEVEL,
                     LAST_THREE_YEAR_FLAG,
                     LAST_THREE_APPEND_YEAR,
                     LAST_TWO_YEAR_FLAG,
                     LAST_TWO_APPEND_YEAR,
                     LAST_YEAR_FLAG,
                     LAST_APPEND_YEAR,
                     CURRENT_YEAR_FLAG,
                     CURRENT_APPEND_YEAR
                     )
           SELECT L2_CEG_CODE,
                  L2_CEG_CN_NAME,
                  L3_CEG_CODE,
                  L3_CEG_CN_NAME,
                  L3_CEG_SHORT_CN_NAME,
                  L4_CEG_CODE,
                  L4_CEG_CN_NAME,
                  L4_CEG_SHORT_CN_NAME,
                  CATEGORY_CODE,
                  CATEGORY_CN_NAME,
                  ITEM_CODE,
                  ITEM_CN_NAME,
                  '||V_FOI_LEVEL||'
                  LEVEL_TYPE,
                  GROUP_LEVEL,
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(APPEND_FLAG,''Y'',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
                  SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR
              FROM '||V_FROM_TABLE||'
              WHERE DEL_FLAG = ''N''
              '||V_FOI_VARA||'
              '||V_SQL_CONDITION||'
              GROUP BY '||V_FOI_LEVEL||'
                       LEVEL_TYPE,
                       GROUP_LEVEL,
                       L2_CEG_CODE,
                       L2_CEG_CN_NAME,
                       L3_CEG_CODE,
                       L3_CEG_CN_NAME,
                       L3_CEG_SHORT_CN_NAME,
                       L4_CEG_CODE,
                       L4_CEG_CN_NAME,
                       L4_CEG_SHORT_CN_NAME,
                       CATEGORY_CODE,
                       CATEGORY_CN_NAME,
                       ITEM_CODE,
                       ITEM_CN_NAME';
                 
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL; 
         
     --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => 'ITEM层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');          
    
     -- ITEM/SUPPLIER层级状态码逻辑
     -- 对ITEM/SUPPLIER层级的年份进行循环 
        FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP
        
        IF YEAR_FLAG = V_YEAR-2 THEN
            V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
            V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
            V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
        
        ELSIF YEAR_FLAG = V_YEAR-1 THEN
            V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
            V_YEAR_FLAG := 'LAST_YEAR_FLAG';
            V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
            
        ELSIF YEAR_FLAG = V_YEAR THEN
            V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
            V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
            V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
        ELSE NULL;
        END IF;
        
        V_SQL := '
        INSERT INTO '||V_TMP1_TABLE||' (
                     PERIOD_YEAR,
                     L2_CEG_CODE,
                     L2_CEG_CN_NAME,
                     L3_CEG_CODE,
                     L3_CEG_CN_NAME,
                     L3_CEG_SHORT_CN_NAME,
                     L4_CEG_CODE,
                     L4_CEG_CN_NAME,
                     L4_CEG_SHORT_CN_NAME,
                     CATEGORY_CODE,
                     CATEGORY_CN_NAME,
                     ITEM_CODE,
                     ITEM_CN_NAME,
                     '||V_FOI_LEVEL||'
                     LEVEL_TYPE,
                     GROUP_LEVEL,
                     STATUS_CODE,
                     APPEND_YEAR)
        SELECT '||YEAR_FLAG||' AS PERIOD_YEAR,
               L2_CEG_CODE,
               L2_CEG_CN_NAME,
               L3_CEG_CODE,
               L3_CEG_CN_NAME,
               L3_CEG_SHORT_CN_NAME,
               L4_CEG_CODE,
               L4_CEG_CN_NAME,
               L4_CEG_SHORT_CN_NAME,
               CATEGORY_CODE,
               CATEGORY_CN_NAME,
               ITEM_CODE,
               ITEM_CN_NAME,
               '||V_FOI_LEVEL||'
               LEVEL_TYPE,
               GROUP_LEVEL,
               CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
                    WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' = 0 THEN 4
                    WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' <> 0 THEN 5
               END AS STATUS_CODE,
               '||V_YEAR_APPEND||' AS APPEND_YEAR
            FROM '||V_TMP_TABLE||' T1
            WHERE 1 = 1
            '||V_FOI_VARA;
               
       DBMS_OUTPUT.PUT_LINE(V_SQL);  
       EXECUTE IMMEDIATE V_SQL;  
       
  --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => 'ITEM层级全维度缺失状态码插入'||V_TMP1_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');         
        END LOOP;        
    END LOOP;
        
-- 把多余的YTD+预测数据删除
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TMP1_TABLE||' 
                          WHERE PERIOD_YEAR BETWEEN '||V_YEAR-3||' AND '||V_YEAR-1||'
                          AND LEVEL_TYPE = ''YTD_PREDICT'''; 
                          
  --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '将'||V_TMP1_TABLE||'表内多余的YTD_PREDICT数据删掉',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
    -- 把ITEM层级状态码数据插入状态码表中
     V_SQL := '
        INSERT INTO '||V_TO_TABLE||' (
                     PERIOD_YEAR,
                     VERSION_ID,
                     GROUP_CODE,
                     GROUP_CN_NAME,
                     GROUP_LEVEL,
                     STATUS_CODE,
                     PARENT_CODE,
                     '||V_PARENT_NAME
                     ||V_COLUMN||',
                     LEVEL_TYPE,
                     '||V_CALIBER||'
                     CREATED_BY,
                     CREATION_DATE,
                     LAST_UPDATED_BY,
                     LAST_UPDATE_DATE,
                     DEL_FLAG,
                     APPEND_YEAR)
        SELECT 
               T1.PERIOD_YEAR,
               '||V_VERSION_ID||' AS VERSION_ID,
               DECODE(T1.SUPPLIER_CODE, NULL, T1.ITEM_CODE, T1.SUPPLIER_CODE) AS GROUP_CODE,
               DECODE(T1.SUPPLIER_CODE, NULL, T1.ITEM_CN_NAME, T1.SUPPLIER_CN_NAME) AS GROUP_CN_NAME,
               DECODE(T1.SUPPLIER_CODE, NULL, ''ITEM'', ''SUPPLIER'') AS GROUP_LEVEL,
               T1.STATUS_CODE,
               DECODE(T1.SUPPLIER_CODE, NULL, T1.CATEGORY_CODE, T1.ITEM_CODE) AS PARENT_CODE,
               '||V_IN_PARENT_NAME
               ||V_COLUMN||',
               T1.LEVEL_TYPE,
               '||V_IN_CALIBER||'
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,
               T1.APPEND_YEAR
            FROM '||V_TMP1_TABLE||' T1';
       EXECUTE IMMEDIATE V_SQL; 
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
            
        --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的ITEM层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
    
    -- 按1-4 ，从品类层级到ICT层级，分不同项目进行循环    
        FOR GRO_LEV IN 1 .. 4 LOOP
        V_FOI_VARA := ' WHERE GROUP_LEVEL = ''ITEM''';
        IF GRO_LEV = 1 THEN   -- 品类层级
                V_GROUP := 'T1.CATEGORY_CODE AS GROUP_CODE,
                            ''CATEGORY'' AS GROUP_LEVEL,';
                V_IN_PARENT_CODE := ' T1.L4_CEG_CODE AS PARENT_CODE,';    
                V_PARTITION_DIM := 'T1.CATEGORY_CODE,
                                    T1.L4_CEG_CODE,';
        ELSIF GRO_LEV = 2 THEN   -- 模块层级
                V_GROUP := 'T1.L4_CEG_CODE AS GROUP_CODE,
                           ''LV4'' AS GROUP_LEVEL,';
                V_IN_PARENT_CODE := ' T1.L3_CEG_CODE AS PARENT_CODE,'; 
                V_PARTITION_DIM := 'T1.L4_CEG_CODE,
                                    T1.L3_CEG_CODE,';
        ELSIF GRO_LEV = 3 THEN   -- 专家团层级
                V_GROUP := 'T1.L3_CEG_CODE AS GROUP_CODE,
                           ''LV3'' AS GROUP_LEVEL,';
                V_IN_PARENT_CODE := ' T1.L2_CEG_CODE AS PARENT_CODE,'; 
                V_PARTITION_DIM := 'T1.L3_CEG_CODE,
                                    T1.L2_CEG_CODE,';
        ELSIF GRO_LEV = 4 THEN   -- 通用：LV3层级/盈利:L2层级/量纲：SUB_DETAIL (量纲子类明细)层级/采购：LV2层级  
                V_GROUP := 'T1.L2_CEG_CODE AS GROUP_CODE,
                           ''LV2'' AS GROUP_LEVEL,';
                V_IN_PARENT_CODE := ' T1.L2_CEG_CODE AS PARENT_CODE,';    
                V_PARTITION_DIM := 'T1.L2_CEG_CODE,';
                V_FOI_VARA := ' 
                      LEFT JOIN (
                         SELECT DISTINCT L3_CEG_CODE, 
                                '||V_COLUMN||' 
                            FROM '||V_FROM2_TABLE||' 
                            WHERE GROUP_LEVEL = ''LV3''
                            AND VERSION_ID = '||V_VERSION_ID||'
                                         ) T2
                        ON T1.L3_CEG_CODE = T2.L3_CEG_CODE
                        WHERE GROUP_LEVEL = ''ITEM''
                       ';
            IF F_CALIBER_FLAG = 'I' THEN  -- 采购ICT
                V_IN_COLUMN := 'T2.CONTINUITY_TYPE,';
                V_REL_COLUMN := ' AND NVL(T1.CONTINUITY_TYPE,''SNULL1'') = NVL(T2.CONTINUITY_TYPE,''SNULL1'') ';
            ELSIF F_CALIBER_FLAG = 'E' THEN  -- 数字能源
                V_IN_COLUMN := 'T2.GROUP_PUR_FLAG,';
                V_REL_COLUMN := ' AND NVL(T1.GROUP_PUR_FLAG,''SNULL1'') = NVL(T2.GROUP_PUR_FLAG,''SNULL1'') ';
            END IF;
        END IF;    

  V_SQL := '
      INSERT INTO '||V_TO_TABLE||' (
               PERIOD_YEAR,
               VERSION_ID,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               STATUS_CODE,
               PARENT_CODE,
               '||V_PARENT_NAME
               ||V_COLUMN||',
               LEVEL_TYPE,
               '||V_CALIBER||'
               CREATED_BY,
               CREATION_DATE,
               LAST_UPDATED_BY,
               LAST_UPDATE_DATE,
               DEL_FLAG
               )
               
      WITH ITEM_STATUS_TMP AS(
           SELECT T1.PERIOD_YEAR,
                  '||V_GROUP    -- 取T1表中的上一层级CODE作为GROUP_CODE
                  ||V_IN_PARENT_CODE
                  ||V_IN_COLUMN||'
                  T1.LEVEL_TYPE,
                  SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
                  SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
                  SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4    -- 值=0，说明子级都为4，赋2
               FROM '||V_TMP1_TABLE||' T1
               '||V_FOI_VARA||'
               GROUP BY T1.LEVEL_TYPE,
               '||V_PARTITION_DIM
               ||V_IN_COLUMN||'
               T1.PERIOD_YEAR)
                                    
        SELECT 
               T1.PERIOD_YEAR,
               '||V_VERSION_ID||' AS VERSION_ID,
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
                    WHEN T2.STATUS_1 = 0 THEN 1
                    WHEN T2.STATUS_4 = 0 THEN 2
               ELSE 4 END AS STATUS_CODE,
               T1.PARENT_CODE,
               '||V_PARENT_NAME||'
               T1.'||V_COLUMN||',
               T1.LEVEL_TYPE,
               '||V_CALIBER||'
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG
            FROM '||V_FROM1_TABLE||' T1
            INNER JOIN ITEM_STATUS_TMP T2
            ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
            AND T1.GROUP_CODE = T2.GROUP_CODE
            AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
            AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
            AND NVL(T1.LEVEL_TYPE,''SNULL2'') = NVL(T2.LEVEL_TYPE,''SNULL2'')
            '||V_REL_COLUMN ||'
            WHERE T1.VERSION_ID = '||V_VERSION_ID ||'
            '||V_SQL_CONDITION;        
        
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL;
        
        --写入日志
   V_STEP_MUM := V_STEP_MUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_MUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的其余层级缺失状态码插入'||V_TO_TABLE||'表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');   
     END LOOP;
        
    -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
 
END$$
/

