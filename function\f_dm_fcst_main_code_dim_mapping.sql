-- Name: f_dm_fcst_main_code_dim_mapping; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_main_code_dim_mapping(f_cost_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
最后修改人: twx1139790
创建人：罗若文
背景描述：主力编码补齐PBI维度
参数描述：X_RESULT_STATUS ：是否成功
事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_TOP_SPART_INFO()
变更示例-202504:
关联条件由BG名称修改为BG编码关联
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_MAIN_CODE_DIM_MAPPING'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自序列:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_ID2 BIGINT;
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_FROM_TABLE VARCHAR(50); 
  V_TO_TABLE VARCHAR(50);
  V_JOIN_TABLE VARCHAR(50);
 
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,成本类型:'||F_COST_TYPE
   );

  
   --判断PBI维度和成本类型选择来源表，目标表
  IF F_COST_TYPE = 'PSP'  THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PROD_MAIN_CODE_DIM_T';
	 V_TO_TABLE := 'DM_FCST_ICT_PSP_PBI_MAIN_CODE_DIM_T'; 
	 V_JOIN_TABLE := 'DM_FCST_ICT_PSP_PROD_UNIT_T';
  ELSIF F_COST_TYPE = 'STD'  THEN 
	 V_FROM_TABLE := 'DM_FCST_ICT_PROD_MAIN_CODE_DIM_T';
	 V_TO_TABLE := 'DM_FCST_ICT_STD_PBI_MAIN_CODE_DIM_T'; 
	 V_JOIN_TABLE := 'DM_FCST_ICT_STD_PROD_UNIT_T';
	 
  ELSE 
	RETURN '入参有误';
  END IF;


  
  
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 0
        AND UPPER(DATA_TYPE) = 'MAIN_DIM'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
  
  
    --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID2
    FROM
       DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
  
  

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录主力编码版本号信息, 版本号='||V_VERSION_ID||',成本类型:'||F_COST_TYPE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
   --删除当前会计期版本的TOP规格品数据, 支持单月重刷
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VERSION_ID = '||V_VERSION_ID2||'';
  EXECUTE IMMEDIATE V_SQL ;
  
   --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除数据, 删除版本='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  

  V_SQL := 'INSERT INTO  '||V_TO_TABLE||'
   (
    VERSION_ID ,
	BG_CODE ,
	BG_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME ,
	LV1_PROD_RND_TEAM_CODE ,
	LV1_PROD_RD_TEAM_CN_NAME ,
	LV2_PROD_RND_TEAM_CODE ,
	LV2_PROD_RD_TEAM_CN_NAME ,
	LV3_PROD_RND_TEAM_CODE ,
	LV3_PROD_RD_TEAM_CN_NAME ,
	LV4_PROD_RND_TEAM_CODE ,
	LV4_PROD_RD_TEAM_CN_NAME ,
	LV1_INDUSTRY_CATG_CODE ,
	LV1_INDUSTRY_CATG_CN_NAME ,
	LV2_INDUSTRY_CATG_CODE ,
	LV2_INDUSTRY_CATG_CN_NAME ,
	LV3_INDUSTRY_CATG_CODE ,
	LV3_INDUSTRY_CATG_CN_NAME ,
	LV4_INDUSTRY_CATG_CODE ,
	LV4_INDUSTRY_CATG_CN_NAME ,
	PRODUCT_CODE ,
	SPART_CODE ,
	SPART_CN_NAME ,
	CODE_ATTRIBUTES ,
	CREATED_BY ,
	CREATION_DATE ,
	LAST_UPDATED_BY ,
	LAST_UPDATE_DATE ,
	DEL_FLAG 
	)
	SELECT 
	DISTINCT '||V_VERSION_ID2||',
	T2.BG_CODE,
	T1.BG_CN_NAME,
    T2.LV1_PROD_LIST_CODE ,
	T1.LV1_PROD_LIST_CN_NAME ,
	T2.LV2_PROD_LIST_CODE ,
	T2.LV2_PROD_LIST_CN_NAME ,
	T2.LV3_PROD_LIST_CODE ,
	T2.LV3_PROD_LIST_CN_NAME ,
	T2.LV4_PROD_LIST_CODE ,
	T2.LV4_PROD_LIST_CN_NAME ,
	T2.LV1_PROD_RND_TEAM_CODE ,
	T2.LV1_PROD_RD_TEAM_CN_NAME ,
	T2.LV2_PROD_RND_TEAM_CODE ,
	T2.LV2_PROD_RD_TEAM_CN_NAME ,
	T2.LV3_PROD_RND_TEAM_CODE ,
	T2.LV3_PROD_RD_TEAM_CN_NAME ,
	T2.LV4_PROD_RND_TEAM_CODE ,
	T2.LV4_PROD_RD_TEAM_CN_NAME ,
	T2.LV1_INDUSTRY_CATG_CODE ,
	T2.LV1_INDUSTRY_CATG_CN_NAME ,
	T2.LV2_INDUSTRY_CATG_CODE ,
	T2.LV2_INDUSTRY_CATG_CN_NAME ,
	T2.LV3_INDUSTRY_CATG_CODE ,
	T2.LV3_INDUSTRY_CATG_CN_NAME ,
	T2.LV4_INDUSTRY_CATG_CODE ,
	T2.LV4_INDUSTRY_CATG_CN_NAME ,
	T1.PRODUCT_CODE ,
	T1.SPART_CODE ,
	T1.SPART_CN_NAME ,
	T1.CODE_ATTRIBUTES ,
	-1 CREATED_BY ,
	CURRENT_TIMESTAMP AS CREATION_DATE ,
	-1 LAST_UPDATED_BY ,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
	''N'' AS DEL_FLAG 
	FROM '||V_FROM_TABLE||' T1
	JOIN '||V_JOIN_TABLE||' T2
ON T1.BG_CODE = T2.BG_CODE   -- 202504,关联条件由名称修改为编码关联
AND T1.SPART_CODE = T2.SPART_CODE
AND T1.LV1_PROD_LIST_CN_NAME = T2.LV1_PROD_LIST_CN_NAME
AND (DECODE(T1.LV2_PROD_LIST_CN_NAME,'''',T1.LV1_PROD_LIST_CN_NAME,T1.LV2_PROD_LIST_CN_NAME) = 
DECODE(T1.LV2_PROD_LIST_CN_NAME,'''',T2.LV1_PROD_LIST_CN_NAME,T2.LV2_PROD_LIST_CN_NAME)
OR 
DECODE(T1.LV2_PROD_LIST_CN_NAME,''ALL'',T1.LV1_PROD_LIST_CN_NAME,T1.LV2_PROD_LIST_CN_NAME) = 
DECODE(T1.LV2_PROD_LIST_CN_NAME,''ALL'',T2.LV1_PROD_LIST_CN_NAME,T2.LV2_PROD_LIST_CN_NAME)
)
AND T1.VERSION_ID = '||V_VERSION_ID
;
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;
 

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成,成本类型:'||F_COST_TYPE);
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败,成本类型:'||F_COST_TYPE, 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

