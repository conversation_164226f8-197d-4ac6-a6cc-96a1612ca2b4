-- Name: f_dm_fcst_point_base_cus_annl; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_point_base_cus_annl(f_cost_type character varying, f_granularity_type character varying, f_cus_id bigint, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年7月11日
  创建人  ：唐钦
  背景描述：组合虚化-年度分析页面
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_POINT_BASE_CUS_ANNL('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_POINT_BASE_CUS_ANNL'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_SQL_CUSTOM_ID VARCHAR(200); --筛选条件
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_CUS_ID BIGINT := F_CUS_ID;
  V_LAST_YEAR_FLAG varchar(100);
  V_YEAR_FLAG VARCHAR(50);
  V_YEAR_APPEND varchar(50);
  V_TO_AMP_TABLE VARCHAR(100);
  V_TO_STATUS_TABLE VARCHAR(100);
  V_TO_WEIGHT_TABLE VARCHAR(100);
  V_TO_COST_TABLE VARCHAR(100);
  V_FROM_MON_TABLE VARCHAR(100);
  V_FROM_AVG_TABLE VARCHAR(100);
  V_FROM_AMP_TABLE VARCHAR(100);
  V_FROM_STATUS_TABLE VARCHAR(100);
  V_FROMVARA_TABLE VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_TMP1_TABLE VARCHAR(100);
  V_TMP2_TABLE VARCHAR(100);
  V_SQL_DE_AMT VARCHAR(200);
  V_PARENT_CODE VARCHAR(200);
  V_PARENT_NAME VARCHAR(300);
  V_PARENT_LEVEL VARCHAR(200);   -- LV0-LV4最细粒度层级
  V_LEVEL_CODE VARCHAR(100);   -- 判断LV0-LV4最细粒度层级，再用哪个层级字段
  V_CUS_GROUP_CODE VARCHAR(50);
  V_CUS_LV_CODE VARCHAR(500);
  V_CUS_LV_NAME VARCHAR(10) := 'NULL';
  V_LV_CODE VARCHAR(100);
  V_LV_NAME VARCHAR(100);
  V_LV0_CODE VARCHAR(100);
  V_LV0_NAME VARCHAR(100);
  V_LV1_CODE VARCHAR(100);
  V_LV1_NAME VARCHAR(100);
  V_LV2_CODE VARCHAR(100);
  V_LV2_NAME VARCHAR(100);
  V_LV3_CODE VARCHAR(100);
  V_LV3_NAME VARCHAR(100);
  V_LV4_CODE VARCHAR(100);
  V_LV4_NAME VARCHAR(100);
  V_MAIN_FLAG VARCHAR(100);
  V_REL_MAIN VARCHAR(500);
  V_REL_DIMENSION VARCHAR(500);
  V_REL_DIM VARCHAR(300);
  V_SQL_CONDITION VARCHAR(500);
  V_BEGIN_NUM INT;
  V_END_NUM INT;
  V_CUS_PARENT_LEVEL VARCHAR(100);
  V_CUS_GROUP_LEVEL VARCHAR(100);
  V_PBI_DIM VARCHAR(500);
  V_REL_CODE VARCHAR(500);
  V_PARTITION_DIM TEXT;
  V_CUS_GROUP VARCHAR(500);
  V_SQL_LV_CODE VARCHAR(50);
  V_INTO_CUS_LV_CODE VARCHAR(100);
  V_PARENT_AMT VARCHAR(500);
  V_SQL_ONLY_FLAG VARCHAR(200);
  V_TYPE_FLAG VARCHAR(50);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 根据入参，对变量进行不同定义
     V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_AMT_TMP';
     V_TMP1_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_CUS_AMT_TMP';
     V_TMP2_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_COST_TMP';
     V_FROM_MON_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DECODE_ENTIRE_T';
     V_FROM_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
     V_FROM_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MID_GROUP_AMP_T';
     V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL_WEIGHT_T';
     V_TO_AMP_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL_AMP_T';
     V_TO_STATUS_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL_STATUS_T';
     V_TO_COST_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_ANNL_COST_T';
  IF F_COST_TYPE = 'PSP' THEN    -- PSP成本类型
     V_SQL_DE_AMT := 'RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_DE_AMT := 'GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,';   -- 解密金额
  END IF;
  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV_CODE := 'PROD_RND_TEAM_CODE';
     V_LV0_CODE := 'LV0_PROD_RND_TEAM_CODE';
     V_LV0_NAME := 'LV0_PROD_RD_TEAM_CN_NAME';
     V_LV1_CODE := 'LV1_PROD_RND_TEAM_CODE'; 
     V_LV1_NAME := 'LV1_PROD_RD_TEAM_CN_NAME';
     V_LV2_CODE := 'LV2_PROD_RND_TEAM_CODE';
     V_LV2_NAME := 'LV2_PROD_RD_TEAM_CN_NAME';
     V_LV3_CODE := 'LV3_PROD_RND_TEAM_CODE';
     V_LV3_NAME := 'LV3_PROD_RD_TEAM_CN_NAME';
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV_CODE := 'INDUSTRY_CATG_CODE'; 
     V_LV0_CODE := 'LV0_INDUSTRY_CATG_CODE'; 
     V_LV0_NAME := 'LV0_INDUSTRY_CATG_CN_NAME';
     V_LV1_CODE := 'LV1_INDUSTRY_CATG_CODE'; 
     V_LV1_NAME := 'LV1_INDUSTRY_CATG_CN_NAME';
     V_LV2_CODE := 'LV2_INDUSTRY_CATG_CODE';
     V_LV2_NAME := 'LV2_INDUSTRY_CATG_CN_NAME';
     V_LV3_CODE := 'LV3_INDUSTRY_CATG_CODE';
     V_LV3_NAME := 'LV3_INDUSTRY_CATG_CN_NAME';
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV_CODE := 'PROD_LIST_CODE'; 
     V_LV0_CODE := 'LV0_PROD_LIST_CODE'; 
     V_LV0_NAME := 'LV0_PROD_LIST_CN_NAME';
     V_LV1_CODE := 'LV1_PROD_LIST_CODE'; 
     V_LV1_NAME := 'LV1_PROD_LIST_CN_NAME';
     V_LV2_CODE := 'LV2_PROD_LIST_CODE';
     V_LV2_NAME := 'LV2_PROD_LIST_CN_NAME';
     V_LV3_CODE := 'LV3_PROD_LIST_CODE';
     V_LV3_NAME := 'LV3_PROD_LIST_CN_NAME';
     V_LV4_CODE := 'LV4_PROD_LIST_CODE';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME';
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_AMP_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_STATUS_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_COST_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||V_CUS_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'，组合ID为：'||V_CUS_ID||'的'||V_TO_WEIGHT_TABLE||'、'||V_TO_AMP_TABLE||'、'||V_TO_STATUS_TABLE||'、'||V_TO_COST_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建金额临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
         PERIOD_YEAR                   INT,
         '||V_LV0_CODE||'              VARCHAR(50),
         '||V_LV0_NAME||'              VARCHAR(200),
         '||V_LV1_CODE||'              VARCHAR(50),
         '||V_LV1_NAME||'              VARCHAR(200),
         '||V_LV2_CODE||'              VARCHAR(50),
         '||V_LV2_NAME||'              VARCHAR(200),
         '||V_LV3_CODE||'              VARCHAR(50),
         '||V_LV3_NAME||'              VARCHAR(200),
         '||V_LV4_CODE||'              VARCHAR(50),
         '||V_LV4_NAME||'              VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         RMB_COST_AMT                  VARCHAR(200),
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10)
        )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP_TABLE||'表成功');
  
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP1_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP1_TABLE||' (
            PERIOD_YEAR       INT,
            CUSTOM_ID         BIGINT,
            CUSTOM_CN_NAME    VARCHAR(200),
            LV_CODE           VARCHAR(200),
            LV_CN_NAME        VARCHAR(200),
            DIMENSION_CODE    VARCHAR(50),
            DIMENSION_CN_NAME VARCHAR(200),
            GROUP_CODE        VARCHAR(50),
            GROUP_CN_NAME     VARCHAR(200),
            GROUP_LEVEL       VARCHAR(50),
            LOGIC_NUM         INT,
            RMB_COST_AMT      NUMERIC,
            PARENT_AMT        NUMERIC,
            PARENT_CODE       VARCHAR(500),
            PARENT_CN_NAME    VARCHAR(500),
            PARENT_LEVEL      VARCHAR(50),
            GRANULARITY_TYPE  VARCHAR(50),
            REGION_CODE       VARCHAR(50),
            REGION_CN_NAME    VARCHAR(200),
            REPOFFICE_CODE    VARCHAR(50),
            REPOFFICE_CN_NAME VARCHAR(200),
            BG_CODE           VARCHAR(50),
            BG_CN_NAME        VARCHAR(200),
            OVERSEA_FLAG      VARCHAR(10),
            VIEW_FLAG         VARCHAR(50),
            MAIN_FLAG         VARCHAR(2),
            CODE_ATTRIBUTES   VARCHAR(50),
            TYPE_FLAG         VARCHAR(50)
        )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP1_TABLE||'表成功');
     
  -- 创建金额临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP2_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP2_TABLE||' (
         PERIOD_YEAR                   INT,
         '||V_LV0_CODE||'              VARCHAR(50),
         '||V_LV0_NAME||'              VARCHAR(200),
         '||V_LV1_CODE||'              VARCHAR(50),
         '||V_LV1_NAME||'              VARCHAR(200),
         '||V_LV2_CODE||'              VARCHAR(50),
         '||V_LV2_NAME||'              VARCHAR(200),
         '||V_LV3_CODE||'              VARCHAR(50),
         '||V_LV3_NAME||'              VARCHAR(200),
         '||V_LV4_CODE||'              VARCHAR(50),
         '||V_LV4_NAME||'              VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         RMB_COST_AMT                  VARCHAR(200),
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10)
        )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP2_TABLE||'表成功');

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表：'||V_TMP_TABLE||'，'||V_TMP1_TABLE||'，'||V_TMP2_TABLE||'成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
     
  FOR NUM IN 1 .. 2 LOOP 
  IF NUM = 1 THEN   -- 第一次循环：将成本分布图的数据解密后放入临时表2
     V_TMP_TABLE := V_TMP2_TABLE;
     V_FROM_AVG_TABLE := V_FROM_MON_TABLE;
     V_SQL_ONLY_FLAG := 'AND T1.ONLY_SPART_FLAG = ''N''';    -- 取LV3.5下非单SPART的数据进行计算
  ELSIF NUM = 2 THEN   -- 第二次循环：将计算权重的数据解密后放入临时表
     V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_AMT_TMP';
     V_FROM_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
     V_SQL_ONLY_FLAG := '';
  END IF;
  
  -- 将金额数据存放入临时表
  V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
         PERIOD_YEAR,                  
         '||V_LV0_CODE||',                  
         '||V_LV0_NAME||',                  
         '||V_LV1_CODE||',                  
         '||V_LV1_NAME||',                  
         '||V_LV2_CODE||',                  
         '||V_LV2_NAME||',                  
         '||V_LV3_CODE||',                  
         '||V_LV3_NAME||',                  
         '||V_LV4_CODE||',                  
         '||V_LV4_NAME||',                  
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         SPART_CODE,                   
         SPART_CN_NAME,                
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         RMB_COST_AMT,                 
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
    )
  SELECT PERIOD_YEAR,                  
         '||V_LV0_CODE||',
         '||V_LV0_NAME||',
         '||V_LV1_CODE||',
         '||V_LV1_NAME||',
         '||V_LV2_CODE||',
         '||V_LV2_NAME||',
         '||V_LV3_CODE||',
         '||V_LV3_NAME||',
         '||V_LV4_CODE||',
         '||V_LV4_NAME||',
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         SPART_CODE,                   
         SPART_CN_NAME,                
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         '||V_SQL_DE_AMT||' 
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
      FROM '||V_FROM_AVG_TABLE||' T1
      WHERE VERSION_ID = '||V_VERSION_ID||'
      '||V_SQL_ONLY_FLAG;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将数据存放入临时表：'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  END LOOP;
  
  -- 造一版全选的主力编码的数据存放入临时表
  V_SQL := '
  INSERT INTO '||V_TMP2_TABLE||'(
         PERIOD_YEAR,                  
         '||V_LV0_CODE||',                  
         '||V_LV0_NAME||',                  
         '||V_LV1_CODE||',                  
         '||V_LV1_NAME||',                  
         '||V_LV2_CODE||',                  
         '||V_LV2_NAME||',                  
         '||V_LV3_CODE||',                  
         '||V_LV3_NAME||',                  
         '||V_LV4_CODE||',                  
         '||V_LV4_NAME||',                  
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         SPART_CODE,                   
         SPART_CN_NAME,                
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         RMB_COST_AMT,                 
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
    )
  SELECT PERIOD_YEAR,                  
         '||V_LV0_CODE||',
         '||V_LV0_NAME||',
         '||V_LV1_CODE||',
         '||V_LV1_NAME||',
         '||V_LV2_CODE||',
         '||V_LV2_NAME||',
         '||V_LV3_CODE||',
         '||V_LV3_NAME||',
         '||V_LV4_CODE||',
         '||V_LV4_NAME||',
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         SPART_CODE,                   
         SPART_CN_NAME,                
         MAIN_FLAG,                    
         ''全选'' AS CODE_ATTRIBUTES,              
         RMB_COST_AMT, 
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
      FROM '||V_TMP2_TABLE||'
      WHERE MAIN_FLAG = ''Y''';    -- 造一版全选的主力编码的数据
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '造一版全选的主力编码的数据存放入临时表：'||V_TMP2_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 将组合维表值插入变量
     V_SQL := '
     SELECT REPLACE(LV_CODE,'','','''''',''''''),LV_CODE,PARENT_LEVEL,GROUP_LEVEL
        FROM DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_DIM_T 
        WHERE CUSTOM_ID = '||V_CUS_ID;
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL INTO V_CUS_LV_CODE,V_INTO_CUS_LV_CODE,V_CUS_PARENT_LEVEL,V_CUS_GROUP_LEVEL;
     DBMS_OUTPUT.PUT_LINE(V_CUS_LV_CODE||','||V_CUS_PARENT_LEVEL||','||V_CUS_GROUP_LEVEL);
     
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到虚化数据为：LV_CODE = '||V_INTO_CUS_LV_CODE||',PARENT_LEVEL = '||V_CUS_PARENT_LEVEL||',GROUP_LEVEL = '||V_CUS_GROUP_LEVEL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
     
  -- 判断LV0-LV4最细粒度层级，再用哪个层级字段
  IF V_CUS_PARENT_LEVEL = 'LV0' THEN 
     V_LEVEL_CODE := V_LV0_CODE;
     V_SQL_LV_CODE := 'LV0_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV1' THEN 
     V_LEVEL_CODE := V_LV1_CODE;
     V_SQL_LV_CODE := 'LV1_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV2' THEN 
     V_LEVEL_CODE := V_LV2_CODE;
     V_SQL_LV_CODE := 'LV2_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV3' THEN 
     V_LEVEL_CODE := V_LV3_CODE;
     V_SQL_LV_CODE := 'LV3_CODE';
  ELSIF V_CUS_PARENT_LEVEL = 'LV4' THEN 
     V_LEVEL_CODE := V_LV4_CODE;   -- 一定是多选，卷积时不加
     V_SQL_LV_CODE := 'LV4_CODE';
  END IF;
  
  -- 判断用户选择的层级不一致，条件不一致
  IF V_CUS_GROUP_LEVEL IN ('SUB_DETAIL','SPART') THEN 
     V_END_NUM := 2;
     V_REL_DIMENSION := 'AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                         AND DECODE(T2.DIMENSION_SUBCATEGORY_CODE,NULL,''SNULL4'',T1.DIMENSION_SUBCATEGORY_CODE) = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL4'')
                         AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL5'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL5'')
                         AND NVL(T1.SPART_CODE,''SNULL2'') = NVL(T2.SPART_CODE,''SNULL2'')';
  ELSIF V_CUS_GROUP_LEVEL = 'SUBCATEGORY' THEN
     V_END_NUM := 3;
     V_REL_DIMENSION := 'AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                         AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL4'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL4'')';
  ELSIF V_CUS_GROUP_LEVEL = 'DIMENSION' THEN
     V_END_NUM := 3;
     V_REL_DIMENSION := 'AND NVL(T1.DIMENSION_CODE,''SNULL3'') = NVL(T2.DIMENSION_CODE,''SNULL3'')';
  END IF;

  -- 查询该条CUSTOM_ID数据是否是主力编码数据
  V_SQL := '
  SELECT COUNT(1)
    FROM DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_DIM_T 
    WHERE MAIN_FLAG = ''Y''
    AND CUSTOM_ID = '||V_CUS_ID;
  EXECUTE IMMEDIATE V_SQL INTO V_MAIN_FLAG;
    
  -- 判断变量是否有值，对条件进行不同限制
  IF V_MAIN_FLAG = 0 THEN    -- 非主力编码
     V_SQL_CONDITION := 'AND (T1.CODE_ATTRIBUTES <> ''全选'' OR T1.CODE_ATTRIBUTES IS NULL)';   -- 非主力编码：选择非全选的数据+编码属性为空的数据
  ELSE
     V_REL_MAIN := 'AND NVL(T1.CODE_ATTRIBUTES,''SNULL1'') = NVL(T2.CODE_ATTRIBUTES,''SNULL1'')
                    AND NVL(T1.MAIN_FLAG,''SNULL0'') = NVL(T2.MAIN_FLAG,''SNULL0'')';
  END IF;
  
  -------------------------------------------------------------------------------权重逻辑计算--------------------------------------------------------------------------------------------
  -- 循环跑数，分别跑权重和成本分布图的数据
  FOR TYPE_NUM IN 1 .. 2 LOOP 
  IF V_CUS_GROUP_LEVEL IN ('SUB_DETAIL','SPART') THEN 
     V_END_NUM := 2;
  ELSE V_END_NUM := 3;
  END IF;
    IF TYPE_NUM = 1 THEN   -- 权重计算逻辑 
       V_FROMVARA_TABLE := V_TMP_TABLE;
       V_BEGIN_NUM := 1;
       V_TYPE_FLAG := '''WEIGHT''';
    ELSIF TYPE_NUM = 2 THEN      -- 成本分布图逻辑
       V_FROMVARA_TABLE := V_TMP2_TABLE;
       V_BEGIN_NUM := 2;
       V_TYPE_FLAG := '''COST''';
    END IF; 
    
  -- 将均本数据从各成本类型/各目录树获取数据，存放到临时表格式
  -- 当路径2选择不同层级值时，对应的计算逻辑也不一样
  FOR LEV_NUM IN V_BEGIN_NUM .. V_END_NUM LOOP
  IF LEV_NUM = 1 THEN 
     V_PBI_DIM := 'T1.'||V_LV4_CODE||',
                   T1.'||V_LV4_NAME||',
                   T1.DIMENSION_CODE,
                   T1.DIMENSION_CN_NAME,';
     V_PARENT_CODE := 'DECODE(T2.VIEW_FLAG,''PROD_SPART'',T1.'||V_LV4_CODE||',''DIMENSION'',T1.DIMENSION_SUBCATEGORY_CODE)';
     V_PARENT_NAME := 'DECODE(T2.VIEW_FLAG,''PROD_SPART'',T1.'||V_LV4_NAME||',''DIMENSION'',T1.DIMENSION_SUBCATEGORY_CN_NAME)';
     V_PARENT_LEVEL := 'DECODE(T2.VIEW_FLAG,''PROD_SPART'',''LV4'',''DIMENSION'',''SUBCATEGORY'')';
     V_CUS_GROUP := 'DECODE(T2.VIEW_FLAG,''PROD_SPART'',T1.SPART_CODE,''DIMENSION'',T1.DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,       
                     DECODE(T2.VIEW_FLAG,''PROD_SPART'',T1.SPART_CN_NAME,''DIMENSION'',T1.DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,  
                     DECODE(T2.VIEW_FLAG,''PROD_SPART'',''SPART'',''DIMENSION'',''SUB_DETAIL'') AS GROUP_LEVEL,';
     V_PARTITION_DIM := ' T1.PERIOD_YEAR,
                          T1.'||V_LV4_CODE||',
                          T1.'||V_LV4_NAME||',
                          T1.DIMENSION_CODE,
                          T1.DIMENSION_CN_NAME,
                          T1.DIMENSION_SUBCATEGORY_CODE,
                          T1.DIMENSION_SUBCATEGORY_CN_NAME,
                          T1.DIMENSION_SUB_DETAIL_CODE,
                          T1.DIMENSION_SUB_DETAIL_CN_NAME,
                          T1.SPART_CODE,
                          T1.SPART_CN_NAME,
                          T1.MAIN_FLAG,
                          T1.CODE_ATTRIBUTES,';
  -- 用户选择的最细粒度层级不同，计算父级金额的公式也不同
     IF V_CUS_GROUP_LEVEL = 'SUBCATEGORY' THEN 
        V_PARENT_AMT := 'SUM(SUM(NVL(RMB_COST_AMT,0))) OVER(PARTITION BY T1.PERIOD_YEAR,T2.CUSTOM_ID,T1.DIMENSION_SUBCATEGORY_CODE,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.BG_CODE,T1.OVERSEA_FLAG,T1.VIEW_FLAG,T1.MAIN_FLAG,NVL(T1.CODE_ATTRIBUTES,''S1'')) AS PARENT_AMT,';
     ELSIF V_CUS_GROUP_LEVEL = 'DIMENSION' THEN 
        V_PARENT_AMT := 'SUM(SUM(NVL(RMB_COST_AMT,0))) OVER(PARTITION BY T1.PERIOD_YEAR,T2.CUSTOM_ID,T1.DIMENSION_CODE,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.BG_CODE,T1.OVERSEA_FLAG,T1.VIEW_FLAG,T1.MAIN_FLAG,NVL(T1.CODE_ATTRIBUTES,''S1'')) AS PARENT_AMT,';
     ELSE 
        V_PARENT_AMT := 'SUM(SUM(NVL(RMB_COST_AMT,0))) OVER(PARTITION BY T1.PERIOD_YEAR,T2.CUSTOM_ID,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.BG_CODE,T1.OVERSEA_FLAG,T1.VIEW_FLAG,T1.MAIN_FLAG,NVL(T1.CODE_ATTRIBUTES,''S1'')) AS PARENT_AMT,';
     END IF;
  ELSIF LEV_NUM = 2 THEN 
     V_PBI_DIM := ''||V_INTO_CUS_LV_CODE||',
                  NULL AS LV_CN_NAME,
                  NULL AS DIMENSION_CODE,
                  NULL AS DIMENSION_CN_NAME,';
     V_PARENT_AMT := 'SUM(SUM(NVL(RMB_COST_AMT,0))) OVER(PARTITION BY T1.PERIOD_YEAR,T2.CUSTOM_ID,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.BG_CODE,T1.OVERSEA_FLAG,T1.VIEW_FLAG,T1.MAIN_FLAG,NVL(T1.CODE_ATTRIBUTES,''S1'')) AS PARENT_AMT,';
    IF V_CUS_GROUP_LEVEL = 'SUBCATEGORY' THEN 
       V_PARENT_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE';
       V_PARENT_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME';
       V_PARENT_LEVEL := '''SUBCATEGORY''';
       V_REL_DIMENSION := 'AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                           AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL4'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL4'')';
       V_CUS_GROUP := 'T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,       
                       T1.DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,  
                       ''SUB_DETAIL'' AS GROUP_LEVEL,';
       V_PARTITION_DIM := ' T1.PERIOD_YEAR,
                            T1.DIMENSION_SUBCATEGORY_CODE,
                            T1.DIMENSION_SUBCATEGORY_CN_NAME,
                            T1.DIMENSION_SUB_DETAIL_CODE,
                            T1.DIMENSION_SUB_DETAIL_CN_NAME,
                            T1.MAIN_FLAG,
                            T1.CODE_ATTRIBUTES,';
    ELSIF V_CUS_GROUP_LEVEL = 'DIMENSION' THEN 
       V_PARENT_CODE := 'T1.DIMENSION_CODE';
       V_PARENT_NAME := 'T1.DIMENSION_CN_NAME';
       V_PARENT_LEVEL := '''DIMENSION''';
       V_REL_DIMENSION := 'AND NVL(T1.DIMENSION_CODE,''SNULL3'') = NVL(T2.DIMENSION_CODE,''SNULL3'')';
       V_CUS_GROUP := 'T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,       
                       T1.DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,  
                       ''SUB_DETAIL'' AS GROUP_LEVEL,';
       V_PARTITION_DIM := 'T1.PERIOD_YEAR,
                           T1.DIMENSION_CODE,
                           T1.DIMENSION_CN_NAME,
                           T1.DIMENSION_SUB_DETAIL_CODE,
                           T1.DIMENSION_SUB_DETAIL_CN_NAME,
                           T1.MAIN_FLAG,
                           T1.CODE_ATTRIBUTES,';
    ELSE 
       V_PARENT_CODE := ''||V_INTO_CUS_LV_CODE||'';
       V_PARENT_NAME := V_CUS_LV_NAME;
       V_PARENT_LEVEL := ''''||V_CUS_PARENT_LEVEL||'''';
       V_CUS_GROUP := 'DECODE(T2.VIEW_FLAG,''PROD_SPART'',T1.SPART_CODE,''DIMENSION'',T1.DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,       
                       DECODE(T2.VIEW_FLAG,''PROD_SPART'',T1.SPART_CN_NAME,''DIMENSION'',T1.DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,  
                       DECODE(T2.VIEW_FLAG,''PROD_SPART'',''SPART'',''DIMENSION'',''SUB_DETAIL'') AS GROUP_LEVEL,';
       V_REL_DIMENSION := 'AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                           AND DECODE(T2.DIMENSION_SUBCATEGORY_CODE,NULL,''SNULL4'',T1.DIMENSION_SUBCATEGORY_CODE) = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL4'')
                           AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL5'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL5'')
                           AND NVL(T1.SPART_CODE,''SNULL2'') = NVL(T2.SPART_CODE,''SNULL2'')';
       V_PARTITION_DIM := 'T1.PERIOD_YEAR,
                           T1.DIMENSION_SUB_DETAIL_CODE,
                           T1.DIMENSION_SUB_DETAIL_CN_NAME,
                           T1.SPART_CODE,
                           T1.SPART_CN_NAME,
                           T1.MAIN_FLAG,
                           T1.CODE_ATTRIBUTES,';
    END IF;
  ELSIF LEV_NUM = 3 THEN 
     V_PARENT_CODE := ''||V_INTO_CUS_LV_CODE||'';
     V_PARENT_NAME := V_CUS_LV_NAME;
     V_PARENT_LEVEL := ''''||V_CUS_PARENT_LEVEL||'''';
    IF V_CUS_GROUP_LEVEL = 'SUBCATEGORY' THEN 
       V_CUS_GROUP := 'T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
                       T1.DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
                       ''SUBCATEGORY'' AS GROUP_LEVEL,';
       V_REL_DIMENSION := 'AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                           AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL4'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL4'')';
       V_PARTITION_DIM := 'T1.PERIOD_YEAR,
                           T1.DIMENSION_SUBCATEGORY_CODE,
                           T1.DIMENSION_SUBCATEGORY_CN_NAME,
                           T1.MAIN_FLAG,
                           T1.CODE_ATTRIBUTES,';
    ELSIF V_CUS_GROUP_LEVEL = 'DIMENSION' THEN 
       V_CUS_GROUP := 'T1.DIMENSION_CODE AS GROUP_CODE,
                       T1.DIMENSION_CN_NAME AS GROUP_CN_NAME,
                       ''DIMENSION'' AS GROUP_LEVEL,';
       V_REL_DIMENSION := 'AND NVL(T1.DIMENSION_CODE,''SNULL3'') = NVL(T2.DIMENSION_CODE,''SNULL3'')';
       V_PARTITION_DIM := 'T1.PERIOD_YEAR,
                           T1.DIMENSION_CODE,
                           T1.DIMENSION_CN_NAME,
                           T1.MAIN_FLAG,
                           T1.CODE_ATTRIBUTES,';
    END IF;
  END IF;
     V_SQL := '
     INSERT INTO '||V_TMP1_TABLE||'(
            PERIOD_YEAR,      
            CUSTOM_ID,        
            CUSTOM_CN_NAME,   
            LV_CODE,
            LV_CN_NAME,
            DIMENSION_CODE,
            DIMENSION_CN_NAME,
            GROUP_CODE,       
            GROUP_CN_NAME,    
            GROUP_LEVEL,     
            LOGIC_NUM,
            RMB_COST_AMT,     
            PARENT_AMT,
            PARENT_CODE,      
            PARENT_CN_NAME,   
            PARENT_LEVEL,     
            GRANULARITY_TYPE, 
            REGION_CODE,      
            REGION_CN_NAME,   
            REPOFFICE_CODE,   
            REPOFFICE_CN_NAME,
            BG_CODE,          
            BG_CN_NAME,       
            OVERSEA_FLAG,     
            VIEW_FLAG,        
            MAIN_FLAG,        
            CODE_ATTRIBUTES,
            TYPE_FLAG
        )
     SELECT T1.PERIOD_YEAR,      
            T2.CUSTOM_ID,        
            T2.CUSTOM_CN_NAME,   
            '||V_PBI_DIM||'
            '||V_CUS_GROUP||'
            '||LEV_NUM||' AS LOGIC_NUM,   -- 循环几次跑数
            SUM(NVL(RMB_COST_AMT,0)),
            '||V_PARENT_AMT||'
            '||V_PARENT_CODE||' AS PARENT_CODE,      
            '||V_PARENT_NAME||' AS PARENT_CN_NAME,   
            '||V_PARENT_LEVEL||' AS PARENT_LEVEL,       -- 都取用户选择的最细粒度的LV0-LV4层级作为父级
            '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE, 
            T1.REGION_CODE,      
            T1.REGION_CN_NAME,   
            T1.REPOFFICE_CODE,   
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,          
            T1.BG_CN_NAME,       
            T1.OVERSEA_FLAG,     
            T1.VIEW_FLAG,        
            T1.MAIN_FLAG,        
            T1.CODE_ATTRIBUTES,
            '||V_TYPE_FLAG||' AS TYPE_FLAG
         FROM '||V_FROMVARA_TABLE||' T1
         INNER JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_DIM_T T2
         ON T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         '||V_REL_MAIN||'
         '||V_REL_DIMENSION||'
         WHERE T2.CUSTOM_ID = '||V_CUS_ID||'
         AND T1.'||V_LEVEL_CODE||' IN ('||V_CUS_LV_CODE||')
         '||V_SQL_CONDITION||'
         GROUP BY '||V_PARTITION_DIM||'
                   T1.REGION_CODE,
                   T1.REGION_CN_NAME, 
                   T1.REPOFFICE_CODE,
                   T1.REPOFFICE_CN_NAME,
                   T1.BG_CODE,
                   T1.BG_CN_NAME, 
                   T1.OVERSEA_FLAG,
                   T2.VIEW_FLAG,
                   T1.VIEW_FLAG,
                   T2.CUSTOM_ID,        
                   T2.CUSTOM_CN_NAME';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将数据进行处理后插入到临时表：'||V_TMP1_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  END LOOP;
  END LOOP;
  
  -- 成本分布图数据插入结果表
  V_SQL := '
  INSERT INTO '||V_TO_COST_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM '||V_TMP1_TABLE||'
      WHERE TYPE_FLAG = ''COST''
      AND PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2';
   DBMS_OUTPUT.PUT_LINE(V_SQL);
   EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将成本分布图数据进行处理后插入到结果表：'||V_TO_COST_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除掉临时表中计算成本分布图的数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TMP1_TABLE||' WHERE TYPE_FLAG = ''COST''';
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将计算成本分布图的数据从临时表：'||V_TMP1_TABLE||'删除',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 计算SPART层级/子类明细层级的权重数据
     V_SQL := '
     INSERT INTO '||V_TO_WEIGHT_TABLE||'(
            VERSION_ID,
            PERIOD_YEAR,
            CUSTOM_ID,
            CUSTOM_CN_NAME,
            LV4_CODE,
            LV4_CN_NAME,
            DIMENSION_CODE,
            DIMENSION_CN_NAME,
            GROUP_CODE,
            GROUP_CN_NAME,
            GROUP_LEVEL,
            WEIGHT_RATE,
            RMB_COST_AMT,
            PARENT_CODE,
            PARENT_CN_NAME,
            PARENT_LEVEL,
            GRANULARITY_TYPE,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            VIEW_FLAG,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            LOGIC_NUM,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            DEL_FLAG
     )
     SELECT '||V_VERSION_ID||' AS VERSION_ID,
            PERIOD_YEAR,
            CUSTOM_ID,
            CUSTOM_CN_NAME,
            DECODE(LOGIC_NUM,1,LV_CODE,NULL),
            LV_CN_NAME,
            DIMENSION_CODE,
            DIMENSION_CN_NAME,
            GROUP_CODE,
            GROUP_CN_NAME,
            GROUP_LEVEL,
            DECODE(PARENT_AMT,0,0,RMB_COST_AMT/PARENT_AMT) AS WEIGHT_RATE,
            DECODE(LOGIC_NUM,1,NULL,RMB_COST_AMT) AS RMB_COST_AMT,
            PARENT_CODE,
            PARENT_CN_NAME,
            PARENT_LEVEL,
            GRANULARITY_TYPE,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            VIEW_FLAG,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            LOGIC_NUM,
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG
         FROM '||V_TMP1_TABLE||'
         WHERE PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将权重数据计算后插入结果表：'||V_TO_WEIGHT_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  ------------------------------------------------------------------------------涨跌幅逻辑处理------------------------------------------------------------------------------------------
  -- 判断用户选择的当前层级是什么层级，使用不同的关联条件
  IF V_CUS_GROUP_LEVEL = 'SPART' THEN 
     V_REL_CODE := 'AND T1.GROUP_CODE = T2.SPART_CODE';
     V_PARENT_CODE := ''||V_INTO_CUS_LV_CODE||'';
     V_PARENT_NAME := V_CUS_LV_NAME;
     V_PARENT_LEVEL := ''''||V_CUS_PARENT_LEVEL||'''';
  ELSIF V_CUS_GROUP_LEVEL = 'SUB_DETAIL' THEN 
     V_REL_CODE := 'AND T1.GROUP_CODE = T2.DIMENSION_SUB_DETAIL_CODE
                    AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                    AND DECODE(T2.DIMENSION_SUBCATEGORY_CODE,NULL,''SNULL4'',T1.DIMENSION_SUBCATEGORY_CODE) = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL4'')';
     V_PARENT_CODE := ''||V_INTO_CUS_LV_CODE||'';
     V_PARENT_NAME := V_CUS_LV_NAME;
     V_PARENT_LEVEL := ''''||V_CUS_PARENT_LEVEL||'''';
  ELSIF V_CUS_GROUP_LEVEL = 'SUBCATEGORY' THEN 
     V_REL_CODE := 'AND DECODE(T2.DIMENSION_CODE,NULL,''SNULL3'',T1.DIMENSION_CODE) = NVL(T2.DIMENSION_CODE,''SNULL3'')
                    AND T1.DIMENSION_SUBCATEGORY_CODE = T2.DIMENSION_SUBCATEGORY_CODE';
     V_PARENT_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE';
     V_PARENT_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME';
     V_PARENT_LEVEL := '''SUBCATEGORY''';
  ELSIF V_CUS_GROUP_LEVEL = 'DIMENSION' THEN 
     V_REL_CODE := 'AND T1.DIMENSION_CODE = T2.DIMENSION_CODE';
     V_PARENT_CODE := 'T1.DIMENSION_CODE';
     V_PARENT_NAME := 'T1.DIMENSION_CN_NAME';
     V_PARENT_LEVEL := '''DIMENSION''';
  END IF;

  V_SQL := '
  INSERT INTO '||V_TO_AMP_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  -- 将最细粒度的基础层级涨跌幅数据取出来
  WITH BASE_AMP_TMP AS (
     SELECT T1.PERIOD_YEAR,
            T2.CUSTOM_ID,
            T2.CUSTOM_CN_NAME,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
            T1.GROUP_LEVEL,
            T1.DIMENSION_CODE,
            T1.DIMENSION_CN_NAME,
            T1.DIMENSION_SUBCATEGORY_CODE,
            T1.DIMENSION_SUBCATEGORY_CN_NAME,
            T1.LV4_CODE,
            T1.ANNUAL_AMP,
            T1.PARENT_CODE,
            T1.PARENT_CN_NAME,
            DECODE(T1.VIEW_FLAG,''PROD_SPART'',''LV4'',''DIMENSION'',''SUBCATEGORY'' )AS PARENT_LEVEL,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.VIEW_FLAG,
            T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES
         FROM '||V_FROM_AMP_TABLE||' T1
         INNER JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_DIM_T T2
         ON T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         '||V_REL_CODE||'
         AND NVL(T1.CODE_ATTRIBUTES,''SNULL1'') = NVL(T2.CODE_ATTRIBUTES,''SNULL1'')
         AND NVL(T1.MAIN_FLAG,''SNULL0'') = NVL(T2.MAIN_FLAG,''SNULL0'')
         WHERE T1.'||V_SQL_LV_CODE||' IN ('||V_CUS_LV_CODE||')
         AND T1.GROUP_LEVEL IN (''SPART'',''SUB_DETAIL'')
         AND T2.CUSTOM_ID = '||V_CUS_ID||'
         AND T1.VERSION_ID = '||V_VERSION_ID||'
         ),
  BASE_1_AMP_TMP AS(
     SELECT T1.PERIOD_YEAR,
            T1.CUSTOM_ID,
            T1.CUSTOM_CN_NAME,
            T1.GROUP_CODE,
            T1.GROUP_CN_NAME,
            T1.GROUP_LEVEL,
            T1.ANNUAL_AMP*T2.WEIGHT_RATE AS ANNUAL_AMP,
            '||V_PARENT_CODE||' AS PARENT_CODE,
            '||V_PARENT_NAME||' AS PARENT_CN_NAME,
            '||V_PARENT_LEVEL||' AS PARENT_LEVEL,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.VIEW_FLAG,
            T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES
         FROM BASE_AMP_TMP T1
         INNER JOIN '||V_TO_WEIGHT_TABLE||' T2
         ON T1.CUSTOM_ID = T2.CUSTOM_ID
         AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
         AND T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.GROUP_CODE = T2.GROUP_CODE 
         AND T1.GROUP_LEVEL = T2.GROUP_LEVEL 
         AND T1.PARENT_CODE = T2.PARENT_CODE
         AND T1.PARENT_LEVEL = T2.PARENT_LEVEL
         AND NVL(T1.CODE_ATTRIBUTES,''SNULL1'') = NVL(T2.CODE_ATTRIBUTES,''SNULL1'')
         AND NVL(T1.MAIN_FLAG,''SNULL0'') = NVL(T2.MAIN_FLAG,''SNULL0'')
         AND NVL(T1.DIMENSION_CODE,''SNULL2'') = NVL(T2.DIMENSION_CODE,''SNULL2'')
         AND NVL(T1.LV4_CODE,''SNULL3'') = NVL(T2.LV4_CODE,''SNULL3'')
         WHERE T2.LOGIC_NUM = 1   -- 取第一次计算的权重数据
         AND T2.CUSTOM_ID = '||V_CUS_ID||'
         AND T2.VERSION_ID = '||V_VERSION_ID||'
     )
     SELECT '||V_VERSION_ID||' AS VERSION_ID,
            PERIOD_YEAR,
            CUSTOM_ID,
            CUSTOM_CN_NAME,
            GROUP_CODE,
            GROUP_CN_NAME,
            GROUP_LEVEL,
            NVL(SUM(ANNUAL_AMP),0) AS ANNUAL_AMP,
            PARENT_CODE,
            PARENT_CN_NAME,
            PARENT_LEVEL,
            '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            VIEW_FLAG,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG
         FROM BASE_1_AMP_TMP
         GROUP BY PERIOD_YEAR,
                  CUSTOM_ID,
                  CUSTOM_CN_NAME,
                  GROUP_CODE,
                  GROUP_CN_NAME,
                  GROUP_LEVEL,
                  PARENT_CODE,
                  PARENT_CN_NAME,
                  PARENT_LEVEL,
                  REGION_CODE,
                  REGION_CN_NAME,
                  REPOFFICE_CODE,
                  REPOFFICE_CN_NAME,
                  BG_CODE,
                  BG_CN_NAME,
                  OVERSEA_FLAG,
                  VIEW_FLAG,
                  MAIN_FLAG,
                  CODE_ATTRIBUTES';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将SPART/子类明细层级的涨跌幅数据,插入结果表：'||V_TO_AMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  -- 判断用户选择的最细粒度层级，不是SPART/子类明细层级时，继续计算下面的涨跌幅数据
  IF V_CUS_GROUP_LEVEL IN ('SUBCATEGORY','DIMENSION') THEN 
     V_SQL := '
     INSERT INTO '||V_TO_AMP_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
     WITH BASE_2_AMP_TMP AS (
     SELECT T1.PERIOD_YEAR,
            T1.CUSTOM_ID,
            T1.CUSTOM_CN_NAME,
            T1.PARENT_CODE AS GROUP_CODE,
            T1.PARENT_CN_NAME AS GROUP_CN_NAME,
            '''||V_CUS_GROUP_LEVEL||''' AS GROUP_LEVEL,
            T1.ANNUAL_AMP*T2.WEIGHT_RATE AS ANNUAL_AMP,
            '||V_CUS_LV_CODE||' AS PARENT_CODE,
            '||V_CUS_LV_NAME||' AS PARENT_CN_NAME,
            '''||V_CUS_PARENT_LEVEL||''' AS PARENT_LEVEL,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
            T1.VIEW_FLAG,
            T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES
         FROM '||V_TO_AMP_TABLE||' T1
         INNER JOIN '||V_TO_WEIGHT_TABLE||' T2
         ON T1.CUSTOM_ID = T2.CUSTOM_ID
         AND T1.VERSION_ID = T2.VERSION_ID
         AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
         AND T1.VIEW_FLAG = T2.VIEW_FLAG
         AND T1.REGION_CODE = T2.REGION_CODE
         AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
         AND T1.GROUP_CODE = T2.GROUP_CODE 
         AND T1.GROUP_LEVEL = T2.GROUP_LEVEL 
         AND T1.PARENT_CODE = T2.PARENT_CODE
         AND NVL(T1.CODE_ATTRIBUTES,''SNULL1'') = NVL(T2.CODE_ATTRIBUTES,''SNULL1'')
         AND NVL(T1.MAIN_FLAG,''SNULL0'') = NVL(T2.MAIN_FLAG,''SNULL0'')
         WHERE T2.LOGIC_NUM = 2   -- 取第二次计算的权重数据
         AND T1.CUSTOM_ID = '||V_CUS_ID||'
         AND T1.VERSION_ID = '||V_VERSION_ID||'
     )
     SELECT '||V_VERSION_ID||' AS VERSION_ID,
            PERIOD_YEAR,
            CUSTOM_ID,
            CUSTOM_CN_NAME,
            GROUP_CODE,
            GROUP_CN_NAME,
            GROUP_LEVEL,
            NVL(SUM(ANNUAL_AMP),0) AS ANNUAL_AMP,
            PARENT_CODE,
            PARENT_CN_NAME,
            PARENT_LEVEL,
            '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            VIEW_FLAG,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            -1 AS CREATED_BY,
            CURRENT_TIMESTAMP AS CREATION_DATE,
            -1 AS LAST_UPDATED_BY,
            CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
            ''N'' AS DEL_FLAG
         FROM BASE_2_AMP_TMP
         GROUP BY PERIOD_YEAR,
                  CUSTOM_ID,
                  CUSTOM_CN_NAME,
                  GROUP_CODE,
                  GROUP_CN_NAME,
                  GROUP_LEVEL,
                  PARENT_CODE,
                  PARENT_CN_NAME,
                  PARENT_LEVEL,
                  REGION_CODE,
                  REGION_CN_NAME,
                  REPOFFICE_CODE,
                  REPOFFICE_CN_NAME,
                  BG_CODE,
                  BG_CN_NAME,
                  OVERSEA_FLAG,
                  VIEW_FLAG,
                  MAIN_FLAG,
                  CODE_ATTRIBUTES';
     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将用户选择的层级的涨跌幅数据,插入结果表：'||V_TO_AMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除权重表第一次计算的权重数据，只保留本层级及子级数据
   EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE CUSTOM_ID = '||V_CUS_ID||' AND LOGIC_NUM = 1';
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除权重表：'||V_TO_WEIGHT_TABLE||'，第一次计算的权重数据，只保留本层级及子级数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  ELSE NULL;
  END IF;
  
  --------------------------------------------------------------------------------状态码逻辑处理-----------------------------------------------------------------------------------------
  -- 创建状态码临时表1
  DROP TABLE IF EXISTS DM_CUSTOM_MID_STATUS_TMP;
  CREATE TEMPORARY TABLE DM_CUSTOM_MID_STATUS_TMP(
      CUSTOM_ID         BIGINT,
      CUSTOM_CN_NAME    VARCHAR(200),
      PERIOD_YEAR       VARCHAR(50),
      PARENT_CODE       VARCHAR(500),
      PARENT_CN_NAME    VARCHAR(1000),
      PARENT_LEVEL      VARCHAR(50),
      GROUP_CODE        VARCHAR(50), 
      GROUP_CN_NAME     VARCHAR(1000),
      GROUP_LEVEL       VARCHAR(50), 
      STATUS_CODE       BIGINT,
      APPEND_YEAR       BIGINT,
      GRANULARITY_TYPE  VARCHAR(20),
      REGION_CODE       VARCHAR(50),
      REGION_CN_NAME    VARCHAR(200),
      REPOFFICE_CODE    VARCHAR(50),
      REPOFFICE_CN_NAME VARCHAR(200),
      BG_CODE           VARCHAR(50),
      BG_CN_NAME        VARCHAR(200),
      OVERSEA_FLAG      VARCHAR(2),
      VIEW_FLAG         VARCHAR(50),
      MAIN_FLAG         VARCHAR(2),
      CODE_ATTRIBUTES   VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY ROUNDROBIN;   
  
  -- 创建状态码临时表2
  DROP TABLE IF EXISTS BY_YEAR_AMP_TMP;
  CREATE TEMPORARY TABLE BY_YEAR_AMP_TMP(
      CUSTOM_ID              BIGINT,
      CUSTOM_CN_NAME         VARCHAR(200),
      LV_CODE                VARCHAR(50),
      LV_CN_NAME             VARCHAR(200),
      PARENT_CODE            VARCHAR(500),
      PARENT_CN_NAME         VARCHAR(1000),
      PARENT_LEVEL           VARCHAR(50),
      GROUP_CODE             VARCHAR(50), 
      GROUP_CN_NAME          VARCHAR(200),
      GROUP_LEVEL            VARCHAR(50), 
      LAST_THREE_YEAR_FLAG   BIGINT,
      LAST_THREE_APPEND_YEAR BIGINT,
      LAST_TWO_YEAR_FLAG     BIGINT,
      LAST_TWO_APPEND_YEAR   BIGINT,
      LAST_YEAR_FLAG         BIGINT,
      LAST_APPEND_YEAR       BIGINT,
      CURRENT_YEAR_FLAG      BIGINT,
      CURRENT_APPEND_YEAR    BIGINT,
      GRANULARITY_TYPE       VARCHAR(10),
      REGION_CODE            VARCHAR(50),
      REGION_CN_NAME         VARCHAR(200),
      REPOFFICE_CODE         VARCHAR(50),
      REPOFFICE_CN_NAME      VARCHAR(200),
      BG_CODE                VARCHAR(50),
      BG_CN_NAME             VARCHAR(200),
      OVERSEA_FLAG           VARCHAR(2),
      VIEW_FLAG              VARCHAR(50),
      MAIN_FLAG              VARCHAR(2),
      CODE_ATTRIBUTES        VARCHAR(50)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH (GROUP_CODE); 
  
 V_SQL := '
   INSERT INTO BY_YEAR_AMP_TMP(
          CUSTOM_ID,             
          CUSTOM_CN_NAME,  
          PARENT_CODE,           
          PARENT_CN_NAME,        
          PARENT_LEVEL,          
          GROUP_CODE,            
          GROUP_CN_NAME,         
          GROUP_LEVEL,           
          LAST_THREE_YEAR_FLAG,  
          LAST_TWO_YEAR_FLAG,    
          LAST_YEAR_FLAG,        
          CURRENT_YEAR_FLAG,     
          GRANULARITY_TYPE,      
          REGION_CODE,           
          REGION_CN_NAME,        
          REPOFFICE_CODE,        
          REPOFFICE_CN_NAME,     
          BG_CODE,               
          BG_CN_NAME,            
          OVERSEA_FLAG,          
          VIEW_FLAG,             
          MAIN_FLAG,             
          CODE_ATTRIBUTES
        )
   SELECT CUSTOM_ID,             
          CUSTOM_CN_NAME,
          PARENT_CODE,           
          PARENT_CN_NAME,        
          PARENT_LEVEL,          
          GROUP_CODE,            
          GROUP_CN_NAME,         
          GROUP_LEVEL, 
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-3 THEN DECODE(RMB_COST_AMT,0,1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-2 THEN DECODE(RMB_COST_AMT,0,1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||'-1 THEN DECODE(RMB_COST_AMT,0,1,0) ELSE 0 END) AS LAST_YEAR_FLAG, 
          SUM( CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN DECODE(RMB_COST_AMT,0,1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,
          GRANULARITY_TYPE,      
          REGION_CODE,           
          REGION_CN_NAME,        
          REPOFFICE_CODE,        
          REPOFFICE_CN_NAME,     
          BG_CODE,               
          BG_CN_NAME,            
          OVERSEA_FLAG,          
          VIEW_FLAG,             
          MAIN_FLAG,             
          CODE_ATTRIBUTES
       FROM '||V_TMP1_TABLE||'
       WHERE LOGIC_NUM = 2
       GROUP BY CUSTOM_ID,
                CUSTOM_CN_NAME,
                PARENT_CODE,
                PARENT_CN_NAME,
                PARENT_LEVEL,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                GRANULARITY_TYPE,
                REGION_CODE,           
                REGION_CN_NAME,        
                REPOFFICE_CODE,        
                REPOFFICE_CN_NAME,     
                BG_CODE,               
                BG_CN_NAME,            
                OVERSEA_FLAG,          
                VIEW_FLAG,             
                MAIN_FLAG,             
                CODE_ATTRIBUTES';
     
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;   
    DBMS_OUTPUT.PUT_LINE('状态码各年数据情况插入临时表');            
  
  -- 对ITEM层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
    IF YEAR_FLAG = V_YEAR-2 THEN
       V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
       V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
    ELSIF YEAR_FLAG = V_YEAR-1 THEN
       V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
       V_YEAR_FLAG := 'LAST_YEAR_FLAG';
    ELSIF YEAR_FLAG = V_YEAR THEN
       V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
       V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
    ELSE NULL;
    END IF;
  
  -- ITEM层级年度涨跌幅状态码数据计算逻辑
  V_SQL := '
   INSERT INTO DM_CUSTOM_MID_STATUS_TMP(
          CUSTOM_ID,        
          CUSTOM_CN_NAME,   
          PERIOD_YEAR,      
          PARENT_CODE,      
          PARENT_CN_NAME,   
          PARENT_LEVEL,     
          GROUP_CODE,       
          GROUP_CN_NAME,    
          GROUP_LEVEL,      
          STATUS_CODE,  
          GRANULARITY_TYPE, 
          REGION_CODE,      
          REGION_CN_NAME,   
          REPOFFICE_CODE,   
          REPOFFICE_CN_NAME,
          BG_CODE,          
          BG_CN_NAME,       
          OVERSEA_FLAG,     
          VIEW_FLAG,        
          MAIN_FLAG,        
          CODE_ATTRIBUTES
      ) 
   SELECT CUSTOM_ID,
          CUSTOM_CN_NAME,
          '||YEAR_FLAG||' AS PERIOD_YEAR,   -- 循环的年份即是当次计算的年份
          PARENT_CODE,
          PARENT_CN_NAME,
          PARENT_LEVEL,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
               WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
               WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 THEN 4
          END AS STATUS_CODE,
          GRANULARITY_TYPE,
          REGION_CODE,      
          REGION_CN_NAME,   
          REPOFFICE_CODE,   
          REPOFFICE_CN_NAME,
          BG_CODE,          
          BG_CN_NAME,       
          OVERSEA_FLAG,     
          VIEW_FLAG,        
          MAIN_FLAG,        
          CODE_ATTRIBUTES
       FROM BY_YEAR_AMP_TMP';

    DBMS_OUTPUT.PUT_LINE(V_SQL);
    DBMS_OUTPUT.PUT_LINE('状态码逻辑执行前');
    EXECUTE IMMEDIATE V_SQL;   
    DBMS_OUTPUT.PUT_LINE('状态码逻辑执行后');    
       
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级全维度缺失状态码插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  END LOOP;   -- 结束循环
  
  -- 其余层级状态码计算逻辑
  V_SQL := '
  INSERT INTO DM_CUSTOM_MID_STATUS_TMP(
         CUSTOM_ID,        
         CUSTOM_CN_NAME,   
         PERIOD_YEAR,      
         PARENT_CODE,      
         PARENT_CN_NAME,   
         PARENT_LEVEL,     
         GROUP_CODE,       
         GROUP_CN_NAME,    
         GROUP_LEVEL,      
         STATUS_CODE,
         GRANULARITY_TYPE, 
         REGION_CODE,      
         REGION_CN_NAME,   
         REPOFFICE_CODE,   
         REPOFFICE_CN_NAME,
         BG_CODE,          
         BG_CN_NAME,       
         OVERSEA_FLAG,     
         VIEW_FLAG,        
         MAIN_FLAG,        
         CODE_ATTRIBUTES 
      )
  WITH ITEM_STATUS_TMP AS(
  SELECT T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         T1.PERIOD_YEAR,
         T1.PARENT_CODE AS GROUP_CODE,
         T1.PARENT_LEVEL AS GROUP_LEVEL,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1, -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4,
         T1.GRANULARITY_TYPE,
         T1.REGION_CODE,
         T1.REPOFFICE_CODE,
         T1.BG_CODE,
         T1.OVERSEA_FLAG,     
         T1.VIEW_FLAG,        
         T1.MAIN_FLAG,        
         T1.CODE_ATTRIBUTES
       FROM DM_CUSTOM_MID_STATUS_TMP T1
       WHERE GROUP_LEVEL IN (''SPART'',''SUB_DETAIL'')
       GROUP BY T1.CUSTOM_ID,
                T1.CUSTOM_CN_NAME,
                T1.PERIOD_YEAR,
                T1.PARENT_CODE,
                T1.PARENT_LEVEL,
                T1.GRANULARITY_TYPE,
                T1.REGION_CODE,
                T1.REPOFFICE_CODE,
                T1.BG_CODE,
                T1.OVERSEA_FLAG,     
                T1.VIEW_FLAG,        
                T1.MAIN_FLAG,        
                T1.CODE_ATTRIBUTES)
  SELECT T1.CUSTOM_ID,
         T1.CUSTOM_CN_NAME,
         T1.PERIOD_YEAR,      
         T1.PARENT_CODE,      
         T1.PARENT_CN_NAME,   
         T1.PARENT_LEVEL,     
         T1.GROUP_CODE,       
         T1.GROUP_CN_NAME,    
         T1.GROUP_LEVEL,  
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_4 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
         T1.GRANULARITY_TYPE,
         T1.REGION_CODE,      
         T1.REGION_CN_NAME,   
         T1.REPOFFICE_CODE,   
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,          
         T1.BG_CN_NAME,       
         T1.OVERSEA_FLAG,     
         T1.VIEW_FLAG,        
         T1.MAIN_FLAG,        
         T1.CODE_ATTRIBUTES
     FROM '||V_TO_AMP_TABLE||' T1  
     LEFT JOIN ITEM_STATUS_TMP T2
     ON T1.CUSTOM_ID = T2.CUSTOM_ID
     AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
     AND T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
     AND T1.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND NVL(T1.MAIN_FLAG,''SNULL0'') = NVL(T2.MAIN_FLAG,''SNULL0'')
     AND NVL(T1.CODE_ATTRIBUTES,''SNULL1'') = NVL(T2.CODE_ATTRIBUTES,''SNULL1'')
     WHERE T1.GROUP_LEVEL NOT IN (''SPART'',''SUB_DETAIL'')
     AND T1.VERSION_ID = '||V_VERSION_ID||'
     AND T1.CUSTOM_ID = '||V_CUS_ID;
  DBMS_OUTPUT.PUT_LINE(V_SQL);       
  EXECUTE IMMEDIATE V_SQL;   
          
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '其余层级状态码插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  -- 状态码数据插入结果表
V_SQL := '
  INSERT INTO '||V_TO_STATUS_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         PERIOD_YEAR,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG
      FROM DM_CUSTOM_MID_STATUS_TMP';
    DBMS_OUTPUT.PUT_LINE(V_SQL);       
    EXECUTE IMMEDIATE V_SQL;  
      
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '状态码数据插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 

  EXECUTE IMMEDIATE 'ANALYSE '||V_TO_WEIGHT_TABLE;
  EXECUTE IMMEDIATE 'ANALYSE '||V_TO_AMP_TABLE;
  EXECUTE IMMEDIATE 'ANALYSE '||V_TO_STATUS_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

END$$
/

