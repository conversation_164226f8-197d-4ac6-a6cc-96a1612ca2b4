-- Name: f_dm_foc_mid_month_idx; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_mid_month_idx(f_industry_flag character varying, f_dimension_type character varying, f_keystr character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近修改时间: 2024年6月12日14点22分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间: 2024年4月18日11点22分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间： 2023-03-27
创建人  ： 黄心蕊 HWX1187045
修改时间： 2023-08-15 
创建人  ： 黄心蕊 HWX1187045
修改时间： 2024-03-06
创建人  ： 黄心蕊 HWX1187045
修改内容： 产业加入PARENT_CN_NAME
修改时间：2024-03-27
修改人   ：黄心蕊 hwx1187045
修改内容： 20240327 修改版本号取数逻辑
背景描述： 月度分析-规格品指数初始化
参数描述： 参数一(F_KEYSTR)：绝密数据解密密钥串
		   参数二(F_ITEM_VERSION)：通用版本号
		   参数三(F_DIMENSION_TYPE)：维度类型（U：通用/P：盈利颗粒度）
		   参数四(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ： SELECT FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_IDX('U','密钥串'); --通用颗粒度一个版本的数据
		   SELECT FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_IDX('P','密钥串'); --盈利颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                           VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MID_MONTH_IDX';
  V_VERSION                           BIGINT; --版本号
  V_STEP_NUM                          INT := 0; --函数步骤号
  V_KEYSTR                            VARCHAR(100) := F_KEYSTR; --解密密钥串
  V_BASE_PERIOD_ID                    INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期
  V_DIMENSION_TYPE                    VARCHAR(2) := F_DIMENSION_TYPE; --维度标识
  V_LV3_PROD_RND_TEAM_CODE            TEXT; --LV3CODE字段名
  V_LV3_PROD_RD_TEAM_CN_NAME          TEXT; --LV3NAME字段名
  V_L1_NAME                           TEXT; --盈利颗粒度L1名称字段
  V_L2_NAME                           TEXT; --盈利颗粒度L2名称字段
  V_TOP_ITEM_INFO_T                   TEXT; --规格品维取数表
  V_PROFITS_NAME                      TEXT; --盈利颗粒度字段
  V_BASE_DETAIL_ITEM_T                TEXT; --均本取数表
  V_SQL_LV3_PROD_RND_TEAM_CODE        TEXT; --LV3CODE查询字段
  V_SQL_LV3_PROD_RD_TEAM_CN_NAME      TEXT; --LV3NAME查询字段
  V_SQL_L1_NAME                       TEXT; --盈利颗粒度L1名称查询字段
  V_SQL_L2_NAME                       TEXT; --盈利颗粒度L2名称查询字段
  V_SQL_PROD_RND_TEAM_CODE            TEXT; --重量级团队CODE判断逻辑
  V_SQL_PROD_RND_TEAM_CN_NAME         TEXT; --重量级团队NAME判断逻辑
  V_SQL_PROFITS_NAME                  TEXT; --盈利颗粒度维判断逻辑
  V_JION_LV3_PROD_RND_TEAM_CODE       TEXT; --重量级团队CODE关联逻辑
  V_JOIN_L1_NAME                      TEXT; --盈利颗粒度L1关联逻辑
  V_JOIN_L2_NAME                      TEXT; --盈利颗粒度L2关联逻辑
  V_JOIN_PROFITS_NAME                 TEXT; --盈利颗粒度关联逻辑
  V_SQL_DIMENSION_PART                TEXT; --维度查询字段
  V_MID_TABLE                         TEXT; --中间表
  V_DECRYP_AVG_TEMP                   TEXT; --解密临时表
  V_DM_TOP_BASE_AMT						TEXT;
  V_SQL                               TEXT; --执行语句
  /*九月新增*/
  V_DIMENSION_CODE                    TEXT; --量纲颗粒度
  V_DIMENSION_CN_NAME                 TEXT;
  V_DIMENSION_SUBCATEGORY_CODE        TEXT; --量纲子类
  V_DIMENSION_SUBCATEGORY_CN_NAME     TEXT;
  V_DIMENSION_SUB_DETAIL_CODE         TEXT; --量纲子类明细
  V_DIMENSION_SUB_DETAIL_CN_NAME      TEXT;
  V_DMS_CODE                          TEXT; --量纲粒度
  V_DMS_CN_NAME                       TEXT; --量纲粒度
  V_JOIN_DIMENSION_CODE               TEXT; --量纲关联条件
  V_JOIN_DIMENSION_SUBCATEGORY_CODE   TEXT; --量纲子类关联条件
  V_JOIN_DIMENSION_SUB_DETAIL_CODE    TEXT; --子类明细关联条件
  V_JOIN_DMS_CODE                     TEXT; --量纲颗粒度关联条件
  V_SQL_DIMENSION_CODE                TEXT; --量纲查询表字段
  V_SQL_DIMENSION_CN_NAME             TEXT; --量纲查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CODE    TEXT; --量纲子类查询表字段
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME TEXT; --量纲子类查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CODE     TEXT; --子类明细查询表字段
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  TEXT; --子类明细查询表字段
  V_SQL_DMS_CODE                      TEXT;
  V_SQL_DMS_CN_NAME                   TEXT;
  V_RESERVE_SQL						TEXT;--通用层级反向视角VIEW条件字段
  
    --202401版本新增SPART层级
  V_SPART_CODE                     	   TEXT; --SPART编码
  V_SPART_CN_NAME                  	   TEXT; --SPART中文名称
  V_JOIN_SPART_CODE                	   TEXT; --SPART关联条件
  V_SQL_SPART_CODE                 	   TEXT; --SPART查询表字段
  V_SQL_SPART_CN_NAME				   TEXT; --SPART查询表字段
  
    --202405版本 数字能源新增COA层级
  V_COA_PART		  TEXT :='';
  V_PROD_COA_PART     TEXT :='';
  --202407版本 IAS新增LV4层级
  V_LV4_PART		TEXT :='';
  V_SQL_DIFF_COLUMN_PART      TEXT :='';	--数字能源产业为COA层级,IAS产业为LV4层级
  V_JOIN_DIFF_COLUMN_CODE	  TEXT :='';	--数字能源产业为COA层级,IAS产业为LV4层级
  
BEGIN 
X_RESULT_STATUS := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

V_STEP_NUM:=V_STEP_NUM+1; 
--版本号入参判断，当入参为空，取TOP规格品清单最新版本号
IF F_ITEM_VERSION IS NULL THEN
	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑
	ELSIF F_INDUSTRY_FLAG = 'E' THEN --202405版本 新增数字能源部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	END IF ; 
	
--入参不为空，则以入参为版本号
ELSE V_VERSION := F_ITEM_VERSION;
END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||V_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');

 
 V_STEP_NUM:=V_STEP_NUM+1;
  /*量纲颗粒度变量*/
  V_DIMENSION_CODE                               :='';
  V_DIMENSION_CN_NAME                            :='';
  V_DIMENSION_SUBCATEGORY_CODE                   :='';
  V_DIMENSION_SUBCATEGORY_CN_NAME                :='';
  V_DIMENSION_SUB_DETAIL_CODE                    :='';
  V_DIMENSION_SUB_DETAIL_CN_NAME                 :='';
  V_DMS_CODE                                     :='';
  V_DMS_CN_NAME                                  :='';
  V_JOIN_DIMENSION_CODE                          :='';
  V_JOIN_DIMENSION_SUBCATEGORY_CODE              :='';
  V_JOIN_DIMENSION_SUB_DETAIL_CODE               :='';
  V_SQL_DIMENSION_CODE                           :='';
  V_SQL_DIMENSION_CN_NAME                        :='';
  V_SQL_DIMENSION_SUBCATEGORY_CODE               :='';
  V_SQL_DIMENSION_SUBCATEGORY_CN_NAME            :='';
  V_SQL_DIMENSION_SUB_DETAIL_CODE                :='';
  V_SQL_DIMENSION_SUB_DETAIL_CN_NAME             :='';
  V_SQL_DMS_CODE                                 :='';
  V_SQL_DMS_CN_NAME                              :='';
  --202401版本新增SPART层级
  V_SPART_CODE       	:='';
  V_SPART_CN_NAME       :='';
  V_JOIN_SPART_CODE     :='';
  V_SQL_SPART_CODE      :='';
  V_SQL_SPART_CN_NAME   :='';
  
  /*盈利颗粒度变量*/
   V_L1_NAME            := '';
   V_L2_NAME            := '';
   V_PROFITS_NAME       := '';
   V_SQL_L1_NAME        := '';
   V_SQL_L2_NAME        := '';
   V_JOIN_L1_NAME       := '';
   V_JOIN_L2_NAME       := '';
   V_JOIN_PROFITS_NAME  := '';
   V_SQL_PROFITS_NAME   := '';
   V_SQL_DIMENSION_PART := '';
   
   /*通用颗粒度变量*/
   V_LV3_PROD_RND_TEAM_CODE       := '';
   V_LV3_PROD_RD_TEAM_CN_NAME     := '';
   V_SQL_LV3_PROD_RND_TEAM_CODE   := '';
   V_SQL_LV3_PROD_RD_TEAM_CN_NAME := '';
   V_JION_LV3_PROD_RND_TEAM_CODE  := '';
   V_RESERVE_SQL:='';
  
  IF V_DIMENSION_TYPE = 'U' THEN

	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_TOP_ITEM_INFO_T';
		V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T';
		V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_IDX_T';
		V_DECRYP_AVG_TEMP    := 'DM_FOC_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'UNI_DM_TOP_BASE_AMT';
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
			 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
			   WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
			   ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,';
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
			 END AS PROD_RND_TEAM_CN_NAME,';
			 
		V_RESERVE_SQL:=' AND VIEW_FLAG IN (''0'',''1'',''2'',''3'') '; --正向视角中，视角数据不参与计算
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_ITEM_INFO_T';
	    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_BASE_DETAIL_ITEM_T';
	    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_IDX_T';
	    V_DECRYP_AVG_TEMP    := 'DM_FOC_ENERGY_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'UNI_ENERGY_DM_TOP_BASE_AMT';
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
			 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
			   WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
			   ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,';
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
			 END AS PROD_RND_TEAM_CN_NAME,';
			 
		V_RESERVE_SQL:=' AND VIEW_FLAG IN (''0'',''1'',''2'',''3'') '; --正向视角中，视角数据不参与计算

		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
	  /*表定义*/
	    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_ITEM_INFO_T';
	    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_IAS_BASE_DETAIL_ITEM_T';
	    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_IDX_T';
	    V_DECRYP_AVG_TEMP    := 'DM_FOC_IAS_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'UNI_IAS_DM_TOP_BASE_AMT';
		
		V_SQL_PROD_RND_TEAM_CODE       := '         
			 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
			   WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
			   WHEN ''2'' THEN T2.LV2_PROD_RND_TEAM_CODE
			   WHEN ''7'' THEN T2.LV4_PROD_RND_TEAM_CODE		--202407版本 IAS新增LV4层级
			   ELSE T2.LV3_PROD_RND_TEAM_CODE
			 END AS PROD_RND_TEAM_CODE,';
		V_SQL_PROD_RND_TEAM_CN_NAME    := '
		 CASE T2.VIEW_FLAG
			   WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
			   WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
			   WHEN ''2'' THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			   WHEN ''7'' THEN T2.LV4_PROD_RD_TEAM_CN_NAME		--202407版本 IAS新增LV4层级
			   ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
			 END AS PROD_RND_TEAM_CN_NAME,';
			 
		V_RESERVE_SQL:= ' AND VIEW_FLAG IN (''0'',''1'',''2'',''3'',''7'') '; --202407版本 IAS新增视角7
		
		V_LV4_PART						:='LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';		--202407版本 IAS新增LV4层级
		V_SQL_DIFF_COLUMN_PART			:= 'T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_DIFF_COLUMN_CODE			:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,4) = NVL(T2.LV4_PROD_RND_TEAM_CODE,4)';

		
	  END IF;
	  
    /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE       := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME     := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_LV3_PROD_RND_TEAM_CODE   := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';
  
    /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
  
  ELSIF V_DIMENSION_TYPE = 'P' THEN
  
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_ITEM_INFO_T';
		V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_PFT_BASE_DETAIL_ITEM_T';
		V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_IDX_T';
		V_DECRYP_AVG_TEMP    := 'DM_FOC_PFT_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'PFT_DM_TOP_BASE_AMT';
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_TOP_ITEM_INFO_T';
	    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_BASE_DETAIL_ITEM_T';
	    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_IDX_T';
	    V_DECRYP_AVG_TEMP    := 'DM_FOC_ENERGY_PFT_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'PFT_ENERGY_DM_TOP_BASE_AMT';
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
	  /*表定义*/
	    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_TOP_ITEM_INFO_T';
	    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_BASE_DETAIL_ITEM_T';
	    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_IDX_T';
	    V_DECRYP_AVG_TEMP    := 'DM_FOC_IAS_PFT_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'PFT_IAS_DM_TOP_BASE_AMT';
		
	  END IF;
	
  
    /*字段值定义*/
    V_L1_NAME                   := 'L1_NAME,';
    V_L2_NAME                   := 'L2_NAME,';
    V_PROFITS_NAME              := 'PROFITS_NAME,';
    V_SQL_L1_NAME               := 'T2.L1_NAME,';
    V_SQL_L2_NAME               := 'T2.L2_NAME,';
    V_SQL_DIMENSION_PART        := 'T2.PROFITS_NAME,';
    V_SQL_PROD_RND_TEAM_CODE    := '         
       CASE T2.VIEW_FLAG
         WHEN ''0'' THEN T2.LV0_PROD_RND_TEAM_CODE
         WHEN ''1'' THEN T2.LV1_PROD_RND_TEAM_CODE
         ELSE T2.LV2_PROD_RND_TEAM_CODE
       END AS PROD_RND_TEAM_CODE,';
    V_SQL_PROD_RND_TEAM_CN_NAME := '
       CASE T2.VIEW_FLAG
         WHEN ''0'' THEN T2.LV0_PROD_RD_TEAM_CN_NAME
         WHEN ''1'' THEN T2.LV1_PROD_RD_TEAM_CN_NAME
         ELSE T2.LV2_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';
    V_SQL_PROFITS_NAME          := '
       CASE T2.VIEW_FLAG
         WHEN ''3'' THEN T2.L1_NAME
         WHEN ''4'' THEN T2.L2_NAME
         ELSE ''''
       END AS PROFITS_NAME,';
  
    /*条件定义*/
    V_JOIN_L1_NAME      := ' AND NVL(T1.L1_NAME,3) = NVL(T2.L1_NAME,3)';
    V_JOIN_L2_NAME      := ' AND NVL(T1.L2_NAME,4) = NVL(T2.L2_NAME,4)';
    V_JOIN_PROFITS_NAME := ' AND NVL(T1.PROFITS_NAME,5) = NVL(T2.PROFITS_NAME,5) ';
  
  ELSIF V_DIMENSION_TYPE = 'D' THEN
  
      V_SQL_PROD_RND_TEAM_CODE    := '         
      CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RND_TEAM_CODE
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
         ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,'; 
	  V_SQL_PROD_RND_TEAM_CN_NAME := '
       CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RD_TEAM_CN_NAME
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,';
	
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_TOP_ITEM_INFO_T		:= 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_ITEM_INFO_T';
		V_BASE_DETAIL_ITEM_T	:= 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T';
		V_MID_TABLE				:= 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T';
		V_DECRYP_AVG_TEMP		:= 'DM_FOC_DMS_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT		:= 'DMS_DM_TOP_BASE_AMT';
		
	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_ITEM_INFO_T';
	    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T';
	    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_IDX_T';
	    V_DECRYP_AVG_TEMP    := 'DM_FOC_ENERGY_DMS_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'DMS_ENERGY_DM_TOP_BASE_AMT';
		
		--202405版本 数字能源新增COA层级
		V_COA_PART			:='COA_CODE,COA_CN_NAME,'; 
		V_SQL_DIFF_COLUMN_PART		:='T2.COA_CODE,T2.COA_CN_NAME,';
		V_JOIN_DIFF_COLUMN_CODE 	:='AND NVL(T1.COA_CODE,''D5'') = NVL(T2.COA_CODE,''D5'') ';
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_TOP_ITEM_INFO_T    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_ITEM_INFO_T';
	    V_BASE_DETAIL_ITEM_T := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T';
	    V_MID_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_IDX_T';
	    V_DECRYP_AVG_TEMP    := 'DM_FOC_IAS_DMS_DECRYP_AVG_TEMP';
		V_DM_TOP_BASE_AMT	 := 'DMS_IAS_DM_TOP_BASE_AMT';
		
		--202407版本 IAS新增LV4层级
		V_LV4_PART						:= 'LV4_PROD_RND_TEAM_CODE,LV4_PROD_RD_TEAM_CN_NAME,';	
		V_SQL_DIFF_COLUMN_PART			:= 'T2.LV4_PROD_RND_TEAM_CODE,T2.LV4_PROD_RD_TEAM_CN_NAME,';
		V_JOIN_DIFF_COLUMN_CODE			:= 'AND NVL(T1.LV4_PROD_RND_TEAM_CODE,4) = NVL(T2.LV4_PROD_RND_TEAM_CODE,4)';
		
      V_SQL_PROD_RND_TEAM_CODE    := '         
      CASE  WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RND_TEAM_CODE
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RND_TEAM_CODE
			WHEN T2.VIEW_FLAG = ''12'' THEN T2.LV4_PROD_RND_TEAM_CODE
         ELSE T2.LV3_PROD_RND_TEAM_CODE
         END AS PROD_RND_TEAM_CODE,';  --202407版本 IAS新增视角12
	  V_SQL_PROD_RND_TEAM_CN_NAME := '
       CASE WHEN T2.VIEW_FLAG IN (''0'',''1'',''2'',''9'')  THEN T2.LV1_PROD_RD_TEAM_CN_NAME
            WHEN T2.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T2.LV2_PROD_RD_TEAM_CN_NAME
			WHEN T2.VIEW_FLAG = ''12'' THEN T2.LV4_PROD_RD_TEAM_CN_NAME
       ELSE T2.LV3_PROD_RD_TEAM_CN_NAME
       END AS PROD_RND_TEAM_CN_NAME,'; --202407版本 IAS新增视角12

	  END IF;
  
    /*字段值定义*/
    V_LV3_PROD_RND_TEAM_CODE   := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_DIMENSION_CODE                := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME             := 'DIMENSION_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE    := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE     := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME  := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DMS_CODE                      := 'DMS_CODE,';
    V_DMS_CN_NAME                   := 'DMS_CN_NAME,';
	--202401版本新增SPART层级
	V_SPART_CODE	:='SPART_CODE,'; 
	V_SPART_CN_NAME :='SPART_CN_NAME,';
  
    V_SQL_LV3_PROD_RND_TEAM_CODE        := 'T2.LV3_PROD_RND_TEAM_CODE,';
    V_SQL_LV3_PROD_RD_TEAM_CN_NAME      := 'T2.LV3_PROD_RD_TEAM_CN_NAME,';
    V_SQL_DIMENSION_CODE                := 'T2.DIMENSION_CODE,';
    V_SQL_DIMENSION_CN_NAME             := 'T2.DIMENSION_CN_NAME,';
    V_SQL_DIMENSION_SUBCATEGORY_CODE    := 'T2.DIMENSION_SUBCATEGORY_CODE,';
    V_SQL_DIMENSION_SUBCATEGORY_CN_NAME := 'T2.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_SQL_DIMENSION_SUB_DETAIL_CODE     := 'T2.DIMENSION_SUB_DETAIL_CODE,';
    V_SQL_DIMENSION_SUB_DETAIL_CN_NAME  := 'T2.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_SQL_DIMENSION_PART                := 'T2.DMS_CODE,T2.DMS_CN_NAME,';
    V_SQL_DMS_CODE   					:= '         
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CODE
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CODE
           WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CODE
		   ELSE T2.SPART_CODE
         END AS DMS_CODE,';  --202401版本新增SPART层级 新增9，10，11视角
    V_SQL_DMS_CN_NAME   				:= '
      CASE WHEN T2.VIEW_FLAG IN (''0'',''3'',''6'') THEN T2.DIMENSION_CN_NAME
           WHEN T2.VIEW_FLAG IN (''1'',''4'',''7'') THEN T2.DIMENSION_SUBCATEGORY_CN_NAME
		   WHEN T2.VIEW_FLAG IN (''2'',''5'',''8'') THEN T2.DIMENSION_SUB_DETAIL_CN_NAME
		   ELSE T2.SPART_CN_NAME
       END AS DMS_CN_NAME,';   --202401版本新增SPART层级 新增9，10，11视角
	--202401版本新增SPART层级 
	V_SQL_SPART_CODE      :='T2.SPART_CODE  , ';
	V_SQL_SPART_CN_NAME   :='T2.SPART_CN_NAME ,';  
  
    /*条件定义*/
    V_JION_LV3_PROD_RND_TEAM_CODE     := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,3) = NVL(T2.LV3_PROD_RND_TEAM_CODE,3)';
    V_JOIN_DIMENSION_CODE             := ' AND NVL(T1.DIMENSION_CODE,''D1'') = NVL(T2.DIMENSION_CODE,''D1'')';
    V_JOIN_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''D2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''D2'')';
    V_JOIN_DIMENSION_SUB_DETAIL_CODE  := ' AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''D3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''D3'')';
    V_JOIN_DMS_CODE                   := ' AND NVL(T1.DMS_CODE,''DD'') = NVL(T2.DMS_CODE,''DD'') ';
	--202401版本新增SPART层级
	V_JOIN_SPART_CODE :='AND NVL(T1.SPART_CODE,''D4'') = NVL(T2.SPART_CODE,''D4'') ';
	
  END IF;
  
  
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '变量判断完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
 
 V_STEP_NUM:=V_STEP_NUM+1;
  
/*关联明细表及TOP维表，提取出需要解密的数据*/
 V_SQL:='   
DROP TABLE IF EXISTS '||V_DM_TOP_BASE_AMT||';
CREATE TEMPORARY TABLE '||V_DM_TOP_BASE_AMT||'(
 PERIOD_YEAR bigint,
 PERIOD_ID bigint,
 LV0_PROD_RND_TEAM_CODE	varchar(50),
 LV0_PROD_RD_TEAM_CN_NAME	varchar(200),
 LV1_PROD_RND_TEAM_CODE	varchar(50),
 LV1_PROD_RD_TEAM_CN_NAME	varchar(200),
 LV2_PROD_RND_TEAM_CODE		varchar(50),
 LV2_PROD_RD_TEAM_CN_NAME	varchar(200),
 LV3_PROD_RND_TEAM_CODE		varchar(50),
 LV3_PROD_RD_TEAM_CN_NAME    varchar(200),
 LV4_PROD_RND_TEAM_CODE		varchar(50),		--202407版本 IAS新增LV4层级
 LV4_PROD_RD_TEAM_CN_NAME    varchar(200),
 L1_NAME 					varchar(200),
 L2_NAME 					varchar(200),
 COA_CODE					varchar(50)  ,		--202405版本 数字能源新增COA层级
 COA_CN_NAME                 varchar(200) , 
 DIMENSION_CODE 					varchar(50)	 ,
 DIMENSION_CN_NAME               varchar(200) ,
 DIMENSION_SUBCATEGORY_CODE		varchar(50)  ,
 DIMENSION_SUBCATEGORY_CN_NAME   varchar(200) ,
 DIMENSION_SUB_DETAIL_CODE		varchar(50)  ,
 DIMENSION_SUB_DETAIL_CN_NAME    varchar(200) ,
 SPART_CODE						varchar(50)  ,
 SPART_CN_NAME		             varchar(200) ,  --202401版本新增SPART层级
 TOP_L3_CEG_CODE				varchar(50)  ,
 TOP_L3_CEG_SHORT_CN_NAME       varchar(200) ,
 TOP_L4_CEG_CODE                varchar(50)  ,
 TOP_L4_CEG_SHORT_CN_NAME       varchar(200) ,
 TOP_CATEGORY_CODE              varchar(50)  ,
 TOP_CATEGORY_CN_NAME           varchar(200) ,
 PROD_RND_TEAM_CODE    varchar(50)  ,
 PROD_RND_TEAM_CN_NAME varchar(200) ,
 PROFITS_NAME varchar(50)  ,
 DMS_CODE  		varchar(50)  ,
 DMS_CN_NAME     varchar(200) ,
 GROUP_CODE		varchar(50)  ,
 GROUP_CN_NAME  varchar(1000) ,
 AVG_AMT varchar(2000) ,
 PARENT_CODE varchar(50) ,
 PARENT_CN_NAME varchar(200) , --202403版本 供前端展示图表查询使用
 VIEW_FLAG varchar(2) ,
 SCENARIO_FLAG varchar(2) ,
 APPEND_FLAG varchar(2) ,
 CALIBER_FLAG varchar(2) ,
 OVERSEA_FLAG varchar(2) ,
 LV0_PROD_LIST_CODE    varchar(50)  ,
 LV0_PROD_LIST_CN_NAME varchar(200) 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PROD_RND_TEAM_CODE);
 
 WITH BASE_AVG AS (
 SELECT VIEW_FLAG,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV1_PROD_RND_TEAM_CODE,
       LV2_PROD_RND_TEAM_CODE,
       '||V_LV3_PROD_RND_TEAM_CODE
	    ||V_LV4_PART			--202407版本 IAS新增LV4层级
  	    ||V_L1_NAME
  	    ||V_L2_NAME
		||V_COA_PART			--202405版本 数字能源新增COA层级
        ||V_DIMENSION_CODE
        ||V_DIMENSION_SUBCATEGORY_CODE
		||V_DIMENSION_SUB_DETAIL_CODE
		||V_SPART_CODE	
		||V_SPART_CN_NAME||'   --202401版本新增SPART层级
       L3_CEG_CODE,
	   L4_CEG_CODE,
       CATEGORY_CODE,
       ITEM_CODE,
       RMB_AVG_AMT AS AVG_AMT,
       SCENARIO_FLAG,
       APPEND_FLAG,
       CALIBER_FLAG,
  	   OVERSEA_FLAG,
  	   LV0_PROD_LIST_CODE
  FROM '||V_BASE_DETAIL_ITEM_T||'
 WHERE PERIOD_YEAR >= (YEAR(CURRENT_DATE) - 2) --202403版本
 ),
 TOP_ITEM AS
 (SELECT VIEW_FLAG,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       '||V_LV3_PROD_RND_TEAM_CODE||
       V_LV3_PROD_RD_TEAM_CN_NAME||
	   V_LV4_PART||			--202407版本 IAS新增LV4层级
       V_L1_NAME||
       V_L2_NAME||
	   V_COA_PART||			--202405版本 数字能源新增COA层级
       V_DIMENSION_CODE||
       V_DIMENSION_CN_NAME||
       V_DIMENSION_SUBCATEGORY_CODE||
       V_DIMENSION_SUBCATEGORY_CN_NAME||
       V_DIMENSION_SUB_DETAIL_CODE||
       V_DIMENSION_SUB_DETAIL_CN_NAME
	   ||V_SPART_CODE	
	   ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
  	   TOP_L4_CEG_CODE,
  	   TOP_L4_CEG_SHORT_CN_NAME,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       TOP_ITEM_CODE,
       TOP_ITEM_CN_NAME,
       CALIBER_FLAG,
  	   OVERSEA_FLAG,  
  	   LV0_PROD_LIST_CODE, 
  	   LV0_PROD_LIST_CN_NAME
  FROM '||V_TOP_ITEM_INFO_T||'
 WHERE VERSION_ID = '||V_VERSION||'
   AND IS_TOP_FLAG =''Y''
   AND DOUBLE_FLAG =''Y'' 
   '||V_RESERVE_SQL||' )
 
 INSERT INTO '||V_DM_TOP_BASE_AMT||'(
	 PERIOD_YEAR,
	 PERIOD_ID,
	 LV0_PROD_RND_TEAM_CODE,
	 LV0_PROD_RD_TEAM_CN_NAME,
	 LV1_PROD_RND_TEAM_CODE,
	 LV1_PROD_RD_TEAM_CN_NAME,
	 LV2_PROD_RND_TEAM_CODE,
	 LV2_PROD_RD_TEAM_CN_NAME,
	'||V_LV3_PROD_RND_TEAM_CODE
	 ||V_LV3_PROD_RD_TEAM_CN_NAME
	 ||V_L1_NAME
	 ||V_L2_NAME
	 ||V_LV4_PART			--202407版本 IAS新增LV4层级
	 ||V_COA_PART			--202405版本 数字能源新增COA层级
	 ||V_DIMENSION_CODE
	 ||V_DIMENSION_CN_NAME
	 ||V_DIMENSION_SUBCATEGORY_CODE
	 ||V_DIMENSION_SUBCATEGORY_CN_NAME
	 ||V_DIMENSION_SUB_DETAIL_CODE
	 ||V_DIMENSION_SUB_DETAIL_CN_NAME
	 ||V_SPART_CODE	
	 ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
	 TOP_L3_CEG_CODE,
	 TOP_L3_CEG_SHORT_CN_NAME,
	 TOP_L4_CEG_CODE,
	 TOP_L4_CEG_SHORT_CN_NAME,
	 TOP_CATEGORY_CODE,
	 TOP_CATEGORY_CN_NAME,
	 PROD_RND_TEAM_CODE,
	 PROD_RND_TEAM_CN_NAME,
	  '||V_PROFITS_NAME||
	  V_DMS_CODE||
	  V_DMS_CN_NAME||' 
	 GROUP_CODE,
	 GROUP_CN_NAME,
	 AVG_AMT,
	 PARENT_CODE,
	 PARENT_CN_NAME,  ----202403版本 供前端展示图表查询使用
	 VIEW_FLAG,
	 SCENARIO_FLAG,
	 APPEND_FLAG,
	 CALIBER_FLAG,
	 OVERSEA_FLAG,  
	 LV0_PROD_LIST_CODE,
	 LV0_PROD_LIST_CN_NAME
 )
 SELECT T1.PERIOD_YEAR,
        T1.PERIOD_ID,
        T2.LV0_PROD_RND_TEAM_CODE,
        T2.LV0_PROD_RD_TEAM_CN_NAME,
        T2.LV1_PROD_RND_TEAM_CODE,
        T2.LV1_PROD_RD_TEAM_CN_NAME,
        T2.LV2_PROD_RND_TEAM_CODE,
        T2.LV2_PROD_RD_TEAM_CN_NAME,
       '||V_SQL_LV3_PROD_RND_TEAM_CODE
        ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
        ||V_SQL_L1_NAME
        ||V_SQL_L2_NAME
		||V_SQL_DIFF_COLUMN_PART			--202405版本 数字能源新增COA层级 202407版本 IAS新增LV4层级
        ||V_SQL_DIMENSION_CODE
        ||V_SQL_DIMENSION_CN_NAME
        ||V_SQL_DIMENSION_SUBCATEGORY_CODE
        ||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
        ||V_SQL_DIMENSION_SUB_DETAIL_CODE
        ||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SQL_SPART_CODE	
		||V_SQL_SPART_CN_NAME||'   --202401版本新增SPART层级
       T2.TOP_L3_CEG_CODE,
       T2.TOP_L3_CEG_SHORT_CN_NAME,
  	   T2.TOP_L4_CEG_CODE,
  	   T2.TOP_L4_CEG_SHORT_CN_NAME,
       T2.TOP_CATEGORY_CODE,
       T2.TOP_CATEGORY_CN_NAME,
       '||V_SQL_PROD_RND_TEAM_CODE||
        V_SQL_PROD_RND_TEAM_CN_NAME||
        V_SQL_PROFITS_NAME||
    	V_SQL_DMS_CODE||
    	V_SQL_DMS_CN_NAME||' 
       T2.TOP_ITEM_CODE AS GROUP_CODE,
       T2.TOP_ITEM_CN_NAME AS GROUP_CN_NAME,
       T1.AVG_AMT,
       T2.TOP_CATEGORY_CODE AS PARENT_CODE,
	   T2.TOP_CATEGORY_CN_NAME AS PARENT_CN_NAME, --202403版本 供前端展示图表查询使用
       T2.VIEW_FLAG,
       T1.SCENARIO_FLAG,
       T1.APPEND_FLAG,
       T2.CALIBER_FLAG,
	   T2.OVERSEA_FLAG,  
	   T2.LV0_PROD_LIST_CODE,
	   T2.LV0_PROD_LIST_CN_NAME
  FROM BASE_AVG T1
 INNER JOIN TOP_ITEM T2
    ON T1.VIEW_FLAG = T2.VIEW_FLAG
   AND T1.LV0_PROD_RND_TEAM_CODE = T2.LV0_PROD_RND_TEAM_CODE
   AND NVL(T1.LV1_PROD_RND_TEAM_CODE, 1) =
       NVL(T2.LV1_PROD_RND_TEAM_CODE, 1)
   AND NVL(T1.LV2_PROD_RND_TEAM_CODE, 2) =
       NVL(T2.LV2_PROD_RND_TEAM_CODE, 2)
	   '||V_JION_LV3_PROD_RND_TEAM_CODE
	    ||V_JOIN_L1_NAME
	    ||V_JOIN_L2_NAME
		||V_JOIN_DIFF_COLUMN_CODE		--202405版本 数字能源新增COA层级
	    ||V_JOIN_DIMENSION_CODE
	    ||V_JOIN_DIMENSION_SUBCATEGORY_CODE
	    ||V_JOIN_DIMENSION_SUB_DETAIL_CODE
		||V_JOIN_SPART_CODE||'   --202401版本新增SPART层级
   AND T1.L3_CEG_CODE = T2.TOP_L3_CEG_CODE
   AND T1.L4_CEG_CODE = T2.TOP_L4_CEG_CODE
   AND T1.CATEGORY_CODE = T2.TOP_CATEGORY_CODE
   AND T1.ITEM_CODE = T2.TOP_ITEM_CODE
   AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG  
   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE ;';

DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '规格品均本明细筛取完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
 
 V_STEP_NUM:=V_STEP_NUM+1;

V_SQL := '
DROP TABLE IF EXISTS '||V_DECRYP_AVG_TEMP||';
CREATE TEMPORARY TABLE '||V_DECRYP_AVG_TEMP||'(
 PERIOD_YEAR bigint,
 PERIOD_ID bigint,
 LV0_PROD_RND_TEAM_CODE	varchar(50),
 LV0_PROD_RD_TEAM_CN_NAME	varchar(200),
 LV1_PROD_RND_TEAM_CODE	varchar(50),
 LV1_PROD_RD_TEAM_CN_NAME	varchar(200),
 LV2_PROD_RND_TEAM_CODE		varchar(50),
 LV2_PROD_RD_TEAM_CN_NAME	varchar(200),
 LV3_PROD_RND_TEAM_CODE		varchar(50),
 LV3_PROD_RD_TEAM_CN_NAME    varchar(200),
 LV4_PROD_RND_TEAM_CODE		varchar(50),		--202407版本 IAS新增LV4层级
 LV4_PROD_RD_TEAM_CN_NAME    varchar(200),
 L1_NAME 					varchar(200),
 L2_NAME 					varchar(200),
 DIMENSION_CODE 					varchar(50)	 ,
 DIMENSION_CN_NAME               varchar(200) ,
 DIMENSION_SUBCATEGORY_CODE		varchar(50)  ,
 DIMENSION_SUBCATEGORY_CN_NAME   varchar(200) ,
 DIMENSION_SUB_DETAIL_CODE		varchar(50)  ,
 DIMENSION_SUB_DETAIL_CN_NAME    varchar(200) ,
 COA_CODE						varchar(50)  ,
 COA_CN_NAME                     varchar(200) ,	--202405版本 数字能源新增COA层级
 SPART_CODE						varchar(50)  ,
 SPART_CN_NAME   				 varchar(200) ,	--202401版本新增SPART层级
 TOP_L3_CEG_CODE				varchar(50)  ,
 TOP_L3_CEG_SHORT_CN_NAME       varchar(200) ,
 TOP_L4_CEG_CODE                varchar(50)  ,
 TOP_L4_CEG_SHORT_CN_NAME       varchar(200) ,
 TOP_CATEGORY_CODE              varchar(50)  ,
 TOP_CATEGORY_CN_NAME           varchar(200) ,
 PROD_RND_TEAM_CODE    varchar(50)  ,
 PROD_RND_TEAM_CN_NAME varchar(200) ,
 PROFITS_NAME varchar(50)  ,
 DMS_CODE  		varchar(50)  ,
 DMS_CN_NAME     varchar(200) ,
 GROUP_CODE		varchar(50)  ,
 GROUP_CN_NAME  varchar(1000) ,
 AVG_AMT numeric ,
 PARENT_CODE varchar(50) ,
 PARENT_CN_NAME VARCHAR(200), --202403版本 供前端展示图表查询使用
 VIEW_FLAG varchar(2) ,
 SCENARIO_FLAG varchar(2) ,
 APPEND_FLAG varchar(2) ,
 CALIBER_FLAG varchar(2) ,
 OVERSEA_FLAG varchar(2) ,
 LV0_PROD_LIST_CODE    varchar(50)  ,
 LV0_PROD_LIST_CN_NAME varchar(200) 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PROD_RND_TEAM_CODE);

INSERT INTO '||V_DECRYP_AVG_TEMP||'
  (PERIOD_YEAR,								
   PERIOD_ID,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   '||V_LV3_PROD_RND_TEAM_CODE
   ||V_LV3_PROD_RD_TEAM_CN_NAME
   ||V_LV4_PART			--202407版本 IAS新增LV4层级
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_COA_PART			--202405版本 数字能源新增COA层级
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_SPART_CODE	
   ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,
   TOP_L4_CEG_CODE,
   TOP_L4_CEG_SHORT_CN_NAME,
   TOP_CATEGORY_CODE,
   TOP_CATEGORY_CN_NAME,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '|| V_PROFITS_NAME||
    V_DMS_CODE||
    V_DMS_CN_NAME||'
   GROUP_CODE,
   GROUP_CN_NAME,
   AVG_AMT,
   PARENT_CODE,
   PARENT_CN_NAME,  --202403版本 供前端展示图表查询使用
   VIEW_FLAG,
   SCENARIO_FLAG,
   APPEND_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME)
SELECT PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,
       LV1_PROD_RND_TEAM_CODE,
       LV1_PROD_RD_TEAM_CN_NAME,
       LV2_PROD_RND_TEAM_CODE,
       LV2_PROD_RD_TEAM_CN_NAME,
       '||V_LV3_PROD_RND_TEAM_CODE
       ||V_LV3_PROD_RD_TEAM_CN_NAME
	   ||V_LV4_PART			--202407版本 IAS新增LV4层级
       ||V_L1_NAME
       ||V_L2_NAME
	   ||V_COA_PART			--202405版本 数字能源新增COA层级
       ||V_DIMENSION_CODE
       ||V_DIMENSION_CN_NAME
       ||V_DIMENSION_SUBCATEGORY_CODE
       ||V_DIMENSION_SUBCATEGORY_CN_NAME
       ||V_DIMENSION_SUB_DETAIL_CODE
       ||V_DIMENSION_SUB_DETAIL_CN_NAME
	   ||V_SPART_CODE	
	   ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
       TOP_L3_CEG_CODE,
       TOP_L3_CEG_SHORT_CN_NAME,
       TOP_L4_CEG_CODE,
       TOP_L4_CEG_SHORT_CN_NAME,
       TOP_CATEGORY_CODE,
       TOP_CATEGORY_CN_NAME,
       PROD_RND_TEAM_CODE,
	   PROD_RND_TEAM_CN_NAME,
       '||V_PROFITS_NAME||
          V_DMS_CODE||
          V_DMS_CN_NAME||' 
       GROUP_CODE,
       GROUP_CN_NAME,
       TO_NUMBER(GS_DECRYPT(AVG_AMT,
                            '''||V_KEYSTR||''',
                            ''aes128'',
                            ''cbc'',
                            ''sha256'')) AS AVG_AMT,
       PARENT_CODE,
	   PARENT_CN_NAME, --202403版本 供前端展示图表查询使用
       VIEW_FLAG,
       SCENARIO_FLAG,
       APPEND_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_DM_TOP_BASE_AMT||';';
 
 DBMS_OUTPUT.PUT_LINE(V_SQL);
 EXECUTE IMMEDIATE V_SQL;
 
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '规格品指数数据解密完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
 
 V_STEP_NUM:=V_STEP_NUM+1;
 V_SQL:='
 TRUNCATE TABLE '||V_MID_TABLE||';
 
WITH BASE_PERIOD_AVG AS
 (--取基期均本
SELECT VIEW_FLAG,
       PROD_RND_TEAM_CODE,
       '||V_PROFITS_NAME||V_L1_NAME||V_L2_NAME
	    ||V_DMS_CODE||V_DMS_CN_NAME
		||V_DIMENSION_CODE||V_LV4_PART	--202407版本 IAS新增LV4层级
		||V_COA_PART			--202405版本 数字能源新增COA层级
		||V_DIMENSION_SUBCATEGORY_CODE
		||V_DIMENSION_SUB_DETAIL_CODE
		||V_SPART_CODE||'   --202401版本新增SPART层级
       GROUP_CODE,
       NULLIF(AVG_AMT, 0) AS AVG_AMT,
       CALIBER_FLAG,
	   OVERSEA_FLAG,  
	   LV0_PROD_LIST_CODE,
	   LV0_PROD_LIST_CN_NAME
  FROM '||V_DECRYP_AVG_TEMP||'
 WHERE PERIOD_ID = '||V_BASE_PERIOD_ID||')
 
--规格品层级指数计算   
INSERT INTO '||V_MID_TABLE||'
  (PERIOD_YEAR,
   PERIOD_ID,
   VERSION_ID,
   BASE_PERIOD_ID,
   VIEW_FLAG,
   LV0_PROD_RND_TEAM_CODE,
   LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE,
   LV1_PROD_RD_TEAM_CN_NAME,
   LV2_PROD_RND_TEAM_CODE,
   LV2_PROD_RD_TEAM_CN_NAME,
   '||V_LV3_PROD_RND_TEAM_CODE
   ||V_LV3_PROD_RD_TEAM_CN_NAME
   ||V_L1_NAME
   ||V_L2_NAME
   ||V_LV4_PART			--202407版本 IAS新增LV4层级
   ||V_COA_PART			--202405版本 数字能源新增COA层级
   ||V_DIMENSION_CODE
   ||V_DIMENSION_CN_NAME
   ||V_DIMENSION_SUBCATEGORY_CODE
   ||V_DIMENSION_SUBCATEGORY_CN_NAME
   ||V_DIMENSION_SUB_DETAIL_CODE
   ||V_DIMENSION_SUB_DETAIL_CN_NAME
   ||V_SPART_CODE	
   ||V_SPART_CN_NAME||'   --202401版本新增SPART层级
   TOP_L3_CEG_CODE,
   TOP_L3_CEG_SHORT_CN_NAME,
   TOP_L4_CEG_CODE,
   TOP_L4_CEG_SHORT_CN_NAME,
   TOP_CATEGORY_CODE,
   TOP_CATEGORY_CN_NAME,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_PROFITS_NAME||
    V_DMS_CODE||
    V_DMS_CN_NAME||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   COST_INDEX,
   PARENT_CODE,
   PARENT_CN_NAME, --202403版本 供前端展示图表查询使用
   SCENARIO_FLAG,
   APPEND_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE ,
   LV0_PROD_LIST_CN_NAME)
SELECT T2.PERIOD_YEAR,
       T2.PERIOD_ID,
       '||V_VERSION||' AS VERSION_ID,
       '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
       T2.VIEW_FLAG,
       T2.LV0_PROD_RND_TEAM_CODE,
       T2.LV0_PROD_RD_TEAM_CN_NAME,
       T2.LV1_PROD_RND_TEAM_CODE,
       T2.LV1_PROD_RD_TEAM_CN_NAME,
       T2.LV2_PROD_RND_TEAM_CODE,
       T2.LV2_PROD_RD_TEAM_CN_NAME,
       '||V_SQL_LV3_PROD_RND_TEAM_CODE
        ||V_SQL_LV3_PROD_RD_TEAM_CN_NAME
        ||V_SQL_L1_NAME
        ||V_SQL_L2_NAME
		||V_SQL_DIFF_COLUMN_PART			--202405版本 数字能源新增COA层级 202407版本 IAS新增LV4层级
    	||V_SQL_DIMENSION_CODE 
    	||V_SQL_DIMENSION_CN_NAME
    	||V_SQL_DIMENSION_SUBCATEGORY_CODE 
    	||V_SQL_DIMENSION_SUBCATEGORY_CN_NAME
    	||V_SQL_DIMENSION_SUB_DETAIL_CODE 
    	||V_SQL_DIMENSION_SUB_DETAIL_CN_NAME
		||V_SQL_SPART_CODE	
		||V_SQL_SPART_CN_NAME||'   --202401版本新增SPART层级
       T2.TOP_L3_CEG_CODE,
       T2.TOP_L3_CEG_SHORT_CN_NAME,
  	   T2.TOP_L4_CEG_CODE,
  	   T2.TOP_L4_CEG_SHORT_CN_NAME, 
       T2.TOP_CATEGORY_CODE,
       T2.TOP_CATEGORY_CN_NAME,
       T2.PROD_RND_TEAM_CODE,
       T2.PROD_RND_TEAM_CN_NAME,
       '||V_SQL_DIMENSION_PART||' /*盈利颗粒度为盈利NAME，量纲颗粒度为量纲CODE与量纲NAME*/
       T2.GROUP_CODE,
       T2.GROUP_CN_NAME,
       ''ITEM'' AS GROUP_LEVEL,
       (T2.AVG_AMT / NULLIF(T1.AVG_AMT,0)*100) AS COST_INDEX,
       T2.TOP_CATEGORY_CODE AS PARENT_CODE,
	   T2.TOP_CATEGORY_CN_NAME AS PARENT_CN_NAME, --202403版本 供前端展示图表查询使用
       T2.SCENARIO_FLAG,
       T2.APPEND_FLAG,
       ''-1'' AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       T2.CALIBER_FLAG,
  	   T2.OVERSEA_FLAG,  
  	   T2.LV0_PROD_LIST_CODE, 
  	   T2.LV0_PROD_LIST_CN_NAME
  FROM '||V_DECRYP_AVG_TEMP||' T2
  JOIN BASE_PERIOD_AVG T1
    ON T1.VIEW_FLAG = T2.VIEW_FLAG
   AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
   '||V_JOIN_PROFITS_NAME|| V_JOIN_L1_NAME|| V_JOIN_L2_NAME|| V_JOIN_DMS_CODE||
    V_JOIN_DIMENSION_CODE||
	V_JOIN_DIMENSION_SUBCATEGORY_CODE||
	V_JOIN_DIMENSION_SUB_DETAIL_CODE||
	V_JOIN_DIFF_COLUMN_CODE||		--202405版本 数字能源新增COA层级
	V_JOIN_SPART_CODE||'   --202401版本新增SPART层级
   AND T1.GROUP_CODE = T2.GROUP_CODE
   AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
   AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE;
 ';
 
 
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;
	 
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '月度分析-规格品指数插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

