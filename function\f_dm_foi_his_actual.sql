-- Name: f_dm_foi_his_actual; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_his_actual(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建人  ：唐钦
背景；ICT/数字能源实际数补齐
参数描述：x_result_status ：是否成功
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOI_HIS_ACTUAL()
*/

DECLARE
  V_SP_NAME VARCHAR2(500):= 'FIN_DM_OPT_FOI.F_DM_FOI_HIS_ACTUAL';
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM'); 
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(200);
  V_TO_TABLE VARCHAR(200);
  V_JOIN_TABLE VARCHAR(200);
  V_STEP_NUM INT := 0;
  V_VERSION_ID INT := F_VERSION_ID;
  V_CURRENT_FLAG INT;
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(300);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_VERSION_TABLE VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS:= 1;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --判断入参是ICT还是数字能源
  IF f_caliber_flag = 'I' THEN 
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T';
    V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T';
    
    IF F_VERSION_ID IS  NULL  THEN 
        SELECT VERSION_ID INTO V_VERSION_ID
           FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T
           WHERE DEL_FLAG = 'N'
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = UPPER('ITEM')
           AND UPPER(VERSION_TYPE) IN ('AUTO', 'FINAL')
           ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE 
        V_VERSION_ID = F_VERSION_ID;
    END IF;
    
  ELSIF f_caliber_flag = 'E' THEN 
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T';
    V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_TOP_ITEM_INFO_T';
  ELSIF f_caliber_flag IN ('IAS','EAST_CHINA_PQC') THEN 
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T';
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ITEM_SUP_AVG_T';
    V_JOIN_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_TOP_ITEM_INFO_T';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    V_SQL_CONDITION := 'AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
    END IF;
  END IF;
  
  IF F_CALIBER_FLAG <> 'I' THEN
  --判断入参版本号
  IF F_VERSION_ID IS  NULL  THEN 
    V_SQL := '
        SELECT VERSION_ID 
           FROM '||V_VERSION_TABLE||'
           WHERE DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = UPPER(''ITEM'')
           AND UPPER(VERSION_TYPE) IN (''AUTO'', ''FINAL'')
           ORDER BY LAST_UPDATE_DATE DESC LIMIT 1';
            EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE 
    V_VERSION_ID = F_VERSION_ID;
  END IF;
END IF;

  --1.清空实际数补齐表的数据
  IF F_CALIBER_FLAG IN ('I','E') THEN
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  ELSE 
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
  END IF;
  
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => '领域标识为：'||F_CALIBER_FLAG||'，清空实际数补齐表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   --创建临时表
    DROP TABLE IF EXISTS BASE_DATA_TEMP;
    CREATE TEMPORARY TABLE BASE_DATA_TEMP (
        YEAR INTEGER,
        PERIOD_ID INTEGER,
        SUPPLIER_CODE CHARACTER VARYING(50),
        SUPPLIER_CN_NAME CHARACTER VARYING(500),
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_NAME CHARACTER VARYING(500),
        CATEGORY_CODE CHARACTER VARYING(50),
        CATEGORY_NAME CHARACTER VARYING(200),
        L4_CEG_CODE CHARACTER VARYING(100),
        L4_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
        L4_CEG_CN_NAME CHARACTER VARYING(255),
        L3_CEG_CODE CHARACTER VARYING(100),
        L3_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
        L3_CEG_CN_NAME CHARACTER VARYING(255),
        L2_CEG_CODE CHARACTER VARYING(50),
        L2_CEG_CN_NAME CHARACTER VARYING(200),
        GROUP_CODE CHARACTER VARYING(50),
        GROUP_CN_NAME CHARACTER VARYING(500),
        GROUP_LEVEL CHARACTER VARYING(50),
        AVG_RECEIVE_AMT NUMERIC,
        RECEIVE_QTY NUMERIC,
        RECEIVE_AMT_CNY NUMERIC,
        PARENT_LEVEL CHARACTER VARYING(50),
        PARENT_CODE CHARACTER VARYING(50),
        PARENT_CN_NAME CHARACTER VARYING(500),
        TOP_FLAG CHARACTER VARYING(2),
        APPEND_FLAG CHARACTER VARYING(2),
        DEL_FLAG CHARACTER VARYING(2)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);

    --写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => '临时表创建成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    
   --数字能源下，插入item-品类层的数据
    V_SQL:= 'INSERT INTO BASE_DATA_TEMP (
             YEAR ,
             PERIOD_ID ,
             SUPPLIER_CODE ,
             SUPPLIER_CN_NAME ,
             ITEM_CODE ,
             ITEM_NAME ,
             CATEGORY_CODE ,
             CATEGORY_NAME ,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             L2_CEG_CODE ,
             L2_CEG_CN_NAME ,
             GROUP_CODE ,
             GROUP_CN_NAME ,
             GROUP_LEVEL ,
             AVG_RECEIVE_AMT ,
             RECEIVE_QTY ,
             RECEIVE_AMT_CNY ,
             PARENT_LEVEL ,
             PARENT_CODE ,
             PARENT_CN_NAME ,
             TOP_FLAG ,
             APPEND_FLAG ,
             DEL_FLAG 
             )
             SELECT 
             DISTINCT 
             YEAR ,
             PERIOD_ID ,
             NULL AS SUPPLIER_CODE ,
             NULL AS SUPPLIER_CN_NAME ,
             ITEM_CODE ,
             ITEM_NAME ,
             CATEGORY_CODE ,
             CATEGORY_NAME ,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             L2_CEG_CODE ,
             L2_CEG_CN_NAME ,
             ITEM_CODE AS GROUP_CODE ,
             ITEM_NAME AS GROUP_CN_NAME ,
             ''ITEM'' GROUP_LEVEL ,
             CASE WHEN NVL(SUM(RECEIVE_QTY),0) = 0 THEN 0 
             ELSE NVL(SUM(RECEIVE_AMT_CNY),0) / NULLIF(SUM(RECEIVE_QTY),0)END AS AVG_RECEIVE_AMT ,
             NVL(SUM(RECEIVE_QTY),0) AS  RECEIVE_QTY,
             NVL(SUM(RECEIVE_AMT_CNY),0) AS  RECEIVE_AMT_CNY,
             ''CATEGORY'' AS PARENT_LEVEL ,
             CATEGORY_CODE AS PARENT_CODE ,
             CATEGORY_NAME AS PARENT_CN_NAME ,
             NULL AS TOP_FLAG,
             ''N'' AS APPEND_FLAG ,
             ''N'' AS DEL_FLAG
             FROM '||V_FROM_TABLE||' T1
             WHERE DEL_FLAG = ''N''
             '||V_SQL_CONDITION||'
             GROUP BY 
             YEAR,
             PERIOD_ID,
             ITEM_CODE,
             ITEM_NAME,
             CATEGORY_CODE,
             CATEGORY_NAME,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             '||V_CALIBER||'
             L2_CEG_CODE ,
             L2_CEG_CN_NAME' 
             ;
            EXECUTE IMMEDIATE V_SQL;

    --写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => '品类-item层数据已插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
    --再插入一份供应商-item的数据
    V_SQL:= 'INSERT INTO BASE_DATA_TEMP (
             YEAR ,
             PERIOD_ID ,
             SUPPLIER_CODE ,
             SUPPLIER_CN_NAME ,
             ITEM_CODE ,
             ITEM_NAME ,
             CATEGORY_CODE ,
             CATEGORY_NAME ,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             L2_CEG_CODE ,
             L2_CEG_CN_NAME ,
             GROUP_CODE ,
             GROUP_CN_NAME ,
             GROUP_LEVEL ,
             AVG_RECEIVE_AMT ,
             RECEIVE_QTY ,
             RECEIVE_AMT_CNY ,
             PARENT_LEVEL ,
             PARENT_CODE ,
             PARENT_CN_NAME ,
             TOP_FLAG ,
             APPEND_FLAG ,
             DEL_FLAG 
             )
             SELECT DISTINCT 
             YEAR ,
             PERIOD_ID ,
             SUPPLIER_CODE ,
             SUPPLIER_CN_NAME ,
             ITEM_CODE ,
             ITEM_NAME ,
             CATEGORY_CODE ,
             CATEGORY_NAME ,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             L2_CEG_CODE ,
             L2_CEG_CN_NAME ,
             SUPPLIER_CODE AS GROUP_CODE ,
             SUPPLIER_CN_NAME AS GROUP_CN_NAME ,
             ''SUPPLIER'' AS GROUP_LEVEL ,
             CASE WHEN NVL(SUM(RECEIVE_QTY),0) = 0 THEN 0 
             ELSE NVL(SUM(RECEIVE_AMT_CNY),0) / NULLIF(SUM(RECEIVE_QTY),0) END AS AVG_RECEIVE_AMT ,
             NVL(SUM(RECEIVE_QTY),0) AS  RECEIVE_QTY,
             NVL(SUM(RECEIVE_AMT_CNY),0) AS  RECEIVE_AMT_CNY ,
             ''ITEM'' AS PARENT_LEVEL ,
             ITEM_CODE AS PARENT_CODE ,
             ITEM_NAME AS PARENT_CN_NAME ,
             NULL  AS TOP_FLAG ,
             ''N'' AS APPEND_FLAG ,
             ''N'' AS DEL_FLAG
             FROM '||V_FROM_TABLE||' 
             WHERE DEL_FLAG = ''N''
             '||V_SQL_CONDITION||'
             GROUP BY 
             YEAR,
             PERIOD_ID,
             SUPPLIER_CODE,
             SUPPLIER_CN_NAME,
             ITEM_CODE,
             ITEM_NAME,
             CATEGORY_CODE,
             CATEGORY_NAME,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             '||V_CALIBER||'
             L2_CEG_CODE ,
             L2_CEG_CN_NAME' 
             ;
            EXECUTE IMMEDIATE V_SQL;
            
      --写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => 'item-供应商层数据已插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

    --2.插入ITEM-品类新补齐的均价实际数
  V_SQL:= 'INSERT INTO '||V_TO_TABLE||'
    (         
             VERSION_ID,
             YEAR ,
             PERIOD_ID ,
             SUPPLIER_CODE ,
             SUPPLIER_CN_NAME ,
             ITEM_CODE ,
             ITEM_NAME ,
             CATEGORY_CODE ,
             CATEGORY_NAME ,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             L2_CEG_CODE ,
             L2_CEG_CN_NAME ,
             GROUP_CODE ,
             GROUP_CN_NAME ,
             GROUP_LEVEL ,
             AVG_RECEIVE_AMT ,
             PARENT_LEVEL ,
             PARENT_CODE ,
             PARENT_CN_NAME ,
             TOP_FLAG ,
             APPEND_FLAG ,
             SCENARIO_FLAG,
             '||V_CALIBER||'
             CREATED_BY ,
             CREATION_DATE ,
             LAST_UPDATED_BY ,
             LAST_UPDATE_DATE ,
             DEL_FLAG )
    WITH CATEGORY_ITEM_TEMP AS
     (
      --历史表里出现的品类,item, 取数范围: (三年前第1月)至当前系统月(不含)  
      SELECT DISTINCT  
                       T.ITEM_CODE,
                       T.ITEM_NAME,
                       T.CATEGORY_CODE,
                       T.CATEGORY_NAME,
                       T.L4_CEG_CODE ,
                       T.L4_CEG_SHORT_CN_NAME ,
                       T.L4_CEG_CN_NAME ,
                       T.L3_CEG_CODE ,
                       T.L3_CEG_SHORT_CN_NAME ,
                       T.L3_CEG_CN_NAME ,
                       T.L2_CEG_CODE ,
                       T.L2_CEG_CN_NAME ,
                       T.GROUP_CODE ,
                       T.GROUP_CN_NAME ,
                       T.GROUP_LEVEL ,
                       T.PARENT_LEVEL ,
                       T.PARENT_CODE ,
                       T.PARENT_CN_NAME 
        FROM BASE_DATA_TEMP T
       WHERE T.YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
         AND T.PERIOD_ID < TO_NUMBER(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM''))
         AND T.PARENT_LEVEL = ''CATEGORY''
         ),
    
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 202101至当前系统实际月, (当前系统实际月 = 当前系统月-1)
      SELECT TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''', NUM.VAL - 1),
                      ''YYYYMM'') AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''
                                                      ||V_BEGIN_DATE||''',
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
    
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的品类,ITEM维
      SELECT  
              A.CATEGORY_CODE,
              A.ITEM_CODE,
              A.L4_CEG_CODE ,
              A.L3_CEG_CODE ,
              A.L2_CEG_CODE ,
              A.GROUP_CODE,
              A.GROUP_LEVEL,
              A.PARENT_CODE,
              A.PARENT_LEVEL,
              SUBSTR(B.PERIOD_ID, 1, 4) AS YEAR,
              B.PERIOD_ID
        FROM CATEGORY_ITEM_TEMP A, PERIOD_DIM_TEMP B),
    ITEM_SUM_TEMP AS (
         SELECT T.PERIOD_ID,
                T.ITEM_CODE,
                T.CATEGORY_CODE,
                T.L4_CEG_CODE ,
                T.L3_CEG_CODE ,
                T.L2_CEG_CODE ,
                T.GROUP_CODE ,
                T.GROUP_LEVEL ,
                T.PARENT_LEVEL ,
                T.PARENT_CODE ,
                T.AVG_RECEIVE_AMT AS AVG_AMT,
                ''N'' AS APD_FLAG,
                TOP_FLAG
            FROM BASE_DATA_TEMP T 
            WHERE  T.PARENT_LEVEL = ''CATEGORY''
            ),
    FORWARD_FILLER_TEMP AS
     (
      --按照品类,ITEM组, 向前寻找会计期补齐均价
        SELECT  
              SS.CATEGORY_CODE,
              SS.ITEM_CODE,
              SS.L4_CEG_CODE ,
              SS.L3_CEG_CODE ,
              SS.L2_CEG_CODE ,
              SS.GROUP_CODE ,
              SS.GROUP_LEVEL ,
              SS.PARENT_LEVEL ,
              SS.PARENT_CODE ,
              SS.YEAR,
              SS.PERIOD_ID,
              SS.AVG_AMT,
              FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.L4_CEG_CODE ,SS.L3_CEG_CODE ,SS.L2_CEG_CODE ,SS.CATEGORY_CODE, SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
              SS.AVG_AMT_FLAG,
              SS.APD_FLAG,
              SS.TOP_FLAG
        FROM (SELECT 
                      S.CATEGORY_CODE,
                      S.ITEM_CODE,
                      S.L4_CEG_CODE ,
                      S.L3_CEG_CODE ,
                      S.L2_CEG_CODE ,
                      S.GROUP_CODE ,
                      S.GROUP_LEVEL ,
                      S.PARENT_LEVEL ,
                      S.PARENT_CODE ,
                      S.YEAR,
                      S.PERIOD_ID,
                      S.AVG_AMT,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.L4_CEG_CODE ,S.L3_CEG_CODE ,S.L2_CEG_CODE ,S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      S.APD_FLAG,
                      S.TOP_FLAG
                 FROM (SELECT T1.CATEGORY_CODE,
                              T1.ITEM_CODE,
                              T1.L4_CEG_CODE ,
                              T1.L3_CEG_CODE ,
                              T1.L2_CEG_CODE ,
                              T1.GROUP_CODE ,
                              T1.GROUP_LEVEL ,
                              T1.PARENT_LEVEL ,
                              T1.PARENT_CODE ,
                              T1.YEAR,
                              T1.PERIOD_ID,
                              T2.AVG_AMT,
                              DECODE(T2.AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
                              NVL(T2.APD_FLAG, ''Y'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始存在
                              NVL(T2.TOP_FLAG,''N'') AS TOP_FLAG  --是否top标识后面的update里只更新了Y
                         FROM CROSS_JOIN_TEMP T1
                         LEFT JOIN ITEM_SUM_TEMP T2
                           ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
                          AND T1.ITEM_CODE = T2.ITEM_CODE
                          AND T1.PERIOD_ID = T2.PERIOD_ID
                          AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
                          AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
                          AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
                          ) S) SS)
    --向后补齐均价
        SELECT '||V_VERSION_ID||',
           S.YEAR,
           S.PERIOD_ID,
           NULL AS SUPPLIER_CODE,
           NULL AS SUPPLIER_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_NAME,
           S.CATEGORY_CODE,
           S.CATEGORY_NAME,
           S.L4_CEG_CODE ,
           S.L4_CEG_SHORT_CN_NAME ,
           S.L4_CEG_CN_NAME ,
           S.L3_CEG_CODE ,
           S.L3_CEG_SHORT_CN_NAME ,
           S.L3_CEG_CN_NAME ,
           S.L2_CEG_CODE ,
           S.L2_CEG_CN_NAME ,
           S.GROUP_CODE ,
           S.GROUP_CN_NAME ,
           S.GROUP_LEVEL ,
           NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS RECEIVE_AMT_CNY,
           S.PARENT_LEVEL ,
           S.PARENT_CODE ,
           S.PARENT_CN_NAME,
           S.TOP_FLAG,
           S.APD_FLAG AS APPEND_FLAG,
           ''S'' AS SCENARIO_FLAG,
           '||V_IN_CALIBER||'
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM (SELECT T1.CATEGORY_CODE,
                   T3.CATEGORY_NAME,
                   T1.ITEM_CODE,
                   T3.ITEM_NAME,
                   T1.L4_CEG_CODE ,
                   T3.L4_CEG_SHORT_CN_NAME ,
                   T3.L4_CEG_CN_NAME ,
                   T1.L3_CEG_CODE ,
                   T3.L3_CEG_SHORT_CN_NAME ,
                   T3.L3_CEG_CN_NAME ,
                   T1.L2_CEG_CODE ,
                   T3.L2_CEG_CN_NAME ,
                   T1.GROUP_CODE,
                   T3.GROUP_CN_NAME ,
                   T1.GROUP_LEVEL,
                   T1.PARENT_LEVEL ,
                   T1.PARENT_CODE ,
                   T3.PARENT_CN_NAME,
                   T1.YEAR,
                   T1.PERIOD_ID,
                   T1.AVG_AMT_2,
                   T2.AVG_AMT_3,
                   T1.APD_FLAG,
                   T1.TOP_FLAG
              FROM FORWARD_FILLER_TEMP T1
              LEFT JOIN (SELECT DISTINCT S.CATEGORY_CODE,
                                        S.ITEM_CODE,
                                        S.L4_CEG_CODE ,
                                        S.L3_CEG_CODE ,
                                        S.L2_CEG_CODE ,
                                        S.GROUP_CODE,
                                        S.GROUP_LEVEL,
                                        S.PARENT_LEVEL ,
                                        S.PARENT_CODE ,
                                        FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY S.L4_CEG_CODE ,S.L3_CEG_CODE ,S.L2_CEG_CODE ,S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(S.AVG_AMT_2) OVER(PARTITION BY S.L4_CEG_CODE ,S.L3_CEG_CODE ,S.L2_CEG_CODE ,S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP S
                         WHERE S.AVG_AMT_FLAG > 0) T2 
                ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
               AND T1.ITEM_CODE = T2.ITEM_CODE
               AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
               AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
               AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
               AND T1.PERIOD_ID < T2.PERIOD_ID               
              LEFT JOIN CATEGORY_ITEM_TEMP T3 --关联带出item, 品类的名称信息
                ON T1.CATEGORY_CODE = T3.CATEGORY_CODE
               AND T1.ITEM_CODE = T3.ITEM_CODE
               AND T1.L4_CEG_CODE = T3.L4_CEG_CODE
               AND T1.L3_CEG_CODE = T3.L3_CEG_CODE
               AND T1.L2_CEG_CODE = T3.L2_CEG_CODE
               ) S'
               ;
    EXECUTE IMMEDIATE V_SQL;
    
  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => '插入ITEM-品类新补齐的均价实际数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
    V_SQL:= 'INSERT INTO '||V_TO_TABLE||'
    (        
            VERSION_ID,
            YEAR ,
             PERIOD_ID ,
             SUPPLIER_CODE ,
             SUPPLIER_CN_NAME ,
             ITEM_CODE ,
             ITEM_NAME ,
             CATEGORY_CODE ,
             CATEGORY_NAME ,
             L4_CEG_CODE ,
             L4_CEG_SHORT_CN_NAME ,
             L4_CEG_CN_NAME ,
             L3_CEG_CODE ,
             L3_CEG_SHORT_CN_NAME ,
             L3_CEG_CN_NAME ,
             L2_CEG_CODE ,
             L2_CEG_CN_NAME ,
             GROUP_CODE ,
             GROUP_CN_NAME ,
             GROUP_LEVEL ,
             AVG_RECEIVE_AMT ,
             PARENT_LEVEL ,
             PARENT_CODE ,
             PARENT_CN_NAME ,
             TOP_FLAG ,
             APPEND_FLAG ,
             SCENARIO_FLAG,
             '||V_CALIBER||'
             CREATED_BY ,
             CREATION_DATE ,
             LAST_UPDATED_BY ,
             LAST_UPDATE_DATE ,
             DEL_FLAG )
    WITH CATEGORY_ITEM_TEMP AS
     (
      --历史表里出现的供应商，品类,item, 取数范围: (三年前第1月)至当前系统月(不含)  
      SELECT DISTINCT  
                       T.ITEM_CODE,
                       T.ITEM_NAME,
                       T.SUPPLIER_CODE ,
                       T.SUPPLIER_CN_NAME ,
                       T.CATEGORY_CODE,
                       T.CATEGORY_NAME,
                       T.L4_CEG_CODE ,
                       T.L4_CEG_SHORT_CN_NAME ,
                       T.L4_CEG_CN_NAME ,
                       T.L3_CEG_CODE ,
                       T.L3_CEG_SHORT_CN_NAME ,
                       T.L3_CEG_CN_NAME ,
                       T.L2_CEG_CODE ,
                       T.L2_CEG_CN_NAME ,
                       T.GROUP_CODE ,
                       T.GROUP_CN_NAME ,
                       T.GROUP_LEVEL ,
                       T.PARENT_LEVEL ,
                       T.PARENT_CODE ,
                       T.PARENT_CN_NAME 
        FROM BASE_DATA_TEMP T
       WHERE T.YEAR >= YEAR(CURRENT_TIMESTAMP) - 3
         AND T.PERIOD_ID < TO_NUMBER(TO_CHAR(CURRENT_TIMESTAMP, ''YYYYMM''))
         AND T.PARENT_LEVEL = ''ITEM''
         ),
    PERIOD_DIM_TEMP AS
     (
      --生成连续月份, 202101至当前系统实际月, (当前系统实际月 = 当前系统月-1)
      SELECT TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''', NUM.VAL - 1),
                      ''YYYYMM'') AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''
                                                      ||V_BEGIN_DATE||''',
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的品类,ITEM维
      SELECT  
              A.CATEGORY_CODE,
              A.ITEM_CODE,
              A.SUPPLIER_CODE,
              A.L4_CEG_CODE ,
              A.L3_CEG_CODE ,
              A.L2_CEG_CODE ,
              A.GROUP_CODE,
              A.GROUP_LEVEL,
              A.PARENT_CODE,
              A.PARENT_LEVEL,
              SUBSTR(B.PERIOD_ID, 1, 4) AS YEAR,
              B.PERIOD_ID
        FROM CATEGORY_ITEM_TEMP A, PERIOD_DIM_TEMP B),
    ITEM_SUM_TEMP AS        
    (
        SELECT T.PERIOD_ID,
               T.ITEM_CODE,
               T.SUPPLIER_CODE ,
               T.CATEGORY_CODE,
               T.L4_CEG_CODE ,
               T.L3_CEG_CODE ,
               T.L2_CEG_CODE ,
               T.GROUP_CODE ,
               T.GROUP_LEVEL ,
               T.PARENT_LEVEL ,
               T.PARENT_CODE ,
               AVG_RECEIVE_AMT AS AVG_AMT,
               ''N'' AS APD_FLAG,
               TOP_FLAG
            FROM BASE_DATA_TEMP T
            WHERE  T.PARENT_LEVEL = ''ITEM''
                       ),
    FORWARD_FILLER_TEMP AS
     (
      --按照品类,ITEM组, 向前寻找会计期补齐均价
      SELECT  
              SS.SUPPLIER_CODE,
              SS.CATEGORY_CODE,
              SS.ITEM_CODE,
              SS.L4_CEG_CODE ,
              SS.L3_CEG_CODE ,
              SS.L2_CEG_CODE ,
              SS.GROUP_CODE ,
              SS.GROUP_LEVEL ,
              SS.PARENT_LEVEL ,
              SS.PARENT_CODE ,
              SS.YEAR,
              SS.PERIOD_ID,
              SS.AVG_AMT,
              FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.SUPPLIER_CODE,SS.L4_CEG_CODE ,SS.L3_CEG_CODE ,SS.L2_CEG_CODE ,SS.CATEGORY_CODE, SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
              SS.AVG_AMT_FLAG,
              SS.APD_FLAG,
              SS.TOP_FLAG
        FROM (SELECT  
                      S.SUPPLIER_CODE,
                      S.CATEGORY_CODE,
                      S.ITEM_CODE,
                      S.L4_CEG_CODE ,
                      S.L3_CEG_CODE ,
                      S.L2_CEG_CODE ,
                      S.GROUP_CODE ,
                      S.GROUP_LEVEL ,
                      S.PARENT_LEVEL ,
                      S.PARENT_CODE ,
                      S.YEAR,
                      S.PERIOD_ID,
                      S.AVG_AMT,
                      SUM(S.NULL_FLAG) OVER(PARTITION BY S.SUPPLIER_CODE,S.L4_CEG_CODE ,S.L3_CEG_CODE ,S.L2_CEG_CODE ,S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                      S.APD_FLAG,
                      S.TOP_FLAG
                 FROM (SELECT T1.SUPPLIER_CODE,
                              T1.CATEGORY_CODE,
                              T1.ITEM_CODE,
                              T1.L4_CEG_CODE ,
                              T1.L3_CEG_CODE ,
                              T1.L2_CEG_CODE ,
                              T1.GROUP_CODE ,
                              T1.GROUP_LEVEL ,
                              T1.PARENT_LEVEL ,
                              T1.PARENT_CODE ,
                              T1.YEAR,
                              T1.PERIOD_ID,
                              T2.AVG_AMT,
                              DECODE(T2.AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
                              NVL(T2.APD_FLAG, ''Y'') AS APD_FLAG, --补齐标识：Y为补齐，N为原始存在
                              NVL(T2.TOP_FLAG,''N'') AS TOP_FLAG
                         FROM CROSS_JOIN_TEMP T1
                         LEFT JOIN ITEM_SUM_TEMP T2
                           ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
                          AND T1.ITEM_CODE = T2.ITEM_CODE
                          AND T1.PERIOD_ID = T2.PERIOD_ID
                          AND T1.SUPPLIER_CODE = T2.SUPPLIER_CODE
                          AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
                          AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
                          AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
                          ) S) SS)
    --向后补齐均价
        SELECT '||V_VERSION_ID||',
           S.YEAR,
           S.PERIOD_ID,
           S.SUPPLIER_CODE,
           S.SUPPLIER_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_NAME,
           S.CATEGORY_CODE,
           S.CATEGORY_NAME,
           S.L4_CEG_CODE ,
           S.L4_CEG_SHORT_CN_NAME ,
           S.L4_CEG_CN_NAME ,
           S.L3_CEG_CODE ,
           S.L3_CEG_SHORT_CN_NAME ,
           S.L3_CEG_CN_NAME ,
           S.L2_CEG_CODE ,
           S.L2_CEG_CN_NAME ,
           S.GROUP_CODE ,
           S.GROUP_CN_NAME ,
           S.GROUP_LEVEL ,
           NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS RECEIVE_AMT_CNY,
           S.PARENT_LEVEL ,
           S.PARENT_CODE ,
           S.PARENT_CN_NAME,
           S.TOP_FLAG,
           S.APD_FLAG AS APPEND_FLAG,
           ''S'' AS SCENARIO_FLAG,
           '||V_IN_CALIBER||'
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM (SELECT 
                   T1.SUPPLIER_CODE,
                   T3.SUPPLIER_CN_NAME,
                   T1.CATEGORY_CODE,
                   T3.CATEGORY_NAME,
                   T1.ITEM_CODE,
                   T3.ITEM_NAME,
                   T1.L4_CEG_CODE ,
                   T3.L4_CEG_SHORT_CN_NAME ,
                   T3.L4_CEG_CN_NAME ,
                   T1.L3_CEG_CODE ,
                   T3.L3_CEG_SHORT_CN_NAME ,
                   T3.L3_CEG_CN_NAME ,
                   T1.L2_CEG_CODE ,
                   T3.L2_CEG_CN_NAME ,
                   T1.GROUP_CODE,
                   T3.GROUP_CN_NAME ,
                   T1.GROUP_LEVEL,
                   T1.PARENT_LEVEL ,
                   T1.PARENT_CODE ,
                   T3.PARENT_CN_NAME,
                   T1.YEAR,
                   T1.PERIOD_ID,
                   T1.AVG_AMT_2,
                   T2.AVG_AMT_3,
                   T1.APD_FLAG,
                   T1.TOP_FLAG
              FROM FORWARD_FILLER_TEMP T1
              LEFT JOIN (SELECT DISTINCT S.SUPPLIER_CODE, 
                                        S.CATEGORY_CODE,
                                        S.ITEM_CODE,
                                        S.L4_CEG_CODE ,
                                        S.L3_CEG_CODE ,
                                        S.L2_CEG_CODE ,
                                        S.GROUP_CODE,
                                        S.GROUP_LEVEL,
                                        S.PARENT_LEVEL ,
                                        S.PARENT_CODE ,
                                        FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY S.SUPPLIER_CODE,S.L4_CEG_CODE ,S.L3_CEG_CODE ,S.L2_CEG_CODE ,S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(S.AVG_AMT_2) OVER(PARTITION BY S.SUPPLIER_CODE,S.L4_CEG_CODE ,S.L3_CEG_CODE ,S.L2_CEG_CODE ,S.CATEGORY_CODE, S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP S
                         WHERE S.AVG_AMT_FLAG > 0) T2 
                ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
               AND T1.ITEM_CODE = T2.ITEM_CODE
               AND T1.L4_CEG_CODE = T2.L4_CEG_CODE
               AND T1.L3_CEG_CODE = T2.L3_CEG_CODE
               AND T1.L2_CEG_CODE = T2.L2_CEG_CODE
               AND T1.SUPPLIER_CODE = T2.SUPPLIER_CODE
               AND T1.PERIOD_ID < T2.PERIOD_ID               
              LEFT JOIN CATEGORY_ITEM_TEMP T3 --关联带出item, 品类的名称信息
                ON T1.CATEGORY_CODE = T3.CATEGORY_CODE
               AND T1.ITEM_CODE = T3.ITEM_CODE
               AND T1.L4_CEG_CODE = T3.L4_CEG_CODE
               AND T1.L3_CEG_CODE = T3.L3_CEG_CODE
               AND T1.L2_CEG_CODE = T3.L2_CEG_CODE
               AND T1.SUPPLIER_CODE = T3.SUPPLIER_CODE
               ) S'
               ;
    EXECUTE IMMEDIATE V_SQL;
     
  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => '插入ITEM-供应商新补齐的均价实际数到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --3.日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM +1 ,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

 RETURN 'SUCCESS';

  EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
    
END$$
/

