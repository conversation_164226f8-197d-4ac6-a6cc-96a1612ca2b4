-- Name: f_dm_fcst_mid_annl_avg; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_mid_annl_avg(f_cost_type character varying, f_granularity_type character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年7月4日
  创建人  ：唐钦
  最新修改时间：2024年9月24日
  修改人：唐钦
  背景描述：根据维度关联后的表，取得不同路径下的维度关系数据，并在得到的这版数据的基础上，关联主力编码维表，对数据打上主力编码标签
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_MID_ANNL_AVG('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_MID_ANNL_AVG'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_TMP1_TABLE VARCHAR(100);
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_SQL_DE_AMT VARCHAR(150);   -- 解密金额
  V_SQL_EN_AMT VARCHAR(150);   -- 加密金额
  V_SQL_AVG VARCHAR(150);   -- 加密均价
  V_LV0_CODE VARCHAR(50);
  V_LV0_NAME VARCHAR(50);
  V_LV1_CODE VARCHAR(50);
  V_LV1_NAME VARCHAR(50);
  V_LV2_CODE VARCHAR(50);
  V_LV2_NAME VARCHAR(50);
  V_LV3_CODE VARCHAR(50);
  V_LV3_NAME VARCHAR(50);
  V_LV4_CODE VARCHAR(50);
  V_LV4_NAME VARCHAR(50);
  V_MAIN_FLAG VARCHAR(50);
  V_MAIN_CODE VARCHAR(50);
  V_CODE_ATTR VARCHAR(50);
  V_SQL_CONDITION VARCHAR(100);
  V_IN_SOFTWARE VARCHAR(50);
  V_INTO_SOFTWARE VARCHAR(50);
  V_REL_SOFTWARE VARCHAR(200);
  V_SQL_ANNL_FLAG VARCHAR(200);
  V_YTD_FLAG VARCHAR(50);
  V_ANNL_NUM INT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 根据入参，对变量进行不同定义
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DECODE_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
     V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DE_AMT_TMP';
     V_TMP1_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_AVG_TMP';
     V_IN_SOFTWARE := 'SOFTWARE_MARK,';
     V_INTO_SOFTWARE := 'T1.SOFTWARE_MARK,';
     V_REL_SOFTWARE := 'AND NVL(T1.SOFTWARE_MARK,''SS'') = NVL(T2.SOFTWARE_MARK,''SS'')';
  -- 判断成本类型，PSP成本不需要加解密，标准成本类型需要加解密
  IF F_COST_TYPE = 'PSP' THEN   -- PSP成本类型
     V_ANNL_NUM := 1;
     V_SQL_DE_AMT := 'RMB_COST_AMT,';
     V_SQL_EN_AMT := 'RMB_COST_AMT,';
     V_SQL_AVG := 'NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS RMB_AVG_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_ANNL_NUM := 1;
     V_SQL_DE_AMT := 'GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,';   -- 解密金额
     V_SQL_EN_AMT := 'GS_ENCRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,';   -- 加密金额
     V_SQL_AVG := 'GS_ENCRYPT( NVL(S.AVG_AMT_2, S.AVG_AMT_3),'''||V_KEYSTR||''',''AES128'',''CBC'',''SHA256'' ) AS RMB_AVG_AMT,';   -- 加密均价
  END IF;
  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV0_CODE := 'LV0_PROD_RND_TEAM_CODE';
     V_LV0_NAME := 'LV0_PROD_RD_TEAM_CN_NAME';
     V_LV1_CODE := 'LV1_PROD_RND_TEAM_CODE'; 
     V_LV1_NAME := 'LV1_PROD_RD_TEAM_CN_NAME';
     V_LV2_CODE := 'LV2_PROD_RND_TEAM_CODE';
     V_LV2_NAME := 'LV2_PROD_RD_TEAM_CN_NAME';
     V_LV3_CODE := 'LV3_PROD_RND_TEAM_CODE';
     V_LV3_NAME := 'LV3_PROD_RD_TEAM_CN_NAME';
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV0_CODE := 'LV0_INDUSTRY_CATG_CODE'; 
     V_LV0_NAME := 'LV0_INDUSTRY_CATG_CN_NAME';
     V_LV1_CODE := 'LV1_INDUSTRY_CATG_CODE'; 
     V_LV1_NAME := 'LV1_INDUSTRY_CATG_CN_NAME';
     V_LV2_CODE := 'LV2_INDUSTRY_CATG_CODE';
     V_LV2_NAME := 'LV2_INDUSTRY_CATG_CN_NAME';
     V_LV3_CODE := 'LV3_INDUSTRY_CATG_CODE';
     V_LV3_NAME := 'LV3_INDUSTRY_CATG_CN_NAME';
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV0_CODE := 'LV0_PROD_LIST_CODE'; 
     V_LV0_NAME := 'LV0_PROD_LIST_CN_NAME';
     V_LV1_CODE := 'LV1_PROD_LIST_CODE'; 
     V_LV1_NAME := 'LV1_PROD_LIST_CN_NAME';
     V_LV2_CODE := 'LV2_PROD_LIST_CODE';
     V_LV2_NAME := 'LV2_PROD_LIST_CN_NAME';
     V_LV3_CODE := 'LV3_PROD_LIST_CODE';
     V_LV3_NAME := 'LV3_PROD_LIST_CN_NAME';
     V_LV4_CODE := 'LV4_PROD_LIST_CODE';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME';
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空维表数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 创建临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
         PERIOD_YEAR                   INT,
         PERIOD_ID                     INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         MAIN_CODE                     VARCHAR(50), 
         SOFTWARE_MARK                 VARCHAR(50),
         PROD_QTY                      NUMERIC,
         COST_AMT                      NUMERIC,
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10),
         CODE_ATTRIBUTES               VARCHAR(50)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP_TABLE||'表成功');

     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP1_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP1_TABLE||' (
         PERIOD_YEAR                   INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         SPART_CODE                    VARCHAR(50),
         SPART_CN_NAME                 VARCHAR(200),
         RMB_COST_AMT                  NUMERIC,
         RMB_AVG_AMT                   NUMERIC,
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         SOFTWARE_MARK                 VARCHAR(50),
         NULL_FLAG                     VARCHAR(2),
         APD_FLAG                      VARCHAR(2),
         YTD_FLAG                      VARCHAR(2)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP1_TABLE||'表成功');
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '解密临时表：'||V_TMP_TABLE||'，'||V_TMP1_TABLE||'，创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  IF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_CONDITION := 'AND T1.ONLY_SPART_FLAG = ''N''   ';    -- 取LV3.5下非单SPART的数据进行计算
  ELSIF F_COST_TYPE = 'PSP' THEN   -- PSP成本类型
     V_SQL_CONDITION := '';
  END IF;
   
  -- 金额数据解密
     V_SQL := '
     INSERT INTO '||V_TMP_TABLE||' (
            PERIOD_YEAR,                     
            PERIOD_ID,
            LV0_CODE,                        
            LV0_CN_NAME,                     
            LV1_CODE,                        
            LV1_CN_NAME,                     
            LV2_CODE,                        
            LV2_CN_NAME,                     
            LV3_CODE,                        
            LV3_CN_NAME,                     
            LV4_CODE,                        
            LV4_CN_NAME,                     
            DIMENSION_CODE,                  
            DIMENSION_CN_NAME,               
            DIMENSION_SUBCATEGORY_CODE,      
            DIMENSION_SUBCATEGORY_CN_NAME,   
            DIMENSION_SUB_DETAIL_CODE,       
            DIMENSION_SUB_DETAIL_CN_NAME,    
            SPART_CODE,                      
            SPART_CN_NAME,                   
            MAIN_CODE,
            '||V_IN_SOFTWARE||'
            PROD_QTY,                        
            COST_AMT,                        
            VIEW_FLAG,                       
            REGION_CODE,                     
            REGION_CN_NAME,                  
            REPOFFICE_CODE,                  
            REPOFFICE_CN_NAME,               
            BG_CODE,                         
            BG_CN_NAME,                      
            OVERSEA_FLAG,
            CODE_ATTRIBUTES
     )
     WITH PBI_MAIN_CODE_DIM_T AS(
     SELECT DISTINCT 
           '||V_LV1_CODE||', 
           '||V_LV2_CODE||', 
           '||V_LV3_CODE||', 
           '||V_LV4_CODE||', 
            SPART_CODE,
            BG_CODE,
            CODE_ATTRIBUTES
         FROM FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_PBI_MAIN_CODE_DIM_T
         WHERE VERSION_ID = '||V_VERSION_ID||')
     SELECT T1.PERIOD_YEAR,                   
            T1.PERIOD_ID,
            T1.'||V_LV0_CODE||',                     
            T1.'||V_LV0_NAME||',                     
            T1.'||V_LV1_CODE||',                     
            T1.'||V_LV1_NAME||',                     
            T1.'||V_LV2_CODE||',                     
            T1.'||V_LV2_NAME||',                     
            T1.'||V_LV3_CODE||',                    
            T1.'||V_LV3_NAME||',                    
            T1.'||V_LV4_CODE||',                    
            T1.'||V_LV4_NAME||',                    
            T1.DIMENSION_CODE,                  
            T1.DIMENSION_CN_NAME,               
            T1.DIMENSION_SUBCATEGORY_CODE,      
            T1.DIMENSION_SUBCATEGORY_CN_NAME,   
            T1.DIMENSION_SUB_DETAIL_CODE,       
            T1.DIMENSION_SUB_DETAIL_CN_NAME,    
            T1.SPART_CODE,                      
            T1.SPART_CN_NAME,                   
            T2.SPART_CODE AS MAIN_CODE,
            '||V_INTO_SOFTWARE||'
            T1.PROD_QTY,                        
            '||V_SQL_DE_AMT||'                        
            T1.VIEW_FLAG,                       
            T1.REGION_CODE,                     
            T1.REGION_CN_NAME,                  
            T1.REPOFFICE_CODE,                  
            T1.REPOFFICE_CN_NAME,               
            T1.BG_CODE,                         
            T1.BG_CN_NAME,                      
            T1.OVERSEA_FLAG,
            T2.CODE_ATTRIBUTES
         FROM '||V_FROM_TABLE||' T1
         LEFT JOIN PBI_MAIN_CODE_DIM_T T2
           ON T1.SPART_CODE = T2.SPART_CODE
           AND T1.'||V_LV1_CODE||' = T2.'||V_LV1_CODE||'
           AND T1.'||V_LV2_CODE||' = T2.'||V_LV2_CODE||'
           AND T1.'||V_LV3_CODE||' = T2.'||V_LV3_CODE||'
           AND T1.'||V_LV4_CODE||' = T2.'||V_LV4_CODE||'
           AND T1.BG_CODE = T2.BG_CODE
         WHERE T1.VERSION_ID = '||V_VERSION_ID||'
         '||V_SQL_CONDITION;   -- 取LV3.5下非单SPART的数据进行计算
         DBMS_OUTPUT.PUT_LINE(V_SQL);    
         EXECUTE IMMEDIATE V_SQL;
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将解密数据处理后插入临时表：'||V_TMP_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  FOR ANNL_NUM IN 0 .. V_ANNL_NUM LOOP
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TMP1_TABLE;   -- 清空临时表数据
  IF ANNL_NUM = 0 THEN   -- 取年度整年数据
     V_SQL_ANNL_FLAG := '';
     V_YTD_FLAG := '''N''';
  ELSIF ANNL_NUM = 1 THEN   -- 取年度YTD数据
     V_SQL_ANNL_FLAG := 'AND CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND MONTH(CURRENT_TIMESTAMP)-1';   -- 只取每年1月-每年YTD月的数据值
     V_YTD_FLAG := '''Y''';
  END IF;
  
  -- 年均本计算逻辑
     V_SQL := '
     INSERT INTO '||V_TMP1_TABLE||' (
            PERIOD_YEAR,                     
            LV0_CODE,                        
            LV0_CN_NAME,                     
            LV1_CODE,                        
            LV1_CN_NAME,                     
            LV2_CODE,                        
            LV2_CN_NAME,                     
            LV3_CODE,                        
            LV3_CN_NAME,                     
            LV4_CODE,                        
            LV4_CN_NAME,                     
            DIMENSION_CODE,                  
            DIMENSION_CN_NAME,               
            DIMENSION_SUBCATEGORY_CODE,      
            DIMENSION_SUBCATEGORY_CN_NAME,   
            DIMENSION_SUB_DETAIL_CODE,       
            DIMENSION_SUB_DETAIL_CN_NAME,    
            SPART_CODE,                      
            SPART_CN_NAME,                   
            RMB_COST_AMT,
            RMB_AVG_AMT,                        
            VIEW_FLAG,                       
            REGION_CODE,                     
            REGION_CN_NAME,                  
            REPOFFICE_CODE,                  
            REPOFFICE_CN_NAME,               
            BG_CODE,                         
            BG_CN_NAME,                      
            OVERSEA_FLAG,
            MAIN_FLAG,      
            CODE_ATTRIBUTES,
            '||V_IN_SOFTWARE||'
            NULL_FLAG,      
            APD_FLAG,
            YTD_FLAG
     )
  -- 在数据里打上主力编码标签
     WITH MAIN_FLAG_TMP AS(
     SELECT PERIOD_YEAR,                     
            LV0_CODE,                        
            LV0_CN_NAME,                     
            LV1_CODE,                        
            LV1_CN_NAME,                     
            LV2_CODE,                        
            LV2_CN_NAME,                     
            LV3_CODE,                        
            LV3_CN_NAME,                     
            LV4_CODE,                        
            LV4_CN_NAME,                     
            DIMENSION_CODE,                  
            DIMENSION_CN_NAME,               
            DIMENSION_SUBCATEGORY_CODE,      
            DIMENSION_SUBCATEGORY_CN_NAME,   
            DIMENSION_SUB_DETAIL_CODE,       
            DIMENSION_SUB_DETAIL_CN_NAME,    
            SPART_CODE,                      
            SPART_CN_NAME,
            DECODE(PROD_QTY,NULL,0,PROD_QTY) AS PROD_QTY,
            DECODE(COST_AMT,NULL,0,COST_AMT) AS COST_AMT,
            DECODE(MAIN_CODE,NULL,''N'',''Y'') AS MAIN_FLAG,
            DECODE(MAIN_CODE,NULL,NULL,CODE_ATTRIBUTES) AS CODE_ATTRIBUTES,
            '||V_IN_SOFTWARE||'
            VIEW_FLAG,                       
            REGION_CODE,                     
            REGION_CN_NAME,                  
            REPOFFICE_CODE,                  
            REPOFFICE_CN_NAME,               
            BG_CODE,                         
            BG_CN_NAME,                      
            OVERSEA_FLAG
          FROM '||V_TMP_TABLE||' 
          WHERE 1=1
          '||V_SQL_ANNL_FLAG||'
     UNION ALL 
  -- 对主力编码部分数据赋予编码属性为：全部
     SELECT PERIOD_YEAR,                     
            LV0_CODE,                        
            LV0_CN_NAME,                     
            LV1_CODE,                        
            LV1_CN_NAME,                     
            LV2_CODE,                        
            LV2_CN_NAME,                     
            LV3_CODE,                        
            LV3_CN_NAME,                     
            LV4_CODE,                        
            LV4_CN_NAME,                     
            DIMENSION_CODE,                  
            DIMENSION_CN_NAME,               
            DIMENSION_SUBCATEGORY_CODE,      
            DIMENSION_SUBCATEGORY_CN_NAME,   
            DIMENSION_SUB_DETAIL_CODE,       
            DIMENSION_SUB_DETAIL_CN_NAME,    
            SPART_CODE,                      
            SPART_CN_NAME,
            DECODE(PROD_QTY,NULL,0,PROD_QTY) AS PROD_QTY,
            DECODE(COST_AMT,NULL,0,COST_AMT) AS COST_AMT,
            ''Y'' AS MAIN_FLAG,           -- 筛选部分数据均为主力编码
            ''全选'' AS CODE_ATTRIBUTES,  -- 所有主力编码数据均赋值为：全选
            '||V_IN_SOFTWARE||'
            VIEW_FLAG,                       
            REGION_CODE,                     
            REGION_CN_NAME,                  
            REPOFFICE_CODE,                  
            REPOFFICE_CN_NAME,               
            BG_CODE,                         
            BG_CN_NAME,                      
            OVERSEA_FLAG
          FROM '||V_TMP_TABLE||' 
          WHERE MAIN_CODE IS NOT NULL     -- 因为与主力编码维表关联上了，取MAIN_CODE不为空的数据
          '||V_SQL_ANNL_FLAG||'
     ),
  -- 将月度的数量和金额，按固定字段分年卷积
     SUM_DATA_TMP AS(
     SELECT PERIOD_YEAR,                     
            LV0_CODE,                        
            LV0_CN_NAME,                     
            LV1_CODE,                        
            LV1_CN_NAME,                     
            LV2_CODE,                        
            LV2_CN_NAME,                     
            LV3_CODE,                        
            LV3_CN_NAME,                     
            LV4_CODE,                        
            LV4_CN_NAME,                     
            DIMENSION_CODE,                  
            DIMENSION_CN_NAME,               
            DIMENSION_SUBCATEGORY_CODE,      
            DIMENSION_SUBCATEGORY_CN_NAME,   
            DIMENSION_SUB_DETAIL_CODE,       
            DIMENSION_SUB_DETAIL_CN_NAME,    
            SPART_CODE,                      
            SPART_CN_NAME,
            SUM(COST_AMT) AS RMB_COST_AMT,
            DECODE(SUM(PROD_QTY),0,0,SUM(COST_AMT)/SUM(PROD_QTY)) AS RMB_AVG_AMT,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            '||V_IN_SOFTWARE||'
            VIEW_FLAG,                       
            REGION_CODE,                     
            REGION_CN_NAME,                  
            REPOFFICE_CODE,                  
            REPOFFICE_CN_NAME,               
            BG_CODE,                         
            BG_CN_NAME,                      
            OVERSEA_FLAG
          FROM MAIN_FLAG_TMP
          GROUP BY PERIOD_YEAR,                     
                   LV0_CODE,                        
                   LV0_CN_NAME,                     
                   LV1_CODE,                        
                   LV1_CN_NAME,                     
                   LV2_CODE,                        
                   LV2_CN_NAME,                     
                   LV3_CODE,                        
                   LV3_CN_NAME,                     
                   LV4_CODE,                        
                   LV4_CN_NAME,                     
                   DIMENSION_CODE,                  
                   DIMENSION_CN_NAME,               
                   DIMENSION_SUBCATEGORY_CODE,      
                   DIMENSION_SUBCATEGORY_CN_NAME,   
                   DIMENSION_SUB_DETAIL_CODE,       
                   DIMENSION_SUB_DETAIL_CN_NAME,    
                   SPART_CODE,                      
                   SPART_CN_NAME,
                   MAIN_FLAG,
                   CODE_ATTRIBUTES,
                   '||V_IN_SOFTWARE||'
                   VIEW_FLAG,                       
                   REGION_CODE,                     
                   REGION_CN_NAME,                  
                   REPOFFICE_CODE,                  
                   REPOFFICE_CN_NAME,               
                   BG_CODE,                         
                   BG_CN_NAME,                      
                   OVERSEA_FLAG
     ),
  -- 将表里LV0-LV4、量纲层级、SPART层级的维度关系去重之后提取出来
     DIM_TEAM_TMP AS(
     SELECT DISTINCT LV0_CODE,                        
            LV0_CN_NAME,                     
            LV1_CODE,                        
            LV1_CN_NAME,                     
            LV2_CODE,                        
            LV2_CN_NAME,                     
            LV3_CODE,                        
            LV3_CN_NAME,                     
            LV4_CODE,                        
            LV4_CN_NAME,                     
            DIMENSION_CODE,                  
            DIMENSION_CN_NAME,               
            DIMENSION_SUBCATEGORY_CODE,      
            DIMENSION_SUBCATEGORY_CN_NAME,   
            DIMENSION_SUB_DETAIL_CODE,       
            DIMENSION_SUB_DETAIL_CN_NAME,    
            SPART_CODE,                      
            SPART_CN_NAME,
            MAIN_FLAG,
            CODE_ATTRIBUTES,
            '||V_IN_SOFTWARE||'
            VIEW_FLAG,                       
            REGION_CODE,                     
            REGION_CN_NAME,                  
            REPOFFICE_CODE,                  
            REPOFFICE_CN_NAME,               
            BG_CODE,                         
            BG_CN_NAME,                      
            OVERSEA_FLAG
          FROM SUM_DATA_TMP ),
  --生成今年预测月份及预测月份之后, 所有的发散维
     CROSS_JOIN_TEMP AS (
     SELECT DISTINCT T2.PERIOD_YEAR,
            T1.LV0_CODE,                        
            T1.LV0_CN_NAME,                     
            T1.LV1_CODE,                        
            T1.LV1_CN_NAME,                     
            T1.LV2_CODE,                        
            T1.LV2_CN_NAME,                     
            T1.LV3_CODE,                        
            T1.LV3_CN_NAME,                     
            T1.LV4_CODE,                        
            T1.LV4_CN_NAME,                     
            T1.DIMENSION_CODE,                  
            T1.DIMENSION_CN_NAME,               
            T1.DIMENSION_SUBCATEGORY_CODE,      
            T1.DIMENSION_SUBCATEGORY_CN_NAME,   
            T1.DIMENSION_SUB_DETAIL_CODE,       
            T1.DIMENSION_SUB_DETAIL_CN_NAME,    
            T1.SPART_CODE,                      
            T1.SPART_CN_NAME,
            T1.MAIN_FLAG,
            T1.CODE_ATTRIBUTES,
            '||V_INTO_SOFTWARE||'
            T1.VIEW_FLAG,                       
            T1.REGION_CODE,                     
            T1.REGION_CN_NAME,                  
            T1.REPOFFICE_CODE,                  
            T1.REPOFFICE_CN_NAME,               
            T1.BG_CODE,                         
            T1.BG_CN_NAME,                      
            T1.OVERSEA_FLAG
          FROM DIM_TEAM_TMP T1,
          (     
           SELECT CAST(GENERATE_SERIES(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''YYYY'')-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   ) T2)   -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）
     
     SELECT T1.PERIOD_YEAR,                     
            T1.LV0_CODE,                        
            T1.LV0_CN_NAME,                     
            T1.LV1_CODE,                        
            T1.LV1_CN_NAME,                     
            T1.LV2_CODE,                        
            T1.LV2_CN_NAME,                     
            T1.LV3_CODE,                        
            T1.LV3_CN_NAME,                     
            T1.LV4_CODE,                        
            T1.LV4_CN_NAME,                     
            T1.DIMENSION_CODE,                  
            T1.DIMENSION_CN_NAME,               
            T1.DIMENSION_SUBCATEGORY_CODE,      
            T1.DIMENSION_SUBCATEGORY_CN_NAME,   
            T1.DIMENSION_SUB_DETAIL_CODE,       
            T1.DIMENSION_SUB_DETAIL_CN_NAME,    
            T1.SPART_CODE,                      
            T1.SPART_CN_NAME,                   
            T2.RMB_COST_AMT,
            T2.RMB_AVG_AMT,                        
            T1.VIEW_FLAG,                       
            T1.REGION_CODE,                     
            T1.REGION_CN_NAME,                  
            T1.REPOFFICE_CODE,                  
            T1.REPOFFICE_CN_NAME,               
            T1.BG_CODE,                         
            T1.BG_CN_NAME,                      
            T1.OVERSEA_FLAG,
            T1.MAIN_FLAG,      
            T1.CODE_ATTRIBUTES,
            '||V_INTO_SOFTWARE||'
            DECODE(T2.RMB_AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
            CASE WHEN T2.RMB_AVG_AMT IS NULL THEN ''Y'' 
                 WHEN T2.RMB_AVG_AMT = 0 THEN ''Y'' 
            ELSE ''N'' END AS APD_FLAG, --补齐标识：Y为补齐，N为原始
            '||V_YTD_FLAG||' AS YTD_FLAG
          FROM CROSS_JOIN_TEMP T1
          LEFT JOIN SUM_DATA_TMP T2
          ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
          AND T1.LV1_CODE = T2.LV1_CODE
          AND T1.LV2_CODE = T2.LV2_CODE
          AND T1.LV3_CODE = T2.LV3_CODE
          AND T1.LV4_CODE = T2.LV4_CODE
          AND NVL(T1.DIMENSION_CODE,''SNULL0'') = NVL(T2.DIMENSION_CODE,''SNULL0'')
          AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL1'')
          AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL2'')
          AND NVL(T1.SPART_CODE,''SNULL3'') = NVL(T2.SPART_CODE,''SNULL3'')
          AND T1.VIEW_FLAG = T2.VIEW_FLAG
          AND T1.REGION_CODE = T2.REGION_CODE
          AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
          AND T1.BG_CODE = T2.BG_CODE
          AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
          AND NVL(T1.MAIN_FLAG,''SNULL4'') = NVL(T2.MAIN_FLAG,''SNULL4'')
          AND NVL(T1.CODE_ATTRIBUTES,''SNULL5'') = NVL(T2.CODE_ATTRIBUTES,''SNULL5'')
          '||V_REL_SOFTWARE;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL; 
    DBMS_OUTPUT.PUT_LINE('执行完成');
   
     --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '计算年均本数据插入到临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
          
  -- 插入补齐后的分视角下年均本数据
     V_SQL := '
     INSERT INTO '||V_TO_TABLE||'(
            VERSION_ID,
            PERIOD_YEAR,                  
            '||V_LV0_CODE||',              
            '||V_LV0_NAME||',              
            '||V_LV1_CODE||',              
            '||V_LV1_NAME||',              
            '||V_LV2_CODE||',              
            '||V_LV2_NAME||',              
            '||V_LV3_CODE||',              
            '||V_LV3_NAME||',              
            '||V_LV4_CODE||',              
            '||V_LV4_NAME||',              
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,                
            RMB_COST_AMT,
            RMB_AVG_AMT,                     
            REGION_CODE,                  
            REGION_CN_NAME,               
            REPOFFICE_CODE,               
            REPOFFICE_CN_NAME,            
            BG_CODE,                      
            BG_CN_NAME,                   
            OVERSEA_FLAG,              
            MAIN_FLAG,                    
            CODE_ATTRIBUTES,
            '||V_IN_SOFTWARE||'
            VIEW_FLAG,
            APPEND_FLAG,
            APPEND_YEAR,
            YTD_FLAG,
            CREATED_BY,
            CREATION_DATE,
            LAST_UPDATED_BY,
            LAST_UPDATE_DATE,
            DEL_FLAG
     )
 -- 按不同视角，补齐对应的重量级团队，以及采购信息维，前向补齐年均本
 WITH FORWARD_FILLER_TEMP AS
     (
     SELECT PERIOD_YEAR,                  
            LV0_CODE,                
            LV0_CN_NAME,             
            LV1_CODE,                
            LV1_CN_NAME,             
            LV2_CODE,                
            LV2_CN_NAME,             
            LV3_CODE,                
            LV3_CN_NAME,             
            LV4_CODE,                
            LV4_CN_NAME,             
            DIMENSION_CODE,               
            DIMENSION_CN_NAME,            
            DIMENSION_SUBCATEGORY_CODE,   
            DIMENSION_SUBCATEGORY_CN_NAME,
            DIMENSION_SUB_DETAIL_CODE,    
            DIMENSION_SUB_DETAIL_CN_NAME, 
            SPART_CODE,                   
            SPART_CN_NAME,                
            RMB_COST_AMT,
            AVG_AMT,      
            VIEW_FLAG,        
            REGION_CODE,      
            REGION_CN_NAME,   
            REPOFFICE_CODE,   
            REPOFFICE_CN_NAME,
            BG_CODE,          
            BG_CN_NAME,       
            OVERSEA_FLAG,
            MAIN_FLAG,      
            CODE_ATTRIBUTES,
            '||V_IN_SOFTWARE||'
            FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.VIEW_FLAG, SS.REGION_CODE, SS.OVERSEA_FLAG, SS.REPOFFICE_CODE, SS.BG_CODE, SS.MAIN_FLAG, SS.CODE_ATTRIBUTES,'||V_IN_SOFTWARE||'SS.DIMENSION_CODE,SS.DIMENSION_SUBCATEGORY_CODE,SS.DIMENSION_SUB_DETAIL_CODE,SS.SPART_CODE,SS.LV1_CODE,SS.LV2_CODE,SS.LV3_CODE,SS.LV4_CODE,SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS AVG_AMT_2, --新补齐的均价字段
            FIRST_VALUE(SS.PERIOD_YEAR) OVER(PARTITION BY SS.VIEW_FLAG, SS.REGION_CODE, SS.OVERSEA_FLAG, SS.REPOFFICE_CODE, SS.BG_CODE, SS.MAIN_FLAG, SS.CODE_ATTRIBUTES,'||V_IN_SOFTWARE||'SS.DIMENSION_CODE,SS.DIMENSION_SUBCATEGORY_CODE,SS.DIMENSION_SUB_DETAIL_CODE,SS.SPART_CODE,SS.LV1_CODE,SS.LV2_CODE,SS.LV3_CODE,SS.LV4_CODE,SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_YEAR) AS PERIOD_YEAR_2, --新补齐的年份字段
            AVG_AMT_FLAG,
            APD_FLAG,
            YTD_FLAG
        FROM (SELECT PERIOD_YEAR,                  
                     LV0_CODE,                 
                     LV0_CN_NAME,              
                     LV1_CODE,                 
                     LV1_CN_NAME,              
                     LV2_CODE,                 
                     LV2_CN_NAME,              
                     LV3_CODE,                 
                     LV3_CN_NAME,              
                     LV4_CODE,                 
                     LV4_CN_NAME,              
                     DIMENSION_CODE,               
                     DIMENSION_CN_NAME,            
                     DIMENSION_SUBCATEGORY_CODE,   
                     DIMENSION_SUBCATEGORY_CN_NAME,
                     DIMENSION_SUB_DETAIL_CODE,    
                     DIMENSION_SUB_DETAIL_CN_NAME, 
                     SPART_CODE,                   
                     SPART_CN_NAME,                
                     RMB_COST_AMT,
                     RMB_AVG_AMT AS AVG_AMT,      
                     VIEW_FLAG,        
                     REGION_CODE,      
                     REGION_CN_NAME,   
                     REPOFFICE_CODE,   
                     REPOFFICE_CN_NAME,
                     BG_CODE,          
                     BG_CN_NAME,       
                     OVERSEA_FLAG,
                     MAIN_FLAG,      
                     CODE_ATTRIBUTES,
                     '||V_IN_SOFTWARE||'
                     APD_FLAG,
                     YTD_FLAG,
                     SUM(S.NULL_FLAG) OVER(PARTITION BY S.VIEW_FLAG, S.REGION_CODE, S.OVERSEA_FLAG, S.REPOFFICE_CODE, S.BG_CODE, S.MAIN_FLAG, S.CODE_ATTRIBUTES,'||V_IN_SOFTWARE||'S.DIMENSION_CODE,S.DIMENSION_SUBCATEGORY_CODE,S.DIMENSION_SUB_DETAIL_CODE,S.SPART_CODE,S.LV1_CODE,S.LV2_CODE,S.LV3_CODE,S.LV4_CODE ORDER BY S.PERIOD_YEAR) AS AVG_AMT_FLAG --均价标识: 为空不参与累计加1
                  FROM '||V_TMP1_TABLE||' S) SS)
    --向后补齐均价
    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           PERIOD_YEAR,                  
           LV0_CODE,                 
           LV0_CN_NAME,              
           LV1_CODE,                 
           LV1_CN_NAME,              
           LV2_CODE,                 
           LV2_CN_NAME,              
           LV3_CODE,                 
           LV3_CN_NAME,              
           LV4_CODE,                 
           LV4_CN_NAME,              
           DIMENSION_CODE,               
           DIMENSION_CN_NAME,            
           DIMENSION_SUBCATEGORY_CODE,   
           DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE,    
           DIMENSION_SUB_DETAIL_CN_NAME, 
           SPART_CODE,                   
           SPART_CN_NAME,                
           '||V_SQL_EN_AMT||'
           '||V_SQL_AVG||'
           REGION_CODE,                  
           REGION_CN_NAME,               
           REPOFFICE_CODE,               
           REPOFFICE_CN_NAME,            
           BG_CODE,                      
           BG_CN_NAME,                   
           OVERSEA_FLAG,              
           MAIN_FLAG,                    
           CODE_ATTRIBUTES,
           '||V_IN_SOFTWARE||'
           VIEW_FLAG,
           S.APD_FLAG AS APPEND_FLAG,
           S.APPEND_YEAR,
           YTD_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
       FROM (SELECT T1.PERIOD_YEAR,                  
                    T1.LV0_CODE,                 
                    T1.LV0_CN_NAME,              
                    T1.LV1_CODE,                 
                    T1.LV1_CN_NAME,              
                    T1.LV2_CODE,                 
                    T1.LV2_CN_NAME,              
                    T1.LV3_CODE,                 
                    T1.LV3_CN_NAME,              
                    T1.LV4_CODE,                 
                    T1.LV4_CN_NAME,              
                    T1.DIMENSION_CODE,               
                    T1.DIMENSION_CN_NAME,            
                    T1.DIMENSION_SUBCATEGORY_CODE,   
                    T1.DIMENSION_SUBCATEGORY_CN_NAME,
                    T1.DIMENSION_SUB_DETAIL_CODE,    
                    T1.DIMENSION_SUB_DETAIL_CN_NAME, 
                    T1.SPART_CODE,                   
                    T1.SPART_CN_NAME, 
                    T1.RMB_COST_AMT,
                    T1.AVG_AMT_2,
                    T1.PERIOD_YEAR_2,
                    T2.AVG_AMT_3,
                    T2.PERIOD_YEAR_3,
                    T1.APD_FLAG,
                    T1.YTD_FLAG,
                    T1.VIEW_FLAG,
                    CASE WHEN T1.APD_FLAG = ''Y'' AND T1.AVG_AMT_2 IS NOT NULL THEN
                           T1.PERIOD_YEAR_2 ELSE NULL END AS APPEND_YEAR,
                    T1.OVERSEA_FLAG,
                    T1.REGION_CODE,                  
                    T1.REGION_CN_NAME,               
                    T1.REPOFFICE_CODE,               
                    T1.REPOFFICE_CN_NAME,            
                    T1.BG_CODE,                      
                    T1.BG_CN_NAME,
                    '||V_INTO_SOFTWARE||'
                    T1.MAIN_FLAG,                    
                    T1.CODE_ATTRIBUTES
                FROM FORWARD_FILLER_TEMP T1
                LEFT JOIN (SELECT DISTINCT P.LV0_CODE,
                                  P.LV1_CODE,
                                  P.LV2_CODE,
                                  P.LV3_CODE,
                                  P.LV4_CODE,
                                  P.DIMENSION_CODE,
                                  P.DIMENSION_SUBCATEGORY_CODE,
                                  P.DIMENSION_SUB_DETAIL_CODE,
                                  P.SPART_CODE,
                                  FIRST_VALUE(P.PERIOD_YEAR) OVER(PARTITION BY P.VIEW_FLAG, P.OVERSEA_FLAG, P.REGION_CODE, P.REPOFFICE_CODE, P.BG_CODE, P.MAIN_FLAG,P.CODE_ATTRIBUTES,'||V_IN_SOFTWARE||'P.LV1_CODE,P.LV2_CODE,P.LV3_CODE,P.LV4_CODE,P.DIMENSION_CODE,P.DIMENSION_SUBCATEGORY_CODE,P.DIMENSION_SUB_DETAIL_CODE,P.SPART_CODE ORDER BY P.PERIOD_YEAR ASC) AS PERIOD_YEAR_3, --有均价的首条会计期
                                  FIRST_VALUE(P.AVG_AMT_2) OVER(PARTITION BY P.VIEW_FLAG, P.OVERSEA_FLAG, P.REGION_CODE, P.REPOFFICE_CODE, P.BG_CODE, P.MAIN_FLAG,P.CODE_ATTRIBUTES,'||V_IN_SOFTWARE||'P.LV1_CODE,P.LV2_CODE,P.LV3_CODE,P.LV4_CODE,P.DIMENSION_CODE,P.DIMENSION_SUBCATEGORY_CODE,P.DIMENSION_SUB_DETAIL_CODE,P.SPART_CODE ORDER BY P.PERIOD_YEAR ASC) AS AVG_AMT_3, --有均价的首条补齐均价
                                  P.VIEW_FLAG,
                                  P.OVERSEA_FLAG,
                                  P.REGION_CODE,
                                  P.REPOFFICE_CODE,
                                  P.BG_CODE,
                                  '||V_IN_SOFTWARE||'
                                  P.MAIN_FLAG, 
                                  P.CODE_ATTRIBUTES
                              FROM FORWARD_FILLER_TEMP P
                              WHERE P.AVG_AMT_FLAG > 0) T2
                                ON T1.LV1_CODE = T2.LV1_CODE
                                AND T1.LV2_CODE = T2.LV2_CODE
                                AND T1.LV3_CODE = T2.LV3_CODE
                                AND T1.LV4_CODE = T2.LV4_CODE
                                AND NVL(T1.DIMENSION_CODE,''SNULL0'') = NVL(T2.DIMENSION_CODE,''SNULL0'')
                                AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL1'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL1'')
                                AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL2'')
                                AND NVL(T1.SPART_CODE,''SNULL3'') = NVL(T2.SPART_CODE,''SNULL3'')
                                AND T1.VIEW_FLAG = T2.VIEW_FLAG
                                AND T1.REGION_CODE = T2.REGION_CODE
                                AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
                                AND T1.BG_CODE = T2.BG_CODE
                                AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                                AND NVL(T1.MAIN_FLAG,''SNULL4'') = NVL(T2.MAIN_FLAG,''SNULL4'')
                                AND NVL(T1.CODE_ATTRIBUTES,''SNULL5'') = NVL(T2.CODE_ATTRIBUTES,''SNULL5'')
                                '||V_REL_SOFTWARE||'
                             AND T1.PERIOD_YEAR < T2.PERIOD_YEAR_3) S                          
                   ';                
--        DBMS_OUTPUT.PUT_LINE(V_SQL);
        EXECUTE IMMEDIATE V_SQL; 

 --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '补齐年均本数据，并插入版本号为：'||V_VERSION_ID||'的全量加密数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');      
  END LOOP;   -- 结束循环
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

