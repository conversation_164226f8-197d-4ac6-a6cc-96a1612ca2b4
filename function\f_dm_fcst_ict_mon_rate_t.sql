-- Name: f_dm_fcst_ict_mon_rate_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_rate_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text, f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
--2024年9月24日15点11分 新增软硬件标识
--月度同环比
--来源表
--指数表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_COST_IDX_T     --STD PROD

--目标表
--同环比表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_RATE_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_RATE_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_RATE_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_RATE_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_RATE_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_RATE_T     --STD PROD
--事例
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_RATE_T('PSP','IRB','',''); 
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_RATE_T';
  V_VERSION        BIGINT; --版本号
  V_BASE_PERIOD_ID INT ;
  V_FROM_TABLE     VARCHAR(200);
  V_TO_TABLE       VARCHAR(200);
  V_OTHER_DIM_PART TEXT;
  V_DIMENSION_PART TEXT;
  V_PROD_PART      TEXT;
  V_EXCEPTION_FLAG TEXT;
  V_SQL            TEXT;

BEGIN 

RAISE NOTICE '取版本号';

--版本表
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

  
--判断基期
IF     MONTH(CURRENT_DATE)   = 1 then                                                                                                                                  
	V_BASE_PERIOD_ID := TO_NUMBER((YEAR(CURRENT_DATE) - 2) || '01'); --基期会计期,如果当前是1月则取两年前的1月为基期
ELSIF  MONTH(CURRENT_DATE)   != 1 then 
	V_BASE_PERIOD_ID := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期,如果当前不是1月则取去年前的1月为基期

END IF;  
  
  
V_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_COST_IDX_T';
V_TO_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_RATE_T';

  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
		
  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';

  IF F_GRANULARITY_TYPE = 'IRB' THEN
	  
	V_PROD_PART:='
		PROD_RND_TEAM_CODE,
		PROD_RD_TEAM_CN_NAME,
	';

  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
	  
	V_PROD_PART:='
		INDUSTRY_CATG_CODE,
		INDUSTRY_CATG_CN_NAME,
	';

  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
	  
	V_PROD_PART:='
		PROD_LIST_CODE,
		PROD_LIST_CN_NAME,
	';
  
  END IF;
  
--删数
  V_SQL:= 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION;
  
  EXECUTE V_SQL;
  
--插数
  V_SQL:='
  WITH LEV_INDEX AS
   (SELECT PERIOD_ID,
           SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
           '||V_PROD_PART||V_DIMENSION_PART||' 
		   GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
		   VIEW_FLAG,
           '||V_OTHER_DIM_PART||' 
		   MAIN_FLAG,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           CODE_ATTRIBUTES
      FROM '||V_FROM_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'),
  
  BASE_YOY AS
   (SELECT PERIOD_ID,
           '||V_PROD_PART||V_DIMENSION_PART||' GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
		   VIEW_FLAG,
           '||V_OTHER_DIM_PART||' 
		   LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY '||V_PROD_PART||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') ,--202410版本 新增软硬件标识
														NVL(DIMENSION_CODE,''DC''), NVL(DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
														VIEW_FLAG,MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY '||V_PROD_PART||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG,NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') ,--202410版本 新增软硬件标识
														NVL(DIMENSION_CODE,''DC''), NVL(DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(DIMENSION_SUB_DETAIL_CODE,''DSDC''), 
														VIEW_FLAG,MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY '||V_PROD_PART||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG, NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') ,--202410版本 新增软硬件标识
														NVL(DIMENSION_CODE,''DC''), NVL(DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(DIMENSION_SUB_DETAIL_CODE,''DSDC''),
														VIEW_FLAG
														ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY '||V_PROD_PART||V_OTHER_DIM_PART||' 
														GROUP_CODE, GROUP_LEVEL, PARENT_CODE, MAIN_FLAG, NVL(CODE_ATTRIBUTES,''CA''),
														NVL(SOFTWARE_MARK,''SW'') ,--202410版本 新增软硬件标识
														NVL(DIMENSION_CODE,''DC''), NVL(DIMENSION_SUBCATEGORY_CODE,''DSC''), NVL(DIMENSION_SUB_DETAIL_CODE,''DSDC''),
														VIEW_FLAG
														ORDER BY PERIOD_ID) AS POP_COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
		   MAIN_FLAG,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           CODE_ATTRIBUTES
      FROM LEV_INDEX) 
  INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     '||V_PROD_PART||V_DIMENSION_PART||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RATE,
     RATE_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||' 
	 CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG
     )
  SELECT '||V_VERSION||' AS VERSION_ID,
         LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
         '||V_PROD_PART||V_DIMENSION_PART||' 
		 GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
		 ''YOY''AS RATE_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
		 SOFTWARE_MARK,		--202410版本 新增软硬件标识
         VIEW_FLAG,
         '||V_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N''AS DEL_FLAG
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT '||V_VERSION||' AS VERSION_ID,
         LEFT(PERIOD_ID, 4) AS PERIOD_YEAR,
         PERIOD_ID,
         '||V_PROD_PART||V_DIMENSION_PART||' 
		 GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
		 ''POP''AS RATE_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
		 SOFTWARE_MARK,		--202410版本 新增软硬件标识
         VIEW_FLAG,
         '||V_OTHER_DIM_PART||' 
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
		 ''N''AS DEL_FLAG
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL ';
   
  EXECUTE V_SQL;
  
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 


$$
/

