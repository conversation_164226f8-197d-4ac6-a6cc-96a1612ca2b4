-- Name: f_dm_fol_spec_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_spec_info_t(p_version_id bigint DEFAULT NULL::bigint, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-26
创建人  ：zwx1275798
背景描述：规格品信息表，仅精品海运,包括Top清单、非Top清单的数据
参数描述：参数一(p_refresh_type)：JAVA传入的刷新类型，写入版本信息表
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_spec_info_t()
变更记录：2024-8-21 qwx1110218 来源表变更：由 dm_fol_route_info_sum_t （价格补录表内关联航线量集成表）变更为 dm_fol_route_price_info_sum_t （价格补录表左关联航线量集成表），
                                           且需要剔除柜型量为空的数据（即 container_qty is not null）；
*/


declare
	v_sp_name varchar(500) := 'fin_dm_opt_foi.f_dm_fol_spec_info_t';
	v_tbl_name varchar(500) := 'fin_dm_opt_foi.dm_fol_spec_info_t';
	v_dml_row_count number default 0 ;
	v_max_last_update_date timestamp;
	v_max_version_code varchar(30);
	v_version_status varchar(30);
	v_max_version_id int;


begin
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => ''||v_tbl_name||'，开始运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	   --从 规格品信息表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_spec_info_t t1
              where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_version_info_t t2 where  t2.step='2001');

		--航线量表临时表，储存对航线量表简单逻辑处理后的数据
		drop table if exists spec_route_tmp;
		create temporary table spec_route_tmp(
		 version_id          integer
		,version_code        character varying(50)
	    ,period_id           integer
	    ,transport_mode      character varying(50)
	    ,region_cn_name      character varying(50)
		,route               character varying(200)
		,container_type      varchar(50)
		,top_flag            character varying(100)
		,level_code          character varying(100)
        ,level_desc          character varying(100)
	    ,container_qty       numeric(38,10)
	    );


		-- 取航线清单表的最后更新时间
		select max(last_update_date) as max_last_update_date into v_max_last_update_date from fin_dm_opt_foi.dm_fol_route_info_t  where upper(version_status) in ('AUTO','FINAL');
		 -- 取航线清单表的最大版本编码
        select max(version_code) as max_version_code into v_max_version_code   from fin_dm_opt_foi.dm_fol_route_info_t  where last_update_date = v_max_last_update_date  ;
        -- 取航线清单表最大更新时间时version_status的状态
        select max(version_status) as max_version_status into v_version_status from fin_dm_opt_foi.dm_fol_route_info_t  where last_update_date = v_max_last_update_date  ;


         -- 如果是传version_id调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then
        select  p_version_id into v_max_version_id ;
        else
        select max(version_id) as max_version_id into v_max_version_id
		from fin_dm_opt_foi.dm_fol_route_price_info_sum_t;
        end if
        ;

		if(p_refresh_type is null or p_refresh_type = '') then
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_spec_info_t'
       and refresh_type = '4_AUTO'
       and step = 1
       and upper(del_flag) = 'N'
    ;

  elseif(p_refresh_type = '1_刷新价格表') then
    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where upper(del_flag) = 'N'
       and version_code = to_char(current_date,'yyyymmdd')
       and refresh_type = '4_AUTO'
	   and  source_en_name ='f_dm_fol_spec_info_t'
    ;

	  elseif(p_refresh_type = '2_刷新系统') then
	   -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where upper(del_flag) = 'N'
       and version_id = v_max_version_id
       and refresh_type = '2_刷新系统'
	   and  source_en_name ='f_dm_fol_spec_info_t'
	   and step = 2
    ;

  end if;

   if(p_refresh_type = '' or p_refresh_type is null) then
    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id   as version_id
         , v_max_version_code as version_code
         , 2 as step
         , 'f_dm_fol_spec_info_t' as source_en_name
         , '规格品信息表' as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code为航线清单表的最大版本编码' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
  ;

  elseif(p_refresh_type = '1_刷新价格表') then
    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id  as version_id
         , v_max_version_code as version_code
         , 2 as step
         , 'f_dm_fol_spec_info_t' as source_en_name
         , '规格品信息表' as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code为航线清单表的最大版本编码' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
    ;

	elseif(p_refresh_type = '2_刷新系统') then
    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id  as version_id
         , v_max_version_code as version_code
         , 2 as step
         , 'f_dm_fol_spec_info_t' as source_en_name
         , '规格品信息表' as source_cn_name
         , '2_刷新系统' as refresh_type
         , 'version_code为航线清单表的最大版本编码' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
    ;

	  end if;



		--从 航线量表 取出 version_id 为最大版本,关联航线清单取航线清单最大版本version_code和top_route_flag， 仅筛选精品海运
		insert into spec_route_tmp(
                 version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
				,top_flag
				,container_qty
                )
        with tmp as (
		select distinct version_code
              ,route
              ,region_cn_name
              ,top_route_flag
		from fin_dm_opt_foi.dm_fol_route_info_t
		where 1=1
		and version_status = v_version_status
		and version_code = v_max_version_code
		and top_route_flag = 'Y'
		and transport_mode = '精品海运'
                )
		select  t1.version_id
		       ,v_max_version_code as version_code
	           ,t1.period_id
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,t1.container_type   -- 柜型（20GP、40GP、40HQ、ALL）
			   ,t2.top_route_flag  as top_flag
			   ,sum(t1.container_qty) as container_qty
		from  fin_dm_opt_foi.dm_fol_route_price_info_sum_t  t1
		left join  tmp t2
		   on t1.route = t2.route
		where 1=1
		  and t1.transport_mode = '精品海运'
		  and t1.version_id = v_max_version_id
		  and t1.container_qty is not null  -- 20240821 add by qwx1110128 替换了来源表，需要剔除 container_qty 是空的数据
		group by t1.version_id
		        ,t2.version_code
		        ,t1.period_id
			    ,t1.transport_mode
			    ,t1.region_cn_name
				,t1.route
				,t1.container_type
                ,t2.top_route_flag
			   ;

			 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '【 spec_route_tmp 航线量临时表，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;


		 -- 删除 规格品信息表 最大version_id 数据
		 delete from  fin_dm_opt_foi.dm_fol_spec_info_t where version_id = v_max_version_id;

		 -- 航线层级的规格品，非规格品，及总的月度货量
		 insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,region_cn_name
				,route
				        ,container_type   -- 柜型（20GP、40GP、40HQ、ALL）
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
		    select version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
				,top_flag
                ,'03'   as level_code
                ,'航线' as level_desc
				,sum(container_qty) as container_qty
				, null as percent
				, '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
           from spec_route_tmp
		   where top_flag = 'Y'
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
				,top_flag
				union all
			 select version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
				,'N' as top_flag
                ,'03'   as level_code
                ,'航线' as level_desc
				,sum(container_qty) as container_qty
				, null as percent
				, '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
           from spec_route_tmp
		   where top_flag is null
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
				union all
				 select version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
				,'ALL' as top_flag
                ,'03'   as level_code
                ,'航线' as level_desc
				,sum(container_qty) as container_qty
				, null as percent
				, '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
           from spec_route_tmp
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
                ,route
        ,container_type
	         ;






		-- 区域层级 的 规格品 货量占比：sum（柜型量） group by 会计期、运输方式、区域(筛选Top清单标识为Y的航线)  / sum（柜型量） group by 会计期、运输方式、区域
		insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,region_cn_name
                ,container_type
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
		  with tmp1 as(
		   select version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
				,container_type
				,top_flag
				,sum(container_qty) as container_qty
           from spec_route_tmp
		   where top_flag = 'Y'
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
				,container_type
				,top_flag
                ),
           tmp2 as(
                 select version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
				,container_type
				,sum(container_qty) as container_qty
           from spec_route_tmp
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
				,container_type
                )
          select t1.version_id
		        ,t1.version_code
                ,t1.period_id
                ,t1.transport_mode
                ,t1.region_cn_name
                ,t1.container_type
                ,t1.top_flag
                ,'02'   as level_code
                ,'区域' as level_desc
                ,t1.container_qty  as container_qty
                ,(case when t2.container_qty = 0 then 0
                       else t1.container_qty/t2.container_qty
                  end) as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  tmp1 t1
          left join tmp2 t2
            on t1.version_id = t2.version_id
		   and t1.version_code = t2.version_code
           and t1.period_id = t2.period_id
           and t1.transport_mode = t2.transport_mode
           and t1.region_cn_name = t2.region_cn_name
           and t1.container_type = t2.container_type
               ;

			    v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 3,
            p_log_cal_log_desc => '【 dm_fol_spec_info_t 区域层级规格品数量及占比，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;


			-- 区域层级 带出非规格品数量
		insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,region_cn_name
                ,container_type
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
          select version_id
		        ,version_code
                ,period_id
                ,transport_mode
                ,region_cn_name
                ,container_type
                ,'N' top_flag
                ,'02'   as level_code
                ,'区域' as level_desc
                ,sum(container_qty)  as container_qty
                ,null as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  spec_route_tmp
		   where top_flag is null
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
				,container_type

               ;

			   -- 区域层级月度总量
		insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,region_cn_name
                ,container_type
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
				 select version_id
		        ,version_code
                ,period_id
                ,transport_mode
                ,region_cn_name
                ,container_type
                ,'ALL' top_flag
                ,'02'   as level_code
                ,'区域' as level_desc
                ,sum(container_qty)  as container_qty
                ,null as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  spec_route_tmp
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,region_cn_name
				,container_type
               ;

			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 4,
            p_log_cal_log_desc => '【 dm_fol_spec_info_t 区域层级非规格品数量，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

			-- 运输方式层级 的 规格品 货量占比：sum（柜型量） group by 会计期、运输方式(筛选Top清单标识为Y的航线)  / sum（柜型量） group by 会计期、运输方式
		insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,container_type
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
		  with tmp1 as(
		   select version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
				,top_flag
				,sum(container_qty) as container_qty
           from spec_route_tmp
		   where top_flag = 'Y'
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
				,top_flag
                ),
           tmp2 as(
                 select version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
				,sum(container_qty) as container_qty
           from spec_route_tmp
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
                )
          select t1.version_id
		        ,t1.version_code
                ,t1.period_id
                ,t1.transport_mode
                ,t1.container_type
                ,t1.top_flag
                ,'01'       as level_code
                ,'运输方式' as level_desc
                ,t1.container_qty  as container_qty
                ,(case when t2.container_qty = 0 then 0
                       else t1.container_qty/t2.container_qty
                  end) as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  tmp1 t1
          left join tmp2 t2
            on t1.version_id = t2.version_id
		   and t1.version_code = t2.version_code
           and t1.period_id = t2.period_id
           and t1.transport_mode = t2.transport_mode
           and t1.container_type = t2.container_type
               ;

			   	v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 5,
            p_log_cal_log_desc => '【 dm_fol_spec_info_t 运输方式层级规格品数量及占比，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

			-- 运输方式层级 带出非规格品数量
		insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,container_type
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
          select version_id
		        ,version_code
                ,period_id
                ,transport_mode
                ,container_type
                ,'N' top_flag
                ,'01'   as level_code
                ,'运输方式' as level_desc
                ,sum(container_qty)  as container_qty
                ,null as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  spec_route_tmp
		   where top_flag is null
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
				union all
				 select version_id
		        ,version_code
                ,period_id
                ,transport_mode
                ,container_type
                ,'ALL' top_flag
                ,'01'   as level_code
                ,'运输方式' as level_desc
                ,sum(container_qty)  as container_qty
                ,null as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  spec_route_tmp
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
               ;
/*
			   -- 运输方式层级月度总量
		insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		         version_id
				,version_code
                ,period_id
                ,transport_mode
                ,container_type
                ,top_flag
                ,level_code
                ,level_desc
                ,container_qty
                ,percent
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
				 select version_id
		        ,version_code
                ,period_id
                ,transport_mode
                ,container_type
                ,'ALL' top_flag
                ,'01'   as level_code
                ,'运输方式' as level_desc
                ,sum(container_qty)  as container_qty
                ,null as percent
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
          from  spec_route_tmp
           group by version_id
                ,version_code
				,period_id
				,transport_mode
				,container_type
               ;
*/
          v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 6,
            p_log_cal_log_desc => '【 dm_fol_spec_info_t 运输方式层级非规格品数量，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

    -- qwx1110218  从目标表取对应版本的数据，计算ALL柜型的量，图表没有展示货量占比，所以没有计算 
    insert into fin_dm_opt_foi.dm_fol_spec_info_t (
		       version_id
				 , version_code
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , container_type
         , top_flag
         , level_code
         , level_desc
         , container_qty
         , percent
         , remark
         , created_by
         , creation_date
         , last_updated_by
         , last_update_date
         , del_flag
    )
	  select version_id
		     , version_code
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , 'ALL' as container_type
         , top_flag
         , level_code
         , level_desc
         , sum(container_qty)  as container_qty
         , null as percent
         ,  '' as remark
         ,  -1 as created_by
         ,  current_timestamp as creation_date
         ,  -1 as last_updated_by
         ,  current_timestamp as last_update_date
         ,  'N' as del_flag
      from fin_dm_opt_foi.dm_fol_spec_info_t
     where version_id = v_max_version_id
     group by version_id
		     , version_code
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , top_flag
         , level_code
         , level_desc
    ;

         -- 清理已经写入版本信息表的数据
  delete from fin_dm_opt_foi.dm_fol_version_info_t
   where version_id =v_max_version_id
     and version_code = v_max_version_code
     and source_en_name = 'f_dm_fol_spec_info_t'
     and refresh_type = nvl(p_refresh_type,'4_AUTO')
     and step = 1
     and upper(del_flag) = 'N'
  ;

  if(p_refresh_type = '' or p_refresh_type is null) then
    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1, version_code = v_max_version_code
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_spec_info_t'
       and refresh_type = '4_AUTO'
       and upper(del_flag) = 'N'
    ;

  elseif(p_refresh_type = '1_刷新价格表') then
    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1, version_code = v_max_version_code
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_spec_info_t'
       and refresh_type = '4_AUTO'
       and upper(del_flag) = 'N'
    ;

	elseif(p_refresh_type = '2_刷新系统') then
    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1, version_code = v_max_version_code
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_spec_info_t'
       and refresh_type = '2_刷新系统'
       and upper(del_flag) = 'N'
    ;


  end if;

        v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 7,
            p_log_cal_log_desc => '【 dm_fol_version_info_t  成功数据写入到版本信息表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;



  exception
    when others then
       perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败

	 -- 失败数据写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id  as version_id
         , v_max_version_code as version_code
         , 2001 as step
         , 'f_dm_fol_spec_info_t' as source_en_name
         , '规格品信息表函数' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
         , 'version_code为航线清单表的最大版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_spec_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

