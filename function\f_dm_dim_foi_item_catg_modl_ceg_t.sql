-- Name: f_dm_dim_foi_item_catg_modl_ceg_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_dim_foi_item_catg_modl_ceg_t(f_dim_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$                 
  /*
  创建时间：2022-10-29
  创建人  ：唐钦
  背景描述：维度关联表,然后调用该函数将相对应的数据生成导入到目标表中
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_dim_foi_item_catg_modl_ceg_t()
  */  
  declare v_sp_name varchar(50) := 'f_dm_dim_foi_item_catg_modl_ceg_t';
  v_dml_row_count    number default 0;
  v_dim_version BIGINT;-- 映射表最新的版本号  
begin
  x_success_flag := '1';
 IF F_DIM_VERSION IS NULL THEN
   SELECT
        version_id INTO v_dim_version
    FROM
        FIN_DM_OPT_FOI.dm_foi_plan_version_t 
    WHERE
        creation_date =
         (SELECT MAX(creation_date)
            FROM FIN_DM_OPT_FOI.dm_foi_plan_version_t
           WHERE LOWER(data_type) = 'dimension'
             AND STATUS =1
        AND UPPER( del_flag ) = 'N');  
  ELSE V_DIM_VERSION := F_DIM_VERSION;
  END IF;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME      => v_sp_name,
                                      F_STEP_NUM     => 0,
                                      F_CAL_LOG_DESC => v_sp_name || '开始执行');
  -- 支持重跑，清除目标表要插入预测数据
  truncate table FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t;
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME       => v_sp_name,
                                      F_STEP_NUM      => 1,
                                      F_CAL_LOG_DESC  => '清空FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t表的数据,并取到映射表版本号：'||v_dim_version,
                                      F_DML_ROW_COUNT => SQL%ROWCOUNT,
                                      F_RESULT_STATUS => x_success_flag,
                                      F_ERRBUF        => 'SUCCESS');  
-- 取出l2层级是生产采购认证部的采购组织编码、名称及专家团的编码、名称
with dm_foi_l2_l3_tmp as
 (SELECT distinct l2_ceg_code,
                  l2_ceg_cn_name,
                  l3_ceg_code,
                  l3_ceg_cn_name,
                  l4_ceg_code,
                  l4_ceg_cn_name
    from dmdim.dm_dim_ceg_d -- 采购专家团维表
   where l2_ceg_code = '12229'  -- ICT生产采购组织
     and del_flag = 'N'
--     and scd_active_end_date >= '4712-12-31 00:00:00' -- 限制取最新一条数据
--     and enable_flag = 1
--     and scd_active_ind = 1
  ),
  opt_dim_item_tmp AS (
    SELECT
        * 
    FROM
        ( SELECT item_code, item_name, item_subtype_code,
                 ROW_NUMBER ( ) OVER ( PARTITION BY item_code ORDER BY src_last_update_date ) rn 
             FROM dwrdim.dwr_dim_material_code_d ) 
    WHERE rn = 1 
    ),        
opt_dim_ict_tmp as(
  SELECT *
  FROM FIN_DM_OPT_FOI.dm_dim_catg_modl_ceg_ict_t
  WHERE version_id = v_dim_version
  ),
-- 依据补录表为基础，取出l2-品类的编码及名称
dm_foi_ceg_catg_item_tmp as
 (SELECT distinct t2.item_code,
                  t2.item_name,
                  a.category_code,
                  a.category_name,
                  b.l4_ceg_code,
                  a.l4_ceg_short_cn_name,
                  a.l4_ceg_cn_name,
                  b.l3_ceg_code,
                  a.l3_ceg_short_cn_name,
                  a.l3_ceg_cn_name,
                  b.l2_ceg_code,
                  b.l2_ceg_cn_name
    from opt_dim_ict_tmp a -- 品类-模块-专家团映射表
    left join dm_foi_l2_l3_tmp b
      on a.l3_ceg_cn_name = b.l3_ceg_cn_name
     and a.l4_ceg_cn_name = b.l4_ceg_cn_name
     left join opt_dim_item_tmp t2 -- 物料编码临时表
      on a.category_code = t2.item_subtype_code
   where a.del_flag = 'N')   
-- 数据插入维度关联表
insert into FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t
 (item_code, -- item编码
  item_name, -- item中文名称
  category_code, -- 品类编码
  category_name, -- 品类中文名称
  l2_ceg_code, -- 采购组织编码
  l2_ceg_cn_name, -- 采购组织中文名称
  l3_ceg_code, -- 专家团编码
  l3_ceg_cn_name, -- 专家团中文名称
  l3_ceg_short_cn_name, -- 专家团简称
  l4_ceg_code, -- 模块编码
  l4_ceg_cn_name, -- 模块中文名称
  l4_ceg_short_cn_name, -- 模块简称
  created_by, creation_date, last_updated_by, last_update_date, del_flag)
SELECT item_code,
       item_name,
       category_code,
       category_name,
       l2_ceg_code,
       l2_ceg_cn_name,
       l3_ceg_code,
       l3_ceg_cn_name,
       l3_ceg_short_cn_name,
       l4_ceg_code,
       l4_ceg_cn_name,
       l4_ceg_short_cn_name,
       -1 as created_by,
       current_timestamp as creation_date,
       -1 as last_updated_by,
       current_timestamp as last_update_date,
       'N' as del_flag
  from dm_foi_ceg_catg_item_tmp;    
  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME       => V_SP_NAME,
                                      F_STEP_NUM      => 2,
                                      F_CAL_LOG_DESC  => '插入数据到FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t',
                                      F_DML_ROW_COUNT => SQL%ROWCOUNT,
                                      F_RESULT_STATUS => x_success_flag,
                                      F_ERRBUF        => 'SUCCESS');
-- 收集信息
ANALYSE FIN_DM_OPT_FOI.dm_dim_foi_item_catg_modl_ceg_t;
  --3.日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME      => V_SP_NAME,
                                      F_STEP_NUM     => 3,
                                      F_CAL_LOG_DESC => V_SP_NAME || '运行结束');

    return 'SUCCESS';
EXCEPTION
  WHEN OTHERS THEN
    x_success_flag := 0;  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME       => V_SP_NAME,
                                        F_CAL_LOG_DESC  => V_SP_NAME ||
                                                           '运行失败',
                                        F_RESULT_STATUS => x_success_flag,
                                        F_ERRBUF        => SQLSTATE || ':' ||
                                                           SQLERRM);
end; $$
/

