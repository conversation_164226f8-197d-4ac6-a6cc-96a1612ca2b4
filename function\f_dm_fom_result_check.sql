-- Name: f_dm_fom_result_check; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_result_check(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2024-05-14
创建人  ：黄心蕊 hwx1187045
背景描述：数据调用结果查询
参数描述：参数(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_RESULT_CHECK();
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME            VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FOM_RESULT_CHECK';
  V_ANNUAL_VERSION     BIGINT; --年度分析页面结果表版本号
  V_MONTH_VERSION      BIGINT; --月度分析页面结果表版本号
  V_ANNUAL_RESULT_FLAG INT; --年度版本号核查结果标签
  V_MONTH_RESULT_FLAG  INT; --月度版本号核查结果标签
  
BEGIN
X_RESULT_STATUS := '1';

--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  SELECT VERSION_ID
    INTO V_ANNUAL_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
   WHERE UPPER(DATA_TYPE) = 'ANNUAL'
     AND STATUS = 1
   ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
	 
  SELECT VERSION_ID
    INTO V_MONTH_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
   WHERE UPPER(DATA_TYPE) = 'MONTH'
     AND STATUS = 1
   ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
	 
  --1.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行月度分析页版本号：'||V_MONTH_VERSION||'，以及年度分析页版本号：'||V_ANNUAL_VERSION ,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  --年度分析页面 版本号一致性核查
  SELECT DECODE(SUM(CT_RESULT), 0, 0, 1) /*若所有结果表在最新版本号皆有数，返回0，否则返回1*/
    INTO V_ANNUAL_RESULT_FLAG
    FROM ( /*查询年度最新版本号在年度结果表中的数据量是否为0,有数返回*/
          --年度分析 涨跌幅状态码表
		  SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_STATUS_CODE_T 
           WHERE VERSION_ID = V_ANNUAL_VERSION
          UNION ALL
		  --年度分析 涨跌幅表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_AMP_T			
           WHERE VERSION_ID = V_ANNUAL_VERSION
          UNION ALL
		  --年度分析 权重表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T		
           WHERE VERSION_ID = V_ANNUAL_VERSION);
	
  --月度分析页面 版本号一致性核查
  SELECT DECODE(SUM(CT_RESULT), 0, 0, 1) /*若所有结果表在最新版本号皆有数，返回0，否则返回1*/
    INTO V_MONTH_RESULT_FLAG
    FROM ( /*查询月度最新版本号在月度结果表中的数据量是否为0*/
			--月度分析 规格品监控图表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_ITEM_SUM_AMT_T			
           WHERE VERSION_ID = V_MONTH_VERSION
          UNION ALL
		  --月度分析 权重表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_WEIGHT_T				
           WHERE VERSION_ID = V_MONTH_VERSION
          UNION ALL
		  --月度分析 指数表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_COST_IDX_T				
           WHERE VERSION_ID = V_MONTH_VERSION
          UNION ALL
		  --月度分析 同环比表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_RATE_T					
           WHERE VERSION_ID = V_MONTH_VERSION
          UNION ALL
		  --月度分析 热力图表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_ACTUAL_COST_T				
           WHERE VERSION_ID = V_MONTH_VERSION
          UNION ALL
		  --月度分析 成本分布图表
          SELECT DECODE(COUNT(1), 0, 1, 0) AS CT_RESULT
            FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_RESOURCE_TYPE_AMT_T	
           WHERE VERSION_ID = V_MONTH_VERSION);
	
  IF V_ANNUAL_RESULT_FLAG + V_MONTH_RESULT_FLAG = 0 THEN
		--2.写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
		F_STEP_NUM => 2,
		F_CAL_LOG_DESC => '函数执行完成' ,
		F_RESULT_STATUS => X_RESULT_STATUS,
		F_ERRBUF => 'SUCCESS'); 
	
    RETURN 'SUCCESS';
  ELSE
		--2.写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
		F_STEP_NUM => 2,
		F_CAL_LOG_DESC => '刷数错误' ,
		F_RESULT_STATUS => X_RESULT_STATUS,
		F_ERRBUF => 'SUCCESS'); 
	
    RETURN '刷数错误，请审视！';
  END IF;

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 
$$
/

