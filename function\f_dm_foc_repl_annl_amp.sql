-- Name: f_dm_foc_repl_annl_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_annl_amp(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年9月14日
  创建人  ：唐钦
  背景描述：根据年均本和权重值，计算研发替代指数逻辑
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_ANNL_AMP();
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_ANNL_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_PARENT_AMT VARCHAR(500);
  V_GROUP_LEVEL VARCHAR(200);
  V_BEFORE_TWO_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-2||12;
  V_BEFORE_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-1||12;
  V_CURR_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT);
  -- 去年YTD
  V_BEFORE_YTD_TWO_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-2||MONTH(CURRENT_TIMESTAMP)-1;
  V_BEFORE_YTD_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT)-1||MONTH(CURRENT_TIMESTAMP)-1;
  V_CURR_YTD_YEAR INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYYMM') AS INT);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T WHERE VERSION_ID = '||V_VERSION_ID;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建临时表
  DROP TABLE IF EXISTS FOC_REPL_ANNL_AMP_TMP;
  CREATE TEMPORARY TABLE FOC_REPL_ANNL_AMP_TMP (
         PERIOD_YEAR                INT,
         GROUP_CODE                 VARCHAR(50),
         GROUP_CN_NAME              VARCHAR(500),
         GROUP_LEVEL                VARCHAR(50),
         PARENT_CODE                VARCHAR(50),
         PARENT_CN_NAME             VARCHAR(500),
         ANNUAL_AMP                 NUMERIC,
         LV0_PROD_RND_TEAM_CODE     VARCHAR(50),
         LV0_PROD_RD_TEAM_CN_NAME   VARCHAR(200),
         LV1_PROD_RND_TEAM_CODE     VARCHAR(50),
         LV1_PROD_RD_TEAM_CN_NAME   VARCHAR(200),
         LV2_PROD_RND_TEAM_CODE     VARCHAR(50),
         LV2_PROD_RD_TEAM_CN_NAME   VARCHAR(200),
         LV3_PROD_RND_TEAM_CODE     VARCHAR(50),
         LV3_PROD_RD_TEAM_CN_NAME   VARCHAR(200),
         VIEW_FLAG                  VARCHAR(50),
         CALIBER_FLAG               VARCHAR(2),
         DATA_TYPE                  VARCHAR(50)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 计算研发替代指数
  INSERT INTO FOC_REPL_ANNL_AMP_TMP(
         PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,  
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,  
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,  
         LV3_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
    )
/*
  WITH LACK_PERIOD_TMP AS (
  SELECT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         REPLACEMENT_GROUP_ID,
         CALIBER_FLAG,
         VIEW_FLAG,
         'TOTAL' AS DATA_TYPE
      FROM(   
         SELECT PERIOD_YEAR,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                LV2_PROD_RND_TEAM_CODE,
                LV3_PROD_RND_TEAM_CODE,
                REPLACEMENT_GROUP_ID,
                CALIBER_FLAG,
                VIEW_FLAG,
                SUM(APP_FLAG) AS APP_FLAG 
            FROM(   
                 SELECT PERIOD_YEAR,
                        LV0_PROD_RND_TEAM_CODE,
                        LV1_PROD_RND_TEAM_CODE,
                        LV2_PROD_RND_TEAM_CODE,
                        LV3_PROD_RND_TEAM_CODE,
                        REPLACEMENT_GROUP_ID,
                        CALIBER_FLAG,
                        VIEW_FLAG,
                        DECODE(APPEND_FLAG,'Y',0,1) AS APP_FLAG 
                      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T 
                ) GROUP BY PERIOD_YEAR,
                           LV0_PROD_RND_TEAM_CODE,
                           LV1_PROD_RND_TEAM_CODE,
                           LV2_PROD_RND_TEAM_CODE,
                           LV3_PROD_RND_TEAM_CODE,
                           REPLACEMENT_GROUP_ID,
                           CALIBER_FLAG,
                           VIEW_FLAG     -- 为0的时候，表示整年无数据
         )WHERE APP_FLAG = 0
  UNION ALL 
  SELECT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         REPLACEMENT_GROUP_ID,
         CALIBER_FLAG,
         VIEW_FLAG,
         'YTD' AS DATA_TYPE
      FROM(   
         SELECT PERIOD_YEAR,
                LV0_PROD_RND_TEAM_CODE,
                LV1_PROD_RND_TEAM_CODE,
                LV2_PROD_RND_TEAM_CODE,
                LV3_PROD_RND_TEAM_CODE,
                REPLACEMENT_GROUP_ID,
                CALIBER_FLAG,
                VIEW_FLAG,
                SUM(APP_FLAG) AS APP_FLAG 
            FROM(   
                 SELECT PERIOD_YEAR,
                        LV0_PROD_RND_TEAM_CODE,
                        LV1_PROD_RND_TEAM_CODE,
                        LV2_PROD_RND_TEAM_CODE,
                        LV3_PROD_RND_TEAM_CODE,
                        REPLACEMENT_GROUP_ID,
                        CALIBER_FLAG,
                        VIEW_FLAG,
                        DECODE(APPEND_FLAG,'Y',0,1) AS APP_FLAG 
                      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T 
                     WHERE CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND MONTH(CURRENT_TIMESTAMP)-1   -- 只取每年1月-每年YTD月的数据值
                ) GROUP BY PERIOD_YEAR,
                           LV0_PROD_RND_TEAM_CODE,
                           LV1_PROD_RND_TEAM_CODE,
                           LV2_PROD_RND_TEAM_CODE,
                           LV3_PROD_RND_TEAM_CODE,
                           REPLACEMENT_GROUP_ID,
                           CALIBER_FLAG,
                           VIEW_FLAG     -- 为0的时候，表示整年无数据
         )WHERE APP_FLAG = 0
  ),
  PERIOD_DIM_TMP AS(
  SELECT DISTINCT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         REPLACEMENT_GROUP_ID,
         CALIBER_FLAG,
         VIEW_FLAG,
         'TOTAL' AS DATA_TYPE,
         MAX(PERIOD_ID) OVER(PARTITION BY T1.PERIOD_YEAR,T1.LV0_PROD_RND_TEAM_CODE,T1.LV1_PROD_RND_TEAM_CODE,T1.LV2_PROD_RND_TEAM_CODE,T1.LV3_PROD_RND_TEAM_CODE,T1.REPLACEMENT_GROUP_ID,T1.CALIBER_FLAG,T1.VIEW_FLAG) AS PERIOD_ID
       FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
       WHERE APPEND_FLAG = 'N'        -- 当前年份有数据的，取每年非补齐的最大月份数据
    UNION ALL 
      SELECT DISTINCT T1.PERIOD_YEAR,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV3_PROD_RND_TEAM_CODE,
         T1.REPLACEMENT_GROUP_ID,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         'TOTAL' AS DATA_TYPE,
         MAX(T1.PERIOD_ID) OVER(PARTITION BY T1.PERIOD_YEAR,T1.LV0_PROD_RND_TEAM_CODE,T1.LV1_PROD_RND_TEAM_CODE,T1.LV2_PROD_RND_TEAM_CODE,T1.LV3_PROD_RND_TEAM_CODE,T1.REPLACEMENT_GROUP_ID,T1.CALIBER_FLAG,T1.VIEW_FLAG) AS PERIOD_ID
       FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
       INNER JOIN LACK_PERIOD_TMP T2    -- 整年没有数据，即取最大月份的补齐数据
       ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'S1')
       AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'S2') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'S2')
       AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S3') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S3')
       AND NVL(T1.LV3_PROD_RND_TEAM_CODE,'S4') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'S4')
       AND T1.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       WHERE T2.DATA_TYPE = 'TOTAL' 
  UNION ALL 
  SELECT DISTINCT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         REPLACEMENT_GROUP_ID,
         CALIBER_FLAG,
         VIEW_FLAG,
         'YTD' AS DATA_TYPE,
         MAX(PERIOD_ID) OVER(PARTITION BY T1.PERIOD_YEAR,T1.LV0_PROD_RND_TEAM_CODE,T1.LV1_PROD_RND_TEAM_CODE,T1.LV2_PROD_RND_TEAM_CODE,T1.LV3_PROD_RND_TEAM_CODE,T1.REPLACEMENT_GROUP_ID,T1.CALIBER_FLAG,T1.VIEW_FLAG) AS PERIOD_ID
       FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
       WHERE APPEND_FLAG = 'N'        -- 当前年份有数据的，取每年非补齐的最大月份数据
       AND CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND MONTH(CURRENT_TIMESTAMP)-1   -- 只取每年1月-每年YTD月的数据值
    UNION ALL 
      SELECT DISTINCT T1.PERIOD_YEAR,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV3_PROD_RND_TEAM_CODE,
         T1.REPLACEMENT_GROUP_ID,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         'YTD' AS DATA_TYPE,
         MAX(T1.PERIOD_ID) OVER(PARTITION BY T1.PERIOD_YEAR,T1.LV0_PROD_RND_TEAM_CODE,T1.LV1_PROD_RND_TEAM_CODE,T1.LV2_PROD_RND_TEAM_CODE,T1.LV3_PROD_RND_TEAM_CODE,T1.REPLACEMENT_GROUP_ID,T1.CALIBER_FLAG,T1.VIEW_FLAG) AS PERIOD_ID
       FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
       INNER JOIN LACK_PERIOD_TMP T2    -- 整年没有数据，即取最大月份的补齐数据
       ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
       AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'S1')
       AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'S2') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'S2')
       AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S3') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S3')
       AND NVL(T1.LV3_PROD_RND_TEAM_CODE,'S4') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'S4')
       AND T1.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       WHERE T2.DATA_TYPE = 'YTD' 
    ) */

  WITH PERIOD_DIM_TMP AS(
  SELECT DISTINCT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         REPLACEMENT_GROUP_ID,
         CALIBER_FLAG,
         VIEW_FLAG,
         'TOTAL' AS DATA_TYPE,
         PERIOD_ID
       FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
       WHERE PERIOD_ID IN (V_BEFORE_TWO_YEAR,V_BEFORE_YEAR,V_CURR_YEAR)
       AND APPEND_FLAG = 'N'        -- 当前年份有数据的，取每年非补齐的最大月份数据
  UNION ALL 
  SELECT DISTINCT PERIOD_YEAR,
         LV0_PROD_RND_TEAM_CODE,
         LV1_PROD_RND_TEAM_CODE,
         LV2_PROD_RND_TEAM_CODE,
         LV3_PROD_RND_TEAM_CODE,
         REPLACEMENT_GROUP_ID,
         CALIBER_FLAG,
         VIEW_FLAG,
         'YTD' AS DATA_TYPE,
         PERIOD_ID
       FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
       WHERE PERIOD_ID IN (V_BEFORE_YTD_TWO_YEAR,V_BEFORE_YTD_YEAR,V_CURR_YTD_YEAR)
       AND APPEND_FLAG = 'N'        -- 当前年份有数据的，取每年非补齐的最大月份数据
--       AND CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND MONTH(CURRENT_TIMESTAMP)-1   -- 只取每年1月-每年YTD月的数据值
  )
  -- 计算BINDING组整年涨跌幅数据
  SELECT T1.PERIOD_YEAR,
         T1.REPLACEMENT_GROUP_ID AS GROUP_CODE,
         T1.REPLACEMENT_GROUP_CN_NAME AS GROUP_CN_NAME,
         'BIND' AS GROUP_LEVEL,
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RND_TEAM_CODE,'1',T1.LV1_PROD_RND_TEAM_CODE,'2',T1.LV2_PROD_RND_TEAM_CODE,'3',T1.LV3_PROD_RND_TEAM_CODE) AS PARENT_CODE,
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RD_TEAM_CN_NAME,'1',T1.LV1_PROD_RD_TEAM_CN_NAME,'2',T1.LV2_PROD_RD_TEAM_CN_NAME,'3',T1.LV3_PROD_RD_TEAM_CN_NAME) AS PARENT_CN_NAME,
         NVL(DECODE(T1.BASE_PERIOD_AVG_AMT,0,0,(T1.PERIOD_AVG_AMT/T1.BASE_PERIOD_AVG_AMT)-1),0) AS ANNUAL_AMP,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         DECODE(T1.VIEW_FLAG,'0',NULL,T1.LV1_PROD_RND_TEAM_CODE) AS LV1_PROD_RND_TEAM_CODE,
         DECODE(T1.VIEW_FLAG,'0',NULL,T1.LV1_PROD_RD_TEAM_CN_NAME) AS LV1_PROD_RD_TEAM_CN_NAME,
         CASE WHEN T1.VIEW_FLAG IN ('0','1') THEN NULL 
		      ELSE T1.LV2_PROD_RND_TEAM_CODE END AS LV2_PROD_RND_TEAM_CODE,
         CASE WHEN T1.VIEW_FLAG IN ('0','1') THEN NULL 
		      ELSE T1.LV2_PROD_RD_TEAM_CN_NAME END AS LV2_PROD_RD_TEAM_CN_NAME,
         DECODE(T1.VIEW_FLAG,'3',T1.LV3_PROD_RND_TEAM_CODE,NULL) AS LV3_PROD_RND_TEAM_CODE,
         DECODE(T1.VIEW_FLAG,'3',T1.LV3_PROD_RD_TEAM_CN_NAME,NULL) AS LV3_PROD_RD_TEAM_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         'TOTAL' AS DATA_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
      INNER JOIN PERIOD_DIM_TMP T2
	  ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
	  AND T1.PERIOD_ID = T2.PERIOD_ID
      AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'S1')
      AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'S2') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'S2')
      AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S3') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S3')
      AND NVL(T1.LV3_PROD_RND_TEAM_CODE,'S4') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'S4')
      AND T1.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T2.DATA_TYPE = 'TOTAL' 
  UNION ALL 
    -- 计算BINDING组年度YTD涨跌幅数据
  SELECT T1.PERIOD_YEAR,
         T1.REPLACEMENT_GROUP_ID AS GROUP_CODE,
         T1.REPLACEMENT_GROUP_CN_NAME AS GROUP_CN_NAME,
         'BIND' AS GROUP_LEVEL,
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RND_TEAM_CODE,'1',T1.LV1_PROD_RND_TEAM_CODE,'2',T1.LV2_PROD_RND_TEAM_CODE,'3',T1.LV3_PROD_RND_TEAM_CODE) AS PARENT_CODE,
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RD_TEAM_CN_NAME,'1',T1.LV1_PROD_RD_TEAM_CN_NAME,'2',T1.LV2_PROD_RD_TEAM_CN_NAME,'3',T1.LV3_PROD_RD_TEAM_CN_NAME) AS PARENT_CN_NAME,
         NVL(DECODE(T1.BASE_PERIOD_AVG_AMT,0,0,(T1.PERIOD_AVG_AMT/T1.BASE_PERIOD_AVG_AMT)-1),0) AS ANNUAL_AMP,
         T1.LV0_PROD_RND_TEAM_CODE,
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         DECODE(T1.VIEW_FLAG,'0',NULL,T1.LV1_PROD_RND_TEAM_CODE) AS LV1_PROD_RND_TEAM_CODE,
         DECODE(T1.VIEW_FLAG,'0',NULL,T1.LV1_PROD_RD_TEAM_CN_NAME) AS LV1_PROD_RD_TEAM_CN_NAME,
         CASE WHEN T1.VIEW_FLAG IN ('0','1') THEN NULL 
		      ELSE T1.LV2_PROD_RND_TEAM_CODE END AS LV2_PROD_RND_TEAM_CODE,
         CASE WHEN T1.VIEW_FLAG IN ('0','1') THEN NULL 
		      ELSE T1.LV2_PROD_RD_TEAM_CN_NAME END AS LV2_PROD_RD_TEAM_CN_NAME,
         DECODE(T1.VIEW_FLAG,'3',T1.LV3_PROD_RND_TEAM_CODE,NULL) AS LV3_PROD_RND_TEAM_CODE,
         DECODE(T1.VIEW_FLAG,'3',T1.LV3_PROD_RD_TEAM_CN_NAME,NULL) AS LV3_PROD_RD_TEAM_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         'YTD' AS DATA_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
      INNER JOIN PERIOD_DIM_TMP T2
	  ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
	  AND T1.PERIOD_ID = T2.PERIOD_ID
      AND NVL(T1.LV0_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV0_PROD_RND_TEAM_CODE,'S1')
      AND NVL(T1.LV1_PROD_RND_TEAM_CODE,'S2') = NVL(T2.LV1_PROD_RND_TEAM_CODE,'S2')
      AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S3') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S3')
      AND NVL(T1.LV3_PROD_RND_TEAM_CODE,'S4') = NVL(T2.LV3_PROD_RND_TEAM_CODE,'S4')
      AND T1.REPLACEMENT_GROUP_ID = T2.REPLACEMENT_GROUP_ID
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T2.DATA_TYPE = 'YTD' ;
    
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算整年/YTD的BINDING组涨跌幅数据，并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 计算LV0-LV3层级涨跌幅数据
  INSERT INTO FOC_REPL_ANNL_AMP_TMP(
         PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,  
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,  
         LV2_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
    )
  -- LV3层级
  WITH WEIGHT_AMP_TMP AS (
  SELECT T1.PERIOD_YEAR,             
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RND_TEAM_CODE,'1',T1.LV1_PROD_RND_TEAM_CODE,'2',T1.LV2_PROD_RND_TEAM_CODE,'3',T1.LV3_PROD_RND_TEAM_CODE) AS GROUP_CODE,              
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RD_TEAM_CN_NAME,'1',T1.LV1_PROD_RD_TEAM_CN_NAME,'2',T1.LV2_PROD_RD_TEAM_CN_NAME,'3',T1.LV3_PROD_RD_TEAM_CN_NAME) AS GROUP_CN_NAME,           
         DECODE(T1.VIEW_FLAG,'0','LV0','1','LV1','2','LV2','3','LV3') AS GROUP_LEVEL,             
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RND_TEAM_CODE,'1',T1.LV0_PROD_RND_TEAM_CODE,'2',T1.LV1_PROD_RND_TEAM_CODE,'3',T1.LV2_PROD_RND_TEAM_CODE) AS PARENT_CODE,             
         DECODE(T1.VIEW_FLAG,'0',T1.LV0_PROD_RD_TEAM_CN_NAME,'1',T1.LV0_PROD_RD_TEAM_CN_NAME,'2',T1.LV1_PROD_RD_TEAM_CN_NAME,'3',T1.LV2_PROD_RD_TEAM_CN_NAME) AS PARENT_CN_NAME,          
         T1.ANNUAL_AMP*T2.WEIGHT_RATE AS WEIGHT_AMP,              
         T1.LV0_PROD_RND_TEAM_CODE,  
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE,  
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,  
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.VIEW_FLAG,               
         T1.CALIBER_FLAG,
         T1.DATA_TYPE
      FROM FOC_REPL_ANNL_AMP_TMP T1
      INNER JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      WHERE T1.GROUP_LEVEL = 'BIND'      -- 选择子级-BIND层级
      AND T2.VERSION_ID = V_VERSION_ID
  )
  SELECT PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         SUM(WEIGHT_AMP) AS ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,  
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,  
         LV2_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
      FROM WEIGHT_AMP_TMP
      GROUP BY PERIOD_YEAR,             
               GROUP_CODE,              
               GROUP_CN_NAME,           
               GROUP_LEVEL,             
               PARENT_CODE,             
               PARENT_CN_NAME,          
               LV0_PROD_RND_TEAM_CODE,  
               LV0_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RND_TEAM_CODE,  
               LV1_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RND_TEAM_CODE,  
               LV2_PROD_RD_TEAM_CN_NAME,
               VIEW_FLAG,               
               CALIBER_FLAG,
               DATA_TYPE;
               
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算整年/YTD的LV3层级涨跌幅数据，并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  INSERT INTO FOC_REPL_ANNL_AMP_TMP(
         PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,  
         LV1_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
    )
  -- LV2层级
  WITH WEIGHT_AMP_TMP AS (
  SELECT T1.PERIOD_YEAR,             
         T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,              
         T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,           
         'LV2' AS GROUP_LEVEL,             
         T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,             
         T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,          
         T1.ANNUAL_AMP*T2.WEIGHT_RATE AS WEIGHT_AMP,              
         T1.LV0_PROD_RND_TEAM_CODE,  
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE,  
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.VIEW_FLAG,               
         T1.CALIBER_FLAG,
         T1.DATA_TYPE
      FROM FOC_REPL_ANNL_AMP_TMP T1
      INNER JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      WHERE T1.GROUP_LEVEL = 'LV3'      -- 选择子级-LV3层级
      AND T2.VERSION_ID = V_VERSION_ID
  )
  SELECT PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         SUM(WEIGHT_AMP) AS ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,  
         LV1_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
      FROM WEIGHT_AMP_TMP
      GROUP BY PERIOD_YEAR,             
               GROUP_CODE,              
               GROUP_CN_NAME,           
               GROUP_LEVEL,             
               PARENT_CODE,             
               PARENT_CN_NAME,          
               LV0_PROD_RND_TEAM_CODE,  
               LV0_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RND_TEAM_CODE,  
               LV1_PROD_RD_TEAM_CN_NAME,
               VIEW_FLAG,               
               CALIBER_FLAG,
               DATA_TYPE;
               
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算整年/YTD的LV2层级涨跌幅数据，并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  INSERT INTO FOC_REPL_ANNL_AMP_TMP(
         PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
    )
  -- LV1层级
  WITH WEIGHT_AMP_TMP AS (
  SELECT T1.PERIOD_YEAR,             
         T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,              
         T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,           
         'LV1' AS GROUP_LEVEL,             
         T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,             
         T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,          
         T1.ANNUAL_AMP*T2.WEIGHT_RATE AS WEIGHT_AMP,              
         T1.LV0_PROD_RND_TEAM_CODE,  
         T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.VIEW_FLAG,               
         T1.CALIBER_FLAG,
         T1.DATA_TYPE
      FROM FOC_REPL_ANNL_AMP_TMP T1
      INNER JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      WHERE T1.GROUP_LEVEL = 'LV2'      -- 选择子级-LV2层级
      AND T2.VERSION_ID = V_VERSION_ID
  )
  SELECT PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         SUM(WEIGHT_AMP) AS ANNUAL_AMP,              
         LV0_PROD_RND_TEAM_CODE,  
         LV0_PROD_RD_TEAM_CN_NAME,
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
      FROM WEIGHT_AMP_TMP
      GROUP BY PERIOD_YEAR,             
               GROUP_CODE,              
               GROUP_CN_NAME,           
               GROUP_LEVEL,             
               PARENT_CODE,             
               PARENT_CN_NAME,          
               LV0_PROD_RND_TEAM_CODE,  
               LV0_PROD_RD_TEAM_CN_NAME,
               VIEW_FLAG,               
               CALIBER_FLAG,
               DATA_TYPE;
               
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算整年/YTD的LV1层级涨跌幅数据，并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  INSERT INTO FOC_REPL_ANNL_AMP_TMP(
         PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         ANNUAL_AMP,              
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
    )
  -- LV2层级
  WITH WEIGHT_AMP_TMP AS (
  SELECT T1.PERIOD_YEAR,             
         T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,              
         T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,           
         'LV0' AS GROUP_LEVEL,             
         T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,             
         T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,          
         T1.ANNUAL_AMP*T2.WEIGHT_RATE AS WEIGHT_AMP,   
         T1.VIEW_FLAG,               
         T1.CALIBER_FLAG,
         T1.DATA_TYPE
      FROM FOC_REPL_ANNL_AMP_TMP T1
      INNER JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_WEIGHT_T T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.PARENT_CODE = T2.PARENT_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      WHERE T1.GROUP_LEVEL = 'LV1'      -- 选择子级-LV1层级
      AND T2.VERSION_ID = V_VERSION_ID
  )
  SELECT PERIOD_YEAR,             
         GROUP_CODE,              
         GROUP_CN_NAME,           
         GROUP_LEVEL,             
         PARENT_CODE,             
         PARENT_CN_NAME,          
         SUM(WEIGHT_AMP) AS ANNUAL_AMP,     
         VIEW_FLAG,               
         CALIBER_FLAG,
         DATA_TYPE
      FROM WEIGHT_AMP_TMP
      GROUP BY PERIOD_YEAR,             
               GROUP_CODE,              
               GROUP_CN_NAME,           
               GROUP_LEVEL,             
               PARENT_CODE,             
               PARENT_CN_NAME,          
               VIEW_FLAG,               
               CALIBER_FLAG,
               DATA_TYPE;
               
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '计算整年/YTD的LV0层级涨跌幅数据，并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将临时表的数据放进结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FOC_REPL_ANNL_AMP_TMP;
      
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'的研发替代指数的数据插入到结果表：DM_FOC_REPL_ANNL_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -----------------------------------------------------------------------标准成本年度涨跌幅计算逻辑---------------------------------------------------------------------------------------
  -- 标准成本涨跌幅计算逻辑
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH REPL_AMP_PER_TMP AS(
  -- 研发替代涨跌幅数据
  SELECT T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         NVL(T1.ANNUAL_AMP,0)*T2.RMB_COST_PER AS AMP_PER,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_COST_PER_T T2
	  LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T T1
	  ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
	  AND T1.VERSION_ID = T2.VERSION_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')   -- 限制层级
    ),
  SAME_AMP_TMP AS(
  -- 同编码去年全年比涨跌幅数据
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         CALIBER_FLAG,
         VIEW_FLAG,
         'TOTAL' AS DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_AMP_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND OVERSEA_FLAG = 'G'
	  AND LV0_PROD_LIST_CODE = 'GR'
	  AND GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')   -- 限制层级
  UNION ALL 
  -- 同编码去年YTD比涨跌幅数据
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         CALIBER_FLAG,
         VIEW_FLAG,
         'YTD' AS DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_YTD_AMP_T
	  WHERE VERSION_ID = V_VERSION_ID
	  AND GROUP_LEVEL IN ('LV3','LV2','LV1','LV0')   -- 限制层级
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         NVL(T1.PERIOD_YEAR,T2.PERIOD_YEAR) AS PERIOD_YEAR,
         NVL(T1.GROUP_CODE,T2.GROUP_CODE) AS GROUP_CODE,
         NVL(T1.GROUP_CN_NAME,T2.GROUP_CN_NAME) AS GROUP_CN_NAME,
         NVL(T1.GROUP_LEVEL,T2.GROUP_LEVEL) AS GROUP_LEVEL,
         (NVL(T1.AMP_PER,0)+NVL(T2.ANNUAL_AMP,0)) AS ANNUAL_AMP,
         NVL(T1.CALIBER_FLAG,T2.CALIBER_FLAG) AS CALIBER_FLAG,
         NVL(T1.VIEW_FLAG,T2.VIEW_FLAG) AS VIEW_FLAG,
         NVL(T1.DATA_TYPE,T2.DATA_TYPE) AS DATA_TYPE,
         NVL(T1.PARENT_CODE,T2.PARENT_CODE) AS PARENT_CODE,
         NVL(T1.PARENT_CN_NAME,T2.PARENT_CN_NAME) AS PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM REPL_AMP_PER_TMP T1
	  FULL JOIN SAME_AMP_TMP T2
	  ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
	  AND T1.GROUP_CODE = T2.GROUP_CODE
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.DATA_TYPE = T2.DATA_TYPE;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'的标准成本涨跌幅的数据插入到结果表：DM_FOC_REPL_STANDARD_ANNL_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	  
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T';
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T表统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

