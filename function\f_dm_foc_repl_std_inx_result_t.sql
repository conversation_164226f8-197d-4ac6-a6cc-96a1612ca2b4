-- Name: f_dm_foc_repl_std_inx_result_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_std_inx_result_t(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
最新修改时间 : 2025年1月10日10点07分
修改人：唐钦 twx1139790
创建时间 : 2024年10月15日18点07分
创建人   ：黄心蕊 hwx1187045
修改内容： 202410版本 替代指数及标准成本指数计算
参数	：F_VERSION_ID 版本号
		  X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

--替代指数,标准成本指数计算
----来源表
--均本表
DM_FOC_REPL_MTD_AVG_T

--权重表
权重表				DM_FOC_REPL_MTD_WEIGHT_T
同层级金额占比表	DM_FOC_REPL_MTD_COST_PER_T

----结果表
--替代指数
DM_FOC_REPL_MTD_INDEX_T

--标准成本指数
DM_FOC_REPL_STANDARD_MTD_INDEX_T

SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_STD_INX_RESULT_T();
***************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_STD_INX_RESULT_T';
  V_VERSION                 INT; --版本号
  V_EXCEPTION_FLAG          INT; --异常步骤
  V_MONTH_VERSION			INT;  --月度版本号
  V_NOW_YEAR       INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY') AS INT);     -- 取当前年份（若当月为1月时，即取去年年份）

BEGIN
X_RESULT_STATUS := '1';

 --写入日志
 V_EXCEPTION_FLAG	:= 0;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS');
  
  --版本号取数
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'CATEGORY'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
  --月度版本号取数
    SELECT VERSION_ID
      INTO V_MONTH_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ITEM'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--LV3层级收敛

--配置ID层级计算
DROP TABLE IF EXISTS DM_MID_REPL_IDX_TEMP;
CREATE TEMPORARY TABLE DM_MID_REPL_IDX_TEMP(
PERIOD_YEAR					INT				,
PERIOD_ID					INT             ,
LV0_PROD_RND_TEAM_CODE		VARCHAR(50)     ,
LV0_PROD_RD_TEAM_CN_NAME	VARCHAR(200)    ,
LV1_PROD_RND_TEAM_CODE		VARCHAR(50)     ,
LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200)    ,
LV2_PROD_RND_TEAM_CODE		VARCHAR(50)     ,
LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200)    ,
LV3_PROD_RND_TEAM_CODE		VARCHAR(50)     ,
LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200)    ,
GROUP_CODE					VARCHAR(100)     ,
GROUP_CN_NAME               VARCHAR(500)    ,
COST_INDEX					NUMERIC         ,
GROUP_LEVEL					VARCHAR(20)     ,
PARENT_CODE					VARCHAR(50)     ,
PARENT_CN_NAME              VARCHAR(200)    ,
CALIBER_FLAG				VARCHAR(20)     ,
VIEW_FLAG					VARCHAR(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE);


  --BINDING组计算
  INSERT INTO DM_MID_REPL_IDX_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     COST_INDEX ,
	 GROUP_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     CALIBER_FLAG,
     VIEW_FLAG)
    SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME,
           T1.LV3_PROD_RND_TEAM_CODE,
           T1.LV3_PROD_RD_TEAM_CN_NAME,
           T1.REPLACEMENT_GROUP_ID AS GROUP_CODE,
           T1.REPLACEMENT_GROUP_CN_NAME AS GROUP_CN_NAME,
           DECODE(T1.APPEND_FLAG ,'Y',0,T1.PERIOD_AVG_AMT / NULLIF(T1.BASE_PERIOD_AVG_AMT, 0) - 1) AS COST_INDEX,
           'BIND' AS GROUP_LEVEL,
           CASE T1.VIEW_FLAG 
		     WHEN 0 THEN T1.LV0_PROD_RND_TEAM_CODE
			 WHEN 1 THEN T1.LV1_PROD_RND_TEAM_CODE
			 WHEN 2 THEN T1.LV2_PROD_RND_TEAM_CODE
			 ELSE T1.LV3_PROD_RND_TEAM_CODE 
		   END AS PARENT_CODE,
           CASE T1.VIEW_FLAG 
		     WHEN 0 THEN T1.LV0_PROD_RD_TEAM_CN_NAME
			 WHEN 1 THEN T1.LV1_PROD_RD_TEAM_CN_NAME
			 WHEN 2 THEN T1.LV2_PROD_RD_TEAM_CN_NAME
			 ELSE T1.LV3_PROD_RD_TEAM_CN_NAME 
		   END AS PARENT_CN_NAME,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T T1
     WHERE T1.PERIOD_YEAR = V_NOW_YEAR;
	 
 --写入日志
 V_EXCEPTION_FLAG	:= 2;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => 'BINDING组替代指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  

  --BIND父级收敛 视角0-LV0 视角1-LV1 视角2-LV2 视角3-LV3
  INSERT INTO DM_MID_REPL_IDX_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     COST_INDEX,
	 GROUP_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     CALIBER_FLAG,
     VIEW_FLAG)
    SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME,
           T1.PARENT_CODE AS GROUP_CODE,
           T1.PARENT_CN_NAME AS GROUP_CN_NAME,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           CASE T1.VIEW_FLAG 
		     WHEN 0 THEN 'LV0'
			 WHEN 1 THEN 'LV1'
			 WHEN 2 THEN 'LV2'
			 ELSE 'LV3'
		   END AS GROUP_LEVEL,
           CASE T1.VIEW_FLAG  
		     WHEN 0 THEN ''
			 WHEN 1 THEN T1.LV0_PROD_RND_TEAM_CODE
			 WHEN 2 THEN T1.LV1_PROD_RND_TEAM_CODE
			 ELSE T1.LV2_PROD_RND_TEAM_CODE 
		   END AS PARENT_CODE,
           CASE T1.VIEW_FLAG  
		     WHEN 0 THEN ''
			 WHEN 1 THEN T1.LV0_PROD_RD_TEAM_CN_NAME
			 WHEN 2 THEN T1.LV1_PROD_RD_TEAM_CN_NAME
			 ELSE T1.LV2_PROD_RD_TEAM_CN_NAME 
		   END AS PARENT_CN_NAME,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG
      FROM DM_MID_REPL_IDX_TEMP T1
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T2.VERSION_ID = V_VERSION
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.PERIOD_ID = T2.PERIOD_ID
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
     WHERE T2.GROUP_LEVEL = 'BIND'
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.LV0_PROD_RND_TEAM_CODE,
              T1.LV0_PROD_RD_TEAM_CN_NAME,
              T1.LV1_PROD_RND_TEAM_CODE,
              T1.LV1_PROD_RD_TEAM_CN_NAME,
              T1.LV2_PROD_RND_TEAM_CODE,
              T1.LV2_PROD_RD_TEAM_CN_NAME,
              T1.LV3_PROD_RND_TEAM_CODE,
              T1.LV3_PROD_RD_TEAM_CN_NAME,
			  T1.PARENT_CODE,
			  T1.PARENT_CN_NAME,
              T1.CALIBER_FLAG,
              T1.VIEW_FLAG;
			  
 --写入日志
 V_EXCEPTION_FLAG	:= 3;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => 'LV3指数收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  --LV2收敛 
  INSERT INTO DM_MID_REPL_IDX_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     COST_INDEX ,
	 GROUP_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     CALIBER_FLAG,
     VIEW_FLAG)
    SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           'LV2' AS GROUP_LEVEL,
           T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG
      FROM DM_MID_REPL_IDX_TEMP T1
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       AND T2.VERSION_ID = V_VERSION
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.PERIOD_ID = T2.PERIOD_ID
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	 WHERE T1.GROUP_LEVEL = 'LV3'
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.LV0_PROD_RND_TEAM_CODE,
              T1.LV0_PROD_RD_TEAM_CN_NAME,
              T1.LV1_PROD_RND_TEAM_CODE,
              T1.LV1_PROD_RD_TEAM_CN_NAME,
              T1.LV2_PROD_RND_TEAM_CODE,
              T1.LV2_PROD_RD_TEAM_CN_NAME,
              T1.CALIBER_FLAG,
              T1.VIEW_FLAG;
			  
 --写入日志
 V_EXCEPTION_FLAG	:= 4;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => 'LV2指数收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  --LV1收敛 
  INSERT INTO DM_MID_REPL_IDX_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     COST_INDEX ,
	 GROUP_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     CALIBER_FLAG,
     VIEW_FLAG)
    SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           'LV1' AS GROUP_LEVEL,
           T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG
      FROM DM_MID_REPL_IDX_TEMP T1
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL 
       AND T2.VERSION_ID = V_VERSION
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.PERIOD_ID = T2.PERIOD_ID
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	 WHERE T1.GROUP_LEVEL = 'LV2'
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.LV0_PROD_RND_TEAM_CODE,
              T1.LV0_PROD_RD_TEAM_CN_NAME,
              T1.LV1_PROD_RND_TEAM_CODE,
              T1.LV1_PROD_RD_TEAM_CN_NAME,
              T1.CALIBER_FLAG,
              T1.VIEW_FLAG;
			  
 --写入日志
 V_EXCEPTION_FLAG	:= 5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => 'LV1指数收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

  --LV2收敛 
  INSERT INTO DM_MID_REPL_IDX_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     COST_INDEX,
	 GROUP_LEVEL,
     PARENT_CODE,
     PARENT_CN_NAME,
     CALIBER_FLAG,
     VIEW_FLAG)
    SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           'LV0' AS GROUP_LEVEL,
           '' AS PARENT_CODE,
           '' AS PARENT_CN_NAME,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG
      FROM DM_MID_REPL_IDX_TEMP T1
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_WEIGHT_T T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL 
       AND T2.VERSION_ID = V_VERSION
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.PERIOD_ID = T2.PERIOD_ID
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	 WHERE T1.GROUP_LEVEL = 'LV1'
     GROUP BY T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.LV0_PROD_RND_TEAM_CODE,
              T1.LV0_PROD_RD_TEAM_CN_NAME,
              T1.CALIBER_FLAG,
              T1.VIEW_FLAG;
			  
 --写入日志
 V_EXCEPTION_FLAG	:= 6;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => 'LV0指数收敛完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
			  
  DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_INDEX_T WHERE VERSION_ID = V_VERSION;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 7;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => '替代指数表同版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_INDEX_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     CALIBER_FLAG,
     VIEW_FLAG,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           CALIBER_FLAG,
           VIEW_FLAG,
           PARENT_CODE,
           PARENT_CN_NAME,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG
      FROM DM_MID_REPL_IDX_TEMP;
	  
 --写入日志
 V_EXCEPTION_FLAG	:= 8;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '替代指数结果表数据插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	  
  --标准成本指数计算
  DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_MTD_INDEX_T WHERE VERSION_ID = V_VERSION;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 9;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 9,
  F_CAL_LOG_DESC => '标准成本指数同版本数据删除完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  WITH FULL_DIM AS
   (SELECT DISTINCT PERIOD_YEAR,
                    PERIOD_ID,
                    GROUP_CODE,
                    GROUP_CN_NAME,
                    GROUP_LEVEL,
                    CALIBER_FLAG,
                    VIEW_FLAG,
                    PARENT_CODE,
                    PARENT_CN_NAME
      FROM (SELECT DISTINCT PERIOD_YEAR,
                            PERIOD_ID,
                            GROUP_CODE,
                            GROUP_CN_NAME,
                            GROUP_LEVEL,
                            CALIBER_FLAG,
                            VIEW_FLAG,
                            PARENT_CODE,
                            PARENT_CN_NAME
              FROM DM_MID_REPL_IDX_TEMP
             WHERE GROUP_LEVEL <> 'BIND'
            UNION ALL
            SELECT DISTINCT PERIOD_YEAR,
                            PERIOD_ID,
                            GROUP_CODE,
                            GROUP_CN_NAME,
                            GROUP_LEVEL,
                            CALIBER_FLAG,
                            VIEW_FLAG,
                            PARENT_CODE,
                            PARENT_CN_NAME
              FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_MTD_INDEX_T
             WHERE VERSION_ID = V_MONTH_VERSION)),
  
  SAME_IDX AS
   (SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           T1.COST_INDEX AS SAME_IDX,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_MTD_INDEX_T T1
      WHERE VERSION_ID = V_MONTH_VERSION
    /*  LEFT JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_COST_PER_T T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       AND T2.DATA_FLAG = 'SAME_CODE'
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
     WHERE T2.DATA_FLAG = 'SAME_CODE'*/
	 ),
  
  REPL_IDX AS
   (SELECT T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           NVL(T1.COST_INDEX,0) * T2.RMB_COST_PER AS REPL_IDX,
           T1.CALIBER_FLAG,
           T1.VIEW_FLAG,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME
      FROM DM_MID_REPL_IDX_TEMP T1
      INNER JOIN FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_COST_PER_T T2
        ON T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
       --AND T2.DATA_FLAG = 'REPLACE'
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND DECODE(T1.GROUP_LEVEL,'LV0',T1.GROUP_CODE,T1.PARENT_CODE) = T2.PARENT_CODE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.PERIOD_ID = T2.PERIOD_ID
       AND T2.VERSION_ID = V_VERSION
    -- WHERE T2.DATA_FLAG = 'REPLACE'
    
    )
   INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_MTD_INDEX_T
     (VERSION_ID,
      PERIOD_ID,
      PERIOD_YEAR,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
      CALIBER_FLAG,
      VIEW_FLAG,
      PARENT_CODE,
      PARENT_CN_NAME,
      CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG)
  SELECT V_VERSION AS VERSION_ID,
         T1.PERIOD_ID,
		 T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         NVL(T2.SAME_IDX,0) + T3.REPL_IDX,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
    FROM FULL_DIM T1
    LEFT JOIN SAME_IDX T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
      and T1.PERIOD_ID = T2.PERIOD_ID
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
     AND NVL(T1.PARENT_CODE,'S1') = NVL(T2.PARENT_CODE,'S1')
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
    LEFT JOIN REPL_IDX T3
      ON T1.GROUP_CODE = T3.GROUP_CODE
      and T1.PERIOD_ID = T3.PERIOD_ID     --不加数据发散
     AND T1.GROUP_LEVEL = T3.GROUP_LEVEL
     AND T1.CALIBER_FLAG = T3.CALIBER_FLAG
     AND NVL(T1.PARENT_CODE,'S2') = NVL(T3.PARENT_CODE,'S2')
     AND T1.VIEW_FLAG = T3.VIEW_FLAG;
	 
 --写入日志
 V_EXCEPTION_FLAG	:= 10;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 10,
  F_CAL_LOG_DESC => '标准成本指数结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

