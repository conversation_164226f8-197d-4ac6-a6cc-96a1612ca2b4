-- Name: f_dm_repl_mid_mon_group_id; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_repl_mid_mon_group_id(f_caliber_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*

最后修改人: 罗若文
背景描述:分视角统计替代ID的月卷积发货额


参数描述:f_caliber_flag : 业务口径(R：收入时点,C：发货成本)), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T(发货成本),FIN_DM_OPT_FOI.DM_FOC_REVENUE_ITEM_SHIP_DTL_T(收入时点)
目标表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_REPL_MID_MON_GROUP_ID()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_REPL_MID_MON_GROUP_ID'; --存储过程名称
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_BEGIN_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP)-3;  -- 系统年-3的年份
  V_END_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);  -- 系统年
  V_SQL        TEXT;   --SQL逻辑
  V_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50); 
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV1_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV1_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV2_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV2_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_INSERT_LV1_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV2_PROD_RND_TEAM_CODE VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(200);
  V_JOIN_TABLE VARCHAR(200);
  
  -- 7月版本需求新增
  V_FROM_TABLE VARCHAR(200); -- 来源表
  V_TO_TABLE VARCHAR(200); -- 目标表
  V_VIEW_CNT BIGINT; 

  V_VIEW_BEGIN BIGINT; /*新增(视角)开始值*/
  V_VIEW_NUM BIGINT;
  V_SQL_VIEW VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_COMDITION_BG VARCHAR(300);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,时点：'||F_CALIBER_FLAG);

  --对入参合法性判断
  IF  F_CALIBER_FLAG NOT IN ('C','R') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
  IF F_CALIBER_FLAG = 'C' THEN -- ICT 发货成本
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SHIP_RAW_DATA_T';--来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_MID_MON_GROUP_T';--目标表  
     V_VIEW_CNT := 3; --通用颗粒度视角从0到3
     V_VIEW_BEGIN := 0; 

  ELSIF F_CALIBER_FLAG = 'R'  THEN -- IAS 收入时点 202407新增
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_INCOME_RAW_DATA_T'; --来源表
	 V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_MID_MON_GROUP_T';--目标表  
     V_VIEW_CNT := 3; --通用颗粒度视角从0到3
     V_VIEW_BEGIN := 0; 
   
  
  ELSE
    NULL;
  END IF;

  
  --清空目标表数据
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表开始------'); 
 DELETE FROM  DM_FOC_REPL_MID_MON_GROUP_T WHERE CALIBER_FLAG = F_CALIBER_FLAG ;
 
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表结束------'); 
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  DBMS_OUTPUT.PUT_LINE('1:-----创建月卷积发货额临时表------');   
  --创建月卷积发货额临时表
    DROP TABLE IF EXISTS BASE_DATA_TEMP;
    CREATE TEMPORARY TABLE BASE_DATA_TEMP (
        VERSION_ID 					  BIGINT,
        PERIOD_YEAR 			      BIGINT,
        PERIOD_ID 					  BIGINT,
        LV0_PROD_RND_TEAM_CODE        VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME      VARCHAR(100),
        LV1_PROD_RND_TEAM_CODE        VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME      VARCHAR(100),
        LV2_PROD_RND_TEAM_CODE        VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME      VARCHAR(100),
        LV3_PROD_RND_TEAM_CODE        VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME      VARCHAR(100),
		REPLACEMENT_GROUP_ID 		  VARCHAR(50),
		BINDING_CONFIG_ID 			  VARCHAR(50),
		REPLACEMENT_DESCRIPTION 	  VARCHAR(2000),
		REPLACING_LY_SHIP_QTY 		  NUMERIC,
		REPLACING_CY_SHIP_QTY 		  NUMERIC,
		RMB_AAA_REP_BASELINE_COST_AMT NUMERIC,
		RMB_AAA_BINDING_CUR_COST_AMT  NUMERIC,
		RMB_COST_AMT 				  NUMERIC,
		LAST_AMT					  NUMERIC,
	    CURRENT_AMT					  NUMERIC
		
		
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID,BINDING_CONFIG_ID);
    
    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => '月卷积发货额临时表创建完成,f_caliber_flag:'||f_caliber_flag,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
        
    --7月版本需求新增
    --重置变量入参
    V_LV2_PROD_RND_TEAM_CODE := 'LV2_PROD_RND_TEAM_CODE,';
    V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';

    V_IN_LV2_PROD_RND_TEAM_CODE := 'T.LV2_PROD_RND_TEAM_CODE,';
    V_IN_LV2_PROD_RD_TEAM_CN_NAME :='T.LV2_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME := 'T.LV3_PROD_RD_TEAM_CN_NAME,';
	




    
    DBMS_OUTPUT.PUT_LINE('2:----开始插入数据BASE_DATA_TEMP------'); 
    
  --该数据区分国内和海外
      
      V_SQL := 
        'INSERT INTO BASE_DATA_TEMP(
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,'||
                V_LV2_PROD_RND_TEAM_CODE ||
                V_LV2_PROD_RD_TEAM_CN_NAME ||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||'
				REPLACEMENT_GROUP_ID ,
				BINDING_CONFIG_ID ,
				REPLACEMENT_DESCRIPTION ,
				REPLACING_LY_SHIP_QTY,
				REPLACING_CY_SHIP_QTY,
				RMB_AAA_REP_BASELINE_COST_AMT,
				RMB_AAA_BINDING_CUR_COST_AMT,
				RMB_COST_AMT,
				LAST_AMT,
				CURRENT_AMT
                )
            SELECT distinct T.VERSION_ID,
             SUBSTR(T.PERIOD_ID,0,4) AS PERIOD_YEAR,
             T.PERIOD_ID,
             T.LV0_PROD_RND_TEAM_CODE,
             T.LV0_PROD_RD_TEAM_CN_NAME,
             T.LV1_PROD_RND_TEAM_CODE,
             T.LV1_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||'
			REPLACEMENT_GROUP_ID ,
			BINDING_CONFIG_ID ,
			REPLACEMENT_DESCRIPTION ,
			REPLACING_LY_SHIP_QTY,
			REPLACING_CY_SHIP_QTY,
			RMB_AAA_REP_BASELINE_COST_AMT,
			RMB_AAA_BINDING_CUR_COST_AMT,
			REPLACING_CY_SHIP_QTY * RMB_AAA_BINDING_CUR_COST_AMT AS RMB_COST_AMT,
			RMB_AAA_REP_BASELINE_COST_AMT * REPLACING_LY_SHIP_QTY as LAST_AMT,
			RMB_AAA_REP_BASELINE_COST_AMT * REPLACING_CY_SHIP_QTY as CURRENT_AMT
			
          FROM '||V_FROM_TABLE ||' T
          WHERE  1 = 1
		  ';
		  
	 DBMS_OUTPUT.PUT_LINE(V_SQL);  
     EXECUTE IMMEDIATE V_SQL;
  
   
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入会话级临时表,时点：'||F_CALIBER_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   

   
  DBMS_OUTPUT.PUT_LINE('8:----版本号赋值.------'); 
  

--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	  

       --创建ITEM月卷积发货额临时表
    DROP TABLE IF EXISTS VIEW_DATA_TEMP;
    CREATE TEMPORARY TABLE VIEW_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
		REPLACEMENT_GROUP_ID VARCHAR(50),
		BINDING_CONFIG_ID VARCHAR(50),
		REPLACEMENT_DESCRIPTION VARCHAR(2000),
		REPLACING_LY_SHIP_QTY NUMERIC,
		REPLACING_CY_SHIP_QTY NUMERIC,
		RMB_AAA_REP_BASELINE_COST_AMT NUMERIC,
		RMB_AAA_BINDING_CUR_COST_AMT NUMERIC,
		RMB_COST_AMT  NUMERIC,
		LAST_AMT					  NUMERIC,
	    CURRENT_AMT					  NUMERIC,
        VIEW_FLAG VARCHAR(2)
    )
    ON COMMIT PRESERVE ROWS 
    DISTRIBUTE BY HASH(PERIOD_ID,BINDING_CONFIG_ID);
    DBMS_OUTPUT.PUT_LINE('----插入分视角下计算ITEM的月卷积额临时表【VIEW_DATA_TEMP】------'); 
    
 --根据不同视角表示, 循环收敛均价, 并且插数
  FOR VIEW_NUM IN V_VIEW_BEGIN..V_VIEW_CNT LOOP

    --重置变量入参
    V_LV1_PROD_RND_TEAM_CODE := 'LV1_PROD_RND_TEAM_CODE,';
    V_LV1_PROD_RD_TEAM_CN_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
    V_LV2_PROD_RND_TEAM_CODE := 'LV2_PROD_RND_TEAM_CODE,';
    V_LV2_PROD_RD_TEAM_CN_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';

    V_INSERT_LV1_PROD_RND_TEAM_CODE := ' AND A.LV1_PROD_RND_TEAM_CODE = B.LV1_PROD_RND_TEAM_CODE';
    V_INSERT_LV2_PROD_RND_TEAM_CODE := ' AND A.LV2_PROD_RND_TEAM_CODE = B.LV2_PROD_RND_TEAM_CODE';
    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND A.LV3_PROD_RND_TEAM_CODE = B.LV3_PROD_RND_TEAM_CODE';

	
    V_IN_LV1_PROD_RND_TEAM_CODE := 'A.LV1_PROD_RND_TEAM_CODE,';
    V_IN_LV1_PROD_RD_TEAM_CN_NAME :='A.LV1_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV2_PROD_RND_TEAM_CODE := 'A.LV2_PROD_RND_TEAM_CODE,';
    V_IN_LV2_PROD_RD_TEAM_CN_NAME :='A.LV2_PROD_RD_TEAM_CN_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'A.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='A.LV3_PROD_RD_TEAM_CN_NAME,';

    V_SQL_VIEW := 'AND VIEW_FLAG = '||V_VIEW_NUM;   -- 在IF判断重赋值之前取到的V_VIEW_NUM值为上一次循环结束时的赋值；即本次循环的下一粒度视角。
    V_TMP_TABLE := 'VIEW_DATA_TEMP';
  
   DBMS_OUTPUT.PUT_LINE('9:----根据不同视角表示, 循环收敛均价, 并且插数------'); 
   
   DBMS_OUTPUT.PUT_LINE('9——1:----内部循环收敛继续------'); 

   
   
   
        --视角1时
      IF (VIEW_NUM = 3 ) THEN 
      V_VIEW_NUM := 0;
      V_LV1_PROD_RND_TEAM_CODE := '';
      V_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';

      V_IN_LV1_PROD_RND_TEAM_CODE := '';
      V_IN_LV1_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';

      V_INSERT_LV1_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';

	
	
	   
   
      ELSIF (VIEW_NUM = 2 ) THEN 
      V_VIEW_NUM := 1;
      V_LV2_PROD_RND_TEAM_CODE := '';
      V_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';


      V_IN_LV2_PROD_RND_TEAM_CODE := '';
      V_IN_LV2_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
	
	  V_INSERT_LV2_PROD_RND_TEAM_CODE := '';
	  V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
	
	
    --视角3时
       ELSIF (VIEW_NUM = 1 ) THEN  
      V_VIEW_NUM := 2;
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';

      V_IN_LV3_PROD_RND_TEAM_CODE := '';
      V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';

      
      V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
	
     
	
	
	  --视角4时
	  ELSIF (VIEW_NUM = 0 ) THEN
      V_VIEW_NUM := 3;
     
	  V_SQL_VIEW := '';
      V_TMP_TABLE := 'BASE_DATA_TEMP';
	
    END IF;

    --插入分视角下计算ITEM的月卷积额临时表
    V_SQL := 
    'INSERT INTO VIEW_DATA_TEMP
      (
	   VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,'||
       V_LV1_PROD_RND_TEAM_CODE ||
       V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE ||
       V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||'
	   REPLACEMENT_GROUP_ID ,
	   BINDING_CONFIG_ID ,
	   REPLACEMENT_DESCRIPTION ,
	   REPLACING_LY_SHIP_QTY,
	   REPLACING_CY_SHIP_QTY,
	   RMB_AAA_REP_BASELINE_COST_AMT,
	   RMB_AAA_BINDING_CUR_COST_AMT,
	   RMB_COST_AMT,
	   LAST_AMT,
	   CURRENT_AMT,
       VIEW_FLAG
	   )
      SELECT DISTINCT A.VERSION_ID,
             A.PERIOD_YEAR,
             A.PERIOD_ID,
             A.LV0_PROD_RND_TEAM_CODE,
             A.LV0_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV1_PROD_RND_TEAM_CODE ||
             V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||'
			 A.REPLACEMENT_GROUP_ID ,
	         A.BINDING_CONFIG_ID ,
			 A.REPLACEMENT_DESCRIPTION ,
			 SUM(A.REPLACING_LY_SHIP_QTY) AS REPLACING_LY_SHIP_QTY,
			 SUM(A.REPLACING_CY_SHIP_QTY) AS REPLACING_CY_SHIP_QTY, 
			 SUM(A.RMB_AAA_REP_BASELINE_COST_AMT) AS RMB_AAA_REP_BASELINE_COST_AMT,
			 SUM(A.RMB_AAA_BINDING_CUR_COST_AMT) AS RMB_AAA_BINDING_CUR_COST_AMT,
			 SUM(A.RMB_COST_AMT) AS RMB_COST_AMT,
			 SUM(A.LAST_AMT) AS  LAST_AMT,
			 SUM(A.CURRENT_AMT)	AS  CURRENT_AMT,
             '||V_VIEW_NUM ||' AS VIEW_FLAG
        FROM '||V_TMP_TABLE||' A 
       WHERE 1 = 1
       '||V_SQL_VIEW||'
       GROUP BY A.VERSION_ID,
             A.PERIOD_YEAR,
             A.PERIOD_ID,
             A.LV0_PROD_RND_TEAM_CODE,
             A.LV0_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV1_PROD_RND_TEAM_CODE ||
             V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||'
			 A.REPLACEMENT_GROUP_ID ,
	         A.BINDING_CONFIG_ID ,
			 A.REPLACEMENT_DESCRIPTION ';
			 
  DBMS_OUTPUT.PUT_LINE(V_SQL);  
  EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE('4:----卷积金额插入临时表------');    
  
          
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '所有数据计算完，插入会话级临时表,时点：'||F_CALIBER_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
 
  DBMS_OUTPUT.PUT_LINE('-----开始插入结果表------'); 
    V_SQL := 
    'INSERT INTO '||V_TO_TABLE||'
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_PROD_RND_TEAM_CODE,
       LV0_PROD_RD_TEAM_CN_NAME,'||
       V_LV1_PROD_RND_TEAM_CODE ||
       V_LV1_PROD_RD_TEAM_CN_NAME ||
       V_LV2_PROD_RND_TEAM_CODE ||
       V_LV2_PROD_RD_TEAM_CN_NAME ||
       V_LV3_PROD_RND_TEAM_CODE ||
       V_LV3_PROD_RD_TEAM_CN_NAME ||'
	   REPLACEMENT_GROUP_ID ,
	   BINDING_CONFIG_ID ,
	   REPLACEMENT_DESCRIPTION ,
	   REPLACING_LY_SHIP_QTY,
	   REPLACING_CY_SHIP_QTY,
	   RMB_AAA_REP_BASELINE_COST_AMT,
	   RMB_AAA_BINDING_CUR_COST_AMT,
	   RMB_COST_AMT,
	   LAST_AMT,
	   CURRENT_AMT,
       VIEW_FLAG,
	   CALIBER_FLAG,
	   CREATED_BY ,
	   CREATION_DATE ,
	   LAST_UPDATED_BY ,
	   LAST_UPDATE_DATE ,
	   DEL_FLAG
	   )
      SELECT '||V_VERSION_ID||',
             A.PERIOD_YEAR,
             A.PERIOD_ID,
             A.LV0_PROD_RND_TEAM_CODE,
             A.LV0_PROD_RD_TEAM_CN_NAME,'||
             V_IN_LV1_PROD_RND_TEAM_CODE ||
             V_IN_LV1_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV2_PROD_RND_TEAM_CODE ||
             V_IN_LV2_PROD_RD_TEAM_CN_NAME ||
             V_IN_LV3_PROD_RND_TEAM_CODE ||
             V_IN_LV3_PROD_RD_TEAM_CN_NAME ||'
			A.REPLACEMENT_GROUP_ID ,
			A.BINDING_CONFIG_ID ,
			A.REPLACEMENT_DESCRIPTION ,
			A.REPLACING_LY_SHIP_QTY,
			A.REPLACING_CY_SHIP_QTY,
			A.RMB_AAA_REP_BASELINE_COST_AMT,
			A.RMB_AAA_BINDING_CUR_COST_AMT,
			A.RMB_COST_AMT,
			A.LAST_AMT,
	        A.CURRENT_AMT,
			A.VIEW_FLAG,
			'''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,
			-1 AS CREATED_BY ,
			CURRENT_TIMESTAMP AS CREATION_DATE ,
			-1 AS LAST_UPDATED_BY ,
			CURRENT_TIMESTAMP AS LAST_UPDATE_DATE ,
			''N'' AS DEL_FLAG
        FROM VIEW_DATA_TEMP A 
        WHERE VIEW_FLAG = '||V_VIEW_NUM;
  DBMS_OUTPUT.PUT_LINE(V_SQL);  
  EXECUTE IMMEDIATE V_SQL;
  
  DBMS_OUTPUT.PUT_LINE('-----开始插入结果表【结束】------'); 


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '视角标识:'||V_VIEW_NUM||', 计算的月卷积发货额写入目标表, 版本号='||V_VERSION_ID||',时点：'||F_CALIBER_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
   
  END LOOP;
     DBMS_OUTPUT.PUT_LINE('10:----所有循环结束------'); 

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
  
  RETURN 'SUCCESS';
        
    EXCEPTION
    WHEN OTHERS THEN
    X_RESULT_STATUS := '0';
    
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败,时点：'||F_CALIBER_FLAG, 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
   
END
$$
/

