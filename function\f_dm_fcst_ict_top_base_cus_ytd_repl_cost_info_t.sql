-- Name: f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_top_base_cus_ytd_repl_cost_info_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text, f_custom_id bigint DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

	/*
创建时间：2024-02-15
创建人  ：QWX1110218
修改人：TWX1139790
背景描述：产业成本指数ICT-编码替换关系页面TOP-SPART月累计实时虚化发货量、成本金额逻辑
参数描述：参数一： F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本；自动调度也会传值；
          参数二： F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录；自动调度也会传值；
          参数三： F_KEYSTR	密钥
		      参数四:  F_CUSTOM_ID 组合ID
          参数五： X_RESULT_STATUS 运行状态返回值 'SUCCESS'为成功，'0'为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_TOP_BASE_CUS_YTD_REPL_COST_INFO_T()
变更记录-202503：
部分会话级临时表由先建表再INSERT INTO的形式调整为CREATE TEMPORARY TABLE XXX AS的形式，缩短代码行数
*/


DECLARE
  V_SP_NAME            VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_TOP_BASE_CUS_YTD_REPL_COST_INFO_T';
  V_VERSION_ID         BIGINT;        --版本号
  V_FROM_AMT_TABLE     VARCHAR(100);  -- 来源表
  V_FROM_DIM_TABLE     VARCHAR(100);  -- 维表
  V_TO_QTY_TABLE       VARCHAR(100);  -- 量目标表
  V_SQL_FROM_PBI_PART    TEXT;	-- 来源表的PBI层级查询字段
  V_GS_DECRYPT_COST_AMT  TEXT;	-- 解密金额（PSP成本不用解密；STD成本需要解密）
  V_GS_ENCRYPT_COST_AMT  TEXT;	-- 加密金额（PSP成本不用加密；STD成本需要加密）
  V_BASE_PERIOD_ID      INT;   -- 默认基期
  V_MAX_REPL_VERSION_ID INT; -- 替换关系维表最大版本ID
  V_SPART_CODE   VARCHAR(50);
  V_PARENT_LEVEL VARCHAR(50);
  V_SQL         TEXT;
  V_STEP_NUM    INT;


BEGIN

	X_RESULT_STATUS := 'SUCCESS';        --1表示成功
	V_STEP_NUM = 1;

	-- 开始记录日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
      F_SP_NAME => V_SP_NAME,    --SP名称
      F_STEP_NUM => V_STEP_NUM,
      F_CAL_LOG_DESC => '函数 '||V_SP_NAME||' 开始运行',--日志描述
      F_FORMULA_SQL_TXT  => V_SQL,
      F_DML_ROW_COUNT => SQL%ROWCOUNT,
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS'
  );


  -- 创建临时表
  DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP2;
  CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP2(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , PERIOD_YEAR                  INT             -- 会计年
       , PBI_DIM_CODE                 VARCHAR(50)
       , PBI_DIM_CN_NAME              VARCHAR(200)
       , GROUP_CODE                   VARCHAR(50)     -- 各层级编码
       , GROUP_CN_NAME                VARCHAR(50)     -- 各层级中文名称
       , LV0_CODE                     VARCHAR(50)     -- LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- LV0名称
       , LV1_CODE                     VARCHAR(50)     -- LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- LV1名称
       , LV2_CODE                     VARCHAR(50)     -- LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- LV2名称
       , LV3_CODE                     VARCHAR(50)     -- LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- LV3名称
       , PARENT_CODE                  VARCHAR(50)
       , PARENT_CN_NAME               VARCHAR(200)
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , GROUP_LEVEL                  VARCHAR(50)
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;


  -- 来源表
  V_FROM_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_SUM_DETAIL_SPART_T';  -- 月均本累积表

  V_FROM_DIM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_DIM_T';  -- 组合虚化维表

  -- 量目标表
  V_TO_QTY_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_BASE_CUS_YTD_REPL_INFO_IDX_T';  -- 虚化-编码替换月累计信息表

  IF(F_COST_TYPE = 'PSP') THEN
    V_GS_DECRYPT_COST_AMT := ', RMB_AVG_AMT';  -- 解密
    V_GS_ENCRYPT_COST_AMT := ', NVL(RMB_COST_AMT::NUMERIC,0) AS RMB_COST_AMT';  -- 加密

  ELSEIF(F_COST_TYPE = 'STD') THEN
    V_GS_DECRYPT_COST_AMT := ', GS_DECRYPT(RMB_AVG_AMT,'''||F_KEYSTR||''',''AES128'',''CBC'',''SHA256'') AS RMB_AVG_AMT';  -- 解密
    V_GS_ENCRYPT_COST_AMT := ', GS_ENCRYPT(RMB_COST_AMT,'''||F_KEYSTR||''',''AES128'',''CBC'',''SHA256'') AS RMB_COST_AMT';  -- 只有SPART层级需要加密

  END IF;

  IF(F_GRANULARITY_TYPE = 'IRB') THEN
    -- 来源表的查询字段
    V_SQL_FROM_PBI_PART := ', LV0_PROD_RND_TEAM_CODE AS LV0_CODE
                       , LV0_PROD_RD_TEAM_CN_NAME    AS LV0_CN_NAME
                       , LV1_PROD_RND_TEAM_CODE      AS LV1_CODE
                       , LV1_PROD_RD_TEAM_CN_NAME    AS LV1_CN_NAME
                       , LV2_PROD_RND_TEAM_CODE      AS LV2_CODE
                       , LV2_PROD_RD_TEAM_CN_NAME    AS LV2_CN_NAME
                       , LV3_PROD_RND_TEAM_CODE      AS LV3_CODE
                       , LV3_PROD_RD_TEAM_CN_NAME    AS LV3_CN_NAME
                       , LV4_PROD_RND_TEAM_CODE      AS LV4_CODE
                       , LV4_PROD_RD_TEAM_CN_NAME    AS LV4_CN_NAME
                      ';

  ELSEIF(F_GRANULARITY_TYPE = 'INDUS') THEN
    -- 来源表的查询字段
    V_SQL_FROM_PBI_PART := ', LV0_INDUSTRY_CATG_CODE   AS LV0_CODE
                       , LV0_INDUSTRY_CATG_CN_NAME     AS LV0_CN_NAME
                       , LV1_INDUSTRY_CATG_CODE        AS LV1_CODE
                       , LV1_INDUSTRY_CATG_CN_NAME     AS LV1_CN_NAME
                       , LV2_INDUSTRY_CATG_CODE        AS LV2_CODE
                       , LV2_INDUSTRY_CATG_CN_NAME     AS LV2_CN_NAME
                       , LV3_INDUSTRY_CATG_CODE        AS LV3_CODE
                       , LV3_INDUSTRY_CATG_CN_NAME     AS LV3_CN_NAME
                       , LV4_INDUSTRY_CATG_CODE        AS LV4_CODE
                       , LV4_INDUSTRY_CATG_CN_NAME     AS LV4_CN_NAME
                      ';

  ELSEIF(F_GRANULARITY_TYPE = 'PROD') THEN
    -- 来源表的查询字段
    V_SQL_FROM_PBI_PART := ', LV0_PROD_LIST_CODE  AS LV0_CODE
                       , LV0_PROD_LIST_CN_NAME    AS LV0_CN_NAME
                       , LV1_PROD_LIST_CODE       AS LV1_CODE
                       , LV1_PROD_LIST_CN_NAME    AS LV1_CN_NAME
                       , LV2_PROD_LIST_CODE       AS LV2_CODE
                       , LV2_PROD_LIST_CN_NAME    AS LV2_CN_NAME
                       , LV3_PROD_LIST_CODE       AS LV3_CODE
                       , LV3_PROD_LIST_CN_NAME    AS LV3_CN_NAME
                       , LV4_PROD_LIST_CODE       AS LV4_CODE
                       , LV4_PROD_LIST_CN_NAME    AS LV4_CN_NAME
                      ';

  END IF;

  -- 从版本表取最大版本
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'MONTH'
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1
  ;

  -- 从月均价收敛补齐表或取默认基期，默认基期=T-1年的1月
  V_SQL := '
    DROP TABLE IF EXISTS VERSION_ID_TMP;
    CREATE TEMPORARY TABLE VERSION_ID_TMP AS
        SELECT (SUBSTR(MAX(PERIOD_ID),1,4)::INT - 1)||''01'' AS VERSION_ID
          FROM '||V_FROM_AMT_TABLE||'
         WHERE DEL_FLAG = ''N''
           AND VERSION_ID ='|| V_VERSION_ID
  ;

  EXECUTE V_SQL;

  SELECT VERSION_ID FROM VERSION_ID_TMP INTO  V_BASE_PERIOD_ID;

  -- 替换关系维表取最大版本的数据
  SELECT MAX(VERSION_ID) INTO V_MAX_REPL_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T  -- 新旧编码替换关系表（24年7月版只有销售目录树的）
   WHERE DEL_FLAG = 'N'
  ;

  -- 版本信息表需要更新 VERSION_TYPE
  UPDATE FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T SET VERSION_TYPE = 'FINAL', STATUS = 1
   WHERE DEL_FLAG = 'N'
    AND STATUS = 1
    AND UPPER(DATA_TYPE) = 'REPLACE_DIM'
    AND VERSION_ID = V_MAX_REPL_VERSION_ID
  ;

  V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '版本信息表 '||V_MAX_REPL_VERSION_ID||' 的版本类型(VERSION_TYPE)字段值已更新为 FINAL',--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  IF(F_CUSTOM_ID IS NOT NULL) THEN

    DBMS_OUTPUT.PUT_LINE('V_FROM_DIM_TABLE：'||V_FROM_DIM_TABLE);
    DBMS_OUTPUT.PUT_LINE('F_CUSTOM_ID：'||F_CUSTOM_ID);
    DBMS_OUTPUT.PUT_LINE('F_GRANULARITY_TYPE：'||F_GRANULARITY_TYPE);

    -- 根据传入组合ID查询虚化维表
    V_SQL := '
    DROP TABLE IF EXISTS BASE_CUS_DIM_TMP;
    CREATE TEMPORARY TABLE BASE_CUS_DIM_TMP AS
              SELECT CUSTOM_ID
                   , LV_CODE
                   , LV_CN_NAME
                   , SPART_CODE
                   , SPART_CN_NAME
                   , PAGE_TYPE
                   , GRANULARITY_TYPE
                   , REGION_CODE
                   , REGION_CN_NAME
                   , REPOFFICE_CODE
                   , REPOFFICE_CN_NAME
                   , BG_CODE
                   , BG_CN_NAME
                   , OVERSEA_FLAG
				   , SOFTWARE_MARK
                   , VIEW_FLAG
                   , STATUS_FLAG
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , RELATION_TYPE
                   , CODE_TYPE
                   , PARENT_LEVEL
                   , GROUP_LEVEL
                FROM '||V_FROM_DIM_TABLE||'
               WHERE DEL_FLAG = ''N''
                 AND UPPER(PAGE_TYPE) = ''MONTH''
                 AND UPPER(VIEW_FLAG) = ''PROD_SPART''
                 AND UPPER(GROUP_LEVEL) = ''SPART''
                 AND CUSTOM_ID = '||F_CUSTOM_ID
    ;

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '传入的组合ID： '||F_CUSTOM_ID||'，获取组合虚化维表的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
  );

  DBMS_OUTPUT.PUT_LINE('传入的组合ID： '||F_CUSTOM_ID||'，获取组合虚化维表的数据');

  -- 根据传入参数获取组合虚化维表的值
  SELECT SPART_CODE INTO V_SPART_CODE FROM BASE_CUS_DIM_TMP;
  SELECT PARENT_LEVEL INTO V_PARENT_LEVEL FROM BASE_CUS_DIM_TMP;

  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '根据组合ID获取的SPART编码： '||V_SPART_CODE||'，需要虚化的层级：'||V_PARENT_LEVEL,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
  );

  DBMS_OUTPUT.PUT_LINE('根据组合ID获取的SPART编码： '||V_SPART_CODE||'，需要虚化的层级：'||V_PARENT_LEVEL);

  -- 计算所有基础数据
  -- 从月均价收敛补齐表取数路径1（即TOP-SPART）的数据，均价用此表补齐的均价，成本金额、发货量不用补齐
  V_SQL := '
    DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP1;
    CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP1 AS
              SELECT VERSION_ID
                   , PERIOD_ID
                   , PERIOD_YEAR
                   '||V_SQL_FROM_PBI_PART||'
                   , REGION_CODE
                   , REGION_CN_NAME
                   , REPOFFICE_CODE
                   , REPOFFICE_CN_NAME
                   , SPART_CODE
                   , BG_CODE
                   , BG_CN_NAME
                   , OVERSEA_FLAG
				   , SOFTWARE_MARK
                   , VIEW_FLAG
                   , NVL(ACTUAL_QTY,0) AS PROD_QTY
                   '||V_GS_DECRYPT_COST_AMT||'
                FROM '||V_FROM_AMT_TABLE||'
               WHERE DEL_FLAG = ''N''
                 AND VIEW_FLAG = ''PROD_SPART''
                 AND VERSION_ID = '||V_VERSION_ID||'
                 AND SPART_CODE = '''||V_SPART_CODE||'''
  ';

  EXECUTE V_SQL;

  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '从月均价收敛补齐表 '||V_FROM_AMT_TABLE||' 取数路径1（即TOP-SPART）的数据入到临时表，最大版本：'||V_VERSION_ID||'，默认基期：'||V_BASE_PERIOD_ID||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
  );

  DBMS_OUTPUT.PUT_LINE('从月均价收敛补齐表 '||V_FROM_AMT_TABLE||' 取数路径1（即TOP-SPART）的数据入到临时表');


    -- 虚化到LV3层级
    IF(V_PARENT_LEVEL = 'LV3') THEN

    DBMS_OUTPUT.PUT_LINE('555555555');

    INSERT INTO MON_REPL_COST_INFO_TMP2(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , PERIOD_YEAR                     -- 会计年
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE                      -- 各层级编码
         , GROUP_CN_NAME                   -- 各层级中文名称
         , LV0_CODE                        -- LV0编码
         , LV0_CN_NAME                     -- LV0名称
         , LV1_CODE                        -- LV1编码
         , LV1_CN_NAME                     -- LV1名称
         , LV2_CODE                        -- LV2编码
         , LV2_CN_NAME                     -- LV2名称
         , LV3_CODE                        -- LV3编码
         , LV3_CN_NAME                     -- LV3名称
         , PARENT_CODE                     -- 父级编码
         , PARENT_CN_NAME                  -- 父级中文名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , GROUP_LEVEL                     -- 各层级（TOP-SPART/SPART/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
    )
    SELECT VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , LV3_CODE    AS PBI_DIM_CODE
         , LV3_CN_NAME AS PBI_DIM_CN_NAME
         , SPART_CODE  AS GROUP_CODE
         , '' AS GROUP_CN_NAME
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV2_CODE    AS PARENT_CODE
         , LV2_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , 'LV3' AS GROUP_LEVEL
         , SUM(PROD_QTY)     AS PROD_QTY
         , SUM(NVL(RMB_AVG_AMT,0)*PROD_QTY) AS RMB_COST_AMT
      FROM MON_REPL_COST_INFO_TMP1
     GROUP BY VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SPART_CODE
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
    ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '虚化到LV3层级的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 虚化到LV2层级
    ELSEIF(V_PARENT_LEVEL = 'LV2') THEN
      INSERT INTO MON_REPL_COST_INFO_TMP2(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , PERIOD_YEAR                     -- 会计年
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE                      -- 各层级编码
         , GROUP_CN_NAME                   -- 各层级中文名称
         , LV0_CODE                        -- LV0编码
         , LV0_CN_NAME                     -- LV0名称
         , LV1_CODE                        -- LV1编码
         , LV1_CN_NAME                     -- LV1名称
         , LV2_CODE                        -- LV2编码
         , LV2_CN_NAME                     -- LV2名称
         , LV3_CODE                        -- LV3编码
         , LV3_CN_NAME                     -- LV3名称
         , PARENT_CODE                     -- 父级编码
         , PARENT_CN_NAME                  -- 父级中文名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , GROUP_LEVEL                     -- 各层级（TOP-SPART/SPART/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
      )
    SELECT VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , LV2_CODE    AS PBI_DIM_CODE
         , LV2_CN_NAME AS PBI_DIM_CN_NAME
         , SPART_CODE  AS GROUP_CODE
         , '' AS GROUP_CN_NAME
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , '' AS LV3_CODE
         , '' AS LV3_CN_NAME
         , LV1_CODE    AS PARENT_CODE
         , LV1_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , 'LV2' AS GROUP_LEVEL
         , SUM(PROD_QTY)     AS PROD_QTY
         , SUM(NVL(RMB_AVG_AMT,0)*PROD_QTY) AS RMB_COST_AMT
      FROM MON_REPL_COST_INFO_TMP1
     GROUP BY VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , SPART_CODE
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
    ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '虚化到LV2层级的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 虚化到LV1层级
    ELSEIF(V_PARENT_LEVEL = 'LV1') THEN
    INSERT INTO MON_REPL_COST_INFO_TMP2(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , PERIOD_YEAR                     -- 会计年
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE                      -- 各层级编码
         , GROUP_CN_NAME                   -- 各层级中文名称
         , LV0_CODE                        -- LV0编码
         , LV0_CN_NAME                     -- LV0名称
         , LV1_CODE                        -- LV1编码
         , LV1_CN_NAME                     -- LV1名称
         , LV2_CODE                        -- LV2编码
         , LV2_CN_NAME                     -- LV2名称
         , LV3_CODE                        -- LV3编码
         , LV3_CN_NAME                     -- LV3名称
         , PARENT_CODE                     -- 父级编码
         , PARENT_CN_NAME                  -- 父级中文名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , GROUP_LEVEL                     -- 各层级（TOP-SPART/SPART/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
    )
    SELECT VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , LV1_CODE    AS PBI_DIM_CODE
         , LV1_CN_NAME AS PBI_DIM_CN_NAME
         , SPART_CODE  AS GROUP_CODE
         , '' AS GROUP_CN_NAME
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , '' AS LV2_CODE
         , '' AS LV2_CN_NAME
         , '' AS LV3_CODE
         , '' AS LV3_CN_NAME
         , LV0_CODE    AS PARENT_CODE
         , LV0_CN_NAME AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , 'LV1' AS GROUP_LEVEL
         , SUM(PROD_QTY)     AS PROD_QTY
         , SUM(NVL(RMB_AVG_AMT,0)*PROD_QTY) AS RMB_COST_AMT
      FROM MON_REPL_COST_INFO_TMP1
     GROUP BY VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , SPART_CODE
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
    ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '虚化到LV1层级的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 虚化到LV0层级
    ELSEIF(V_PARENT_LEVEL = 'LV0') THEN

    DBMS_OUTPUT.PUT_LINE('LV0：'||V_PARENT_LEVEL);

    INSERT INTO MON_REPL_COST_INFO_TMP2(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , PERIOD_YEAR                     -- 会计年
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE                      -- 各层级编码
         , GROUP_CN_NAME                   -- 各层级中文名称
         , LV0_CODE                        -- LV0编码
         , LV0_CN_NAME                     -- LV0名称
         , LV1_CODE                        -- LV1编码
         , LV1_CN_NAME                     -- LV1名称
         , LV2_CODE                        -- LV2编码
         , LV2_CN_NAME                     -- LV2名称
         , LV3_CODE                        -- LV3编码
         , LV3_CN_NAME                     -- LV3名称
         , PARENT_CODE                     -- 父级编码
         , PARENT_CN_NAME                  -- 父级中文名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , GROUP_LEVEL                     -- 各层级（TOP-SPART/SPART/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
    )
    SELECT VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , LV0_CODE    AS PBI_DIM_CODE
         , LV0_CN_NAME AS PBI_DIM_CN_NAME
         , SPART_CODE  AS GROUP_CODE
         , '' AS GROUP_CN_NAME
         , LV0_CODE
         , LV0_CN_NAME
         , '' AS LV1_CODE
         , '' AS LV1_CN_NAME
         , '' AS LV2_CODE
         , '' AS LV2_CN_NAME
         , '' AS LV3_CODE
         , '' AS LV3_CN_NAME
         , '' AS PARENT_CODE
         , '' AS PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , 'LV0' AS GROUP_LEVEL
         , SUM(PROD_QTY)     AS PROD_QTY
         , SUM(NVL(RMB_AVG_AMT,0)*PROD_QTY) AS RMB_COST_AMT
      FROM MON_REPL_COST_INFO_TMP1
     GROUP BY VERSION_ID
         , PERIOD_ID
         , PERIOD_YEAR
         , SPART_CODE
         , LV0_CODE
         , LV0_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
    ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '虚化到LV0层级的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 虚化到TOP-SPART层级
    ELSEIF(V_PARENT_LEVEL = 'SPART') THEN
    
    DBMS_OUTPUT.PUT_LINE('SPART：'||V_PARENT_LEVEL);
    
    INSERT INTO MON_REPL_COST_INFO_TMP2(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , PERIOD_YEAR                     -- 会计年
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE                      -- 各层级编码
         , GROUP_CN_NAME                   -- 各层级中文名称
         , LV0_CODE                        -- LV0编码
         , LV0_CN_NAME                     -- LV0名称
         , LV1_CODE                        -- LV1编码
         , LV1_CN_NAME                     -- LV1名称
         , LV2_CODE                        -- LV2编码
         , LV2_CN_NAME                     -- LV2名称
         , LV3_CODE                        -- LV3编码
         , LV3_CN_NAME                     -- LV3名称
         , PARENT_CODE                     -- 父级编码
         , PARENT_CN_NAME                  -- 父级中文名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , GROUP_LEVEL                     -- 各层级（TOP-SPART/SPART/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
    )
  SELECT VERSION_ID
       , PERIOD_ID
       , PERIOD_YEAR
       , SPART_CODE AS PBI_DIM_CODE
       , '' AS PBI_DIM_CN_NAME
       , '' AS LV0_CODE
       , '' AS LV0_CN_NAME
       , '' AS LV1_CODE
       , '' AS LV1_CN_NAME
       , '' AS LV2_CODE
       , '' AS LV2_CN_NAME
       , '' AS LV3_CODE
       , '' AS LV3_CN_NAME
       , LV4_CODE    AS PARENT_CODE
       , LV4_CN_NAME AS PARENT_CN_NAME
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , BG_CODE
       , BG_CN_NAME
       , OVERSEA_FLAG
	   , SOFTWARE_MARK
       , VIEW_FLAG
       , 'SPART' AS GROUP_LEVEL
       , SUM(PROD_QTY)     AS PROD_QTY
       , SUM(NVL(RMB_AVG_AMT,0)*PROD_QTY) AS RMB_COST_AMT
    FROM MON_REPL_COST_INFO_TMP1
   GROUP BY VERSION_ID
       , PERIOD_ID
       , PERIOD_YEAR
       , LV4_CODE
       , LV4_CN_NAME
       , REGION_CODE
       , REGION_CN_NAME
       , REPOFFICE_CODE
       , REPOFFICE_CN_NAME
       , SPART_CODE
       , BG_CODE
       , BG_CN_NAME
       , OVERSEA_FLAG
	   , SOFTWARE_MARK
       , VIEW_FLAG
  ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '虚化到SPART层级的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
  
  END IF;
  
   DBMS_OUTPUT.PUT_LINE('END IF');
  
  V_SQL := 'DELETE FROM '||V_TO_QTY_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' AND CUSTOM_ID = '||F_CUSTOM_ID;  -- 删除版本数据

  EXECUTE V_SQL;

  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '删除月度成本偏差 '||V_TO_QTY_TABLE||' 表 '||V_VERSION_ID||' 版本的数据',--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
  );
    
  DBMS_OUTPUT.PUT_LINE('DELETE'); 
  
  -- 数据入到成本偏差信息表
  V_SQL := 'INSERT INTO '||V_TO_QTY_TABLE||'(
                   VERSION_ID                      /*版本ID                                            */
                 , CUSTOM_ID                       /*组合ID                                            */
                 , CUSTOM_CN_NAME                  /*组合名称                                          */
                 , PERIOD_YEAR                     /*会计年                                            */
                 , PERIOD_ID                       /*会计月                                            */
                 , BASE_PERIOD_ID                  /*基期                                              */
                 , PBI_DIM_CODE                    /*PBI维度编码（包括LV0、LV1、LV2、LV3）             */
                 , PBI_DIM_CN_NAME                 /*PBI维度中文名称                                   */
                 , GROUP_CODE                      /*各层级编码                                        */
                 , GROUP_CN_NAME                   /*各层级中文名称                                    */
                 , GROUP_LEVEL                     /*各层级（TOP-SPART/SPART/LV3/LV2/LV1/LV0）         */
                 , GROUP_LEVEL_TYPE                /*层级类型（值包括：TOP-SPART、PBI等）              */
                 , PART_QTY                        /*数量                                              */
                 , RMB_COST_AMT                    /*标准成本                                          */
                 , REPLACE_RELATION_NAME           /*替换关系名称                                      */
                 , REPLACE_RELATION_TYPE           /*替换关系类型（一对一 、一对多 、多对多）          */
                 , RELATION_TYPE                   /*关系（替换、收编）                                */
                 , CODE_TYPE                       /*编码类型（NEW:新编码  OLD: 旧编码 ）              */
                 , PARENT_CODE                     /*父级编码                                          */
                 , PARENT_CN_NAME                  /*父级中文名称                                      */
                 , REGION_CODE                     /*地区部编码                                        */
                 , REGION_CN_NAME                  /*地区部中文名称                                    */
                 , REPOFFICE_CODE                  /*代表处编码                                        */
                 , REPOFFICE_CN_NAME               /*代表处中文名称                                    */
                 , BG_CODE                         /*BG编码                                            */
                 , BG_CN_NAME                      /*BG中文名称                                        */
                 , OVERSEA_FLAG                    /*国内海外标识                                      */
				 , SOFTWARE_MARK
                 , GRANULARITY_TYPE                /*重量级团队目录：IRB 产业目录：INDUS 销售目录：PROD*/
                 , CREATED_BY                      /*创建人                                            */
                 , CREATION_DATE                   /*创建时间                                          */
                 , LAST_UPDATED_BY                 /*修改人                                            */
                 , LAST_UPDATE_DATE                /*修改时间                                          */
                 , DEL_FLAG                        /*删除标识(未删除：N，已删除：Y)                    */
    )
    SELECT VERSION_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_ID
         , '||F_CUSTOM_ID||' AS CUSTOM_CN_NAME
         , PERIOD_YEAR
         , PERIOD_ID
         , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE
         , GROUP_CN_NAME
         , GROUP_LEVEL
         , ''TOP-SPART'' AS GROUP_LEVEL_TYPE
         , PROD_QTY AS PART_QTY
         , RMB_COST_AMT
         , '''' AS REPLACE_RELATION_NAME
         , '''' AS REPLACE_RELATION_TYPE
         , '''' AS RELATION_TYPE
         , '''' AS CODE_TYPE
         , PARENT_CODE
         , PARENT_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , '''||F_GRANULARITY_TYPE||''' AS GRANULARITY_TYPE
         , -1 AS CREATED_BY
 	       , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM MON_REPL_COST_INFO_TMP2
    ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '数据入到月累计成本偏差目标表 '||V_TO_QTY_TABLE||'，数据量：'||SQL%ROWCOUNT||'，运行结束！',--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );
  
  DBMS_OUTPUT.PUT_LINE('数据入到目标表');
  
  END IF;

  --收集统计信息
	V_SQL := 'ANALYSE '||V_TO_QTY_TABLE;
	EXECUTE V_SQL;

  EXCEPTION
  	WHEN OTHERS THEN

      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
         F_SP_NAME => V_SP_NAME,    -- SP名称
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => V_SP_NAME||'：运行错误',-- 日志描述
         F_FORMULA_SQL_TXT  => V_SQL,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_RESULT_STATUS => '0',
         F_ERRBUF => SQLSTATE  -- 错误编码
      ) ;

      X_RESULT_STATUS := 'FAILED';


END

$$
/

