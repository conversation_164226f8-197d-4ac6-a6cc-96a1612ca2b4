-- Name: f_dm_fom_rel_dim; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_rel_dim(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-12-11
  创建人  ：唐钦
  背景描述：年度涨跌幅状态码表
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_fom_rel_dim()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_REL_DIM'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_ONLY_FLAG VARCHAR(500);
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  -- 取刷新数据的版本号
  IF F_VERSION_ID IS NULL THEN
     SELECT VERSION_ID INTO V_VERSION_ID
         FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
         ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 删除年度分析中间表数据:
  DELETE FROM FIN_DM_OPT_FOI.DM_FOM_REL_DIM_D WHERE CALIBER_FLAG = F_CALIBER_FLAG;
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除FIN_DM_OPT_FOI.DM_FOM_REL_DIM_D表，数据口径为：'||F_CALIBER_FLAG||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  IF F_CALIBER_FLAG = 'E' THEN
     V_ONLY_FLAG := '';
  ELSIF F_CALIBER_FLAG = 'M' THEN
     V_ONLY_FLAG := ' AND T.ONLY_ITEM_FLAG = ''N''';
  END IF;
  -- 维表数据插入结果表
  V_SQL := '
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_REL_DIM_D
      (VERSION_ID,
       GROUP_LEVEL,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG, 
       CALIBER_FLAG)
  WITH DIS_DIM_TMP AS(
    SELECT DISTINCT T.LV0_CODE,
           T.LV0_CN_NAME,
           T.LV1_CODE,
           T.LV1_CN_NAME,
           T.BUSSINESS_OBJECT_CODE,
           T.BUSSINESS_OBJECT_CN_NAME,
           T.SHIPPING_OBJECT_CODE,
           T.SHIPPING_OBJECT_CN_NAME,
           T.MANUFACTURE_OBJECT_CODE,
           T.MANUFACTURE_OBJECT_CN_NAME,
           T.ITEM_CODE,
           T.ITEM_CN_NAME,
           T.CALIBER_FLAG
         FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T T
         WHERE T.CALIBER_FLAG = '''||F_CALIBER_FLAG||'''
         AND T.VERSION_ID = '||V_VERSION_ID||V_ONLY_FLAG||'
  )
  , LEV_DIM_TMP AS(
    SELECT ''ITEM'' AS GROUP_LEVEL,
           T.LV0_CODE,
           T.LV0_CN_NAME,
           T.LV1_CODE,
           T.LV1_CN_NAME,
           T.BUSSINESS_OBJECT_CODE,
           T.BUSSINESS_OBJECT_CN_NAME,
           T.SHIPPING_OBJECT_CODE,
           T.SHIPPING_OBJECT_CN_NAME,
           T.MANUFACTURE_OBJECT_CODE,
           T.MANUFACTURE_OBJECT_CN_NAME,
           T.ITEM_CODE,
           T.ITEM_CN_NAME
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''MANUFACTURE_OBJECT'' AS GROUP_LEVEL,
           T.LV0_CODE,
           T.LV0_CN_NAME,
           T.LV1_CODE,
           T.LV1_CN_NAME,
           T.BUSSINESS_OBJECT_CODE,
           T.BUSSINESS_OBJECT_CN_NAME,
           T.SHIPPING_OBJECT_CODE,
           T.SHIPPING_OBJECT_CN_NAME,
           T.MANUFACTURE_OBJECT_CODE,
           T.MANUFACTURE_OBJECT_CN_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME
        FROM DIS_DIM_TMP T
        WHERE T.MANUFACTURE_OBJECT_CODE IS NOT NULL
    UNION ALL
    SELECT DISTINCT ''SHIPPING_OBJECT'' AS GROUP_LEVEL,
           T.LV0_CODE,
           T.LV0_CN_NAME,
           T.LV1_CODE,
           T.LV1_CN_NAME,
           T.BUSSINESS_OBJECT_CODE,
           T.BUSSINESS_OBJECT_CN_NAME,
           T.SHIPPING_OBJECT_CODE,
           T.SHIPPING_OBJECT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME
        FROM DIS_DIM_TMP T
        WHERE T.SHIPPING_OBJECT_CODE IS NOT NULL
    UNION ALL
    SELECT DISTINCT ''BUSSINESS_OBJECT'' AS GROUP_LEVEL,
           T.LV0_CODE,
           T.LV0_CN_NAME,
           T.LV1_CODE,
           T.LV1_CN_NAME,
           T.BUSSINESS_OBJECT_CODE,
           T.BUSSINESS_OBJECT_CN_NAME,
           NULL AS SHIPPING_OBJECT_CODE,
           NULL AS SHIPPING_OBJECT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''LV1'' AS GROUP_LEVEL,
           T.LV0_CODE,
           T.LV0_CN_NAME,
           T.LV1_CODE,
           T.LV1_CN_NAME,
           NULL AS BUSSINESS_OBJECT_CODE,
           NULL AS BUSSINESS_OBJECT_CN_NAME,
           NULL AS SHIPPING_OBJECT_CODE,
           NULL AS SHIPPING_OBJECT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME
        FROM DIS_DIM_TMP T
    UNION ALL
    SELECT DISTINCT ''LV0'' AS GROUP_LEVEL,
           T.LV0_CODE,
           T.LV0_CN_NAME,
           NULL AS LV1_CODE,
           NULL AS LV1_CN_NAME,
           NULL AS BUSSINESS_OBJECT_CODE,
           NULL AS BUSSINESS_OBJECT_CN_NAME,
           NULL AS SHIPPING_OBJECT_CODE,
           NULL AS SHIPPING_OBJECT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS ITEM_CODE,
           NULL AS ITEM_CN_NAME
        FROM DIS_DIM_TMP T)
  SELECT '||V_VERSION_ID||' AS VERSION_ID,
         GROUP_LEVEL,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG, 
         '''||F_CALIBER_FLAG||''' AS CALIBER_FLAG
      FROM LEV_DIM_TMP';
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL; 

 --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，的全量数据到DM_FOM_REL_DIM_D表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');      
  DBMS_OUTPUT.PUT_LINE('所有维度数据全量保存');    
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOM_REL_DIM_D';

  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOM_REL_DIM_D统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

