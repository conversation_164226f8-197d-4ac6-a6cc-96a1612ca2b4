-- Name: f_dm_fol_air_spec_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_spec_info_t(p_version_id bigint DEFAULT NULL::bigint, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建人  ：zwx1275798
创建时间：2024年8月27日
背景描述：物流空运月度货量信息表，仅精品空运
参数描述： p_version_id   逻辑：1、自动调度，取航线量汇总表的最大版本ID；2、刷新（页面的刷新价格表）：取java传版本ID；
           p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表）：取java传的值（1_刷新价格表 ）
		   x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_spec_info_t()
变更记录：202503 zwx1275798 代码优化(代码由945行缩减至626行)：
                                         1、将所有with as 临时表修改为temporary会话临时表
                                         2、将所有temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
										 3、将最小粒度的货量和是否精品在最开始取数的时候计算，删除后面每个指标单独计算ALL的代码。
										 3、将相同逻辑的代码整合，精简代码，去除冗余逻辑
*/

declare
	v_sp_name varchar(500) := 'fin_dm_opt_foi.f_dm_fol_air_spec_info_t';
	v_tbl_name varchar(500) := 'fin_dm_opt_foi.dm_fol_air_spec_info_t';
	v_dml_row_count number default 0 ;
	v_max_version_id int;

begin
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => ''||v_tbl_name||'，开始运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

	   --从 物流空运月度货量信息表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_spec_info_t t1
              where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001');
	

         -- 如果是传version_id调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then
        select  p_version_id into v_max_version_id ;
        else
        select max(version_id) as max_version_id into v_max_version_id
		from fin_dm_opt_foi.dm_fol_air_route_info_sum_t;
        end if
        ;

		 -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 2 
		 and source_en_name = 'f_dm_fol_air_spec_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');

   
    --  将执行步骤：2  执行中 插入版本信息表中的
    insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id  as version_id
         , '' as version_code
         , 2 as step
         , 'f_dm_fol_air_spec_info_t' as source_en_name
         , '物流空运月度货量信息表' as source_cn_name
         ,  nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , '月度货量没用到价格补录表，故无版本code' as remark
         , -1 as created_by
         , current_timestamp as creation_date
         , -1 as last_updated_by
         , current_timestamp as last_update_date
         , 'N' as del_flag
    ;
	
	 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '【 dm_fol_air_version_info_t 将版本信息数据插入版本表中，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

		--从 空运航线量汇总表 取出 version_id 为最大版本, 仅筛选精品空运
		drop table if exists air_spec_route_tmp;
		create temporary table air_spec_route_tmp
		      as	
        -- 区分是否精品：精品、货代			  
		select  t1.version_id
	           ,t1.period_id
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,sum(t1.container_qty) as container_qty
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,t1.is_high_quality
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t  t1
		where 1=1
		  and t1.transport_mode = '精品空运'
		  and t1.version_id = v_max_version_id
		  and t1.container_qty is not null
		group by t1.version_id
		        ,t1.period_id
			    ,t1.transport_mode
			    ,t1.region_cn_name
				,t1.route
				,t1.source_port_name
                ,t1.dest_port_name
	            ,t1.dest_country_name
			    ,t1.Huawei_group    
                ,t1.service_level   
			    ,t1.is_high_quality
				union all
			-- 不区分是否精品：ALL=精品+货代
				select  t1.version_id
	           ,t1.period_id
			   ,t1.transport_mode
			   ,t1.region_cn_name
			   ,t1.route
			   ,sum(t1.container_qty) as container_qty
			   ,t1.source_port_name
               ,t1.dest_port_name
	           ,t1.dest_country_name
			   ,t1.Huawei_group    
               ,t1.service_level   
			   ,'ALL'  as is_high_quality
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t  t1
		where 1=1
		  and t1.transport_mode = '精品空运'
		  and t1.version_id = v_max_version_id
		  and t1.container_qty is not null
		group by t1.version_id
		        ,t1.period_id
			    ,t1.transport_mode
			    ,t1.region_cn_name
				,t1.route
				,t1.source_port_name
                ,t1.dest_port_name
	            ,t1.dest_country_name
			    ,t1.Huawei_group    
                ,t1.service_level   
			   ;

			 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 3,
            p_log_cal_log_desc => '【 air_spec_route_tmp 航线量临时表，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;


		 -- 删除 物流空运月度货量信息表 最大version_id 数据
		 delete from  fin_dm_opt_foi.dm_fol_air_spec_info_t where version_id = v_max_version_id;

		 -- 航线层级的月度货量
		 insert into fin_dm_opt_foi.dm_fol_air_spec_info_t (
		         version_id
                ,period_id
                ,transport_mode
                ,region_cn_name
				,route
                ,level_code
                ,level_desc
                ,container_qty
				,source_port_name     
                ,dest_port_name       
	            ,dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality
                ,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)
		    select version_id
				,period_id
				,transport_mode
				,region_cn_name
                ,route
                ,'03'   as level_code
                ,'航线' as level_desc
				,sum(container_qty) as container_qty
				,source_port_name     
                ,dest_port_name       
	            ,dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality
				, '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag
           from air_spec_route_tmp
           group by version_id
				,period_id
				,transport_mode
				,region_cn_name
                ,route
				,source_port_name     
                ,dest_port_name       
	            ,dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality
				;
				
				 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 4,
            p_log_cal_log_desc => '【 dm_fol_air_spec_info_t 航线层级的月度货量（区分是否精品），数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		 		 	
				 -- 区域层级各国家的月度货量
			drop table if exists air_country_qty_tmp;
		    create temporary table air_country_qty_tmp
		      as			 
		    select version_id
				,period_id
				,transport_mode
				,region_cn_name
                ,'02'   as level_code
                ,'区域' as level_desc
				,sum(container_qty) as container_qty
				,dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality				
           from air_spec_route_tmp
           group by version_id
				,period_id
				,transport_mode
				,region_cn_name
				,dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality
				;
				
				 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 6,
            p_log_cal_log_desc => '【 air_qty_tmp 区域层级各国家的月度货量（区分是否精品），数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		 		 				
				 -- 区域层级（不区分国家）的总月度货量
			drop table if exists air_region_tmp;
		    create temporary table air_region_tmp
		      as		
		    select version_id
				,period_id
				,transport_mode
				,region_cn_name
                ,'02'   as level_code
                ,'区域' as level_desc
				,sum(container_qty) as container_qty
				,'ALL' as dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality				
           from air_spec_route_tmp
           group by version_id
				,period_id
				,transport_mode
				,region_cn_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality
				;
				
				 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 8,
            p_log_cal_log_desc => '【 air_qty_tmp 区域层级（不区分国家）的总月度货量（区分是否精品），数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
				
				
				-- 运输方式层级的总月度货量
				drop table if exists air_transport_tmp;
		    create temporary table air_transport_tmp
		      as
		    select version_id
				,period_id
				,transport_mode
                ,'01'   as level_code
                ,'运输方式' as level_desc
				,sum(container_qty) as container_qty
				,Huawei_group    
                ,service_level   
			    ,is_high_quality				
           from air_spec_route_tmp
           group by version_id
				,period_id
				,transport_mode
				,Huawei_group    
                ,service_level   
			    ,is_high_quality
				;
				
				v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 10,
            p_log_cal_log_desc => '【 air_qty_tmp 运输方式层级的总月度货量（区分是否精品），数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
				
				
				-- 计算国家占区域的货量占比，往目标表插入国家占区域的货量占比和各国家的货量
				insert into fin_dm_opt_foi.dm_fol_air_spec_info_t (
		         version_id
                ,period_id
                ,transport_mode
                ,region_cn_name
                ,level_code
                ,level_desc
                ,container_qty
				,percent				
				,dest_country_name
				,Huawei_group    
                ,service_level   
			    ,is_high_quality 
				,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)				 
				select t1.version_id
				,t1.period_id
				,t1.transport_mode
				,t1.region_cn_name
                ,t1.level_code
                ,t1.level_desc
				,t1.container_qty
				,t1.container_qty/t2.container_qty as percent
				,t1.dest_country_name
				,t1.Huawei_group    
                ,t1.service_level   
			    ,t1.is_high_quality	
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag				
           from air_country_qty_tmp t1
		   left join air_region_tmp t2
		   on t1.version_id = t2.version_id
		   and t1.period_id = t2.period_id
		   and t1.transport_mode = t2.transport_mode
		   and t1.region_cn_name = t2.region_cn_name		   
		   and t1.level_code = t2.level_code
		   and t1.Huawei_group = t2.Huawei_group
		   and t1.service_level = t2.service_level
		   and t1.is_high_quality = t2.is_high_quality
		   ;
		   
		   v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 12,
            p_log_cal_log_desc => '【 dm_fol_air_spec_info_t 往目标表插入国家占区域的货量占比和各国家的货量，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		   
		   -- 计算区域占运输方式的货量占比，往目标表插入区域占运输方式的货量占比和各区域的货量
				insert into fin_dm_opt_foi.dm_fol_air_spec_info_t (
		         version_id
                ,period_id
                ,transport_mode
                ,region_cn_name
				,dest_country_name
                ,level_code
                ,level_desc
                ,container_qty
				,percent				
				,Huawei_group    
                ,service_level   
			    ,is_high_quality 
				,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)								
				select t1.version_id
				,t1.period_id
				,t1.transport_mode
				,t1.region_cn_name
				,t1.dest_country_name				
                ,t1.level_code
                ,t1.level_desc
				,t1.container_qty
				,t1.container_qty/t2.container_qty as percent
				,t1.Huawei_group    
                ,t1.service_level   
			    ,t1.is_high_quality	
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag				
           from air_region_tmp t1
		   left join air_transport_tmp t2
		   on t1.version_id = t2.version_id
		   and t1.period_id = t2.period_id
		   and t1.transport_mode = t2.transport_mode
		   and t1.Huawei_group = t2.Huawei_group
		   and t1.service_level = t2.service_level
		   and t1.is_high_quality = t2.is_high_quality		                
				;
				
				 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 13,
            p_log_cal_log_desc => '【 dm_fol_air_spec_info_t 往目标表插入区域占运输方式和各区域的货量，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		 
		 
				-- 往目标表插入运输方式层级的月度总货量
				insert into fin_dm_opt_foi.dm_fol_air_spec_info_t (
		         version_id
                ,period_id
                ,transport_mode
                ,level_code
                ,level_desc
                ,container_qty				
				,Huawei_group    
                ,service_level   
			    ,is_high_quality 
				,remark
                ,created_by
                ,creation_date
                ,last_updated_by
                ,last_update_date
                ,del_flag
				)								
				select version_id
				,period_id
				,transport_mode
                ,level_code
                ,level_desc
				,container_qty
				,Huawei_group    
                ,service_level   
			    ,is_high_quality	
                , '' as remark
  	            , -1 as created_by
  	            , current_timestamp as creation_date
  	            , -1 as last_updated_by
  	            , current_timestamp as last_update_date
  	            , 'N' as del_flag				
           from air_transport_tmp
		   ;
				
				v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 14,
            p_log_cal_log_desc => '【 dm_fol_air_spec_info_t 往目标表插入运输方式层级的月度总货量，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
				
				-- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 1 
		 and source_en_name = 'f_dm_fol_air_spec_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');
				
			 --将版本信息表中的执行步骤改为：1 成功
		update fin_dm_opt_foi.dm_fol_air_version_info_t 
		set step = 1 
		where source_en_name = 'f_dm_fol_air_spec_info_t' 
		  and version_id = v_max_version_id
		  and refresh_type = nvl(p_refresh_type,'4_AUTO');

         v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 15,
            p_log_cal_log_desc => '【 dm_fol_air_version_info_t  成功数据写入到版本信息表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
		 				
			  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_air_spec_info_t;
  analyse fin_dm_opt_foi.dm_fol_air_version_info_t;
   
			 exception
    when others then
       perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败
	
	 -- 失败数据写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )   
    select v_max_version_id   as version_id
         , '' as version_code
         , 2001 as step
         , 'f_dm_fol_air_spec_info_t' as source_en_name
         , '物流空运月度货量信息表' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , '月度货量不涉及价格，故无价格补录表的版本code' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  ;
end;
$$
/

