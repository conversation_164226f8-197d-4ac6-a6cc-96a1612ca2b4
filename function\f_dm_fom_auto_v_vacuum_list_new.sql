-- Name: f_dm_fom_auto_v_vacuum_list_new; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_auto_v_vacuum_list_new(OUT x_vacuum_list text, OUT x_vacuum_list_1 text, OUT x_vacuum_list_2 text, OUT x_vacuum_list_3 text, OUT x_vacuum_list_4 text, OUT x_result_status character varying, OUT x_errbuf character varying)
 RETURNS record
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$




DECLARE
/********************************************************************************
F_NAME：自动碎片清理及统计信息收集函数
F_DESCRIPTION：自动碎片清理及统计信息收集
F_PARA_DESC：   X_RESULT_STATUS     出参：执行结果1成功，2001失败
              X_ERRBUF            出参：执行错误信息
VERSION1.0      CREATE          h00797860         20231220
VERSION2.0      MODIFY          SWX1271676        20231221  增加获取指标配置，补充日志，格式调整
*********************************************************************************/
    V_SP_NAME                 VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_AUTO_V_VACUUM_LIST_NEW'; -- 存储过程名, 日志通用变量
    V_STEP_NUM                NUMBER        := 0;
    V_CAL_LOG_DESC            TEXT; -- 步骤描述, 日志变量
    V_SQL_TEXT                TEXT; -- 语法描述, 日志变量
    V_COUNT                   NUMBER;
    V_DIRTY_PERCENT_THRESHOLD INT; -- 脏页率阈值
    V_TABLE_SIZE_THRESHOLD    BIGINT; -- 表大小阈值
    V_UPDATA_ROWS             INT; -- 更新数据行数
    V_UPDATA_RATES            INT; -- 更新数据比率
/*  X_VACUUM_LIST_1 TEXT; -- 去掉空格
  X_VACUUM_LIST_2 TEXT; -- 去掉空格
  X_VACUUM_LIST_3 TEXT; -- 去掉空格
  X_VACUUM_LIST_4 TEXT; -- 去掉空格
  X_VACUUM_LIST_5 TEXT; -- 去掉空格
  X_VACUUM_LIST_6 TEXT; -- 去掉空格
  X_VACUUM_LIST_7 TEXT; -- 去掉空格*/
    elements                  TEXT[];
    num_chunks                INT;
    i                         INT           := 1;
    chunk_size                INT           := 50; -- 每个变量最多保存的元素个数

BEGIN
    -- 初始化写日志
    X_RESULT_STATUS := '1';
    V_STEP_NUM := V_STEP_NUM + 1;
    V_CAL_LOG_DESC := '开始执行';
    FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                       F_STEP_NUM => V_STEP_NUM ,
                                       F_CAL_LOG_DESC => V_CAL_LOG_DESC
    );

-- STEP1 获取指标配置信息
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := '获取指标配置信息: ';

        SELECT
            MAX( DECODE( INDICATOR_NAME , 'dirty_percent_threshold' , INDICATOR_VALUE ) ) AS DIRTY_PERCENT_THRESHOLD,
            MAX( DECODE( INDICATOR_NAME , 'table_size_threshold' , INDICATOR_VALUE ) )    AS TABLE_SIZE_THRESHOLD,
            MAX( DECODE( INDICATOR_NAME , 'updata_rows' , INDICATOR_VALUE ) )             AS UPDATA_ROWS,
            MAX( DECODE( INDICATOR_NAME , 'updata_rates' , INDICATOR_VALUE ) )            AS UPDATA_RATES
        INTO
            V_DIRTY_PERCENT_THRESHOLD,
            V_TABLE_SIZE_THRESHOLD,
            V_UPDATA_ROWS,
            V_UPDATA_RATES
        FROM
            FIN_DM_OPT_FOI.DM_FOM_INDICATOR_STANDARD_CONFIG_T;


        -- 写入日志
        V_CAL_LOG_DESC := V_CAL_LOG_DESC || CHR( 10 ) ||
                          'V_DIRTY_PERCENT_THRESHOLD:' || V_DIRTY_PERCENT_THRESHOLD || CHR( 10 ) ||
                          'V_TABLE_SIZE_THRESHOLD:' || V_TABLE_SIZE_THRESHOLD || CHR( 10 ) ||
                          'V_UPDATA_ROWS:' || V_UPDATA_ROWS || CHR( 10 ) ||
                          'V_UPDATA_RATES:' || V_UPDATA_RATES;

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );
    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SUBSTR( SQLERRM , 1 , 300 );
            FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                               F_STEP_NUM => V_STEP_NUM ,
                                               F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                               F_RESULT_STATUS => X_RESULT_STATUS ,
                                               F_ERRBUF => X_ERRBUF
            );
            RETURN;
    END;

-- STEP2 根据目标schema下的数据模型，自动更新信息表中的对象
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := '根据目标schema下的数据模型，自动更新信息表中的对象: ';

        -- 1.获取表清单写入临时表
        CREATE TEMP TABLE TAB_LIST AS
            SELECT
                T.TABLENAME
            FROM
                PG_TABLES T
            WHERE
                  T.SCHEMANAME = 'fin_dm_opt_foi'
              AND t.TABLENAME  LIKE '%dm_foc%'
              AND t.TABLENAME  LIKE '%dm_fcst%'
              AND t.TABLENAME  LIKE '%dwl_prod%'
              AND t.TABLENAME NOT LIKE '%标准%';
        --2024年7月10日 排除中文表名

        -- 2.根据清单临时表更新信息表
        MERGE INTO FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO-- 自动清理信息表
        USING TAB_LIST A
        ON INFO.OBJ_EN_NAME = A.TABLENAME
        WHEN NOT MATCHED THEN
            INSERT
            (
                OBJ_EN_NAME , CREATED_BY , CREATION_DATE , LAST_UPDATED_BY , LAST_UPDATE_DATE
            )
            VALUES (
                       A.TABLENAME , - 1 , sysdate , - 1 , sysdate
                   );

        DELETE
        FROM
            FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        WHERE
            NOT EXISTS (
                       SELECT
                           1
                       FROM
                           TAB_LIST T
                       WHERE
                           INFO.OBJ_EN_NAME = T.TABLENAME
                       );

        -- 3.阈值指标初始化，更新自动清理信息表所有空指标为false
        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        SET INFO.DIRTY_PERCENT_FLAG = FALSE
        WHERE
            INFO.DIRTY_PERCENT_FLAG IS NULL;

        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        SET INFO.TABLE_SIZE_FLAG = FALSE
        WHERE
            INFO.TABLE_SIZE_FLAG IS NULL;

        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        SET INFO.ANALYZE_FLAG = FALSE
        WHERE
            INFO.ANALYZE_FLAG IS NULL;

        -- 写入日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );
    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SUBSTR( SQLERRM , 1 , 300 );
            FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                               F_STEP_NUM => V_STEP_NUM ,
                                               F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                               F_RESULT_STATUS => X_RESULT_STATUS ,
                                               F_ERRBUF => X_ERRBUF
            );
            RETURN;
    END;

-- STEP3 更新信息表脏页率
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := '更新信息表脏页率: ';

        -- 获取脏页率信息写入临时表
        CREATE TEMP TABLE DIRTY_T AS
            WITH TEMP AS (
                         SELECT OBJ_EN_NAME
                         FROM FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T
                         )
            SELECT
                RELNAME
            FROM
                PGXC_GET_STAT_DIRTY_TABLES( V_DIRTY_PERCENT_THRESHOLD , 1 ) T -- 脏页率及脏数据行数
                    JOIN TEMP
                         ON TEMP.OBJ_EN_NAME = T.RELNAME
            WHERE
                        SCHEMANAME = 'fin_dm_opt_foi'
                    AND RELNAME LIKE '%foc%'
              OR        RELNAME LIKE 'dwl%';

        -- 更新自动清理信息表脏页率指标
        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        SET DIRTY_PERCENT_FLAG = TRUE
        FROM
            (
            SELECT RELNAME
            FROM DIRTY_T
            ) TEMP1
        WHERE
            INFO.OBJ_EN_NAME = TEMP1.RELNAME;
        V_COUNT := SQL % ROWCOUNT;

        -- 写入日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_DML_ROW_COUNT => V_COUNT ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );
    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SUBSTR( SQLERRM , 1 , 300 );
            FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                               F_STEP_NUM => V_STEP_NUM ,
                                               F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                               F_RESULT_STATUS => X_RESULT_STATUS ,
                                               F_ERRBUF => X_ERRBUF
            );
            RETURN;
    END;

-- STEP4 更新信息表表大小指标
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := '更新信息表表大小指标: ';

        -- 获取表占空间大小写入临时表
        CREATE TEMP TABLE SIZE_T AS
            WITH TEMP AS (
                         SELECT
                             OBJ_EN_NAME,
                             CASE
                                 WHEN SUBSTR( PG_SIZE_PRETTY( PG_TOTAL_RELATION_SIZE( INFO.OBJ_EN_NAME ) ) , -2 , 2 ) =
                                      'GB' AND
                                      TO_NUMBER( SUBSTR( PG_SIZE_PRETTY( PG_TOTAL_RELATION_SIZE( INFO.OBJ_EN_NAME ) ) ,
                                                         0 ,
                                                         LENGTH( PG_SIZE_PRETTY( PG_TOTAL_RELATION_SIZE( INFO.OBJ_EN_NAME ) ) ) -
                                                         2 ) ) > 1
                                     THEN 'T'
                                 WHEN SUBSTR( PG_SIZE_PRETTY( PG_TOTAL_RELATION_SIZE( INFO.OBJ_EN_NAME ) ) , -2 , 2 ) =
                                      'MB' AND
                                      TO_NUMBER( SUBSTR( PG_SIZE_PRETTY( PG_TOTAL_RELATION_SIZE( INFO.OBJ_EN_NAME ) ) ,
                                                         0 ,
                                                         LENGTH( PG_SIZE_PRETTY( PG_TOTAL_RELATION_SIZE( INFO.OBJ_EN_NAME ) ) ) -
                                                         2 ) ) > 1024
                                     THEN 'T'
                                 ELSE 'F'
                             END AS SIZE_FLAG
                         FROM
                             FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
                         )
            SELECT
                OBJ_EN_NAME,
                SIZE_FLAG
            FROM
                TEMP;

        -- 更新自动清理信息表表大小指标
        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        SET INFO.TABLE_SIZE_FLAG = TRUE
        FROM
            (
            SELECT
                OBJ_EN_NAME,
                SIZE_FLAG
            FROM
                SIZE_T
            WHERE
                SIZE_FLAG = 'T'
            ) TEMP1
        WHERE
            INFO.OBJ_EN_NAME = TEMP1.OBJ_EN_NAME;
        V_COUNT := SQL % ROWCOUNT;
        -- 写入日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_DML_ROW_COUNT => V_COUNT ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );
    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SUBSTR( SQLERRM , 1 , 300 );
            FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                               F_STEP_NUM => V_STEP_NUM ,
                                               F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                               F_RESULT_STATUS => X_RESULT_STATUS ,
                                               F_ERRBUF => X_ERRBUF
            );
            RETURN;
    END;

-- STEP5 更新信息表行数及比例指标
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := '更新信息表行数及比例指标: ';

        -- 1.获取表更新行数及比例写入临时表
        CREATE TEMP TABLE ANALYZE_T AS (
                                       SELECT
                                           N.NSPNAME,
                                           C.RELNAME,
                                           'stale'                                                                    AS NEED_ANALYZ,
                                           PG_STAT_GET_LAST_ANALYZE_TIME( C.OID )                                     AS LAST_ANALYZE_TIME,
                                           ROUND( PG_STAT_GET_LOCAL_TUPLES_CHANGED( C.OID ) * 100 / C.RELTUPLES ,
                                                  0 )                                                                 AS CHANGE_RATE,
                                           PG_STAT_GET_LOCAL_TUPLES_CHANGED( C.OID )                                  AS CHANGE_TUPELS,
                                           C.RELTUPLES,
                                           PG_STAT_GET_LOCAL_TUPLES_CHANGED( C.OID )                                  AS TUPLES_CHANGED
                                       FROM
                                           PG_CLASS C,
                                           PG_NAMESPACE N,
                                           FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
                                       WHERE
                                             C.RELNAMESPACE = N.OID
                                         AND C.RELKIND = 'r'
                                         AND C.RELTUPLES > 0
                                         AND C.RELNAME NOT LIKE '%err'
                                         AND C.RELNAME = INFO.OBJ_EN_NAME
                                         AND ( CHANGE_TUPELS > V_UPDATA_ROWS OR CHANGE_RATE > V_UPDATA_RATES )
                                         AND N.NSPNAME = 'fin_dm_opt_foi'
                                         AND N.NSPNAME NOT LIKE 'pg_temp%'
                                         AND C.OID >= 16384
                                       );

        -- 2.更新信息表行数及比例指标
        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
        SET ANALYZE_FLAG = TRUE
        FROM
            ANALYZE_T T
        WHERE
            INFO.OBJ_EN_NAME = T.RELNAME;
        V_COUNT := SQL % ROWCOUNT;

        -- 写入日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_DML_ROW_COUNT => V_COUNT ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );
    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SUBSTR( SQLERRM , 1 , 300 );
            FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                               F_STEP_NUM => V_STEP_NUM ,
                                               F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                               F_RESULT_STATUS => X_RESULT_STATUS ,
                                               F_ERRBUF => X_ERRBUF
            );
            RETURN;
    END;

-- STEP6 执行脏页清理
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := '执行脏页清理 ';

        -- 1.更新时间
        UPDATE FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T
        SET LAST_VACUUM_DATE = NOW( )
        WHERE
            ( ( TABLE_SIZE_FLAG = TRUE AND DIRTY_PERCENT_FLAG = TRUE ) OR ANALYZE_FLAG = TRUE );

        -- 2.获取待执行SQL列表
        SELECT
            STRING_AGG( EXECUTE_SQL_LIST , CHR( 10 ) )
        INTO X_VACUUM_LIST
        FROM
            (
            SELECT
                CASE
                    WHEN TABLE_SIZE_FLAG = TRUE AND DIRTY_PERCENT_FLAG = TRUE AND ANALYZE_FLAG = TRUE THEN
                        'VACUUM FULL ANALYZE ' || OBJ_EN_NAME || ';'
                    WHEN TABLE_SIZE_FLAG = TRUE AND DIRTY_PERCENT_FLAG = TRUE AND ANALYZE_FLAG = FALSE THEN
                        'VACUUM FULL ' || OBJ_EN_NAME || ';' || CHR( 10 )
                    WHEN ANALYZE_FLAG = TRUE AND DIRTY_PERCENT_FLAG = FALSE THEN
                        'ANALYZE ' || OBJ_EN_NAME || ';' || CHR( 10 )
                END AS EXECUTE_SQL_LIST
            FROM
                FIN_DM_OPT_FOI.DM_FOM_AUTO_VACUUM_INFO_T INFO
            ) B;
        -- 3.回滚临时表
        DROP TABLE TAB_LIST;
        DROP TABLE DIRTY_T;
        DROP TABLE SIZE_T;
        DROP TABLE ANALYZE_T;

        --DBMS_OUTPUT.PUT_LINE( X_VACUUM_LIST );
        --DBMS_OUTPUT.PUT_LINE( '----111111111---------------' );
        -- 将 X_VACUUM_LIST 按分号分割成数组
        elements := STRING_TO_ARRAY( X_VACUUM_LIST , ';' );
       -- DBMS_OUTPUT.PUT_LINE( X_VACUUM_LIST );

        -- 计算需要分成多少个变量
        num_chunks := CEIL( ARRAY_LENGTH( elements , 1 ) / chunk_size );
       -- DBMS_OUTPUT.PUT_LINE( num_chunks );

        -- 循环处理数组，每 chunk_size 个元素存入一个变量中
        WHILE i <= num_chunks
            LOOP
            -- 构造变量名
            --EXECUTE format('DECLARE var_%s TEXT[] := ARRAY[%L];', i, array_to_string(elements[(i-1)*chunk_size+1 : i*chunk_size], ', '));
                CASE
                    WHEN i = 1 THEN x_vacuum_list_1 :=
                            ARRAY_TO_STRING( elements[( i - 1 ) * chunk_size + 1 : i * chunk_size] , '; ' );
                    WHEN i = 2 THEN x_vacuum_list_2 :=
                            ARRAY_TO_STRING( elements[( i - 1 ) * chunk_size + 1 : i * chunk_size] , '; ' );
                    WHEN i = 3 THEN x_vacuum_list_3 :=
                            ARRAY_TO_STRING( elements[( i - 1 ) * chunk_size + 1 : i * chunk_size] , '; ' );
                    WHEN i = 4 THEN x_vacuum_list_4 :=
                            ARRAY_TO_STRING( elements[( i - 1 ) * chunk_size + 1 : i * chunk_size] , '; ' );
					ELSE 
					 NULL;
                END CASE;

                i := i + 1;
				--DBMS_OUTPUT.PUT_LINE( 'i继续循环' );
            END LOOP;
--DBMS_OUTPUT.PUT_LINE( '循环结束' );
        IF LENGTH( x_vacuum_list_1 ) != 0 THEN
            x_vacuum_list_1 := STRING_AGG( x_vacuum_list_1 || ';' , E'\n' );
        ELSE
            x_vacuum_list_1 := x_vacuum_list_1 || ' VACUUM  FULL fin_dm_opt_foi.auto_vacuum_t;';
        END IF;
        IF LENGTH( x_vacuum_list_2 ) != 0 THEN
            x_vacuum_list_2 := STRING_AGG( x_vacuum_list_2 || ';' , E'\n' );
        ELSE
            x_vacuum_list_2 := x_vacuum_list_2 || ' VACUUM  FULL fin_dm_opt_foi.auto_vacuum_t;';
        END IF;
        IF LENGTH( x_vacuum_list_3 ) != 0 THEN
            x_vacuum_list_3 := STRING_AGG( x_vacuum_list_3 || ';' , E'\n' );
        ELSE
            x_vacuum_list_3 := x_vacuum_list_3 || ' VACUUM  FULL fin_dm_opt_foi.auto_vacuum_t;';
        END IF;
        IF LENGTH( x_vacuum_list_4 ) != 0 THEN
            x_vacuum_list_4 := STRING_AGG( x_vacuum_list_4 || ';' , E'\n' );
        ELSE
            x_vacuum_list_4 := x_vacuum_list_4 || ' VACUUM  FULL fin_dm_opt_foi.auto_vacuum_t;';
        END IF;

              -- DBMS_OUTPUT.PUT_LINE('----22222222222222---------------');
--         DBMS_OUTPUT.PUT_LINE( '--list1信息-------------' );
--         DBMS_OUTPUT.PUT_LINE( x_vacuum_list_1 );
--             DBMS_OUTPUT.PUT_LINE( '--list2信息-------------' );
--         DBMS_OUTPUT.PUT_LINE( x_vacuum_list_2 );
--             DBMS_OUTPUT.PUT_LINE( '--list3信息-------------' );
--         DBMS_OUTPUT.PUT_LINE( x_vacuum_list_3 );
--             DBMS_OUTPUT.PUT_LINE( '--list4信息-------------' );
--         DBMS_OUTPUT.PUT_LINE( x_vacuum_list_4 );
 --DBMS_OUTPUT.PUT_LINE( '--end-------------' );
        -- 写入日志
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_FORMULA_SQL_TXT => X_VACUUM_LIST ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );
    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SUBSTR( SQLERRM , 1 , 300 );
            FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                               F_STEP_NUM => V_STEP_NUM ,
                                               F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                               F_RESULT_STATUS => X_RESULT_STATUS ,
                                               F_ERRBUF => X_ERRBUF
            );
            RETURN;
    END;

    -- 结束日志
    BEGIN
        V_STEP_NUM := V_STEP_NUM + 1;
        V_CAL_LOG_DESC := V_SP_NAME || '运行结束';
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS );

    EXCEPTION
        WHEN OTHERS THEN
            X_RESULT_STATUS := '2001';
            X_ERRBUF := SQLSTATE || ':' || SQLERRM;

            -- 写入日志
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                       F_STEP_NUM => V_STEP_NUM ,
                                                       F_CAL_LOG_DESC => V_CAL_LOG_DESC ,
                                                       F_RESULT_STATUS => X_RESULT_STATUS ,
                                                       F_ERRBUF => X_ERRBUF );
    END;

    RETURN;

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := '2001';
        X_ERRBUF := SQLSTATE || ':' || SQLERRM;

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T( F_SP_NAME => V_SP_NAME ,
                                                   F_STEP_NUM => V_STEP_NUM ,
                                                   F_CAL_LOG_DESC => V_SP_NAME || '运行失败' ,
                                                   F_RESULT_STATUS => X_RESULT_STATUS ,
                                                   F_ERRBUF => X_ERRBUF );


END



$$
/

