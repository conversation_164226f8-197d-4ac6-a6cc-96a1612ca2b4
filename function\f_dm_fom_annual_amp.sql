-- Name: f_dm_fom_annual_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_annual_amp(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-12-11
  创建人  ：唐钦
  背景描述：分视角年度涨跌幅表(年度分析-柱状图)
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOM_ANNUAL_AMP()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_ANNUAL_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR  BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_KEYSTR  varchar(50) := F_KEYSTR;  -- 密钥入参
  V_IN_AMT varchar(200);
  V_SQL TEXT;

BEGIN
  X_RESULT_STATUS = '1';
  -- 取刷新数据的版本号
  IF F_VERSION_ID IS NULL THEN
     SELECT VERSION_ID INTO V_VERSION_ID
         FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 
         ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;
  
  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  -- 删除年度分析中间表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T';
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T表，数据口径为：'||F_CALIBER_FLAG||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 如果是自制数据的话，需要建临时表对数据进行解密
   DROP TABLE IF EXISTS AVG_TMP;
   CREATE TEMPORARY TABLE AVG_TMP(
      VERSION_ID    bigint,
      PERIOD_YEAR  bigint,
      LV0_CODE    varchar(50),
      LV0_CN_NAME    varchar(200),
      LV1_CODE    varchar(50),
      LV1_CN_NAME    varchar(200),
      BUSSINESS_OBJECT_CODE    varchar(200),
      BUSSINESS_OBJECT_CN_NAME    varchar(200),
      SHIPPING_OBJECT_CODE     varchar(200),
      SHIPPING_OBJECT_CN_NAME    varchar(200),
      MANUFACTURE_OBJECT_CODE     varchar(200),
      MANUFACTURE_OBJECT_CN_NAME    varchar(200),
      GROUP_CODE    varchar(200),
      GROUP_CN_NAME    varchar(1000),
      GROUP_LEVEL    varchar(50),
      CALIBER_FLAG  varchar(2),
      RMB_AVG_AMT NUMERIC
   )
   ON COMMIT PRESERVE ROWS
   DISTRIBUTE BY HASH(GROUP_CODE);

  IF F_CALIBER_FLAG = 'M' THEN 
     V_IN_AMT := 'CAST(GS_DECRYPT(RMB_MADE_AVG, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS NUMERIC)';
  ELSIF F_CALIBER_FLAG = 'E' THEN 
        V_IN_AMT := 'RMB_EMS_AVG';
  END IF;
   
  V_SQL := '
  -- 解密数据，并插入临时表
  INSERT INTO AVG_TMP(
      VERSION_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      CALIBER_FLAG,
      RMB_AVG_AMT)
   SELECT VERSION_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      ITEM_CODE AS GROUP_CODE,
      ITEM_CN_NAME AS GROUP_CN_NAME,
      ''ITEM'' AS GROUP_LEVEL,
      CALIBER_FLAG,
      '||V_IN_AMT||' AS RMB_AVG_AMT  -- 解密
    FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T
    WHERE VERSION_ID = '||V_VERSION_ID||'
    AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';   
 
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>  V_STEP_NUM,
   F_CAL_LOG_DESC => '数据口径为：'||F_CALIBER_FLAG||'的均本数据，已插入临时表',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  

  -- 将ITEM层级的年度涨跌幅数据插入中间表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T(
             VERSION_ID,
             PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ANNUAL_AMP,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG
            )
  -- ITEM层级年度涨跌幅的计算逻辑
  -- 将年均本数据按年份行转列 
  WITH BY_YEAR_AVG_TMP AS(
        SELECT LV0_CODE,
               LV1_CODE,
               BUSSINESS_OBJECT_CODE,
               SHIPPING_OBJECT_CODE,
               MANUFACTURE_OBJECT_CODE,
               GROUP_CODE,
               GROUP_LEVEL,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG
           FROM AVG_TMP
           WHERE (SHIPPING_OBJECT_CODE IS NOT NULL AND MANUFACTURE_OBJECT_CODE IS NOT NULL)
           GROUP BY LV0_CODE,
                    LV1_CODE,
                    BUSSINESS_OBJECT_CODE,
                    SHIPPING_OBJECT_CODE,
                    MANUFACTURE_OBJECT_CODE,
                    GROUP_CODE,
                    GROUP_LEVEL
        UNION ALL  -- 特殊情况处理（SHIPPING_OBJECT_CODE/MANUFACTURE_OBJECT_CODE置空）
        SELECT LV0_CODE,
               LV1_CODE,
               BUSSINESS_OBJECT_CODE,
               NULL AS SHIPPING_OBJECT_CODE,
               NULL AS MANUFACTURE_OBJECT_CODE,
               GROUP_CODE,
               GROUP_LEVEL,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = V_YEAR THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG
           FROM AVG_TMP
           WHERE (SHIPPING_OBJECT_CODE IS NULL AND MANUFACTURE_OBJECT_CODE IS NULL)
           GROUP BY LV0_CODE,
                    LV1_CODE,
                    BUSSINESS_OBJECT_CODE,
                    GROUP_CODE,
                    GROUP_LEVEL
        )
  , YEAR_ANNUAL_AMP_TMP AS(
      SELECT V_YEAR-2 AS PERIOD_YEAR,
              LV0_CODE,
              LV1_CODE,
              BUSSINESS_OBJECT_CODE,
              SHIPPING_OBJECT_CODE,
              MANUFACTURE_OBJECT_CODE,    
              GROUP_CODE,
              GROUP_LEVEL,
              ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP
          FROM BY_YEAR_AVG_TMP
      UNION ALL
      SELECT V_YEAR-1 AS PERIOD_YEAR,
             LV0_CODE,
             LV1_CODE,
             BUSSINESS_OBJECT_CODE,
             SHIPPING_OBJECT_CODE,
             MANUFACTURE_OBJECT_CODE,   
             GROUP_CODE,
             GROUP_LEVEL,
             ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP
         FROM BY_YEAR_AVG_TMP   
      UNION ALL
      SELECT V_YEAR AS PERIOD_YEAR,
             LV0_CODE,
             LV1_CODE,
             BUSSINESS_OBJECT_CODE,
             SHIPPING_OBJECT_CODE,
             MANUFACTURE_OBJECT_CODE,      
             GROUP_CODE,
             GROUP_LEVEL,
             ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP
         FROM BY_YEAR_AVG_TMP 
              )
  -- 计算ITEM层级的年度涨跌幅数据
       SELECT V_VERSION_ID AS VERSION_ID,
              T1.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
              T1.SHIPPING_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CN_NAME,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CN_NAME,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              NVL(T2.ANNUAL_AMP,0) AS ANNUAL_AMP,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              'N' AS DEL_FLAG
           FROM YEAR_ANNUAL_AMP_TMP T2
           LEFT JOIN AVG_TMP T1
           ON  T1.PERIOD_YEAR = T2.PERIOD_YEAR
           AND NVL(T1.LV1_CODE,'SNULL0') = NVL(T2.LV1_CODE,'SNULL0') 
           AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL1') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL1') 
           AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL2') = NVL(T2.SHIPPING_OBJECT_CODE,'SNULL2') 
           AND NVL(T1.MANUFACTURE_OBJECT_CODE,'SNULL3') = NVL(T2.MANUFACTURE_OBJECT_CODE,'SNULL3') 
           AND T1.GROUP_CODE =T2.GROUP_CODE
           AND T1.LV0_CODE = T2.LV0_CODE
           AND T1.GROUP_LEVEL = T2.GROUP_LEVEL;
       DBMS_OUTPUT.PUT_LINE('ITEM层级涨跌幅数据插入表成功');
                                        
   -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||'的ITEM层级的数据到DM_FOM_ANNL_MID_AMP_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  -- 年度涨跌幅各层级逻辑
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T(
             VERSION_ID,
             PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ANNUAL_AMP,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG
            )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                T1.SHIPPING_OBJECT_CODE,
                T1.SHIPPING_OBJECT_CN_NAME,
                T1.MANUFACTURE_OBJECT_CODE,
                T1.MANUFACTURE_OBJECT_CN_NAME,
                T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT
             FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T T1
             INNER JOIN FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.GROUP_CODE =T2.GROUP_CODE
             AND T1.LV0_CODE = T2.LV0_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             AND NVL(T1.LV1_CODE,'SNULL0') = NVL(T2.LV1_CODE,'SNULL0')
             AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL1') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL1')
             AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL2') = NVL(T2.SHIPPING_OBJECT_CODE,'SNULL2')
             AND NVL(T1.MANUFACTURE_OBJECT_CODE,'SNULL3') = NVL(T2.MANUFACTURE_OBJECT_CODE,'SNULL3')
             WHERE T1.VERSION_ID = V_VERSION_ID
             AND T2.CALIBER_FLAG = F_CALIBER_FLAG
             AND T1.GROUP_LEVEL = 'ITEM'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT V_VERSION_ID AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                T1.SHIPPING_OBJECT_CODE,
                T1.SHIPPING_OBJECT_CN_NAME,
                T1.MANUFACTURE_OBJECT_CODE,
                T1.MANUFACTURE_OBJECT_CN_NAME,
                T1.MANUFACTURE_OBJECT_CODE AS GROUP_CODE,
                T1.MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME,
                'MANUFACTURE_OBJECT' AS GROUP_LEVEL,
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                'N' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             WHERE T1.MANUFACTURE_OBJECT_CODE IS NOT NULL
             AND T1.SHIPPING_OBJECT_CODE IS NOT NULL
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_CODE,
                      T1.LV0_CN_NAME,
                      T1.LV1_CODE,
                      T1.LV1_CN_NAME,
                      T1.BUSSINESS_OBJECT_CODE,
                      T1.BUSSINESS_OBJECT_CN_NAME,
                      T1.SHIPPING_OBJECT_CODE,
                      T1.SHIPPING_OBJECT_CN_NAME,
                      T1.MANUFACTURE_OBJECT_CODE,
                      T1.MANUFACTURE_OBJECT_CN_NAME
         UNION ALL
         SELECT V_VERSION_ID AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                NULL AS SHIPPING_OBJECT_CODE,
                NULL AS SHIPPING_OBJECT_CN_NAME,
                NULL AS MANUFACTURE_OBJECT_CODE,
                NULL AS MANUFACTURE_OBJECT_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE AS GROUP_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
                'BUSSINESS_OBJECT' AS GROUP_LEVEL,
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                'N' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             WHERE T1.MANUFACTURE_OBJECT_CODE IS NULL
             AND T1.SHIPPING_OBJECT_CODE IS NULL
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_CODE,
                      T1.LV0_CN_NAME,
                      T1.LV1_CODE,
                      T1.LV1_CN_NAME,
                      T1.BUSSINESS_OBJECT_CODE,
                      T1.BUSSINESS_OBJECT_CN_NAME;
       DBMS_OUTPUT.PUT_LINE('MANUFACTURE_OBJECT层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||'，MANUFACTURE_OBJECT层级涨跌幅数据插入：DM_FOM_ANNL_MID_AMP_T表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T(
             VERSION_ID,
             PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ANNUAL_AMP,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG
            )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                T1.SHIPPING_OBJECT_CODE,
                T1.SHIPPING_OBJECT_CN_NAME,
                T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT
             FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T T1
             INNER JOIN FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.GROUP_CODE =T2.GROUP_CODE
             AND T1.LV0_CODE = T2.LV0_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             AND NVL(T1.LV1_CODE,'SNULL0') = NVL(T2.LV1_CODE,'SNULL0')
             AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL1') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL1')
             AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL2') = NVL(T2.SHIPPING_OBJECT_CODE,'SNULL2')
             WHERE T1.VERSION_ID = V_VERSION_ID
             AND T2.CALIBER_FLAG = F_CALIBER_FLAG
             AND T1.GROUP_LEVEL = 'MANUFACTURE_OBJECT'
             AND T1.MANUFACTURE_OBJECT_CODE IS NOT NULL
             AND T1.SHIPPING_OBJECT_CODE IS NOT NULL
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT V_VERSION_ID AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                T1.SHIPPING_OBJECT_CODE,
                T1.SHIPPING_OBJECT_CN_NAME,
                T1.SHIPPING_OBJECT_CODE AS GROUP_CODE,
                T1.SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
                'SHIPPING_OBJECT' AS GROUP_LEVEL,
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                'N' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_CODE,
                      T1.LV0_CN_NAME,
                      T1.LV1_CODE,
                      T1.LV1_CN_NAME,
                      T1.BUSSINESS_OBJECT_CODE,
                      T1.BUSSINESS_OBJECT_CN_NAME,
                      T1.SHIPPING_OBJECT_CODE,
                      T1.SHIPPING_OBJECT_CN_NAME;
       DBMS_OUTPUT.PUT_LINE('SHIPPING_OBJECT层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||'，SHIPPING_OBJECT层级涨跌幅数据插入：DM_FOM_ANNL_MID_AMP_T表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T(
             VERSION_ID,
             PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ANNUAL_AMP,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG
            )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT
             FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T T1
             INNER JOIN FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.GROUP_CODE =T2.GROUP_CODE
             AND T1.LV0_CODE = T2.LV0_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             AND NVL(T1.LV1_CODE,'SNULL0') = NVL(T2.LV1_CODE,'SNULL0')
             AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL1') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL1')
             WHERE T1.VERSION_ID = V_VERSION_ID
             AND T2.CALIBER_FLAG = F_CALIBER_FLAG
             AND T1.GROUP_LEVEL = 'SHIPPING_OBJECT'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT V_VERSION_ID AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME,
                T1.BUSSINESS_OBJECT_CODE AS GROUP_CODE,
                T1.BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
                'BUSSINESS_OBJECT' AS GROUP_LEVEL,
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                'N' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_CODE,
                      T1.LV0_CN_NAME,
                      T1.LV1_CODE,
                      T1.LV1_CN_NAME,
                      T1.BUSSINESS_OBJECT_CODE,
                      T1.BUSSINESS_OBJECT_CN_NAME;
       DBMS_OUTPUT.PUT_LINE('BUSSINESS_OBJECT层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||'，BUSSINESS_OBJECT层级涨跌幅数据插入：DM_FOM_ANNL_MID_AMP_T表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T(
             VERSION_ID,
             PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ANNUAL_AMP,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG
            )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT
             FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T T1
             INNER JOIN FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.GROUP_CODE =T2.GROUP_CODE
             AND T1.LV0_CODE = T2.LV0_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             AND NVL(T1.LV1_CODE,'SNULL0') = NVL(T2.LV1_CODE,'SNULL0')
             WHERE T1.VERSION_ID = V_VERSION_ID
             AND T2.CALIBER_FLAG = F_CALIBER_FLAG
             AND T1.GROUP_LEVEL = 'BUSSINESS_OBJECT'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT V_VERSION_ID AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV1_CODE,
                T1.LV1_CN_NAME,
                T1.LV1_CODE AS GROUP_CODE,
                T1.LV1_CN_NAME AS GROUP_CN_NAME,
                'LV1' AS GROUP_LEVEL,
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                'N' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_CODE,
                      T1.LV0_CN_NAME,
                      T1.LV1_CODE,
                      T1.LV1_CN_NAME;
       DBMS_OUTPUT.PUT_LINE('LV1层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||'，LV1层级涨跌幅数据插入：DM_FOM_ANNL_MID_AMP_T表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T(
             VERSION_ID,
             PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ANNUAL_AMP,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG
            )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT
             FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T T1
             INNER JOIN FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.GROUP_CODE =T2.GROUP_CODE
             AND T1.LV0_CODE = T2.LV0_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             WHERE T1.VERSION_ID = V_VERSION_ID
             AND T2.CALIBER_FLAG = F_CALIBER_FLAG
             AND T1.GROUP_LEVEL = 'LV1'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT V_VERSION_ID AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_CODE,
                T1.LV0_CN_NAME,
                T1.LV0_CODE AS GROUP_CODE,
                T1.LV0_CN_NAME AS GROUP_CN_NAME,
                'LV0' AS GROUP_LEVEL,
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                'N' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_CODE,
                      T1.LV0_CN_NAME;
       DBMS_OUTPUT.PUT_LINE('LV0层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||'，LV0层级涨跌幅数据插入：DM_FOM_ANNL_MID_AMP_T表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

 -- 删除分视角年度涨跌幅表同版本的数据
   EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_AMP_T WHERE VERSION_ID = '||V_VERSION_ID||' AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
 
 -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除VERSION_ID= '||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||' 的分层级年度涨跌幅数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将所有层级年度涨跌幅数据插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNUAL_AMP_T(
--         ID,
         VERSION_ID,
         PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         CALIBER_FLAG)
  SELECT VERSION_ID,
         PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         CASE
           WHEN (GROUP_LEVEL = 'ITEM' AND MANUFACTURE_OBJECT_CODE IS NOT NULL AND SHIPPING_OBJECT_CODE IS NOT NULL) THEN MANUFACTURE_OBJECT_CODE
           WHEN (GROUP_LEVEL = 'ITEM' AND MANUFACTURE_OBJECT_CODE IS NULL AND SHIPPING_OBJECT_CODE IS NULL) THEN BUSSINESS_OBJECT_CODE
           WHEN GROUP_LEVEL = 'MANUFACTURE_OBJECT' THEN SHIPPING_OBJECT_CODE
           WHEN GROUP_LEVEL = 'SHIPPING_OBJECT' THEN BUSSINESS_OBJECT_CODE
           WHEN GROUP_LEVEL = 'BUSSINESS_OBJECT' THEN LV1_CODE
           WHEN GROUP_LEVEL = 'LV1' THEN LV0_CODE
           WHEN GROUP_LEVEL = 'LV0' THEN LV0_CODE
         END AS PARENT_CODE,
         CASE
           WHEN (GROUP_LEVEL = 'ITEM' AND MANUFACTURE_OBJECT_CODE IS NOT NULL AND SHIPPING_OBJECT_CODE IS NOT NULL) THEN MANUFACTURE_OBJECT_CN_NAME
           WHEN (GROUP_LEVEL = 'ITEM' AND MANUFACTURE_OBJECT_CODE IS NULL AND SHIPPING_OBJECT_CODE IS NULL) THEN BUSSINESS_OBJECT_CN_NAME
           WHEN GROUP_LEVEL = 'MANUFACTURE_OBJECT' THEN SHIPPING_OBJECT_CN_NAME
           WHEN GROUP_LEVEL = 'SHIPPING_OBJECT' THEN BUSSINESS_OBJECT_CN_NAME
           WHEN GROUP_LEVEL = 'BUSSINESS_OBJECT' THEN LV1_CN_NAME
           WHEN GROUP_LEVEL = 'LV1' THEN LV0_CN_NAME
           WHEN GROUP_LEVEL = 'LV0' THEN LV0_CN_NAME
         END AS PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         F_CALIBER_FLAG AS CALIBER_FLAG
       FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_MID_AMP_T;

 -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将VERSION_ID = '||V_VERSION_ID ||'，数据口径为：'||F_CALIBER_FLAG||' 的各层级年度涨跌幅数据插入到结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOM_ANNUAL_AMP_T';

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOM_ANNUAL_AMP_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

