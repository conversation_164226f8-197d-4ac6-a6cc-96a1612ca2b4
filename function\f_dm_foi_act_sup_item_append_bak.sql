-- Name: f_dm_foi_act_sup_item_append_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_act_sup_item_append_bak(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2023-08-08
创建人  ：黄心蕊 hwx1187045
背景描述：月度分析-TOP品类下供应商均价补齐，用于供应商到品类指数计算
参数描述：参数一(F_VERSION_ID)：通用版本号
		  参数二(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_ACT_SUPPLIER_APPEND(); --一个版本的数据
****************************************************************************************************************************************************************/

DECLARE
  V_SP_NAME VARCHAR2(500):= 'FIN_DM_OPT_FOI.F_DM_FOI_ACT_SUP_ITEM_APPEND';
  V_STEP_NUM INT:= 0 ; --步骤号
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-3)||'01','YYYYMM'); 
  V_VERSION INT;
BEGIN
  X_RESULT_STATUS:= 1;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
	V_STEP_NUM := V_STEP_NUM + 1; 
	--版本号入参判断，当入参为空，取TOP规格品清单最新版本号
	IF F_VERSION_ID IS NULL THEN
	SELECT VERSION_ID INTO V_VERSION
	  FROM FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T
	 ORDER BY LAST_UPDATE_DATE DESC
	 LIMIT 1;
	--入参不为空，则以入参为版本号
	ELSE V_VERSION := F_VERSION_ID;
	END IF;
  
  --1.清空供应商实际数补齐表的数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ACT_SUP_ITEM_APPEND_T';
  
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到本次执行版本号'||V_VERSION||'，并清空FIN_DM_OPT_FOI.DM_FOI_ACT_SUP_ITEM_APPEND_T供应商下ITEM实际数补齐',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  DROP TABLE IF EXISTS DM_SUP_ITEM_APPEND_TEMP;
  CREATE TEMPORARY TABLE DM_SUP_ITEM_APPEND_TEMP(
	YEAR INT,
	PERIOD_ID INT,
	ITEM_CODE	VARCHAR(50),
	ITEM_NAME	VARCHAR(2000),
	AVG_PRICE_CNY NUMERIC,
	SUPPLIER_CODE VARCHAR(200),
	SUPPLIER_CN_NAME VARCHAR(500),
	CATEGORY_CODE VARCHAR(200),
	CATEGORY_NAME VARCHAR(500),
	APPEND_FLAG VARCHAR(2),
	CREATION_DATE TIMESTAMP
	)
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY REPLICATION;

 --2.插入新补齐的均价实际数
 WITH CATEGORY_SUP_TEMP AS
 (
  --历史表里出现的品类,item,供应商,取数范围: (三年前第1月)至当前系统月(不含)  
  SELECT DISTINCT T.ITEM_CODE,
                   T.ITEM_NAME,
				   T.GROUP_CODE AS SUPPLIER_CODE,
				   T.GROUP_CN_NAME AS SUPPLIER_CN_NAME,
                   T.CATEGORY_CODE,
                   T.CATEGORY_NAME
    FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T T
   WHERE VERSION_ID = V_VERSION
	 AND TOP_FLAG = 'Y'
     AND UPPER(T.GROUP_LEVEL) = 'SUPPLIER'),

 PERIOD_DIM_TEMP AS
 (
  --生成连续月份, 三年前首月至当前系统实际月, (当前系统实际月 = 当前系统月-1)
  SELECT TO_CHAR(ADD_MONTHS(V_BEGIN_DATE, NUM.VAL - 1), 'YYYYMM') AS PERIOD_ID
    FROM GENERATE_SERIES(1,
                          TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                  V_BEGIN_DATE,
                                                  CURRENT_TIMESTAMP)),
                          1) NUM(VAL)),

 CROSS_JOIN_TEMP AS
 (
  --生成连续年月的品类,ITEM,供应商维
  SELECT A.CATEGORY_CODE,
          A.SUPPLIER_CODE,
		  A.ITEM_CODE,
		   A.ITEM_NAME,
          SUBSTR(B.PERIOD_ID, 1, 4) AS YEAR,
          B.PERIOD_ID
    FROM CATEGORY_SUP_TEMP A, PERIOD_DIM_TEMP B),

 SUP_SUM_TEMP AS
 (
  --按品类, 供应商, 会计期, 计算均价=汇总订单量和订单金额
  SELECT YEAR,
          PERIOD_ID,
		  ITEM_CODE,
		  ITEM_NAME,
          GROUP_CODE AS SUPPLIER_CODE,
          GROUP_CN_NAME AS SUPPLIER_CN_NAME,
          CATEGORY_CODE,
          CATEGORY_NAME,
          SUM(RECEIVE_AMT_CNY) / SUM(RECEIVE_QTY) AS AVG_AMT,
          'N' AS APD_FLAG
    FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T
   WHERE VERSION_ID = V_VERSION
	 AND TOP_FLAG = 'Y'
     AND UPPER(GROUP_LEVEL) = 'SUPPLIER'
   GROUP BY YEAR,
             PERIOD_ID,
             GROUP_CODE,
             GROUP_CN_NAME,
			 ITEM_CODE,
			 ITEM_NAME,
             CATEGORY_CODE,
             CATEGORY_NAME),

 FORWARD_FILLER_TEMP AS
 (
  --按照品类,供应商组, 向前寻找会计期补齐均价
  SELECT SS.CATEGORY_CODE,
          SS.SUPPLIER_CODE,
		  SS.ITEM_CODE,
          SS.YEAR,
          SS.PERIOD_ID,
          SS.AVG_AMT,
          FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY SS.CATEGORY_CODE, SS.SUPPLIER_CODE,SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
          SS.AVG_AMT_FLAG,
          SS.APD_FLAG
    FROM (SELECT S.CATEGORY_CODE,
                  S.SUPPLIER_CODE,
				  S.ITEM_CODE,
                  S.YEAR,
                  S.PERIOD_ID,
                  S.AVG_AMT,
                  SUM(S.NULL_FLAG) OVER(PARTITION BY S.CATEGORY_CODE, S.SUPPLIER_CODE,S.ITEM_CODE ORDER BY S.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                  S.APD_FLAG
             FROM (SELECT T1.CATEGORY_CODE,
                          T1.SUPPLIER_CODE,
						  T1.ITEM_CODE,
                          T1.YEAR,
                          T1.PERIOD_ID,
                          T2.AVG_AMT,
                          DECODE(T2.AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
                          NVL(T2.APD_FLAG, 'Y') AS APD_FLAG --补齐标识：Y为补齐，N为原始存在
                     FROM CROSS_JOIN_TEMP T1
                     LEFT JOIN SUP_SUM_TEMP T2
                       ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
                      AND T1.SUPPLIER_CODE = T2.SUPPLIER_CODE
					  AND T1.ITEM_CODE = T2.ITEM_CODE
                      AND T1.PERIOD_ID = T2.PERIOD_ID) S) SS)

	INSERT INTO DM_SUP_ITEM_APPEND_TEMP
	  (YEAR,
	   PERIOD_ID,
	   SUPPLIER_CODE,
	   SUPPLIER_CN_NAME,
	   ITEM_CODE,
	   ITEM_NAME,
	   AVG_PRICE_CNY,
	   CATEGORY_CODE,
	   CATEGORY_NAME,
	   CREATION_DATE,
	   APPEND_FLAG)
	--向后补齐均价
	  SELECT S.YEAR,
			 S.PERIOD_ID,
			 S.SUPPLIER_CODE,
			 S.SUPPLIER_CN_NAME,
			 S.ITEM_CODE,
			 S.ITEM_NAME,
			 NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS RECEIVE_AMT_CNY,
			 S.CATEGORY_CODE,
			 S.CATEGORY_NAME,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 S.APD_FLAG AS APPEND_FLAG
		FROM (SELECT T1.CATEGORY_CODE,
					 T1.SUPPLIER_CODE,
					 T3.CATEGORY_NAME,
					 T3.SUPPLIER_CN_NAME,
					 T3.ITEM_NAME,
					 T1.ITEM_CODE,
					 T1.YEAR,
					 T1.PERIOD_ID,
					 T1.AVG_AMT_2,
					 T2.AVG_AMT_3,
					 T1.APD_FLAG
				FROM FORWARD_FILLER_TEMP T1
				LEFT JOIN (SELECT DISTINCT S.CATEGORY_CODE,
										  S.SUPPLIER_CODE,
										  S.ITEM_CODE,
										  FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY S.CATEGORY_CODE, S.SUPPLIER_CODE,S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
										  FIRST_VALUE(S.AVG_AMT_2) OVER(PARTITION BY S.CATEGORY_CODE, S.SUPPLIER_CODE,S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
							FROM FORWARD_FILLER_TEMP S
						   WHERE S.AVG_AMT_FLAG > 0) T2
				  ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
				 AND T1.SUPPLIER_CODE = T2.SUPPLIER_CODE
				 AND T1.ITEM_CODE = T2.ITEM_CODE
				 AND T1.PERIOD_ID < T2.PERIOD_ID
				LEFT JOIN CATEGORY_SUP_TEMP T3 --关联带出供应商, 品类的名称信息
				  ON T1.CATEGORY_CODE = T3.CATEGORY_CODE
				 AND T1.SUPPLIER_CODE = T3.SUPPLIER_CODE
				 AND T1.ITEM_CODE = T3.ITEM_CODE) S;

  --2.写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入新补齐的供应商均价实际数到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');				 

INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ACT_SUP_ITEM_APPEND_T
  (YEAR,
   PERIOD_ID,
   ITEM_CODE,
   ITEM_NAME,
   SUPPLIER_CODE,
   SUPPLIER_CN_NAME,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L4_CEG_CODE,
   L4_CEG_SHORT_CN_NAME,
   L4_CEG_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_SHORT_CN_NAME,
   L3_CEG_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   AVG_PRICE_CNY,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   APPEND_FLAG)
  SELECT T1.YEAR,
         T1.PERIOD_ID,
		 T1.ITEM_CODE,
		 T2.ITEM_NAME,
         T1.SUPPLIER_CODE,
         T1.SUPPLIER_CN_NAME,
         T1.CATEGORY_CODE,
         T2.CATEGORY_NAME,
         T2.L4_CEG_CODE,
         T2.L4_CEG_SHORT_CN_NAME,
         T2.L4_CEG_CN_NAME,
         T2.L3_CEG_CODE,
         T2.L3_CEG_SHORT_CN_NAME,
         T2.L3_CEG_CN_NAME,
         T2.L2_CEG_CODE,
         T2.L2_CEG_CN_NAME,
         T1.AVG_PRICE_CNY,
         '-1' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         T1.APPEND_FLAG
    FROM DM_SUP_ITEM_APPEND_TEMP T1
    LEFT JOIN (SELECT DISTINCT CATEGORY_CODE,
                               CATEGORY_NAME,
							   ITEM_CODE,
							   ITEM_NAME,
                               L4_CEG_CODE,
                               L4_CEG_SHORT_CN_NAME,
                               L3_CEG_CODE,
                               L3_CEG_SHORT_CN_NAME,
                               L2_CEG_CODE,
                               L2_CEG_CN_NAME,
                               L4_CEG_CN_NAME,
                               L3_CEG_CN_NAME
                 FROM FIN_DM_OPT_FOI.DM_FOI_TOP_ITEM_INFO_T
                WHERE VERSION_ID = V_VERSION) T2
      ON T1.CATEGORY_CODE = T2.CATEGORY_CODE
	 AND T1.ITEM_CODE = T2.ITEM_CODE ;
     
  --写入日志
  V_STEP_NUM:=V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'FIN_DM_OPT_FOI.DM_FOI_ACT_SUP_ITEM_APPEND_T插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOI_ACT_SUP_ITEM_APPEND_T;
  
  --日志结束
  V_STEP_NUM:=V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOI_ACT_SUP_ITEM_APPEND_T统计信息完成!');
   
  
 RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END
$$
/

