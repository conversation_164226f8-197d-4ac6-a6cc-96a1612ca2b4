-- Name: f_dm_fom_resource_type_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_resource_type_amt_t(f_keystr character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
最近修改时间: 2024年11月22日 14点24分
修改时间: 2024年4月3日14点16分
修改内容: 任务令吸收明细表取数逻辑更新 1,关联字段修改  2,金额字段取相反数 3,筛选条件修改
修改人 :黄心蕊
创建时间：2023/12/08
创建人  ：许灿烽
背景描述： f_dm_fom_resource_type_amt_t 自制分资源类型金额表
参数描述:x_result_status :是否成功
来源表:  FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T A--标准成本吸收收敛
         FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I A1--任务令吸收明细(不是补录)
         INNER JOIN FIN_DM_OPT_FOI.APD_FOM_RESOUCE_CODE_T A2--资源代码类型
         FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T  --制造量纲维表
目标表:FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  --自制分资源类型金额表
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_RESOURCE_TYPE_AMT_T'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T'; -- 目标表
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT ; --版本号ID
  V_VERSION_NAME VARCHAR2(50) ; --版本中文名称
  V_DIM_VERSION_ID BIGINT ; --量纲维表版本号ID
  V_DIM_RESOURCE_VERSION_ID BIGINT ; --资源代码类型维表的版本号
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NULL THEN
--查询月度版本号
  SELECT VERSION_ID,VERSION INTO V_VERSION_ID,V_VERSION_NAME
    FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE UPPER(DATA_TYPE) = 'MONTH'
        AND DEL_FLAG = 'N'
        AND STATUS = 1  AND VERSION_TYPE='AUTO'
	ORDER BY VERSION_ID DESC LIMIT 1;
ELSE 
	V_VERSION_ID := F_VERSION_ID;
	SELECT VERSION INTO V_VERSION_NAME
	FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	WHERE VERSION_ID = V_VERSION_ID;
END IF;

-- 查询制造量纲维表的最新版本号
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIM_DMS'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;

-- 查询资源代码类型维表的版本号
  SELECT MAX(T.VERSION_ID) 
    INTO V_DIM_RESOURCE_VERSION_ID 
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIM_RESOURCE'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1;

--创建临时表, 插入解密的数据 (标准成本吸收收敛,自制历史金额)
  DROP TABLE IF EXISTS STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP;
  CREATE TEMPORARY TABLE STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP
  (
     PRIMARY_ID BIGINT,
     AMOUNT NUMERIC(100,2)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN
;

INSERT INTO STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP
SELECT PRIMARY_ID,
       TO_NUMBER(GS_DECRYPT(AMOUNT, f_keystr, 'AES128', 'CBC', 'SHA256')) AS AMOUNT
  FROM FIN_DM_OPT_FOI.STANDARD_CST_JAVA_ABS_AGGRE_T
--LIMIT 1000
;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入解密的数据 (标准成本吸收收敛,自制历史金额)',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

--创建临时表, 插入解密的数据 (任务令吸收明细(不是补录) 自制新增金额)
  DROP TABLE IF EXISTS RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP;
  CREATE TEMPORARY TABLE RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP
  (
     PRIMARY_ID BIGINT,
     GC_TRANSACTION_AMOUNT NUMERIC(100,2)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN
;

INSERT INTO RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP
SELECT PRIMARY_ID,
       TO_NUMBER(GS_DECRYPT(GC_TRANSACTION_AMOUNT,
                            f_keystr,
                            'AES128',
                            'CBC',
                            'SHA256')) AS GC_TRANSACTION_AMOUNT
  FROM FIN_DM_OPT_FOI.RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I
--LIMIT 1000
;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入解密的数据 (任务令吸收明细(不是补录) 自制新增金额)',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

 --创建临时表, 用于插入自制分资源类型金额
  DROP TABLE IF EXISTS RESOURCE_TYPE_AMT_TEMP;
  CREATE TEMPORARY TABLE RESOURCE_TYPE_AMT_TEMP
  (
    ITEM_CODE VARCHAR(50),
    PERIOD_ID VARCHAR(50),
    RESOURCE_TYPE VARCHAR(200),
    RMB_MADE_AMT NUMERIC
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;

INSERT INTO RESOURCE_TYPE_AMT_TEMP
SELECT T3.ITEM_CODE,
		T3.PERIOD_ID ,
		T3.RESOURCE_TYPE , --资源分类
		SUM(T3.RMB_MADE_AMT) AS RMB_MADE_AMT --吸收金额
  FROM (  --历史数
		SELECT ITEM AS ITEM_CODE,
			   PERIOD_ID :: INT AS PERIOD_ID,
			   CASE RESOURCE_TYPE
				 WHEN 'RES吸收_人工' THEN '人工'
				 WHEN 'RES吸收_通用机器' THEN '通用机器'
				 WHEN 'FT机器' THEN 'FT机器'
				 WHEN 'OH吸收_制造部' THEN 'OH'
			   END AS RESOURCE_TYPE, --资源分类
			   A2.AMOUNT AS RMB_MADE_AMT --吸收金额
		  FROM FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T A1 --标准成本吸收收敛 
		 INNER JOIN STANDARD_CST_JAVA_ABS_AGGRE_T_TEMP A2
			ON (A1.PRIMARY_ID = A2.PRIMARY_ID)
		 WHERE A1.RESOURCE_TYPE IN
			   ('RES吸收_人工', 'RES吸收_通用机器', 'FT机器', 'OH吸收_制造部')	
		   AND A1.HOMEMADE_EMS = '自制'                                         
		   AND A2.AMOUNT <> 0 --剔除金额为0的数据 202403版本修改                
                                                                                
		UNION ALL                                                               
		--新增数   
/*		
		SELECT A1.ASSEMBLY_ITEM_CODE AS ITEM_CODE,
			   CAST(CONCAT('20',
						   RIGHT(A1.PERIOD_NAME, 2),
						   CASE LEFT(A1.PERIOD_NAME, 3)
							 WHEN 'JAN' THEN '01'
							 WHEN 'FEB' THEN '02'
							 WHEN 'MAR' THEN '03'
							 WHEN 'APR' THEN '04'
							 WHEN 'MAY' THEN '05'
							 WHEN 'JUN' THEN '06'
							 WHEN 'JUL' THEN '07'
							 WHEN 'AUG' THEN '08'
							 WHEN 'SEP' THEN '09'
							 WHEN 'OCT' THEN '10'
							 WHEN 'NOV' THEN '11'
							 WHEN 'DEC' THEN '12'
						   END) AS BIGINT) AS PERIOD_ID ,--交易日期
			   CASE A2.RESOURCE_TYPE
				 WHEN 'RES-人工' THEN '人工'
				 WHEN 'RES-通用机器' THEN '通用机器'
				 WHEN 'RES-FT机器' THEN 'FT机器'
				 WHEN 'OH-制造平台' THEN 'OH'
				 WHEN 'OH-制造平台-自制' THEN 'OH'
			   END AS RESOURCE_TYPE, --资源分类 
			   A3.GC_TRANSACTION_AMOUNT * (-1) AS RMB_MADE_AMT --20240402修改 取相反数
		  FROM FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I A1 --任务令吸收明细(不是补录)
		 INNER JOIN FIN_DM_OPT_FOI.APD_FOM_RESOUCE_CODE_T A2 --资源代码类型
			ON (--A1.RESOURCE_CODE = A2.RESOURCE_CODE
				A1.RESOURCE_OVERHEAD_CODE = A2.RESOURCE_CODE) --20240402修改 
		 INNER JOIN RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP A3
			ON (A1.PRIMARY_ID = A3.PRIMARY_ID)
		 WHERE A2.VERSION_ID = V_DIM_RESOURCE_VERSION_ID
           --AND A2.HOMEMADE_EMS = '自制' --********修改
		   AND A1.WIP_ORDER_NUMBER IN
								   (SELECT DISTINCT T1.WIP_ORDER_NUMBER
									  FROM FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I T1
									  JOIN FIN_DM_OPT_FOI.APD_FOM_RESOUCE_CODE_T T2
										ON T1. RESOURCE_OVERHEAD_CODE = T2.RESOURCE_CODE
									 WHERE T2.HOMEMADE_EMS = '自制'
									   AND T2.VERSION_ID = V_DIM_RESOURCE_VERSION_ID)  --********修改 
		   AND A1.COA_ACCOUNT_CODE in('5980201','5980202','5980203') --********修改 
		   AND A2.RESOURCE_TYPE IN ('RES-人工',
									'RES-通用机器',
									'RES-FT机器',
									'OH-制造平台',
									'OH-制造平台-自制')
		   AND A3.GC_TRANSACTION_AMOUNT <> 0 --剔除金额为0的数据 202403版本修改

		  */
	--202411版本
        SELECT T1.ASSEMBLY_ITEM_CODE AS ITEM_CODE, --ITEM_CODE
			TO_CHAR(T1.TRANSACTION_DATE, 'YYYYMM') :: INT AS PERIOD_ID, --会计期
                CASE
                  WHEN T1.APD_RESOUCE_TYPE IN ('RES-FT机器', 'RES-FT低耗') THEN 'FT机器'
                  WHEN T1.APD_RESOUCE_TYPE IN ('RES-通用机器', 'RES_通用物耗') THEN '通用机器'
                  WHEN T1.APD_RESOUCE_TYPE = 'RES-人工' THEN '人工'
                  WHEN T1.APD_RESOUCE_TYPE = 'OH-制造平台' THEN 'OH'
                END AS RESOURCE_TYPE,
                T2.GC_TRANSACTION_AMOUNT AS RMB_MADE_AMT --吸收金额
          FROM FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_RESOUCE_TRANS_T T1
         INNER JOIN RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I_TEMP T2
            ON (T1.PRIMARY_ID = T2.PRIMARY_ID)
		 INNER JOIN DWRDIM.DWR_DIM_DEPARTMENT_D D		--******** 加入部门维表
		 	ON T1.COA_DEPT_KEY = D.DEPT_KEY
         WHERE T1.ABSORPTION_ACCOUNT IN (5980201, 5980202, 5980203)
           AND T1.HOMEMADE_TYPE = '自制'
		   AND D.L1_DEPT_CODE = '035347'		--******** 加入部门维表条件筛选
		   AND T2.GC_TRANSACTION_AMOUNT <> 0 --剔除金额为0的数据
           AND T1.APD_RESOUCE_TYPE IN ('RES-FT机器',
                                       'RES-FT低耗',
                                       'RES-通用机器',
                                       'RES_通用物耗',
                                       'RES-人工',
                                       'OH-制造平台')
 ) T3
 GROUP BY T3.ITEM_CODE,
          T3.PERIOD_ID ,
          T3.RESOURCE_TYPE --资源分类
		  ;
 
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '解密自制分资源类型金额,并分组汇总插入数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

--删除当前版本数据
-- DELETE FROM FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T
-- WHERE VERSION_ID = V_VERSION_ID;

TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T; /*20240113修改 集成之后表数据无需保留多版本*/

INSERT INTO FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T
/*
SELECT V_VERSION_ID AS VERSION_ID,
       V_VERSION_NAME AS VERSION_NAME,
       SUBSTR(A.PERIOD_ID, 1, 4) AS PERIOD_YEAR,
       A.PERIOD_ID,
       B.LV0_CODE,
       B.LV0_CN_NAME,
       B.LV1_CODE,
       B.LV1_CN_NAME,
       B.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CODE,
       B.BUSSINESS_OBJECT AS BUSSINESS_OBJECT_CN_NAME,
       B.SHIPPING_OBJECT AS SHIPPING_OBJECT_CODE,
       B.SHIPPING_OBJECT AS SHIPPING_OBJECT_CN_NAME,
       B.MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT_CODE,
       B.MANUFACTURE_OBJECT AS MANUFACTURE_OBJECT_CN_NAME,
       A.ITEM_CODE ,
       '' AS ITEM_CN_NAME ,--ITEM中文名称  --20240113修改，NAME不全字段置空，以免后续数据发散
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE, 
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       'N' AS DEL_FLAG,
       GS_ENCRYPT(A.RMB_MADE_AMT, f_keystr, 'AES128', 'CBC', 'SHA256') AS RMB_MADE_AMT,
       A.RESOURCE_TYPE --资源分类
  FROM RESOURCE_TYPE_AMT_TEMP A
 INNER JOIN FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T B
    ON (A.ITEM_CODE = B.ITEM_CODE)
 WHERE B.DIM_TREE_FLAG = 1 --只取在维度树的数据
   AND B.VERSION_ID = V_DIM_VERSION_ID --只取一个版本的数据
;*/
--202411版本
SELECT V_VERSION_ID AS VERSION_ID,
       V_VERSION_NAME AS VERSION_NAME,
       SUBSTR(A.PERIOD_ID, 1, 4) AS PERIOD_YEAR,
       A.PERIOD_ID,
       B.APD_MANUFACTURE_PROD_LV0 AS LV0_CODE,
       B.APD_MANUFACTURE_PROD_LV0 AS LV0_CN_NAME,
       B.APD_MANUFACTURE_PROD_LV1 AS LV1_CODE,
       B.APD_MANUFACTURE_PROD_LV1 AS LV1_CN_NAME,
       B.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT_CODE,
       B.APD_OPERATE_OBJECT AS BUSSINESS_OBJECT_CN_NAME,
       CASE
         WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE B.APD_SHIPMENT_OBJECT
       END AS SHIPMENT_OBJECT_CODE, --发货对象编码
       CASE
         WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE B.APD_SHIPMENT_OBJECT
       END AS APD_SHIPMENT_OBJECT_CN_NAME, --发货对象名称
       CASE
         WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE B.APD_MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CODE, --制造对象编码
       CASE
         WHEN B.APD_MANUFACTURE_PROD_LV0 = 'ICT' AND B.APD_MANUFACTURE_PROD_LV1 = '云核心网' THEN ''
         WHEN B.APD_MANUFACTURE_PROD_LV0 = '海思光电' AND B.APD_MANUFACTURE_PROD_LV1 = '海思光电' THEN ''
         ELSE B.APD_MANUFACTURE_OBJECT
       END AS MANUFACTURE_OBJECT_CN_NAME, --制造对象名称
       A.ITEM_CODE ,
       '' AS ITEM_CN_NAME ,--ITEM中文名称  /*20240113修改，NAME不全字段置空，以免后续数据发散*/
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE, 
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       'N' AS DEL_FLAG,
       GS_ENCRYPT(A.RMB_MADE_AMT, f_keystr, 'AES128', 'CBC', 'SHA256') AS RMB_MADE_AMT,
       A.RESOURCE_TYPE --资源分类
  FROM RESOURCE_TYPE_AMT_TEMP A
   INNER JOIN FIN_DM_OPT_FOI.APD_INV_ITEM_MANUFACTURE_T B
    ON (A.ITEM_CODE = B.ITEM_CODE);


  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除当月版本自制分资源类型金额,并重新插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
-- 202411版本 经营对象 发货对象 制造对象中，任意层级为空，则继承上级后赋值‘未定义’
--制造对象依赖发货对象，发货对象依赖经营对象，即UPDATE存在先后顺序，不可更改

--替换经营对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  SET BUSSINESS_OBJECT_CODE = LV1_CN_NAME||'_未定义经营对象' WHERE 
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE BUSSINESS_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  SET BUSSINESS_OBJECT_CN_NAME = LV1_CN_NAME||'_未定义经营对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE BUSSINESS_OBJECT_CN_NAME IS NULL
  END );
  
--替换发货对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  SET SHIPPING_OBJECT_CODE = BUSSINESS_OBJECT_CN_NAME||'_未定义发货对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE SHIPPING_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  SET SHIPPING_OBJECT_CN_NAME = BUSSINESS_OBJECT_CN_NAME||'_未定义发货对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE SHIPPING_OBJECT_CN_NAME IS NULL
  END );
  
--替换制造对象空值字段
UPDATE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  SET MANUFACTURE_OBJECT_CODE = SHIPPING_OBJECT_CODE||'_未定义制造对象' WHERE 
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE MANUFACTURE_OBJECT_CODE IS NULL
  END );
UPDATE FIN_DM_OPT_FOI.DM_FOM_RESOURCE_TYPE_AMT_T  SET MANUFACTURE_OBJECT_CN_NAME = SHIPPING_OBJECT_CN_NAME||'_未定义制造对象' WHERE
(CASE 
  WHEN LV0_CN_NAME = 'ICT' AND LV1_CN_NAME = '云核心网' THEN 1=0
  WHEN LV0_CN_NAME = '海思光电' AND LV1_CN_NAME = '海思光电' THEN 1=0
  ELSE MANUFACTURE_OBJECT_CN_NAME IS NULL
  END );
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '自制分资源类型金额重置维度字段空值完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END

$$
/

