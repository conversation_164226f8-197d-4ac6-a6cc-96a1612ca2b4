-- Name: f_dm_foi_public_logic_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_public_logic_t(f_flag text DEFAULT NULL::text, f_jud_condition text DEFAULT NULL::text, f_prar1 text DEFAULT NULL::text, f_prar2 text DEFAULT NULL::text, f_version text DEFAULT NULL::text, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2023/1/30
创建人  ：唐钦
背景描述：公共逻辑调用函数,提取其余逻辑函数中复用率较高的SQL组成，通过入参的判断进行调用
参数描述：x_success_flag ：是否成功
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_public_logic_t(f_flag,f_jud_condition,f_prar1,f_prar2)
*/
DECLARE
	V_SP_NAME        VARCHAR(50):= 'F_DM_FOI_PUBLIC_LOGIC_T';
	V_EXCEPTION_FLAG VARCHAR(10):='1';   --异常定点
	v_version        bigint;
  v_dml_row_count  number default 0 ;
  v_base_period_id  number default (select to_number(to_char(now()+'-3 year','yyyy')||'01')) ;
	v_base_period_id_array TEXT := ' IN (to_char(now()+''-2 year'',''yyyy'')||''01'', to_char(now()+''-1 year'',''yyyy'')||''01'', to_char(now(),''yyyy'')||''01'')';
  v_year  int default (to_number(to_char(now()+'-3 year','yyyy'))) ; /*这里的4年的第一年的年份*/
	V_INSERT_PUBLIC	 TEXT := NULL;   -- 指数计算插入语句公共部分
	V_PART1_PUBLIC   TEXT := NULL;   -- 提取函数公共部分变量
	V_PART2_PUBLIC   TEXT := NULL;   -- 提取函数公共部分变量
	V_PART3_PUBLIC   TEXT := NULL;   -- 提取函数公共部分变量
	V_PART4_PUBLIC   TEXT := NULL;   -- 提取函数公共部分变量
	V_PART5_PUBLIC   TEXT := NULL;   -- 提取函数公共部分变量
	V_VAR_PRAR1			 TEXT := NULL;	 -- 变量1
	V_VAR_PRAR2			 TEXT := NULL;	 -- 变量2
	V_VAR_PRAR3			 TEXT := NULL;	 -- 变量3
	V_VAR_PRAR4			 TEXT := NULL;	 -- 变量4
	V_PART_CLASS 		 TEXT := NULL;	 -- 变量，LV2层级、TOP分类、品类分类逻辑计算时，公用部分
	V_VAR_PRAR			 TEXT := NULL;	 -- 变量，用于指数计算V_PART2_PUBLIC的'$compute_sub_sql$'部分替换
	V_BY_YEAR_PRAR1	 TEXT := NULL;	 -- 变量，用于指计算分年指数时使用
	V_BY_YEAR_PRAR2	 TEXT := NULL;	 -- 变量，用于指计算分年指数时使用
	V_EXECUTE_SQL    TEXT := NULL;   -- 执行SQL
BEGIN
	X_SUCCESS_FLAG := '1';
--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');	 
-- 优先判断入参F_FLAG
-- 当F_FLAG = 'SCHEDULE',走调度的逻辑
	IF UPPER(F_FLAG) = 'SCHEDULE' THEN	
	V_EXCEPTION_FLAG := '1';
-- 删除变量参数表中之前插入的版本号数据
	DELETE FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
			WHERE UPPER(SUBSTR(PARA_NAME,1,10)) = 'VERSION_ID'
			AND PARA_NAME = 'VERSION_ID-'||F_PRAR1;	
-- 季度调度/月度调度时，查询版本号的公用sql部分
	V_PART1_PUBLIC :=  '
											INSERT INTO FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
											( ID,
												PARA_NAME,
												PARA_DESCRIPTION,
												VALUE,
												VERSION_ID,
												CREATED_BY,
												CREATION_DATE,
												LAST_UPDATED_BY,
												LAST_UPDATE_DATE,
												DEL_FLAG,
												ENABLE_FLAG
											)
											';  -- 插入查询的版本号及相关内容到变量参数表
	V_PART2_PUBLIC := '  
										SELECT FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_S.NEXTVAL AS ID,
													 ''VERSION_ID-$data_type$'' AS PARA_NAME,
													 ''配置调度时函数查询使用的版本号'' AS PARA_DESCRIPTION,
													 T.VERSION_ID AS VALUE,
													 NULL AS VERSION_ID,
													 -1 AS CREATED_BY,
													 CURRENT_TIMESTAMP  AS CREATION_DATE,
													 -1 AS LAST_UPDATED_BY,
													 CURRENT_TIMESTAMP  AS LAST_UPDATE_DATE,
													 ''N'' AS DEL_FLAG,
													 ''Y'' AS ENABLE_FLAG
												FROM
													FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T T
												WHERE
													SUBSTR(VERSION,1,6) = ( SELECT MAX (SUBSTR(VERSION,1,6)  ) 
																											FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T 
																											WHERE UPPER(DATA_TYPE) IN (''CATEGORY'',''ITEM'')) 
													AND UPPER(DEL_FLAG) = ''N''
													AND STATUS = 1
													AND UPPER(DATA_TYPE) = ''$data_type$''
												';  -- 条件DATE_TYPE作为变量替换，可替换为CATEGORY或ITEM
	
-- 判断F_JUD_CONDITION的入参，如果是'Q'，是季度调度，如果是'M'，是月度调度，V_PART3_PUBLIC内容不一致
	IF UPPER(F_JUD_CONDITION) = 'Q' THEN 									 
		V_PART3_PUBLIC := ' AND UPPER(version_type) = ''AUTO''';   -- 季度调度
	ELSIF UPPER(F_JUD_CONDITION) = 'M' THEN  	
		V_PART3_PUBLIC := '	
											AND UPPER(VERSION_TYPE) IN (''FINAL'',''AUTO'')
											ORDER BY  VERSION_TYPE DESC
											LIMIT 1;
												';   -- 月度调度
	END IF;	
-- 拼接SQL，执行操作	
	V_PART2_PUBLIC := REPLACE( V_PART2_PUBLIC,'$data_type$',F_PRAR1 );
	V_EXECUTE_SQL := V_PART1_PUBLIC || V_PART2_PUBLIC || V_PART3_PUBLIC;
	EXECUTE V_EXECUTE_SQL;	
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '删除变量参数表版本号数据，并插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');	 
	 -- 当F_FLAG = 'INDEX',走指数计算的逻辑
	 V_EXCEPTION_FLAG := '2';
	 ELSIF UPPER(F_FLAG) = 'INDEX' THEN 
	 V_VERSION := F_VERSION;    -- 给版本号赋值入参传递的值										
		-- 当F_JUD_CONDITION = 'TEMP'时，是计算逻辑插入到临时表中，当F_JUD_CONDITION = 'FORMAL'时,插入到正式表
	 IF UPPER(F_JUD_CONDITION) = 'TEMP' THEN
	 -- 指数计算插入临时表语句公共部分
	 V_INSERT_PUBLIC :='
											insert into FIN_DM_OPT_FOI.dm_foi_price_index_tmp1         
											(year,
											period_id,
											base_period_id,
											group_code,
											group_cn_name,
											group_level,
											parent_code,
											price_index,
											$ceg_code$,
											top_type
										';																	
	 -- 指数计算逻辑部分									
	 V_PART1_PUBLIC := ' )
											 select t.year,
														t.period_id,
														$base_period$,
														$ceg_part$,
														sum(t.price_index) * 100 as price_index,  
														$ceg_code$,
														NULL AS TOP_TYPE
													from (
													';
	 V_PART2_PUBLIC := '
											 select t.year,
											 			t.period_id,
											 			t.group_code,
											 			t.group_cn_name,
											 			t.group_level,
											 			$compute_sub_sql$,
											 			t.l4_ceg_code,
											 			t.l4_ceg_short_cn_name,
											 			t.l3_ceg_code,
											 			t.l3_ceg_short_cn_name,
											 			t.l2_ceg_code,
											 			t.l2_ceg_cn_name,
											 			t.l4_ceg_cn_name,
											 			t.l3_ceg_cn_name
											 				';		
	 V_PART3_PUBLIC := '								
										 from $source_table$
										 join (select distinct group_code, weight
                        from FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t
                       where version_id = '||v_version||'
                         and period_type = ''S'' --取四年综合权重
                         and top_flag = ''Y'' --排除供应商层级权重以及配置表数据
												 and upper(parent_level) = ''$parent_level$''
                         and upper(group_level) = ''$group_level$'') t3
              on t.group_code = t3.group_code
								';							
		V_PART4_PUBLIC := '	
										) t
									group by t.year,
												t.period_id,
												t.parent_code,
												t.l2_ceg_code,
												t.l2_ceg_cn_name
											';
		V_PART5_PUBLIC := '									
											left join (select t1.group_code, $field$
                        from FIN_DM_OPT_FOI.dm_foi_price_index_tmp1 t1
                      where upper(t1.group_level) = ''$group_level$''
                        and t1.period_id $period$ ) t2
              on t.group_code = $rel_condition$
							where upper(t.group_level) = ''$group_level$''
											 ';			
		-- LV2层级、TOP分类、品类分类逻辑计算时，公用部分
		V_PART_CLASS := '
										 FROM FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_TMP1 T
										 JOIN (
											 SELECT DISTINCT GROUP_CODE, WEIGHT, $CLASS_TYPE$
												 FROM FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
												 WHERE VERSION_ID = '|| V_VERSION ||'
													 AND PERIOD_TYPE = ''S''
													 AND TOP_FLAG = ''Y''
													 $SCREE_CONDITION$) T3
										 ON T.GROUP_CODE = T3.GROUP_CODE
											';
		-- 指数分年计算时，关联条件的公用部分
		V_BY_YEAR_PRAR1 := '
									and t.year!= '||v_year||'
									and t.base_period_id '||v_base_period_id_array
									 ;
		V_BY_YEAR_PRAR2 := v_base_period_id_array ||'
						and t1.base_period_id = t1.period_id
										';						 		
	 /* 当F_PRAR1 = 'CATEGORY'，是品类层级计算；'LV4'，是模块层级计算；'LV3'，是专家团层级计算；'LV2'，是生产采购层级计算；'TOP_TYPE'，是TOP分类逻辑计算；
	 'CATEGORY_TYPE'，是品类分类逻辑计算 */
	 IF UPPER(F_PRAR1) = 'CATEGORY' THEN   -- 品类层级计算
	 V_VAR_PRAR1 := '
									 l4_ceg_code,
									 l4_ceg_short_cn_name,
									 l3_ceg_code,
									 l3_ceg_short_cn_name,
									 l2_ceg_code,
									 l2_ceg_cn_name,
									 l4_ceg_cn_name,
									 l3_ceg_cn_name
										';
	 V_INSERT_PUBLIC := REPLACE( V_INSERT_PUBLIC,'$ceg_code$',V_VAR_PRAR1 );
	 V_VAR_PRAR2 := '
									t.category_code  group_code,
								  t.category_name group_cn_name,
								  ''CATEGORY'' group_level,
								  t.l4_ceg_code parent_code
										';
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_code$',V_VAR_PRAR1 ); 
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_part$',V_VAR_PRAR2 );
	 V_VAR_PRAR1 := 'CATEGORY';
	 V_VAR_PRAR2 := 'ITEM';
	 V_VAR_PRAR4 := ' FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t t';
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$parent_level$',V_VAR_PRAR1 );
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$group_level$',V_VAR_PRAR2 );
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$source_table$',V_VAR_PRAR4 );
	 V_PART4_PUBLIC := '  ) t
											group by t.year,
															t.period_id,
															t.parent_code,
															t.category_code,
															t.category_name,
															t.l4_ceg_code,
															t.l4_ceg_short_cn_name,
															t.l3_ceg_code,
															t.l3_ceg_short_cn_name,
															t.l2_ceg_code,
															t.l2_ceg_cn_name,
															t.l4_ceg_cn_name,
															t.l3_ceg_cn_name,
															t.top_flag,
															t.version_id
												';
	 V_PART5_PUBLIC := '
											left join (select t1.group_code,$field$         
                        from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t t1
                       where t1.version_id = '||v_version||'
                         and upper(t1.group_level) = ''ITEM''
                         and t1.period_id $period$       
                         and t1.top_flag = ''Y''
												 ) t2
										 on t.group_code = $rel_condition$
										where upper(t.group_level) = ''ITEM''
										and t.version_id = '||v_version||' and t.top_flag = ''Y''
											 ';												
	 V_VAR_PRAR := '
									 t.parent_code,
									 t.avg_price_cny / nullif(t2.avg_price_cny, 0) * t3.weight as price_index,
									 t.item_code,
									 t.item_name,
									 t.category_code,
									 t.category_name,
									 t.top_flag,
									 t.version_id
									  ';									 
	 IF UPPER(F_PRAR2) = 'BY_YEAR' THEN   -- 分年指数计算，基期为每年1月份的
	 V_VAR_PRAR1 := 't.base_period_id';
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',V_VAR_PRAR1 );
	 V_VAR_PRAR := 't2.period_id base_period_id,' || V_VAR_PRAR;
	 V_PART2_PUBLIC := REPLACE( V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_PART4_PUBLIC := V_PART4_PUBLIC || ', t.base_period_id';
	 V_VAR_PRAR1 := 't1.avg_price_cny,t1.period_id,t1.year';   /*需要加增关联条件和当年的准值*/
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$',v_base_period_id_array );   /*取后面3年的数据的准值*/
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code and t.year=t2.year' );
	 V_PART5_PUBLIC := V_PART5_PUBLIC || ' and t.year!= '||v_year;
	 ELSIF UPPER(F_PRAR2) = 'BASE' THEN    -- 基期为默认基期的
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',v_base_period_id );
	 V_PART2_PUBLIC := REPLACE( V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_VAR_PRAR1 := 't1.avg_price_cny';  
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$','= '||v_base_period_id );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code' );
	 END IF;	 
	 ELSIF UPPER(F_PRAR1) = 'LV4' THEN   -- 模块层级计算
	 V_VAR_PRAR1 := '
									 l3_ceg_code,
									 l3_ceg_short_cn_name,
									 l2_ceg_code,
									 l2_ceg_cn_name,
									 l4_ceg_cn_name,
									 l3_ceg_cn_name
										';
	 V_INSERT_PUBLIC := REPLACE( V_INSERT_PUBLIC,'$ceg_code$',V_VAR_PRAR1 );
	 V_VAR_PRAR2 := '
									t.l4_ceg_code group_code,
									t.l4_ceg_short_cn_name group_cn_name,
									''LV4'' group_level,
									t.l3_ceg_code parent_code
										';
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_code$',V_VAR_PRAR1 ); 
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_part$',V_VAR_PRAR2 ); 
	 V_VAR_PRAR1 := 'LV4';
	 V_VAR_PRAR2 := 'CATEGORY';
	 V_VAR_PRAR3 := ' FIN_DM_OPT_FOI.dm_foi_price_index_tmp1 t';
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$parent_level$',V_VAR_PRAR1 );
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$group_level$',V_VAR_PRAR2 );
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$source_table$',V_VAR_PRAR3 );		
	 V_VAR_PRAR1 := ', t.l4_ceg_code,
									   t.l4_ceg_short_cn_name,
									   t.l4_ceg_cn_name,
									   t.l3_ceg_code,
									   t.l3_ceg_short_cn_name,
									   t.l3_ceg_cn_name
									  ';
	 V_PART4_PUBLIC := V_PART4_PUBLIC || V_VAR_PRAR1;
	 V_VAR_PRAR1 := 'CATEGORY';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$group_level$',V_VAR_PRAR1 );
	 V_VAR_PRAR := 't.parent_code,
									t.price_index / nullif(t2.price_index, 0) * t3.weight   price_index';	 
	 IF UPPER(F_PRAR2) = 'BY_YEAR' THEN   -- 分年指数计算，基期为每年1月份的
	 V_VAR_PRAR1 := 't.base_period_id';
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',V_VAR_PRAR1 );
	 V_VAR_PRAR := 't2.period_id base_period_id,' || V_VAR_PRAR; 
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_PART4_PUBLIC := V_PART4_PUBLIC || ', t.base_period_id';
	 V_VAR_PRAR1 := 't1.price_index,t1.period_id,t1.year ';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$',V_BY_YEAR_PRAR2 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code and t.year=t2.year' );
	 V_PART5_PUBLIC := V_PART5_PUBLIC || V_BY_YEAR_PRAR1;			
	 ELSIF UPPER(F_PRAR2) = 'BASE' THEN    -- 基期为默认基期的
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',v_base_period_id );
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_VAR_PRAR1 := 't1.price_index';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$','= '||v_base_period_id );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code' );
	 END IF;	 
	 -- 专家团层级计算	
	 ELSIF UPPER(F_PRAR1) = 'LV3' THEN   
	 V_VAR_PRAR1 := '
									 l2_ceg_code,
									 l2_ceg_cn_name,
									 l3_ceg_cn_name
										';
	 V_INSERT_PUBLIC := REPLACE( V_INSERT_PUBLIC,'$ceg_code$',V_VAR_PRAR1 );
	 V_VAR_PRAR2 := '
									t.l3_ceg_code group_code,
									t.l3_ceg_short_cn_name group_cn_name,
									''LV3'' group_level,
									t.l2_ceg_code parent_code
										';
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_code$',V_VAR_PRAR1 ); 
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_part$',V_VAR_PRAR2 ); 
	 V_VAR_PRAR1 := 'LV3';
	 V_VAR_PRAR2 := 'LV4';
	 V_VAR_PRAR3 := ' FIN_DM_OPT_FOI.dm_foi_price_index_tmp1 t';
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$parent_level$',V_VAR_PRAR1 );
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$group_level$',V_VAR_PRAR2 );
	 V_PART3_PUBLIC := REPLACE( V_PART3_PUBLIC,'$source_table$',V_VAR_PRAR3 );	
	 V_VAR_PRAR1 := ', t.l3_ceg_code,
									   t.l3_ceg_short_cn_name,
									   t.l3_ceg_cn_name
									  ';
	 V_PART4_PUBLIC := V_PART4_PUBLIC || V_VAR_PRAR1; 
	 V_VAR_PRAR1 := 'LV4';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$group_level$',V_VAR_PRAR1 );
	 V_VAR_PRAR := 't.parent_code,
									t.price_index / nullif(t2.price_index, 0) * t3.weight   price_index';	 
	 IF UPPER(F_PRAR2) = 'BY_YEAR' THEN   -- 分年指数计算，基期为每年1月份的
	 V_VAR_PRAR1 := 't.base_period_id';
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',V_VAR_PRAR1 );
	 V_VAR_PRAR := 't2.period_id base_period_id,' || V_VAR_PRAR; 
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_PART4_PUBLIC := V_PART4_PUBLIC || ', t.base_period_id';
	 V_VAR_PRAR1 := 't1.price_index,t1.period_id,t1.year ';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$',V_BY_YEAR_PRAR2 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code and t.year=t2.year' );   /*分年计算指数，所有新增年相等*/
	 V_PART5_PUBLIC := V_PART5_PUBLIC || V_BY_YEAR_PRAR1;				 	
	 ELSIF UPPER(F_PRAR2) = 'BASE' THEN    -- 基期为默认基期的
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',v_base_period_id );
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_VAR_PRAR1 := 't1.price_index';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$','= '||v_base_period_id );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code' );
	 END IF;	 
	 -- 生产采购层级计算
	 ELSIF UPPER(F_PRAR1) = 'LV2' THEN   
	 V_INSERT_PUBLIC := REPLACE( V_INSERT_PUBLIC,'$ceg_code$','continuity_type' );
	 V_VAR_PRAR1 := '
									  t.l2_ceg_code as group_code,
									  t.l2_ceg_cn_name as group_cn_name,
									  ''LV2'' group_level,
									  null as parent_code
										';
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_code$','continuity_type' ); 
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_part$',V_VAR_PRAR1 ); 
	 V_PART_CLASS := REPLACE(  V_PART_CLASS,'$CLASS_TYPE$','CONTINUITY_TYPE');   --指数表取出带连续性标签的专家团权重
	 V_VAR_PRAR2 := ' AND UPPER(PARENT_LEVEL) = ''LV2''
                   AND UPPER(GROUP_LEVEL) = ''LV3''
									  ';
	 V_PART3_PUBLIC := REPLACE(  V_PART_CLASS,'$SCREE_CONDITION$',V_VAR_PRAR2 ); 
	 V_VAR_PRAR1 := ', T.CONTINUITY_TYPE';
	 V_PART4_PUBLIC := V_PART4_PUBLIC || V_VAR_PRAR1;
	 V_VAR_PRAR1 := 'LV3';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$group_level$',V_VAR_PRAR1 );		
	 V_VAR_PRAR := 't3.continuity_type,
	                null as parent_code,
									t.price_index / nullif(t2.price_index, 0) * t3.weight   price_index';
	 
	 IF UPPER(F_PRAR2) = 'BY_YEAR' THEN   -- 分年指数计算，基期为每年1月份的
	 V_VAR_PRAR1 := 't.base_period_id';
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',V_VAR_PRAR1 );
	 V_VAR_PRAR := 't2.period_id base_period_id,' || V_VAR_PRAR; 
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_PART4_PUBLIC := V_PART4_PUBLIC || ', t.base_period_id';
	 V_VAR_PRAR1 := 't1.price_index,t1.period_id,t1.year ';
	 V_VAR_PRAR2 := 't2.group_code  
									 and t.year=t2.year
								    ';     /*分年计算指数，所有新增年相等*/
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$',V_BY_YEAR_PRAR2 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$',V_VAR_PRAR2 );   /*分年计算指数，所有新增年相等*/
	 V_PART5_PUBLIC := V_PART5_PUBLIC || V_BY_YEAR_PRAR1;	
	 ELSIF UPPER(F_PRAR2) = 'BASE' THEN    -- 基期为默认基期的
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',v_base_period_id );
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );
	 V_VAR_PRAR1 := 't1.price_index';
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$','= '||v_base_period_id );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code' );
	 END IF;									 	 
	 ELSIF UPPER(F_PRAR1) = 'TOP_TYPE' THEN   -- TOP分类逻辑计算
	 V_VAR_PRAR1 := 'l2_ceg_code,l2_ceg_cn_name';
   V_INSERT_PUBLIC := REPLACE( V_INSERT_PUBLIC,'$ceg_code$',V_VAR_PRAR1 );
	 V_VAR_PRAR2 := '
									 null group_code,
									 t.parent_code group_cn_name,
									 ''TOP_TYPE'' group_level,
									 t.l2_ceg_code parent_code
										';
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'NULL','t.parent_code' ); 										
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_code$',V_VAR_PRAR1 ); 
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_part$',V_VAR_PRAR2 ); 
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',v_base_period_id );
	 V_VAR_PRAR := 't3.top_type parent_code,
									t.price_index / nullif(t2.price_index, 0) * t3.weight   price_index';
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );	
	 V_PART_CLASS := REPLACE(  V_PART_CLASS,'$CLASS_TYPE$','TOP_TYPE');
	 V_VAR_PRAR2 := ' AND TOP_TYPE IS NOT NULL';
	 V_PART3_PUBLIC := REPLACE(  V_PART_CLASS,'$SCREE_CONDITION$',V_VAR_PRAR2 );
	 V_VAR_PRAR1 := 'CATEGORY';
	 V_VAR_PRAR2 := 't1.price_index';
	 V_VAR_PRAR3 := ' and t.base_period_id = '||v_base_period_id;
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$group_level$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR2 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$','= '||v_base_period_id );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code' );
	 V_PART5_PUBLIC := V_PART5_PUBLIC || V_VAR_PRAR3;	 							
	 ELSIF UPPER(F_PRAR1) = 'CATEGORY_TYPE' THEN   -- 品类分类逻辑计算
	 V_VAR_PRAR1 := 'l2_ceg_code,l2_ceg_cn_name';
   V_INSERT_PUBLIC := REPLACE( V_INSERT_PUBLIC,'$ceg_code$',V_VAR_PRAR1 );
	 V_VAR_PRAR2 := '
									 null group_code,
									 t.parent_code group_cn_name,
									 ''CATEGORY_TYPE'' group_level,
									 t.l2_ceg_code parent_code
										';										
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_code$',V_VAR_PRAR1 ); 
	 V_PART1_PUBLIC := REPLACE(  V_PART1_PUBLIC,'$ceg_part$',V_VAR_PRAR2 );
	 V_PART1_PUBLIC := REPLACE( V_PART1_PUBLIC,'$base_period$',v_base_period_id );
	 V_VAR_PRAR := 't3.parent_code parent_code,
									t.price_index / nullif(t2.price_index, 0) * t3.weight   price_index';
	 V_PART2_PUBLIC := REPLACE(  V_PART2_PUBLIC,'$compute_sub_sql$',V_VAR_PRAR );	
	 V_PART_CLASS := REPLACE(  V_PART_CLASS,'$CLASS_TYPE$','parent_code');
	 V_VAR_PRAR2 := ' and upper(parent_level) = ''CATEGORY_TYPE''';
	 V_PART3_PUBLIC := REPLACE(  V_PART_CLASS,'$SCREE_CONDITION$',V_VAR_PRAR2 );
	 V_VAR_PRAR1 := 'CATEGORY';
	 V_VAR_PRAR2 := 't1.price_index';
	 V_VAR_PRAR3 := ' and t.base_period_id = '||v_base_period_id;
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$group_level$',V_VAR_PRAR1 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$field$',V_VAR_PRAR2 );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$period$','= '||v_base_period_id );
	 V_PART5_PUBLIC := REPLACE( V_PART5_PUBLIC,'$rel_condition$','t2.group_code' );
	 V_PART5_PUBLIC := V_PART5_PUBLIC || V_VAR_PRAR3;			
	 END IF;   -- F_PRAR1
	 -- 执行SQL
	 V_EXECUTE_SQL := V_INSERT_PUBLIC || V_PART1_PUBLIC || V_PART2_PUBLIC || V_PART3_PUBLIC || V_PART5_PUBLIC || V_PART4_PUBLIC;
	 EXECUTE V_EXECUTE_SQL;	 
	 --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 2,
   F_CAL_LOG_DESC => '删除：'||F_PRAR1||' 的数据，并插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');		 
	 V_EXCEPTION_FLAG := '3';
	 -- 当F_JUD_CONDITION = 'FORMAL'时,插入到正式表
	 ELSIF UPPER(F_JUD_CONDITION) = 'FORMAL' THEN
	 insert into FIN_DM_OPT_FOI.dm_foi_price_index_t
  (  id,
   year,
   period_id,
   base_period_id,
   group_code,
   group_cn_name,
   group_level,
   parent_code,
   price_index,
   l4_ceg_code,
   l4_ceg_short_cn_name,
   l3_ceg_code,
   l3_ceg_short_cn_name,
   l2_ceg_code,
   l2_ceg_cn_name,
   l4_ceg_cn_name,
   l3_ceg_cn_name,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag,
   version_id,
	 CONTINUITY_TYPE)
  select FIN_DM_OPT_FOI.dm_foi_price_index_s.nextval as id,
         year,
         period_id,
         base_period_id,
         group_code,
         group_cn_name,
         group_level,
         parent_code,
         price_index,
         l4_ceg_code,
         l4_ceg_short_cn_name,
         l3_ceg_code,
         l3_ceg_short_cn_name,
         l2_ceg_code,
         l2_ceg_cn_name,
         l4_ceg_cn_name,
         l3_ceg_cn_name,
         -1,
         current_timestamp,
         -1,
         current_timestamp,
         'N',
         v_version,
				 CONTINUITY_TYPE
    from FIN_DM_OPT_FOI.dm_foi_price_index_tmp1;			
	 --3.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 3,
   F_CAL_LOG_DESC => '删除指数表版本号为：'||v_version||'的数据，并插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => x_success_flag,
   F_ERRBUF => 'SUCCESS');	 
	 END IF;   -- F_JUD_CONDITION	 
	 END IF;   -- F_FLAG	 
	 -- 收集信息
  ANALYSE FIN_DM_OPT_FOI.dm_foi_price_index_tmp1;
  --4.日志结束
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 4,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束');
	return 'SUCCESS';
	EXCEPTION
  WHEN OTHERS THEN
  x_success_flag := 0;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   f_step_num => v_exception_flag,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => x_success_flag, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );	 
	 END; 
	 $$
/

