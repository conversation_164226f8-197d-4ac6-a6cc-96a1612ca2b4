-- Name: f_dm_fcst_version_info_is_running_change; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_version_info_is_running_change(f_industry_flag character varying, x_is_running character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间:2024年8月6日
创建人  :李志勇
背景描述:修改版本信息表中当月生成的最大版本号（年度和月度）的刷数状态，其对应 x_is_running 设置为 Y（正在刷数） 或者N （当前未刷数）
参数描述: x_is_running :刷数目标状态  x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T(版本信息表)
目标表:FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T(版本信息表)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FCST_VERSION_INFO_IS_RUNNING_CHANGE('N')
-- */
--     SELECT * FROM   DM_FCST_ICT_VERSION_INFO_T  WHERE VERSION_TYPE = 'AUTO' AND  DATA_TYPE = 'MONTH' OR  DATA_TYPE = 'ANNUAL' ORDER BY VERSION_ID DESC

DECLARE
    V_SP_NAME             VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_VERSION_INFO_IS_RUNNING_CHANGE'; --存储过程名称
    V_STEP_MUM            BIGINT        := 0; --步骤号
    V_SQL                 TEXT; --SQL逻辑
    V_CNT_MONTH           BIGINT;
    V_CNT_ANNUAL          BIGINT;
    V_IS_RUNNING          VARCHAR(5)    := x_is_running;
    V_FROM_TABLE          varchar(100) ;
    V_MONTH_MAX_VERSION   int8;
    V_ANNUAL_MAX_VERSION  int8;
    V_MONTH_VERSION_TIME  timestamp;
    V_ANNUAL_VERSION_TIME timestamp;

BEGIN
    X_RESULT_STATUS = 'SUCCESS';
--     SELECT  * FROM  DM_FCST_ICT_VERSION_INFO_T ;

    --1、开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_STEP_NUM => V_STEP_MUM,
             F_CAL_LOG_DESC => V_SP_NAME || '开始执行');

    IF UPPER( f_industry_flag ) = 'ICT' THEN
        V_FROM_TABLE := ' FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T ';
    ELSE
           PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_STEP_NUM => V_STEP_MUM,
             F_CAL_LOG_DESC =>  '请指定需要更改的产业，比如IAS或者是ICT');
    END IF;

    /*检查是否有多个版本号*/
    IF x_is_running IS NULL THEN
        V_SQL := 'SELECT COUNT(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''MONTH''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)
          AND del_flag = ''N''';
--         RAISE NOTICE '%',V_SQL;
        EXECUTE IMMEDIATE V_SQL INTO V_CNT_MONTH;

        V_SQL := ' SELECT COUNT(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ANNUAL''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)
          AND del_flag = ''N''';
--         RAISE NOTICE '%',V_SQL;
        EXECUTE IMMEDIATE V_SQL INTO V_CNT_ANNUAL;

        IF V_CNT_MONTH <> 1 OR V_CNT_ANNUAL <> 1 THEN
            x_result_status = 'FAILED';
            --写入日志
            V_STEP_MUM := V_STEP_MUM + 1;
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                    (F_SP_NAME => V_SP_NAME,
                     F_STEP_NUM => V_STEP_MUM,
                     F_CAL_LOG_DESC => 'ERROR:当月版本号存在多个有效版本号或者无版本',
                     F_DML_ROW_COUNT => 0,
                     F_RESULT_STATUS => X_RESULT_STATUS,
                     F_ERRBUF => 'ERROR');
            RETURN 'ERROR:当月版本号存在多个有效版本号或者无版本.';
        END IF;

    ELSE
        /*2、获取当月最大有效版本号*/
        V_SQL := ' SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''MONTH''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7) AND DEL_FLAG = ''N'' ';
--         RAISE NOTICE '%',V_SQL;
        EXECUTE IMMEDIATE V_SQL INTO V_MONTH_MAX_VERSION;

        V_SQL := 'SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ANNUAL''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7) AND DEL_FLAG = ''N'' ';
--         RAISE NOTICE '%',V_SQL;
        EXECUTE IMMEDIATE V_SQL INTO V_ANNUAL_MAX_VERSION;

        V_SQL := ' SELECT MAX(creation_date)
        FROM ' || V_FROM_TABLE || '
        WHERE  DATA_TYPE = ''MONTH'' AND DEL_FLAG = ''N'' ';
--         RAISE NOTICE '%',V_SQL;
        EXECUTE IMMEDIATE V_SQL INTO V_MONTH_VERSION_TIME;

        V_SQL := ' SELECT MAX(creation_date)
        FROM ' || V_FROM_TABLE || '
        WHERE  DATA_TYPE = ''ANNUAL'' AND DEL_FLAG = ''N'' ';
--         RAISE NOTICE '%',V_SQL;
        EXECUTE IMMEDIATE V_SQL INTO V_ANNUAL_VERSION_TIME;

        IF substr(V_MONTH_VERSION_TIME, 1, 7) <> SUBSTR(CURRENT_DATE, 1, 7) OR
           substr(V_ANNUAL_VERSION_TIME, 1, 7) <> SUBSTR(CURRENT_DATE, 1, 7) THEN
            --写入日志
            x_result_status = 'FAILED';
            V_STEP_MUM := V_STEP_MUM + 1;
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                    (F_SP_NAME => V_SP_NAME,
                     F_STEP_NUM => V_STEP_MUM,
                     F_CAL_LOG_DESC => '当前月份暂无版本号生成或者版本号生成不全',
                     F_DML_ROW_COUNT => 0,
                     F_RESULT_STATUS => X_RESULT_STATUS,
                     F_ERRBUF => 'ERROR');
            RETURN 'ERROR:当前月份暂无版本号生成或生成不全.';
        END IF;

        --3、屏蔽/开放当期版本号,取最大且有效
        IF V_IS_RUNNING = 'N' THEN --放开当期版本号
            V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_MONTH_MAX_VERSION || '
				AND DEL_FLAG = ''N''
              AND is_running = ''Y''';
--             RAISE NOTICE '%',V_SQL;
            EXECUTE IMMEDIATE V_SQL;


            V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_ANNUAL_MAX_VERSION || '
			AND DEL_FLAG = ''N''
              AND is_running = ''Y''';
--             RAISE NOTICE '%',V_SQL;
            EXECUTE IMMEDIATE V_SQL;

        ELSE
            IF V_IS_RUNNING = 'Y' THEN --屏蔽当期版本号
                V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_MONTH_MAX_VERSION || '
			AND DEL_FLAG = ''N''
              AND is_running = ''N''';
--                 RAISE NOTICE '%',V_SQL;
                EXECUTE IMMEDIATE V_SQL;

                V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET is_running         = ''' || V_IS_RUNNING || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_ANNUAL_MAX_VERSION || '
			AND DEL_FLAG = ''N''
              AND is_running = ''N''';
--                 RAISE NOTICE '%',V_SQL;
                EXECUTE IMMEDIATE V_SQL;


            END IF;
        END IF;

    END IF;

    --写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_STEP_NUM => V_STEP_MUM,
             F_CAL_LOG_DESC => '版本号状态修改完成。' || 'ICT经管【月度】本月最大版本号: ' || V_MONTH_MAX_VERSION ||
                               '跑数状态修改成: ' || V_IS_RUNNING || ';    ICT经管【年度】本月最大版本号: ' ||
                               V_ANNUAL_MAX_VERSION ||
                               '跑数状态修改成: ' || V_IS_RUNNING,
             F_DML_ROW_COUNT => 0,
             F_RESULT_STATUS => X_RESULT_STATUS,
             F_ERRBUF => 'SUCCESS');

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
                (F_SP_NAME => V_SP_NAME,
                 F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
                 F_RESULT_STATUS => X_RESULT_STATUS,
                 F_ERRBUF => SQLSTATE || ':' || SQLERRM
                );

END

$$
/

