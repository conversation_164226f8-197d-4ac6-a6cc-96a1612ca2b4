-- Name: f_dm_fcst_price_base_cus_mon_rate_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_rate_t(f_custom_id character varying, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 	/*
创建时间：2024-11-12
创建人  ：朱雅欣
背景描述：虚化后月累计同环比表 函数,调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一： F_CUSTOM_ID 组合ID
          参数二:  F_VERSION_ID 版本ID
          参数三:  x_result_status 返回状态 运行状态返回值 ‘1’为成功，‘0’为失败
来源表  ：虚化后月累计指数表      ：FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T
                    
结果表  ：虚化后月累计同环比表  ：FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_RATE_T

事例    ：select fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_rate_t()

*/
DECLARE
  V_SP_NAME    VARCHAR(400) := 'FIN_DM_OPT_FOI.f_dm_fcst_price_base_cus_mon_rate_t';
  V_version_id BIGINT;      --版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  
BEGIN   
  
   IF (F_VERSION_ID IS NULL OR F_VERSION_ID = '')  THEN
SELECT max(VERSION_ID) as VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
	   ;
 ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;
      
	    DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_RATE_T WHERE VERSION_ID = V_VERSION_ID AND CUSTOM_ID = F_CUSTOM_ID ;
		
	  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_RATE_T(
	             VERSION_ID                         /*版本ID*/
                ,PERIOD_YEAR                        /*会计年*/
                ,PERIOD_ID                          /*会计月*/
                ,CUSTOM_ID                          /*虚化组合ID*/
                ,CUSTOM_CN_NAME                     /*虚化组合名称*/
                ,GROUP_CODE                         /*层级编码*/
                ,GROUP_CN_NAME                      /*层级中文名称*/
                ,GROUP_LEVEL                        /*层级描述（LV0:ICT、LV1：LV1层级、LV2：LV2层级、LV3：LV3层级、LV4：LV4层级、SPART：SPART层级）*/
                ,RATE                               /*同环比值*/
                ,RATE_FLAG                          /*同环比标识（YOY：同比，POP：环比）*/
                ,OVERSEA_FLAG                       /*国内海外标识*/
                ,REGION_CODE                        /*地区部编码*/
                ,REGION_CN_NAME                     /*地区部名称*/
                ,REPOFFICE_CODE                     /*代表处编码*/
                ,REPOFFICE_CN_NAME                  /*代表处名称*/
                ,SIGN_TOP_CUST_CATEGORY_CODE        /*签约客户_大T系统部编码*/
                ,SIGN_TOP_CUST_CATEGORY_CN_NAME     /*签约客户_大T系统部名称*/
                ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME   /*签约客户_子网系统部名称*/
                ,VIEW_FLAG                          /*视角标识，用于区分不同视角下的数据*/
                ,PARENT_CODE                        /*父层级编码*/
                ,PARENT_CN_NAME                     /*父层级中文名称*/
				,BG_CODE                            /*BG编码*/
                ,BG_CN_NAME                         /*BG中文名称*/
                ,CREATED_BY                         /*创建人*/
                ,CREATION_DATE                      /*创建时间*/
                ,LAST_UPDATED_BY                    /*修改人*/
                ,LAST_UPDATE_DATE                   /*修改时间*/
                ,DEL_FLAG                           /*删除标识(未删除：N，已删除：Y)*/
	         )  
       with FCST_PRICE_BASE_CUS_MON_COST_IDX_TMP AS(
       select VERSION_ID                           /*版本ID*/
             ,PERIOD_YEAR                          /*会计年*/
             ,PERIOD_ID                            /*会计月*/
             ,CUSTOM_ID                            /*虚化组合ID*/
             ,CUSTOM_CN_NAME                       /*虚化组合名称*/
             ,GROUP_CODE                           /*层级编码*/
             ,GROUP_CN_NAME                        /*层级中文名称*/
             ,GROUP_LEVEL                          /*层级描述（LV0:ICT、LV1：LV1层级、LV2：LV2层级、LV3：LV3层级、LV4：LV4层级、SPART：SPART层级）*/
             ,COST_INDEX                           /*指数值*/
             ,OVERSEA_FLAG                         /*国内海外标识*/
             ,REGION_CODE                          /*地区部编码*/
             ,REGION_CN_NAME                       /*地区部名称*/
             ,REPOFFICE_CODE                       /*代表处编码*/
             ,REPOFFICE_CN_NAME                    /*代表处名称*/
             ,SIGN_TOP_CUST_CATEGORY_CODE          /*签约客户_大T系统部编码*/
             ,SIGN_TOP_CUST_CATEGORY_CN_NAME       /*签约客户_大T系统部名称*/
             ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME     /*签约客户_子网系统部名称*/
             ,VIEW_FLAG                            /*视角标识，用于区分不同视角下的数据*/
             ,PARENT_CODE                          /*父层级编码*/
             ,PARENT_CN_NAME                       /*父层级中文名称*/
             ,BG_CODE                              /*BG编码*/
             ,BG_CN_NAME                           /*BG中文名称*/
	   from FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T
	    where CUSTOM_ID = f_custom_id
        and VERSION_ID = V_VERSION_ID
           )
		   SELECT T1.VERSION_ID
                 ,T1.PERIOD_YEAR
                 ,T1.PERIOD_ID
                 ,T1.CUSTOM_ID
                 ,T1.CUSTOM_CN_NAME
                 ,T1.GROUP_CODE
                 ,T1.GROUP_CN_NAME
                 ,T1.GROUP_LEVEL
                 ,(T1.COST_INDEX/NULLIF(t2.COST_INDEX, 0)  - 1) AS RATE
                 ,'YOY' AS RATE_FLAG
                 ,T1.OVERSEA_FLAG
                 ,T1.REGION_CODE
                 ,T1.REGION_CN_NAME
                 ,T1.REPOFFICE_CODE
                 ,T1.REPOFFICE_CN_NAME
                 ,T1.SIGN_TOP_CUST_CATEGORY_CODE
                 ,T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
                 ,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                 ,T1.VIEW_FLAG
                 ,T1.PARENT_CODE
                 ,T1.PARENT_CN_NAME
				 ,T1.BG_CODE
                 ,T1.BG_CN_NAME
                 ,-1 AS CREATED_BY
	             ,CURRENT_TIMESTAMP AS CREATION_DATE
	             ,-1 AS LAST_UPDATED_BY
	             ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
	             ,'N' AS DEL_FLAG		                 
			FROM FCST_PRICE_BASE_CUS_MON_COST_IDX_TMP      T1
			LEFT JOIN FCST_PRICE_BASE_CUS_MON_COST_IDX_TMP T2
			ON   T1.VERSION_ID  = T2.VERSION_ID                                                             
            and  T1.CUSTOM_ID   = T2.CUSTOM_ID                                            
            and  T1.GROUP_CODE  = T2.GROUP_CODE                                        
            and  T1.GROUP_LEVEL = T2.GROUP_LEVEL  
            and  T1.PARENT_CODE = T2.PARENT_CODE   			
            and  NVL(T1.OVERSEA_FLAG,'SNULL')                     = NVL(T2.OVERSEA_FLAG,'SNULL')                     
            and  NVL(T1.REGION_CODE,'SNULL')                      = NVL(T2.REGION_CODE,'SNULL')                                          
            and  NVL(T1.REPOFFICE_CODE,'SNULL')                   = NVL(T2.REPOFFICE_CODE,'SNULL')                                     
            and  NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')      = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')         
            and  NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL') 
            and  NVL(T1.VIEW_FLAG,'SNULL')                        = NVL(T2.VIEW_FLAG,'SNULL')                                                          
            and  NVL(T1.BG_CODE,'SNULL')                          = NVL(T2.BG_CODE,'SNULL')  
            AND  t2.PERIOD_ID=TO_CHAR(to_date(t1.PERIOD_ID,'yyyyMM')- interval '12months' ,'yyyyMM')::numeric	
              UNION ALL		
           SELECT T1.VERSION_ID
                 ,T1.PERIOD_YEAR
                 ,T1.PERIOD_ID
                 ,T1.CUSTOM_ID
                 ,T1.CUSTOM_CN_NAME
                 ,T1.GROUP_CODE
                 ,T1.GROUP_CN_NAME
                 ,T1.GROUP_LEVEL
                 ,(T1.COST_INDEX/NULLIF(t2.COST_INDEX, 0)  - 1) AS RATE
                 ,'POP' AS RATE_FLAG
                 ,T1.OVERSEA_FLAG
                 ,T1.REGION_CODE
                 ,T1.REGION_CN_NAME
                 ,T1.REPOFFICE_CODE
                 ,T1.REPOFFICE_CN_NAME
                 ,T1.SIGN_TOP_CUST_CATEGORY_CODE
                 ,T1.SIGN_TOP_CUST_CATEGORY_CN_NAME
                 ,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                 ,T1.VIEW_FLAG
                 ,T1.PARENT_CODE
                 ,T1.PARENT_CN_NAME
				 ,T1.BG_CODE
                 ,T1.BG_CN_NAME
                 ,-1 AS CREATED_BY
	             ,CURRENT_TIMESTAMP AS CREATION_DATE
	             ,-1 AS LAST_UPDATED_BY
	             ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
	             ,'N' AS DEL_FLAG		                   
			FROM FCST_PRICE_BASE_CUS_MON_COST_IDX_TMP      T1
			LEFT JOIN FCST_PRICE_BASE_CUS_MON_COST_IDX_TMP T2
			ON   T1.VERSION_ID  = T2.VERSION_ID                                                               
            and  T1.CUSTOM_ID   = T2.CUSTOM_ID                                            
            and  T1.GROUP_CODE  = T2.GROUP_CODE                                         
            and  T1.GROUP_LEVEL = T2.GROUP_LEVEL  
            and  T1.PARENT_CODE                      = T2.PARENT_CODE       			
            and  NVL(T1.OVERSEA_FLAG,'SNULL')                     = NVL(T2.OVERSEA_FLAG,'SNULL')                     
            and  NVL(T1.REGION_CODE,'SNULL')                      = NVL(T2.REGION_CODE,'SNULL')                                          
            and  NVL(T1.REPOFFICE_CODE,'SNULL')                   = NVL(T2.REPOFFICE_CODE,'SNULL')                                     
            and  NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')      = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')         
            and  NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL') 
            and  NVL(T1.VIEW_FLAG,'SNULL')                        = NVL(T2.VIEW_FLAG,'SNULL')                                                      
            and  NVL(T1.BG_CODE,'SNULL')                          = NVL(T2.BG_CODE,'SNULL')  
            AND  t2.PERIOD_ID=TO_CHAR(to_date(t1.PERIOD_ID,'yyyyMM')- interval '1months'  ,'yyyyMM')::numeric			  
               ;
     

	     --写入日志
        V_STEP_MUM := V_STEP_MUM + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '虚化后月累计同环比表 计算完成,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_FORMULA_SQL_TXT => NULL,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS'); 
	  
	  
	     RETURN 'SUCCESS';
 
          --收集统计信息
        ANALYSE FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_RATE_T;


        EXCEPTION
          WHEN OTHERS THEN
          X_RESULT_STATUS := '0';
          
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
          (F_SP_NAME => V_SP_NAME, 
           F_STEP_NUM => V_STEP_MUM,
           F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_MUM||'步'||'运行失败,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID, 
           F_RESULT_STATUS => X_RESULT_STATUS, 
           F_ERRBUF => SQLSTATE||':'||SQLERRM
           );




end;
$$
/

