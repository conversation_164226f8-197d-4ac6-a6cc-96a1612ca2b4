-- Name: f_dm_fcst_ict_mon_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_cost_idx_t(f_cost_type character varying, f_idx_type character varying, f_granularity_type character varying, f_keystr text, f_version_id integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************
--2024年9月24日14点49分 新增软硬件标识
--指数&降成本指数

参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二: F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录 指数类型为降成本指数时本入参为空
		参数三: F_KEYSTR	密钥
		参数四: F_VERSION_ID 版本号
		参数五: X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败
		参数六: F_IDX_TYPE 指数类型 ('COST':月度指数 ,'RED_COST':降成本指数 ,'YTD' :月累积指数 )

----------来源表
------权重表
--月度权重表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_WEIGHT_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_WEIGHT_T		--PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_WEIGHT_T		--PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_WEIGHT_T		--STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_WEIGHT_T		--STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_WEIGHT_T		--STD PROD

------指数中间表
--降成本指数中间表
重量级团队目录-PSP:		DM_FCST_ICT_PSP_MON_MID_COST_RED_IDX_T	--PSP
重量级团队目录-STD:		DM_FCST_ICT_STD_MON_MID_COST_RED_IDX_T	--STD

--降成本目标值表
DM_FCST_ICT_COST_RED_OBJ_T

--月度指数中间表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_MID_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_MID_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_MID_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_MID_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_MID_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_MID_COST_IDX_T     --STD PROD


--月累积指数中间表
重量级团队目录		DM_FCST_ICT_PSP_IRB_YTD_MID_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_YTD_MID_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_YTD_MID_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_YTD_MID_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_YTD_MID_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_YTD_MID_COST_IDX_T     --STD PROD

----------目标表
--指数表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_COST_IDX_T     --STD PROD



--月累积指数表
重量级团队目录		DM_FCST_ICT_PSP_IRB_YTD_COST_IDX_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_YTD_COST_IDX_T    --PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_YTD_COST_IDX_T     --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_YTD_COST_IDX_T      --STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_YTD_COST_IDX_T    --STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_YTD_COST_IDX_T     --STD PROD


--降成本指数表
重量级团队目录-PSP	DM_FCST_ICT_PSP_MON_COST_RED_IDX_T
重量级团队目录-STD	DM_FCST_ICT_STD_MON_COST_RED_IDX_T


--事例
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_COST_IDX_T('PSP','COST','IRB','',''); 
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_COST_IDX_T('PSP','COST_RED','','',''); 
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_COST_IDX_T';
  V_VERSION                VARCHAR(10);
  V_EXCEPTION_FLAG         VARCHAR(2);
  V_FROM_COST_WEIGHT_TABLE VARCHAR(200);
  V_FROM_COST_MID_TABLE    VARCHAR(200);
  V_TO_COST_IDX_TABLE      VARCHAR(200);
  V_FROM_RED_WEIGHT_TABLE  VARCHAR(200);
  V_FROM_RED_MID_TABLE     VARCHAR(200);
  V_TO_RED_IDX_TABLE       VARCHAR(200);
  V_YEAR1                  VARCHAR(50);
  V_BASE_PERIOD_ID         INT ;
  V_OTHER_DIM_PART         TEXT;
  V_SQL_OTHER_DIM_PART     TEXT;
  V_JOIN_OTHER_DIM_PART    TEXT;
  V_DIMENSION_PART         TEXT;
  V_SQL_DIMENSION_PART     TEXT;
  V_JOIN_DIMENSION_CODE    TEXT;
  V_PUBLIC_PBI_PART        TEXT;
  V_SQL_PUBLIC_PBI_PART    TEXT;
  V_PROD_PART              TEXT;
  V_JOIN_PROD_CODE         TEXT;
  V_CHILD_LEVEL            TEXT;
  V_SQL_PROD_PART          TEXT;
  V_GROUP_LEVEL            TEXT;
  V_SQL_PARENT_PART        TEXT;
  V_VIEW_FLAG              TEXT;
  V_SQL                    TEXT;

BEGIN   

RAISE NOTICE '取版本号';

--判断基期
IF     MONTH(CURRENT_DATE)   = 1 then                                                                                                                                  
	V_BASE_PERIOD_ID := TO_NUMBER((YEAR(CURRENT_DATE) - 2) || '01'); --基期会计期,如果当前是1月则取两年前的1月为基期
ELSIF  MONTH(CURRENT_DATE)   != 1 then 
	V_BASE_PERIOD_ID := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期,如果当前不是1月则取去年前的1月为基期

END IF;

--版本表
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;

--表名定义
V_FROM_COST_WEIGHT_TABLE:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_WEIGHT_T';

IF F_IDX_TYPE = 'COST' THEN 
	V_FROM_COST_MID_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_MID_COST_IDX_T';
	V_TO_COST_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_COST_IDX_T';
	
ELSIF F_IDX_TYPE = 'YTD' THEN 
	V_FROM_COST_MID_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_YTD_MID_COST_IDX_T';
	V_TO_COST_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_YTD_COST_IDX_T';
	
END IF;

V_FROM_RED_WEIGHT_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_IRB_MON_WEIGHT_T';
V_FROM_RED_MID_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_MON_MID_COST_RED_IDX_T';
V_TO_RED_IDX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_MON_COST_RED_IDX_T';


--判断V_YEAR
IF     MONTH(CURRENT_DATE)   = 1 then                                                                                                                                  
	V_YEAR1 := TO_CHAR((YEAR(CURRENT_DATE) - 2) || '-' || (YEAR(CURRENT_DATE)-1));
ELSIF  MONTH(CURRENT_DATE)   != 1 then 
	V_YEAR1 := TO_CHAR((YEAR(CURRENT_DATE) - 1) || '-' || YEAR(CURRENT_DATE));

END IF;



  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
		';
		
  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')		
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';
					
  V_PUBLIC_PBI_PART:='LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,
                      LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
  '	;
  
  V_SQL_PUBLIC_PBI_PART:='T1.LV0_CODE,T1.LV1_CODE,T1.LV2_CODE,T1.LV3_CODE,T1.LV4_CODE,
						  T1.LV0_CN_NAME,T1.LV1_CN_NAME,T1.LV2_CN_NAME,T1.LV3_CN_NAME,T1.LV4_CN_NAME,
  '	;
					
--字段判断 PBI层级字段定义
--不同目录维度字段判断
  IF F_GRANULARITY_TYPE = 'IRB' OR (F_GRANULARITY_TYPE IS NULL AND F_IDX_TYPE = 'RED_COST') THEN
	  
	V_PROD_PART:='
		PROD_RND_TEAM_CODE,
		PROD_RD_TEAM_CN_NAME,
	';
	
	V_JOIN_PROD_CODE := '
	AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
	';

  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
	  
	V_PROD_PART:='
		INDUSTRY_CATG_CODE,
		INDUSTRY_CATG_CN_NAME,
	';
	
	V_JOIN_PROD_CODE := '
	AND T1.INDUSTRY_CATG_CODE = T2.INDUSTRY_CATG_CODE
	';

  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
	  
	V_PROD_PART:='
		PROD_LIST_CODE,
		PROD_LIST_CN_NAME,
	';
	
	V_JOIN_PROD_CODE := '
	AND T1.PROD_LIST_CODE = T2.PROD_LIST_CODE
	';
  
  END IF;

--指数循环计算
  IF F_GRANULARITY_TYPE IS NOT NULL AND F_IDX_TYPE IN ( 'COST','YTD') THEN
  --月度指数计算
    FOR V_GROUP_NUM IN 1 .. 7 LOOP
    
      IF V_GROUP_NUM = 1 THEN
        --路径二:量纲子类
		RAISE NOTICE '路径二:量纲子类';
        V_DIMENSION_PART      := '
          DIMENSION_CODE,
          DIMENSION_CN_NAME,
          DIMENSION_SUBCATEGORY_CODE,
          DIMENSION_SUBCATEGORY_CN_NAME,
          ';
        V_SQL_DIMENSION_PART  := '
          T1.DIMENSION_CODE,
          T1.DIMENSION_CN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,
          T1.DIMENSION_SUBCATEGORY_CN_NAME,
          ';
        V_JOIN_DIMENSION_CODE := '
          AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')   
          AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
          ';
      
        V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,
                      LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
		';
      
        V_SQL_PUBLIC_PBI_PART := 'T1.LV0_CODE,T1.LV1_CODE,T1.LV2_CODE,T1.LV3_CODE,T1.LV4_CODE,
              T1.LV0_CN_NAME,T1.LV1_CN_NAME,T1.LV2_CN_NAME,T1.LV3_CN_NAME,T1.LV4_CN_NAME,
		';
        V_CHILD_LEVEL         := '''SUB_DETAIL''';
        V_SQL_PROD_PART       := 'T1.LV4_CODE AS PROD_CODE,T1.LV4_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''SUBCATEGORY''';
        V_SQL_PARENT_PART     := 'T1.DIMENSION_CODE AS PARENT_CODE , T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
        V_VIEW_FLAG           := '''DIMENSION''';
      
      ELSIF V_GROUP_NUM = 2 THEN
        --路径二:量纲
		RAISE NOTICE '路径二:量纲';
        V_DIMENSION_PART      := '
          DIMENSION_CODE,
          DIMENSION_CN_NAME,
          ';
        V_SQL_DIMENSION_PART  := '
          T1.DIMENSION_CODE,
          T1.DIMENSION_CN_NAME,
          ';
        V_JOIN_DIMENSION_CODE := '
          AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')   
          ';
        V_CHILD_LEVEL         := '''SUBCATEGORY''';
        V_GROUP_LEVEL         := '''DIMENSION''';
        V_SQL_PARENT_PART     := 'T1.DIMENSION_CODE AS PARENT_CODE , T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 3 THEN
        --路径一:LV4
		RAISE NOTICE '路径一:LV4';
        V_DIMENSION_PART      := '';
        V_SQL_DIMENSION_PART  := '';
        V_JOIN_DIMENSION_CODE := '';
      
        V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,
							  LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
		  ';
      
        V_SQL_PUBLIC_PBI_PART := 'T1.LV0_CODE,T1.LV1_CODE,T1.LV2_CODE,T1.LV3_CODE,T1.LV4_CODE,
								  T1.LV0_CN_NAME,T1.LV1_CN_NAME,T1.LV2_CN_NAME,T1.LV3_CN_NAME,T1.LV4_CN_NAME,
					  ';
        V_CHILD_LEVEL         := '''SPART''';
        V_SQL_PROD_PART       := 'T1.LV4_CODE AS PROD_CODE,T1.LV4_CN_NAME AS PROD_CN_NAME,'; --
        V_GROUP_LEVEL         := '''LV4''';
        V_SQL_PARENT_PART     := 'T1.LV3_CODE AS PARENT_CODE , T1.LV3_CN_NAME AS PARENT_CN_NAME,';
        V_VIEW_FLAG           := '''PROD_SPART''';
      
      ELSIF V_GROUP_NUM = 4 THEN
        --路径一:LV3
		RAISE NOTICE '路径一:LV3';
        V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,
							  LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,
					';
      
        V_SQL_PUBLIC_PBI_PART := 'T1.LV0_CODE,T1.LV1_CODE,T1.LV2_CODE,T1.LV3_CODE,
								  T1.LV0_CN_NAME,T1.LV1_CN_NAME,T1.LV2_CN_NAME,T1.LV3_CN_NAME,
								';
        V_CHILD_LEVEL         := '''LV4''';
        V_SQL_PROD_PART       := 'T1.LV3_CODE AS PROD_CODE,T1.LV3_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV3''';
        V_SQL_PARENT_PART     := 'T1.LV2_CODE AS PARENT_CODE , T1.LV2_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 5 THEN
        --路径一:LV2
		RAISE NOTICE '路径一:LV2';
        V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,
							  LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,
		  ';
      
        V_SQL_PUBLIC_PBI_PART := 'T1.LV0_CODE,T1.LV1_CODE,T1.LV2_CODE,
								  T1.LV0_CN_NAME,T1.LV1_CN_NAME,T1.LV2_CN_NAME,
					  ';
        V_CHILD_LEVEL         := '''LV3''';
        V_SQL_PROD_PART       := 'T1.LV2_CODE AS PROD_CODE,T1.LV2_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV2''';
        V_SQL_PARENT_PART     := 'T1.LV1_CODE AS PARENT_CODE , T1.LV1_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 6 THEN
        --路径一:LV1
		RAISE NOTICE '路径一:LV1';
        V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,
							  LV0_CN_NAME,LV1_CN_NAME,
		  ';
        V_SQL_PUBLIC_PBI_PART := 'T1.LV0_CODE,T1.LV1_CODE,
								  T1.LV0_CN_NAME,T1.LV1_CN_NAME,
					  ';
        V_CHILD_LEVEL         := '''LV2''';
        V_SQL_PROD_PART       := 'T1.LV1_CODE AS PROD_CODE,T1.LV1_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV1''';
        V_SQL_PARENT_PART     := 'T1.LV0_CODE AS PARENT_CODE , T1.LV0_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 7 THEN
        --路径一:LV0
		RAISE NOTICE '路径一:LV0';
        V_PUBLIC_PBI_PART := 'LV0_CODE,
							  LV0_CN_NAME,
		  ';
      
        V_SQL_PUBLIC_PBI_PART := 'T1.LV0_CODE,
								  T1.LV0_CN_NAME,
					  ';
					  
        V_CHILD_LEVEL         := '''LV1''';
        V_SQL_PROD_PART       := 'T1.LV0_CODE AS PROD_CODE,T1.LV0_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV0''';
        V_SQL_PARENT_PART     := ' '''' AS PARENT_CODE , '''' AS PARENT_CN_NAME,';
      
      END IF;
  
	  V_SQL:='
		WITH BASE_INDEX AS
		 (
		  --子集指数
		  SELECT BASE_PERIOD_ID,
				 PERIOD_ID,
				 PERIOD_YEAR,
				 '||V_PROD_PART||V_PUBLIC_PBI_PART||V_DIMENSION_PART||' 
				 GROUP_CODE,
				 COST_INDEX,
				 GROUP_LEVEL,
				 PARENT_CODE,
				 PARENT_CN_NAME,
				 VIEW_FLAG,
				 '||V_OTHER_DIM_PART||' 
				 MAIN_FLAG,
				 SOFTWARE_MARK,		--202410版本 新增软硬件标识
				 CODE_ATTRIBUTES
			FROM '||V_FROM_COST_MID_TABLE||'
		   WHERE GROUP_LEVEL = '||V_CHILD_LEVEL||'
			 AND VERSION_ID = '||V_VERSION||'
			 AND VIEW_FLAG = '||V_VIEW_FLAG||' ),
		
		BASE_WEIGHT AS
		 (
		  --子集权重
		  SELECT PERIOD_YEAR,
				 '||V_PROD_PART||V_DIMENSION_PART||' 
				 WEIGHT_RATE,
				 GROUP_CODE,
				 GROUP_LEVEL,
				 PARENT_CODE,
				 VIEW_FLAG,
				 '||V_OTHER_DIM_PART||' 
				 MAIN_FLAG,
				 SOFTWARE_MARK,		--202410版本 新增软硬件标识
				 CODE_ATTRIBUTES
			FROM '||V_FROM_COST_WEIGHT_TABLE||'
		   WHERE GROUP_LEVEL = '||V_CHILD_LEVEL||'
			 AND VERSION_ID = '||V_VERSION||'
			 AND PERIOD_YEAR = '''||V_YEAR1||'''
			 AND VIEW_FLAG = '||V_VIEW_FLAG||' )
		
		INSERT INTO '||V_FROM_COST_MID_TABLE||'
		  (VERSION_ID,
		   PERIOD_ID,
		   PERIOD_YEAR,
		   '||V_PROD_PART||V_PUBLIC_PBI_PART||V_DIMENSION_PART||' 
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
		   COST_INDEX,
		   PARENT_CODE,
		   PARENT_CN_NAME,
		   VIEW_FLAG,
		   '||V_OTHER_DIM_PART||' 
		   MAIN_FLAG,
		   CODE_ATTRIBUTES,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
		   CREATED_BY,
		   CREATION_DATE,
		   LAST_UPDATED_BY,
		   LAST_UPDATE_DATE,
		   DEL_FLAG)
		SELECT '||V_VERSION||' AS VERSION_ID,
			   T1.PERIOD_ID,
			   T1.PERIOD_YEAR,
			   '||V_SQL_PROD_PART||V_SQL_PUBLIC_PBI_PART||V_SQL_DIMENSION_PART||' 
			   T1.PARENT_CODE AS GROUP_CODE,
			   T1.PARENT_CN_NAME AS GROUP_CN_NAME,
			   '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
			   SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
			   '||V_SQL_PARENT_PART||' 
			   T1.VIEW_FLAG,
			   '||V_SQL_OTHER_DIM_PART||' 
			   T1.MAIN_FLAG,
			   T1.CODE_ATTRIBUTES,
			   T1.SOFTWARE_MARK,		--202410版本 新增软硬件标识
			   -1 AS CREATED_BY,
			   CURRENT_TIMESTAMP AS CREATION_DATE,
			   -1 AS LAST_UPDATED_BY,
			   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			   ''N'' AS DEL_FLAG
		  FROM BASE_INDEX T1
		  LEFT JOIN BASE_WEIGHT T2
			ON T1.GROUP_LEVEL = T2.GROUP_LEVEL
		   AND T1.GROUP_CODE = T2.GROUP_CODE
		   AND T1.PARENT_CODE = T2.PARENT_CODE
		   AND T1.VIEW_FLAG = T2.VIEW_FLAG
		   AND NVL(T1.MAIN_FLAG, ''MF'') = NVL(T2.MAIN_FLAG, ''MF'')
		   AND NVL(T1.SOFTWARE_MARK,''SW'') = NVL(T2.SOFTWARE_MARK,''SW'') --202410版本 新增软硬件标识
		   AND NVL(T1.CODE_ATTRIBUTES, ''CA'') =
			   NVL(T2.CODE_ATTRIBUTES, ''CA'')
			'||V_JOIN_DIMENSION_CODE||V_JOIN_OTHER_DIM_PART||V_JOIN_PROD_CODE||'
		 GROUP BY T1.PERIOD_ID,
				  '||V_SQL_PUBLIC_PBI_PART||V_SQL_DIMENSION_PART||' 
				  T1.VIEW_FLAG,
				  T1.PERIOD_YEAR,
				  T1.PARENT_CODE,
				  T1.PARENT_CN_NAME,
				  '||V_SQL_OTHER_DIM_PART||' 
				  T1.MAIN_FLAG,
				  T1.SOFTWARE_MARK,		--202410版本 新增软硬件标识
				  T1.CODE_ATTRIBUTES;
				  
		';
		DBMS_OUTPUT.PUT_LINE(V_SQL);
		EXECUTE V_SQL;
			 
   --写入日志
 
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_CAL_LOG_DESC => '第'||V_GROUP_NUM||'次循环开始'||V_GROUP_LEVEL||'层级指数收敛完成',
  F_FORMULA_SQL_TXT => V_SQL,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_ERRBUF => 'SUCCESS');  
			 
    END LOOP;


  ELSIF F_GRANULARITY_TYPE IS NULL AND F_IDX_TYPE = 'RED_COST' THEN
  --降成本指数计算
    FOR V_GROUP_NUM IN 1 .. 7 LOOP
    
      IF V_GROUP_NUM = 1 THEN
        --路径二:量纲子类
		RAISE NOTICE '路径二:量纲子类';
        V_DIMENSION_PART      := '
          DIMENSION_CODE,
          DIMENSION_CN_NAME,
          DIMENSION_SUBCATEGORY_CODE,
          DIMENSION_SUBCATEGORY_CN_NAME,
          ';
        V_SQL_DIMENSION_PART  := '
          T1.DIMENSION_CODE,
          T1.DIMENSION_CN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,
          T1.DIMENSION_SUBCATEGORY_CN_NAME,
          ';
        V_JOIN_DIMENSION_CODE := '
          AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')   
          AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
          ';
		V_PUBLIC_PBI_PART:='
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV2_PROD_RND_TEAM_CODE,
					LV3_PROD_RND_TEAM_CODE,
					LV4_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					LV1_PROD_RD_TEAM_CN_NAME,
					LV2_PROD_RD_TEAM_CN_NAME,
					LV3_PROD_RD_TEAM_CN_NAME,
					LV4_PROD_RD_TEAM_CN_NAME,
					';
					
		V_SQL_PUBLIC_PBI_PART:='
					T1.LV0_PROD_RND_TEAM_CODE,
					T1.LV1_PROD_RND_TEAM_CODE,
					T1.LV2_PROD_RND_TEAM_CODE,
					T1.LV3_PROD_RND_TEAM_CODE,
					T1.LV4_PROD_RND_TEAM_CODE,
					T1.LV0_PROD_RD_TEAM_CN_NAME,
					T1.LV1_PROD_RD_TEAM_CN_NAME,
					T1.LV2_PROD_RD_TEAM_CN_NAME,
					T1.LV3_PROD_RD_TEAM_CN_NAME,
					T1.LV4_PROD_RD_TEAM_CN_NAME,
					';
        V_CHILD_LEVEL         := '''SUB_DETAIL''';
        V_SQL_PROD_PART       := 'T1.LV4_PROD_RND_TEAM_CODE AS PROD_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''SUBCATEGORY''';
        V_SQL_PARENT_PART     := 'T1.DIMENSION_CODE AS PARENT_CODE , T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
        V_VIEW_FLAG           := '''DIMENSION''';
      
      ELSIF V_GROUP_NUM = 2 THEN
        --路径二:量纲
		RAISE NOTICE '路径二:量纲';
        V_DIMENSION_PART      := '
          DIMENSION_CODE,
          DIMENSION_CN_NAME,
          ';
        V_SQL_DIMENSION_PART  := '
          T1.DIMENSION_CODE,
          T1.DIMENSION_CN_NAME,
          ';
        V_JOIN_DIMENSION_CODE := '
          AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')   
          ';
        V_CHILD_LEVEL         := '''SUBCATEGORY''';
        V_GROUP_LEVEL         := '''DIMENSION''';
        V_SQL_PARENT_PART     := 'T1.DIMENSION_CODE AS PARENT_CODE , T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 3 THEN
        --路径一:LV4
		RAISE NOTICE '路径一:LV4';
        V_DIMENSION_PART      := '';
        V_SQL_DIMENSION_PART  := '';
        V_JOIN_DIMENSION_CODE := '';
		
        V_CHILD_LEVEL         := '''SPART''';
        V_GROUP_LEVEL         := '''LV4''';
        V_SQL_PARENT_PART     := 'T1.LV3_PROD_RND_TEAM_CODE AS PARENT_CODE , T1.LV3_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
        V_VIEW_FLAG           := '''PROD_SPART''';
      
      ELSIF V_GROUP_NUM = 4 THEN
        --路径一:LV3
		RAISE NOTICE '路径一:LV3';
		V_PUBLIC_PBI_PART:='
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV2_PROD_RND_TEAM_CODE,
					LV3_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					LV1_PROD_RD_TEAM_CN_NAME,
					LV2_PROD_RD_TEAM_CN_NAME,
					LV3_PROD_RD_TEAM_CN_NAME,
					';
					
		V_SQL_PUBLIC_PBI_PART:='
					T1.LV0_PROD_RND_TEAM_CODE,
					T1.LV1_PROD_RND_TEAM_CODE,
					T1.LV2_PROD_RND_TEAM_CODE,
					T1.LV3_PROD_RND_TEAM_CODE,
					T1.LV0_PROD_RD_TEAM_CN_NAME,
					T1.LV1_PROD_RD_TEAM_CN_NAME,
					T1.LV2_PROD_RD_TEAM_CN_NAME,
					T1.LV3_PROD_RD_TEAM_CN_NAME,
					';
					
        V_CHILD_LEVEL         := '''LV4''';
        V_SQL_PROD_PART       := 'T1.LV3_PROD_RND_TEAM_CODE AS PROD_CODE,T1.LV3_PROD_RD_TEAM_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV3''';
        V_SQL_PARENT_PART     := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE , T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 5 THEN
        --路径一:LV2
		RAISE NOTICE '路径一:LV2';
		V_PUBLIC_PBI_PART:='
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV2_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					LV1_PROD_RD_TEAM_CN_NAME,
					LV2_PROD_RD_TEAM_CN_NAME,
					';
					
		V_SQL_PUBLIC_PBI_PART:='
					T1.LV0_PROD_RND_TEAM_CODE,
					T1.LV1_PROD_RND_TEAM_CODE,
					T1.LV2_PROD_RND_TEAM_CODE,
					T1.LV0_PROD_RD_TEAM_CN_NAME,
					T1.LV1_PROD_RD_TEAM_CN_NAME,
					T1.LV2_PROD_RD_TEAM_CN_NAME,
					';
        V_CHILD_LEVEL         := '''LV3''';
        V_SQL_PROD_PART       := 'T1.LV2_PROD_RND_TEAM_CODE AS PROD_CODE,T1.LV2_PROD_RD_TEAM_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV2''';
        V_SQL_PARENT_PART     := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE , T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 6 THEN
        --路径一:LV1
		RAISE NOTICE '路径一:LV1';
		V_PUBLIC_PBI_PART:='
					LV0_PROD_RND_TEAM_CODE,
					LV1_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					LV1_PROD_RD_TEAM_CN_NAME,
					';
		V_SQL_PUBLIC_PBI_PART:='
					T1.LV0_PROD_RND_TEAM_CODE,
					T1.LV1_PROD_RND_TEAM_CODE,
					T1.LV0_PROD_RD_TEAM_CN_NAME,
					T1.LV1_PROD_RD_TEAM_CN_NAME,
					';
        V_CHILD_LEVEL         := '''LV2''';
        V_SQL_PROD_PART       := 'T1.LV1_PROD_RND_TEAM_CODE AS PROD_CODE,T1.LV1_PROD_RD_TEAM_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV1''';
        V_SQL_PARENT_PART     := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE , T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      
      ELSIF V_GROUP_NUM = 7 THEN
        --路径一:LV0
		RAISE NOTICE '路径一:LV0';
		V_PUBLIC_PBI_PART:='
					LV0_PROD_RND_TEAM_CODE,
					LV0_PROD_RD_TEAM_CN_NAME,
					';
		V_SQL_PUBLIC_PBI_PART:='
					T1.LV0_PROD_RND_TEAM_CODE,
					T1.LV0_PROD_RD_TEAM_CN_NAME,
					';
					  
        V_CHILD_LEVEL         := '''LV1''';
        V_SQL_PROD_PART       := 'T1.LV0_PROD_RND_TEAM_CODE AS PROD_CODE,T1.LV0_PROD_RD_TEAM_CN_NAME AS PROD_CN_NAME,';
        V_GROUP_LEVEL         := '''LV0''';
        V_SQL_PARENT_PART     := ' '''' AS PARENT_CODE , '''' AS PARENT_CN_NAME,';
      
      END IF;
  
	  V_SQL:='
		WITH BASE_INDEX AS
		 (
		  --子集指数
		  SELECT PERIOD_ID,
				 PERIOD_YEAR,
				 '||V_PROD_PART||V_PUBLIC_PBI_PART||V_DIMENSION_PART||' 
				 GROUP_CODE,
				 COST_INDEX,
				 YTD_COST_INDEX,
				 COST_REDUCTION_RATE,
				 GROUP_LEVEL,
				 PARENT_CODE,
				 PARENT_CN_NAME,
				 VIEW_FLAG,
				 '||V_OTHER_DIM_PART||' 
				 MAIN_FLAG,
				 SOFTWARE_MARK,		--202410版本 新增软硬件标识
				 CODE_ATTRIBUTES
			FROM '||V_FROM_RED_MID_TABLE||'
		   WHERE GROUP_LEVEL = '||V_CHILD_LEVEL||'
			 AND VERSION_ID = '||V_VERSION||'
			 AND VIEW_FLAG = '||V_VIEW_FLAG||' ),
		
		BASE_WEIGHT AS
		 (
		  --子集权重
		  SELECT PERIOD_YEAR,
				 '||V_PROD_PART||V_DIMENSION_PART||' 
				 GROUP_CODE,
				 GROUP_LEVEL,
				 PARENT_CODE,
				 WEIGHT_RATE,
				 VIEW_FLAG,
				 '||V_OTHER_DIM_PART||' 
				 MAIN_FLAG,
				 SOFTWARE_MARK,		--202410版本 新增软硬件标识
				 CODE_ATTRIBUTES
			FROM '||V_FROM_RED_WEIGHT_TABLE||'
		   WHERE GROUP_LEVEL = '||V_CHILD_LEVEL||'
			 AND VERSION_ID = '||V_VERSION||'
			 AND VIEW_FLAG = '||V_VIEW_FLAG||' )
		
		INSERT INTO '||V_FROM_RED_MID_TABLE||'
		  (VERSION_ID,
		   PERIOD_ID,
		   PERIOD_YEAR,
		   '||V_PROD_PART||V_PUBLIC_PBI_PART||V_DIMENSION_PART||' 
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
		   COST_INDEX,
		   YTD_COST_INDEX,
		   COST_REDUCTION_RATE,
		   PARENT_CODE,
		   PARENT_CN_NAME,
		   VIEW_FLAG,
		   '||V_OTHER_DIM_PART||' 
		   MAIN_FLAG,
		   CODE_ATTRIBUTES,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
		   CREATED_BY,
		   CREATION_DATE,
		   LAST_UPDATED_BY,
		   LAST_UPDATE_DATE,
		   DEL_FLAG)
		SELECT '||V_VERSION||' AS VERSION_ID,
			   T1.PERIOD_ID,
			   T1.PERIOD_YEAR,
			   '||V_SQL_PROD_PART||V_SQL_PUBLIC_PBI_PART||V_SQL_DIMENSION_PART||' 
			   T1.PARENT_CODE AS GROUP_CODE,
			   T1.PARENT_CN_NAME AS GROUP_CN_NAME,
			   '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
			   SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
			   SUM(T1.YTD_COST_INDEX* T2.WEIGHT_RATE) AS YTD_COST_INDEX,
			   T1.COST_REDUCTION_RATE,
			   '||V_SQL_PARENT_PART||' 
			   T1.VIEW_FLAG,
			   '||V_SQL_OTHER_DIM_PART||' 
			   T1.MAIN_FLAG,
			   T1.CODE_ATTRIBUTES,
			   T1.SOFTWARE_MARK,		--202410版本 新增软硬件标识
			   -1 AS CREATED_BY,
			   CURRENT_TIMESTAMP AS CREATION_DATE,
			   -1 AS LAST_UPDATED_BY,
			   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			   ''N'' AS DEL_FLAG
		  FROM BASE_INDEX T1
		  LEFT JOIN BASE_WEIGHT T2
			ON T1.GROUP_LEVEL = T2.GROUP_LEVEL
		   AND T1.GROUP_CODE = T2.GROUP_CODE
		   AND T1.PARENT_CODE = T2.PARENT_CODE
		   AND T1.VIEW_FLAG = T2.VIEW_FLAG
		   AND T1.PERIOD_YEAR = SUBSTR(T2.PERIOD_YEAR,-4,4)
		   AND NVL(T1.MAIN_FLAG, ''MF'') = NVL(T2.MAIN_FLAG, ''MF'')
		   AND NVL(T1.SOFTWARE_MARK,''SW'') = NVL(T2.SOFTWARE_MARK,''SW'') --202410版本 新增软硬件标识
		   AND NVL(T1.CODE_ATTRIBUTES, ''CA'') =
			   NVL(T2.CODE_ATTRIBUTES, ''CA'')
			'||V_JOIN_DIMENSION_CODE||V_JOIN_OTHER_DIM_PART||V_JOIN_PROD_CODE||'
		 GROUP BY T1.PERIOD_ID,
				  '||V_SQL_PUBLIC_PBI_PART||V_SQL_DIMENSION_PART||' 
				  T1.VIEW_FLAG,
				  T1.PERIOD_YEAR,
				  T1.COST_REDUCTION_RATE,
				  T1.PARENT_CODE,
				  T1.PARENT_CN_NAME,
				  '||V_SQL_OTHER_DIM_PART||' 
				  T1.MAIN_FLAG,
				  T1.SOFTWARE_MARK,		--202410版本 新增软硬件标识
				  T1.CODE_ATTRIBUTES;
				  
		';
		DBMS_OUTPUT.PUT_LINE(V_SQL);
		EXECUTE V_SQL;
			  
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_CAL_LOG_DESC => '第'||V_GROUP_NUM||'次循环开始'||V_GROUP_LEVEL||'层级指数收敛完成',
	  F_FORMULA_SQL_TXT => V_SQL,
	  F_RESULT_STATUS => X_RESULT_STATUS,
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_ERRBUF => 'SUCCESS');  
			  
    END LOOP;
  END IF;
  
--结果表删数
IF F_IDX_TYPE IN ('COST','YTD') THEN 
  RAISE NOTICE '删数';
  V_SQL:= 'DELETE FROM '||V_TO_COST_IDX_TABLE||' WHERE VERSION_ID = '||V_VERSION;
  EXECUTE V_SQL;
  
  RAISE NOTICE '插数';
  V_SQL:='
  INSERT INTO '||V_TO_COST_IDX_TABLE||'
    (VERSION_ID,
     BASE_PERIOD_ID,
	 PERIOD_YEAR,
     PERIOD_ID,
     '||V_PROD_PART||'
     DIMENSION_CODE,
     DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE,
     DIMENSION_SUBCATEGORY_CN_NAME,
     DIMENSION_SUB_DETAIL_CODE,
     DIMENSION_SUB_DETAIL_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
     VIEW_FLAG,
     APPEND_FLAG,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
		   PERIOD_YEAR,
           PERIOD_ID,
           '||V_PROD_PART||'
           DIMENSION_CODE,
           DIMENSION_CN_NAME,
           DIMENSION_SUBCATEGORY_CODE,
           DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE,
           DIMENSION_SUB_DETAIL_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           MAIN_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           VIEW_FLAG,
           APPEND_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM '||V_FROM_COST_MID_TABLE||';';
  
  EXECUTE V_SQL;
  
ELSIF F_IDX_TYPE = 'RED_COST' THEN 
  RAISE NOTICE '删数';
  V_SQL:= 'DELETE FROM '||V_TO_RED_IDX_TABLE||' WHERE VERSION_ID = '||V_VERSION;
  EXECUTE V_SQL;
  
  RAISE NOTICE '插数';
  V_SQL:='
  INSERT INTO '||V_TO_RED_IDX_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     PROD_RND_TEAM_CODE,
     PROD_RD_TEAM_CN_NAME,
     '||V_DIMENSION_PART||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
	 YTD_COST_INDEX,
	 COST_REDUCTION_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
     VIEW_FLAG,
     '||V_OTHER_DIM_PART||'
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
	 COST_REDUCTION_CODE,
	 COST_REDUCTION_CN_NAME,
	 COST_REDUCTION_LEVEL
)
    SELECT '||V_VERSION||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.PROD_RND_TEAM_CODE,
           T1.PROD_RD_TEAM_CN_NAME,
           '||V_SQL_DIMENSION_PART||'
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           T1.COST_INDEX,
		   T1.YTD_COST_INDEX,
		   (1-T2.OBJECTIVE)*100 AS COST_REDUCTION_RATE,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           T1.MAIN_FLAG,
           T1.CODE_ATTRIBUTES,
		   T1.SOFTWARE_MARK,		--202410版本 新增软硬件标识
           T1.VIEW_FLAG,
           '||V_SQL_OTHER_DIM_PART||'
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N''AS DEL_FLAG,
		   T2.COST_REDUCTION_CODE,
		   T2.COST_REDUCTION_CN_NAME,
		   T2.COST_REDUCTION_LEVEL
      FROM '||V_FROM_RED_MID_TABLE||' T1
	  LEFT JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_COST_RED_OBJ_FILL_UP_T T2
	    ON T1.GROUP_LEVEL = T2.GROUP_LEVEL
		AND T1.GROUP_CODE = T2.GROUP_CODE
		AND T2.VERSION_ID = ( 
							SELECT VERSION_ID
							  FROM DM_FCST_ICT_VERSION_INFO_T
							 WHERE DEL_FLAG = ''N''
							   AND STATUS = 1
							   AND UPPER(DATA_TYPE) = ''RED_DIM''
							 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1
									)
		AND T1.PERIOD_YEAR = T2.PERIOD_ID
		--AND T1.VIEW_FLAG = T2.VIEW_FLAG
		AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
	  ;';
  
  EXECUTE V_SQL;

 
  
  
END IF;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

