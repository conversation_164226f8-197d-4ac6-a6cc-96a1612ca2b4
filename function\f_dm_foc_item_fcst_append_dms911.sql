-- Name: f_dm_foc_item_fcst_append_dms911; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_item_fcst_append_dms911(f_industry_flag character varying, f_caliber_flag character varying, f_oversea_flag character varying, f_lv0_prod_list_code character varying, f_view_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*

最后修改人:罗若文
背景描述：对预测数实际数ITEM补齐当年YTD连续月份的均价: 前向补齐、后项补齐
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_BASE_DETAIL_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T(量纲颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_BASE_DETAIL_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_ITEM_FCST_APPEND()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ITEM_FCST_APPEND_DMS911'; --存储过程名称
  V_STEP_MUM   BIGINT := 0;   --步骤号
  V_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BASE_DETAIL_ITEM_T表补齐实际数的版本号
  V_FCST_PERIOD BIGINT := CAST(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM') AS BIGINT);  --当前系统预测月
  V_VIEW_FLAG VARCHAR := f_view_flag;

  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);

  V_FROM_TABLE VARCHAR(200); -- 来源表
  V_TO_TABLE VARCHAR(200); -- 目标表
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(100);
  V_DIMENSION_EN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_CODE VARCHAR(100);
  V_IN_DIMENSION_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  
  --12月版本新增spart层
  V_SPART_CODE VARCHAR(200);
  V_SPART_CN_NAME VARCHAR(200);
  V_IN_SPART_CODE VARCHAR(200);
  V_IN_SPART_CN_NAME VARCHAR(200);
  V_INSERT_SPART_CODE VARCHAR(200);
  V_INSERT_SPART_CN_NAME VARCHAR(200);
  
  --202403版本需求
  V_END_YEAR VARCHAR(50);
  V_COUNT_MONTH INTEGER;
  
  --202405月版本新增COA层
  V_COA_CODE VARCHAR(200);
  V_COA_CN_NAME VARCHAR(200);
  V_IN_COA_CODE VARCHAR(200);
  V_IN_COA_CN_NAME VARCHAR(200);
  V_INSERT_COA_CODE VARCHAR(200);
  V_INSERT_COA_CN_NAME VARCHAR(200);
  V_VERSION_TABLE VARCHAR(100);
BEGIN
  X_RESULT_STATUS = '1';

  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;


  
--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
	--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSIF F_INDUSTRY_FLAG = 'E' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
		
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	 
	 --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	
  ELSE 
     NULL ;
	 
  END IF;



	IF F_INDUSTRY_FLAG = 'I' AND F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_09RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_10RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_BASE_DETAIL_ITEM_T_DMS_11RO'; --R/O目标表
	 
	--增加对ICT 12视角的判断
	ELSIF F_INDUSTRY_FLAG = 'I' AND  F_VIEW_FLAG = '12'  THEN
    RETURN 'SUCCESS'; 
	 
	ELSIF F_INDUSTRY_FLAG = 'E' AND F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_09RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_10RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_11RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'E' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_BASE_DETAIL_ITEM_T_DMS_12RO'; --R/O目标表
	
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '9' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_09RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '10' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_10RO'; --R/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '11' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_11RO'; --R/O目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CG'; --C/G目标表
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CI'; --C/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'C' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12CO'; --C/O目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'G' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RG'; --R/G目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'I' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RI'; --R/I目标表
	ELSIF F_INDUSTRY_FLAG = 'IAS' AND  F_VIEW_FLAG = '12' AND F_CALIBER_FLAG = 'R' AND F_OVERSEA_FLAG = 'O' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_BASE_DETAIL_ITEM_T_DMS_12RO'; --R/O目标表
	
   END IF;
   
   V_FROM_TABLE := V_TO_TABLE;
   
   
  --取预测月的最大月份 202303版本需求 杨泽宝
  --预测期间动态扩展从12个月到18个月。当最大实际月是1月到6月,预测月取到第二年的6月;当最大实际月是7月到12月,预测月取到第二年的12月
	SELECT (CASE WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) between 1 and 6 
			THEN CAST((YEAR(CURRENT_DATE)+1)||'06' AS BIGINT)
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) between 7 and 11 
			THEN CAST((YEAR(CURRENT_DATE)+1)||'12' AS BIGINT)
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) = 12 
			THEN CAST(YEAR(CURRENT_DATE)||'12' AS BIGINT)
			END) AS END_YEAR, 
			CASE WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (1,7) THEN 16
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (2,8) THEN 15
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (3,9) THEN 14
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (4,10) THEN 13
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (5,11) THEN 12
			WHEN MONTH(ADD_MONTHS(CURRENT_TIMESTAMP, -1)) IN (6,12) THEN 11
			END AS COUNT_MONTH INTO V_END_YEAR,V_COUNT_MONTH
  FROM DUAL;

  --1.删除目标表中预测数据
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.SCENARIO_FLAG = ''Y'' AND T.VIEW_FLAG = '''||f_view_flag||''' AND CALIBER_FLAG = '''||f_caliber_flag||''' AND OVERSEA_FLAG = '''||f_oversea_flag||''' AND LV0_PROD_LIST_CODE = '''||f_lv0_prod_list_code||'''';
  EXECUTE IMMEDIATE V_SQL;

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'预测数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T1.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='T1.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'T1.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME :='T1.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'T1.L1_NAME,';
    V_IN_L2_NAME := 'T1.L2_NAME,';

    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'T1.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'T1.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'T1.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'T1.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T1.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'T1.DIMENSION_SUB_DETAIL_EN_NAME,';

	--12月版本新增spart层
	V_SPART_CODE := 'SPART_CODE,';
	V_SPART_CN_NAME  := 'SPART_CN_NAME,';
	V_IN_SPART_CODE  := 'T1.SPART_CODE,';
	V_IN_SPART_CN_NAME := 'T1.SPART_CN_NAME,';
	V_INSERT_SPART_CODE  := ' AND NVL(T1.SPART_CODE,2) = NVL(T2.SPART_CODE,2)';
	
    --5月版本新增COA层
	V_COA_CODE := 'COA_CODE,';
	V_COA_CN_NAME  := 'COA_CN_NAME,';
	V_IN_COA_CODE  := 'T1.COA_CODE,';
	V_IN_COA_CN_NAME := 'T1.COA_CN_NAME,';
	V_INSERT_COA_CODE  := ' AND NVL(T1.COA_CODE,2) = NVL(T2.COA_CODE,2)';


       --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';


       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	   
	ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('IAS') THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';


       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
  ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';


       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';

       V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';

   
    --量纲颗粒度的维度时，不需要L1、L2字段
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';

	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';


   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
	   
	   
	   V_LV4_PROD_RND_TEAM_CODE := '';
       V_LV4_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV4_PROD_RND_TEAM_CODE := '';
       V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';

	   
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';

	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	      
	   
	   
    ELSE
      NULL;
    END IF;


  --2.补齐预测数
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
	 V_LV4_PROD_RND_TEAM_CODE ||
     V_LV4_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
	 V_SPART_CODE||
	 V_SPART_CN_NAME||
	 V_COA_CODE||
	 V_COA_CN_NAME||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     SHIP_QUANTITY,
     RMB_COST_AMT,
     RMB_AVG_AMT,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
  WITH RECENT_ACTUAL_TEMP AS
     (
      --从实际数补齐表取最近一次实际月(即当前预测月-1)的重量级团队、采购维信息
      SELECT DISTINCT  T.LV0_PROD_LIST_CODE,
                       T.LV0_PROD_LIST_CN_NAME,
                       T.LV0_PROD_LIST_EN_NAME,
                       T.OVERSEA_FLAG,
                       T.CALIBER_FLAG,
                       T.VIEW_FLAG,
                       T.LV0_PROD_RND_TEAM_CODE,
                       T.LV0_PROD_RD_TEAM_CN_NAME,
                       T.LV1_PROD_RND_TEAM_CODE,
                       T.LV1_PROD_RD_TEAM_CN_NAME,
                       T.LV2_PROD_RND_TEAM_CODE,
                       T.LV2_PROD_RD_TEAM_CN_NAME,'||
                       V_LV3_PROD_RND_TEAM_CODE ||
                       V_LV3_PROD_RD_TEAM_CN_NAME ||
					   V_LV4_PROD_RND_TEAM_CODE ||
                       V_LV4_PROD_RD_TEAM_CN_NAME ||
                       V_L1_NAME ||
                       V_L2_NAME ||
                       V_DIMENSION_CODE ||
                       V_DIMENSION_CN_NAME ||
                       V_DIMENSION_EN_NAME||
                       V_DIMENSION_SUBCATEGORY_CODE ||
                       V_DIMENSION_SUBCATEGORY_CN_NAME ||
                       V_DIMENSION_SUBCATEGORY_EN_NAME||
                       V_DIMENSION_SUB_DETAIL_CODE ||
                       V_DIMENSION_SUB_DETAIL_CN_NAME ||
                       V_DIMENSION_SUB_DETAIL_EN_NAME ||
					   V_SPART_CODE||
					   V_SPART_CN_NAME||
					   V_COA_CODE||
					   V_COA_CN_NAME||'
                       T.L3_CEG_CODE,
                       T.L3_CEG_CN_NAME,
                       T.L3_CEG_SHORT_CN_NAME,
                       T.L4_CEG_CODE,
                       T.L4_CEG_CN_NAME,
                       T.L4_CEG_SHORT_CN_NAME,
                       T.CATEGORY_CODE,
                       T.CATEGORY_CN_NAME,
                       T.ITEM_CODE,
                       T.ITEM_CN_NAME,
                       --TO_NUMBER(GS_DECRYPT(T.RMB_AVG_AMT,''f_keystr'',''AES128'',''CBC'',''SHA256'')) AS RMB_AVG_AMT
                       T.RMB_AVG_AMT
        FROM '||V_FROM_TABLE||' T
       WHERE T.PERIOD_ID =
             CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1), ''YYYYMM'') AS BIGINT) AND T.VIEW_FLAG = '''||V_VIEW_FLAG||''' 
			AND OVERSEA_FLAG = '''||f_oversea_flag||''' AND LV0_PROD_LIST_CODE = '''||f_lv0_prod_list_code||'''
			AND CALIBER_FLAG = '''||f_caliber_flag||'''

			 ),

    CROSS_JOIN_TEMP AS
     (
      --生成今年预测月份及预测月份之后, 所有的发散维
      SELECT  A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME,
              A.OVERSEA_FLAG,
              A.CALIBER_FLAG,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE||
			  V_SPART_CN_NAME||
			  V_COA_CODE||
			  V_COA_CN_NAME||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              A.ITEM_CODE,
              A.ITEM_CN_NAME,
              CAST(SUBSTR(TO_CHAR(B.PERIOD_ID), 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              A.RMB_AVG_AMT
        FROM RECENT_ACTUAL_TEMP A,
		       --B表为截止到最大预测月的月份 202303版本需求 杨泽宝
              (SELECT TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, LEVEL),''yyyymm'') AS PERIOD_ID
						FROM (SELECT GENERATE_SERIES(0,'''||V_COUNT_MONTH||''') AS LEVEL FROM DUAL)) B
						),

    FCST_ITEM_TEMP AS
     (
      SELECT
		PERIOD_ID, FCST_PERIOD_ID, ITEM_CODE, RMB_UNIT_PRICE,CALIBER_FLAG
	  FROM 
	  (SELECT A.PERIOD_ID, 
			 A.FCST_PERIOD_ID,
			 A.ITEM_CODE,
			 A.RMB_UNIT_PRICE,
			 A.CALIBER_FLAG,
			 CASE WHEN A.FCST_PERIOD_ID BETWEEN C.START_PERIOD AND C.END_PERIOD THEN 1 ELSE 0 END AS REVIEW_ITEM_FLAG
	  FROM
      --A表:取对应系统会计期的预测数(R 收入时点,C 发货成本)
	  (SELECT I.PERIOD_ID, I.FCST_PERIOD_ID, I.ITEM_CODE, I.RMB_UNIT_PRICE,''C'' AS CALIBER_FLAG
        FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I I
       WHERE I.FCST_PERIOD_ID <=
             TO_NUMBER('''||V_END_YEAR||''')
         AND I.PERIOD_ID IN (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I )
	   UNION ALL
	   SELECT I.PERIOD_ID, I.FCST_PERIOD_ID, I.ITEM_CODE, I.RMB_UNIT_PRICE,''R'' AS CALIBER_FLAG
        FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I I
       WHERE I.FCST_PERIOD_ID <=
             TO_NUMBER('''||V_END_YEAR||''')
         AND I.PERIOD_ID IN (SELECT MAX(PERIOD_ID) FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I )) A
	  --C表：底层数据审视信息表(FIRST_VALUE函数是为了取同一个item的最早时间和最晚时间) ADD BY YWX1106160 202401 
	   LEFT JOIN (SELECT  DISTINCT ITEM_CODE,
							FIRST_VALUE(START_PERIOD)OVER(PARTITION BY ITEM_CODE,CALIBER_FLAG ORDER BY START_PERIOD) AS START_PERIOD,
							FIRST_VALUE(END_PERIOD)OVER(PARTITION BY ITEM_CODE,CALIBER_FLAG ORDER BY END_PERIOD DESC) AS END_PERIOD,
							CALIBER_FLAG
							FROM (
							--根据更新时间排序,取最新一条不是撤销(REVOKE)状态的数据
							SELECT ITEM_CODE,START_PERIOD,END_PERIOD,CALIBER_FLAG FROM					
							(SELECT ITEM_CODE,START_PERIOD,END_PERIOD,DEL_FLAG,MODIFY_TYPE,CALIBER_FLAG,
							ROW_NUMBER()OVER(PARTITION BY ITEM_CODE,START_PERIOD,END_PERIOD,CALIBER_FLAG ORDER BY LAST_UPDATE_DATE DESC) AS RN
							FROM FIN_DM_OPT_FOI.DM_FOC_BASE_DATA_REVIEW_INFO_T) 
						WHERE RN=1 AND DEL_FLAG = ''N'' AND MODIFY_TYPE <> ''REVOKE'')) C
		ON A.ITEM_CODE = C.ITEM_CODE AND A.CALIBER_FLAG = C.CALIBER_FLAG)
		WHERE  REVIEW_ITEM_FLAG = 0)

    SELECT '||V_VERSION_ID||' AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_PROD_RND_TEAM_CODE,
           T1.LV0_PROD_RD_TEAM_CN_NAME,
           T1.LV1_PROD_RND_TEAM_CODE,
           T1.LV1_PROD_RD_TEAM_CN_NAME,
           T1.LV2_PROD_RND_TEAM_CODE,
           T1.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_IN_LV4_PROD_RND_TEAM_CODE ||
           V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
           V_IN_L1_NAME ||
           V_IN_L2_NAME ||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_IN_SPART_CODE||
		   V_IN_SPART_CN_NAME||
		   V_IN_COA_CODE||
		   V_IN_COA_CN_NAME||'
           T1.L3_CEG_CODE,
           T1.L3_CEG_CN_NAME,
           T1.L3_CEG_SHORT_CN_NAME,
           T1.L4_CEG_CODE,
           T1.L4_CEG_CN_NAME,
           T1.L4_CEG_SHORT_CN_NAME,
           T1.CATEGORY_CODE,
           T1.CATEGORY_CN_NAME,
           T1.ITEM_CODE,
           T1.ITEM_CN_NAME,
           NULL AS SHIP_QUANTITY,
           NULL AS RMB_COST_AMT,
           --GS_ENCRYPT(DECODE(T2.ITEM_CODE, NULL, T1.RMB_AVG_AMT, T2.RMB_UNIT_PRICE),''f_keystr'', ''AES128'', ''CBC'', ''SHA256'') AS RMB_AVG_AMT,
           DECODE(T2.ITEM_CODE, NULL, T1.RMB_AVG_AMT, T2.RMB_UNIT_PRICE) AS RMB_AVG_AMT,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           DECODE(T2.ITEM_CODE, NULL, ''Y'', ''N'') AS APPEND_FLAG,
           ''Y'' AS SCENARIO_FLAG,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME,
           T1.LV0_PROD_LIST_EN_NAME
      FROM CROSS_JOIN_TEMP T1
      LEFT JOIN FCST_ITEM_TEMP T2
        ON T1.ITEM_CODE = T2.ITEM_CODE
       AND T1.PERIOD_ID = T2.FCST_PERIOD_ID
	   AND T1.CALIBER_FLAG = T2.CALIBER_FLAG';
      EXECUTE IMMEDIATE V_SQL;

  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插入新补齐的分视角均价预测数到'||V_TO_TABLE||'表, 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;

  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END

$$
/

