-- Name: f_dm_fol_air_weight_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_weight_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建人  ： zwx1275798
创建时间： 2024年8月27日
背景描述： 物流空运权重信息表，仅精品空运
参数描述： p_version_id   逻辑：1、自动调度，取航线量汇总表的最大版本ID；2、刷新（页面的刷新价格表）：取java传版本ID；
           p_version_code 逻辑：1、自动调度，取价格补录头表的最大版本code；2、刷新（页面的刷新价格表）：取java传版本code；
           p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表）：取java传的值（1_刷新价格表 ）
		   x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_weight_info_t()
变更记录：202503 zwx1275798 代码优化(代码由688行缩减至586行)：
                                         1、将所有temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
										 2、将最小粒度的成本和是否精品在最开始取数的时候计算，代码整合，去除冗余逻辑
*/

declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_fol_air_weight_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_fol_air_weight_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_price_version_code  varchar(30);

begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '物流空运权重信息表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

         --从 物流空运权重信息表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_weight_info_t t1 
		where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001');
		

         -- 如果是传 version_id 调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then
        select  p_version_id into v_max_version_id ;
        else
        select max(version_id) as max_version_id into v_max_version_id
		from fin_dm_opt_foi.dm_fol_air_route_info_sum_t;
        end if
        ;

        -- 如果p_version_code为空，则取 物流空运价格补录表头表中 的最大版本编码，如果p_version_code不为空，则取传入的p_version_code   
		if (p_version_code is null or p_version_code = '') then
        select max(version_code) as max_version_code into v_price_version_code 
		from fin_dm_opt_foi.apd_fol_air_route_price_heaer_t 
		where upper(status)='FINAL';
		else 
		select  p_version_code into v_price_version_code ;
		end if
          ; 

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_air_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_air_weight_info_t'
       and refresh_type =  nvl(p_refresh_type,'4_AUTO')
       and step = 2
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )   
  	select v_max_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_air_weight_info_t' as source_en_name
         , '物流空运权重信息函数'           as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '自动调度，版本ID：'||v_max_version_id||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

      	--从 物流空运航线量价汇总表 取出 version_id 为最大版本 
		drop table if exists air_weight_route_tmp;
		create temporary table air_weight_route_tmp
		      as		
		select  version_id
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name 
			   ,dest_port_name   
			   ,dest_country_name
			   ,price_id
			   ,supplier_short_name
			   ,currency
			   ,sum(price*container_qty) as amount
			   ,sum(container_qty) as container_qty
			   ,Huawei_group   
			   ,service_level  
			   ,is_high_quality          
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and version_id = v_max_version_id
		  and transport_mode = '精品空运'
		  and price <> 0
		  and container_qty is not null
		group by version_id
		       ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name 
			   ,dest_port_name   
			   ,dest_country_name
			   ,price_id
			   ,supplier_short_name
			   ,currency
			   ,Huawei_group   
			   ,service_level  
			   ,is_high_quality
	   union all
			   select  version_id
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name 
			   ,dest_port_name   
			   ,dest_country_name
			   ,price_id
			   ,supplier_short_name
			   ,currency
			   ,sum(price*container_qty) as amount
			   ,sum(container_qty) as container_qty
			   ,Huawei_group   
			   ,service_level  
			   ,'ALL' as is_high_quality          
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and version_id = v_max_version_id
		  and transport_mode = '精品空运'
		  and price <> 0
		  and container_qty is not null
		group by version_id
		       ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name 
			   ,dest_port_name   
			   ,dest_country_name
			   ,price_id
			   ,supplier_short_name
			   ,currency
			   ,Huawei_group   
			   ,service_level  
			   ;

        v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => 'air_weight_route_tmp 从物流空运航线量价汇总表取出 version_id 为最大版本：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
	
    -- 清理表数据
    delete from fin_dm_opt_foi.dm_fol_air_weight_info_t where version_id = v_max_version_id and upper(del_flag) = 'N';
	
	
	-- 供应商层级当月的成本
	insert into fin_dm_opt_foi.dm_fol_air_weight_info_t(
	       version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,route
          ,source_port_name 
		  ,dest_port_name   
		  ,dest_country_name
          ,supplier_short_name
          ,weight_type
          ,level_code
          ,level_desc
          ,currency
          ,amount
          ,weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          ,remark
          ,created_by
          ,creation_date
          ,last_updated_by
          ,last_update_date
          ,del_flag
		  )
		select version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,route
          ,source_port_name 
		  ,dest_port_name   
		  ,dest_country_name
          ,supplier_short_name
          ,'COST'weight_type
          ,'04' as level_code
          ,'供应商' as level_desc
          ,currency
          ,sum(amount) as amount
          ,null weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          , '' as remark
  	      , -1 as created_by
  	      , current_timestamp as creation_date
  	      , -1 as last_updated_by
  	      , current_timestamp as last_update_date
  	      , 'N' as del_flag
        from air_weight_route_tmp
		group by version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,route
          ,source_port_name 
		  ,dest_port_name   
		  ,dest_country_name
          ,supplier_short_name
          ,currency
          ,Huawei_group
          ,service_level
          ,is_high_quality
		  ;
		  
		   v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => 'dm_fol_air_weight_info_t 供应商层级当月的成本：区分是否精品：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
	
	
	-- 航线层级当月的成本：是否精品（精品、货代、ALL）
	insert into fin_dm_opt_foi.dm_fol_air_weight_info_t(
	       version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,route
          ,source_port_name 
		  ,dest_port_name   
		  ,dest_country_name
          ,weight_type
          ,level_code
          ,level_desc
          ,currency
          ,amount
          ,weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          ,remark
          ,created_by
          ,creation_date
          ,last_updated_by
          ,last_update_date
          ,del_flag
		  )
		select version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,route
          ,source_port_name 
		  ,dest_port_name   
		  ,dest_country_name
          ,'COST'weight_type
          ,'03' as level_code
          ,'航线' as level_desc
          ,currency
          ,sum(amount) as amount
          ,null weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          , '' as remark
  	      , -1 as created_by
  	      , current_timestamp as creation_date
  	      , -1 as last_updated_by
  	      , current_timestamp as last_update_date
  	      , 'N' as del_flag
        from air_weight_route_tmp
		group by version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,route
          ,source_port_name 
		  ,dest_port_name   
		  ,dest_country_name
          ,currency
          ,Huawei_group
          ,service_level
          ,is_high_quality
		  ;
		  
		  v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => 'dm_fol_air_weight_info_t 航线层级当月的成本：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

		  -- 区域层级当月的成本：是否精品（精品、货代、ALL）
	insert into fin_dm_opt_foi.dm_fol_air_weight_info_t(
	       version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,weight_type
          ,level_code
          ,level_desc
          ,currency
          ,amount
          ,weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          ,remark
          ,created_by
          ,creation_date
          ,last_updated_by
          ,last_update_date
          ,del_flag
		  )
		select version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,'COST'weight_type
          ,'02' as level_code
          ,'区域' as level_desc
          ,currency
          ,sum(amount) as amount
          ,null weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          , '' as remark
  	      , -1 as created_by
  	      , current_timestamp as creation_date
  	      , -1 as last_updated_by
  	      , current_timestamp as last_update_date
  	      , 'N' as del_flag
        from air_weight_route_tmp
		group by version_id
          ,period_id
          ,transport_mode
          ,region_cn_name
          ,currency
          ,Huawei_group
          ,service_level
          ,is_high_quality
		  ;
		  
		   v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => 'dm_fol_air_weight_info_t 区域层级当月的成本：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
		  
		  
		  -- 运输方式层级当月的成本：是否精品（精品、货代、ALL）
	insert into fin_dm_opt_foi.dm_fol_air_weight_info_t(
	       version_id
          ,period_id
          ,transport_mode
          ,weight_type
          ,level_code
          ,level_desc
          ,currency
          ,amount
          ,weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          ,remark
          ,created_by
          ,creation_date
          ,last_updated_by
          ,last_update_date
          ,del_flag
		  )
		select version_id
          ,period_id
          ,transport_mode
          ,'COST'weight_type
          ,'01' as level_code
          ,'运输方式' as level_desc
          ,currency
          ,sum(amount) as amount
          ,null weight
          ,Huawei_group
          ,service_level
          ,is_high_quality
          , '' as remark
  	      , -1 as created_by
  	      , current_timestamp as creation_date
  	      , -1 as last_updated_by
  	      , current_timestamp as last_update_date
  	      , 'N' as del_flag
        from air_weight_route_tmp
		group by version_id
          ,period_id
          ,transport_mode
          ,currency
          ,Huawei_group
          ,service_level
          ,is_high_quality
		  ;

	 v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => '运输方式层级当月的成本：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_air_version_info_t set step = 1
     where version_id = v_max_version_id 
		 and step = 2
		 and source_en_name = 'f_dm_fol_air_weight_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO')
         and upper(del_flag) = 'N'		 
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 9,
        p_log_cal_log_desc =>  '版本信息表中的step已更新为完成，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_air_weight_info_t;
  analyse fin_dm_opt_foi.dm_fol_air_version_info_t;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
	   , transport_mode       -- 运输方式（精品空运、精品海运）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
	select nvl(p_version_id,v_max_version_id)   as version_id
       , v_price_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_air_weight_info_t' as source_en_name
       , '物流空运权重信息函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
	   , '精品空运' as transport_mode
       , 'version_code 是物流航线价格补录表的版本编码' as remark
	   , -1 as created_by
	   , current_timestamp as creation_date
	   , -1 as last_updated_by
	   , current_timestamp as last_update_date
	   , 'N' as del_flag
  ;
end;
$$
/

