-- Name: f_dm_fcst_ict_mon_weight_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_weight_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text DEFAULT NULL::text, f_version_id character varying DEFAULT NULL::character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
--2024年9月24日14点49分 新增软硬件标识
创建时间: 2024年7月12日
创建人  : 黄心蕊 hwx1187045
背景描述: 月度权重 (分三个两年计算每层权重)
参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二: F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录
		参数三: F_KEYSTR	密钥
		参数四: F_VERSION_ID 版本号
		参数五: X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败
--------------------来源表
----------分子
----月均本表
--PSP
重量级团队目录		DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_BASE_DETAIL_SPART_T   --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_T    --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_T     --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_BASE_DETAIL_SPART_T   --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_BASE_DETAIL_SPART_T    --STD PROD

--------------------目标表
--月度权重表
重量级团队目录		DM_FCST_ICT_PSP_IRB_MON_WEIGHT_T		--PSP IRB
产业目录			DM_FCST_ICT_PSP_INDUS_MON_WEIGHT_T		--PSP INDUS
销售目录			DM_FCST_ICT_PSP_PROD_MON_WEIGHT_T		--PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_MON_WEIGHT_T		--STD IRB
产业目录			DM_FCST_ICT_STD_INDUS_MON_WEIGHT_T		--STD INDUS
销售目录			DM_FCST_ICT_STD_PROD_MON_WEIGHT_T		--STD PROD
--事例
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T('PSP','IRB','',''); 		--PSP成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T('PSP','INDUS','',''); 	--PSP成本 产业目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T('PSP','PROD','',''); 	--PSP成本 销售目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T('STD''IRB','密钥',''); 	--标准成本 重量级团队目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T('STD''INDUS','密钥',''); --标准成本 产业目录一个版本数据
  SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T('STD''PROD','密钥','');  --标准成本 销售目录一个版本数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME             VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_WEIGHT_T';
  V_VERSION             VARCHAR(10); --版本号
  V_EXCEPTION_FLAG		VARCHAR(2);
  V_YEAR1               VARCHAR(200);
  V_YEAR2               VARCHAR(200);
  V_YEAR3               VARCHAR(200);
  V_FROM_TABLE          VARCHAR(200);
  V_TO_TABLE            VARCHAR(200);
  V_OTHER_DIM_PART      TEXT;
  V_SQL_OTHER_DIM_PART  TEXT;
  V_JOIN_OTHER_DIM_PART TEXT;
  V_DIMENSION_PART      TEXT;
  V_SQL_DIMENSION_PART  TEXT;
  V_JOIN_DIMENSION_CODE TEXT;
  V_PUBLIC_PBI_PART     TEXT;
  V_RMB_COST_AMT        TEXT;
  V_PBI_PART            TEXT;
  V_SQL_PBI_PART        TEXT;
  V_PROD_PART           TEXT;
  V_IN_LEV              TEXT;
  V_SQL                 TEXT;
  V_SQL2                TEXT;
  V_PARENT_PART         TEXT;
  V_SQL_PROD_PART       TEXT;
  V_GROUP_LEVEL         TEXT;
  
BEGIN 
  V_YEAR3 := TO_CHAR((YEAR(CURRENT_DATE) - 3) || '-' ||(YEAR(CURRENT_DATE) - 2));
  V_YEAR2 := TO_CHAR((YEAR(CURRENT_DATE) - 2) || '-' ||(YEAR(CURRENT_DATE) - 1));
  V_YEAR1 := TO_CHAR((YEAR(CURRENT_DATE) - 1) || '-' || YEAR(CURRENT_DATE));

  V_EXCEPTION_FLAG:='0';
  
RAISE NOTICE '取版本号';
--版本表
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
----变量赋值
--表变量赋值
  V_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BASE_DETAIL_SPART_T';
  V_TO_TABLE 	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_WEIGHT_T';
  
  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
		';
		
  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')		
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';
  V_PUBLIC_PBI_PART:='LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,
                      LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
					  SPART_CODE,SPART_CN_NAME,
  '	;				
					
--条件判断(加密不加密)
--是否解密判断
  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_COST_AMT := 'RMB_COST_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_COST_AMT := '
					 TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,
					   '''||F_KEYSTR||''',
					   ''aes128'',
					   ''cbc'',
					   ''sha256'')) AS RMB_COST_AMT,
				  ';
  END IF;
--不同目录维度字段判断
  IF F_GRANULARITY_TYPE = 'IRB' THEN
	
    V_PBI_PART := '
      LV0_PROD_RND_TEAM_CODE,
      LV1_PROD_RND_TEAM_CODE,
      LV2_PROD_RND_TEAM_CODE,
      LV3_PROD_RND_TEAM_CODE,
      LV4_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RD_TEAM_CN_NAME,
      LV3_PROD_RD_TEAM_CN_NAME,
      LV4_PROD_RD_TEAM_CN_NAME,
      ';
	  
    V_SQL_PBI_PART  := '
      T1.LV0_PROD_RND_TEAM_CODE,
      T1.LV1_PROD_RND_TEAM_CODE,
      T1.LV2_PROD_RND_TEAM_CODE,
      T1.LV3_PROD_RND_TEAM_CODE,
      T1.LV4_PROD_RND_TEAM_CODE,
      T1.LV0_PROD_RD_TEAM_CN_NAME,
      T1.LV1_PROD_RD_TEAM_CN_NAME,
      T1.LV2_PROD_RD_TEAM_CN_NAME,
      T1.LV3_PROD_RD_TEAM_CN_NAME,
      T1.LV4_PROD_RD_TEAM_CN_NAME,
      ';
	V_PROD_PART:='
		PROD_RND_TEAM_CODE,
		PROD_RD_TEAM_CN_NAME,
	';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN
    V_PBI_PART := '
      LV0_INDUSTRY_CATG_CODE    ,
      LV1_INDUSTRY_CATG_CODE      ,
      LV2_INDUSTRY_CATG_CODE      ,
      LV3_INDUSTRY_CATG_CODE      ,
      LV4_INDUSTRY_CATG_CODE      ,
      LV0_INDUSTRY_CATG_CN_NAME,
      LV1_INDUSTRY_CATG_CN_NAME,
      LV2_INDUSTRY_CATG_CN_NAME,
      LV3_INDUSTRY_CATG_CN_NAME,
      LV4_INDUSTRY_CATG_CN_NAME,
      ';
  
    V_SQL_PBI_PART := '
      T1.LV0_INDUSTRY_CATG_CODE,
      T1.LV1_INDUSTRY_CATG_CODE,
      T1.LV2_INDUSTRY_CATG_CODE,
      T1.LV3_INDUSTRY_CATG_CODE,
      T1.LV4_INDUSTRY_CATG_CODE,
      T1.LV0_INDUSTRY_CATG_CN_NAME,
      T1.LV1_INDUSTRY_CATG_CN_NAME,
      T1.LV2_INDUSTRY_CATG_CN_NAME,
      T1.LV3_INDUSTRY_CATG_CN_NAME,
      T1.LV4_INDUSTRY_CATG_CN_NAME,
      ';
	  
	V_PROD_PART:='
		INDUSTRY_CATG_CODE,
		INDUSTRY_CATG_CN_NAME,
	';

  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN
    V_PBI_PART      := '
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      ';
    V_SQL_PBI_PART  := '
      T1.LV0_PROD_LIST_CODE,
      T1.LV1_PROD_LIST_CODE,
      T1.LV2_PROD_LIST_CODE,
      T1.LV3_PROD_LIST_CODE,
      T1.LV4_PROD_LIST_CODE,
      T1.LV0_PROD_LIST_CN_NAME,
      T1.LV1_PROD_LIST_CN_NAME,
      T1.LV2_PROD_LIST_CN_NAME,
      T1.LV3_PROD_LIST_CN_NAME,
      T1.LV4_PROD_LIST_CN_NAME,
      ';
	  
	V_PROD_PART:='
		PROD_LIST_CODE,
		PROD_LIST_CN_NAME,
	';
  
  END IF;
 
RAISE NOTICE '数据解密落表';
----金额解密落表
--建临时表
DROP TABLE IF EXISTS DM_MONTH_AMT_TEMP;
CREATE TEMPORARY TABLE DM_MONTH_AMT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_COST_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),
 SOFTWARE_MARK		VARCHAR(20), --202410版本 新增软硬件标识
 CODE_ATTRIBUTES    VARCHAR(20)     --是否主力编码 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--解密落表
V_SQL:='
  INSERT INTO DM_MONTH_AMT_TEMP
    (PERIOD_YEAR,
	 PERIOD_ID,
     '||V_PUBLIC_PBI_PART||V_DIMENSION_PART||'
	 RMB_COST_AMT,
	 '||V_OTHER_DIM_PART||'
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
	 VIEW_FLAG)
    SELECT PERIOD_YEAR,
           PERIOD_ID,
		   '||V_PBI_PART||'
		   SPART_CODE,
		   SPART_CN_NAME,
		   '||V_DIMENSION_PART||'
		   '||V_RMB_COST_AMT||'
           '||V_OTHER_DIM_PART||' 
		   MAIN_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
		   VIEW_FLAG
      FROM '||V_FROM_TABLE||' 
	 WHERE APPEND_FLAG = ''N''

';

  EXECUTE V_SQL;

--建表
RAISE NOTICE '金额卷积出主力编码部分(并分出区间年)';
DROP TABLE IF EXISTS DM_BASE_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_AMT_TEMP(
 LV0_CODE		VARCHAR(50),
 LV1_CODE   	VARCHAR(50),
 LV2_CODE   	VARCHAR(50),
 LV3_CODE   	VARCHAR(50),
 LV4_CODE   	VARCHAR(50),	
 LV0_CN_NAME	VARCHAR(200),
 LV1_CN_NAME   	VARCHAR(200),
 LV2_CN_NAME   	VARCHAR(200),
 LV3_CN_NAME   	VARCHAR(200),
 LV4_CN_NAME   	VARCHAR(200), 
 PROD_CODE		VARCHAR(50),
 PROD_CN_NAME   VARCHAR(200),
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		VARCHAR(20),
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) ,
 GROUP_CODE			VARCHAR(50)  ,
 GROUP_CN_NAME      VARCHAR(200) ,
 GROUP_LEVEL		VARCHAR(200) , 
 PARENT_CODE		VARCHAR(50)  ,
 PARENT_CN_NAME     VARCHAR(200) ,
 RMB_COST_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 SOFTWARE_MARK		VARCHAR(20),--202410版本 新增软硬件标识
 MAIN_FLAG			VARCHAR(1),
 CODE_ATTRIBUTES    VARCHAR(20)     --是否主力编码 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

----金额卷积出主力编码部分(并分出区间年)
RAISE NOTICE '区间年金额卷积';
 V_SQL:= '
  WITH BASE_AMT AS
   (
    --Y-3到Y-2金额
    SELECT '''||V_YEAR3||''' AS PERIOD_YEAR,
			'||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||V_DIMENSION_PART||' 
			LV4_CODE AS PROD_CODE,
			LV4_CN_NAME AS PROD_CN_NAME,
			DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CODE,DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,
			DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CN_NAME,DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,
			DECODE(VIEW_FLAG,''PROD_SPART'',''SPART'',''SUB_DETAIL'') AS GROUP_LEVEL,
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CODE,DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,
			DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CN_NAME,DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,
			MAIN_FLAG,
            CODE_ATTRIBUTES,
			SOFTWARE_MARK,		--202410版本 新增软硬件标识
            VIEW_FLAG
      FROM DM_MONTH_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 3 AND
           YEAR(CURRENT_DATE) - 2
     GROUP BY '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||V_DIMENSION_PART||'
			   MAIN_FLAG,
               CODE_ATTRIBUTES,
			   SOFTWARE_MARK,		--202410版本 新增软硬件标识
               VIEW_FLAG
    UNION ALL
    --Y-2到Y-1金额
    SELECT '''||V_YEAR2||''' AS PERIOD_YEAR,
			'||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||V_DIMENSION_PART||'
			LV4_CODE AS PROD_CODE,
			LV4_CN_NAME AS PROD_CN_NAME,
			DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CODE,DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,
			DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CN_NAME,DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,
			DECODE(VIEW_FLAG,''PROD_SPART'',''SPART'',''SUB_DETAIL'') AS GROUP_LEVEL,
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CODE,DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,
			DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CN_NAME,DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,
			MAIN_FLAG,
            CODE_ATTRIBUTES,
			SOFTWARE_MARK,		--202410版本 新增软硬件标识
            VIEW_FLAG
      FROM DM_MONTH_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 2 AND
           YEAR(CURRENT_DATE) - 1
     GROUP BY '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||V_DIMENSION_PART||'
			   MAIN_FLAG,
               CODE_ATTRIBUTES,
			   SOFTWARE_MARK,		--202410版本 新增软硬件标识
               VIEW_FLAG
    UNION ALL
    --Y-1到Y金额
    SELECT '''||V_YEAR1||''' AS PERIOD_YEAR,
			'||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||V_DIMENSION_PART||'
			LV4_CODE AS PROD_CODE,
			LV4_CN_NAME AS PROD_CN_NAME,
			DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CODE,DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,
			DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CN_NAME,DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,
			DECODE(VIEW_FLAG,''PROD_SPART'',''SPART'',''SUB_DETAIL'') AS GROUP_LEVEL,
			SUM(RMB_COST_AMT) AS RMB_COST_AMT,
			DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CODE,DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,
			DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CN_NAME,DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,
			MAIN_FLAG,
            CODE_ATTRIBUTES,
			SOFTWARE_MARK,		--202410版本 新增软硬件标识
            VIEW_FLAG
      FROM DM_MONTH_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE) - 1 AND YEAR(CURRENT_DATE)
     GROUP BY '||V_PUBLIC_PBI_PART||V_OTHER_DIM_PART||V_DIMENSION_PART||'
			   MAIN_FLAG,
               CODE_ATTRIBUTES,
			   SOFTWARE_MARK,		--202410版本 新增软硬件标识
               VIEW_FLAG
			   )
	INSERT INTO DM_BASE_AMT_TEMP
	('||V_PUBLIC_PBI_PART||V_DIMENSION_PART||V_OTHER_DIM_PART||'
	 PROD_CODE,
	 PROD_CN_NAME,
	 PERIOD_YEAR,
	 GROUP_CODE,
	 GROUP_CN_NAME,
	 GROUP_LEVEL,
	 RMB_COST_AMT,
	 PARENT_CODE,
	 PARENT_CN_NAME,
	 MAIN_FLAG,
	 CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
	 VIEW_FLAG
	)			   		   
  --全量路径一路径二
  SELECT '||V_PUBLIC_PBI_PART||V_DIMENSION_PART||V_OTHER_DIM_PART||'
         PROD_CODE,
		 PROD_CN_NAME,
		 PERIOD_YEAR,
		 GROUP_CODE,
		 GROUP_CN_NAME,
		 GROUP_LEVEL,
		 RMB_COST_AMT,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         ''N'' AS MAIN_FLAG,
         '''' AS CODE_ATTRIBUTES,
		 SOFTWARE_MARK,		--202410版本 新增软硬件标识
         VIEW_FLAG
    FROM BASE_AMT
  UNION ALL
  --全量路径一主力编码
  SELECT '||V_PUBLIC_PBI_PART||V_DIMENSION_PART||V_OTHER_DIM_PART||'
         PROD_CODE,
		 PROD_CN_NAME,
		 PERIOD_YEAR,
		 GROUP_CODE,
		 GROUP_CN_NAME,
		 GROUP_LEVEL,
		 RMB_COST_AMT,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         ''Y'' AS MAIN_FLAG,
         CODE_ATTRIBUTES,
		 SOFTWARE_MARK,		--202410版本 新增软硬件标识
         VIEW_FLAG
    FROM BASE_AMT
   WHERE MAIN_FLAG = ''Y''
  UNION ALL
  --全量路径一主力编码,编码类型写死
  SELECT '||V_PUBLIC_PBI_PART||V_DIMENSION_PART||V_OTHER_DIM_PART||'
         PROD_CODE,
		 PROD_CN_NAME,
		 PERIOD_YEAR,
		 GROUP_CODE,
		 GROUP_CN_NAME,
		 GROUP_LEVEL,
		 RMB_COST_AMT,
		 PARENT_CODE,
		 PARENT_CN_NAME,
         ''Y'' AS MAIN_FLAG,
         ''全选'' AS CODE_ATTRIBUTES,
		 SOFTWARE_MARK,		--202410版本 新增软硬件标识
         VIEW_FLAG
    FROM BASE_AMT
   WHERE MAIN_FLAG = ''Y'' ; ';
   
  EXECUTE V_SQL;

----分主力编码卷积每层金额及PARENT_CODE PARENT_NAME
  FOR V_LEVEL_NUM IN 1..4 LOOP
    
    IF V_LEVEL_NUM = 1 THEN
      --路径一LV4,路径二量纲子类
	  RAISE NOTICE '路径一LV4,路径二量纲子类-金额卷积';
      V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,	
							LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,LV4_CN_NAME,
							';
      V_DIMENSION_PART  := 'DIMENSION_CODE,DIMENSION_CN_NAME,
							DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
							';
      V_IN_LEV          := ' (''SPART'',''SUB_DETAIL'') ';
      V_GROUP_LEVEL     := 'DECODE(VIEW_FLAG,''PROD_SPART'',''LV4'',''SUBCATEGORY'')';
      V_PARENT_PART     := 'DECODE(VIEW_FLAG,''PROD_SPART'',LV3_CODE,DIMENSION_CODE) AS PARENT_CODE,
							DECODE(VIEW_FLAG,''PROD_SPART'',LV3_CN_NAME,DIMENSION_CN_NAME) AS PARENT_CN_NAME,
							';
      V_SQL_PROD_PART   := 'DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CODE,LV4_CODE) AS PROD_CODE,
							DECODE(VIEW_FLAG,''PROD_SPART'',LV4_CN_NAME,LV4_CN_NAME) AS PROD_CN_NAME,
							';
    
    ELSIF V_LEVEL_NUM = 2 THEN
      --路径一-LV3
	  RAISE NOTICE '路径一LV3,路径二量纲金额卷积';
      V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,
							LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,LV3_CN_NAME,
							';
      V_DIMENSION_PART  := '';
      V_IN_LEV          := ' (''LV4'') ';
	  V_SQL2            := ' AND VIEW_FLAG = ''PROD_SPART'' ';
      V_GROUP_LEVEL     := '''LV3''';
      V_PARENT_PART     := 'LV2_CODE AS PARENT_CODE,
							LV2_CN_NAME AS PARENT_CN_NAME,
							';
      V_SQL_PROD_PART   := 'LV3_CODE AS PROD_CODE,
							LV3_CN_NAME AS PROD_CN_NAME,
							';
    ELSIF V_LEVEL_NUM = 3 THEN
      --路径一-LV2
	  RAISE NOTICE 'LV2卷积';
      V_DIMENSION_PART  := '';
      V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,LV2_CODE,
							LV0_CN_NAME,LV1_CN_NAME,LV2_CN_NAME,
							';
      V_IN_LEV          := ' (''LV3'') ';
      V_SQL2            := ' AND VIEW_FLAG = ''PROD_SPART'' ';
      V_GROUP_LEVEL     := '''LV2''';
      V_PARENT_PART     := 'LV1_CODE AS PARENT_CODE,
							LV1_CN_NAME AS PARENT_CN_NAME,
							';
      V_SQL_PROD_PART   := 'LV2_CODE AS PROD_CODE,
							LV2_CN_NAME AS PROD_CN_NAME,
							';
    ELSIF V_LEVEL_NUM = 4 THEN
      --路径一-LV1
	  RAISE NOTICE 'LV1卷积';
      V_PUBLIC_PBI_PART := 'LV0_CODE,LV1_CODE,
							LV0_CN_NAME,LV1_CN_NAME,
							';
      V_IN_LEV          := ' (''LV2'') ';
      V_GROUP_LEVEL     := ' ''LV1'' ';
      V_PARENT_PART     := 'LV0_CODE AS PARENT_CODE,
							LV0_CN_NAME AS PARENT_CN_NAME,
							';
      V_SQL_PROD_PART   := 'LV1_CODE AS PROD_CODE,
							LV1_CN_NAME AS PROD_CN_NAME,
							';
    END IF;
  
    V_SQL := '
    INSERT INTO DM_BASE_AMT_TEMP
      (PERIOD_YEAR,
       '||V_PUBLIC_PBI_PART||V_DIMENSION_PART||V_OTHER_DIM_PART||'
	   PROD_CODE,
	   PROD_CN_NAME,
	   GROUP_CODE,
	   GROUP_CN_NAME,
       GROUP_LEVEL,
       PARENT_CODE,
       PARENT_CN_NAME,
       RMB_COST_AMT,
       MAIN_FLAG,
       CODE_ATTRIBUTES,
	   SOFTWARE_MARK,		--202410版本 新增软硬件标识
       VIEW_FLAG)
	   SELECT PERIOD_YEAR,
             '||V_PUBLIC_PBI_PART|| V_DIMENSION_PART||V_OTHER_DIM_PART||V_SQL_PROD_PART||' 
			 PARENT_CODE AS GROUP_CODE,
			 PARENT_CN_NAME AS GROUP_CN_NAME,
			 '||V_GROUP_LEVEL||' AS GROUP_LEVEL,
             '||V_PARENT_PART||' 
			 SUM(RMB_COST_AMT) AS RMB_COST_AMT,
             MAIN_FLAG,
             CODE_ATTRIBUTES,
			 SOFTWARE_MARK,		--202410版本 新增软硬件标识
             VIEW_FLAG
        FROM DM_BASE_AMT_TEMP
	   WHERE GROUP_LEVEL IN ' || V_IN_LEV||V_SQL2 || '
       GROUP BY PERIOD_YEAR,
                '||V_PUBLIC_PBI_PART||V_DIMENSION_PART||V_OTHER_DIM_PART||' 
				MAIN_FLAG,
				PARENT_CODE,
				PARENT_CN_NAME,
                CODE_ATTRIBUTES,
				SOFTWARE_MARK,		--202410版本 新增软硬件标识
                VIEW_FLAG;';
				
  EXECUTE V_SQL;
  
  END LOOP;

  --删数
  V_SQL:= 'DELETE FROM '||V_TO_TABLE;
   EXECUTE V_SQL;
  
  
----权重统一计算(只需要计算本层级与上层级金额)
RAISE NOTICE '结果落表';
V_SQL:='
  INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     '||V_PROD_PART||' DIMENSION_CODE,
     DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE,
     DIMENSION_SUBCATEGORY_CN_NAME,
     DIMENSION_SUB_DETAIL_CODE,
     DIMENSION_SUB_DETAIL_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,		--202410版本 新增软硬件标识
     VIEW_FLAG,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_YEAR,
           PROD_CODE,
           PROD_CN_NAME,
           DIMENSION_CODE,
           DIMENSION_CN_NAME,
           DIMENSION_SUBCATEGORY_CODE,
           DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE,
           DIMENSION_SUB_DETAIL_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           CASE
             WHEN GROUP_LEVEL IN
                  (''LV4'',''LV3'',''LV2'',''LV1'') THEN
              NVL(RMB_COST_AMT /
              NULLIF(SUM(RMB_COST_AMT) OVER(PARTITION BY PERIOD_YEAR,
                          GROUP_LEVEL,PARENT_CODE,
                          REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
                          CODE_ATTRIBUTES,SOFTWARE_MARK,		--202410版本 新增软硬件标识
						  MAIN_FLAG)
                     ,0),0)
             WHEN GROUP_LEVEL =''SUB_DETAIL''THEN
              NVL(RMB_COST_AMT /
              NULLIF(SUM(RMB_COST_AMT)
                     OVER(PARTITION BY PERIOD_YEAR,
                          LV0_CODE,LV4_CODE,
                          GROUP_LEVEL,PARENT_CODE,
                          NVL(DIMENSION_CODE,''DC''),NVL(DIMENSION_SUBCATEGORY_CODE,''DSC''),
                          REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
                          NVL(CODE_ATTRIBUTES,''CA''),
						  NVL(SOFTWARE_MARK,''SW''),		--202410版本 新增软硬件标识
						  NVL(MAIN_FLAG,''MF''))
                     ,0),0)
             WHEN GROUP_LEVEL IN (''SUBCATEGORY'',''SPART'') THEN
              NVL(RMB_COST_AMT /
              NULLIF(SUM(RMB_COST_AMT) OVER(PARTITION BY PERIOD_YEAR,
                          LV0_CODE,LV4_CODE,
                          GROUP_LEVEL,PARENT_CODE,
                          REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,
                          NVL(CODE_ATTRIBUTES,''CA''),
						  NVL(SOFTWARE_MARK,''SW''),		--202410版本 新增软硬件标识
						  NVL(MAIN_FLAG,''MF''))
                     ,0),0)
           END AS WEIGHT_RATE,
           PARENT_CODE,
           PARENT_CN_NAME,
           MAIN_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,		--202410版本 新增软硬件标识
           VIEW_FLAG,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_BASE_AMT_TEMP;';

  EXECUTE V_SQL;
  
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_EXCEPTION_FLAG,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 


$$
/

