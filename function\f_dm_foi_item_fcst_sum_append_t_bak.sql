-- Name: f_dm_foi_item_fcst_sum_append_t_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_item_fcst_sum_append_t_bak(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
DECLARE
  V_SP_NAME VARCHAR2(500):= 'FIN_DM_OPT_FOI.F_DM_FOI_ITEM_FCST_SUM_APPEND_T';
  V_FCST_PERIOD NUMBER := TO_NUMBER(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM')); --当前系统预测月
  
BEGIN
  X_RESULT_STATUS:= 1;
  
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '删除FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T预测表中APPEND_FLAG为Y的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
	--2.插入新补齐的均价预测数
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T
    (ID,
     YEAR,
     PERIOD_ID,
     ITEM_CODE,
     ITEM_NAME,
     CATEGORY_CODE,
     CATEGORY_NAME,
     --L4_CEG_CODE,
     --L4_CEG_CN_NAME,
     --L4_CEG_SHORT_CN_NAME,
     --L3_CEG_CODE,
     --L3_CEG_CN_NAME,
     --L3_CEG_SHORT_CN_NAME,
     --L2_CEG_CODE,
     --L2_CEG_CN_NAME,
     --RECEIVE_QTY,
     --RECEIVE_AMT_USD,
     --RECEIVE_AMT_CNY,
     --AVG_PRICE_USD,
     AVG_PRICE_CNY,
     --TOP_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     APPEND_FLAG)
    WITH RECENT_CATEGORY_ITEM_TEMP AS
     (
      --从实际数补齐表取最近一次实际月(即当前预测月-1)的品类信息,item信息和均价信息
      SELECT T.CATEGORY_CODE,
              T.CATEGORY_NAME,
              T.CATEGORY_EN_NAME,
              T.ITEM_CODE,
              T.ITEM_ID,
              T.ITEM_NAME,
              T.YEAR,
              T.PERIOD_ID,
              T.AVG_PRICE_CNY
        FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T T
       WHERE T.PERIOD_ID =
             TO_NUMBER(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1), 'YYYYMM'))),
    
    NO_CATEGORY_ITEM_TEMP AS
     (
      --筛选出实际数历史表中不在预测表中的品类和item
      SELECT A.CATEGORY_CODE, A.ITEM_CODE
        FROM ( --历史表中实际月份存在的品类和item, 取数范围: (三年前第1月)至当前系统月(不含)
               SELECT DISTINCT T.CATEGORY_CODE, T.ITEM_CODE
                 FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T T
                WHERE T.PERIOD_ID >= TO_NUMBER(TO_CHAR(YEAR(CURRENT_TIMESTAMP) - 3) || '01')
                  AND T.PERIOD_ID < V_FCST_PERIOD) A
       WHERE NOT EXISTS
       ( --预测表中筛选出剩余月份都有值item,品类
              SELECT S.CATEGORY_CODE, S.ITEM_CODE
                FROM (SELECT T.CATEGORY_CODE, T.ITEM_CODE, COUNT(1) AS CNT_FLAG
                         FROM FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T T
                        WHERE T.PERIOD_ID >= V_FCST_PERIOD
                          AND T.APPEND_FLAG = 'N'
                        GROUP BY T.CATEGORY_CODE, T.ITEM_CODE) S
               WHERE S.CNT_FLAG = 13 - TO_NUMBER(SUBSTR(TO_CHAR(V_FCST_PERIOD), 5, 6)) --剩余月份数
                 AND A.CATEGORY_CODE = S.CATEGORY_CODE
                 AND A.ITEM_CODE = S.ITEM_CODE)),
    
    CROSS_JOIN_DIM_TEMP AS
     (
      --生成今年预测月份及预测月份之后,不存在的品类和item维
      SELECT A.CATEGORY_CODE,
              A.ITEM_CODE,
              SUBSTR(TO_CHAR(B.PERIOD_ID), 1, 4) AS YEAR,
              B.PERIOD_ID
        FROM NO_CATEGORY_ITEM_TEMP A,
              (SELECT V_FCST_PERIOD + NUM.VAL AS PERIOD_ID
                 FROM GENERATE_SERIES(0, 12 - MONTH(CURRENT_TIMESTAMP), 1) NUM(VAL)) B)
    
    SELECT FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_S.NEXTVAL AS ID,
           A.YEAR,
           A.PERIOD_ID,
           A.ITEM_CODE,
           B.ITEM_NAME,
           A.CATEGORY_CODE,
           B.CATEGORY_NAME,
           --NULL AS L4_CEG_CODE,
           --NULL AS L4_CEG_CN_NAME,
           --NULL AS L4_CEG_SHORT_CN_NAME,
           --NULL AS L3_CEG_CODE,
           --NULL AS L3_CEG_CN_NAME,
           --NULL AS L3_CEG_SHORT_CN_NAME,
           --NULL AS L2_CEG_CODE,
           --NULL AS L2_CEG_CN_NAME,
           --NULL AS RECEIVE_QTY,
           --NULL AS RECEIVE_AMT_USD,
           --NULL AS RECEIVE_AMT_CNY,
           --NULL AS AVG_PRICE_USD,
           DECODE(C.PERIOD_ID, NULL, B.AVG_PRICE_CNY, C.AVG_PRICE_CNY) AS AVG_PRICE_CNY,
           --NULL AS TOP_FLAG,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           'Y' AS APPEND_FLAG
      FROM CROSS_JOIN_DIM_TEMP A --补齐的笛卡尔乘积临时表
      LEFT JOIN RECENT_CATEGORY_ITEM_TEMP B --最近实际月(即当前预测月-1)的品类,item和均价临时表
        ON A.CATEGORY_CODE = B.CATEGORY_CODE
       AND A.ITEM_CODE = B.ITEM_CODE
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T C
        ON A.CATEGORY_CODE = C.CATEGORY_CODE
       AND A.ITEM_CODE = C.ITEM_CODE
       AND A.PERIOD_ID = C.PERIOD_ID
     WHERE C.PERIOD_ID IS NULL;
     
     
  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 2,
   F_CAL_LOG_DESC => '插入新补齐的均价预测数到FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --3.收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T;
  
  --3.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 3,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_T统计信息完成!');

    return 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

