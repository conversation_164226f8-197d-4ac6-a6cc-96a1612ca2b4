-- Name: f_dm_foc_made_month_custom_comb; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_month_custom_comb(f_industry_flag character varying, f_item_version bigint, f_custom_id bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************
创建时间：2023-10-25
创建人  ：黄心蕊 hwx1187045
背景描述：制造月度分析-自选组合数据初始化及新增调用
参数描述：参数一(F_ITEM_VERSION)：通用版本号
		  参数二(F_CUSTOM_ID)：自选组合ID，输入此版本号增量刷新自选组合权重及指数
		  参数三(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
事例    ： SELECT FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_CUSTOM_COMB('',''); --全量刷新一个版本的自选组合权重及指数	
来源表	： FIN_DM_OPT_FOI.DM_FOC_MADE_CUS_BASE_DETAIL_ITEM_T   
目标表	： FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_WEIGHT_T --权重表
		   FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_COST_INDEX_T --指数表
		   FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_RATE_T --同环比
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME        VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_MONTH_CUSTOM_COMB';
  V_STEP_NUM       BIGINT := 0; --函数步骤号
  V_VERSION        BIGINT;
  --V_KEYSTR         VARCHAR(100) := F_KEYSTR; --解密密钥串 
  V_CUSTOM_ID      BIGINT := F_CUSTOM_ID;
  V_SQL_CUSTOM_ID  TEXT; --筛选条件
  V_SQL            TEXT; --执行语句
  V_WEIGHT_YEAR    VARCHAR(20) := YEAR(CURRENT_DATE) - 1 || '-' ||
                                  YEAR(CURRENT_DATE);
  V_BASE_PERIOD_ID INT := TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '01'); --基期会计期
  V_MIN_PERIOD					 VARCHAR(100);
  V_MAX_PERIOD					 VARCHAR(100);
  V_TO_WEIGHT_TABLE		VARCHAR(200);	
  V_TO_IDX_TABLE		VARCHAR(200);
  V_TO_RATE_TABLE		VARCHAR(200);
  V_FROM_TABLE			VARCHAR(200);

  
BEGIN

  X_RESULT_STATUS:='1';
  
  --日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
 V_STEP_NUM:=V_STEP_NUM+1;  

 /* SELECT VERSION_ID INTO V_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;*/
  --入参不为空，则以入参为版本号
  
  
  IF F_INDUSTRY_FLAG = 'I' THEN
  
    SELECT MIN(PERIOD_ID), MAX(PERIOD_ID)
      INTO V_MIN_PERIOD, V_MAX_PERIOD
      FROM FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T;
  
    V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_WEIGHT_T';
    V_TO_IDX_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_COST_INDEX_T';
    V_TO_RATE_TABLE   := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUSTOM_MONTH_RATE_T';
    V_FROM_TABLE      := 'FIN_DM_OPT_FOI.DM_FOC_MADE_CUS_BASE_DETAIL_ITEM_T'; --组合维度表 
  
    IF F_ITEM_VERSION IS NULL THEN
    
      SELECT VERSION_ID
        INTO V_VERSION
        FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
       WHERE DEL_FLAG = 'N'
         AND STATUS = 1
         AND UPPER(DATA_TYPE) = 'ITEM'
       ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
      --20240327 修改版本号取数逻辑
    ELSE
      V_VERSION := F_ITEM_VERSION;
    END IF;
  
  ELSIF F_INDUSTRY_FLAG = 'E' THEN
    --202405版本 新增数字能源部分
  
    SELECT MIN(PERIOD_ID), MAX(PERIOD_ID)
      INTO V_MIN_PERIOD, V_MAX_PERIOD
      FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T;
  
    V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_MONTH_WEIGHT_T';
    V_TO_IDX_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_MONTH_COST_INDEX_T';
    V_TO_RATE_TABLE   := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUSTOM_MONTH_RATE_T';
    V_FROM_TABLE      := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_CUS_BASE_DETAIL_ITEM_T'; --组合维度表 
  
    IF F_ITEM_VERSION IS NULL THEN
    
      SELECT VERSION_ID
        INTO V_VERSION
        FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
       WHERE DEL_FLAG = 'N'
         AND STATUS = 1
         AND UPPER(DATA_TYPE) = 'ITEM'
       ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
      --20240327 修改版本号取数逻辑
    ELSE
      V_VERSION := F_ITEM_VERSION;
    END IF;
	
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
    --202405版本 新增数字能源部分
  
    SELECT MIN(PERIOD_ID), MAX(PERIOD_ID)
      INTO V_MIN_PERIOD, V_MAX_PERIOD
      FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T;
  
    V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_MONTH_WEIGHT_T';
    V_TO_IDX_TABLE    := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_MONTH_COST_INDEX_T';
    V_TO_RATE_TABLE   := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUSTOM_MONTH_RATE_T';
    V_FROM_TABLE      := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_CUS_BASE_DETAIL_ITEM_T'; --组合维度表 
  
    IF F_ITEM_VERSION IS NULL THEN
    
      SELECT VERSION_ID
        INTO V_VERSION
        FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
       WHERE DEL_FLAG = 'N'
         AND STATUS = 1
         AND UPPER(DATA_TYPE) = 'ITEM'
       ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
      --20240327 修改版本号取数逻辑
    ELSE
      V_VERSION := F_ITEM_VERSION;
    END IF;
  
  END IF;
  
  
  IF V_CUSTOM_ID IS NULL THEN
    V_SQL_CUSTOM_ID:='';
  ELSE 
    V_SQL_CUSTOM_ID:= ' AND CUSTOM_ID = '||V_CUSTOM_ID||'' ;
  END IF;
  
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '判断完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  
  V_STEP_NUM:=V_STEP_NUM+1;
  RAISE NOTICE '临时表建表';
  DROP TABLE IF EXISTS DATA_MONTH_CUSTOM_AMT_TEMP;
  CREATE TEMPORARY TABLE DATA_MONTH_CUSTOM_AMT_TEMP(
  	VERSION_ID   BIGINT,
  	PERIOD_YEAR   BIGINT,
  	PERIOD_ID   BIGINT,
  	CUSTOM_ID   BIGINT,                
  	CUSTOM_CN_NAME  VARCHAR(200),   
  	PARENT_CODE      VARCHAR(200),  
  	PARENT_CN_NAME  VARCHAR(1000),  
  	PARENT_LEVEL      VARCHAR(50),  
  	GRANULARITY_TYPE  VARCHAR(2),
	GROUP_CODE      VARCHAR(200),
	GROUP_CN_NAME  VARCHAR(1000),
  	VIEW_FLAG VARCHAR(2),
  	RMB_AVG_AMT  NUMERIC,
  	RMB_COST_AMT NUMERIC,
	APPEND_FLAG VARCHAR(2),
	SCENARIO_FLAG VARCHAR(2),
	CALIBER_FLAG  VARCHAR(2),
	OVERSEA_FLAG	VARCHAR(2),
	LV0_PROD_LIST_CODE	VARCHAR(50),
	LV0_PROD_LIST_CN_NAME  VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY ROUNDROBIN;

  RAISE NOTICE '临时表插数';
  V_SQL:='
  INSERT INTO DATA_MONTH_CUSTOM_AMT_TEMP
    (PERIOD_YEAR,
	 PERIOD_ID,
	 CUSTOM_ID,
	 CUSTOM_CN_NAME,
	 PARENT_CODE,
	 PARENT_CN_NAME,
	 PARENT_LEVEL,
	 GRANULARITY_TYPE,
	 GROUP_CODE,
	 GROUP_CN_NAME,
	 VIEW_FLAG,
	 RMB_AVG_AMT,
	 RMB_COST_AMT,
	 APPEND_FLAG,
	 SCENARIO_FLAG,
	 CALIBER_FLAG,
	 OVERSEA_FLAG,
	 LV0_PROD_LIST_CODE,
	 LV0_PROD_LIST_CN_NAME)
  SELECT PERIOD_YEAR,
         PERIOD_ID,
         CUSTOM_ID,
         CUSTOM_CN_NAME,
         PARENT_CODE,
         PARENT_CN_NAME,
         PARENT_LEVEL,
         GRANULARITY_TYPE,
         GROUP_CODE,
         GROUP_CN_NAME,
         VIEW_FLAG,
		 RMB_AVG_AMT,
         RMB_COST_AMT,
         APPEND_FLAG,
         SCENARIO_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_FROM_TABLE||'
   WHERE PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND YEAR(CURRENT_DATE)
	   AND VERSION_ID = '||V_VERSION|| V_SQL_CUSTOM_ID||';';
   
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
 
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '解密完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  
  V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION|| V_SQL_CUSTOM_ID||';';
   
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '同版本权重删除完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 	
	
  RAISE NOTICE '权重计算';
  V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     PERIOD_YEAR,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
     GROUP_CODE,
     GROUP_CN_NAME,
     WEIGHT_RATE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
    SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
           '||V_VERSION||' AS VERSION_ID,
           '||V_WEIGHT_YEAR||' AS PERIOD_YEAR,
           PARENT_CODE,
           PARENT_CN_NAME,
           PARENT_LEVEL,
           GROUP_CODE,
           GROUP_CN_NAME,
           SUM(RMB_COST_AMT) / SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY CUSTOM_ID, PARENT_CODE, PARENT_CN_NAME, PARENT_LEVEL, GRANULARITY_TYPE, VIEW_FLAG, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE) AS WEIGHT_RATE,
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           GRANULARITY_TYPE,
           VIEW_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
      FROM DATA_MONTH_CUSTOM_AMT_TEMP
     WHERE PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 1) AND
           YEAR(CURRENT_DATE)
       AND APPEND_FLAG = ''N''
       AND SCENARIO_FLAG = ''S''
     GROUP BY GRANULARITY_TYPE,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG,
              LV0_PROD_LIST_CODE,
              LV0_PROD_LIST_CN_NAME,
              PARENT_CODE,
              PARENT_CN_NAME,
              PARENT_LEVEL,
			  GROUP_CODE,
			  GROUP_CN_NAME,
              CUSTOM_ID,
              CUSTOM_CN_NAME;';
	EXECUTE V_SQL;
  
  EXECUTE 'ANALYZE '||V_TO_WEIGHT_TABLE;
  
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '权重插数完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
  
  RAISE NOTICE '指数临时表建表';
  V_STEP_NUM:=V_STEP_NUM+1;
  DROP TABLE IF EXISTS DM_CUSTOM_MID_COST_IDX;
  CREATE TEMPORARY TABLE DM_CUSTOM_MID_COST_IDX(
  	CUSTOM_ID	BIGINT,
	CUSTOM_CN_NAME VARCHAR(1000),
  	BASE_PERIOD_ID	BIGINT,
  	PERIOD_YEAR	VARCHAR(50),
  	PERIOD_ID	VARCHAR(50),
  	PARENT_CODE	VARCHAR(200),
  	PARENT_CN_NAME	VARCHAR(1000),
  	PARENT_LEVEL	VARCHAR(50),
  	GROUP_CODE	VARCHAR(200), 
	GROUP_CN_NAME VARCHAR(1000),
	GROUP_LEVEL VARCHAR(50), 
  	COST_INDEX	NUMERIC,
	APPEND_FLAG	VARCHAR(2),
	SCENARIO_FLAG	VARCHAR(2),
  	GRANULARITY_TYPE	VARCHAR(2),
  	VIEW_FLAG	VARCHAR(2),
  	CALIBER_FLAG	VARCHAR(2),
	OVERSEA_FLAG	VARCHAR(2),
	LV0_PROD_LIST_CODE	VARCHAR(50),
	LV0_PROD_LIST_CN_NAME	VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY ROUNDROBIN; 
  
  RAISE NOTICE 'ITEM指数插数'; 
  WITH BASE_PERIOD_AVG AS
   (SELECT CUSTOM_ID,
           PARENT_CODE,
           PARENT_CN_NAME,
           PARENT_LEVEL,
           GROUP_CODE,
           GROUP_CN_NAME,
           RMB_AVG_AMT AS BASE_AVG,
           GRANULARITY_TYPE,
           VIEW_FLAG,
		   SCENARIO_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
      FROM DATA_MONTH_CUSTOM_AMT_TEMP
     WHERE PERIOD_ID = V_BASE_PERIOD_ID) 
  INSERT INTO DM_CUSTOM_MID_COST_IDX
    (CUSTOM_ID,
	CUSTOM_CN_NAME,
     BASE_PERIOD_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     PARENT_CODE,
     PARENT_CN_NAME,
     PARENT_LEVEL,
  	 GROUP_CODE,
  	 GROUP_CN_NAME,
  	 GROUP_LEVEL,
     COST_INDEX,
     APPEND_FLAG,
     SCENARIO_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
    SELECT T1.CUSTOM_ID,
		   T1.CUSTOM_CN_NAME,
           V_BASE_PERIOD_ID AS BASE_PERIOD_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
		   T1.PARENT_LEVEL,
		   T1.GROUP_CODE,
		   T1.GROUP_CN_NAME,
		   'ITEM' AS GROUP_LEVEL,
           T1.RMB_AVG_AMT / NULLIF(T2.BASE_AVG,0)*100 AS COST_INDEX,
           T1.APPEND_FLAG,
           T1.SCENARIO_FLAG,
           T1.GRANULARITY_TYPE,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME
      FROM DATA_MONTH_CUSTOM_AMT_TEMP T1
      LEFT JOIN BASE_PERIOD_AVG T2
        ON T1.CUSTOM_ID = T2.CUSTOM_ID
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.PARENT_LEVEL = T2.PARENT_LEVEL
       AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       AND T1.LV0_PROD_LIST_CN_NAME = T2.LV0_PROD_LIST_CN_NAME;
	
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => 'ITEM指数插入临时表完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
	
  RAISE NOTICE '其他层级指数插数';
  V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='
  INSERT INTO DM_CUSTOM_MID_COST_IDX
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     BASE_PERIOD_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     SCENARIO_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME)
    SELECT T1.CUSTOM_ID,
           T1.CUSTOM_CN_NAME,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.PARENT_CODE AS GROUP_CODE,
           T1.PARENT_CN_NAME AS GROUP_CN_NAME,
           T1.PARENT_LEVEL AS GROUP_LEVEL,
           SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
           T1.SCENARIO_FLAG,
           T1.GRANULARITY_TYPE,
           T1.VIEW_FLAG,
           T1.CALIBER_FLAG,
           T1.OVERSEA_FLAG,
           T1.LV0_PROD_LIST_CODE,
           T1.LV0_PROD_LIST_CN_NAME
      FROM DM_CUSTOM_MID_COST_IDX T1
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2
        ON T1.CUSTOM_ID = T2.CUSTOM_ID
       AND T1.GROUP_CODE = T2.GROUP_CODE
       AND T1.PARENT_CODE = T2.PARENT_CODE
       AND T1.PARENT_LEVEL = T2.PARENT_LEVEL
       AND T1.GRANULARITY_TYPE = T2.GRANULARITY_TYPE
       AND T1.VIEW_FLAG = T2.VIEW_FLAG
       AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
       AND T1.LV0_PROD_LIST_CN_NAME = T2.LV0_PROD_LIST_CN_NAME
     GROUP BY T1.CUSTOM_ID,
              T1.CUSTOM_CN_NAME ,
			  T1.PERIOD_YEAR,
              T1.PERIOD_ID,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.PARENT_LEVEL,
              T1.SCENARIO_FLAG,
              T1.GRANULARITY_TYPE,
              T1.VIEW_FLAG,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME;  ';
			  
EXECUTE V_SQL;
			
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '自选组合内其他层级指数插表成功',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
	
  
  V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='DELETE FROM '||V_TO_IDX_TABLE||' WHERE VERSION_ID = '||V_VERSION|| V_SQL_CUSTOM_ID||';';
   
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '同版本指数删除完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 	
			
  V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='
  INSERT INTO '||V_TO_IDX_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     BASE_PERIOD_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     APPEND_FLAG,
     SCENARIO_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     PARENT_CODE,
	 PARENT_LEVEL,
	 PARENT_CN_NAME)
    SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
           '||V_VERSION||' AS VERSION_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           ''-1'' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           ''-1'' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           APPEND_FLAG,
           SCENARIO_FLAG,
           GRANULARITY_TYPE,
           VIEW_FLAG,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME,
           PARENT_CODE,
		   PARENT_LEVEL,
		   PARENT_CN_NAME
      FROM DM_CUSTOM_MID_COST_IDX;';
EXECUTE V_SQL;


EXECUTE 'ANALYZE '||V_TO_IDX_TABLE;
	  
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '自选组合指数结果表插数成功',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
	
  
  V_STEP_NUM:=V_STEP_NUM+1;
  V_SQL:='DELETE FROM '||V_TO_RATE_TABLE||' WHERE VERSION_ID = '||V_VERSION|| V_SQL_CUSTOM_ID||';';
   
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
  
  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '同版本同环比删除完成',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 	
	
	
  V_STEP_NUM:=V_STEP_NUM+1;  
  RAISE NOTICE '同环比计算';
  
	V_SQL:= '
  WITH LEV_INDEX AS
   (SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
           VIEW_FLAG,
           PERIOD_YEAR,
           PERIOD_ID,
           SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
           PARENT_CODE,
           PARENT_LEVEL,
		   PARENT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           GRANULARITY_TYPE,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
      FROM '||V_TO_IDX_TABLE||'
     WHERE VERSION_ID = '||V_VERSION||'
	   AND PERIOD_ID BETWEEN '||V_MIN_PERIOD||' AND '||V_MAX_PERIOD||'
       AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||V_SQL_CUSTOM_ID||'),
  
  BASE_YOY AS
   (SELECT CUSTOM_ID,
           CUSTOM_CN_NAME,
           VIEW_FLAG,
           PERIOD_YEAR,
           PERIOD_ID,
           PARENT_CODE,
           PARENT_LEVEL,
		   PARENT_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CUSTOM_ID,VIEW_FLAG, PARENT_CODE, PARENT_LEVEL, GROUP_CODE, GROUP_LEVEL, GRANULARITY_TYPE, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CUSTOM_ID,VIEW_FLAG, PARENT_CODE, PARENT_LEVEL, GROUP_CODE, GROUP_LEVEL, GRANULARITY_TYPE, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
           LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CUSTOM_ID,VIEW_FLAG, PARENT_CODE, PARENT_LEVEL, GROUP_CODE, GROUP_LEVEL, GRANULARITY_TYPE, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
           LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CUSTOM_ID,VIEW_FLAG, PARENT_CODE,PARENT_LEVEL, GROUP_CODE, GROUP_LEVEL, GRANULARITY_TYPE, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE ORDER BY PERIOD_ID) AS POP_COST_INDEX,
           GRANULARITY_TYPE,
           CALIBER_FLAG,
           OVERSEA_FLAG,
           LV0_PROD_LIST_CODE,
           LV0_PROD_LIST_CN_NAME
      FROM LEV_INDEX)
  
  INSERT INTO '||V_TO_RATE_TABLE||'
    (CUSTOM_ID,
     CUSTOM_CN_NAME,
     VERSION_ID,
     BASE_PERIOD_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RATE,
     RATE_FLAG,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     GRANULARITY_TYPE,
     VIEW_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     PARENT_CODE,
     PARENT_LEVEL,
	 PARENT_CN_NAME)
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION||' AS VERSION_ID,
         '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(YOY_COST_INDEX, 0)) - 1) AS RATE,
         ''YOY'' AS RATE_FLAG,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         GRANULARITY_TYPE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         PARENT_CODE,
         PARENT_LEVEL,
		 PARENT_CN_NAME
    FROM BASE_YOY
   WHERE YOY_COST_INDEX IS NOT NULL
  UNION ALL
  SELECT CUSTOM_ID,
         CUSTOM_CN_NAME,
         '||V_VERSION||' AS VERSION_ID,
		 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
         PERIOD_YEAR,
         PERIOD_ID,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ((COST_INDEX / NULLIF(POP_COST_INDEX, 0)) - 1) AS RATE,
         ''POP'' AS RATE_FLAG,
         ''-1'' AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         ''-1'' AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         GRANULARITY_TYPE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         PARENT_CODE,
         PARENT_LEVEL,
		 PARENT_CN_NAME
    FROM BASE_YOY
   WHERE POP_COST_INDEX IS NOT NULL;';
	 
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;

  --写入日志
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '自选组合同环比结果表插数成功',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS'); 
	
    EXECUTE 'ANALYZE '||V_TO_RATE_TABLE;
		 
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

