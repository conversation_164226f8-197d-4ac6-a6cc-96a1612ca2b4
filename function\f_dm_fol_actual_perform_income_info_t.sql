-- Name: f_dm_fol_actual_perform_income_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_actual_perform_income_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：实际履行收益表的加工规则：1、层级：航线；
                                      2、展示收益、累计收益；
                                      3、收益计算公式：a航线Xeneta当月价格*华为当月实际履行货量- a航线当月华为均价*华为当月实际履行货量（只有华为有量，量从航线量表取出）
                                      4、Xeneta 量、价都没有的时候，量用精品海运的补，价用Xeneta的补（优先往前）;
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；2、刷新（页面的刷新价格表、刷新系统）：取java传版本ID；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_actual_perform_income_info_t()
变更记录：2024-6-6 qwx1110218 新增非Top航线的取数逻辑、新增ALL柜型的取数逻辑、新增“比定标价”的取数逻辑；若无定标比例，则计算收益（即比定标价）为０，实际履行份额图中展示为０；
          的加权定标价=供应商的均价*供应商的初始使用比例*sum(供应商的量);
          2024-8-21 qwx1110218 24年9月7号版本逻辑变更：计算成本的柜型量无需将20GP数量×1，40GP数量×2，40HQ数量×2；下载的柜型量字段需20GP数量×1，40GP数量×2，40HQ数量×2；
          2024-9-3  qwx1110218 24年9月版逻辑变更：对于年度分析以及月度分析中比定标价以及比市场价收益、若数据为空，应展示为空，不展示为0；


*/


declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_fol_actual_perform_income_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_fol_actual_perform_income_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_route_version_code  varchar(30);
	v_price_version_code varchar(30);
	v_max_last_update_date timestamp;
	v_version_status      varchar(50);


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '实际履行收益表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建临时表
  drop table if exists actual_perform_income_info_tmp;
  create temporary table actual_perform_income_info_tmp(
         version_id               int               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     int               -- 年份
       , period_id                int               -- 会计期
       , target_type              varchar(50)       -- 目标时点类型（M、YTD、H1、H2、Y等）
       , transport_mode           varchar(50)       -- 运输方式（精品海运）
       , region_cn_name           varchar(50)       -- 区域
       , route                    varchar(200)      -- 航线（起始港_目的港       目的港_国家）
       , source_country_name      varchar(100)      -- 起始国家
       , dest_country_name        varchar(100)      -- 目的国家
       , container_type           varchar(50)       -- 柜型（20GP、40GP、40HQ、ALL）
       , currency                 varchar(10)       -- 币种
       , level_code               varchar(10)       -- 层级编码（01、02、03）
       , level_desc               varchar(100)      -- 层级描述（01 运输方式、02 区域、03 航线）
       , price_comparison_type    varchar(50)       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
       , container_qty            numeric(38,10)    -- 柜型量
       , hw_cost_amt              numeric(38,10)    -- 华为成本金额
       , xeneta_cost_amt          numeric(38,10)    -- Xeneta成本金额
       , posted_cost_amt          numeric(38,10)    -- 定标成本金额
       , income_amt               numeric(38,10)    -- 比对收益（比市场价结果收益、比定标价结果收益）
       , income_ratio             numeric(38,10)    -- 收益率
  )on commit preserve rows distribute by replication
  ;
  
  -- 创建临时表
  drop table if exists year_apd_tmp1;
  create temporary table year_apd_tmp1(year int)on commit preserve rows distribute by replication;

  -- 版本信息表中执行失败的版本ID，目标表中需要清理
  with version_info_2001_tmp as(
  select distinct nvl(version_id,0) as version_id
    from fin_dm_opt_foi.dm_fol_version_info_t
   where step = 2001   -- 执行失败
     and upper(del_flag) = 'N'
  )
  delete from fin_dm_opt_foi.dm_fol_actual_perform_income_info_t where upper(del_flag) = 'N' and nvl(version_id,0) in(select nvl(version_id,0) from version_info_2001_tmp)
  ;

  -- 从航线量汇总表取最大版本ID的数据
  select max(version_id) as max_version_id into v_max_version_id from fin_dm_opt_foi.dm_fol_route_price_info_sum_t where upper(del_flag) = 'N';

  -- 如果是自动调度，版本ID则取版本信息表的最大版本ID+1；如果是刷新按钮，则直接取版本信息表的最大版本ID；
  if((p_version_id is null or p_version_id = '') and (p_refresh_type is null or p_refresh_type = '')) then
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
   where version_id = v_max_version_id
     and source_en_name = 'f_dm_fol_route_info_t'
     and not exists(select distinct version_id
                      from fin_dm_opt_foi.dm_fol_version_info_t t2
                     where t1.version_id = t2.version_id
                       and t2.step in(2001,0,2)
                       and upper(t2.del_flag) = 'N'
                   )
     and upper(t1.del_flag) = 'N'
  ;

  -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
  select max(version_code) as version_code into v_price_version_code
    from fin_dm_opt_foi.dm_fol_version_info_t t1
   where version_id = v_max_version_id
     and source_en_name = 'apd_fol_route_price_info_t'
     and not exists(select distinct version_id
                      from dm_fol_version_info_t t2
                     where t1.version_id = t2.version_id
                       and t2.step in(2001,0,2)
                       and upper(t2.del_flag) = 'N'
                   )
     and upper(t1.del_flag) = 'N'
  ;

  -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_actual_perform_income_info_t'
       and refresh_type = '4_AUTO'
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id   as version_id
         , v_route_version_code as version_code
         , 2 as step
         , 'f_dm_fol_actual_perform_income_info_t' as source_en_name
         , '实际履行收益函数'           as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code 是航线清单表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
     union all
    select v_max_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_actual_perform_income_info_t' as source_en_name
         , '实际履行收益函数'           as source_cn_name
         , '4_AUTO' as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '自动调度，版本ID：'||v_max_version_id||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  elseif((p_version_id is not null or p_version_id <> '') and (p_refresh_type = '1_刷新价格表')) then
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
   where version_id < p_version_id
     and source_en_name = 'f_dm_fol_route_info_t'
     and not exists(select distinct version_id
                      from dm_fol_version_info_t t2
                     where t1.version_id = t2.version_id
                       and t2.step in(2001,0,2)
                       and upper(t2.del_flag) = 'N'
                   )
     and upper(t1.del_flag) = 'N'
  ;

    -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
  select max(version_code) as version_code into v_price_version_code
    from fin_dm_opt_foi.dm_fol_version_info_t t1
   where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '1_刷新价格表'
       and upper(del_flag) = 'N'
       --and step = 2  -- 价格表刷新时，java会更新“价格补录表”的step=2，所有表数据刷新完成后，才更新“价格补录表”的step=1
    ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = p_version_id
       and source_en_name = 'f_dm_fol_actual_perform_income_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select p_version_id   as version_id
         , v_price_version_code as version_code
         , 2 as step
         , 'f_dm_fol_actual_perform_income_info_t' as source_en_name
         , '实际履行收益函数'           as source_cn_name
         , p_refresh_type as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  elseif((p_version_id is not null or p_version_id <> '') and p_refresh_type = '2_刷新系统') then
    -- 从版本表获取航线量汇总表版本ID对应的航线信息表的版本编码
    select max(version_code) as version_code into v_route_version_code
      from fin_dm_opt_foi.dm_fol_version_info_t t1
     where version_id = p_version_id
       and source_en_name is null
       and refresh_type = '2_刷新系统'
       and upper(del_flag) = 'N'
       --and step = 2  -- 系统刷新时，java会更新“航线清单表”的step=2，所有表数据刷新完成后，才更新“航线清单表”的step=1
    ;

  -- 从版本表获取航线量汇总表版本ID对应的物流航线价格补录表的版本编码
  select max(version_code) as version_code into v_price_version_code
    from fin_dm_opt_foi.dm_fol_version_info_t t1
   where version_id < p_version_id
     and source_en_name = 'apd_fol_route_price_info_t'
     and not exists(select distinct version_id
                      from dm_fol_version_info_t t2
                     where t1.version_id = t2.version_id
                       and t2.step in(2001,0,2)
                       and upper(t2.del_flag) = 'N'
                   )
     and upper(t1.del_flag) = 'N'
  ;

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_version_info_t
     where version_id = p_version_id
       and source_en_name = 'f_dm_fol_actual_perform_income_info_t'
       and refresh_type = p_refresh_type
       and step = 1
       and upper(del_flag) = 'N'
    ;

    -- 成功数据写入到版本信息表
    insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select p_version_id   as version_id
         , v_route_version_code as version_code
         , 2 as step
         , 'f_dm_fol_actual_perform_income_info_t' as source_en_name
         , '实际履行收益函数'           as source_cn_name
         , p_refresh_type as refresh_type
         , 'version_code 是航线清单表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type||'，航线清单表的版本编码：'||v_route_version_code||'，价格补录表的版本编码：'||v_price_version_code||'，写入到版本信息表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  end if;

  -- 如果获取的航线信息表的版本编码不为空值
  if(p_refresh_type = '2_刷新系统' and (v_route_version_code is  null or v_route_version_code = '')) then

    -- 开始记录日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '航线信息表的版本编码为空值： '||v_route_version_code||'，需要排查版本信息表的最大版本ID是否有2001（执行失败）、2（执行中）、0（草稿（java））',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    x_success_flag := 2001;
    return;

  -- 如果获取的价格补录表的版本编码不为空值
  elseif(p_refresh_type = '1_刷新价格表' and (v_price_version_code is  null or v_price_version_code = '')) then

    -- 开始记录日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '价格补录表的版本编码为空值： '||v_price_version_code||'，需要排查版本信息表的最大版本ID是否有2001（执行失败）、2（执行中）、0（草稿（java））',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    x_success_flag := 2001;
    return;

  else
    -- 根据版本ID获取的版本编码，取最大更新时间的数据
    -- 取Top航线的数据
    select max(last_update_date) as max_last_update_date into v_max_last_update_date
      from fin_dm_opt_foi.dm_fol_route_info_t
     where version_code = v_route_version_code
       -- and top_route_flag = 'Y'  -- 只取是Top航线的
       and upper(version_status) <> 'ADJUST'  -- 排除ADJUST版本
       and upper(del_flag) = 'N'
    ;

    -- 根据版本版本+最后更新时间获取对应的状态
    select distinct version_status into v_version_status
      from fin_dm_opt_foi.dm_fol_route_info_t
     where version_code = v_route_version_code
       -- and top_route_flag = 'Y'  -- 只取是Top航线的
       and upper(del_flag) = 'N'
       and last_update_date = v_max_last_update_date
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '航线清单表的状态：'||v_version_status||'，数据入到目标表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

    -- 数据入到临时表
    insert into actual_perform_income_info_tmp(
           version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode              -- 运输方式（精品海运）
         , region_cn_name              -- 区域
         , route                       -- 航线（起始港_目的港       目的港_国家）
         , source_country_name         -- 起始国家
         , dest_country_name           -- 目的国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                    -- 币种
         , level_code                  -- 层级编码（01、02、03）
         , level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , container_qty               -- 柜型量
         , hw_cost_amt                 -- 华为成本金额
         , xeneta_cost_amt             -- Xeneta成本金额
         , posted_cost_amt             -- 定标成本金额
         , income_amt                  -- 比对收益（比市场价结果收益、比定标价结果收益）
         , income_ratio                -- 收益率
    )
    -- 取初始使用比例
    with price_ratio_info_tmp as(
    select region_cn_name          -- 区域
         , dest_port_name as route -- 目的港（即航线，目的港_国家）
         , supplier_short_name     -- LST（即供应商）
         , (to_char(begin_date,'yyyymm'))::int as begin_period  -- 开始时间
         , (to_char(end_date,'yyyymm'))::int   as end_period    -- 结束时间
         , init_ratio           -- 初始使用比例
      from fin_dm_opt_foi.apd_fol_price_ratio_info_t
     where upper(status) = 'FINAL'  -- 只取终稿的
       and del_flag = 'N'
    ),
    route_info_sum_tmp3 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , (case when container_type = '20GP' then container_qty
                 when container_type = '40GP' then container_qty/2
                 when container_type = '40HQ' then container_qty/2
            end) as container_qty   -- 柜型量
         , price                    -- 价格
         , row_number() over(partition by version_id, period_id, transport_mode, region_cn_name, route, supplier_short_name, container_type, currency order by price desc) as row_num  -- 同个航线、同个供应商、同个会计期、同个柜型，比定标价用的时候要用最低的价格
      from fin_dm_opt_foi.dm_fol_route_price_info_sum_t
     where version_id = nvl(p_version_id,v_max_version_id)
       and upper(del_flag) = 'N'
    ),
    route_info_sum_tmp6 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , sum(nvl(container_qty,0))   as container_qty               -- 柜型量
      from route_info_sum_tmp3
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
    ),
    -- 同个航线、同个供应商、同个会计期、同个柜型，比定标价用的时候要用最低的价格
    route_info_sum_tmp7 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , t1.route                    -- 航线（目的港_国家）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ）
         , t1.currency                 -- 币种
         , sum(t1.price*t2.init_ratio) as posted_income_amt  -- 定标成本
      from route_info_sum_tmp3 t1
      join price_ratio_info_tmp t2
        on t1.region_cn_name = t2.region_cn_name
       and t1.route = t2.route
       and t1.supplier_short_name = t2.supplier_short_name
       and t1.period_id >= t2.begin_period
       and t1.period_id < t2.end_period
     where t1.row_num = 1
     group by t1.version_id
         , t1.year
         , t1.period_id
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
    ),
    -- 定标成本=min(价格)*初始使用比例*sum(所有柜型量)
    route_info_sum_tmp8 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.transport_mode           -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name           -- 目的地区域
         , t1.route                    -- 航线（目的港_国家）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ）
         , t1.currency                 -- 币种
         , t2.container_qty            -- 柜型量
         , t1.posted_income_amt as posted_income_amt2
         , (t1.posted_income_amt*t2.container_qty) as posted_income_amt  -- 定标成本
      from route_info_sum_tmp7 t1
      left join route_info_sum_tmp6 t2
        on t1.version_id = t2.version_id
       and t1.period_id = t2.period_id
       and t1.transport_mode = t2.transport_mode
       and t1.region_cn_name = t2.region_cn_name
       and t1.route = t2.route
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
    ),
    -- 所有柜型：20GP、40GP、40HQ、ALL
    route_info_sum_tmp4 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , container_qty            -- 柜型量
         , posted_income_amt        -- 定标成本
      from route_info_sum_tmp8
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , 'ALL' as container_type  -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , sum(container_qty)     as container_qty            -- 柜型量
         , sum(posted_income_amt) as posted_income_amt        -- 定标成本
      from route_info_sum_tmp8
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , currency
    ),
    -- 华为成本、Xeneta补均价只用柜型量有值的数据
    route_info_sum_tmp1 as(
    -- 取航线量汇总表的最大版本ID数据
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , supplier_short_name      -- LST（即供应商）
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , sum(nvl(container_qty,0)) as container_qty     -- 柜型量
         , sum(nvl(container_qty,0)*nvl(price,0)) as income_amt  -- 履行成本（即华为成本）
      from route_info_sum_tmp3
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , supplier_short_name
         , container_type
         , currency
    ),
    -- 履行成本（即华为成本）从供应商汇总到航线
    route_info_sum_tmp2 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , sum(container_qty)     as container_qty -- 柜型量
         , sum(income_amt)        as income_amt    -- 履行成本（即华为成本）
      from route_info_sum_tmp1
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
    ),
    route_info_sum_tmp as(
    -- 20GP、40GP、40HQ 3种柜型的数据
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , container_qty            -- 柜型量
         , income_amt               -- 履行成本
      from route_info_sum_tmp2
     union all
    -- 只计算“精品海运”的ALL柜型的数据，因为Xeneta后续还要补价格，所以它的ALL柜型数据补齐价格了再计算
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , 'ALL' as container_type  -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                -- 币种
         , sum(container_qty)     as container_qty  -- 柜型量
         , sum(income_amt)        as income_amt     -- 履行成本
      from route_info_sum_tmp2
     where transport_mode = '精品海运'
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , currency
    ),
    -- 精品海运的会计期有数据的，Xeneta 也一定要有数据，如果没有则优先往前补齐价格，补齐的价格用均价
    -- 精品海运已有的数据
    data_make_up_tmp1 as(
    select distinct version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
      from route_info_sum_tmp
     where transport_mode = '精品海运'
       and container_type <> 'ALL'
    ),
    -- Xeneta 已有数据
    data_make_up_tmp2 as(
    select version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , container_qty
         , income_amt
         , (case when container_qty = 0 then 0 else income_amt/container_qty end) as avg_price  -- 均价
      from route_info_sum_tmp
     where transport_mode = 'Xeneta'
    ),
    -- Xeneta最小会计期的数据
    min_period_tmp as(
    select min(period_id) as min_period_id
         , version_id
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
      from data_make_up_tmp2
     group by version_id
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
    ),
    -- Xeneta已有数据关联精品海运数据，标识出 Xeneta需要补齐的会计期数据
    data_make_up_tmp3 as(
    select t1.version_id
         , t1.year
         , t1.period_id
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
         , (case when t2.period_id is null then 'Y' else 'N' end) as apd_flag  -- Xeneta 补齐标识
      from data_make_up_tmp1 t1
      left join data_make_up_tmp2 t2
        on t1.version_id          = t2.version_id
       and t1.year                = t2.year
       and t1.period_id           = t2.period_id
       and t1.region_cn_name      = t2.region_cn_name
       and t1.route               = t2.route
       and t1.source_country_name = t2.source_country_name
       and t1.dest_country_name   = t2.dest_country_name
       and t1.container_type      = t2.container_type
       and t1.currency            = t2.currency
    ),
    -- Xeneta 补齐会计期与已有会计期数据
    data_make_up_tmp4 as(
    select version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , null as container_qty
         , null as apd_income_amt
         , '' as apd_period
         , 'Y' as apd_flag   -- Xeneta补齐标识（Y 是）
      from data_make_up_tmp3
     where apd_flag = 'Y'
     union all
    select version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , container_qty
         , income_amt as apd_income_amt
         , period_id as apd_period
         , 'N' as apd_flag
      from data_make_up_tmp2
    ),
    data_make_up_tmp5 as(
    select version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , container_qty
         , apd_income_amt
         , apd_period
         , apd_flag
         , count(apd_period) over(partition by version_id
                                          , region_cn_name
                                          , route
                                          , container_type
                                          , currency
                                      order by period_id
                                          , apd_period
                                 ) as cn
      from data_make_up_tmp4
    ),
    data_make_up_tmp6 as(
    select t1.version_id
         , t1.year
         , t1.period_id
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
         , t1.container_qty
         , t1.apd_income_amt
         , t1.apd_period
         , t1.apd_flag
         , t1.cn
         , nvl((case when t1.cn = 0 then t3.min_period_id end),(max(t1.apd_period) over(partition by t1.version_id,t1.region_cn_name,t1.route,t1.container_type,t1.currency order by t1.period_id,t1.apd_period))) as copy_period
      from data_make_up_tmp5 t1
      /*left join data_make_up_tmp2 t2
        on t1.version_id          = t2.version_id
       and t1.year                = t2.year
       and t1.period_id           = t2.period_id
       and t1.region_cn_name      = t2.region_cn_name
       and t1.route               = t2.route
       and t1.container_type      = t2.container_type
       and t1.currency            = t2.currency
       */
      left join min_period_tmp t3
        on t1.version_id          = t3.version_id
       and t1.region_cn_name      = t3.region_cn_name
       and t1.route               = t3.route
       and t1.container_type      = t3.container_type
       and t1.currency            = t3.currency
    ),
    data_make_up_tmp7 as(
    select t1.version_id
         , t1.year
         , t1.period_id
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
         , nvl(t1.container_qty,t3.container_qty) as container_qty  -- 没有量则用“精品海运”的量补齐
         , t1.apd_income_amt
         , t1.apd_period
         , t1.apd_flag
         , t1.cn
         , t1.copy_period
         , (case when t1.apd_flag = 'Y' then t2.avg_price*t3.container_qty else t2.income_amt end) as income_amt -- 补齐的用均价
      from data_make_up_tmp6 t1
      left join data_make_up_tmp2 t2
        on t1.version_id          = t2.version_id
       and t1.copy_period         = t2.period_id
       and t1.region_cn_name      = t2.region_cn_name
       and t1.route               = t2.route
       and t1.container_type      = t2.container_type
       and t1.currency            = t2.currency
      left join route_info_sum_tmp2 t3
        on t1.version_id          = t3.version_id
       and t1.period_id           = t3.period_id
       and t1.region_cn_name      = t3.region_cn_name
       and t1.route               = t3.route
       and t1.container_type      = t3.container_type
       and t1.currency            = t3.currency
       and t3.transport_mode = '精品海运'
    ),
    -- Xeneta 所有柜型：20GP、40GP、40HQ、ALL
    data_make_up_tmp8 as(
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , container_qty
         , income_amt
      from data_make_up_tmp7
     union all
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , 'ALL' as container_type
         , currency
         , sum(container_qty) as container_qty
         , sum(income_amt)    as income_amt
      from data_make_up_tmp7
     group by version_id
         , year
         , period_id
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , currency
    ),
    route_info_all_tmp as(
    -- 华为成本
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , container_qty            -- 柜型量
         , income_amt     as hw_cost_amt	   -- 华为成本金额
         , null::numeric  as xeneta_cost_amt -- Xeneta成本金额
         , income_amt                        -- 履行成本
         , null::numeric  as posted_income_amt  -- 定标成本
      from route_info_sum_tmp
     where transport_mode = '精品海运'
     union all
    -- Xeneta
    select version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , container_qty
         , null::numeric  as hw_cost_amt	     -- 华为成本金额
         , income_amt     as xeneta_cost_amt   -- Xeneta成本金额
         , income_amt
         , null::numeric  as posted_income_amt -- 加权定标价
      from data_make_up_tmp8
     union all
    -- 定标成本
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route                    -- 航线（目的港_国家）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency                 -- 币种
         , container_qty            -- 柜型量
         , null::numeric as hw_cost_amt	     -- 华为成本金额
         , null::numeric as xeneta_cost_amt  -- Xeneta成本金额
         , null::numeric as income_amt       -- 履行成本
         , posted_income_amt        -- 定标成本
      from route_info_sum_tmp4
    ),
    -- Xeneta 按月、YTD、年按层级汇总，比市场价收益率会用到
    -- Xeneta按月汇总
    xeneta_info_tmp1 as(
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , 'M' as target_type
         , '03'   as level_code        -- 航线层级编码
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , container_qty
         , income_amt
      from data_make_up_tmp8
     union all
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , 'M' as target_type
         , '02'   as level_code        -- 区域层级编码
         , region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , sum(container_qty) as container_qty
         , sum(income_amt)    as income_amt
      from data_make_up_tmp8
     group by version_id
         , year
         , period_id
         , region_cn_name
         , container_type
         , currency
     union all
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , 'M' as target_type
         , '01'   as level_code        -- 运输方式层级编码
         , '' as region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , sum(container_qty) as container_qty
         , sum(income_amt)    as income_amt
      from data_make_up_tmp8
     group by version_id
         , year
         , period_id
         , container_type
         , currency
    ),
    -- Xeneta按YTD汇总
    xeneta_info_tmp2 as(
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , 'YTD'    as target_type
         , level_code        -- 航线层级编码
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , sum(container_qty) over(partition by version_id
                                           , year
                                           , level_code
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as container_qty
         , sum(income_amt) over(partition by version_id
                                        , year
                                        , level_code
                                        , region_cn_name
                                        , route
                                        , source_country_name
                                        , dest_country_name
                                        , container_type
                                        , currency
                                    order by period_id
                               ) as income_amt
      from xeneta_info_tmp1
     where level_code = '03'  -- 航线
     union all
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , 'YTD'    as target_type
         , level_code        -- 区域层级编码
         , region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , sum(container_qty) over(partition by version_id
                                           , year
                                           , level_code
                                           , region_cn_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as container_qty
         , sum(income_amt) over(partition by version_id
                                        , year
                                        , level_code
                                        , region_cn_name
                                        , container_type
                                        , currency
                                    order by period_id
                               ) as income_amt
      from xeneta_info_tmp1
     where level_code = '02'  -- 区域
     union all
    select version_id
         , year
         , period_id
         , 'Xeneta' as transport_mode
         , 'YTD'    as target_type
         , level_code        -- 运输方式层级编码
         , '' as region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , sum(container_qty) over(partition by version_id
                                           , year
                                           , level_code
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as container_qty
         , sum(income_amt) over(partition by version_id
                                        , year
                                        , level_code
                                        , container_type
                                        , currency
                                    order by period_id
                               ) as income_amt
      from xeneta_info_tmp1
     where level_code = '01'  -- 运输方式
    ),
    -- Xeneta按年汇总
    xeneta_info_tmp3 as(
    select version_id
         , year
         , 'Xeneta' as transport_mode
         , 'Y' as target_type
         , level_code
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , sum(container_qty) as container_qty
         , sum(income_amt)    as income_amt
      from xeneta_info_tmp1
     where level_code = '03'  -- 航线
     group by version_id
         , year
         , level_code
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
     union all
    select version_id
         , year
         , 'Xeneta' as transport_mode
         , 'Y' as target_type
         , level_code
         , region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , sum(container_qty) as container_qty
         , sum(income_amt)    as income_amt
      from xeneta_info_tmp1
     where level_code = '02'  -- 区域
     group by version_id
         , year
         , level_code
         , region_cn_name
         , container_type
         , currency
     union all
    select version_id
         , year
         , 'Xeneta' as transport_mode
         , 'Y' as target_type
         , level_code
         , '' as region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , sum(container_qty) as container_qty
         , sum(income_amt)    as income_amt
      from xeneta_info_tmp1
     where level_code = '01'  -- 运输方式
     group by version_id
         , year
         , level_code
         , container_type
         , currency
    ),
    act_perform_income_m_tmp3 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                -- 币种
         , container_qty            -- 柜型量
         , income_amt
      from route_info_all_tmp
     where transport_mode = 'Xeneta'
    ),
    act_perform_income_m_tmp2 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                -- 币种
         , container_qty            -- 柜型量
         , income_amt
      from route_info_all_tmp
     where transport_mode = '精品海运'
    ),
    -- 比市场价航线月度收益临时表
    act_perform_income_m_tmp4 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency	               -- 币种
         , t1.container_qty            -- 柜型量
         , (t1.income_amt-t2.income_amt) as income_amt -- 比市场价收益
      from act_perform_income_m_tmp3 t1
      left join act_perform_income_m_tmp2 t2
        on t1.version_id = t2.version_id
       and t1.period_id = t2.period_id
       and t1.region_cn_name = t2.region_cn_name
       and t1.route = t2.route
       and t1.source_country_name = t2.source_country_name
       and t1.dest_country_name = t2.dest_country_name
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
    ),
    act_perform_income_m_tmp6 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ）
         , t1.currency	               -- 币种
         , t1.container_qty            -- 柜型量
         , t1.income_amt as hw_cost_amt	      -- 华为成本金额
         , t2.income_amt as xeneta_cost_amt   -- Xeneta成本金额
         , t3.posted_income_amt        -- 定标成本
      from route_info_sum_tmp t1
      left join data_make_up_tmp8 t2
        on t1.version_id = t2.version_id
       and t1.period_id = t2.period_id
       and t1.region_cn_name = t2.region_cn_name
       and t1.route = t2.route
       and t1.source_country_name = t2.source_country_name
       and t1.dest_country_name = t2.dest_country_name
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
      left join route_info_sum_tmp4 t3
        on t1.version_id = t3.version_id
       and t1.period_id = t3.period_id
       and t1.transport_mode = t3.transport_mode
       and t1.region_cn_name = t3.region_cn_name
       and t1.route = t3.route
       and t1.source_country_name = t3.source_country_name
       and t1.dest_country_name = t3.dest_country_name
       and t1.container_type = t3.container_type
       and t1.currency = t3.currency
     where t1.transport_mode = '精品海运'
    ),
    -- 比市场价航线月度收益临时表
    act_perform_income_m_tmp1 as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , 'M' as target_type          -- 目标时点类型（M、YTD、H1、H2等）
         , '精品海运' as transport_mode  -- 运输方式（精品海运）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency	               -- 币种
         , '03'   as level_code        -- 层级编码（01、02、03）
         , '航线' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty            -- 柜型量
         , t1.income_amt     -- 比市场价收益
         , t2.hw_cost_amt	   -- 华为成本金额
         , t2.xeneta_cost_amt   -- Xeneta成本金额
         , t2.posted_income_amt -- 定标成本
      from act_perform_income_m_tmp4 t1
      left join act_perform_income_m_tmp6 t2
        on t1.version_id = t2.version_id
       and t1.period_id = t2.period_id
       and t1.region_cn_name = t2.region_cn_name
       and t1.route = t2.route
       and t1.source_country_name = t2.source_country_name
       and t1.dest_country_name = t2.dest_country_name
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
     where t1.income_amt is not null
    ),
    -- 比市场价月度收益（航线、区域、运输方式）
    act_perform_income_m_tmp as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , target_type              -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                -- 币种
         , level_code               -- 层级编码（01、02、03）
         , level_desc               -- 层级描述（01 运输方式、02 区域、03 航线）
         , container_qty            -- 柜型量
         , hw_cost_amt              -- 华为成本金额
         , xeneta_cost_amt          -- Xeneta成本金额
         , posted_income_amt        -- 加权定标价(量*价*初始比例)
         , income_amt               -- 收益（比市场价）
      from act_perform_income_m_tmp1
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , target_type              -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , '' as route	               -- 航线（起始港_目的港）
         , '' as source_country_name   -- 起运地国家
         , '' as dest_country_name     -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                -- 币种
         , '02'   as level_code        -- 层级编码（01、02、03）
         , '区域' as level_desc        -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty)   as container_qty   -- 柜型量
         , sum(hw_cost_amt)     as hw_cost_amt     -- 华为成本金额
         , sum(xeneta_cost_amt) as xeneta_cost_amt -- Xeneta成本金额
         , sum(posted_income_amt) as posted_income_amt    -- 加权定标价(量*价*初始比例)
         , sum(xeneta_cost_amt-hw_cost_amt) as income_amt -- 收益
      from act_perform_income_m_tmp1
     group by version_id
         , year
         , period_id
         , target_type
         , transport_mode
         , region_cn_name
         , container_type
         , currency
     union all
    select version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode              -- 运输方式（精品海运）
         , '' as region_cn_name        -- 目的地区域
         , '' as route	               -- 航线（起始港_目的港）
         , '' as source_country_name   -- 起运地国家
         , '' as dest_country_name     -- 目的地国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                   -- 币种
         , '01'       as level_code    -- 层级编码（01、02、03）
         , '运输方式' as level_desc    -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty)   as container_qty   -- 柜型量
         , sum(hw_cost_amt)     as hw_cost_amt     -- 华为成本金额
         , sum(xeneta_cost_amt) as xeneta_cost_amt -- Xeneta成本金额
         , sum(posted_income_amt) as posted_income_amt    -- 加权定标价(量*价*初始比例)
         , sum(xeneta_cost_amt-hw_cost_amt) as income_amt -- 收益
      from act_perform_income_m_tmp1
     group by version_id
         , year
         , period_id
         , target_type
         , transport_mode
         , container_type
         , currency
    ),
    -- 比市场价运输方式、区域、航线每月的收益率的计算逻辑
    market_income_ratio_m_tmp as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.target_type              -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode           -- 运输方式（精品海运）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency	               -- 币种
         , t1.level_code               -- 层级编码（01、02、03）
         , t1.level_desc               -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty            -- 柜型量
         , t1.hw_cost_amt              -- 华为成本金额
         , t1.xeneta_cost_amt          -- Xeneta成本金额
         , t1.posted_income_amt        -- 加权定标价(量*价*初始比例)
         , t1.income_amt               -- 收益
         , t2.income_amt as xeneta_income_amt
         , (case when t2.income_amt = 0 then 0 else t1.income_amt/t2.income_amt end) as income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from act_perform_income_m_tmp t1
      left join xeneta_info_tmp1 t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.period_id = t2.period_id
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
    ),
    -- 比市场价累计收益的计算逻辑
    act_perform_income_ytd_tmp as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , 'YTD' as target_type     -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , '03'   as level_code     -- 层级编码（01、02、03）
         , '航线' as level_desc     -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as container_qty            -- 柜型量
         , sum(hw_cost_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as hw_cost_amt              -- 华为成本金额
         , sum(xeneta_cost_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as xeneta_cost_amt          -- Xeneta成本金额
         , sum(posted_income_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as posted_income_amt        -- 加权定标价(量*价*初始比例)
         , sum(income_amt) over(partition by version_id
                                        , year
                                        , transport_mode
                                        , region_cn_name
                                        , route
                                        , source_country_name
                                        , dest_country_name
                                        , container_type
                                        , currency
                                    order by period_id
                               ) as income_amt  -- 收益
      from act_perform_income_m_tmp
     where level_code = '03'
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , 'YTD' as target_type     -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , '02'   as level_code     -- 层级编码（01、02、03）
         , '区域' as level_desc     -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as container_qty            -- 柜型量
         , sum(hw_cost_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as hw_cost_amt              -- 华为成本金额
         , sum(xeneta_cost_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as xeneta_cost_amt          -- Xeneta成本金额
         , sum(posted_income_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as posted_income_amt        -- 加权定标价(量*价*初始比例)
         , sum(income_amt) over(partition by version_id
                                        , year
                                        , transport_mode
                                        , region_cn_name
                                        , route
                                        , source_country_name
                                        , dest_country_name
                                        , container_type
                                        , currency
                                    order by period_id
                               ) as income_amt  -- 收益
      from act_perform_income_m_tmp
     where level_code = '02'
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , 'YTD' as target_type     -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , '01'       as level_code -- 层级编码（01、02、03）
         , '运输方式' as level_desc -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as container_qty  -- 柜型量
         , sum(hw_cost_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as hw_cost_amt              -- 华为成本金额
         , sum(xeneta_cost_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as xeneta_cost_amt          -- Xeneta成本金额
         , sum(posted_income_amt) over(partition by version_id
                                           , year
                                           , transport_mode
                                           , region_cn_name
                                           , route
                                           , source_country_name
                                           , dest_country_name
                                           , container_type
                                           , currency
                                       order by period_id
                                  ) as posted_income_amt        -- 加权定标价(量*价*初始比例)
         , sum(income_amt) over(partition by version_id
                                        , year
                                        , transport_mode
                                        , region_cn_name
                                        , route,source_country_name
                                        , dest_country_name
                                        , container_type
                                        , currency
                                    order by period_id
                               ) as income_amt  -- 收益
      from act_perform_income_m_tmp
     where level_code = '01'
    ),
    -- 比市场价运输方式、区域、航线YTD的收益率的计算逻辑
    market_income_ratio_ytd_tmp as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.target_type              -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode           -- 运输方式（精品海运）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency	               -- 币种
         , t1.level_code               -- 层级编码（01、02、03）
         , t1.level_desc               -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty            -- 柜型量
         , t1.hw_cost_amt              -- 华为成本金额
         , t1.xeneta_cost_amt          -- Xeneta成本金额
         , t1.posted_income_amt        -- 加权定标价(量*价*初始比例)
         , t1.income_amt               -- 收益
         , t2.income_amt as xeneta_income_amt
         , (case when t2.income_amt = 0 then 0 else t1.income_amt/t2.income_amt end) as income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from act_perform_income_ytd_tmp t1
      left join xeneta_info_tmp2 t2   -- 各层级的加权xeneta价*履行量
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.period_id = t2.period_id
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
    ),
    -- 比市场价年收益的计算逻辑
    act_perform_income_y_tmp as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , (year||'00')::int as period_id   -- 会计期
         , 'Y' as target_type       -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , '03'   as level_code     -- 层级编码（01、02、03）
         , '航线' as level_desc     -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) as container_qty -- 柜型量
         , sum(hw_cost_amt)   as hw_cost_amt   -- 华为成本金额
         , sum(xeneta_cost_amt)   as xeneta_cost_amt    -- Xeneta成本金额
         , sum(posted_income_amt) as posted_income_amt  -- 加权定标价(量*价*初始比例)
         , sum(income_amt)    as income_amt    -- 收益
      from act_perform_income_m_tmp
     where level_code = '03'
     group by version_id
         , year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , (year||'00')::int as period_id   -- 会计期
         , 'Y' as target_type       -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , '02'   as level_code     -- 层级编码（01、02、03）
         , '区域' as level_desc     -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) as container_qty -- 柜型量
         , sum(hw_cost_amt)   as hw_cost_amt   -- 华为成本金额
         , sum(xeneta_cost_amt)   as xeneta_cost_amt    -- Xeneta成本金额
         , sum(posted_income_amt) as posted_income_amt  -- 加权定标价(量*价*初始比例)
         , sum(income_amt)    as income_amt    -- 收益
      from act_perform_income_m_tmp
     where level_code = '02'
     group by version_id
         , year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
     union all
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , (year||'00')::int as period_id   -- 会计期
         , 'Y' as target_type       -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode           -- 运输方式（精品海运）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , '01'       as level_code -- 层级编码（01、02、03）
         , '运输方式' as level_desc -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) as container_qty -- 柜型量
         , sum(hw_cost_amt)   as hw_cost_amt   -- 华为成本金额
         , sum(xeneta_cost_amt)   as xeneta_cost_amt    -- Xeneta成本金额
         , sum(posted_income_amt) as posted_income_amt  -- 加权定标价(量*价*初始比例)
         , sum(income_amt)    as income_amt    -- 收益
      from act_perform_income_m_tmp
     where level_code = '01'
     group by version_id
         , year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
    ),
    -- 比市场价运输方式、区域、航线汇总年的收益率的计算逻辑
    market_income_ratio_y_tmp as(
    select t1.version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                     -- 年份
         , t1.period_id                -- 会计期
         , t1.target_type              -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode           -- 运输方式（精品海运）
         , t1.region_cn_name           -- 目的地区域
         , t1.route	                   -- 航线（起始港_目的港）
         , t1.source_country_name      -- 起运地国家
         , t1.dest_country_name        -- 目的地国家
         , t1.container_type           -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency	               -- 币种
         , t1.level_code               -- 层级编码（01、02、03）
         , t1.level_desc               -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty            -- 柜型量
         , t1.hw_cost_amt              -- 华为成本金额
         , t1.xeneta_cost_amt          -- Xeneta成本金额
         , t1.posted_income_amt        -- 加权定标价
         , t1.income_amt               -- 收益
         , t2.income_amt as xeneta_income_amt
         , (case when t2.income_amt = 0 then 0 else t1.income_amt/t2.income_amt end) as income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from act_perform_income_y_tmp t1
      left join xeneta_info_tmp3 t2  -- 各层级的加权xeneta价*履行量
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
    ),

    /***************************************************************** 比定标价 start*****************************************************************/
    -- 每月的加权定标价
    posted_price_info_m_tmp1 as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , container_qty            -- 柜型量
         , posted_income_amt        -- 加权定标价(sum(均价*初始使用比例)*sum(量))
      from route_info_sum_tmp4
     where transport_mode = '精品海运'
    ),
    -- 计算月度航线的比定标收益
    posted_price_info_m_tmp as(
    select version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                     -- 年份
         , period_id                -- 会计期
         , transport_mode           -- 运输方式（精品海运、Xeneta）
         , region_cn_name           -- 目的地区域
         , route	                  -- 航线（起始港_目的港）
         , source_country_name      -- 起运地国家
         , dest_country_name        -- 目的地国家
         , container_type           -- 柜型（20GP、40GP、40HQ）
         , currency	                -- 币种
         , 'M'    as target_type    -- 目标时点类型（M、YTD、H1、H2、Y等）
         , '03'   as level_code     -- 层级编码（01、02、03）
         , '航线' as level_desc     -- 层级描述（01 运输方式、02 区域、03 航线）
         , container_qty            -- 柜型量
         , posted_income_amt        -- 加权定标价
      from posted_price_info_m_tmp1
     where transport_mode = '精品海运'
   ),
   -- 各层级的比定标价收益
    -- 月度航线层级比定标价收益
    posted_price_info_tmp as(
    select t1.version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                    -- 年份
         , t1.period_id               -- 会计期
         , t1.transport_mode          -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name          -- 目的地区域
         , t1.route                   -- 航线（起始港_目的港）
         , t1.source_country_name     -- 起运地国家
         , t1.dest_country_name       -- 目的地国家
         , t1.container_type          -- 柜型（20GP、40GP、40HQ）
         , t1.currency                -- 币种
         , t1.target_type             -- 目标时点类型（M、YTD、H1、H2、Y等）
         , t1.level_code              -- 层级编码（01、02、03）
         , t1.level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty           -- 柜型量
         , nvl(t1.posted_income_amt,0) as posted_cost_amt  -- 定标成本金额
         , (case when nvl(t1.posted_income_amt,0) = 0 then 0
                 else (nvl(t1.posted_income_amt,0) - nvl(t2.hw_cost_amt,0))
            end) as posted_income_amt -- 比定标收益  -- 如果定价比例为空，直接收益为0，如果定标比例有值，就计算；如果定价比例为空，直接收益为0，如果定标比例有值，就计算（这个逻辑是针对单航线），但22年所有航线数据都无初始使用比例，那卷积出来就是0
      from posted_price_info_m_tmp t1
      left join act_perform_income_m_tmp t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.period_id = t2.period_id
       and t1.transport_mode = t2.transport_mode
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
    ),
    -- 月度各层级比定标价收益，通过航线层级卷积
    posted_price_m_info_tmp as(
    select version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                    -- 年份
         , period_id               -- 会计期
         , transport_mode          -- 运输方式（精品海运、Xeneta）
         , region_cn_name          -- 目的地区域
         , route                   -- 航线（起始港_目的港）
         , source_country_name     -- 起运地国家
         , dest_country_name       -- 目的地国家
         , container_type          -- 柜型（20GP、40GP、40HQ）
         , currency                -- 币种
         , target_type             -- 目标时点类型（M、YTD、H1、H2、Y等）
         , level_code              -- 层级编码（01、02、03）
         , level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , container_qty           -- 柜型量
         , posted_cost_amt         -- 定标成本金额
         , posted_income_amt       -- 比定标收益  -- 如果定价比例为空，直接收益为0，如果定标比例有值，就计算；如果定价比例为空，直接收益为0，如果定标比例有值，就计算（这个逻辑是针对单航线），但22年所有航线数据都无初始使用比例，那卷积出来就是0
      from posted_price_info_tmp  -- 月度航线层级比定标价收益临时表
     union all
    select version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , target_type
         , '02'   as level_code
         , '区域' as level_desc
         , sum(container_qty)     as container_qty
         , sum(posted_cost_amt)   as posted_cost_amt
         , sum(posted_income_amt) as posted_income_amt
      from posted_price_info_tmp  -- 月度航线层级比定标价收益临时表
     group by version_id
         , year
         , period_id
         , transport_mode
         , region_cn_name
         , container_type
         , currency
         , target_type
     union all
    select version_id
         , year
         , period_id
         , transport_mode
         , '' as region_cn_name
         , '' as route
         , '' as source_country_name
         , '' as dest_country_name
         , container_type
         , currency
         , target_type
         , '01'   as level_code
         , '运输方式' as level_desc
         , sum(container_qty)     as container_qty
         , sum(posted_cost_amt)   as posted_cost_amt
         , sum(posted_income_amt) as posted_income_amt
      from posted_price_info_tmp  -- 月度航线层级比定标价收益临时表
     group by version_id
         , year
         , period_id
         , transport_mode
         , container_type
         , currency
         , target_type
    ),
    -- 月累计各层级的比定标价收益直接用月度的卷积
    posted_price_ytd_info_tmp as(
    select version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                    -- 年份
         , period_id               -- 会计期
         , transport_mode          -- 运输方式（精品海运、Xeneta）
         , region_cn_name          -- 目的地区域
         , route                   -- 航线（起始港_目的港）
         , source_country_name     -- 起运地国家
         , dest_country_name       -- 目的地国家
         , container_type          -- 柜型（20GP、40GP、40HQ）
         , currency                -- 币种
         , 'YTD' as target_type    -- 目标时点类型（M、YTD、H1、H2、Y等）
         , level_code              -- 层级编码（01、02、03）
         , level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) over(partition by version_id
                                               , year
                                               , transport_mode
                                               , region_cn_name
                                               , route
                                               , source_country_name
                                               , dest_country_name
                                               , container_type
                                               , currency
                                               , level_code
                                               , level_desc
                                           order by period_id
                                      ) as container_qty           -- 柜型量
         , sum(posted_cost_amt) over(partition by version_id
                                               , year
                                               , transport_mode
                                               , region_cn_name
                                               , route
                                               , source_country_name
                                               , dest_country_name
                                               , container_type
                                               , currency
                                               , level_code
                                               , level_desc
                                           order by period_id
                                      ) as posted_cost_amt         -- 定标成本金额
         , sum(posted_income_amt) over(partition by version_id
                                               , year
                                               , transport_mode
                                               , region_cn_name
                                               , route
                                               , source_country_name
                                               , dest_country_name
                                               , container_type
                                               , currency
                                               , level_code
                                               , level_desc
                                           order by period_id
                                      ) as posted_income_amt       -- 比定标收益
      from posted_price_m_info_tmp  -- 月度各层级比定标价收益临时表
    ),
    -- 年度各层级的比定标价收益直接用月度的卷积
    posted_price_y_info_tmp as(
    select version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                    -- 年份
         , (year||'00')::int as period_id   -- 会计期
         , transport_mode          -- 运输方式（精品海运、Xeneta）
         , region_cn_name          -- 目的地区域
         , route                   -- 航线（起始港_目的港）
         , source_country_name     -- 起运地国家
         , dest_country_name       -- 目的地国家
         , container_type          -- 柜型（20GP、40GP、40HQ）
         , currency                -- 币种
         , 'Y' as target_type      -- 目标时点类型（M、YTD、H1、H2、Y等）
         , level_code              -- 层级编码（01、02、03）
         , level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty)     as container_qty     -- 柜型量
         , sum(posted_cost_amt)   as posted_cost_amt   -- 定标成本金额
         , sum(posted_income_amt) as posted_income_amt -- 比定标收益
      from posted_price_m_info_tmp  -- 月度各层级比定标价收益临时表
     group by version_id
         , year
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
    ),
    -- 各层级的比定标价收益率
    posted_price_info_all_tmp as(
    select t1.version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                    -- 年份
         , t1.period_id               -- 会计期
         , t1.transport_mode          -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name          -- 目的地区域
         , t1.route                   -- 航线（起始港_目的港）
         , t1.source_country_name     -- 起运地国家
         , t1.dest_country_name       -- 目的地国家
         , t1.container_type          -- 柜型（20GP、40GP、40HQ）
         , t1.currency                -- 币种
         , t1.target_type             -- 目标时点类型（M、YTD、H1、H2、Y等）
         , t1.level_code              -- 层级编码（01、02、03）
         , t1.level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty           -- 柜型量
         , t1.posted_cost_amt         -- 定标成本金额
         , t1.posted_income_amt       -- 加权定标价收益
         , (case when t2.posted_income_amt = 0 then 0 else t1.posted_income_amt/t2.posted_income_amt end) as posted_income_ratio -- 加权定标收益率
      from posted_price_m_info_tmp t1
      left join act_perform_income_m_tmp t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.period_id = t2.period_id
       and t1.transport_mode = t2.transport_mode
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
     where t1.target_type = 'M'
     union all
    select t1.version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                    -- 年份
         , t1.period_id               -- 会计期
         , t1.transport_mode          -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name          -- 目的地区域
         , t1.route                   -- 航线（起始港_目的港）
         , t1.source_country_name     -- 起运地国家
         , t1.dest_country_name       -- 目的地国家
         , t1.container_type          -- 柜型（20GP、40GP、40HQ）
         , t1.currency                -- 币种
         , t1.target_type             -- 目标时点类型（M、YTD、H1、H2、Y等）
         , t1.level_code              -- 层级编码（01、02、03）
         , t1.level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty           -- 柜型量
         , t1.posted_cost_amt         -- 定标成本金额
         , t1.posted_income_amt       -- 加权定标价收益
         , (case when t2.posted_income_amt = 0 then 0 else t1.posted_income_amt/t2.posted_income_amt end) as posted_income_ratio -- 加权定标收益率
      from posted_price_ytd_info_tmp t1
      left join act_perform_income_ytd_tmp t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.period_id = t2.period_id
       and t1.transport_mode = t2.transport_mode
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
     where t1.target_type = 'YTD'
     union all
    select t1.version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                    -- 年份
         , t1.period_id               -- 会计期
         , t1.transport_mode          -- 运输方式（精品海运、Xeneta）
         , t1.region_cn_name          -- 目的地区域
         , t1.route                   -- 航线（起始港_目的港）
         , t1.source_country_name     -- 起运地国家
         , t1.dest_country_name       -- 目的地国家
         , t1.container_type          -- 柜型（20GP、40GP、40HQ）
         , t1.currency                -- 币种
         , t1.target_type             -- 目标时点类型（M、YTD、H1、H2、Y等）
         , t1.level_code              -- 层级编码（01、02、03）
         , t1.level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.container_qty           -- 柜型量
         , t1.posted_cost_amt         -- 定标成本金额
         , t1.posted_income_amt       -- 加权定标价收益
         , (case when t2.posted_income_amt = 0 then 0 else t1.posted_income_amt/t2.posted_income_amt end) as posted_income_ratio -- 加权定标收益率
      from posted_price_y_info_tmp t1
      left join act_perform_income_y_tmp t2
        on t1.version_id = t2.version_id
       and t1.year = t2.year
       and t1.period_id = t2.period_id
       and t1.transport_mode = t2.transport_mode
       and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL') = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type = t2.container_type
       and t1.currency = t2.currency
       and t1.target_type = t2.target_type
       and t1.level_code = t2.level_code
     where t1.target_type = 'Y'
    ),
    /***************************************************************** 比定标价 end*****************************************************************/
    -- 包含所有金额的临时表，其中金额中用到的柜型量无需20GP数量×1，40GP数量×2，40HQ数量×2
    market_income_ratio_amt_tmp as(
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route	                      -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                    -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , hw_cost_amt                  -- 华为成本金额
         , xeneta_cost_amt              -- Xeneta成本金额
         , round(income_amt,6)    as income_amt      -- 收益
         , round(income_ratio,10) as income_ratio    -- 收益率=收益/(加权xeneta价*履行量)
      from market_income_ratio_m_tmp
     union all
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route	                      -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                    -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , hw_cost_amt                  -- 华为成本金额
         , xeneta_cost_amt              -- Xeneta成本金额
         , round(income_amt,6)    as income_amt    -- 收益
         , round(income_ratio,10) as income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from market_income_ratio_ytd_tmp
     union all
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route                        -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                     -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , hw_cost_amt                  -- 华为成本金额
         , xeneta_cost_amt              -- Xeneta成本金额
         , round(income_amt,6)    as income_amt    -- 收益
         , round(income_ratio,10) as income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from market_income_ratio_y_tmp
    ),
    -- 柜型量字段需要20GP数量×1，40GP数量×2，40HQ数量×2
    market_income_ratio_qty_tmp1 as(
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route	                      -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                    -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , (case when container_type = '20GP' then container_qty
                 when container_type = '40GP' then container_qty*2
                 when container_type = '40HQ' then container_qty*2
            end) as container_qty                -- 柜型量
      from market_income_ratio_m_tmp
     where container_type <> 'ALL'
     union all
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route	                      -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                    -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , (case when container_type = '20GP' then container_qty
                 when container_type = '40GP' then container_qty/2
                 when container_type = '40HQ' then container_qty/2
            end) as container_qty                -- 柜型量
      from market_income_ratio_ytd_tmp
     where container_type <> 'ALL'
     union all
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、Y、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route                        -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                     -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , (case when container_type = '20GP' then container_qty
                 when container_type = '40GP' then container_qty/2
                 when container_type = '40HQ' then container_qty/2
            end) as container_qty                -- 柜型量
      from market_income_ratio_y_tmp
     where container_type <> 'ALL'
    ),
    market_income_ratio_qty_tmp2 as(
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、Y、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route	                      -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                    -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , container_qty                -- 柜型量
      from market_income_ratio_qty_tmp1
     union all
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、Y、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route	                      -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , 'ALL' as container_type      -- 柜型（20GP、40GP、40HQ、ALL）
         , currency	                    -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , sum(container_qty) as container_qty  -- 柜型量
      from market_income_ratio_qty_tmp1
     group by version_id
         , year
         , period_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , currency
         , level_code
         , level_desc
    ),
    -- 比市场价金额、柜型量临时表
    market_income_ratio_all_tmp as(
    select t1.version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                         -- 年份
         , t1.period_id                    -- 会计期
         , t1.target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , t1.transport_mode               -- 运输方式（精品海运）
         , t1.region_cn_name               -- 目的地区域
         , t1.route                        -- 航线（起始港_目的港）
         , t1.source_country_name          -- 起运地国家
         , t1.dest_country_name            -- 目的地国家
         , t1.container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency                     -- 币种
         , t1.level_code                   -- 层级编码（01、02、03）
         , t1.level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , t2.container_qty                -- 柜型量
         , t1.hw_cost_amt                  -- 华为成本金额
         , t1.xeneta_cost_amt              -- Xeneta成本金额
         , t1.income_amt    -- 收益
         , t1.income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from market_income_ratio_amt_tmp t1   -- 金额临时表
      left join market_income_ratio_qty_tmp2 t2  -- 柜型量临时表
        on t1.version_id          = t2.version_id
       and t1.year                = t2.year
       and t1.period_id           = t2.period_id
       and t1.target_type         = t2.target_type
       and t1.transport_mode      = t2.transport_mode
       and t1.region_cn_name      = t2.region_cn_name
       and t1.route               = t2.route
       and t1.source_country_name = t2.source_country_name
       and t1.dest_country_name   = t2.dest_country_name
       and t1.container_type      = t2.container_type
       and t1.currency            = t2.currency
       and t1.level_code          = t2.level_code
       and t1.level_desc          = t2.level_desc
    )
    select version_id                   -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                         -- 年份
         , period_id                    -- 会计期
         , target_type                  -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode               -- 运输方式（精品海运）
         , region_cn_name               -- 目的地区域
         , route                        -- 航线（起始港_目的港）
         , source_country_name          -- 起运地国家
         , dest_country_name            -- 目的地国家
         , container_type               -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                     -- 币种
         , level_code                   -- 层级编码（01、02、03）
         , level_desc                   -- 层级描述（01 运输方式、02 区域、03 航线）
         , '1' as price_comparison_type -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , container_qty                -- 柜型量
         , hw_cost_amt                  -- 华为成本金额
         , xeneta_cost_amt              -- Xeneta成本金额
         , null::numeric as posted_cost_amt        -- 定标成本金额
         , income_amt    -- 收益
         , income_ratio  -- 收益率=收益/(加权xeneta价*履行量)
      from market_income_ratio_all_tmp
     union all
    -- 比定标价数据入到目标表
    select version_id              -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                    -- 年份
         , period_id               -- 会计期
         , target_type             -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode          -- 运输方式（精品海运、Xeneta）
         , region_cn_name          -- 目的地区域
         , route                   -- 航线（起始港_目的港）
         , source_country_name     -- 起运地国家
         , dest_country_name       -- 目的地国家
         , container_type          -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                -- 币种
         , level_code              -- 层级编码（01、02、03）
         , level_desc              -- 层级描述（01 运输方式、02 区域、03 航线）
         , '2' as price_comparison_type     -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , null::numeric as container_qty   -- 柜型量
         , null::numeric as hw_cost_amt     -- 华为成本金额
         , null::numeric as xeneta_cost_amt -- Xeneta成本金额
         , posted_cost_amt                  -- 定标成本金额
         , round(posted_income_amt,6)    as income_amt   -- 收益
         , round(posted_income_ratio,10) as income_ratio -- 收益率=收益/(加权定标价*履行量)
      from posted_price_info_all_tmp
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '数据入到临时表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

    delete from actual_perform_income_info_tmp where price_comparison_type = '1' and (hw_cost_amt = 0 or hw_cost_amt is null);

    delete from actual_perform_income_info_tmp where price_comparison_type = '2' and posted_cost_amt = 0;

    -- 补齐缺失的会计期，金额、量都给空值
    -- 年份
    for i in 2020..(to_char(current_date,'yyyy'))::numeric loop
      insert into year_apd_tmp1 values(i);
      i := i+1;
    end loop
    ;
    
    -- 1、target_type='Y'的补齐数据入到临时表
    -- 数据入到临时表
    insert into actual_perform_income_info_tmp(
           version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode              -- 运输方式（精品海运）
         , region_cn_name              -- 区域
         , route                       -- 航线（起始港_目的港       目的港_国家）
         , source_country_name         -- 起始国家
         , dest_country_name           -- 目的国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                    -- 币种
         , level_code                  -- 层级编码（01、02、03）
         , level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , container_qty               -- 柜型量
         , hw_cost_amt                 -- 华为成本金额
         , xeneta_cost_amt             -- Xeneta成本金额
         , posted_cost_amt             -- 定标成本金额
         , income_amt                  -- 比对收益（比市场价结果收益、比定标价结果收益）
         , income_ratio                -- 收益率
    )
    with year_apd_tmp2 as(
    select distinct version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
      from actual_perform_income_info_tmp
     where target_type = 'Y'
    ),
    -- 全的数据（包括补齐的数据）
    year_apd_tmp3 as(
    select t1.year
         , t1.year||'00' as period_id
         , t2.version_id
         , t2.target_type
         , t2.transport_mode
         , t2.region_cn_name
         , t2.route
         , t2.source_country_name
         , t2.dest_country_name
         , t2.container_type
         , t2.currency
         , t2.level_code
         , t2.level_desc
         , t2.price_comparison_type
      from year_apd_tmp1 t1
      left join year_apd_tmp2 t2
        on 1=1
    ),
    -- 实际已存在的数据
    year_apd_tmp4 as(
    select distinct year
         , period_id
         , version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
      from actual_perform_income_info_tmp
     where target_type = 'Y'
    ),
    -- 打上补齐标识
    year_apd_tmp5 as(
    select t1.year
         , t1.period_id
         , t1.version_id
         , t1.target_type
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
         , t1.level_code
         , t1.level_desc
         , t1.price_comparison_type
         , t2.period_id as t2_period_id
         , (case when t1.period_id = t2.period_id then 'N' else 'Y' end) as apd_flag  -- 补齐标识（Y 是）
      from year_apd_tmp3 t1
      left join year_apd_tmp4 t2
        on t1.year                  = t2.year
       and t1.period_id             = t2.period_id
       and t1.version_id            = t2.version_id
       and t1.target_type           = t2.target_type
       and nvl(t1.transport_mode,'SNULL')        = nvl(t2.transport_mode,'SNULL')
       and nvl(t1.region_cn_name,'SNULL')        = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL')                 = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL')   = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL')     = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type        = t2.container_type
       and t1.currency              = t2.currency
       and t1.level_code            = t2.level_code
       and t1.level_desc            = t2.level_desc
       and t1.price_comparison_type = t2.price_comparison_type
    )
    select version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode              -- 运输方式（精品海运）
         , region_cn_name              -- 区域
         , route                       -- 航线（起始港_目的港       目的港_国家）
         , source_country_name         -- 起始国家
         , dest_country_name           -- 目的国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                    -- 币种
         , level_code                  -- 层级编码（01、02、03）
         , level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , null::numeric as container_qty     -- 柜型量
         , null::numeric as hw_cost_amt       -- 华为成本金额
         , null::numeric as xeneta_cost_amt   -- Xeneta成本金额
         , null::numeric as posted_cost_amt   -- 定标成本金额
         , null::numeric as income_amt        -- 比对收益（比市场价结果收益、比定标价结果收益）
         , null::numeric as income_ratio      -- 收益率
      from year_apd_tmp5
     where apd_flag = 'Y'
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => '补齐target_type=Y 的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    
    -- 2、target_type='M'的补齐数据入到临时表
    -- 数据入到临时表
    insert into actual_perform_income_info_tmp(
           version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode              -- 运输方式（精品海运）
         , region_cn_name              -- 区域
         , route                       -- 航线（起始港_目的港       目的港_国家）
         , source_country_name         -- 起始国家
         , dest_country_name           -- 目的国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                    -- 币种
         , level_code                  -- 层级编码（01、02、03）
         , level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , container_qty               -- 柜型量
         , hw_cost_amt                 -- 华为成本金额
         , xeneta_cost_amt             -- Xeneta成本金额
         , posted_cost_amt             -- 定标成本金额
         , income_amt                  -- 比对收益（比市场价结果收益、比定标价结果收益）
         , income_ratio                -- 收益率
    )
    with month_apd_tmp1 as(
    select t1.year, t1.year||lpad(t2.month,2,'0') as period_id
      from year_apd_tmp1 t1  --  年份临时表
      left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month) t2
        on 1=1
    ),
    month_apd_tmp2 as(
    select distinct version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
      from actual_perform_income_info_tmp
     where target_type = 'M'
    ),
    -- 全的数据（包括补齐的数据）
    month_apd_tmp3 as(
    select t1.year
         , t1.period_id
         , t2.version_id
         , t2.target_type
         , t2.transport_mode
         , t2.region_cn_name
         , t2.route
         , t2.source_country_name
         , t2.dest_country_name
         , t2.container_type
         , t2.currency
         , t2.level_code
         , t2.level_desc
         , t2.price_comparison_type
      from month_apd_tmp1 t1
      left join month_apd_tmp2 t2
        on 1=1
     where t1.period_id <= to_char(current_date,'yyyymm')
    ),
    -- 实际已存在的数据
    month_apd_tmp4 as(
    select distinct year
         , period_id
         , version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
      from actual_perform_income_info_tmp
     where target_type = 'M'
    ),
    -- 打上补齐标识
    month_apd_tmp5 as(
    select t1.year
         , t1.period_id
         , t1.version_id
         , t1.target_type
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
         , t1.level_code
         , t1.level_desc
         , t1.price_comparison_type
         , t2.period_id as t2_period_id
         , (case when t1.period_id = t2.period_id then 'N' else 'Y' end) as apd_flag  -- 补齐标识（Y 是）
      from month_apd_tmp3 t1
      left join month_apd_tmp4 t2
        on t1.year                  = t2.year
       and t1.period_id             = t2.period_id
       and t1.version_id            = t2.version_id
       and t1.target_type           = t2.target_type
       and nvl(t1.transport_mode,'SNULL')        = nvl(t2.transport_mode,'SNULL')
       and nvl(t1.region_cn_name,'SNULL')        = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL')                 = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL')   = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL')     = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type        = t2.container_type
       and t1.currency              = t2.currency
       and t1.level_code            = t2.level_code
       and t1.level_desc            = t2.level_desc
       and t1.price_comparison_type = t2.price_comparison_type
    )
    select version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode              -- 运输方式（精品海运）
         , region_cn_name              -- 区域
         , route                       -- 航线（起始港_目的港       目的港_国家）
         , source_country_name         -- 起始国家
         , dest_country_name           -- 目的国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                    -- 币种
         , level_code                  -- 层级编码（01、02、03）
         , level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , null::numeric as container_qty     -- 柜型量
         , null::numeric as hw_cost_amt       -- 华为成本金额
         , null::numeric as xeneta_cost_amt   -- Xeneta成本金额
         , null::numeric as posted_cost_amt   -- 定标成本金额
         , null::numeric as income_amt        -- 比对收益（比市场价结果收益、比定标价结果收益）
         , null::numeric as income_ratio      -- 收益率
      from month_apd_tmp5
     where apd_flag = 'Y'
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '补齐target_type=M 的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    
    -- 2、target_type='YTD'的补齐数据入到临时表
    -- 数据入到临时表
    insert into actual_perform_income_info_tmp(
           version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                        -- 年份
         , period_id                   -- 会计期
         , target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , transport_mode              -- 运输方式（精品海运）
         , region_cn_name              -- 区域
         , route                       -- 航线（起始港_目的港       目的港_国家）
         , source_country_name         -- 起始国家
         , dest_country_name           -- 目的国家
         , container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , currency                    -- 币种
         , level_code                  -- 层级编码（01、02、03）
         , level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , container_qty               -- 柜型量
         , hw_cost_amt                 -- 华为成本金额
         , xeneta_cost_amt             -- Xeneta成本金额
         , posted_cost_amt             -- 定标成本金额
         , income_amt                  -- 比对收益（比市场价结果收益、比定标价结果收益）
         , income_ratio                -- 收益率
    )
    with ytd_apd_tmp1 as(
    select t1.year, t1.year||lpad(t2.month,2,'0') as period_id
      from year_apd_tmp1 t1  --  年份临时表
      left join (select unnest(array[1,2,3,4,5,6,7,8,9,10,11,12]) as month) t2
        on 1=1
    ),
    ytd_apd_tmp2 as(
    select distinct version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
      from actual_perform_income_info_tmp
     where target_type = 'YTD'
    ),
    -- 全的数据（包括补齐的数据）
    ytd_apd_tmp3 as(
    select t1.year
         , t1.period_id
         , t2.version_id
         , t2.target_type
         , t2.transport_mode
         , t2.region_cn_name
         , t2.route
         , t2.source_country_name
         , t2.dest_country_name
         , t2.container_type
         , t2.currency
         , t2.level_code
         , t2.level_desc
         , t2.price_comparison_type
      from ytd_apd_tmp1 t1
      left join ytd_apd_tmp2 t2
        on 1=1
     where t1.period_id <= to_char(current_date,'yyyymm')
    ),
    -- 实际已存在的数据
    ytd_apd_tmp4 as(
    select distinct year
         , period_id
         , version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
         , container_qty
         , hw_cost_amt
         , xeneta_cost_amt
         , posted_cost_amt
         , income_amt
         , income_ratio
      from actual_perform_income_info_tmp
     where target_type = 'YTD'
    ),
    -- 打上补齐标识
    ytd_apd_tmp5 as(
    select t1.year
         , t1.period_id
         , t1.version_id
         , t1.target_type
         , t1.transport_mode
         , t1.region_cn_name
         , t1.route
         , t1.source_country_name
         , t1.dest_country_name
         , t1.container_type
         , t1.currency
         , t1.level_code
         , t1.level_desc
         , t1.price_comparison_type
         , t2.period_id as apd_period_id
         , (case when t1.period_id = t2.period_id then 'N' else 'Y' end) as apd_flag  -- 补齐标识（Y 是）
      from ytd_apd_tmp3 t1
      left join ytd_apd_tmp4 t2
        on t1.year                  = t2.year
       and t1.period_id             = t2.period_id
       and t1.version_id            = t2.version_id
       and t1.target_type           = t2.target_type
       and nvl(t1.transport_mode,'SNULL')        = nvl(t2.transport_mode,'SNULL')
       and nvl(t1.region_cn_name,'SNULL')        = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL')                 = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL')   = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL')     = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type        = t2.container_type
       and t1.currency              = t2.currency
       and t1.level_code            = t2.level_code
       and t1.level_desc            = t2.level_desc
       and t1.price_comparison_type = t2.price_comparison_type
    ),
    -- 标识出补齐的会计期
    ytd_apd_tmp6 as(
    select year
         , period_id
         , version_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
         , apd_period_id
         , apd_flag  -- 补齐标识（Y 是）
         , max(apd_period_id) over(partition by year,version_id,target_type,transport_mode,region_cn_name,route,source_country_name
                                           , dest_country_name,container_type,currency,level_code,level_desc,price_comparison_type
                                       order by period_id
                                  ) as copy_period
      from ytd_apd_tmp5
    )
    select t1.version_id                  -- 版本ID（java传版本ID，即版本信息表的version_id）
         , t1.year                        -- 年份
         , t1.period_id                   -- 会计期
         , t1.target_type                 -- 目标时点类型（M、YTD、H1、H2、Y等）
         , t1.transport_mode              -- 运输方式（精品海运）
         , t1.region_cn_name              -- 区域
         , t1.route                       -- 航线（起始港_目的港       目的港_国家）
         , t1.source_country_name         -- 起始国家
         , t1.dest_country_name           -- 目的国家
         , t1.container_type              -- 柜型（20GP、40GP、40HQ、ALL）
         , t1.currency                    -- 币种
         , t1.level_code                  -- 层级编码（01、02、03）
         , t1.level_desc                  -- 层级描述（01 运输方式、02 区域、03 航线）
         , t1.price_comparison_type       -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , t2.container_qty               -- 柜型量
         , t2.hw_cost_amt                 -- 华为成本金额
         , t2.xeneta_cost_amt             -- Xeneta成本金额
         , t2.posted_cost_amt             -- 定标成本金额
         , t2.income_amt                  -- 比对收益（比市场价结果收益、比定标价结果收益）
         , t2.income_ratio                -- 收益率
      from ytd_apd_tmp6 t1
      left join ytd_apd_tmp4 t2
        on t1.year                  = t2.year
       and nvl(t1.copy_period,99)   = nvl(t2.period_id,99)
       and t1.version_id            = t2.version_id
       and t1.target_type           = t2.target_type
       and nvl(t1.transport_mode,'SNULL')        = nvl(t2.transport_mode,'SNULL')
       and nvl(t1.region_cn_name,'SNULL')        = nvl(t2.region_cn_name,'SNULL')
       and nvl(t1.route,'SNULL')                 = nvl(t2.route,'SNULL')
       and nvl(t1.source_country_name,'SNULL')   = nvl(t2.source_country_name,'SNULL')
       and nvl(t1.dest_country_name,'SNULL')     = nvl(t2.dest_country_name,'SNULL')
       and t1.container_type        = t2.container_type
       and t1.currency              = t2.currency
       and t1.level_code            = t2.level_code
       and t1.level_desc            = t2.level_desc
       and t1.price_comparison_type = t2.price_comparison_type
     where t1.apd_flag = 'Y'
    ;
    
    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => '补齐target_type=YTD 的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

    -- 清理数据
    delete from fin_dm_opt_foi.dm_fol_actual_perform_income_info_t where version_id = nvl(p_version_id,v_max_version_id) and upper(del_flag) = 'N';

    -- 数据入到目标表
    insert into fin_dm_opt_foi.dm_fol_actual_perform_income_info_t(
           version_id             -- 版本ID（java传版本ID，即版本信息表的version_id）
         , year                   -- 年份
         , period_id              -- 会计期
         , target_type            -- 目标时点类型（M、YTD、H1、H2等）
         , transport_mode         -- 运输方式（精品海运）
         , region_cn_name         -- 区域
         , route                  -- 航线（起始港_目的港）
         , source_country_name    -- 起始国家
         , dest_country_name      -- 目的国家
         , container_type         -- 柜型（20GP、40GP、40HQ）
         , currency               -- 币种
         , level_code             -- 层级编码（01、02、03）
         , level_desc             -- 层级描述（01 运输方式、02 区域、03 航线）
         , price_comparison_type  -- 价格比较类型（1、比市场价结果   2、比定标价结果）
         , container_qty          -- 柜型量
         , hw_cost_amt            -- 华为成本金额
         , xeneta_cost_amt        -- Xeneta成本金额
         , posted_cost_amt        -- 定标成本金额
         , income_amt             -- 比对收益（比市场价结果收益、比定标价结果收益）
         , income_ratio           -- 收益率
         , remark                 -- 备注
         , created_by             -- 创建人
         , creation_date          -- 创建时间
         , last_updated_by        -- 修改人
         , last_update_date       -- 修改时间
         , del_flag               -- 是否删除
    )
    select version_id
         , year
         , period_id
         , target_type
         , transport_mode
         , region_cn_name
         , route
         , source_country_name
         , dest_country_name
         , container_type
         , currency
         , level_code
         , level_desc
         , price_comparison_type
         , container_qty
         , hw_cost_amt
         , xeneta_cost_amt
         , posted_cost_amt
         , income_amt
         , income_ratio
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
      from actual_perform_income_info_tmp
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => '入到目标表的数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;
    

    -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
    update fin_dm_opt_foi.dm_fol_version_info_t set step = 1
     where version_id = nvl(p_version_id,v_max_version_id)
       and source_en_name = 'f_dm_fol_actual_perform_income_info_t'
       and refresh_type = nvl(p_refresh_type,'4_AUTO')
       and upper(del_flag) = 'N'
    ;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 9,
        p_log_cal_log_desc => '版本信息表中的step已更新为完成，运行结束！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
    ) ;

  end if;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id) as version_id
       , v_route_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_actual_perform_income_info_t' as source_en_name
       , '实际履行收益函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , 'version_code 是航线清单表的' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
	 union all
	select nvl(p_version_id,v_max_version_id)   as version_id
       , v_price_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_actual_perform_income_info_t' as source_en_name
       , '实际履行收益函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , 'version_code 是物流航线价格补录表的' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
  ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_actual_perform_income_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

