-- Name: f_foc_dwl_prod_bom_item_rev_detail_i_quarter_merge; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_foc_dwl_prod_bom_item_rev_detail_i_quarter_merge(OUT x_success_flag text)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023-9-19
创建人  ：李志勇 00808731
背景描述：收入时点源表小表合并到目标表
参数描述：参数一(p_period)：传入会计期（年月）,改成全量新增，所以不需要会计期参数
		  参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_foc_dwl_prod_bom_item_rev_detail_i_quarter_merge();

*/

DECLARE
    v_sp_name        varchar(100) := 'fin_dm_opt_foi.f_foc_dwl_prod_bom_item_rev_detail_i_quarter_merge';
    v_tbl_name       varchar(100) := 'fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i  '; ----收入明细数据添加主键后小表合并后的表
    v_dml_row_count  number DEFAULT 0 ;
    V_FROM_TABLE     varchar(200) := ' fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i_';
    V_SQL            TEXT;
    V_SQL_TAIL       TEXT; --tt_202101_202103  V_SQL_TAIL用于标识202103
    V_PERIOD_ID      BIGINT;
    V_PERIOD_ID_TAIL BIGINT;
    V_NUM            BIGINT       := -32; --用于标识 202101,9月份，10月使用需要-33
    V_STEP_MUM       BIGINT       := 0;


BEGIN
    x_success_flag := '1';
    --1表示成功
	
	  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

    --1.写入开始日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '收入明细数据源表分季度合并' || v_tbl_name || '：开始运行',
         F_DML_ROW_COUNT => SQL % ROWCOUNT,
         F_RESULT_STATUS => x_success_flag,
         F_ERRBUF => 'SUCCESS');


    ---2、支持重跑，清除目标表要插入会计期的数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_REV_DETAIL_I';
    -- 		delete from fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_TEMP
-- 		where period_id in (select distinct period_id from fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DTL_I_TEMP);    ---temp表的日期

    FOR NUM_FLAG IN 0 .. 10
        LOOP
            V_SQL := 'SELECT TO_CHAR(ADD_MONTHS(CURRENT_DATE,' || V_NUM || '),''YYYYMM'')'; --ADD_MONTHS 增加
            V_SQL_TAIL := 'SELECT TO_CHAR(ADD_MONTHS(CURRENT_DATE,' || V_NUM + 2 || '),''YYYYMM'')'; --ADD_MONTHS 增加
            EXECUTE IMMEDIATE V_SQL INTO V_PERIOD_ID;
            EXECUTE IMMEDIATE V_SQL_TAIL INTO V_PERIOD_ID_TAIL;


            ---3、插入目标表数据

            V_SQL := '
	   	insert into fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i
	   	select *
	      from ' || V_FROM_TABLE || V_PERIOD_ID || '_' || V_PERIOD_ID_TAIL;

            EXECUTE IMMEDIATE V_SQL;
            DBMS_OUTPUT.PUT_LINE(V_SQL);

            V_STEP_MUM := V_STEP_MUM + 1;
            PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
                (F_SP_NAME => V_SP_NAME,
                 F_STEP_NUM => V_STEP_MUM,
                 F_CAL_LOG_DESC => '将表' || V_FROM_TABLE || V_PERIOD_ID || '_' || V_PERIOD_ID_TAIL || '的数据插入表中',
                 F_DML_ROW_COUNT => SQL % ROWCOUNT,
                 F_ERRBUF => 'SUCCESS');

            V_NUM := V_NUM + 3;


        END LOOP;

    --4、 写结束日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '收入明细数据源表分季度合并' || v_tbl_name || '：结束运行',
         F_DML_ROW_COUNT => SQL % ROWCOUNT,
         F_RESULT_STATUS => x_success_flag,
         F_ERRBUF => 'SUCCESS');
		 
		 
        --5收集统计信息
        ANALYSE fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i;
		
				  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集fin_dm_opt_foi.foc_dwl_prod_bom_item_rev_detail_i统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
	x_success_flag := '0';
        PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
             F_RESULT_STATUS => x_success_flag,
             F_ERRBUF => SQLSTATE || ':' || SQLERRM
            );

        x_success_flag := '2001';
		return x_success_flag;
        --fail表示失败
END;
$$
/

