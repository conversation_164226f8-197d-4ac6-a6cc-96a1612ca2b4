-- Name: f_dm_foc_updete_dimension; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_updete_dimension(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：	1.更新量纲维表
*/



DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_UPDETE_DIMENSION'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 查看是否更新完成
  V_SQL TEXT;
  
  V_RMB_COST_AMT VARCHAR(100);
  V_SUM_RMB_AMT VARCHAR(200);
  V_AVG_RMB_AMT VARCHAR(200);
  V_FROM_TABLE VARCHAR(50);
  V_TO_TABLE VARCHAR(50);
  V_JOIN_TABLE VARCHAR(50);


BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
--清空目标表

V_SQL := 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOC_DIM_PRODUCTDIMENSION_D';
	EXECUTE IMMEDIATE V_SQL ;
	 
	 
	 
	 	
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空DM_FOC_DIM_PRODUCTDIMENSION_D表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
	 
	--更新语句 
	V_SQL := 'INSERT INTO  FIN_DM_OPT_FOI.DM_FOC_DIM_PRODUCTDIMENSION_D 
	SELECT 		
  T1.DIMENSION_KEY,
  T1.DIMENSION_CODE ,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.DIMENSION_CN_NAME ELSE T2.DIMENSION_CN_NAME END AS DIMENSION_CN_NAME,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.DIMENSION_EN_NAME ELSE T2.DIMENSION_EN_NAME END AS DIMENSION_EN_NAME,
	T1.DIMENSION_DESC ,
	T1.DIMENSION_LEVEL ,
	T1.DIMENSION_TYPE ,
	T1.DIMENSION_STATUS ,
	T1.PRODUCT_DIMENSION_CODE ,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.PRODUCT_DIMENSION_CN_NAME ELSE T2.PRODUCT_DIMENSION_CN_NAME END AS PRODUCT_DIMENSION_CN_NAME,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.PRODUCT_DIMENSION_EN_NAME ELSE T2.PRODUCT_DIMENSION_EN_NAME END AS PRODUCT_DIMENSION_EN_NAME,
	T1.INDUSTRY_CATG_CODE ,
	T1.INDUSTRY_CATG_CN_NAME ,
	T1.INDUSTRY_CATG_EN_NAME ,
	T1.INDUSTRY_DIMENSION_CODE ,
	T1.INDUSTRY_DIMENSION_CN_NAME ,
	T1.INDUSTRY_DIMENSION_EN_NAME ,
	T1.DIMENSION_SUBCATEGORY_CODE ,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.DIMENSION_SUBCATEGORY_CN_NAME ELSE T2.DIMENSION_SUBCATEGORY_CN_NAME END AS DIMENSION_SUBCATEGORY_CN_NAME,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.DIMENSION_SUBCATEGORY_EN_NAME ELSE T2.DIMENSION_SUBCATEGORY_EN_NAME END AS DIMENSION_SUBCATEGORY_EN_NAME,
	T1.DIMENSION_SUB_DETAIL_CODE ,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.DIMENSION_SUB_DETAIL_CN_NAME ELSE T2.DIMENSION_SUB_DETAIL_CN_NAME END AS DIMENSION_SUB_DETAIL_CN_NAME,
	CASE WHEN T1.SCD_ACTIVE_IND = 1 THEN T1.DIMENSION_SUB_DETAIL_EN_NAME ELSE T2.DIMENSION_SUB_DETAIL_EN_NAME END AS DIMENSION_SUB_DETAIL_EN_NAME,
	T1.DEL_FLAG ,
	T1.SS_ID ,
	T1.SCD_ACTIVE_IND ,
	T1.SCD_ACTIVE_BEGIN_DATE ,
	T1.SCD_ACTIVE_END_DATE ,
	T1.CRT_CYCLE_ID ,
	T1.LAST_UPD_CYCLE_ID ,
	T1.CRT_JOB_INSTANCE_ID ,
	T1.UPD_JOB_INSTANCE_ID ,
	T1.DW_LAST_UPDATE_DATE ,
	T1.PRODUCT_DIMENSION_GROUP
	FROM DMDIM.DM_DIM_PRODUCTDIMENSION_D T1
INNER JOIN
		(SELECT * FROM DMDIM.DM_DIM_PRODUCTDIMENSION_D
		WHERE SCD_ACTIVE_IND = 1
		) T2
ON  T1.PRODUCT_DIMENSION_CODE  = T2.PRODUCT_DIMENSION_CODE
AND T1.DIMENSION_SUBCATEGORY_CODE = T2.DIMENSION_SUBCATEGORY_CODE
AND T1.DIMENSION_SUB_DETAIL_CODE  = T2.DIMENSION_SUB_DETAIL_CODE
AND T1.DEL_FLAG = ''N''
			  ';
	DBMS_OUTPUT.PUT_LINE(V_SQL);		  
	EXECUTE IMMEDIATE V_SQL ;
	
	

	
	
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '更新到DM_FOC_DIM_PRODUCTDIMENSION_D表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  	
	
	
	

	  V_SQL := 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_DIM_PRODUCTDIMENSION_D';
   EXECUTE IMMEDIATE V_SQL;


  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 DM_FOC_DIM_PRODUCTDIMENSION_D统计信息完成!');
 
  RETURN 'SUCCESS';

  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

