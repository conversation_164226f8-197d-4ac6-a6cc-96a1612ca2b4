-- Name: f_dm_fcst_std_prod_unit_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_std_prod_unit_t(f_keystr character varying, f_year character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
背景描述：	1.标准成本收入实际数关联维表
			2.标准成本收入实际数加解密
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_STD_PROD_UNIT_T'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYYMMDD')||'-001'; --新的版本中文名称
  V_DIM_VERSION_ID BIGINT ; 
  V_DIM_VERSION_ID2 BIGINT; 
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --查询或新建版本号
-- 查询该月月度版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH';
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH';
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
  --往版本信息表记录本次月度版本号, 版本号为V_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','MONTH',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   END IF;
 
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往DM_FCST_ICT_VERSION_INFO_T版本信息表记录月度版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
 
 --查询或新建版本号
-- 查询该月年度版本是否已存在，若存在，沿用，否则新建 
  SELECT COUNT(1) INTO V_CURRENT_FLAG
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL';
    -- FLAG 不等于0，说明已有版本号，沿用        
  IF V_CURRENT_FLAG <> 0 THEN 
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,0,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL';
  ELSE
  --新版本号赋值
  SELECT NEXTVAL('FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_S')
    INTO V_VERSION_ID
    FROM DUAL;
     
  --往版本信息表记录本次年度版本号, 版本号为V_VERSION_ID
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_DIM_VERSION_ID,V_VERSION_NAME,1,'AUTO','ANNUAL',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   END IF;
 
 
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往DM_FCST_ICT_VERSION_INFO_T版本信息表记录年度版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 


  --清空目标表数据:
EXECUTE IMMEDIATE 'DELETE FROM  DM_FCST_ICT_STD_PROD_UNIT_T WHERE SUBSTR(PERIOD_ID,0,4) = '''||F_YEAR||''' ';
  

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空DM_FCST_STD_PROD_UNIT_T的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 


   
 
  --往目标表里插数
V_SQL := '
WITH DATA_DECRYPT AS (
SELECT  
	T1.P_FLAG ,
	T1.PERIOD_ID ,
	T1.SCENARIO ,
	T1.PROD_KEY ,
	T1.PROD_CODE ,
	T1.CONTRACT_KEY ,
	T1.HW_CONTRACT_NUM ,
	T1.GEO_PC_KEY ,
	RC.GEO_PC_CODE,
	T1.END_CUST_KEY ,
	CU.CUST_ACCOUNT_NUM,
	T1.END_CUST_KEY ,
	T1.SPART_CODE ,
	TO_NUMBER(GS_DECRYPT(T2.RMB_FACT_RATE_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256''))  AS RMB_FACT_RATE_AMT  ,
	NULL AS USD_FACT_RATE_AMT ,
	NULL AS PART_QTY ,
	NULL AS PROD_UNIT_QTY ,
	T1.DIMENSION_KEY ,
	T1.MAIN_DIMENSION_FLAG, 
	T1.SELF_PROD_AND_SALES_FLAG ,
	T1.PRIMARY_ID 
	FROM DWL_PROD_PROD_UNIT_KMS_I T1
	JOIN DM_FCST_DATA_PRIMARY_ENCRYPT_T T2
	ON T1.PRIMARY_ID = T2.PRIMARY_ID
	LEFT  JOIN  DWRDIM.DWR_DIM_REGION_RC_D RC --区域维表
	ON T1.GEO_PC_KEY  =  RC.GEO_PC_KEY
	LEFT JOIN  DWRDIM.DWR_DIM_CUSTOMER_D CU  --客户标准维
	ON T1.END_CUST_KEY = CU.CUST_ACCOUNT_KEY  
	WHERE SUBSTR(T1.PERIOD_ID,0,4) = '''||F_YEAR||'''
	AND T1.SPART_CODE NOT IN (''********'',''88030CAQ'',''********'',''********'')
	AND T1. DATA_SOURCE NOT IN (''CUTOFF_TEST'',''CUTOFF_TEST_REC'' ,''OEM_SPLIT_REC'',''OEM_SPLIT'',''OEM_SP_REC'',''OEM_SPLIT'') --去除截止性测试和统筹分销
	
),
DATA_ROLL_UP AS (
	SELECT 	
	PERIOD_ID ,
	PROD_CODE ,
	CONTRACT_KEY ,
	HW_CONTRACT_NUM ,
	GEO_PC_CODE,
	CUST_ACCOUNT_NUM ,
	SPART_CODE ,
	SUM(RMB_FACT_RATE_AMT) AS  RMB_FACT_RATE_AMT ,
	DIMENSION_KEY ,
	MAIN_DIMENSION_FLAG, 
	SELF_PROD_AND_SALES_FLAG 
	FROM DATA_DECRYPT
	GROUP BY 
	PERIOD_ID ,
	PROD_CODE ,
	CONTRACT_KEY ,
	HW_CONTRACT_NUM ,
	GEO_PC_CODE ,
	CUST_ACCOUNT_NUM ,
	SPART_CODE ,
	DIMENSION_KEY ,
	MAIN_DIMENSION_FLAG, 
	SELF_PROD_AND_SALES_FLAG 
),
PSP_DATA_SELECT AS (
SELECT 
	UNIT.P_FLAG ,
	UNIT.PERIOD_ID ,
	UNIT.SCENARIO ,
	UNIT.PROD_CODE ,
	UNIT.CONTRACT_KEY ,
	UNIT.HW_CONTRACT_NUM ,
	UNIT.GEO_PC_KEY ,
	RC.GEO_PC_CODE,--通过区域维表关联带出GEO_PC_CODE,用CODE进行后续关联
	UNIT.END_CUST_KEY ,
	CU.CUST_ACCOUNT_NUM,
	UNIT.SPART_CODE ,
	CASE WHEN UNIT.SCENARIO = ''REV'' THEN UNIT.PROD_UNIT_QTY
	ELSE NULL 
	END AS PROD_UNIT_QTY,--量纲业务量
	CASE WHEN UNIT.SCENARIO = ''REV_SPLIT'' THEN UNIT.RMB_FACT_RATE_AMT
	ELSE NULL 
	END AS RMB_FACT_RATE_AMT,--拆分金额RMB
	CASE WHEN UNIT.SCENARIO = ''REV_SPLIT'' THEN UNIT.USD_FACT_RATE_AMT
	ELSE NULL 
	END AS USD_FACT_RATE_AMT,--拆分金额USD
	CASE WHEN UNIT.SCENARIO = ''PSP'' THEN UNIT.RMB_FACT_RATE_AMT
	ELSE NULL 
	END AS RMB_PSP_FACT_RATE_AMT,--PSP要货成本RMB
	CASE WHEN UNIT.SCENARIO = ''PSP'' THEN UNIT.USD_FACT_RATE_AMT
	ELSE NULL 
	END AS USD_PSP_FACT_RATE_AMT,--PSP要货成本USD
	CASE WHEN UNIT.SCENARIO = ''REV'' THEN UNIT.PART_QTY
	ELSE NULL 
	END AS PART_QTY,--SPART销售量
	UNIT.DIMENSION_KEY ,
	UNIT.MAIN_DIMENSION_FLAG, 
	UNIT.SELF_PROD_AND_SALES_FLAG 
	FROM DWL_PROD_PROD_UNIT_I UNIT
	LEFT  JOIN  DWRDIM.DWR_DIM_REGION_RC_D RC --区域维表
	ON UNIT.GEO_PC_KEY  =  RC.GEO_PC_KEY
	LEFT JOIN  DWRDIM.DWR_DIM_CUSTOMER_D CU  --客户标准维
	ON UNIT.END_CUST_KEY = CU.CUST_ACCOUNT_KEY
	WHERE  SUBSTR(PERIOD_ID,0,4) = '''||F_YEAR||'''
	AND  UNIT.SPART_CODE NOT IN ( ''********'',''88030CAQ'',''********'',''********'')
	AND UNIT.SCENARIO IN (''REV'',''PSP'')
	AND UNIT.P_FLAG != ''截止性测试''
	)
,


PSP_DATA_SUM AS (
SELECT 
	PERIOD_ID ,
	PROD_CODE ,
	CONTRACT_KEY ,
	HW_CONTRACT_NUM ,
	GEO_PC_CODE ,
	CUST_ACCOUNT_NUM ,
	SPART_CODE ,
    SUM(PROD_UNIT_QTY) AS PROD_UNIT_QTY,--量纲业务量
	SUM(RMB_FACT_RATE_AMT) AS RMB_FACT_RATE_AMT,--拆分金额RMB
	SUM(USD_FACT_RATE_AMT) AS USD_FACT_RATE_AMT ,--拆分金额USD
	SUM(RMB_PSP_FACT_RATE_AMT) AS RMB_PSP_FACT_RATE_AMT,--PSP要货成本RMB
	SUM(USD_PSP_FACT_RATE_AMT) AS USD_PSP_FACT_RATE_AMT,--PSP要货成本USD
	SUM(PART_QTY) AS PART_QTY,--SPART销售量
	DIMENSION_KEY ,
	MAIN_DIMENSION_FLAG, 
	SELF_PROD_AND_SALES_FLAG 
FROM PSP_DATA_SELECT A
GROUP BY  
	PERIOD_ID ,
	PROD_CODE ,
	CONTRACT_KEY ,
	HW_CONTRACT_NUM ,
	GEO_PC_CODE ,
	CUST_ACCOUNT_NUM ,
	SPART_CODE ,
	DIMENSION_KEY ,
	MAIN_DIMENSION_FLAG, 
	SELF_PROD_AND_SALES_FLAG 
	
	),
	
	
	
DATA_JOIN AS (

	SELECT 	
	T1.PERIOD_ID ,
	T1.PROD_CODE ,
	T1.CONTRACT_KEY ,
	T1.HW_CONTRACT_NUM ,
	T1.GEO_PC_CODE ,
	T1.CUST_ACCOUNT_NUM ,
	T1.SPART_CODE ,
	T1.RMB_FACT_RATE_AMT ,
	NULL AS USD_FACT_RATE_AMT ,
	T2.PART_QTY  ,
	T2.PROD_UNIT_QTY ,
	T1.DIMENSION_KEY ,
	T1.MAIN_DIMENSION_FLAG, 
	T1.SELF_PROD_AND_SALES_FLAG 
FROM DATA_ROLL_UP T1
FULL JOIN PSP_DATA_SUM T2
	ON T1.PERIOD_ID = T2.PERIOD_ID
	AND T1.PROD_CODE  = T2.PROD_CODE
	AND T1.CONTRACT_KEY  = T2.CONTRACT_KEY
	AND T1.HW_CONTRACT_NUM  = T2.HW_CONTRACT_NUM
	AND T1.GEO_PC_CODE  = T2.GEO_PC_CODE
	AND T1.CUST_ACCOUNT_NUM  = T2.CUST_ACCOUNT_NUM
	AND T1.SPART_CODE  = T2.SPART_CODE
	AND T1.DIMENSION_KEY  = T2.DIMENSION_KEY
	AND T1.MAIN_DIMENSION_FLAG  = T2.MAIN_DIMENSION_FLAG
	AND NVL(T1.SELF_PROD_AND_SALES_FLAG,''SS'')  = NVL(T2.SELF_PROD_AND_SALES_FLAG ,''SS'')
	

	)

INSERT INTO DM_FCST_ICT_STD_PROD_UNIT_T (
	VERSION_ID,
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME,
	REGION_EN_NAME,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME,
	REPOFFICE_EN_NAME,
	FUND_COUNTRY_CODE ,
	FUND_COUNTRY_CN_NAME,
	FUND_COUNTRY_EN_NAME,
	TOP_CUST_CATEGORY_CODE ,
	TOP_CUST_CATEGORY_CN_NAME,
	TOP_CUST_CATEGORY_EN_NAME,
	REGION_CUSTCATG_CODE ,
	REGION_CUSTCATG_CN_NAME,
	REGION_CUSTCATG_EN_NAME,
	ENTERPRISE_CUST_CLASS_CODE ,
	ENTERPRISE_CUST_CLASS_CN_NAME,
	ENTERPRISE_CUST_CLASS_EN_NAME,
	MERGERED_CUST_NUM ,
	MERGERED_CUST_NAME,
	HW_CONTRACT_NUM ,
	BG_CODE ,
	BG_CN_NAME,
	PROD_CODE ,
	LV0_PROD_RND_TEAM_CODE,--重量级团队LV0代码
	LV0_PROD_RD_TEAM_CN_NAME,--重量级团队LV0中文名称
	LV0_PROD_RD_TEAM_EN_NAME,--重量级团队LV0中文名称
	LV1_PROD_RND_TEAM_CODE,--重量级团队LV1代码
	LV1_PROD_RD_TEAM_CN_NAME,--重量级团队LV1中文名称
	LV1_PROD_RD_TEAM_EN_NAME,--重量级团队LV1中文名称
	LV2_PROD_RND_TEAM_CODE,--重量级团队LV2代码
	LV2_PROD_RD_TEAM_CN_NAME,--重量级团队LV2中文名称
	LV2_PROD_RD_TEAM_EN_NAME,--重量级团队LV2中文名称
	LV3_PROD_RND_TEAM_CODE,--重量级团队LV3代码
	LV3_PROD_RD_TEAM_CN_NAME,--重量级团队LV3中文名称
	LV3_PROD_RD_TEAM_EN_NAME,--重量级团队LV3中文名称
	LV4_PROD_RND_TEAM_CODE,--重量级团队LV3.5代码
	LV4_PROD_RD_TEAM_CN_NAME,--重量级团队LV3.5中文名称
	LV4_PROD_RD_TEAM_EN_NAME,--重量级团队LV3.5中文名称
	LV0_INDUSTRY_CATG_CODE,-- 零级产业目录代码
	LV0_INDUSTRY_CATG_CN_NAME,-- 零级产业目录中文名称
	LV0_INDUSTRY_CATG_EN_NAME,-- 零级产业目录中文名称
	LV1_INDUSTRY_CATG_CODE,-- 一级产业目录代码
	LV1_INDUSTRY_CATG_CN_NAME,-- 一级产业目录中文名称
	LV1_INDUSTRY_CATG_EN_NAME,-- 一级产业目录中文名称
	LV2_INDUSTRY_CATG_CODE,-- 二级产业目录代码
	LV2_INDUSTRY_CATG_CN_NAME,-- 二级产业目录中文名称
	LV2_INDUSTRY_CATG_EN_NAME,-- 二级产业目录中文名称
	LV3_INDUSTRY_CATG_CODE,-- 三级产业目录代码
	LV3_INDUSTRY_CATG_CN_NAME,-- 三级产业目录中文名称
	LV3_INDUSTRY_CATG_EN_NAME,-- 三级产业目录中文名称
	LV4_INDUSTRY_CATG_CODE,-- 四级产业目录代码（3.5）
	LV4_INDUSTRY_CATG_CN_NAME,-- 四级产业目录中文名称（3.5)
	LV4_INDUSTRY_CATG_EN_NAME,-- 四级产业目录中文名称（3.5)
	LV0_PROD_LIST_CODE,--零级产品目录代码
	LV0_PROD_LIST_CN_NAME,--	零级产品目录中文名称
	LV0_PROD_LIST_EN_NAME,--	零级产品目录中文名称
	LV1_PROD_LIST_CODE,--一级产品目录代码
	LV1_PROD_LIST_CN_NAME,--	一级产品目录中文名称
	LV1_PROD_LIST_EN_NAME,--	一级产品目录中文名称
	LV2_PROD_LIST_CODE,--二级产品目录代码
	LV2_PROD_LIST_CN_NAME,--	二级产品目录中文名称
	LV2_PROD_LIST_EN_NAME,--	二级产品目录中文名称
	LV3_PROD_LIST_CODE,--三级产品目录代码
	LV3_PROD_LIST_CN_NAME,--	三级产品目录中文名称
	LV3_PROD_LIST_EN_NAME,--	三级产品目录中文名称
	LV4_PROD_LIST_CODE,--四级产品目录代码（3.5）
	LV4_PROD_LIST_CN_NAME,--	四级产品目录中文名称（3.5）
	LV4_PROD_LIST_EN_NAME,--	四级产品目录中文名称（3.5）
	DIMENSION_CODE ,
	DIMENSION_CN_NAME,
	DIMENSION_EN_NAME,
	DIMENSION_SUBCATEGORY_CODE ,
	DIMENSION_SUBCATEGORY_CN_NAME,
	DIMENSION_SUBCATEGORY_EN_NAME,
	DIMENSION_SUB_DETAIL_CODE ,
	DIMENSION_SUB_DETAIL_CN_NAME,
	DIMENSION_SUB_DETAIL_EN_NAME,
	MAIN_DIMENSION_FLAG ,
	PROD_UNIT_QTY ,
	RMB_FACT_RATE_AMT ,
	USD_FACT_RATE_AMT ,
	RMB_COST_AMT , --标准成本
	USD_COST_AMT , --标准成本
	SPART_CODE ,
	PART_QTY ,
	PRODUCT_DIMENSION_GROUP ,
	SELF_PROD_AND_SALES_FLAG ,
	DIMENSION_KEY ,
	CREATED_BY ,
	CREATION_DATE ,
	LAST_UPDATED_BY ,
	LAST_UPDATE_DATE ,
	DEL_FLAG ,
	OVERSEA_FLAG
)

SELECT 
	'||V_VERSION_ID||',
	UNIT.PERIOD_ID,
	SUBSTR(UNIT.PERIOD_ID,0,4) AS PERIOD_YEAR,
	RC.REGION_CODE, --地区部编码
	RC.REGION_CN_NAME, --地区部名称
	RC.REGION_EN_NAME, --地区部名称
	RC.REPOFFICE_CODE, --代表处编码
	RC.REPOFFICE_CN_NAME, --代表处名称
	RC.REPOFFICE_EN_NAME, --代表处名称
	RC.FUND_COUNTRY_CODE,  --国家编码
	RC.FUND_COUNTRY_CN_NAME, --国家名称
	RC.FUND_COUNTRY_EN_NAME, --国家名称
	CU.TOP_CUST_CATEGORY_CODE ,--大T系统部编码
	CU.TOP_CUST_CATEGORY_CN_NAME ,--大T系统部名称
	CU.TOP_CUST_CATEGORY_EN_NAME ,--大T系统部名称
	CU.REGION_CUSTCATG_CODE,--区域系统部编码
	CU.REGION_CUSTCATG_CN_NAME,--区域系统部名称
	CU.REGION_CUSTCATG_EN_NAME,--区域系统部名称
	CU.ENTERPRISE_CUST_CLASS_CODE,--客户组合群编码
	CU.ENTERPRISE_CUST_CLASS_CN_NAME,--客户组合群名称
	CU.ENTERPRISE_CUST_CLASS_EN_NAME,--客户组合群名称
	CU.MERGERED_CUST_NUM, --客户编码
	CU.MERGERED_CUST_NAME, --客户名称 
	UNIT.HW_CONTRACT_NUM,
	CP.LV0_PROD_LIST_CODE AS BG_CODE,--BG编码
	CP.LV0_PROD_LIST_CN_NAME AS BG_CN_NAME,--BG名称
	UNIT.PROD_CODE,--产品编码
	CP.LV0_PROD_RND_TEAM_CODE,--重量级团队LV0代码
	CP.LV0_PROD_RD_TEAM_CN_NAME,--重量级团队LV0中文名称
	CP.LV0_PROD_RD_TEAM_EN_NAME,--重量级团队LV0中文名称
	CP.LV1_PROD_RND_TEAM_CODE,--重量级团队LV1代码
	CP.LV1_PROD_RD_TEAM_CN_NAME,--重量级团队LV1中文名称
	CP.LV1_PROD_RD_TEAM_EN_NAME,--重量级团队LV1中文名称
	CP.LV2_PROD_RND_TEAM_CODE,--重量级团队LV2代码
	CP.LV2_PROD_RD_TEAM_CN_NAME,--重量级团队LV2中文名称
	CP.LV2_PROD_RD_TEAM_EN_NAME,--重量级团队LV2中文名称
	CP.LV3_PROD_RND_TEAM_CODE,--重量级团队LV3代码
	CP.LV3_PROD_RD_TEAM_CN_NAME,--重量级团队LV3中文名称
	CP.LV3_PROD_RD_TEAM_EN_NAME,--重量级团队LV3中文名称
	CP.LV4_PROD_RND_TEAM_CODE,--重量级团队LV3.5代码
   CASE WHEN CP.LV4_PROD_RND_TEAM_CODE = CP.LV3_PROD_RND_TEAM_CODE THEN  CP.LV4_PROD_RD_TEAM_CN_NAME||''(公共)''
	ELSE CP.LV4_PROD_RD_TEAM_CN_NAME END AS LV4_PROD_RD_TEAM_CN_NAME, --四级产业目录中文名称（3.5) 
	CP.LV4_PROD_RD_TEAM_EN_NAME,
	CP.LV0_INDUSTRY_CATG_CODE,-- 零级产业目录代码
	CP.LV0_INDUSTRY_CATG_CN_NAME,-- 零级产业目录中文名称
	CP.LV0_INDUSTRY_CATG_EN_NAME,-- 零级产业目录中文名称
	CP.LV1_INDUSTRY_CATG_CODE,-- 一级产业目录代码
	CP.LV1_INDUSTRY_CATG_CN_NAME,-- 一级产业目录中文名称
	CP.LV1_INDUSTRY_CATG_EN_NAME,-- 一级产业目录中文名称
	CP.LV2_INDUSTRY_CATG_CODE,-- 二级产业目录代码
	CP.LV2_INDUSTRY_CATG_CN_NAME,-- 二级产业目录中文名称
	CP.LV2_INDUSTRY_CATG_EN_NAME,-- 二级产业目录中文名称
	CP.LV3_INDUSTRY_CATG_CODE,-- 三级产业目录代码
	CP.LV3_INDUSTRY_CATG_CN_NAME,-- 三级产业目录中文名称
	CP.LV3_INDUSTRY_CATG_EN_NAME,-- 三级产业目录中文名称
	CP.LV4_INDUSTRY_CATG_CODE,-- 四级产业目录代码（3.5）
	CASE WHEN CP.LV4_INDUSTRY_CATG_CODE = CP.LV3_INDUSTRY_CATG_CODE THEN  CP.LV4_INDUSTRY_CATG_CN_NAME||''(公共)''
	ELSE CP.LV4_INDUSTRY_CATG_CN_NAME END AS LV4_INDUSTRY_CATG_CN_NAME, --四级产业目录中文名称（3.5) 
	CP.LV4_INDUSTRY_CATG_EN_NAME,-- 四级产业目录中文名称（3.5)
	CP.LV0_PROD_LIST_CODE,
	CP.LV0_PROD_LIST_CN_NAME,
	CP.LV0_PROD_LIST_EN_NAME,
	CP.LV1_PROD_LIST_CODE,--一级产品目录代码
	CP.LV1_PROD_LIST_CN_NAME,--	一级产品目录中文名称
	CP.LV1_PROD_LIST_EN_NAME,--	一级产品目录中文名称
	CP.LV2_PROD_LIST_CODE,--二级产品目录代码
	CP.LV2_PROD_LIST_CN_NAME,--	二级产品目录中文名称
	CP.LV2_PROD_LIST_EN_NAME,--	二级产品目录中文名称
	CP.LV3_PROD_LIST_CODE,--三级产品目录代码
	CP.LV3_PROD_LIST_CN_NAME,--	三级产品目录中文名称
	CP.LV3_PROD_LIST_EN_NAME,--	三级产品目录中文名称
	CP.LV4_PROD_LIST_CODE,--四级产品目录代码（3.5）
	CASE WHEN CP.LV4_PROD_LIST_CODE = CP.LV3_PROD_LIST_CODE THEN CP.LV4_PROD_LIST_CN_NAME||''(公共)''
	ELSE  CP.LV4_PROD_LIST_CN_NAME END AS LV4_PROD_LIST_CN_NAME,--	四级产品目录中文名称（3.5）
	CP.LV4_PROD_LIST_EN_NAME,--	四级产品目录中文名称（3.5）
	PD.PRODUCT_DIMENSION_CODE AS DIMENSION_CODE,  --产品量纲编码
	PD.PRODUCT_DIMENSION_CN_NAME AS DIMENSION_CN_NAME,  --产品量纲中文名称
	PD.PRODUCT_DIMENSION_EN_NAME AS DIMENSION_EN_NAME,  --产品量纲英文名称
	PD.DIMENSION_SUBCATEGORY_CODE,  --量纲子类编码
	PD.DIMENSION_SUBCATEGORY_CN_NAME,  --量纲子类中文名称
	PD.DIMENSION_SUBCATEGORY_EN_NAME,  --量纲子类英文名称
	PD.DIMENSION_SUB_DETAIL_CODE,  --量纲子类明细编码
	PD.DIMENSION_SUB_DETAIL_CN_NAME,  --量纲子类明细中文名称
	PD.DIMENSION_SUB_DETAIL_EN_NAME,  --量纲子类明细英文名称
	UNIT.MAIN_DIMENSION_FLAG,--主量纲标识
	UNIT.PROD_UNIT_QTY,--量纲业务量
	NULL AS RMB_FACT_RATE_AMT,--拆分金额RMB
	NULL AS USD_FACT_RATE_AMT,--拆分金额USD
	GS_ENCRYPT(UNIT.RMB_FACT_RATE_AMT,'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_STD_FACT_RATE_AMT   ,--标准成本RMB
	UNIT.USD_FACT_RATE_AMT AS USD_STD_FACT_RATE_AMT,--标准成本USD
	UNIT.SPART_CODE, --SPART编码
	UNIT.PART_QTY,--SPART销售量
	PD.PRODUCT_DIMENSION_GROUP,--量纲分组暂无
	UNIT.SELF_PROD_AND_SALES_FLAG,--自产自销标识
	UNIT.DIMENSION_KEY,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG,
	RC.OVERSEA_FLAG
FROM DATA_JOIN UNIT --标准成本
LEFT JOIN  (SELECT * FROM DMDIM.DM_DIM_PRODUCT_D WHERE SCD_ACTIVE_IND = 1)   CP --产品标准维维表
ON UNIT.PROD_CODE = CP.PROD_CODE
LEFT JOIN  (SELECT * FROM DMDIM.DM_DIM_PRODUCTDIMENSION_D  WHERE SCD_ACTIVE_IND = 1)  PD --量纲维表
ON UNIT.DIMENSION_KEY = PD.DIMENSION_KEY
LEFT JOIN (SELECT * FROM DWRDIM.DWR_DIM_REGION_RC_D  WHERE SCD_ACTIVE_IND = 1 ) RC --区域维表
ON UNIT.GEO_PC_CODE  =  RC.GEO_PC_CODE
LEFT JOIN (SELECT * FROM DWRDIM.DWR_DIM_CUSTOMER_D WHERE SCD_ACTIVE_IND = 1 )CU  --客户标准维
ON UNIT.CUST_ACCOUNT_NUM = CU.CUST_ACCOUNT_NUM
WHERE  MAIN_DIMENSION_FLAG IN (''Y'',''SNULL'')
AND CP.LV0_PROD_RND_TEAM_CODE = ''104364'' 
AND CP.LV0_PROD_LIST_CODE  IN (''PDCG901160'',''PDCG901159'') 
AND CP.LV0_INDUSTRY_CATG_CODE = ''IDCG000001''
';

	DBMS_OUTPUT.PUT_LINE(V_SQL);
	  EXECUTE IMMEDIATE V_SQL;
 
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 DM_FCST_STD_PROD_UNIT_T 表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE DM_FCST_ICT_STD_PROD_UNIT_T';
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 DM_FCST_STD_PROD_UNIT_T统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

