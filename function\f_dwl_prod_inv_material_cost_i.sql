-- Name: f_dwl_prod_inv_material_cost_i; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dwl_prod_inv_material_cost_i(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
创建时间：2023年11月25日18:30:40
创建人  ：李志勇 00808731
最后修改时间：2023年12月29日
背景描述：产业成本指数--物料标准成本卷积历史 数据增量集成后,调用该函数将相对应的数据生成导入到目标表中，同时插入到备份表中
参数描述：参数一(X_RESULT_STATUS):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dwl_prod_inv_material_cost_i()
*/

DECLARE
    V_SP_NAME  VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DWL_PROD_INV_MATERIAL_COST_I'; --存储过程名称
    V_STEP_NUM BIGINT        := 0; --步骤号

BEGIN
    X_RESULT_STATUS = 'SUCCESS';

    --0.开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => V_SP_NAME || '开始执行');

    --支持重跑，清除目标表要插入会计期的数据; temp表每次数据集成都会truncate掉
    --FOC_DWL_PROD_INV_MATERIAL_COST_I、FOC_DWL_PROD_INV_MATERIAL_COST_I_BAK_NO_DELETE为备份表   DWL_PROD_INV_MATERIAL_COST_I为最终目标表
    DELETE
    FROM fin_dm_opt_foi.DWL_PROD_INV_MATERIAL_COST_I
    WHERE period_id IN (SELECT DISTINCT period_id FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_TEMP);

    DELETE
    FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I
    WHERE period_id IN (SELECT DISTINCT period_id FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_TEMP);

    DELETE
    FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_BAK_NO_DELETE
    WHERE period_id IN (SELECT DISTINCT period_id FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_TEMP);

    --插入备份表1
    INSERT INTO fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I
    (period_id,
     cost_type_name,
     tl_resource,
     tl_outside_processing,
     tl_overhead,
     material_id,
     inventory_org_id)
    SELECT period_id,
           cost_type_name,
           tl_resource,
           tl_outside_processing,
           tl_overhead,
           material_id,
           inventory_org_id
    FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_TEMP;


    ---插入备份表2
    INSERT INTO fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_BAK_NO_DELETE
    (period_id,
     cost_type_name,
     tl_resource,
     tl_outside_processing,
     tl_overhead,
     material_id,
     inventory_org_id)
    SELECT period_id,
           cost_type_name,
           tl_resource,
           tl_outside_processing,
           tl_overhead,
           material_id,
           inventory_org_id
    FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_TEMP;


    ---插入目标表
    INSERT INTO fin_dm_opt_foi.DWL_PROD_INV_MATERIAL_COST_I
    (period_id,
     cost_type_name,
     tl_resource,
     tl_outside_processing,
     tl_overhead,
     material_id,
     inventory_org_id)
    SELECT period_id,
           cost_type_name,
           tl_resource,
           tl_outside_processing,
           tl_overhead,
           material_id,
           inventory_org_id
    FROM fin_dm_opt_foi.FOC_DWL_PROD_INV_MATERIAL_COST_I_TEMP;


    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => '插入数据到DWL_PROD_INV_MATERIAL_COST_I表',
         F_DML_ROW_COUNT => SQL % ROWCOUNT,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS');


    --收集统计信息
    EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DWL_PROD_INV_MATERIAL_COST_I';
    EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.FOC_DWL_PROD_INV_MATERIAL_COST_I';
    EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.FOC_DWL_PROD_INV_MATERIAL_COST_I_BAK_NO_DELETE';

    --日志结束
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => V_SP_NAME || '运行结束, 收集统计信息完成!');
    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
             F_RESULT_STATUS => X_RESULT_STATUS,
             F_ERRBUF => SQLSTATE || ':' || SQLERRM
            );
        RETURN X_RESULT_STATUS;
END

$$
/

