-- Name: f_dm_foc_item_dtl_decode_202401; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_item_dtl_decode_202401(f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间:2023/03/21
创建人  :刘必华
最后修改时间:2023/12/31
最后修改人:罗若文
背景描述:1. 发货额解密; 2. 分视角收敛均价
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T(量纲颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_ITEM_DECODE_DTL_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_ITEM_DECODE_DTL_T(量纲颗粒度)
事例:FIN_DM_OPT_FOI.F_DM_FOC_ITEM_DTL_DECODE()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ITEM_DTL_DECODE_202401'; --存储过程名称
  V_VERSION_ID BIGINT ; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_BOM_ITEM_SHIP_DTL_T的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  -- 7月版本需求新增
  V_SQL  TEXT; -- SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  
  V_FROM_TABLE VARCHAR(50); -- 来源表
  V_TO_TABLE VARCHAR(50); -- 目标表
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(500);
  V_DIMENSION_CN_NAME VARCHAR(2000);
  V_DIMENSION_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(2000);
  V_IN_DIMENSION_CODE VARCHAR(200);
  V_IN_DIMENSION_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(200);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(200);
  --202401月版本需求新增量纲
  V_SPART_CODE VARCHAR(50);
  V_SPART_CN_NAME VARCHAR(50);
  V_IN_SPART_CODE VARCHAR(50);
  V_IN_SPART_CN_NAME VARCHAR(50);
  V_INSERT_SPART_CODE VARCHAR(100);
	
	
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
  IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ITEM_DECODE_DTL_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_ITEM_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ITEM_DECODE_DTL_T'; --目标表 
  ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_ITEM_T_202401';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ITEM_DECODE_DTL_T_202401'; --目标表 
  ELSE
    NULL;
  END IF;

  --1.清空目标表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TO_TABLE;
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空'||V_TO_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
 --创建Item实际数解密收敛临时表
    DROP TABLE IF EXISTS DECODE_DATA_TEMP;
    CREATE TEMPORARY TABLE DECODE_DATA_TEMP (
        VERSION_ID BIGINT,
        PERIOD_YEAR BIGINT,
        PERIOD_ID BIGINT,
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        --7月版本需求新增
        LV3_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
        --9月版本需求新增量纲
        DIMENSION_CODE    VARCHAR(500),
        DIMENSION_CN_NAME    VARCHAR(2000),
        DIMENSION_EN_NAME    VARCHAR(2000),
        DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
        DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
        DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
        DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
        DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
        DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
				--202401月版本需求新增量纲
				SPART_CODE VARCHAR(50),
				SPART_CN_NAME VARCHAR(50),
        L1_NAME VARCHAR(200),
        L2_NAME VARCHAR(200),
        L3_CEG_CODE    VARCHAR(50),
        L3_CEG_CN_NAME    VARCHAR(200),
        L3_CEG_SHORT_CN_NAME    VARCHAR(200),
        L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
        L4_CEG_CN_NAME    VARCHAR(200),
        L4_CEG_SHORT_CN_NAME    VARCHAR(200),
        CATEGORY_CODE CHARACTER VARYING(50),
        CATEGORY_CN_NAME CHARACTER VARYING(200),
        ITEM_CODE CHARACTER VARYING(50),
        ITEM_CN_NAME CHARACTER VARYING(2000),
        SHIP_QUANTITY NUMERIC,
        RMB_COST_AMT NUMERIC,
        VIEW_FLAG VARCHAR(2),
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(200),
        LV0_PROD_LIST_EN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PERIOD_ID,ITEM_CODE);

    --2.写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
        F_STEP_NUM =>  V_STEP_MUM,
        F_CAL_LOG_DESC => 'Item实际数解密收敛临时表创建完成',
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS');  
    
    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'T.LV3_PROD_RND_TEAM_CODE,';--7月版本需求新增
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='T.LV3_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'T.L1_NAME,';
    V_IN_L2_NAME := 'T.L2_NAME,';
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'T.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'T.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'T.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'T.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'T.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'T.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'T.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'T.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'T.DIMENSION_SUB_DETAIL_EN_NAME,';
    
		--202401月版本需求新增
    V_SPART_CODE :='SPART_CODE,';
    V_SPART_CN_NAME := 'SPART_CN_NAME,';
    V_IN_SPART_CODE := 'T.SPART_CODE,';
    V_IN_SPART_CN_NAME := 'T.SPART_CN_NAME,';
		
    --通用颗粒度的维度时，不需要L1、L2和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
			 
			 V_SPART_CODE :='';
			 V_SPART_CN_NAME := '';
			 V_IN_SPART_CODE := '';
			 V_IN_SPART_CN_NAME := '';
       
    --盈利颗粒度的维度时，不需要LV3和所有量纲字段
   ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
        
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
			 V_SPART_CODE :='';
			 V_SPART_CN_NAME := '';
			 V_IN_SPART_CODE := '';
			 V_IN_SPART_CN_NAME := '';
       
    --量纲颗粒度的维度时，不需要L1、L2字段
   ELSIF F_DIMENSION_TYPE = 'D' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
   ELSE
      NULL;
    END IF;
        
    V_SQL := 
   'INSERT INTO DECODE_DATA_TEMP(
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_ID,
                LV0_PROD_RND_TEAM_CODE,
                LV0_PROD_RD_TEAM_CN_NAME,
                LV1_PROD_RND_TEAM_CODE,
                LV1_PROD_RD_TEAM_CN_NAME,
                LV2_PROD_RND_TEAM_CODE,
                LV2_PROD_RD_TEAM_CN_NAME,'||
                V_LV3_PROD_RND_TEAM_CODE ||
                V_LV3_PROD_RD_TEAM_CN_NAME ||
                V_L1_NAME ||
                V_L2_NAME ||
                V_DIMENSION_CODE ||
                V_DIMENSION_CN_NAME ||
                V_DIMENSION_EN_NAME||
                V_DIMENSION_SUBCATEGORY_CODE ||
                V_DIMENSION_SUBCATEGORY_CN_NAME ||
                V_DIMENSION_SUBCATEGORY_EN_NAME||
                V_DIMENSION_SUB_DETAIL_CODE ||
                V_DIMENSION_SUB_DETAIL_CN_NAME ||
                V_DIMENSION_SUB_DETAIL_EN_NAME ||
								V_SPART_CODE||
								V_SPART_CN_NAME ||'
                L3_CEG_CODE,
                L3_CEG_CN_NAME,
                L3_CEG_SHORT_CN_NAME,
                L4_CEG_CODE,
                L4_CEG_CN_NAME,
                L4_CEG_SHORT_CN_NAME,
                CATEGORY_CODE,
                CATEGORY_CN_NAME,
                ITEM_CODE,
                ITEM_CN_NAME,
                SHIP_QUANTITY,
                RMB_COST_AMT,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME,
                LV0_PROD_LIST_EN_NAME)
       SELECT T.VERSION_ID,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME||
              V_IN_L2_NAME||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
							V_IN_SPART_CODE||
							V_IN_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.SHIP_QUANTITY,
              --TO_NUMBER(GS_DECRYPT(T.RMB_COST_AMT,''F_KEYSTR'', ''AES128'', ''CBC'',  ''SHA256'')) AS RMB_COST_AMT,
              T.RMB_COST_AMT AS RMB_COST_AMT,
              T.VIEW_FLAG,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
            FROM '||V_FROM_TABLE ||' T
            WHERE T.ONLY_ITEM_FLAG = ''N''  ';
       
    EXECUTE IMMEDIATE V_SQL;
           
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入会话级临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
  --版本号赋值
  V_SQL := 'SELECT VERSION_ID FROM '||V_FROM_TABLE||' T LIMIT 1 ';
  EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  
  --插入均价数据
  V_SQL := 
  'INSERT INTO '|| V_TO_TABLE ||' 
       (VERSION_ID,
        PERIOD_YEAR,
        PERIOD_ID,
        LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE,
        LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,
        LV2_PROD_RD_TEAM_CN_NAME,'||
        V_LV3_PROD_RND_TEAM_CODE ||
        V_LV3_PROD_RD_TEAM_CN_NAME ||
        V_L1_NAME ||
        V_L2_NAME ||
        V_DIMENSION_CODE ||
        V_DIMENSION_CN_NAME ||
        V_DIMENSION_EN_NAME||
        V_DIMENSION_SUBCATEGORY_CODE ||
        V_DIMENSION_SUBCATEGORY_CN_NAME ||
        V_DIMENSION_SUBCATEGORY_EN_NAME||
        V_DIMENSION_SUB_DETAIL_CODE ||
        V_DIMENSION_SUB_DETAIL_CN_NAME ||
        V_DIMENSION_SUB_DETAIL_EN_NAME ||
				V_SPART_CODE||
				V_SPART_CN_NAME ||'
        L3_CEG_CODE,
        L3_CEG_CN_NAME,
        L3_CEG_SHORT_CN_NAME,
        L4_CEG_CODE,
        L4_CEG_CN_NAME,
        L4_CEG_SHORT_CN_NAME,
        CATEGORY_CODE,
        CATEGORY_CN_NAME,
        ITEM_CODE,
        ITEM_CN_NAME,
        SHIP_QUANTITY,
        RMB_COST_AMT,
        RMB_AVG_AMT,
        CREATED_BY,
        CREATION_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATE_DATE,
        DEL_FLAG,
        VIEW_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME,
        LV0_PROD_LIST_EN_NAME)
    SELECT T.VERSION_ID,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_IN_LV3_PROD_RND_TEAM_CODE ||
              V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
              V_IN_L1_NAME||
              V_IN_L2_NAME||
              V_IN_DIMENSION_CODE ||
              V_IN_DIMENSION_CN_NAME ||
              V_IN_DIMENSION_EN_NAME||
              V_IN_DIMENSION_SUBCATEGORY_CODE ||
              V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
              V_IN_DIMENSION_SUB_DETAIL_CODE ||
              V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
							V_IN_SPART_CODE||
							V_IN_SPART_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              SUM(T.SHIP_QUANTITY) AS SHIP_QUANTITY,
              SUM(T.RMB_COST_AMT) AS RMB_COST_AMT,
              --GS_ENCRYPT(SUM(T.RMB_COST_AMT),''f_keystr'',''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,
              CASE
                WHEN SUM(T.RMB_COST_AMT) = 0 OR SUM(T.SHIP_QUANTITY) = 0 THEN
                 NULL --聚合后, 有量无价或者有价无量的情况, 均价置空
                ELSE
                SUM(T.RMB_COST_AMT) / SUM(T.SHIP_QUANTITY)
                 --GS_ENCRYPT(SUM(T.RMB_COST_AMT) / SUM(T.SHIP_QUANTITY),''f_keystr'', ''AES128'', ''CBC'', ''SHA256'') --总金额/总销量
              END AS RMB_AVG_AMT,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              ''N'' AS DEL_FLAG,
              T.VIEW_FLAG,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
         FROM DECODE_DATA_TEMP T
        GROUP BY T.VERSION_ID,
                 T.VIEW_FLAG,
                 T.PERIOD_YEAR,
                 T.PERIOD_ID,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_IN_LV3_PROD_RND_TEAM_CODE ||
                 V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
                 V_IN_L1_NAME||
                 V_IN_L2_NAME||
                 V_IN_DIMENSION_CODE ||
                 V_IN_DIMENSION_CN_NAME ||
                 V_IN_DIMENSION_EN_NAME||
                 V_IN_DIMENSION_SUBCATEGORY_CODE ||
                 V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_IN_DIMENSION_SUB_DETAIL_CODE ||
                 V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
								 V_IN_SPART_CODE||
								 V_IN_SPART_CN_NAME ||'
                 T.L3_CEG_CODE,
                 T.L3_CEG_CN_NAME,
                 T.L3_CEG_SHORT_CN_NAME,
                 T.L4_CEG_CODE,
                 T.L4_CEG_CN_NAME,
                 T.L4_CEG_SHORT_CN_NAME,
                 T.CATEGORY_CODE,
                 T.CATEGORY_CN_NAME,
                 T.ITEM_CODE,
                 T.ITEM_CN_NAME,
                 T.CALIBER_FLAG,
                 T.OVERSEA_FLAG,
                 T.LV0_PROD_LIST_CODE,
                 T.LV0_PROD_LIST_CN_NAME,
                 T.LV0_PROD_LIST_EN_NAME';    

    EXECUTE IMMEDIATE V_SQL;                 
                          
  --3.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '收敛均价, 并插数到'||V_TO_TABLE||', 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   
   
  --如果是通用颗粒度需要增加反向视角4的数据
  IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
  V_SQL := 
  'INSERT INTO '|| V_TO_TABLE ||' 
       (VERSION_ID,
        PERIOD_YEAR,
        PERIOD_ID,
        LV0_PROD_RND_TEAM_CODE,
        LV0_PROD_RD_TEAM_CN_NAME,
        LV1_PROD_RND_TEAM_CODE,
        LV1_PROD_RD_TEAM_CN_NAME,
        LV2_PROD_RND_TEAM_CODE,
        LV2_PROD_RD_TEAM_CN_NAME,
        LV3_PROD_RND_TEAM_CODE,
        LV3_PROD_RD_TEAM_CN_NAME,
        L3_CEG_CODE,
        L3_CEG_CN_NAME,
        L3_CEG_SHORT_CN_NAME,
        L4_CEG_CODE,
        L4_CEG_CN_NAME,
        L4_CEG_SHORT_CN_NAME,
        CATEGORY_CODE,
        CATEGORY_CN_NAME,
        ITEM_CODE,
        ITEM_CN_NAME,
        SHIP_QUANTITY,
        RMB_COST_AMT,
        RMB_AVG_AMT,
        CREATED_BY,
        CREATION_DATE,
        LAST_UPDATED_BY,
        LAST_UPDATE_DATE,
        DEL_FLAG,
        VIEW_FLAG,
        CALIBER_FLAG,
        OVERSEA_FLAG,
        LV0_PROD_LIST_CODE,
        LV0_PROD_LIST_CN_NAME,
        LV0_PROD_LIST_EN_NAME)
       SELECT T.VERSION_ID,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              NULL AS LV0_PROD_RND_TEAM_CODE,
              NULL AS LV0_PROD_RD_TEAM_CN_NAME,
              NULL AS LV1_PROD_RND_TEAM_CODE,
              NULL AS LV1_PROD_RD_TEAM_CN_NAME,
              NULL AS LV2_PROD_RND_TEAM_CODE,
              NULL AS LV2_PROD_RD_TEAM_CN_NAME,
              NULL AS LV3_PROD_RND_TEAM_CODE,
              NULL AS LV3_PROD_RD_TEAM_CN_NAME,
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              SUM(T.SHIP_QUANTITY) AS SHIP_QUANTITY,
              SUM(T.RMB_COST_AMT)AS RMB_COST_AMT,
              --GS_ENCRYPT(SUM(T.RMB_COST_AMT),''f_keystr'',''AES128'', ''CBC'', ''SHA256'') AS RMB_COST_AMT,
              CASE
                WHEN SUM(T.RMB_COST_AMT) = 0 OR SUM(T.SHIP_QUANTITY) = 0 THEN
                 NULL --聚合后, 有量无价或者有价无量的情况, 均价置空
                ELSE
                 --GS_ENCRYPT(SUM(T.RMB_COST_AMT) / SUM(T.SHIP_QUANTITY),'''||f_keystr||''', ''AES128'', ''CBC'', ''SHA256'') --总金额/总销量
                 (SUM(T.RMB_COST_AMT) / SUM(T.SHIP_QUANTITY)
              END AS RMB_AVG_AMT,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              ''N'' AS DEL_FLAG,
              4 AS VIEW_FLAG,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
         FROM DECODE_DATA_TEMP T -- 该临时表数据取自 FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T
        WHERE T.VIEW_FLAG = ''3'' --用视角VIEW_FLAG=3去生成反向视角VIEW_FLAG=4的数据
     GROUP BY T.VERSION_ID,
              --T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.PERIOD_ID,
              --T.LV0_PROD_RND_TEAM_CODE,
              --T.LV0_PROD_RD_TEAM_CN_NAME,
              --T.LV1_PROD_RND_TEAM_CODE,
              --T.LV1_PROD_RD_TEAM_CN_NAME,
              --T.LV2_PROD_RND_TEAM_CODE,
              --T.LV2_PROD_RD_TEAM_CN_NAME,
              --T.LV3_PROD_RND_TEAM_CODE,
              --T.LV3_PROD_RD_TEAM_CN_NAME,
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.CALIBER_FLAG,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME';
         
            EXECUTE IMMEDIATE V_SQL;


  --4.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '通用颗粒度的反向视角view_flag=4的数据, 插数到'||V_TO_TABLE||', 版本号='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
       END IF;
   
  
  --5.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --6.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

