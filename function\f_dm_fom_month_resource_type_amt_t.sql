-- Name: f_dm_fom_month_resource_type_amt_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_month_resource_type_amt_t(f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-09
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-分资源类型金额表初始化
参数描述：参数一(F_VERSION_ID)：运行版本号
		  参数二(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T 分资源类型中间表
目标表：FIN_DM_OPT_FOI.DM_FOM_MONTH_RESOURCE_TYPE_AMT_T 分资源类型金额表（成本分布图）
事例  ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_MONTH_RESOURCE_TYPE_AMT_T(''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_MONTH_RESOURCE_TYPE_AMT_T';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP) - 2) || '01','YYYYMM'); 

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_RESOURCE_TYPE_AMT_T WHERE VERSION_ID = V_VERSION ;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除同版本数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
  V_STEP_NUM := V_STEP_NUM + 1;
  --2.分层金额卷积  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     RMB_COST_AMT,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     RESOURCE_TYPE)
    
    --发货对象层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           '',
           '',
           SHIPPING_OBJECT_CODE AS GROUP_CODE,
           SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'SHIPPING_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT) AS ACTUAL_COST_AMT,
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,
                       LV1_CODE,
                       BUSSINESS_OBJECT_CODE,
                       SHIPPING_OBJECT_CODE,
					   PERIOD_ID),
                  0) AS WEIGHT_RATE,
           BUSSINESS_OBJECT_CODE AS PARENT_CODE,
           BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           RESOURCE_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T
     WHERE GROUP_LEVEL = 'MANUFACTURE_OBJECT'
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              SHIPPING_OBJECT_CODE,
              SHIPPING_OBJECT_CN_NAME,
              PERIOD_YEAR,
              PERIOD_ID,
              RESOURCE_TYPE
    
    UNION ALL
    --经营对象层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           '',
           '',
           '',
           '',
           BUSSINESS_OBJECT_CODE AS GROUP_CODE,
           BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'BUSSINESS_OBJECT' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,
                       LV1_CODE,
                       BUSSINESS_OBJECT_CODE,
					   PERIOD_ID),
                  0) AS WEIGHT_RATE,
           LV1_CODE AS PARENT_CODE,
           LV1_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           RESOURCE_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T
     WHERE GROUP_LEVEL = 'MANUFACTURE_OBJECT'
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              BUSSINESS_OBJECT_CODE,
              BUSSINESS_OBJECT_CN_NAME,
              PERIOD_YEAR,
              PERIOD_ID,
              RESOURCE_TYPE
    
    UNION ALL
    --LV1层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           '',
           '',
           '',
           '',
           '',
           '',
           LV1_CODE AS GROUP_CODE,
           LV1_CN_NAME AS GROUP_CN_NAME,
           'LV1' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           SUM(RMB_COST_AMT) / NULLIF(SUM(SUM(RMB_COST_AMT))
                                      OVER(PARTITION BY LV0_CODE, LV1_CODE,PERIOD_ID),
                                      0) AS WEIGHT_RATE,
           LV0_CODE AS PARENT_CODE,
           LV0_CN_NAME AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           RESOURCE_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T
     WHERE GROUP_LEVEL IN ('MANUFACTURE_OBJECT','BUSSINESS_OBJECT') --加入海思与云核心网的经营对象层级数据卷积
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              PERIOD_YEAR,
              PERIOD_ID,
              RESOURCE_TYPE
    
    UNION ALL
    --LV0层级金额卷积
    SELECT V_VERSION AS VERSION_ID,
           PERIOD_YEAR,
           PERIOD_ID,
           LV0_CODE,
           LV0_CN_NAME,
           '',
           '',
           '',
           '',
           '',
           '',
           '',
           '',
           LV0_CODE AS GROUP_CODE,
           LV0_CN_NAME AS GROUP_CN_NAME,
           'LV0' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT),
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,PERIOD_ID), 0) AS WEIGHT_RATE,
           '' AS PARENT_CODE,
           '' AS PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           RESOURCE_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T
     WHERE GROUP_LEVEL IN ('MANUFACTURE_OBJECT','BUSSINESS_OBJECT') --加入海思与云核心网的经营对象层级数据卷积
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              PERIOD_YEAR,
              PERIOD_ID,
              RESOURCE_TYPE
;
  		
			
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '成本分布图实际数插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
--3.缺失月份金额补0
  WITH ACTUAL_AMT_TEMP AS
   ( --实际存在的所有维度
    SELECT DISTINCT LV0_CODE,
                     LV0_CN_NAME,
                     LV1_CODE,
                     LV1_CN_NAME,
                     BUSSINESS_OBJECT_CODE,
                     BUSSINESS_OBJECT_CN_NAME,
                     SHIPPING_OBJECT_CODE,
                     SHIPPING_OBJECT_CN_NAME,
                     MANUFACTURE_OBJECT_CODE,
                     MANUFACTURE_OBJECT_CN_NAME,
                     GROUP_CODE,
					 GROUP_CN_NAME,
					 GROUP_LEVEL,
					 RESOURCE_TYPE,
					 PARENT_CODE,
					 PARENT_CN_NAME
      FROM FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T
	  WHERE VERSION_ID = V_VERSION ),
  
  PERIOD_DIM_TEMP AS
   ( --生成连续月份, 两年前第1月至当前系统月(不含)
    SELECT CAST(TO_CHAR(ADD_MONTHS(V_BEGIN_DATE, NUM.VAL - 1), 'YYYYMM') AS
                 BIGINT) AS PERIOD_ID
      FROM GENERATE_SERIES(1,
                            TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                    V_BEGIN_DATE,
                                                    CURRENT_TIMESTAMP)),
                            1) NUM(VAL)),
  
  CROSS_JOIN_TEMP AS
   (
    --生成连续年月的发散维
    SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
            B.PERIOD_ID,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.BUSSINESS_OBJECT_CODE,
            T1.BUSSINESS_OBJECT_CN_NAME,
            T1.SHIPPING_OBJECT_CODE,
            T1.SHIPPING_OBJECT_CN_NAME,
            T1.MANUFACTURE_OBJECT_CODE,
            T1.MANUFACTURE_OBJECT_CN_NAME,
            T1.GROUP_CODE,
			T1.GROUP_CN_NAME,
			T1.GROUP_LEVEL,
			T1.RESOURCE_TYPE,
			T1.PARENT_CODE,
			T1.PARENT_CN_NAME
      FROM ACTUAL_AMT_TEMP T1, PERIOD_DIM_TEMP B) 
      
  INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MONTH_RESOURCE_TYPE_AMT_T
    (VERSION_ID,
     PERIOD_YEAR,
     PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     ACTUAL_COST_AMT,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     RESOURCE_TYPE)
    SELECT V_VERSION AS VERSION_ID,
           T1.PERIOD_YEAR,
           T1.PERIOD_ID,
           T1.LV0_CODE,
           T1.LV0_CN_NAME,
           T1.LV1_CODE,
           T1.LV1_CN_NAME,
           T1.BUSSINESS_OBJECT_CODE,
           T1.BUSSINESS_OBJECT_CN_NAME,
           T1.SHIPPING_OBJECT_CODE,
           T1.SHIPPING_OBJECT_CN_NAME,
           T1.MANUFACTURE_OBJECT_CODE,
           T1.MANUFACTURE_OBJECT_CN_NAME,
           T1.GROUP_CODE,
           T1.GROUP_CN_NAME,
           T1.GROUP_LEVEL,
           NVL(T2.RMB_COST_AMT, 0) AS ACTUAL_COST_AMT,
           NVL(T2.WEIGHT_RATE, 0) AS WEIGHT_RATE,
           T1.PARENT_CODE,
           T1.PARENT_CN_NAME,
           '-1' AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           '-1' AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           'N' AS DEL_FLAG,
           T1.RESOURCE_TYPE
      FROM CROSS_JOIN_TEMP T1
      LEFT JOIN FIN_DM_OPT_FOI.DM_FOM_MONTH_MIDRESOURCE_TYPE_AMT_T T2
        ON NVL(T1.LV0_CODE, '0') = NVL(T2.LV0_CODE, '0')
       AND NVL(T1.LV1_CODE, '1') = NVL(T2.LV1_CODE, '1')
       AND NVL(T1.BUSSINESS_OBJECT_CODE, '2') =
           NVL(T2.BUSSINESS_OBJECT_CODE, '2')
       AND NVL(T1.SHIPPING_OBJECT_CODE, '3') =
           NVL(T2.SHIPPING_OBJECT_CODE, '3')
       AND NVL(T1.MANUFACTURE_OBJECT_CODE, '4') =
           NVL(T2.MANUFACTURE_OBJECT_CODE, '4')
       AND NVL(T1.GROUP_LEVEL, '5') = NVL(T2.GROUP_LEVEL, '5')
       AND NVL(T1.PERIOD_ID, '6') = NVL(T2.PERIOD_ID, '6')
       AND NVL(T1.RESOURCE_TYPE, '7') = NVL(T2.RESOURCE_TYPE, '7')
       AND NVL(T1.GROUP_CODE, '8') = NVL(T2.GROUP_CODE, '8');

		
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '规格品监控图数据落表成功',
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 

  
 ANALYZE FIN_DM_OPT_FOI.DM_FOM_MONTH_RESOURCE_TYPE_AMT_T;

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

