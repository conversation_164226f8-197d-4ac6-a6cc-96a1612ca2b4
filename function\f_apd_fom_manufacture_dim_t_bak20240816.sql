-- Name: f_apd_fom_manufacture_dim_t_bak20240816; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_apd_fom_manufacture_dim_t_bak20240816(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023/12/05
创建人  ：许灿烽
背景描述：制造量纲维表 增加字段 制造产品LV0,LV1,维度树FLAG：（1：在维度树、0：不在维度树）
参数描述:x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T --制造量纲维表  FIN_DM_OPT_FOI.APD_MANUFACTURING_PROD_DIM_TREE_T --制造产品维度树  FIN_DM_OPT_FOI.APD_MANUFACTURING_PROD_LV1_MAP_BU_T   --后面会提供 有差异的数据
目标表:FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T
事例：FIN_DM_OPT_FOI.F_APD_FOM_MANUFACTURE_DIM_T()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_APD_FOM_MANUFACTURE_DIM_T'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T'; -- 目标表
  V_DIM_DMS_VERSION_ID BIGINT; --制造量纲维表版本号, 
  V_DIM_TREE_VERSION_ID BIGINT; --版本号, 取自 FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

-- 制造量纲维表 版本号
  SELECT MAX(VERSION_ID)
    INTO V_DIM_DMS_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
   WHERE UPPER(DATA_TYPE) = 'DIM_DMS'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
;

  --VERSION_ID 制造产品维度树
  SELECT MAX(VERSION_ID)
    INTO V_DIM_TREE_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
   WHERE UPPER(DATA_TYPE) = 'DIM_TREE'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
;

  DROP TABLE IF EXISTS APD_FOM_MANUFACTURE_DIM_T_TEMP;
  CREATE TEMPORARY TABLE APD_FOM_MANUFACTURE_DIM_T_TEMP (
    item_code character varying(50),
    cn_desc character varying(1000),
    lv0_org_cn character varying(50),
    bussiness_object character varying(50),
    shipping_object character varying(50),
    manufacture_object character varying(50),
    manufacture_bu character varying(200),
    version_id bigint,
    dim_tree_flag integer,
    lv0_code character varying(50),
    lv0_cn_name character varying(200),
    lv1_code character varying(50),
    lv1_cn_name character varying(200),
	CREATED_BY character varying(200),
	LAST_UPDATED_BY character varying(200),
	CREATION_DATE timestamp without time zone,
	LAST_UPDATE_DATE timestamp without time zone
)
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;

INSERT INTO APD_FOM_MANUFACTURE_DIM_T_TEMP 
SELECT  
 T1.ITEM_CODE                     --编码
,T1.CN_DESC                      --名称（中文描述）
,T1.LV0_ORG_CN                   --LV0名称
,T1.BUSSINESS_OBJECT             --经营对象
,CASE WHEN T4.LV0_CN_NAME = 'ICT' AND T4.LV1_CN_NAME = '云核心网' THEN ''
       WHEN T4.LV0_CN_NAME = '海思光电' AND T4.LV1_CN_NAME = '海思光电' THEN '' ELSE T1.SHIPPING_OBJECT END AS SHIPPING_OBJECT              --发货对象
,CASE WHEN T4.LV0_CN_NAME = 'ICT' AND T4.LV1_CN_NAME = '云核心网' THEN ''
       WHEN T4.LV0_CN_NAME = '海思光电' AND T4.LV1_CN_NAME = '海思光电' THEN '' ELSE T1.MANUFACTURE_OBJECT END AS MANUFACTURE_OBJECT           --制造对象
,T1.MANUFACTURE_BU               --制造BU
,V_DIM_DMS_VERSION_ID AS VERSION_ID                   --版本ID
,CASE WHEN T4.MANUFACTURE_BU IS NULL THEN 0
      WHEN T4.MANUFACTURE_BU = '' THEN 0 
      ELSE 1 END AS DIM_TREE_FLAG   --维度树FLAG：（1：在维度树、0：不在维度树）
,T4.LV0_CODE                      --制造产品LV0
,T4.LV0_CN_NAME                   --制造产品LV0中文名称
,T4.LV1_CODE                      --制造产品LV1
,T4.LV1_CN_NAME                   --制造产品LV1中文名称
,T1.CREATED_BY                    --创建人
,T1.LAST_UPDATED_BY               --最后更新人
,T1.CREATION_DATE                 --创建日期
,T1.LAST_UPDATE_DATE              --最后更新日期
FROM FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T T1  --制造量纲维表
LEFT JOIN (
SELECT DISTINCT 
 T2.LV0_CODE
,T2.LV0_CN_NAME
,T2.LV1_CODE
,T2.LV1_CN_NAME  
,NVL(T3.MANUFACTURE_BU,T2.LV1_CN_NAME) AS MANUFACTURE_BU
FROM (SELECT * FROM FIN_DM_OPT_FOI.APD_MANUFACTURING_PROD_DIM_TREE_T WHERE VERSION_ID = V_DIM_TREE_VERSION_ID)T2         --制造产品维度树
LEFT JOIN FIN_DM_OPT_FOI.APD_MANUFACTURING_PROD_LV1_MAP_BU_T T3  --后面会提供 有差异的数据
ON(T2.LV1_CN_NAME = T3.LV1_CN_NAME)
)T4
ON(T1.MANUFACTURE_BU = T4.MANUFACTURE_BU)
WHERE T1.VERSION_ID = V_DIM_DMS_VERSION_ID
;

  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表,计算并插入制造产品LV0,LV1到 APD_FOM_MANUFACTURE_DIM_T_TEMP',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

DELETE FROM FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T WHERE VERSION_ID = V_DIM_DMS_VERSION_ID;
INSERT INTO FIN_DM_OPT_FOI.APD_FOM_MANUFACTURE_DIM_T
SELECT 
ITEM_CODE              --编码
,CN_DESC               --名称（中文描述）
,LV0_ORG_CN            --LV0名称
,BUSSINESS_OBJECT      --经营对象
,SHIPPING_OBJECT       --发货对象
,MANUFACTURE_OBJECT    --制造对象
,MANUFACTURE_BU        --制造BU
,V_DIM_DMS_VERSION_ID AS VERSION_ID            --版本ID
,DIM_TREE_FLAG         --维度树FLAG：（1：在维度树、0：不在维度树）
,LV0_CODE              --制造产品LV0
,LV0_CN_NAME           --制造产品LV0中文名称
,LV1_CODE              --制造产品LV1
,LV1_CN_NAME           --制造产品LV1中文名称
,CREATED_BY            --创建人
,CREATION_DATE         --创建日期
,LAST_UPDATED_BY       --最后更新人
,LAST_UPDATE_DATE      --最后更新日期
FROM APD_FOM_MANUFACTURE_DIM_T_TEMP;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空制造量纲维表后，把临时表数据插入制造量纲维表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

