-- Name: f_dm_foc_made_reverses_top_item_info; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_made_reverses_top_item_info(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/10/28
创建人  ：罗若文
最后修改时间:
最后修改人:
背景描述：1. 计算反向视角统计TOP ITEM的权重; 2.TOP标识依照已有的视角4 VIEW_FLAG=3 的数据打标识 3.增加数字能源适配
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,仅通用的有反向视角), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_TOP_MADE_INFO_T(通用颗粒度)+FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T(通用颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T(通用颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REVERSES_TOP_ITEM_INFO()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_MADE_REVERSES_TOP_ITEM_INFO'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自序列:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_S
  V_TOP_MADE_ID BIGINT; --TOP品类清单最新的版本号
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  
  V_SQL        TEXT;   --SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_L1_NAME VARCHAR(200);
  V_L2_NAME VARCHAR(200);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(200);
  V_IN_L1_NAME VARCHAR(200);
  V_IN_L2_NAME VARCHAR(200);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_FROM_TABLE1 VARCHAR(50); -- 来源表1
  V_FROM_TABLE2 VARCHAR(50); -- 来源表2
  V_TO_TABLE VARCHAR(50); -- 目标表
  V_VERSION_TABLE  VARCHAR(100);
  V_VIEW_NUM VARCHAR(1);
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U') THEN -- 仅通用颗粒度的需处理反向视角
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN 'SUCCESS'; -- 如果不是通用颗粒度直接成功结束
  END IF;
  
  IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_TOP_MADE_INFO_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T';--来源表2
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T';--目标表
	 V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'E'  THEN -- 通用颗粒度
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_MADE_INFO_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T';--来源表2
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_TOP_ITEM_INFO_T';--目标表  
	 V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
  
    ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS'  THEN -- 通用颗粒度
     V_FROM_TABLE1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_MADE_INFO_T';--来源表1
     V_FROM_TABLE2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T';--来源表2
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_TOP_ITEM_INFO_T';--目标表  
	 V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
	 
  
  END IF;
  

  --判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN   
  --查询TOP品类清单中最新的版本号, 作为TOP规格品的父版本ID
  SELECT T.VERSION_ID
    INTO V_TOP_MADE_ID 
    FROM DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'CATEGORY'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	  ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
	 
   
	 
 -- 查询该月版本是否已存在，若存在，沿用，否则新建 
   SELECT COUNT(1) INTO V_CURRENT_FLAG
	FROM
		DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM' ;
	
	 	
		
	-- FLAG 不等于0，说明已有版本号，沿用	
  IF V_CURRENT_FLAG <> 0 THEN 
 SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		DM_FOC_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM';
		
  ELSE
  --每次刷新TOP规格品清单, 赋予新的版本号
  SELECT DM_FOC_VERSION_INFO_S.NEXTVAL
    INTO V_VERSION_ID
    FROM DUAL;
  
  
  --往版本信息表记录本次TOP规格品信息, 版本号为V_VERSION_ID, 依赖的品类专家维V_TOP_MADE_ID
  INSERT INTO DM_FOC_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_TOP_MADE_ID,V_VERSION_NAME,1,'AUTO','ITEM',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   
   END IF;
   
  --判断版本表
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   
  --查询TOP品类清单中最新的版本号, 作为TOP规格品的父版本ID
  SELECT T.VERSION_ID 
    INTO V_TOP_MADE_ID 
    FROM DM_FOC_ENERGY_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'CATEGORY'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	  ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
	 
   
	 
 -- 查询该月版本是否已存在，若存在，沿用，否则新建 
   SELECT COUNT(1) INTO V_CURRENT_FLAG
	FROM
		DM_FOC_ENERGY_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM' ;
	
	 	
		
	-- FLAG 不等于0，说明已有版本号，沿用	
  IF V_CURRENT_FLAG <> 0 THEN 
 SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		DM_FOC_ENERGY_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM';
		
  ELSE
  --每次刷新TOP规格品清单, 赋予新的版本号
  SELECT DM_FOC_ENERGY_VERSION_INFO_S.NEXTVAL
    INTO V_VERSION_ID
    FROM DUAL;
  
  
  --往版本信息表记录本次TOP规格品信息, 版本号为V_VERSION_ID, 依赖的品类专家维V_TOP_MADE_ID
  INSERT INTO DM_FOC_ENERGY_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_TOP_MADE_ID,V_VERSION_NAME,1,'AUTO','ITEM',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   
   END IF;
 
--判断版本表
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   
  --查询TOP品类清单中最新的版本号, 作为TOP规格品的父版本ID
  SELECT T.VERSION_ID 
    INTO V_TOP_MADE_ID 
    FROM DM_FOC_IAS_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'CATEGORY'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	  ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
	 
   
	 
 -- 查询该月版本是否已存在，若存在，沿用，否则新建 
   SELECT COUNT(1) INTO V_CURRENT_FLAG
	FROM
		DM_FOC_IAS_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM' ;
	
	 	
		
	-- FLAG 不等于0，说明已有版本号，沿用	
  IF V_CURRENT_FLAG <> 0 THEN 
 SELECT VERSION_ID INTO V_VERSION_ID
	FROM
		DM_FOC_IAS_VERSION_INFO_T 
	WHERE
		VERSION = TO_CHAR(CURRENT_DATE, 'YYYY-MM')
		AND DEL_FLAG = 'N'
		AND STATUS = 1
		AND UPPER(DATA_TYPE) = 'ITEM';
		
  ELSE
  --每次刷新TOP规格品清单, 赋予新的版本号
  SELECT DM_FOC_IAS_VERSION_INFO_S.NEXTVAL
    INTO V_VERSION_ID
    FROM DUAL;
  
  
  --往版本信息表记录本次TOP规格品信息, 版本号为V_VERSION_ID, 依赖的品类专家维V_TOP_MADE_ID
  INSERT INTO DM_FOC_IAS_VERSION_INFO_T
  (VERSION_ID,
   PARENT_VERSION_ID,
   VERSION,
   STATUS,
   VERSION_TYPE,
   DATA_TYPE,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   IS_RUNNING)
   VALUES
   (V_VERSION_ID,V_TOP_MADE_ID,V_VERSION_NAME,1,'AUTO','ITEM',-1,CURRENT_TIMESTAMP,-1,CURRENT_TIMESTAMP,'N','Y');
   
   END IF; 
 
 
 ELSE 
    NULL;
END IF ; 
     
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往版本信息表记录TOP规格品版本号信息, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  

  --删除当前会计期版本的TOP规格品数据, 支持单月重刷(该反向视角函数仅新增VIEW_FLAG = 5的数据)
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VIEW_FLAG = 5 AND  T.VERSION_ID = '''||V_VERSION_ID||''''; -- 该反向视角函数仅新增VIEW_FLAG = 5的数据，重跑需清掉VIEW_FLAG = 5的
  EXECUTE IMMEDIATE V_SQL ;
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP规格品清单的数据, 删除版本名称='||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
    IF F_INDUSTRY_FLAG IN ('E','I') THEN 
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	V_VIEW_NUM := '3';
	
   ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'A.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := 'A.LV4_PROD_RD_TEAM_CN_NAME,';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := ' AND NVL(A.LV4_PROD_RND_TEAM_CODE, 3) = NVL(B.LV4_PROD_RND_TEAM_CODE, 3)';
	V_VIEW_NUM := '7';
	
	ELSE 
	    NULL;
		
	END IF;
  
  
  --创建分视角收敛ITEM的年发货额临时表 
  DROP TABLE IF EXISTS RMB_MANUFACTURE_TEMP ;
  CREATE TEMPORARY TABLE RMB_MANUFACTURE_TEMP
  (
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200),
    CALIBER_FLAG VARCHAR2(2),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR VARCHAR(50),
    YEAR_FLAG VARCHAR2(2),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV4_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(200),
    L2_NAME VARCHAR(200),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(500),
    DIMENSION_CN_NAME    VARCHAR(2000),
    DIMENSION_EN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
    SHIPPING_OBJECT_CODE VARCHAR(200),  --11月需求新增
    SHIPPING_OBJECT_CN_NAME VARCHAR(200),
    MANUFACTURE_OBJECT_CODE VARCHAR(200),
    MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    rmb_cost_amt NUMERIC
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;

    --创建汇总分视角收敛ITEM的年发货额临时表 
  DROP TABLE IF EXISTS SUM_MANUFACTURE_TEMP ;
  CREATE TEMPORARY TABLE SUM_MANUFACTURE_TEMP
  (
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200),
    CALIBER_FLAG VARCHAR2(2),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR VARCHAR(50),
    YEAR_FLAG VARCHAR2(2),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV4_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(200),
    L2_NAME VARCHAR(200),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(500),
    DIMENSION_CN_NAME    VARCHAR(2000),
    DIMENSION_EN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(500),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(2000),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(500),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(2000),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(2000),
    SHIPPING_OBJECT_CODE VARCHAR(200),  --11月需求新增
    SHIPPING_OBJECT_CN_NAME VARCHAR(200),
    MANUFACTURE_OBJECT_CODE VARCHAR(200),
    MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    rmb_cost_amt NUMERIC
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;

  
  
  --分视角分地区(全球、国内、海外)分BG去计算TOP品类下的ITEM权重 (9月版本需求新增)
  DROP TABLE IF EXISTS DM_FOC_WEIGHT_ITEM_INFO_TEMP ;
  CREATE TEMPORARY TABLE DM_FOC_WEIGHT_ITEM_INFO_TEMP
  (
    VERSION_ID BIGINT,
    VERSION_NAME VARCHAR2(50),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR	VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    LV4_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    SHIPPING_OBJECT_CODE VARCHAR(200),  --11月需求新增
    SHIPPING_OBJECT_CN_NAME VARCHAR(200),
    MANUFACTURE_OBJECT_CODE VARCHAR(200),
    MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
    ITEM_CODE VARCHAR(50),
    ITEM_CN_NAME VARCHAR(1000),
    WEIGHT_RATE    NUMERIC,
    PARENT_CODE VARCHAR(50),
    PARENT_WEIGHT_RATE NUMERIC,
    DOUBLE_FLAG VARCHAR2(2),
    CALIBER_FLAG VARCHAR2(2),
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  
    
      --第一步先处理按照分地区(全球、国内、海外)分BG计算得到一份权重数据存入临时表
      --数据插入分视角收敛ITEM的年发货额
      V_SQL := 
      'INSERT INTO RMB_MANUFACTURE_TEMP
        ( CALIBER_FLAG,
         VIEW_FLAG,
         PERIOD_YEAR,
         YEAR_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,'
		||V_LV4_PROD_RND_TEAM_CODE
		||V_LV4_PROD_RD_TEAM_CN_NAME||'
		 SHIPPING_OBJECT_CODE,
		 SHIPPING_OBJECT_CN_NAME,
		 MANUFACTURE_OBJECT_CODE ,
		 MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         rmb_cost_amt,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME
        )
      --分视角收敛ITEM的年发货额
      SELECT  T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              CASE
                WHEN T.PERIOD_YEAR BETWEEN YEAR(CURRENT_TIMESTAMP) - 1 AND
                     YEAR(CURRENT_TIMESTAMP) THEN
                 1
                ELSE
                 0
              END AS YEAR_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,
              T.LV3_PROD_RND_TEAM_CODE,
              T.LV3_PROD_RD_TEAM_CN_NAME,'
			||V_LV4_PROD_RND_TEAM_CODE
			||V_LV4_PROD_RD_TEAM_CN_NAME||'
              T.SHIPPING_OBJECT_CODE,
			  T.SHIPPING_OBJECT_CN_NAME,
			  T.MANUFACTURE_OBJECT_CODE ,
			  T.MANUFACTURE_OBJECT_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              T.rmb_cost_amt ,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE2 ||' T 
       WHERE T.VIEW_FLAG = '''||V_VIEW_NUM||''' -- 用视角4的数据去加工视角6的数据
        '; 
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '数据插入分视角分地区(全球、国内、海外)分BG收敛ITEM的年发货额, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

      --第一步先处理按照分地区(全球、国内、海外)分BG计算得到一份权重数据存入临时表
      --汇总数据插入汇总分视角收敛ITEM的年发货额
      V_SQL := 
      'INSERT INTO SUM_MANUFACTURE_TEMP
        ( CALIBER_FLAG,
         VIEW_FLAG,
         PERIOD_YEAR,
         YEAR_FLAG,
         LV0_PROD_RND_TEAM_CODE,
         LV0_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV3_PROD_RND_TEAM_CODE,
         LV3_PROD_RD_TEAM_CN_NAME,'
		||V_LV4_PROD_RND_TEAM_CODE
		||V_LV4_PROD_RD_TEAM_CN_NAME||'
         SHIPPING_OBJECT_CODE,
		 SHIPPING_OBJECT_CN_NAME,
		 MANUFACTURE_OBJECT_CODE ,
		 MANUFACTURE_OBJECT_CN_NAME,
         ITEM_CODE,
         ITEM_CN_NAME,
         rmb_cost_amt,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_EN_NAME
        )
      --分视角收敛ITEM的年发货额
      SELECT  T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.YEAR_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,
              T.LV3_PROD_RND_TEAM_CODE,
              T.LV3_PROD_RD_TEAM_CN_NAME,'
			||V_LV4_PROD_RND_TEAM_CODE
			||V_LV4_PROD_RD_TEAM_CN_NAME||'
              T.SHIPPING_OBJECT_CODE,
			  T.SHIPPING_OBJECT_CN_NAME,
			  T.MANUFACTURE_OBJECT_CODE ,
			  T.MANUFACTURE_OBJECT_CN_NAME,
              T.ITEM_CODE,
              T.ITEM_CN_NAME,
              SUM(T.rmb_cost_amt) AS rmb_cost_amt,
              T.OVERSEA_FLAG,
              T.LV0_PROD_LIST_CODE,
              T.LV0_PROD_LIST_CN_NAME,
              T.LV0_PROD_LIST_EN_NAME
        FROM RMB_MANUFACTURE_TEMP T
       GROUP BY  T.CALIBER_FLAG,
                 T.VIEW_FLAG,
                 T.YEAR_FLAG,
                 T.PERIOD_YEAR,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,
                 T.LV3_PROD_RND_TEAM_CODE,
                 T.LV3_PROD_RD_TEAM_CN_NAME,
                 T.LV3_PROD_RND_TEAM_CODE,
                 T.LV3_PROD_RD_TEAM_CN_NAME,'
				||V_LV4_PROD_RND_TEAM_CODE
				||V_LV4_PROD_RD_TEAM_CN_NAME||'				 
                 T.SHIPPING_OBJECT_CODE,
			     T.SHIPPING_OBJECT_CN_NAME,
			     T.MANUFACTURE_OBJECT_CODE ,
			     T.MANUFACTURE_OBJECT_CN_NAME,
                 T.ITEM_CODE,
                 T.ITEM_CN_NAME,
                 T.OVERSEA_FLAG,
                 T.LV0_PROD_LIST_CODE,
                 T.LV0_PROD_LIST_CN_NAME,
                 T.LV0_PROD_LIST_EN_NAME';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '汇总数据插入汇总分视角分地区(全球、国内、海外)分BG收敛ITEM的年发货额, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');


  --第一步先处理按照分地区(全球、国内、海外)分BG计算得到一份权重数据存入临时表
  V_SQL :=
  'INSERT INTO DM_FOC_WEIGHT_ITEM_INFO_TEMP
    (VERSION_ID,
     VERSION_NAME,
     VIEW_FLAG,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,'
	||V_LV4_PROD_RND_TEAM_CODE
	||V_LV4_PROD_RD_TEAM_CN_NAME||'
     SHIPPING_OBJECT_CODE,
	 SHIPPING_OBJECT_CN_NAME,
	 MANUFACTURE_OBJECT_CODE ,
	 MANUFACTURE_OBJECT_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_WEIGHT_RATE,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
    WITH TOP_MADE_TEMP AS
     (
      --取最新版本号的TOP品类
      SELECT DISTINCT  T.LV0_PROD_RND_TEAM_CODE,
                       T.LV0_PROD_RD_TEAM_CN_NAME,
                       T.LV1_PROD_RND_TEAM_CODE,
                       T.LV1_PROD_RD_TEAM_CN_NAME,
                       T.LV2_PROD_RND_TEAM_CODE,
                       T.LV2_PROD_RD_TEAM_CN_NAME,
                       T.LV3_PROD_RND_TEAM_CODE,
                       T.LV3_PROD_RD_TEAM_CN_NAME,'
						||V_LV4_PROD_RND_TEAM_CODE
						||V_LV4_PROD_RD_TEAM_CN_NAME||'
                       T.TOP_SHIPPING_OBJECT_CODE AS SHIPPING_OBJECT_CODE,
					   T.TOP_SHIPPING_OBJECT_CN_NAME AS SHIPPING_OBJECT_CN_NAME,
					   T.TOP_MANUFACTURE_OBJECT_CODE AS MANUFACTURE_OBJECT_CODE,
					   T.TOP_MANUFACTURE_OBJECT_CN_NAME AS MANUFACTURE_OBJECT_CN_NAME,
                       T.VIEW_FLAG,
                       T.WEIGHT_RATE,
                       T.CALIBER_FLAG,
                       T.OVERSEA_FLAG,
                       T.LV0_PROD_LIST_CODE,
                       T.LV0_PROD_LIST_CN_NAME,
                       T.LV0_PROD_LIST_EN_NAME
        FROM '||V_FROM_TABLE1 ||' T -- TOP制造对象表
       WHERE T.IS_TOP_FLAG = ''Y''
         AND T.VIEW_FLAG = '''||V_VIEW_NUM||''' -- 用视角4的数据去加工视角6的数据
         AND T.VERSION_ID = '|| V_TOP_MADE_ID ||'),
    
    TOP_ITEM_TEMP AS
     (
      --分视角下分地区(全球、国内、海外)分BG计算筛选TOP品类的ITEM
      SELECT  A.CALIBER_FLAG,
              A.VIEW_FLAG,
              TO_CHAR(A.PERIOD_YEAR) AS PERIOD_YEAR,
              A.YEAR_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,
              A.LV3_PROD_RND_TEAM_CODE,
              A.LV3_PROD_RD_TEAM_CN_NAME,
               '||V_IN_LV4_PROD_RND_TEAM_CODE
			||V_IN_LV4_PROD_RD_TEAM_CN_NAME||'
              A.SHIPPING_OBJECT_CODE,
			  A.SHIPPING_OBJECT_CN_NAME,
			  A.MANUFACTURE_OBJECT_CODE ,
			  A.MANUFACTURE_OBJECT_CN_NAME,
              A.ITEM_CODE,
              A.ITEM_CN_NAME,
              A.rmb_cost_amt,
              SUM(A.rmb_cost_amt) OVER(PARTITION BY A.OVERSEA_FLAG, A.LV0_PROD_LIST_CODE, A.CALIBER_FLAG, A.VIEW_FLAG, A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE, A.LV3_PROD_RND_TEAM_CODE,'||V_IN_LV3_PROD_RND_TEAM_CODE||' A.SHIPPING_OBJECT_CODE, A.MANUFACTURE_OBJECT_CODE, A.PERIOD_YEAR) AS TOTAL_CATE_AMT,
              B.MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
              B.WEIGHT_RATE AS PARENT_WEIGHT_RATE,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME
        FROM SUM_MANUFACTURE_TEMP A
       INNER JOIN TOP_MADE_TEMP B
          ON A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
         AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) =
             NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
         AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) =
             NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
         AND NVL(A.LV3_PROD_RND_TEAM_CODE, 3) =
             NVL(B.LV3_PROD_RND_TEAM_CODE, 3)
			 '||V_INSERT_LV4_PROD_RND_TEAM_CODE||'
         AND A.SHIPPING_OBJECT_CODE = B.SHIPPING_OBJECT_CODE
		 AND A.MANUFACTURE_OBJECT_CODE = B.MANUFACTURE_OBJECT_CODE
         AND A.VIEW_FLAG = B.VIEW_FLAG
         AND A.CALIBER_FLAG = B.CALIBER_FLAG
         AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
         AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE)
    
    --分视角计算各年的权重值
    SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
           '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           M.VIEW_FLAG,
           DECODE(M.PERIOD_YEAR,
                  YEAR(CURRENT_TIMESTAMP),
                  M.PERIOD_YEAR || ''YTD'',
                  M.PERIOD_YEAR) AS PERIOD_YEAR,
           M.LV0_PROD_RND_TEAM_CODE,
           M.LV0_PROD_RD_TEAM_CN_NAME,
           M.LV1_PROD_RND_TEAM_CODE,
           M.LV1_PROD_RD_TEAM_CN_NAME,
           M.LV2_PROD_RND_TEAM_CODE,
           M.LV2_PROD_RD_TEAM_CN_NAME,
           M.LV3_PROD_RND_TEAM_CODE,
           M.LV3_PROD_RD_TEAM_CN_NAME,'
		||V_LV4_PROD_RND_TEAM_CODE
		||V_LV4_PROD_RD_TEAM_CN_NAME||'
           M.SHIPPING_OBJECT_CODE,
		   M.SHIPPING_OBJECT_CN_NAME,
		   M.MANUFACTURE_OBJECT_CODE ,
		   M.MANUFACTURE_OBJECT_CN_NAME,
           M.ITEM_CODE,
           M.ITEM_CN_NAME,
           M.rmb_cost_amt / NULLIF(M.TOTAL_CATE_AMT,0) AS WEIGHT_RATE,
           M.PARENT_CODE,
           M.PARENT_WEIGHT_RATE,
           ''N'' AS DOUBLE_FLAG,
           M.CALIBER_FLAG,
           M.OVERSEA_FLAG,
           M.LV0_PROD_LIST_CODE,
           M.LV0_PROD_LIST_CN_NAME,
           M.LV0_PROD_LIST_EN_NAME
      FROM TOP_ITEM_TEMP M
    UNION ALL
    --分视角计算上一年到今年YTD的TOP品类下item的权重值
    SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
           '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           S.VIEW_FLAG,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' ||
           YEAR(CURRENT_TIMESTAMP) || ''YTD'' AS PERIOD_YEAR,
           S.LV0_PROD_RND_TEAM_CODE,
           S.LV0_PROD_RD_TEAM_CN_NAME,
           S.LV1_PROD_RND_TEAM_CODE,
           S.LV1_PROD_RD_TEAM_CN_NAME,
           S.LV2_PROD_RND_TEAM_CODE,
           S.LV2_PROD_RD_TEAM_CN_NAME,
           S.LV3_PROD_RND_TEAM_CODE,
           S.LV3_PROD_RD_TEAM_CN_NAME,'
			||V_LV4_PROD_RND_TEAM_CODE
			||V_LV4_PROD_RD_TEAM_CN_NAME||'
           S.SHIPPING_OBJECT_CODE,
		   S.SHIPPING_OBJECT_CN_NAME,
		   S.MANUFACTURE_OBJECT_CODE ,
		   S.MANUFACTURE_OBJECT_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_CN_NAME,
           S.rmb_cost_amt / NULLIF(S.TOTAL_CATE_AMT,0) AS WEIGHT_RATE,
           S.PARENT_CODE,
           S.PARENT_WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG,
           S.CALIBER_FLAG,
           S.OVERSEA_FLAG,
           S.LV0_PROD_LIST_CODE,
           S.LV0_PROD_LIST_CN_NAME,
           S.LV0_PROD_LIST_EN_NAME
      FROM (SELECT P.CALIBER_FLAG,
                   P.VIEW_FLAG,
                   P.LV0_PROD_RND_TEAM_CODE,
                   P.LV0_PROD_RD_TEAM_CN_NAME,
                   P.LV1_PROD_RND_TEAM_CODE,
                   P.LV1_PROD_RD_TEAM_CN_NAME,
                   P.LV2_PROD_RND_TEAM_CODE,
                   P.LV2_PROD_RD_TEAM_CN_NAME,
                   P.LV3_PROD_RND_TEAM_CODE,
                   P.LV3_PROD_RD_TEAM_CN_NAME,'
				||V_LV4_PROD_RND_TEAM_CODE
				||V_LV4_PROD_RD_TEAM_CN_NAME||'
                   P.SHIPPING_OBJECT_CODE,
				   P.SHIPPING_OBJECT_CN_NAME,
				   P.MANUFACTURE_OBJECT_CODE ,
				   P.MANUFACTURE_OBJECT_CN_NAME,
                   P.ITEM_CODE,
                   P.ITEM_CN_NAME,
                   SUM(P.rmb_cost_amt) AS rmb_cost_amt,
                   SUM(SUM(P.rmb_cost_amt)) OVER(PARTITION BY P.OVERSEA_FLAG, P.LV0_PROD_LIST_CODE, P.CALIBER_FLAG, P.VIEW_FLAG, P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE, P.LV3_PROD_RND_TEAM_CODE,'||V_IN_LV3_PROD_RND_TEAM_CODE||' P.SHIPPING_OBJECT_CODE, P.MANUFACTURE_OBJECT_CODE) AS TOTAL_CATE_AMT,
                   P.PARENT_CODE AS PARENT_CODE,
                   MIN(P.PARENT_WEIGHT_RATE) AS PARENT_WEIGHT_RATE,
                   P.OVERSEA_FLAG,
                   P.LV0_PROD_LIST_CODE,
                   P.LV0_PROD_LIST_CN_NAME,
                   P.LV0_PROD_LIST_EN_NAME
              FROM TOP_ITEM_TEMP P
             WHERE P.YEAR_FLAG = 1
             GROUP BY P.CALIBER_FLAG,
                      P.VIEW_FLAG,
                      P.LV0_PROD_RND_TEAM_CODE,
                      P.LV0_PROD_RD_TEAM_CN_NAME,
                      P.LV1_PROD_RND_TEAM_CODE,
                      P.LV1_PROD_RD_TEAM_CN_NAME,
                      P.LV2_PROD_RND_TEAM_CODE,
                      P.LV2_PROD_RD_TEAM_CN_NAME,
                      P.LV3_PROD_RND_TEAM_CODE,
                      P.LV3_PROD_RD_TEAM_CN_NAME,'
					||V_LV4_PROD_RND_TEAM_CODE
					||V_LV4_PROD_RD_TEAM_CN_NAME||'
                      P.SHIPPING_OBJECT_CODE,
					  P.SHIPPING_OBJECT_CN_NAME,
					  P.MANUFACTURE_OBJECT_CODE ,
					  P.MANUFACTURE_OBJECT_CN_NAME,
                      P.ITEM_CODE,
                      P.ITEM_CN_NAME,
                      P.PARENT_CODE,
                      P.OVERSEA_FLAG,
                      P.LV0_PROD_LIST_CODE,
                      P.LV0_PROD_LIST_CN_NAME,
                      P.LV0_PROD_LIST_EN_NAME) S';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角分视角分地区(全球、国内、海外)分BG各年TOP规格品的权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    
----------------------------------------------------------------------以上第一步先处理按照分地区(全球、国内、海外)分BG的权重数据-------------

----------------------------------------------------------------------以下第二步TOP标签数据延用视角4的---------------------------------------




 --第一步往TOP规格品清单插数, 打上前95%TOP的标识
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     VERSION_NAME,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE,
     LV3_PROD_RD_TEAM_CN_NAME,'
	||V_LV4_PROD_RND_TEAM_CODE
	||V_LV4_PROD_RD_TEAM_CN_NAME||'
     TOP_SHIPPING_OBJECT_CODE,
	 TOP_SHIPPING_OBJECT_CN_NAME,
	 TOP_MANUFACTURE_OBJECT_CODE ,
	 TOP_MANUFACTURE_OBJECT_CN_NAME,
     TOP_ITEM_CODE,
     TOP_ITEM_CN_NAME,
     WEIGHT_RATE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     IS_TOP_FLAG,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)

    --临时表左关联TOP规格品临时表, 打上是否为TOP类标识
    SELECT A.VERSION_ID,
           A.VERSION_NAME,
           A.PERIOD_YEAR,
           A.LV0_PROD_RND_TEAM_CODE,
           A.LV0_PROD_RD_TEAM_CN_NAME,
           A.LV1_PROD_RND_TEAM_CODE,
           A.LV1_PROD_RD_TEAM_CN_NAME,
           A.LV2_PROD_RND_TEAM_CODE,
           A.LV2_PROD_RD_TEAM_CN_NAME,
           A.LV3_PROD_RND_TEAM_CODE,
           A.LV3_PROD_RD_TEAM_CN_NAME,'
		||V_IN_LV4_PROD_RND_TEAM_CODE
		||V_IN_LV4_PROD_RD_TEAM_CN_NAME||'
           A.SHIPPING_OBJECT_CODE,
		   A.SHIPPING_OBJECT_CN_NAME,
		   A.MANUFACTURE_OBJECT_CODE ,
		   A.MANUFACTURE_OBJECT_CN_NAME,
           A.ITEM_CODE,
           A.ITEM_CN_NAME,
           A.WEIGHT_RATE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           5 AS VIEW_FLAG, -- 生成新的视角6数据
           DECODE(B.IS_TOP_FLAG, ''N'', ''N'', ''Y'') AS IS_TOP_FLAG, --延用视角4的数据打top标识
           A.DOUBLE_FLAG,
           A.CALIBER_FLAG,
           A.OVERSEA_FLAG,
           A.LV0_PROD_LIST_CODE,
           A.LV0_PROD_LIST_CN_NAME,
           A.LV0_PROD_LIST_EN_NAME
      FROM DM_FOC_WEIGHT_ITEM_INFO_TEMP A
      LEFT JOIN '||V_TO_TABLE||' B
        ON A.VIEW_FLAG = B.VIEW_FLAG
       AND B.VIEW_FLAG = '||V_VIEW_NUM||' -- 延用视角4的数据打top标识
       AND A.PERIOD_YEAR = B.PERIOD_YEAR
	   AND A.VERSION_ID = B.VERSION_ID
	   AND B.VERSION_ID = '||V_VERSION_ID||'
       AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
       AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) = NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
       AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) = NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
       AND NVL(A.LV3_PROD_RND_TEAM_CODE, 3) = NVL(B.LV3_PROD_RND_TEAM_CODE, 3)'
	   ||V_INSERT_LV4_PROD_RND_TEAM_CODE||'
       AND A.SHIPPING_OBJECT_CODE = B.TOP_SHIPPING_OBJECT_CODE
	   AND A.MANUFACTURE_OBJECT_CODE = B.TOP_MANUFACTURE_OBJECT_CODE
       AND A.ITEM_CODE = B.TOP_ITEM_CODE
       AND A.CALIBER_FLAG = B.CALIBER_FLAG
       AND A.OVERSEA_FLAG = B.OVERSEA_FLAG
       AND A.LV0_PROD_LIST_CODE = B.LV0_PROD_LIST_CODE';
	DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往TOP规格品表里插入反向视角6的数据, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');




   
   
   
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

