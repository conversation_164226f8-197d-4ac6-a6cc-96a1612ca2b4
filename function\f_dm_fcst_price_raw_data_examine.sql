-- Name: f_dm_fcst_price_raw_data_examine; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_raw_data_examine(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2024年12月版本
创建人：twx1139790
背景描述：	1.计算国内海外全球月卷积金额
			2.筛选单SPART
			3.关联主力编码表字段
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_RAW_DATA_EXAMINE'; --存储过程名称
  V_VERSION_ID BIGINT ; --新版本号ID, 取自 FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_S
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION BIGINT ; --最新的品类-专家团映射维版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_CURRENT_FLAG BIGINT;   -- 存放当前是否已有版本号标识
  V_SQL TEXT;

BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
--新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

   --清空目标表数据
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表开始------'); 
 EXECUTE IMMEDIATE 'TRUNCATE FIN_DM_OPT_FOI.DM_FCST_PRICE_RAW_DATA_EXAMINE_T';
 DBMS_OUTPUT.PUT_LINE('-----清空临时目标表结束------'); 
  
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空DM_FCST_PRICE_RAW_DATA_EXAMINE_T数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
	
	
--向目标表插数,带上均本
V_SQL:= 'INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_RAW_DATA_EXAMINE_T (
	VERSION_ID,
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_CODE ,
	REGION_CN_NAME ,
	REPOFFICE_CODE ,
	REPOFFICE_CN_NAME ,
	HW_CONTRACT_NUM,
	BG_CODE ,
	BG_CN_NAME ,
	LV0_PROD_LIST_CODE ,
	LV0_PROD_LIST_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME , 
	USD_PNP_AMT ,
	USD_PNP_AVG,
	SPART_CODE,
	PROD_QTY,
	OVERSEA_FLAG ,
	SIGN_TOP_CUST_CATEGORY_CODE,
    SIGN_TOP_CUST_CATEGORY_CN_NAME,
    SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
	CREATED_BY,
	CREATION_DATE,
	LAST_UPDATED_BY,
	LAST_UPDATE_DATE,
	DEL_FLAG
	)
	SELECT 
	'||V_VERSION_ID||',
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_HRMS_ORG_CODE,
    REGION_HRMS_ORG_CN_NAME,
	REPOFFICE_ORG_CODE,
    REPOFFICE_ORG_CN_NAME,
	HW_CONTRACT_NUM,
	LV0_PROD_LIST_CODE AS BG_CODE ,
	LV0_PROD_LIST_CN_NAME AS BG_CN_NAME ,
	LV0_PROD_LIST_CODE ,
	LV0_PROD_LIST_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME ,  
	NULL AS USD_PNP_AMT,
	NULL AS USD_PNP_AVG,
    SPART_CODE,
	SUM(QTY) AS PART_QTY,
	OVERSEAS_FLAG,
	SIGN_TOP_CUST_CATEGORY_CODE,
    SIGN_TOP_CUST_CATEGORY_CN_NAME,
    SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
	-1 AS CREATED_BY,
	CURRENT_TIMESTAMP AS CREATION_DATE,
	-1 AS LAST_UPDATED_BY,
	CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
	''N'' AS DEL_FLAG
	FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_GROUP_ORD_SPART_SRAN_T
	GROUP BY
	PERIOD_ID ,
	PERIOD_YEAR ,
	REGION_HRMS_ORG_CODE,
    REGION_HRMS_ORG_CN_NAME,
	REPOFFICE_ORG_CODE,
    REPOFFICE_ORG_CN_NAME,
	HW_CONTRACT_NUM,
	LV0_PROD_LIST_CODE ,
	LV0_PROD_LIST_CN_NAME ,
	LV1_PROD_LIST_CODE ,
	LV1_PROD_LIST_CN_NAME ,
	LV2_PROD_LIST_CODE ,
	LV2_PROD_LIST_CN_NAME ,
	LV3_PROD_LIST_CODE ,
	LV3_PROD_LIST_CN_NAME ,
	LV4_PROD_LIST_CODE ,
	LV4_PROD_LIST_CN_NAME , 
	SPART_CODE,
	OVERSEAS_FLAG,
	SIGN_TOP_CUST_CATEGORY_CODE,
    SIGN_TOP_CUST_CATEGORY_CN_NAME,
    SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
	'
	;
	
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE IMMEDIATE V_SQL;	


   
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '插数到 DM_FCST_PRICE_RAW_DATA_EXAMINE_T表',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
  --3.收集统计信息
  V_SQL := 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_PRICE_RAW_DATA_EXAMINE_T';
   EXECUTE IMMEDIATE V_SQL;
  
  
  --3.日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集 DM_FCST_PRICE_RAW_DATA_EXAMINE_T统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END
$$
/

