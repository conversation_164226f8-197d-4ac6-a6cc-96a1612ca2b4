-- Name: f_dm_foc_prod_bom_insert_rev_total; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_prod_bom_insert_rev_total(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/06/23
创建人  ：唐钦
最后修改时间:
最后修改人:
背景描述：1.从贴源层的临时表增量插入到全量结果表, 并插入主键
参数描述：x_success_flag ：是否成功
事例：fin_dm_opt_foi.F_DM_FOC_PROD_BOM_INSERT_REV_TOTAL()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'fin_dm_opt_foi.F_DM_FOC_PROD_BOM_INSERT_REV_TOTAL'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_LAST_MONTH BIGINT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1),'YYYYMM') AS BIGINT); --当前系统月份的上一个月
  V_TIMESTAMP  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISSFF'); --当前current_timestamp的时间戳
  
BEGIN
  X_RESULT_STATUS = 'SUCCESS';
  
  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --1.删除目标表会计期等于上一个月的数据
--  DELETE FROM fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I WHERE PERIOD_ID = V_LAST_MONTH;


  --【全量使用】清空目标表数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I';
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I所有的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --2.从临时表增量刷数到全量表, 并生成主键
  FOR CUR IN (SELECT DISTINCT PERIOD_ID
  				 FROM fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_REV_DETAIL_I
  				 ORDER BY PERIOD_ID ASC) LOOP
  INSERT INTO fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I
    (--PRIMARY_ID,
     PERIOD_ID,
		 ITEM_CODE,
		 ITEM_CN_NAME,
		 QUANTITY,
		 RMB_ITEM_UNIT_COST_AMT,
		 PER_UNIT_QTY,
		 PARENTPARTNUMBER,
		 PARENT_QUANTITY,
		 RMB_FACT_RATE_GC_AMT,
		 NON_CHILD_FLAG,
		 LEVEL_REL,
		 NON_SALE_FLAG,
		 CONSIGNMENT_TYPE_CODE,
		 CONSIGNMENT_TYPE,
		 MODEL_NUM,
		 ITEM_SUBTYPE_CODE,
		 ITEM_SUBTYPE_CN_NAME,
		 PROD_KEY,
		 GEO_PC_KEY,
		 SIGN_CUST_KEY,
		 END_CUST_KEY,
		 AGENT_DISTRIBUTION_CUST_KEY,
		 ENTERPRISE_CUST_KEY,
		 ACCOUNT_DEPT_CUST_KEY,
		 CUST_KEY,
		 CONTRACT_KEY,
		 HW_CONTRACT_NUM,
		 PROJ_KEY,
		 SRC_SYS_NAME,
		 CRT_CYCLE_ID,
		 LAST_UPD_CYCLE_ID,
		 DW_LAST_UPDATE_DATE,
			 DEL_FLAG,
		 IS_RESALE_FLAG, --9月新增
		 MAIN_DIMENSION_KEY --9月新增
		 )
  
    SELECT -- NEXTVAL('fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_S') || V_TIMESTAMP, --主键值=序列+时间戳
           P.PERIOD_ID,
					 P.ITEM_CODE,
					 P.ITEM_CN_NAME,
					 P.QUANTITY,
					 P.RMB_ITEM_UNIT_COST_AMT,
					 P.PER_UNIT_QTY,
					 P.PARENTPARTNUMBER,
					 P.PARENT_QUANTITY,
					 P.RMB_FACT_RATE_GC_AMT,
					 P.NON_CHILD_FLAG,
					 P.LEVEL_REL,
					 P.NON_SALE_FLAG,
					 P.CONSIGNMENT_TYPE_CODE,
					 P.CONSIGNMENT_TYPE,
					 P.MODEL_NUM,
					 P.ITEM_SUBTYPE_CODE,
					 P.ITEM_SUBTYPE_CN_NAME,
					 P.PROD_KEY,
					 P.GEO_PC_KEY,
					 P.SIGN_CUST_KEY,
					 P.END_CUST_KEY,
					 P.AGENT_DISTRIBUTION_CUST_KEY,
					 P.ENTERPRISE_CUST_KEY,
					 P.ACCOUNT_DEPT_CUST_KEY,
					 P.CUST_KEY,
					 P.CONTRACT_KEY,
					 P.HW_CONTRACT_NUM,
					 P.PROJ_KEY,
					 P.SRC_SYS_NAME,
					 P.CRT_CYCLE_ID,
					 P.LAST_UPD_CYCLE_ID,
					 P.DW_LAST_UPDATE_DATE,
					 P.DEL_FLAG,
					P.IS_RESALE_FLAG, --9月新增
					P.MAIN_DIMENSION_KEY --9月新增
      FROM fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_REV_DETAIL_I P
      WHERE PERIOD_ID = CUR.PERIOD_ID
      ;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '从fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_REV_DETAIL_I临时表刷数月份: ' || CUR.PERIOD_ID ||'到全量表fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I, 并生成主键',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   END LOOP;
  
  --收集统计信息
  ANALYZE fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集fin_dm_opt_foi.DWL_PROD_BOM_ITEM_REV_DETAIL_I统计信息完成!');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';
  
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

