-- Name: f_dm_foi_lev_annual_weight; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_lev_annual_weight(f_caliber_flag character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年2月19日11点14分
  创建人  ：唐钦
  背景描述：采购ICT&数字能源-单年权重表(年度分析-一览表)
  参数描述：f_caliber_flag ：I：采购价格指数、E：数字能源指数
            f_version_id ：年度分析页面：版本号入参
            x_result_status ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOI_LEV_ANNUAL_WEIGHT()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOI_LEV_ANNUAL_WEIGHT'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL        TEXT; 
  V_VERSION_TABLE VARCHAR(200);
  V_FROM_TABLE VARCHAR(200);
  V_FROM1_TABLE VARCHAR(200);
  V_TO_TABLE   VARCHAR(200);
  V_COLUMN     VARCHAR(50);
  V_COLUMN_FLAG  VARCHAR(50);
  V_COLUMN1_FLAG VARCHAR(50);
  V_PARA_NAME  VARCHAR(50);
  V_SEQUENCE   VARCHAR(100);
  V_PARENT_NAME VARCHAR(50);
  -- 202407版本新增
  V_SQL_CONDITION VARCHAR(300);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
  
-- 判断不同入参，对应不同变量、参数
IF F_CALIBER_FLAG = 'I' THEN  -- ICT价格指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PLAN_VERSION_T'; 
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_WEIGHT_T';
    V_COLUMN := 'CONTINUITY_TYPE';
    V_COLUMN_FLAG := 'PARA_NAME AS CONTINUITY_TYPE';
    V_COLUMN1_FLAG := '''含连续性影响'' AS CONTINUITY_TYPE';
    V_PARA_NAME := '''不含连续性影响''';
    V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_WEIGHT_S.NEXTVAL';
    V_PARENT_NAME := '';
ELSIF F_CALIBER_FLAG = 'E' THEN  -- 数字能源指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_BASE_ITEM_AMT_T';  -- ITEM层级数据（实际）
    V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PLAN_VERSION_T';  
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_WEIGHT_T';
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_COLUMN_FLAG := '''N'' AS GROUP_PUR_FLAG';
    V_COLUMN1_FLAG := '''Y'' AS GROUP_PUR_FLAG';
    V_PARA_NAME := '''ENERGY''';
    V_SEQUENCE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_WEIGHT_S.NEXTVAL';
    V_PARENT_NAME := 'PARENT_CN_NAME,';
ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') THEN  -- 数字能源指数
    V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T';
    V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_BASE_ITEM_AMT_T';  -- ITEM层级数据（实际）
    V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_LEV_ANNUAL_WEIGHT_T';
    V_COLUMN := 'GROUP_PUR_FLAG';
    V_PARENT_NAME := 'PARENT_CN_NAME,';
    V_SQL_CONDITION := 'AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
    V_CALIBER := 'CALIBER_FLAG,';
    V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
    IF F_CALIBER_FLAG = 'IAS' THEN   -- IAS
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_PLAN_VERSION_T';
       V_COLUMN_FLAG := '''N'' AS GROUP_PUR_FLAG';
       V_COLUMN1_FLAG := '''Y'' AS GROUP_PUR_FLAG';
       V_PARA_NAME := '''IAS''';
    ELSIF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购
       V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ECPQC_PLAN_VERSION_T';
       V_COLUMN_FLAG := 'NULL AS GROUP_PUR_FLAG';
       V_COLUMN1_FLAG := 'NULL AS GROUP_PUR_FLAG';
       V_PARA_NAME := '''EAST_CHINA_PQC''';
    END IF;
END IF;
  
-- 版本号取值
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '   
         SELECT VERSION_ID
            FROM '||V_VERSION_TABLE||'
            WHERE
             DEL_FLAG = ''N''
             AND STATUS = 1
             AND UPPER(DATA_TYPE) = ''CATEGORY''
             AND UPPER(VERSION_TYPE) IN (''AUTO'',''FINAL'')
             ORDER BY LAST_UPDATE_DATE DESC
             LIMIT 1';
   EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF; 
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --1.清空各层级权重表(年度分析)数据:
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||' '||V_SQL_CONDITION;
  EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除产业标识为：'||F_CALIBER_FLAG||'，'||V_TO_TABLE||'表版本号为：'||V_VERSION_ID||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --创建年度发货额临时表
    DROP TABLE IF EXISTS SUM_LEV_COST_TMP;
    CREATE TEMPORARY TABLE SUM_LEV_COST_TMP (
        PERIOD_YEAR BIGINT,
        GROUP_CODE CHARACTER VARYING(50),
        GROUP_CN_NAME CHARACTER VARYING(2000),
        GROUP_LEVEL CHARACTER VARYING(50),
        RMB_COST_AMT NUMERIC,
        PARENT_CODE VARCHAR(50),
        PARENT_CN_NAME VARCHAR(1000),
        ITEM_CODE VARCHAR(50),
        ITEM_NAME VARCHAR(1000),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_NAME VARCHAR(200),
        L4_CEG_CODE VARCHAR(50),
        L4_CEG_SHORT_CN_NAME VARCHAR(200),
        L3_CEG_CODE VARCHAR(50),
        L3_CEG_SHORT_CN_NAME VARCHAR(200),
        L2_CEG_CODE VARCHAR(50),
        L2_CEG_CN_NAME VARCHAR(200),
        CONTINUITY_TYPE VARCHAR(50),
        GROUP_PUR_FLAG VARCHAR(2)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE);
    
    --2.写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '年度发货额临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');     
   
  V_SQL := '
     INSERT INTO SUM_LEV_COST_TMP(
                PERIOD_YEAR,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                RMB_COST_AMT,
                PARENT_CODE,
                PARENT_CN_NAME,
                ITEM_CODE,
                ITEM_NAME,
                CATEGORY_CODE,
                CATEGORY_NAME,
                L4_CEG_CODE,
                L4_CEG_SHORT_CN_NAME,
                L3_CEG_CODE,
                L3_CEG_SHORT_CN_NAME,
                L2_CEG_CODE,
                L2_CEG_CN_NAME,
                '||V_COLUMN||'
            )                
  -- 各层级CODE数据按年汇总
    WITH LEV_COST_TMP AS(
    -- 供应商层级年发货额            
          SELECT SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                 T1.SUPPLIER_CODE AS GROUP_CODE,
                 T1.SUPPLIER_CN_NAME AS GROUP_CN_NAME,  
                 ''SUPPLIER'' AS GROUP_LEVEL,
                 T1.RECEIVE_AMT_CNY AS RMB_COST_AMT,
                 T1.ITEM_CODE AS PARENT_CODE,
                 T1.ITEM_NAME AS PARENT_CN_NAME,
                 T1.ITEM_CODE,
                 T1.ITEM_NAME,
                 T1.CATEGORY_CODE,
                 T1.CATEGORY_NAME,
                 T1.L4_CEG_CODE,
                 T1.L4_CEG_SHORT_CN_NAME,
                 T1.L3_CEG_CODE,
                 T1.L3_CEG_SHORT_CN_NAME,
                 T1.L2_CEG_CODE,
                 T1.L2_CEG_CN_NAME,
                 NULL AS '||V_COLUMN||'
             FROM '||V_FROM_TABLE||' T1
             WHERE PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')  -- 取小于当前月份的实际数
             '||V_SQL_CONDITION||'
          UNION ALL
    -- ITEM层级年发货额            
          SELECT SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                 T1.ITEM_CODE AS GROUP_CODE,
                 T1.ITEM_NAME AS GROUP_CN_NAME,  
                 ''ITEM'' AS GROUP_LEVEL,
                 T1.RECEIVE_AMT_CNY AS RMB_COST_AMT,
                 T1.CATEGORY_CODE AS PARENT_CODE,
                 T1.CATEGORY_NAME AS PARENT_CN_NAME,
                 T1.ITEM_CODE,
                 NULL AS ITEM_NAME,
                 T1.CATEGORY_CODE,
                 T1.CATEGORY_NAME,
                 T1.L4_CEG_CODE,
                 T1.L4_CEG_SHORT_CN_NAME,
                 T1.L3_CEG_CODE,
                 T1.L3_CEG_SHORT_CN_NAME,
                 T1.L2_CEG_CODE,
                 T1.L2_CEG_CN_NAME,
                 NULL AS '||V_COLUMN||'
             FROM '||V_FROM1_TABLE||' T1
             WHERE PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')  -- 取小于当前月份的实际数
             '||V_SQL_CONDITION||'
    -- 品类层级年发货额                                         
          UNION ALL
          SELECT SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                 T1.CATEGORY_CODE AS GROUP_CODE,
                 T1.CATEGORY_NAME AS GROUP_CN_NAME,  
                 ''CATEGORY'' AS GROUP_LEVEL,
                 T1.RECEIVE_AMT_CNY AS RMB_COST_AMT,
                 T1.L4_CEG_CODE AS PARENT_CODE,
                 T1.L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,
                 NULL AS ITEM_CODE,
                 NULL AS ITEM_NAME,
                 T1.CATEGORY_CODE,
                 NULL AS CATEGORY_NAME,
                 T1.L4_CEG_CODE,
                 T1.L4_CEG_SHORT_CN_NAME,
                 T1.L3_CEG_CODE,
                 T1.L3_CEG_SHORT_CN_NAME,
                 T1.L2_CEG_CODE,
                 T1.L2_CEG_CN_NAME,
                 NULL AS '||V_COLUMN||'
             FROM '||V_FROM1_TABLE||' T1
             WHERE PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')  -- 取小于当前月份的实际数
             '||V_SQL_CONDITION||'
    -- 模块层级年发货额                    
          UNION ALL    
          SELECT SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                 T1.L4_CEG_CODE AS GROUP_CODE,
                 T1.L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,  
                 ''LV4'' AS GROUP_LEVEL,
                 T1.RECEIVE_AMT_CNY AS RMB_COST_AMT,
                 T1.L3_CEG_CODE AS PARENT_CODE,
                 T1.L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,
                 NULL AS ITEM_CODE,
                 NULL AS ITEM_NAME,
                 NULL AS CATEGORY_CODE,
                 NULL AS CATEGORY_NAME,
                 T1.L4_CEG_CODE,
                 NULL AS L4_CEG_SHORT_CN_NAME,
                 T1.L3_CEG_CODE,
                 T1.L3_CEG_SHORT_CN_NAME,
                 T1.L2_CEG_CODE,
                 T1.L2_CEG_CN_NAME,
                 NULL AS '||V_COLUMN||'
             FROM '||V_FROM1_TABLE||' T1
             WHERE PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')  -- 取小于当前月份的实际数
             '||V_SQL_CONDITION||'
    -- 专家团层级年发货额                
          UNION ALL    
          SELECT SUBSTR(T2.PERIOD_ID,1,4) AS PERIOD_YEAR,
                 T2.L3_CEG_CODE AS GROUP_CODE,
                 T2.L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                 ''LV3'' AS GROUP_LEVEL,
                 T2.RECEIVE_AMT_CNY AS RMB_COST_AMT,
                 T2.L2_CEG_CODE AS PARENT_CODE,
                 T2.L2_CEG_CN_NAME AS PARENT_CN_NAME,
                 NULL AS ITEM_CODE,
                 NULL AS ITEM_NAME,
                 NULL AS CATEGORY_CODE,
                 NULL AS CATEGORY_NAME,
                 NULL AS L4_CEG_CODE,
                 NULL AS L4_CEG_SHORT_CN_NAME,
                 T2.L3_CEG_CODE,
                 NULL AS L3_CEG_SHORT_CN_NAME,
                 T2.L2_CEG_CODE,
                 T2.L2_CEG_CN_NAME,
                 T1.'||V_COLUMN||' -- 专家团层级对8个专家团打上不含连续性影响的标签/含集团代采/不含集团代采标签
             FROM
                 ( SELECT VALUE AS L3_CEG_CN_NAME,REL_VALUE1 AS L3_CEG_CODE,
                          '||V_COLUMN_FLAG||'
                      FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T 
                      WHERE ENABLE_FLAG = ''Y'' AND PARA_NAME = '||V_PARA_NAME||'
                  UNION ALL 
                  SELECT DISTINCT L3_CEG_CN_NAME,L3_CEG_CODE,
                         '||V_COLUMN1_FLAG||'
                      FROM '||V_FROM1_TABLE||'
                      WHERE DEL_FLAG = ''N''
                      '||V_SQL_CONDITION||'
                         ) T1 
             LEFT JOIN         
             '||V_FROM1_TABLE||' T2        
             ON T1.L3_CEG_CODE = T2.L3_CEG_CODE                            
             WHERE T2.PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')  -- 取小于当前月份的实际数
             '||V_SQL_CONDITION||'
    -- LV2层级年发货额                
          UNION ALL    
          SELECT SUBSTR(T1.PERIOD_ID,1,4) AS PERIOD_YEAR,
                 T1.L2_CEG_CODE AS GROUP_CODE,
                 T1.L2_CEG_CN_NAME AS GROUP_CN_NAME,  
                 ''LV2'' AS GROUP_LEVEL,
                 T1.RECEIVE_AMT_CNY AS RMB_COST_AMT,
                 T1.L2_CEG_CODE AS PARENT_CODE,
                 T1.L2_CEG_CN_NAME AS PARENT_CN_NAME,
                 NULL AS ITEM_CODE,
                 NULL AS ITEM_NAME,
                 NULL AS CATEGORY_CODE,
                 NULL AS CATEGORY_NAME,
                 NULL AS L4_CEG_CODE,
                 NULL AS L4_CEG_SHORT_CN_NAME,
                 NULL AS L3_CEG_CODE,
                 NULL AS L3_CEG_SHORT_CN_NAME,
                 T1.L2_CEG_CODE,
                 NULL AS L2_CEG_CN_NAME,
                 NULL AS '||V_COLUMN||'
             FROM '||V_FROM1_TABLE||' T1
             WHERE T1.PERIOD_ID < TO_CHAR(CURRENT_TIMESTAMP,''YYYYMM'')  -- 取小于当前月份的实际数
             '||V_SQL_CONDITION||'
             )
    SELECT PERIOD_YEAR,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           SUM( RMB_COST_AMT ) AS RMB_COST_AMT,
           PARENT_CODE,
           PARENT_CN_NAME,
           ITEM_CODE,
           ITEM_NAME,
           CATEGORY_CODE,
           CATEGORY_NAME,
           L4_CEG_CODE,
           L4_CEG_SHORT_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME,
           '||V_COLUMN||'
       FROM LEV_COST_TMP
       GROUP BY PERIOD_YEAR,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                PARENT_CODE,
                PARENT_CN_NAME,
                ITEM_CODE,
                ITEM_NAME,
                CATEGORY_CODE,
                CATEGORY_NAME,
                L4_CEG_CODE,
                L4_CEG_SHORT_CN_NAME,
                L3_CEG_CODE,
                L3_CEG_SHORT_CN_NAME,
                L2_CEG_CODE,
                L2_CEG_CN_NAME,
                '||V_COLUMN;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
                                
   -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '各层级发货额数据落到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                            
                                
   -- 插入各视角、各层级的权重数据到分视角权重表(年度分析-一览表)
  V_SQL := '
    INSERT INTO '||V_TO_TABLE||'(
                VERSION_ID,
                PERIOD_YEAR,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                WEIGHT_RATE,
                PARENT_CODE,
                '||V_PARENT_NAME||'
                ITEM_CODE,
                ITEM_NAME,
                CATEGORY_CODE,
                CATEGORY_NAME,
                L4_CEG_CODE,
                L4_CEG_SHORT_CN_NAME,
                L3_CEG_CODE,
                L3_CEG_SHORT_CN_NAME,
                L2_CEG_CODE,
                L2_CEG_CN_NAME,
                '||V_CALIBER||'
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                APPEND_FLAG,
                '||V_COLUMN||'
        )                        
        
    -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  WITH PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )    
    -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT DISTINCT T2.PERIOD_YEAR,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              T1.ITEM_CODE,
              T1.ITEM_NAME,
              T1.CATEGORY_CODE,
              T1.CATEGORY_NAME,
              T1.L4_CEG_CODE,
              T1.L4_CEG_SHORT_CN_NAME,
              T1.L3_CEG_CODE,
              T1.L3_CEG_SHORT_CN_NAME,
              T1.L2_CEG_CODE,
              T1.L2_CEG_CN_NAME,
              T1.'||V_COLUMN||'
          FROM SUM_LEV_COST_TMP T1,PERIOD_YEAR_TMP T2
  )    
    -- 各视角下各层级的权重逻辑计算
       SELECT 
              '||V_VERSION_ID||' AS VERSION_ID,
              T1.PERIOD_YEAR,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              DECODE(T2.RMB_COST_AMT, NULL, 0, T2.RMB_COST_AMT / NULLIF(T2.PARENT_AMT,0)) AS WEIGHT_RATE,  -- 原始有数据，则计算，维度补齐的情况，则赋0
              T1.PARENT_CODE,
              '||V_PARENT_NAME||'
              T1.ITEM_CODE,
              T1.ITEM_NAME,
              T1.CATEGORY_CODE,
              T1.CATEGORY_NAME,
              T1.L4_CEG_CODE,
              T1.L4_CEG_SHORT_CN_NAME,
              T1.L3_CEG_CODE,
              T1.L3_CEG_SHORT_CN_NAME,
              T1.L2_CEG_CODE,
              T1.L2_CEG_CN_NAME,
              '||V_IN_CALIBER||'
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              ''N'' AS DEL_FLAG,
              DECODE(T2.RMB_COST_AMT, NULL, ''Y'',''N'') AS APPEND_FLAG,  --补齐标识：Y为补齐，N为原始
              T1.'||V_COLUMN||'
          FROM CONTIN_DIM_TMP T1
          LEFT JOIN (
              SELECT SS.PERIOD_YEAR,
                     SS.GROUP_CODE,
                     SS.GROUP_LEVEL,
                     SS.RMB_COST_AMT,
                     SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.PARENT_CODE,SS.GROUP_LEVEL,SS.'||V_COLUMN||') AS PARENT_AMT,
                     SS.PARENT_CODE,
                     SS.ITEM_CODE,
                     SS.CATEGORY_CODE,
                     SS.L4_CEG_CODE,
                     SS.L3_CEG_CODE,
                     SS.L2_CEG_CODE,
                     SS.'||V_COLUMN||'
                 FROM SUM_LEV_COST_TMP SS
                      ) T2
          ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
          AND T1.GROUP_CODE = T2.GROUP_CODE
          AND T1.PARENT_CODE = T2.PARENT_CODE
          AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
          AND NVL(T1.'||V_COLUMN||',''SNULL'') = NVL(T2.'||V_COLUMN||',''SNULL'')
          WHERE T1.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2'
          ;
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||' 的权重数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
     
     --收集统计信息
  V_SQL := 'ANALYZE '||V_TO_TABLE;
  EXECUTE IMMEDIATE V_SQL;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

