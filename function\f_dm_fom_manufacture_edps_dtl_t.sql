-- Name: f_dm_fom_manufacture_edps_dtl_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_manufacture_edps_dtl_t(f_keystr character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间：2023/12/06
创建人  ：许灿烽
背景描述：制造对象层级金额表 
参数描述:x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T --制造单领域数据明细表(包含自制与EMS)
目标表:FIN_DM_OPT_FOI.DM_FOM_MANUFACTURE_EDPS_DTL_T  --制造对象层级金额表
事例：fin_dm_opt_foi.f_dm_fom_manufacture_edps_dtl_t()
*/

DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_MANUFACTURE_EDPS_DTL_T'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_MANUFACTURE_EDPS_DTL_T'; -- 目标表
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT ; --版本号ID
  V_VERSION_NAME VARCHAR2(50) ; --版本中文名称
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--取刷新数据的版本号,如果前端传值就用前端的,没有再赋值
IF F_VERSION_ID IS NULL THEN
--查询年度版本号
  SELECT VERSION_ID,VERSION INTO V_VERSION_ID,V_VERSION_NAME
    FROM
        FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T 
    WHERE
        SUBSTR(VERSION,1,6) = TO_CHAR(CURRENT_DATE, 'YYYYMM')
        AND DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL' AND VERSION_TYPE='AUTO';
ELSE V_VERSION_ID := F_VERSION_ID;
	
	SELECT VERSION INTO V_VERSION_NAME
	FROM FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
	WHERE VERSION_ID = V_VERSION_ID;
END IF;


TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOM_MANUFACTURE_EDPS_DTL_T;
--制造单领域数据明细表(包含自制与EMS) 
INSERT INTO FIN_DM_OPT_FOI.DM_FOM_MANUFACTURE_EDPS_DTL_T
SELECT 
 V_VERSION_ID AS VERSION_ID    --版本ID
,V_VERSION_NAME AS VERSION_NAME    --版本名称
,PERIOD_YEAR    --会计年
,PERIOD_ID    --会计期
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,CREATED_BY    --创建人
,CURRENT_TIMESTAMP AS CREATION_DATE    --创建日期
,LAST_UPDATED_BY    --最后更新人
,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE    --最后更新日期
,DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
--,SUM(CASE WHEN CALIBER_FLAG = 'M' THEN CAST(RMB_MADE_AMT AS NUMERIC) ELSE RMB_EMS_AMT END) AS RMB_COST_AMT    --吸收金额
,SUM(CASE WHEN CALIBER_FLAG = 'M' THEN TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,f_keystr, 'AES128', 'CBC', 'SHA256')) ELSE RMB_EMS_AMT END) AS RMB_COST_AMT--吸收金额  加密后使用
,SUM(TRANSACTION_QUANTITY) AS TRANSACTION_QUANTITY    --交易数量
FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T
WHERE VERSION_ID = V_VERSION_ID
GROUP BY 
 PERIOD_YEAR    --会计年
,PERIOD_ID    --会计期
,LV0_CODE    --重量级团队LV0编码
,LV0_CN_NAME    --重量级团队LV0中文名称
,LV1_CODE    --重量级团队LV1编码
,LV1_CN_NAME    --重量级团队LV1中文名称
,BUSSINESS_OBJECT_CODE    --经营对象编码
,BUSSINESS_OBJECT_CN_NAME    --经营对象名称
,SHIPPING_OBJECT_CODE     --发货对象编码
,SHIPPING_OBJECT_CN_NAME    --发货对象名称
,MANUFACTURE_OBJECT_CODE     --制造对象编码
,MANUFACTURE_OBJECT_CN_NAME    --制造对象名称
,CREATED_BY    --创建人
,LAST_UPDATED_BY    --最后更新人
,DEL_FLAG    --删除标识(未删除：N，已删除：Y)
,CALIBER_FLAG    --业务口径（E：EMS/M：自制）
;

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '清空制造对象层级金额表,并重新插入新的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

