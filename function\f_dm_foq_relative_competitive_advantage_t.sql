-- Name: f_dm_foq_relative_competitive_advantage_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foq_relative_competitive_advantage_t(p_version_id integer, p_control_version_code character varying, p_hedging_version_code character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

	/*
创建时间：2024-11-20
创建人  ：朱雅欣
背景描述：采购相对竞争优势结果表,调用该函数将相对应的数据生成导入到目标表中
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_foq_relative_competitive_advantage_t(p_version_id,p_control_version_code,p_hedging_version_code)

*/


DECLARE
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_foq_relative_competitive_advantage_t';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_foq_relative_competitive_advantage_t';
	v_dml_row_count number default 0 ;
	v_max_control_version_code varchar(30);
	v_max_finance_version_code varchar(30);
	v_max_version_id int;

BEGIN
	x_success_flag := '1';                          --1表示成功
	

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '采购相对竞争优势结果表'||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
						
     --从 采购相对竞争优势结果表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_foq_relative_competitive_advantage_t t1 
		 where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_foq_version_info_t t2 where  t2.step='2001' and t2.module_type = '大宗敏感类');		 		 
		 

		
		-- 如果 p_control_version_code 为空，则取 补录表版本状态信息表 的最大版本编码，如果 p_control_version_code 不为空，则取传入的 p_control_version_code   
		if (p_control_version_code is null or p_control_version_code = '') then
        select max(version_code) as max_version_code into v_max_control_version_code 
		from fin_dm_opt_foi.apd_foq_version_code_status_info_t 
		where upper(status)='FINAL'
		and upper(source_name) = 'APD_FOQ_CONTROL_ORDER_INCOME_T';
		else 
		select  p_control_version_code into v_max_control_version_code ;
		end if
          ; 
		  
		  -- 如果 p_hedging_version_code 为空，则取 补录表版本状态信息表 的最大版本编码，如果 p_hedging_version_code 不为空，则取传入的 p_hedging_version_code   
		if (p_hedging_version_code is null or p_hedging_version_code = '') then
        select max(version_code) as max_version_code into v_max_finance_version_code 
		from fin_dm_opt_foi.apd_foq_version_code_status_info_t 
		where upper(status)='FINAL'
		and upper(source_name) = 'APD_FOQ_FINANCE_HEDGING_INCOME_T';
		else 
		select  p_hedging_version_code into v_max_finance_version_code ;
		end if
          ; 
		  
        -- 如果 p_version_id 为空，则取 大宗敏感类金属量价汇总表 最大版本ID, 如果 p_version_id 不为空，则取传入的 p_version_id   
		if (p_version_id is null or p_version_id = '') then
        select max(version_id) as max_version_id into v_max_version_id       
		from fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t	
		where upper(del_flag) = 'N';
		else 
		select  p_version_id into v_max_version_id ;
		end if
          ; 
		  
		   -- 创建 大宗敏感类金属量价汇总表 临时表 
        drop table if exists bulk_sensitive_metal_tmp;
	    create temporary table bulk_sensitive_metal_tmp
	    as 
	    select version_id             /*版本ID*/
              ,period_id              /*会计期*/
              ,data_date              /*数据日期*/
              ,category_code          /*品类编码*/
              ,category_name          /*品类名称*/
              ,l3_ceg_code            /*专家团编码*/
              ,l3_ceg_cn_name         /*专家团中文名称*/
              ,l4_ceg_code            /*模块编码*/
              ,l4_ceg_cn_name         /*模块中文名称*/
			  ,metal_cn_name          /*金属类别*/
              ,currency               /*币种*/
              ,quantity               /*铜下单量*/
              ,unit_code              /*单位编码*/
              ,unit_name              /*单位名称*/
              ,market_price           /*市场价*/
              ,apd_market_flag        /*市场价补齐标识（Y 是、N 否），优先往前补齐*/
              ,benchmark_price        /*基准价*/
              ,apd_benchmark_flag     /*基准价补齐标识（Y 是、N 否），优先往前补齐*/
	    from  fin_dm_opt_foi.dm_foq_bulk_sensitive_metal_sum_t
	    where version_id = v_max_version_id
           ;
		  
		  
		   -- 创建 控单收益（补录）表 临时表 
        drop table if exists control_order_income_tmp;
	    create temporary table control_order_income_tmp
	    as 
	    select version_code           /*版本编码*/
              ,period_id              /*会计期*/
              ,category_code          /*品类编码*/
              ,unit_name              /*单位*/
              ,frequency              /*频度*/
              ,source_name            /*数据源*/
              ,data_value             /*数据值*/
	    from  fin_dm_opt_foi.apd_foq_control_order_income_t
	    where version_code = v_max_control_version_code
           ;
		   
		    -- 创建 账务对冲收益（补录）表 临时表 
        drop table if exists finance_hedging_income_tmp;
	    create temporary table finance_hedging_income_tmp
	    as 
	    select version_code           /*版本编码*/
              ,period_id              /*会计期*/
              ,metal_cn_name          /*金属类别*/
              ,unit_name              /*单位*/
              ,data_value             /*总金额*/
              ,l3_ceg_code            /*专项采购认证部*/
              ,l4_ceg_code            /*模块*/
              ,category_code          /*分摊品类编码*/
              ,allocate_rate          /*分摊比例*/
              ,allocate_amount        /*分摊金额*/
	    from  fin_dm_opt_foi.apd_foq_finance_hedging_income_t
	    where version_code = v_max_finance_version_code
           ;
		   
		   -- 创建 金额 临时表，包含 市场价金额，基准价金额，潜在采购金额，我司采购金额，下单收益
		   drop table if exists price_tmp;
         create temporary table price_tmp( 		  
		  -- 计算 我司采购金额 、 潜在采购金额 、下单收益，由日粒度收敛至月粒度
		       version_id             int/*版本ID*/
              ,period_id              varchar(100)/*会计期*/
              ,category_code          varchar(600)/*品类编码*/
              ,category_name          varchar(600)/*品类名称*/
              ,l3_ceg_code            varchar(200)/*专家团编码*/
              ,l3_ceg_cn_name         varchar(600)/*专家团中文名称*/
              ,l4_ceg_code            varchar(200)/*模块编码*/
              ,l4_ceg_cn_name         varchar(600)/*模块中文名称*/
			  ,metal_cn_name          varchar(600)/*金属类别*/
              ,currency               varchar(10 )/*币种*/
              ,quantity               numeric     /*铜下单量*/
              ,market_price           numeric     /*市场价*/
              ,benchmark_price        numeric     /*基准价*/			  
              ,market_purchase_amount numeric     /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,hw_purchase_amount     numeric     /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,order_income           numeric     /*下单收益 = 潜在采购金额 - 司采购金额*/
			  )on commit preserve rows distribute by hash(period_id)
			  ;
			  
			   -- 创建 累计金额 临时表，包含 月度，年度，当年YTD的市场价金额，基准价金额，潜在采购金额，我司采购金额，下单收益，总收益
		   drop table if exists price_target_tmp;
         create temporary table price_target_tmp( 		  
		       version_id             int/*版本ID*/
			  ,year                   varchar(100)/*年份*/
              ,period_id              varchar(100)/*会计期*/
			  ,target_type            varchar(100)/*目标时点类型（月度：M、当年YTD:YTD、年度：Y）*/
              ,category_code          varchar(600)/*品类编码*/
              ,category_name          varchar(600)/*品类名称*/
              ,l3_ceg_code            varchar(200)/*专家团编码*/
              ,l3_ceg_cn_name         varchar(600)/*专家团中文名称*/
              ,l4_ceg_code            varchar(200)/*模块编码*/
              ,l4_ceg_cn_name         varchar(600)/*模块中文名称*/
			  ,metal_cn_name          varchar(600)/*金属类别*/
              ,currency               varchar(10 )/*币种*/
              ,quantity               numeric     /*铜下单量*/
              ,market_price           numeric     /*市场价*/
              ,benchmark_price        numeric     /*基准价*/			  
              ,market_purchase_amount numeric     /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,hw_purchase_amount     numeric     /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,order_income           numeric     /*下单收益 = 潜在采购金额 - 司采购金额*/
			  ,control_order_income   numeric     /*控单收益*/
              ,finance_hedging_income numeric     /*财务对冲收益*/
			  ,total_income           numeric     /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
			  )on commit preserve rows distribute by hash(period_id)
			  ;
		  
		   -- 计算 我司采购金额 、 潜在采购金额 、下单收益，由日粒度收敛至月粒度(所有金额除以一万)
		  insert into price_tmp(
		       version_id           /*版本ID*/
              ,period_id              /*会计期*/
              ,category_code          /*品类编码*/
              ,category_name          /*品类名称*/
              ,l3_ceg_code            /*专家团编码*/
              ,l3_ceg_cn_name         /*专家团中文名称*/
              ,l4_ceg_code            /*模块编码*/
              ,l4_ceg_cn_name         /*模块中文名称*/
			  ,metal_cn_name          /*金属类别*/
              ,currency               /*币种*/
              ,quantity            /*铜下单量*/
              ,market_price           /*市场价*/
              ,benchmark_price        /*基准价*/			  
              ,market_purchase_amount /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,hw_purchase_amount     /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,order_income           /*下单收益 = 潜在采购金额 - 司采购金额*/
		     )
		  select version_id                                                      /*版本ID*/
              ,period_id                                                         /*会计期*/
              ,category_code                                                     /*品类编码*/
              ,category_name                                                     /*品类名称*/
              ,l3_ceg_code                                                       /*专家团编码*/
              ,l3_ceg_cn_name                                                    /*专家团中文名称*/
              ,l4_ceg_code                                                       /*模块编码*/
              ,l4_ceg_cn_name                                                    /*模块中文名称*/
			  ,metal_cn_name                                                     /*金属类别*/
              ,currency                                                          /*币种*/
              ,sum(quantity)                 as quantity                         /*铜下单量*/
              ,sum(market_price/10000)             as market_price                     /*市场价*/
              ,sum(benchmark_price/10000)          as benchmark_price                  /*基准价*/			  
              ,sum(market_price*quantity/10000)    as market_purchase_amount           /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,sum(benchmark_price*quantity/10000) as hw_purchase_amount               /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,sum((market_price - benchmark_price)*quantity/10000) as order_income    /*下单收益 = 潜在采购金额 - 司采购金额*/
	    from  bulk_sensitive_metal_tmp
		group by version_id           
              ,period_id                           
              ,category_code          
              ,category_name          
              ,l3_ceg_code            
              ,l3_ceg_cn_name         
              ,l4_ceg_code            
              ,l4_ceg_cn_name  
              ,metal_cn_name			  
              ,currency                                                      
		  ;
		  
		   v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '采购金额、下单收益计算完毕，version_id为'||v_max_version_id||',控单收益补录表版本为'||v_max_control_version_code||',财务对冲收益补录表版本为'||v_max_finance_version_code||',数据量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
	  
	  
		  -- 关联 控单收益临时表 和 账务对冲收益临时表，取月度财务对冲收益和月度控单收益,并月度计算总收益
		  insert into price_target_tmp( 		  
		       version_id             /*版本ID*/
			  ,year                   /*年份*/
              ,period_id              /*会计期*/
			  ,target_type            /*目标时点类型（月度：M、当年YTD:YTD、年度：Y）*/
              ,category_code          /*品类编码*/
              ,category_name          /*品类名称*/
              ,l3_ceg_code            /*专家团编码*/
              ,l3_ceg_cn_name         /*专家团中文名称*/
              ,l4_ceg_code            /*模块编码*/
              ,l4_ceg_cn_name         /*模块中文名称*/
			  ,metal_cn_name          /*金属类别*/
              ,currency               /*币种*/
              ,quantity               /*铜下单量*/
              ,market_price           /*市场价*/
              ,benchmark_price        /*基准价*/			  
              ,market_purchase_amount /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,hw_purchase_amount     /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,order_income           /*下单收益 = 潜在采购金额 - 司采购金额*/
			  ,control_order_income   /*控单收益*/
			  ,finance_hedging_income /*财务对冲收益*/
			  ,total_income           /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
			  )
		select t1.version_id          /*版本ID*/
              ,substr(t1.period_id,1,4) as year             /*年份*/
              ,t1.period_id                                 /*会计期*/
              ,'M' as target_type                           /*目标时点类型（M、YTD、Y）*/
              ,t1.category_code                             /*品类编码*/
              ,t1.category_name                             /*品类名称*/
              ,t1.l3_ceg_code                               /*专家团编码*/
              ,t1.l3_ceg_cn_name                            /*专家团中文名称*/
              ,t1.l4_ceg_code                               /*模块编码*/
              ,t1.l4_ceg_cn_name                            /*模块中文名称*/
			  ,t1.metal_cn_name                             /*金属类别*/
			  ,t1.currency                                  /*币种*/
              ,t1.quantity                                  /*下单量（吨）*/
			  ,t1.market_price                              /*市场价*/
              ,t1.benchmark_price                           /*基准价*/
              ,t1.market_purchase_amount                    /*潜在采购金额*/
              ,t1.hw_purchase_amount                        /*我司采购金额*/
              ,t1.order_income                              /*下单收益*/
              ,t2.data_value/10000      as control_order_income   /*控单收益*/
              ,t3.allocate_amount/10000 as finance_hedging_income /*财务对冲收益*/
              ,(nvl(t1.order_income,0)+ nvl(t2.data_value/10000,0) + nvl(t3.allocate_amount/10000,0)) as total_income /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
		  from price_tmp t1
		  left join control_order_income_tmp t2
		   on t1.period_id      = t2.period_id
		  and t1.category_code  = t2.category_code
		  left join finance_hedging_income_tmp t3
		  on t1.period_id      = t3.period_id
		  and t1.category_code  = t3.category_code
		  and t1.l3_ceg_code    = t3.l3_ceg_code
		  and t1.l4_ceg_code    = t3.l4_ceg_code
		  ;
		  
		  v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
  perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '月度收益计算完毕,数据量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
		  
		  
		  -- 年度 市场价金额，基准价金额，潜在采购金额，我司采购金额，下单收益，总收益
		   insert into price_target_tmp( 		  
		       version_id             /*版本ID*/
			  ,year                   /*年份*/
              ,period_id              /*会计期*/
			  ,target_type            /*目标时点类型（月度：M、当年YTD:YTD、年度：Y）*/
              ,category_code          /*品类编码*/
              ,category_name          /*品类名称*/
              ,l3_ceg_code            /*专家团编码*/
              ,l3_ceg_cn_name         /*专家团中文名称*/
              ,l4_ceg_code            /*模块编码*/
              ,l4_ceg_cn_name         /*模块中文名称*/
			  ,metal_cn_name          /*金属类别*/
              ,currency               /*币种*/
              ,quantity               /*铜下单量*/
              ,market_price           /*市场价*/
              ,benchmark_price        /*基准价*/			  
              ,market_purchase_amount /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,hw_purchase_amount     /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,order_income           /*下单收益 = 潜在采购金额 - 司采购金额*/
			  ,control_order_income   /*控单收益*/
              ,finance_hedging_income /*财务对冲收益*/
			  ,total_income           /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
			  )
		select t1.version_id                                            /*版本ID*/
              ,t1.year                                                  /*年份*/
              ,(t1.year||'00')::int as period_id                        /*会计期*/
              ,'Y' as target_type                                       /*目标时点类型（M、YTD、Y）*/
              ,t1.category_code                                         /*品类编码*/
              ,t1.category_name                                         /*品类名称*/
              ,t1.l3_ceg_code                                           /*专家团编码*/
              ,t1.l3_ceg_cn_name                                        /*专家团中文名称*/
              ,t1.l4_ceg_code                                           /*模块编码*/
              ,t1.l4_ceg_cn_name                                        /*模块中文名称*/
			  ,t1.metal_cn_name                                         /*金属类别*/
			  ,t1.currency                                              /*币种*/
              ,sum(t1.quantity)               as quantity               /*铜下单量（吨）*/
			  ,sum(t1.market_price)           as market_price           /*市场价*/
              ,sum(t1.benchmark_price)        as benchmark_price        /*基准价*/
              ,sum(t1.market_purchase_amount) as market_purchase_amount /*潜在采购金额*/
              ,sum(t1.hw_purchase_amount)     as hw_purchase_amount     /*我司采购金额*/
              ,sum(t1.order_income)           as order_income           /*下单收益*/
              ,sum(t1.control_order_income)   as control_order_income   /*控单收益*/
              ,sum(t1.finance_hedging_income) as finance_hedging_income /*财务对冲收益*/
              ,sum(t1.total_income) as total_income /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
		  from price_target_tmp t1
		  where upper(target_type) = 'M'
		  group by t1.version_id                            
              ,t1.year                                                                     
              ,t1.category_code                             
              ,t1.category_name                             
              ,t1.l3_ceg_code                               
              ,t1.l3_ceg_cn_name                            
              ,t1.l4_ceg_code                               
              ,t1.l4_ceg_cn_name  
              ,t1.metal_cn_name	
              ,t1.currency			                    
               ;			  
			   
			    v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '年度收益计算完毕,数据量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
	  
	  
			    -- 当年YTD 市场价金额，基准价金额，潜在采购金额，我司采购金额，下单收益，总收益
		   insert into price_target_tmp( 		  
		        version_id            /*版本ID*/
			  ,year                   /*年份*/
              ,period_id              /*会计期*/
			  ,target_type            /*目标时点类型（月度：M、当年YTD:YTD、年度：Y）*/
              ,category_code          /*品类编码*/
              ,category_name          /*品类名称*/
              ,l3_ceg_code            /*专家团编码*/
              ,l3_ceg_cn_name         /*专家团中文名称*/
              ,l4_ceg_code            /*模块编码*/
              ,l4_ceg_cn_name         /*模块中文名称*/
			  ,metal_cn_name          /*金属类别*/
              ,currency               /*币种*/
              ,quantity               /*铜下单量*/
              ,market_price           /*市场价*/
              ,benchmark_price        /*基准价*/			  
              ,market_purchase_amount /*潜在采购金额  = 铜市场价 * 铜下单量*/
              ,hw_purchase_amount     /*我司采购金额  = 华为铜基准价 * 铜下单量*/
			  ,order_income           /*下单收益 = 潜在采购金额 - 司采购金额*/
			  ,control_order_income   /*控单收益*/
              ,finance_hedging_income /*财务对冲收益*/
			  ,total_income           /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
			  )
		select t1.version_id          /*版本ID*/
              ,t1.year                /*年份*/
              ,t1.period_id           /*会计期*/
              ,'YTD' as target_type   /*目标时点类型（M、YTD、Y）*/
              ,t1.category_code       /*品类编码*/
              ,t1.category_name       /*品类名称*/
              ,t1.l3_ceg_code         /*专家团编码*/
              ,t1.l3_ceg_cn_name      /*专家团中文名称*/
              ,t1.l4_ceg_code         /*模块编码*/
              ,t1.l4_ceg_cn_name      /*模块中文名称*/
			  ,t1.metal_cn_name       /*金属类别*/
			  ,t1.currency            /*币种*/
              ,sum(t1.quantity) over(partition by t1.version_id         
                                                    ,t1.year                       
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as quantity            /*铜下单量（吨）*/
			  ,sum(t1.market_price) over(partition by t1.version_id         
                                                    ,t1.year                         
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as market_price            /*市场价*/
			  ,sum(t1.benchmark_price) over(partition by t1.version_id         
                                                    ,t1.year                       
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as benchmark_price            /*基准价*/									
              ,sum(t1.market_purchase_amount) over(partition by t1.version_id         
                                                    ,t1.year                       
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as market_purchase_amount /*潜在采购金额*/
              ,sum(t1.hw_purchase_amount)  over(partition by t1.version_id         
                                                    ,t1.year                  
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as hw_purchase_amount     /*我司采购金额*/
              ,sum(t1.order_income) over(partition by t1.version_id         
                                                    ,t1.year                       
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as order_income           /*下单收益*/
              ,sum(t1.control_order_income) over(partition by t1.version_id         
                                                    ,t1.year                        
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as control_order_income   /*控单收益*/
              ,sum(t1.finance_hedging_income) over(partition by t1.version_id         
                                                    ,t1.year                         
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as finance_hedging_income /*财务对冲收益*/
              ,sum(t1.total_income) over(partition by t1.version_id         
                                                    ,t1.year                        
                                                    ,t1.category_code      
                                                    ,t1.category_name      
                                                    ,t1.l3_ceg_code        
                                                    ,t1.l3_ceg_cn_name     
                                                    ,t1.l4_ceg_code        
                                                    ,t1.l4_ceg_cn_name
                                                    order by period_id) as total_income          /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
		  from price_target_tmp t1
		  where upper(target_type) = 'M'
               ;			  
		  
		     v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => 'YTD收益计算完毕,数据量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;
		  
		  
		   delete from fin_dm_opt_foi.dm_foq_relative_competitive_advantage_t where  version_id = v_max_version_id;
		  
		  insert into fin_dm_opt_foi.dm_foq_relative_competitive_advantage_t(
		       version_id                   /*版本ID*/
              ,year                         /*年份*/
              ,period_id                    /*会计期*/
              ,target_type                  /*月度：M、当年YTD:YTD、年度：Y*/
              ,category_code                /*品类编码*/
              ,category_name                /*品类名称*/
              ,l3_ceg_code                  /*专家团编码*/
              ,l3_ceg_cn_name               /*专家团中文名称*/
              ,l4_ceg_code                  /*模块编码*/
              ,l4_ceg_cn_name               /*模块中文名称*/
			  ,metal_cn_name                /*金属类别*/
              ,currency                     /*币种*/
              ,quantity                     /*下单量（吨）*/
			  ,market_price                 /*市场价*/
              ,benchmark_price              /*基准价*/
              ,market_purchase_amount       /*潜在采购金额*/
              ,hw_purchase_amount           /*我司采购金额*/
              ,order_income                 /*下单收益*/
              ,control_order_income         /*控单收益*/
              ,finance_hedging_income       /*财务对冲收益*/
              ,total_income                 /*总收益*/
              ,order_income_rate            /*下单收益率*/
              ,total_income_rate            /*相对竞争优势(总收益率)*/
              ,remark                       /*备注*/
              ,created_by                   /*创建人*/
              ,creation_date                /*创建时间*/
              ,last_updated_by              /*修改人*/
              ,last_update_date             /*修改时间*/
              ,del_flag		                /*是否删除*/
		  )
		  select t1.version_id              /*版本ID*/
              ,t1.year                      /*年份*/
              ,t1.period_id                 /*会计期*/
              ,t1.target_type               /*目标时点类型（M、YTD、Y）*/
              ,t1.category_code             /*品类编码*/
              ,t1.category_name             /*品类名称*/
              ,t1.l3_ceg_code               /*专家团编码*/
              ,t1.l3_ceg_cn_name            /*专家团中文名称*/
              ,t1.l4_ceg_code               /*模块编码*/
              ,t1.l4_ceg_cn_name            /*模块中文名称*/
			  ,t1.metal_cn_name             /*金属类别*/
			  ,t1.currency                  /*币种*/
              ,t1.quantity                  /*铜下单量（吨）*/
			  ,t1.market_price              /*市场价*/
              ,t1.benchmark_price           /*基准价*/
              ,t1.market_purchase_amount    /*潜在采购金额*/
              ,t1.hw_purchase_amount        /*我司采购金额*/
              ,t1.order_income              /*下单收益*/
              ,t1.control_order_income      /*控单收益*/
              ,t1.finance_hedging_income    /*财务对冲收益*/
              ,t1.total_income              /*总收益=下单收益 + 控单收益 + 财务对冲收益*/
			  ,t1.order_income/ NULLIF(t1.market_purchase_amount,0) as order_income_rate      /*下单收益率*/
              ,t1.total_income/ NULLIF(t1.market_purchase_amount,0) as total_income_rate      /*相对竞争优势(总收益率)*/
			  ,'' as remark
 	          , -1 as created_by
 	          , current_timestamp as creation_date
 	          , -1 as last_updated_by
 	          , current_timestamp as last_update_date
 	          , 'N' as del_flag
		  from price_target_tmp t1		  
               ;			  
		  
		  
		      v_dml_row_count := sql%rowcount;	-- 收集数据量
		  -- 开始记录日志
        perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,               --sp名称
        p_log_para_list => '',                    --参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '收益率计算完毕,数据量'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,            --错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null                      --错误编码
      ) ;

		    --收集统计信息
    analyse fin_dm_opt_foi.dm_foq_relative_competitive_advantage_t;

exception
  	when others then

      perform fin_dm_opt_foi.f_dm_foq_capture_log_info_t(
        p_log_version_id => null,                     --版本
        p_log_sp_name => v_sp_name,                   --sp名称
        p_log_para_list => '',                        --参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,             --错误信息
		p_log_row_count => null,
		p_log_errbuf => sqlstate                      --错误编码
      ) ;
	x_success_flag := '2001';	                      --2001表示失败



 end;
 
$$
/

