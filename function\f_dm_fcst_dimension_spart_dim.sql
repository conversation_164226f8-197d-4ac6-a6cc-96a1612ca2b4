-- Name: f_dm_fcst_dimension_spart_dim; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_dimension_spart_dim(f_cost_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年12月版本
  创建人  ：唐钦
  背景描述：从源表：DM_FCST_ICT_PSP_PROD_UNIT_T/DM_FCST_ICT_STD_PROD_UNIT_T取得SPART编码与量纲层级的维度
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_DIMENSION_SPART_DIM('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_DIMENSION_SPART_DIM'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_TABLE      VARCHAR(50);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
  IF F_COST_TYPE = 'PSP' THEN 
     V_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_PSP_DIMENSION_SPART_DIM_T';
  ELSIF F_COST_TYPE = 'STD' THEN 
     V_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_STD_DIMENSION_SPART_DIM_T';
  END IF;
  
  -- 清空维表数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_TABLE;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空表：'||V_TABLE||'数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  IF F_COST_TYPE = 'PSP' THEN 
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PSP_DIMENSION_SPART_DIM_T(
         VERSION_ID,
         PROD_LIST_CODE,
         PROD_LIST_CN_NAME,
         PROD_RND_TEAM_CODE,
         PROD_RD_TEAM_CN_NAME,
         INDUSTRY_CATG_CODE,
         INDUSTRY_CATG_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         SPART_CODE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT DISTINCT VERSION_ID,
         LV4_PROD_LIST_CODE AS PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME AS PROD_LIST_CN_NAME,
         LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV4_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,
         LV4_INDUSTRY_CATG_CODE AS INDUSTRY_CATG_CODE,
         LV4_INDUSTRY_CATG_CN_NAME AS INDUSTRY_CATG_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         SPART_CODE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
     FROM FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_UNIT_T;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'PSP成本的SPART层级及量纲层级的维度关系插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  ELSIF F_COST_TYPE = 'STD' THEN 
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_STD_DIMENSION_SPART_DIM_T(
         VERSION_ID,
         PROD_LIST_CODE,
         PROD_LIST_CN_NAME,
         PROD_RND_TEAM_CODE,
         PROD_RD_TEAM_CN_NAME,
         INDUSTRY_CATG_CODE,
         INDUSTRY_CATG_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         SPART_CODE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT DISTINCT VERSION_ID,
         LV4_PROD_LIST_CODE AS PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME AS PROD_LIST_CN_NAME,
         LV4_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,
         LV4_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,
         LV4_INDUSTRY_CATG_CODE AS INDUSTRY_CATG_CODE,
         LV4_INDUSTRY_CATG_CN_NAME AS INDUSTRY_CATG_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         SPART_CODE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
     FROM FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_UNIT_T;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'STD成本的SPART层级及量纲层级的维度关系插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  END IF;
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TABLE;
  
  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

