-- Name: f_dm_fcst_annl_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_annl_amp(f_cost_type character varying, f_granularity_type character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年7月8日
  创建人  ：唐钦
  背景描述：根据年均本计算得到SPART/子类明细层级的涨跌幅；再通过SPART/子类明细层级的涨跌幅*年度权重，得到其余层级的涨跌幅
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ANNL_AMP('PSP','IRB');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_ANNL_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR INT := YEAR(CURRENT_TIMESTAMP);
  V_SQL TEXT; --执行语句
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_FROM_TABLE VARCHAR(100);
  V_FROM1_TABLE VARCHAR(100);
  V_MID_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
  V_TMP_TABLE VARCHAR(100);
  V_VIEW_FLAG VARCHAR(50);
  V_LV_CODE VARCHAR(50);
  V_LV_NAME VARCHAR(50);
  V_LV0_CODE VARCHAR(50);
  V_LV0_NAME VARCHAR(50);
  V_LV1_CODE VARCHAR(50);
  V_LV1_NAME VARCHAR(50);
  V_LV2_CODE VARCHAR(50);
  V_LV2_NAME VARCHAR(50);
  V_LV3_CODE VARCHAR(50);
  V_LV3_NAME VARCHAR(50);
  V_LV4_CODE VARCHAR(50);
  V_LV4_NAME VARCHAR(50);
  V_IN_LV1_CODE VARCHAR(50);
  V_IN_LV1_NAME VARCHAR(50);
  V_IN_LV2_CODE VARCHAR(50);
  V_IN_LV2_NAME VARCHAR(50);
  V_IN_LV3_CODE VARCHAR(50);
  V_IN_LV3_NAME VARCHAR(50);
  V_IN_LV4_CODE VARCHAR(50);
  V_IN_LV4_NAME VARCHAR(50);
  V_IN_LV_CODE VARCHAR(50);
  V_IN_LV_NAME VARCHAR(50);
  V_DIMENSION_CODE VARCHAR(50);
  V_DIMENSION_NAME VARCHAR(50);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(50);
  V_DIMENSION_SUBCATEGORY_NAME VARCHAR(50);
  V_GROUP_CODE VARCHAR(100);
  V_GROUP_NAME VARCHAR(100);
  V_GROUP_LEVEL VARCHAR(100);
  V_PARENT_CODE VARCHAR(100);
  V_PARENT_NAME VARCHAR(100);
  V_SQL_DE_AMT VARCHAR(100);
  V_GROUP_TOTAL VARCHAR(300);
  V_CHILD_LEVEL VARCHAR(50);
  V_SQL_PARENT VARCHAR(200);
  V_REL_LV_CODE VARCHAR(50);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 根据入参，对变量进行不同定义
     V_DIMENSION_CODE := 'DIMENSION_CODE,';
     V_DIMENSION_NAME := 'DIMENSION_CN_NAME,';
     V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
     V_DIMENSION_SUBCATEGORY_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
     V_IN_LV1_CODE := 'LV1_CODE,'; 
     V_IN_LV1_NAME := 'LV1_CN_NAME,';
     V_IN_LV2_CODE := 'LV2_CODE,';
     V_IN_LV2_NAME := 'LV2_CN_NAME,';
     V_IN_LV3_CODE := 'LV3_CODE,';
     V_IN_LV3_NAME := 'LV3_CN_NAME,';
     V_IN_LV4_CODE := 'LV4_CODE,';
     V_IN_LV4_NAME := 'LV4_CN_NAME,';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AVG_T';
     V_FROM1_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_WEIGHT_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_ANNL_AMP_T';
     V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MID_GROUP_AMP_T';
     V_TMP_TABLE := F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_DE_AVG_TMP';
  -- 判断成本类型，PSP成本不需要加解密，标准成本类型需要加解密
  IF F_COST_TYPE = 'PSP' THEN   -- PSP成本类型
     V_SQL_DE_AMT := 'RMB_AVG_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN   -- 标准成本类型
     V_SQL_DE_AMT := 'GS_DECRYPT(RMB_AVG_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS RMB_AVG_AMT,';   -- 解密金额
  END IF;
  -- 判断不同目录树类型，对变量进行不同定义
  IF F_GRANULARITY_TYPE = 'IRB' THEN   -- 重量级团队目录树
     V_LV_CODE := 'PROD_RND_TEAM_CODE,';
     V_LV_NAME := 'PROD_RD_TEAM_CN_NAME,';
     V_LV0_CODE := 'LV0_PROD_RND_TEAM_CODE,';
     V_LV0_NAME := 'LV0_PROD_RD_TEAM_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_RND_TEAM_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_RD_TEAM_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_RND_TEAM_CODE,';
     V_LV2_NAME := 'LV2_PROD_RD_TEAM_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_RND_TEAM_CODE,';
     V_LV3_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_RND_TEAM_CODE,';
     V_LV4_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
     V_REL_LV_CODE := 'PROD_RND_TEAM_CODE';
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN   -- 产业目录树
     V_LV_CODE := 'INDUSTRY_CATG_CODE,'; 
     V_LV_NAME := 'INDUSTRY_CATG_CN_NAME,';
     V_LV0_CODE := 'LV0_INDUSTRY_CATG_CODE,'; 
     V_LV0_NAME := 'LV0_INDUSTRY_CATG_CN_NAME,';
     V_LV1_CODE := 'LV1_INDUSTRY_CATG_CODE,'; 
     V_LV1_NAME := 'LV1_INDUSTRY_CATG_CN_NAME,';
     V_LV2_CODE := 'LV2_INDUSTRY_CATG_CODE,';
     V_LV2_NAME := 'LV2_INDUSTRY_CATG_CN_NAME,';
     V_LV3_CODE := 'LV3_INDUSTRY_CATG_CODE,';
     V_LV3_NAME := 'LV3_INDUSTRY_CATG_CN_NAME,';
     V_LV4_CODE := 'LV4_INDUSTRY_CATG_CODE,';
     V_LV4_NAME := 'LV4_INDUSTRY_CATG_CN_NAME,';
     V_REL_LV_CODE := 'INDUSTRY_CATG_CODE';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN   -- 销售目录树
     V_LV_CODE := 'PROD_LIST_CODE,'; 
     V_LV_NAME := 'PROD_LIST_CN_NAME,';
     V_LV0_CODE := 'LV0_PROD_LIST_CODE,'; 
     V_LV0_NAME := 'LV0_PROD_LIST_CN_NAME,';
     V_LV1_CODE := 'LV1_PROD_LIST_CODE,'; 
     V_LV1_NAME := 'LV1_PROD_LIST_CN_NAME,';
     V_LV2_CODE := 'LV2_PROD_LIST_CODE,';
     V_LV2_NAME := 'LV2_PROD_LIST_CN_NAME,';
     V_LV3_CODE := 'LV3_PROD_LIST_CODE,';
     V_LV3_NAME := 'LV3_PROD_LIST_CN_NAME,';
     V_LV4_CODE := 'LV4_PROD_LIST_CODE,';
     V_LV4_NAME := 'LV4_PROD_LIST_CN_NAME,';
     V_REL_LV_CODE := 'PROD_LIST_CODE';
  END IF;
  
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空维表数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_MID_TABLE;
    EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 创建临时表
     V_SQL := '
     DROP TABLE IF EXISTS '||V_TMP_TABLE||';
     CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
         PERIOD_YEAR                   INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         LV4_CODE                      VARCHAR(50),
         LV4_CN_NAME                   VARCHAR(200),
         DIMENSION_CODE                VARCHAR(50),
         DIMENSION_CN_NAME             VARCHAR(200),
         DIMENSION_SUBCATEGORY_CODE    VARCHAR(50),
         DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200),
         DIMENSION_SUB_DETAIL_CODE     VARCHAR(50),
         DIMENSION_SUB_DETAIL_CN_NAME  VARCHAR(200),
         GROUP_CODE                    VARCHAR(50),
         GROUP_CN_NAME                 VARCHAR(200),
         GROUP_LEVEL                   VARCHAR(50),
         PARENT_CODE                   VARCHAR(50),
         PARENT_CN_NAME                VARCHAR(200),
         MAIN_FLAG                     VARCHAR(2),
         CODE_ATTRIBUTES               VARCHAR(50),
         RMB_AVG_AMT                   NUMERIC,
         VIEW_FLAG                     VARCHAR(50),
         REGION_CODE                   VARCHAR(50),
         REGION_CN_NAME                VARCHAR(200),
         REPOFFICE_CODE                VARCHAR(50),
         REPOFFICE_CN_NAME             VARCHAR(200),
         BG_CODE                       VARCHAR(50),
         BG_CN_NAME                    VARCHAR(200),
         OVERSEA_FLAG                  VARCHAR(10)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(GROUP_CODE)';
     EXECUTE IMMEDIATE V_SQL;
     DBMS_OUTPUT.PUT_LINE('创建'||V_TMP_TABLE||'表成功');
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '解密临时表：'||V_TMP_TABLE||'，创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将数据放入临时表，调整字段结构，方便后续取数
  V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
         PERIOD_YEAR,                  
         LV0_CODE,                     
         LV0_CN_NAME,                  
         LV1_CODE,                     
         LV1_CN_NAME,                  
         LV2_CODE,                     
         LV2_CN_NAME,                  
         LV3_CODE,                     
         LV3_CN_NAME,                  
         LV4_CODE,                     
         LV4_CN_NAME,                  
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         GROUP_CODE,                   
         GROUP_CN_NAME,                
         GROUP_LEVEL,                  
         PARENT_CODE,                  
         PARENT_CN_NAME,               
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         RMB_AVG_AMT,                  
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
    )
  SELECT PERIOD_YEAR,                  
         '||V_LV0_CODE||'
         '||V_LV0_NAME||'
         '||V_LV1_CODE||'
         '||V_LV1_NAME||'
         '||V_LV2_CODE||'
         '||V_LV2_NAME||'
         '||V_LV3_CODE||'
         '||V_LV3_NAME||'
         '||V_LV4_CODE||'
         '||V_LV4_NAME||'
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CODE,DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,                   
         DECODE(VIEW_FLAG,''PROD_SPART'',SPART_CN_NAME,DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,                
         DECODE(VIEW_FLAG,''PROD_SPART'',''SPART'',''SUB_DETAIL'') AS GROUP_LEVEL,                  
         DECODE(VIEW_FLAG,''PROD_SPART'','||V_LV4_CODE||'DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,                  
         DECODE(VIEW_FLAG,''PROD_SPART'','||V_LV4_NAME||'DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,               
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         '||V_SQL_DE_AMT||'
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
      FROM '||V_FROM_TABLE||' 
      WHERE VERSION_ID = '||V_VERSION_ID;
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;

    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '加解密均本数据放入临时表',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');  
     
  -- 将ITEM层级的年度涨跌幅数据插入中间表
  V_SQL := '
  INSERT INTO '||V_MID_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,                  
         LV_CODE,                     
         LV_CN_NAME,                  
         LV0_CODE,                     
         LV0_CN_NAME,                  
         LV1_CODE,                     
         LV1_CN_NAME,                  
         LV2_CODE,                     
         LV2_CN_NAME,                  
         LV3_CODE,                     
         LV3_CN_NAME,                  
         LV4_CODE,                     
         LV4_CN_NAME,                  
         DIMENSION_CODE,               
         DIMENSION_CN_NAME,            
         DIMENSION_SUBCATEGORY_CODE,   
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,    
         DIMENSION_SUB_DETAIL_CN_NAME, 
         GROUP_CODE,                   
         GROUP_CN_NAME,                
         GROUP_LEVEL,                  
         PARENT_CODE,                  
         PARENT_CN_NAME,               
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         ANNUAL_AMP,                  
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  -- ITEM层级年度涨跌幅的计算逻辑
  -- 将年均本数据按年份行转列 
  WITH BY_YEAR_AVG_TMP AS(
  -- 基础数据：主力编码标识全都赋值为：N，编码属性都赋值为空
  SELECT LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,   
         DIMENSION_SUB_DETAIL_CN_NAME, 
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-3||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-2||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-1||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG,
         ''N'' AS MAIN_FLAG,                     
         NULL AS CODE_ATTRIBUTES,
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
     FROM '||V_TMP_TABLE||'  
     WHERE (CODE_ATTRIBUTES <> ''全选'' OR CODE_ATTRIBUTES IS NULL)   -- 由于主力编码维表的数据是在基础数据的基础上打标签，所以这部分数据重合，只排除全选数据即可
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              LV2_CODE,
              LV2_CN_NAME,
              LV3_CODE,
              LV3_CN_NAME,
              LV4_CODE,
              LV4_CN_NAME,
              DIMENSION_CODE,
              DIMENSION_CN_NAME,
              DIMENSION_SUBCATEGORY_CODE,
              DIMENSION_SUBCATEGORY_CN_NAME,
              DIMENSION_SUB_DETAIL_CODE,   
              DIMENSION_SUB_DETAIL_CN_NAME, 
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              PARENT_CODE,
              PARENT_CN_NAME,
              VIEW_FLAG,                    
              REGION_CODE,                  
              REGION_CN_NAME,               
              REPOFFICE_CODE,               
              REPOFFICE_CN_NAME,            
              BG_CODE,                      
              BG_CN_NAME,                   
              OVERSEA_FLAG
  UNION ALL 
  -- 主力编码维表数据+主力编码全选数据
  SELECT LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,   
         DIMENSION_SUB_DETAIL_CN_NAME, 
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-3||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-2||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-1||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
         SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG,
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG
     FROM '||V_TMP_TABLE||'  
     WHERE MAIN_FLAG = ''Y''    -- 主力标识为：Y时，包含主力编码维表数据，以及造的全选属性的数据
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              LV2_CODE,
              LV2_CN_NAME,
              LV3_CODE,
              LV3_CN_NAME,
              LV4_CODE,
              LV4_CN_NAME,
              DIMENSION_CODE,
              DIMENSION_CN_NAME,
              DIMENSION_SUBCATEGORY_CODE,
              DIMENSION_SUBCATEGORY_CN_NAME,
              DIMENSION_SUB_DETAIL_CODE,   
              DIMENSION_SUB_DETAIL_CN_NAME, 
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              PARENT_CODE,
              PARENT_CN_NAME,
              MAIN_FLAG,                    
              CODE_ATTRIBUTES,
              VIEW_FLAG,                    
              REGION_CODE,                  
              REGION_CN_NAME,               
              REPOFFICE_CODE,               
              REPOFFICE_CN_NAME,            
              BG_CODE,                      
              BG_CN_NAME,                   
              OVERSEA_FLAG
  )
  , YEAR_ANNUAL_AMP_TMP AS(
      SELECT '||V_YEAR-2||' AS PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             LV2_CODE,
             LV2_CN_NAME,
             LV3_CODE,
             LV3_CN_NAME,
             LV4_CODE,
             LV4_CN_NAME,
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,   
             DIMENSION_SUB_DETAIL_CN_NAME, 
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             PARENT_CN_NAME,
             MAIN_FLAG,                    
             CODE_ATTRIBUTES,
             VIEW_FLAG,                    
             REGION_CODE,                  
             REGION_CN_NAME,               
             REPOFFICE_CODE,               
             REPOFFICE_CN_NAME,            
             BG_CODE,                      
             BG_CN_NAME,                   
             OVERSEA_FLAG
          FROM BY_YEAR_AVG_TMP
      UNION ALL
      SELECT '||V_YEAR-1||' AS PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             LV2_CODE,
             LV2_CN_NAME,
             LV3_CODE,
             LV3_CN_NAME,
             LV4_CODE,
             LV4_CN_NAME,
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,   
             DIMENSION_SUB_DETAIL_CN_NAME, 
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             PARENT_CN_NAME,
             MAIN_FLAG,                    
             CODE_ATTRIBUTES,
             VIEW_FLAG,                    
             REGION_CODE,                  
             REGION_CN_NAME,               
             REPOFFICE_CODE,               
             REPOFFICE_CN_NAME,            
             BG_CODE,                      
             BG_CN_NAME,                   
             OVERSEA_FLAG
         FROM BY_YEAR_AVG_TMP   
      UNION ALL
      SELECT '||V_YEAR||' AS PERIOD_YEAR,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             LV2_CODE,
             LV2_CN_NAME,
             LV3_CODE,
             LV3_CN_NAME,
             LV4_CODE,
             LV4_CN_NAME,
             DIMENSION_CODE,
             DIMENSION_CN_NAME,
             DIMENSION_SUBCATEGORY_CODE,
             DIMENSION_SUBCATEGORY_CN_NAME,
             DIMENSION_SUB_DETAIL_CODE,   
             DIMENSION_SUB_DETAIL_CN_NAME, 
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             PARENT_CN_NAME,
             MAIN_FLAG,                    
             CODE_ATTRIBUTES,
             VIEW_FLAG,                    
             REGION_CODE,                  
             REGION_CN_NAME,               
             REPOFFICE_CODE,               
             REPOFFICE_CN_NAME,            
             BG_CODE,                      
             BG_CN_NAME,                   
             OVERSEA_FLAG
         FROM BY_YEAR_AVG_TMP 
              )
  -- 计算最细粒度层级的年度涨跌幅数据
       SELECT '
              ||V_VERSION_ID ||' AS VERSION_ID,
              T1.PERIOD_YEAR,
              T1.LV4_CODE AS LV_CODE,                     
              T1.LV4_CN_NAME AS LV_CN_NAME,                  
              T1.LV0_CODE,                     
              T1.LV0_CN_NAME,                  
              T1.LV1_CODE,                     
              T1.LV1_CN_NAME,                  
              T1.LV2_CODE,                     
              T1.LV2_CN_NAME,                  
              T1.LV3_CODE,                     
              T1.LV3_CN_NAME,                  
              T1.LV4_CODE,                     
              T1.LV4_CN_NAME,                  
              T1.DIMENSION_CODE,               
              T1.DIMENSION_CN_NAME,            
              T1.DIMENSION_SUBCATEGORY_CODE,   
              T1.DIMENSION_SUBCATEGORY_CN_NAME,
              T1.DIMENSION_SUB_DETAIL_CODE,    
              T1.DIMENSION_SUB_DETAIL_CN_NAME, 
              T1.GROUP_CODE,                   
              T1.GROUP_CN_NAME,                
              T1.GROUP_LEVEL,                  
              T1.PARENT_CODE,                  
              T1.PARENT_CN_NAME,               
              T1.MAIN_FLAG,                    
              T1.CODE_ATTRIBUTES,              
              NVL(T1.ANNUAL_AMP,0) AS ANNUAL_AMP,              
              T1.VIEW_FLAG,                    
              T1.REGION_CODE,                  
              T1.REGION_CN_NAME,               
              T1.REPOFFICE_CODE,               
              T1.REPOFFICE_CN_NAME,            
              T1.BG_CODE,                      
              T1.BG_CN_NAME,                   
              T1.OVERSEA_FLAG,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              ''N'' AS DEL_FLAG
           FROM YEAR_ANNUAL_AMP_TMP T1
           ';           
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL;      
       DBMS_OUTPUT.PUT_LINE('ITEM层级涨跌幅数据插入表成功');
                                        
   --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||' 的ITEM层级的数据到'||V_MID_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 对于不同颗粒度、不同视角的不同层级进行循环计算
  FOR LEVEL_NUM IN 0 .. 6 LOOP
  -- 路径2：量纲子类层级涨跌幅计算
  IF LEVEL_NUM = 0 THEN 
      V_IN_LV_CODE := 'T1.LV4_CODE AS LV_CODE,';
      V_IN_LV_NAME := 'T1.LV4_CN_NAME AS LV_CN_NAME,';
      V_GROUP_TOTAL := 'T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
                        T1.DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
                        ''SUBCATEGORY'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''SUB_DETAIL''';
      V_SQL_PARENT := ' T1.DIMENSION_CODE AS PARENT_CODE,
                        T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
  -- 路径2：量纲层级涨跌幅计算
  ELSIF LEVEL_NUM = 1 THEN 
      V_DIMENSION_SUBCATEGORY_CODE := '';
      V_DIMENSION_SUBCATEGORY_NAME := '';
      V_GROUP_TOTAL := '
                        T1.DIMENSION_CODE AS GROUP_CODE,
                        T1.DIMENSION_CN_NAME AS GROUP_CN_NAME,
                        ''DIMENSION'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''SUBCATEGORY''';
      V_SQL_PARENT := ' T1.LV4_CODE AS PARENT_CODE,
                        T1.LV4_CN_NAME AS PARENT_CN_NAME,';
  -- 路径1：LV4层级涨跌幅计算
  ELSIF LEVEL_NUM = 2 THEN 
      V_DIMENSION_CODE := '';
      V_DIMENSION_NAME := '';
      V_GROUP_TOTAL := '
                        T1.LV4_CODE AS GROUP_CODE,
                        T1.LV4_CN_NAME AS GROUP_CN_NAME,
                        ''LV4'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''SPART''';  
      V_SQL_PARENT := ' T1.LV3_CODE AS PARENT_CODE,
                        T1.LV3_CN_NAME AS PARENT_CN_NAME,';
  -- 制造对象层级涨跌幅计算
  ELSIF LEVEL_NUM = 3 THEN 
      V_IN_LV4_CODE := '';
      V_IN_LV4_NAME := '';
      V_IN_LV_CODE := 'T1.LV3_CODE AS LV_CODE,';
      V_IN_LV_NAME := 'T1.LV3_CN_NAME AS LV_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.LV3_CODE AS GROUP_CODE,
                        T1.LV3_CN_NAME AS GROUP_CN_NAME,
                        ''LV3'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''LV4''';
      V_SQL_PARENT := ' T1.LV2_CODE AS PARENT_CODE,
                        T1.LV2_CN_NAME AS PARENT_CN_NAME,';
  -- 发货对象层级涨跌幅计算
  ELSIF LEVEL_NUM = 4 THEN 
      V_IN_LV3_CODE := '';
      V_IN_LV3_NAME := '';
      V_IN_LV_CODE := 'T1.LV2_CODE AS LV_CODE,';
      V_IN_LV_NAME := 'T1.LV2_CN_NAME AS LV_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.LV2_CODE AS GROUP_CODE,
                        T1.LV2_CN_NAME AS GROUP_CN_NAME,
                        ''LV2'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''LV3''';  
      V_SQL_PARENT := ' T1.LV1_CODE AS PARENT_CODE,
                        T1.LV1_CN_NAME AS PARENT_CN_NAME,';
  -- 不同颗粒度，不同视角，专家团层级依据不同视角对应的上一层级往上卷积至不同数据层级
  ELSIF LEVEL_NUM = 5 THEN 
      V_IN_LV2_CODE := '';
      V_IN_LV2_NAME := '';
      V_IN_LV_CODE := 'T1.LV1_CODE AS LV_CODE,';
      V_IN_LV_NAME := 'T1.LV1_CN_NAME AS LV_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.LV1_CODE AS GROUP_CODE,
                        T1.LV1_CN_NAME AS GROUP_CN_NAME,
                        ''LV1'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''LV2''';  
      V_SQL_PARENT := ' T1.LV0_CODE AS PARENT_CODE,
                        T1.LV0_CN_NAME AS PARENT_CN_NAME,';
  ELSIF LEVEL_NUM = 6 THEN 
      V_IN_LV1_CODE := '';
      V_IN_LV1_NAME := '';
      V_IN_LV_CODE := 'T1.LV0_CODE AS LV_CODE,';
      V_IN_LV_NAME := 'T1.LV0_CN_NAME AS LV_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.LV0_CODE AS GROUP_CODE,
                        T1.LV0_CN_NAME AS GROUP_CN_NAME,
                        ''LV0'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''LV1''';  
  END IF;
  
  -- 年度涨跌幅主逻辑SQL
  V_SQL := '
  INSERT INTO '||V_MID_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,                  
         LV_CODE,                     
         LV_CN_NAME,                  
         LV0_CODE,                     
         LV0_CN_NAME,                  
         '||V_IN_LV1_CODE||'
         '||V_IN_LV1_NAME||'
         '||V_IN_LV2_CODE||'
         '||V_IN_LV2_NAME||'
         '||V_IN_LV3_CODE||'
         '||V_IN_LV3_NAME||'
         '||V_IN_LV4_CODE||'
         '||V_IN_LV4_NAME||'
         '||V_DIMENSION_CODE||'
         '||V_DIMENSION_NAME||'
         '||V_DIMENSION_SUBCATEGORY_CODE||'
         '||V_DIMENSION_SUBCATEGORY_NAME||'
         GROUP_CODE,                   
         GROUP_CN_NAME,                
         GROUP_LEVEL,                  
         PARENT_CODE,                  
         PARENT_CN_NAME,               
         MAIN_FLAG,                    
         CODE_ATTRIBUTES,              
         ANNUAL_AMP,                  
         VIEW_FLAG,                    
         REGION_CODE,                  
         REGION_CN_NAME,               
         REPOFFICE_CODE,               
         REPOFFICE_CN_NAME,            
         BG_CODE,                      
         BG_CN_NAME,                   
         OVERSEA_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
                 )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV_CODE,
                T1.LV_CN_NAME,
                T1.LV0_CODE,                     
                T1.LV0_CN_NAME,                  
                T1.LV1_CODE,                     
                T1.LV1_CN_NAME,                  
                T1.LV2_CODE,                     
                T1.LV2_CN_NAME,                  
                T1.LV3_CODE,                     
                T1.LV3_CN_NAME,                  
                T1.LV4_CODE,                     
                T1.LV4_CN_NAME,                  
                T1.DIMENSION_CODE,               
                T1.DIMENSION_CN_NAME,            
                T1.DIMENSION_SUBCATEGORY_CODE,   
                T1.DIMENSION_SUBCATEGORY_CN_NAME,
                T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                T1.PARENT_CODE,
                T1.PARENT_CN_NAME,
                T1.MAIN_FLAG,        
                T1.CODE_ATTRIBUTES,  
                T1.ANNUAL_AMP,       
                T1.VIEW_FLAG,        
                T1.REGION_CODE,      
                T1.REGION_CN_NAME,   
                T1.REPOFFICE_CODE,   
                T1.REPOFFICE_CN_NAME,
                T1.BG_CODE,          
                T1.BG_CN_NAME,       
                T1.OVERSEA_FLAG
             FROM '||V_MID_TABLE||' T1
             INNER JOIN '||V_FROM1_TABLE||' T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             AND T1.LV_CODE = T2.'||V_REL_LV_CODE||'
             AND NVL(T1.DIMENSION_CODE,''SNULL1'') = NVL(T2.DIMENSION_CODE,''SNULL1'')
             AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL2'')
             AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL3'')
             AND T1.GROUP_CODE = T2.GROUP_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
             AND T1.VIEW_FLAG = T2.VIEW_FLAG
             AND NVL(T1.MAIN_FLAG,''SNULL4'') =  NVL(T2.MAIN_FLAG,''SNULL4'')
             AND NVL(T1.CODE_ATTRIBUTES,''SNULL5'') = NVL(T2.CODE_ATTRIBUTES,''SNULL5'')
             AND T1.REGION_CODE = T2.REGION_CODE
             AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
             AND T1.BG_CODE =T2.BG_CODE
             AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
             WHERE T2.VERSION_ID = '||V_VERSION_ID||'
             AND T1.GROUP_LEVEL = '||V_CHILD_LEVEL||'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT '
                ||V_VERSION_ID||' AS VERSION_ID,
                T1.PERIOD_YEAR,
                '||V_IN_LV_CODE||'
                '||V_IN_LV_NAME||'
                LV0_CODE,                     
                LV0_CN_NAME,                  
                '||V_IN_LV1_CODE||'
                '||V_IN_LV1_NAME||'
                '||V_IN_LV2_CODE||'
                '||V_IN_LV2_NAME||'
                '||V_IN_LV3_CODE||'
                '||V_IN_LV3_NAME||'
                '||V_IN_LV4_CODE||'
                '||V_IN_LV4_NAME||'
                '||V_DIMENSION_CODE||'
                '||V_DIMENSION_NAME||'
                '||V_DIMENSION_SUBCATEGORY_CODE||'
                '||V_DIMENSION_SUBCATEGORY_NAME||'
                '||V_GROUP_TOTAL||'
                '||V_SQL_PARENT||'
                MAIN_FLAG,                    
                CODE_ATTRIBUTES,              
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                VIEW_FLAG,                    
                REGION_CODE,                  
                REGION_CN_NAME,               
                REPOFFICE_CODE,               
                REPOFFICE_CN_NAME,            
                BG_CODE,                      
                BG_CN_NAME,                   
                OVERSEA_FLAG,
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                ''N'' AS DEL_FLAG
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      LV0_CODE,                     
                      LV0_CN_NAME,                  
                      '||V_IN_LV1_CODE||'
                      '||V_IN_LV1_NAME||'
                      '||V_IN_LV2_CODE||'
                      '||V_IN_LV2_NAME||'
                      '||V_IN_LV3_CODE||'
                      '||V_IN_LV3_NAME||'
                      '||V_IN_LV4_CODE||'
                      '||V_IN_LV4_NAME||'
                      '||V_DIMENSION_CODE||'
                      '||V_DIMENSION_NAME||'
                      '||V_DIMENSION_SUBCATEGORY_CODE||'
                      '||V_DIMENSION_SUBCATEGORY_NAME||'
                      MAIN_FLAG,                    
                      CODE_ATTRIBUTES,
                      VIEW_FLAG,                    
                      REGION_CODE,                  
                      REGION_CN_NAME,               
                      REPOFFICE_CODE,               
                      REPOFFICE_CN_NAME,            
                      BG_CODE,                      
                      BG_CN_NAME,                   
                      OVERSEA_FLAG';
                                             
       DBMS_OUTPUT.PUT_LINE(V_SQL);
       EXECUTE IMMEDIATE V_SQL;  
       DBMS_OUTPUT.PUT_LINE('第'||LEVEL_NUM||'次循环，不同层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的第'||LEVEL_NUM||'次循环的数据到'||V_MID_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                                                 
   END LOOP;   -- 结束循环
 
 -- 删除分视角年度涨跌幅表同版本的数据
   EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
 
 --8.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除VERSION_ID= '||V_VERSION_ID ||' 的分视角年度涨跌幅（'||V_TO_TABLE||'）数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 插入所有数据到分视角年度涨跌幅表
  V_SQL := '
  INSERT INTO '||V_TO_TABLE||'(
         VERSION_ID,
         PERIOD_YEAR,
         '||V_LV_CODE||'
         '||V_LV_NAME||'
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
      )
  SELECT VERSION_ID,
         PERIOD_YEAR,
         LV_CODE,
         LV_CN_NAME,
         DIMENSION_CODE,
         DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE,
         DIMENSION_SUB_DETAIL_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         VIEW_FLAG,
         MAIN_FLAG,
         CODE_ATTRIBUTES,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
      FROM '||V_MID_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID;
    EXECUTE IMMEDIATE V_SQL;  

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||' 的权重数据到'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_MID_TABLE;
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  DBMS_OUTPUT.PUT_LINE('收集统计信息成功'); 

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

