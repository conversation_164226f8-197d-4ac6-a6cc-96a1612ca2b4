-- Name: f_dm_foc_prod_bom_insert_total; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_prod_bom_insert_total(OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/*
创建时间：2023/03/21
创建人  ：刘必华
最后修改时间:2023/09/19
最后修改人:lizhiyong
背景描述：1.从贴源层的临时表增量插入到全量结果表, 并插入主键
参数描述：x_success_flag ：是否成功
事例：fin_dm_opt_foi.F_DM_FOC_PROD_BOM_INSERT_TOTAL()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'fin_dm_opt_foi.F_DM_FOC_PROD_BOM_INSERT_TOTAL'; --存储过程名称
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_LAST_MONTH BIGINT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1),'YYYYMM') AS BIGINT); --当前系统月份的上一个月
  -- V_TIMESTAMP  VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMMDDHH24MISSFF'); --当前current_timestamp的时间戳
  
BEGIN
  X_RESULT_STATUS = 'SUCCESS';
  
  --0.开始日志
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --1.删除目标表会计期等于上一个月的数据
--  DELETE FROM fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I WHERE PERIOD_ID = V_LAST_MONTH;
  
  --【初始化使用】清空目标表数据:
   EXECUTE IMMEDIATE 'TRUNCATE TABLE fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I';
  
  --1.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I所有的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --2.从临时表增量刷数到全量表, 并生成主键
  FOR CUR IN (SELECT DISTINCT PERIOD_ID
  				 FROM fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_SHIP_DIM_I
  				 ORDER BY PERIOD_ID ASC) LOOP
  INSERT INTO fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I
    (
     PERIOD_ID,
     PARENTPARTNUMBER,
     PARENT_SHIP_QUANTITY,
     RMB_COST_AMT,
     ITEM_CODE,
     ITEM_CN_NAME,
     MODEL_NUM,
     ITEM_SUBTYPE_CODE,
     ITEM_SUBTYPE_CN_NAME,
     LEVEL_REL,
     PER_UNIT_QTY,
     PROD_KEY,
     GEO_PC_KEY,
     SHIP_QUANTITY,
     PRIMARY_MEASURE_UNIT_ID,
     NON_SALE_FLAG,
     RECOGNISE_TYPE_ID,
     RECOGNISE_TYPE_L2_CN_NAME,
     CONSIGNMENT_TYPE_CODE,
     CONSIGNMENT_TYPE,
     CURRENCY_CODE,
     RMB_FACT_RATE_GC_AMT,
     DATA_SOURCE_TABLE_NAME,
     CRT_CYCLE_ID,
     LAST_UPD_CYCLE_ID,
     DW_LAST_UPDATE_DATE,
		 DEL_FLAG,
		 IS_RESALE_FLAG, --9月新增
		 MAIN_DIMENSION_KEY --9月新增
		 )
  
    SELECT -- NEXTVAL('fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DTL_S') || V_TIMESTAMP, --主键值=序列+时间戳
           P.PERIOD_ID,
           P.PARENTPARTNUMBER,
           P.PARENT_SHIP_QUANTITY,
           P.RMB_COST_AMT,
           P.ITEM_CODE,
           P.ITEM_CN_NAME,
           P.MODEL_NUM,
           P.ITEM_SUBTYPE_CODE,
           P.ITEM_SUBTYPE_CN_NAME,
           P.LEVEL_REL,
           P.PER_UNIT_QTY,
           P.PROD_KEY,
           P.GEO_PC_KEY,
           P.SHIP_QUANTITY,
           P.PRIMARY_MEASURE_UNIT_ID,
           P.NON_SALE_FLAG,
           P.RECOGNISE_TYPE_ID,
           P.RECOGNISE_TYPE_L2_CN_NAME,
           P.CONSIGNMENT_TYPE_CODE,
           P.CONSIGNMENT_TYPE,
           P.CURRENCY_CODE,
           P.RMB_FACT_RATE_GC_AMT,
           P.DATA_SOURCE_TABLE_NAME,
           P.CRT_CYCLE_ID,
           P.LAST_UPD_CYCLE_ID,
           P.DW_LAST_UPDATE_DATE,
           P.DEL_FLAG,
		       P.IS_RESALE_FLAG, --9月新增
		       P.MAIN_DIMENSION_KEY --9月新增
      FROM fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_SHIP_DIM_I P
      WHERE P.PERIOD_ID = CUR.PERIOD_ID
      ;
  
  --2.写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '从fin_dm_opt_foi.FOC_DWL_PROD_BOM_ITEM_SHIP_DIM_I临时表刷数月份: ' || CUR.PERIOD_ID ||'到全量表fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I, 并生成主键',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  END LOOP;

  --收集统计信息
  ANALYZE fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I;

  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集fin_dm_opt_foi.DWL_PROD_BOM_ITEM_SHIP_DIM_I统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := 'FAILED';
  
  PERFORM fin_dm_opt_foi.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

