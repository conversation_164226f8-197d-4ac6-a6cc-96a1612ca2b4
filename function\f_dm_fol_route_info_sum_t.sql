-- Name: f_dm_fol_route_info_sum_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_route_info_sum_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-19
创建人  ：qwx1110218
背景描述：航线量汇总表：航线量集成表内关联物流价格补录表（即以价格补录表的数据为准）
          p_version_id 逻辑：1、自动调度，取版本表最大版本ID+1；
                             2、刷新（页面的刷新价格表、刷新系统）之前必须先编辑，否则刷新按钮置灰：取java传版本ID；
                                2.1）刷新价格表：先编辑价格补录数据，价格补录表会生成新版本编码的数据，状态是“草稿”，同时版本信息表会写入一条数据，版本ID=版本信息表最大版本ID+1，step=0；
                                                 点刷新按钮时，价格补录表的状态会更新为“终稿”，版本编码不变，同时版本信息表中step=0（初始化）的更新为step=2（执行中），版本ID不变；
                                                 刷新执行完成后，版本信息表中step=2的更新为step=1（执行成功），版本ID不变；
                                2.2）系统刷新同理；
          p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表、刷新系统）：取java传的值（1_刷新价格表 或 2_刷新系统）
参数描述：参数(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_route_info_sum_t()
变更记录：2024-6-3 qwx1110218 7月版新增“航线”字段及逻辑


*/


declare
	v_sp_name varchar(100) := 'fin_dm_opt_foi.f_dm_fol_route_info_sum_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(100) := 'fin_dm_opt_foi.dm_fol_route_info_sum_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;
	v_max_version_code varchar(30);


begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '航线量汇总表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

  -- 创建 scm_lg_shipment_f_tmp1 临时表
  drop table if exists scm_lg_shipment_f_tmp1;
	create temporary table scm_lg_shipment_f_tmp1(
         year                  int              -- 年份
       , period_id             int              -- 会计期
       , first_asd             timestamp        -- 首次ASD
       , source_location_gid   varchar(200)     -- 起运地（港口-国家）
       , source_port_name      varchar(50)      -- 起运地港口
       , source_country_name   varchar(100)     -- 起运地国家
       , dest_location_gid     varchar(200)     -- 目的地（港口-国家）
       , dest_port_name        varchar(50)      -- 目的地港口
       , dest_country_name     varchar(100)     -- 目的地国家
       , transport_mode        varchar(50)      -- 运输方式（精品海运、Xeneta）
       , supplier_short_name   varchar(50)      -- LST（即供应商）
       , max_order_type        varchar(200)     -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id              varchar(200)     -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark           varchar(5000)    -- 费率备注
       , eq_20gp_qty           numeric(38,10)   -- 20GP柜型量
       , eq_40gp_qty           numeric(38,10)   -- 40GP柜型量
       , eq_40hq_qty           numeric(38,10)   -- 40HQ柜型量
  )on commit preserve rows distribute by hash(first_asd)
  ;

  -- 创建 scm_lg_shipment_f_tmp3 临时表
  drop table if exists scm_lg_shipment_f_tmp3;
	create temporary table scm_lg_shipment_f_tmp3(
         year                  int              -- 年份
       , period_id             int              -- 会计期
       --, first_asd             timestamp        -- 首次ASD
       , source_location_gid   varchar(200)     -- 起运地（港口-国家）
       , source_port_name      varchar(50)      -- 起运地港口
       , source_country_name   varchar(100)     -- 起运地国家
       , dest_location_gid     varchar(200)     -- 目的地（港口-国家）
       , dest_port_name        varchar(50)      -- 目的地港口
       , dest_country_name     varchar(100)     -- 目的地国家
       , transport_mode        varchar(50)      -- 运输方式（精品海运、Xeneta）
       , supplier_short_name   varchar(50)      -- LST（即供应商）
       , max_order_type        varchar(200)     -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id              varchar(200)     -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark           varchar(5000)    -- 费率备注
       , container_type        varchar(50)      -- 柜型（20GP、40GP、40HQ）
       , container_qty         numeric(38,10)   -- 柜型量
  )on commit preserve rows distribute by hash(period_id,price_id,supplier_short_name,container_type)
	;

	-- 创建 route_price_info_tmp2 临时表
  drop table if exists route_price_info_tmp2;
	create temporary table route_price_info_tmp2(
         version_code          varchar(30)    -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , period_id             int            -- 有效时间（年月）
       , price_id              varchar(200)   -- 价格ID，对应集成表字段 rate_geo_gid
       , source_port_name      varchar(50)    -- 起始港
       , source_country_name   varchar(100)   -- 起始国家
       , dest_port_name        varchar(50)    -- 目的港
       , dest_country_name     varchar(100)   -- 目的国家
       , region_cn_name        varchar(50)    -- 区域
       , supplier_short_name   varchar(50)    -- LST（即供应商）
       , price_type            varchar(50)    -- 价格类型（包括：PTP价格、Xeneta价格、BAF价格）
       , currency              varchar(10)    -- 币种
       , container_type        varchar(50)    -- 柜型（20GP、40GP、40HQ）
       , price                 numeric(38,10) -- 价格
       , status                varchar(20)    -- 状态（DRAFT 草稿、FINAL 终稿）
  )on commit preserve rows distribute by /*replication*/ hash(version_code,price_id,container_type)
	;
	
	-- 创建 route_price_info_tmp3 临时表
  drop table if exists route_price_info_tmp3;
	create temporary table route_price_info_tmp3(
         version_code          varchar(30)    -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , period_id             int            -- 有效时间（年月）
       , price_id              varchar(200)   -- 价格ID，对应集成表字段 rate_geo_gid
       , source_port_name      varchar(50)    -- 起始港
       , source_country_name   varchar(100)   -- 起始国家
       , dest_port_name        varchar(50)    -- 目的港
       , dest_country_name     varchar(100)   -- 目的国家
       , region_cn_name        varchar(50)    -- 区域
       , supplier_short_name   varchar(50)    -- LST（即供应商）
       , price_type            varchar(50)    -- 价格类型（包括：PTP价格、Xeneta价格、BAF价格）
       , currency              varchar(10)    -- 币种
       , container_type        varchar(50)    -- 柜型（20GP、40GP、40HQ）
       , price                 numeric(38,10) -- 价格
       , status                varchar(20)    -- 状态（DRAFT 草稿、FINAL 终稿）
  )on commit preserve rows distribute by /*replication*/ hash(version_code,price_id,container_type)
	;
  
  -- 版本信息表中执行失败的版本ID，目标表中需要清理
  with version_info_2001_tmp as(
  select distinct nvl(version_id,0) as version_id
    from fin_dm_opt_foi.dm_fol_version_info_t
   where step = 2001   -- 执行失败
     and upper(del_flag) = 'N'
  )
  delete from fin_dm_opt_foi.dm_fol_route_info_sum_t where nvl(version_id,0) in(select nvl(version_id,0) from version_info_2001_tmp)
  ;

  -- 取物流价格补录表的最大版本编码
  select max(version_code) as max_version_code into v_max_version_code from fin_dm_opt_foi.apd_fol_route_price_info_t where upper(status) = 'FINAL' and upper(del_flag) = 'N';

  -- 如果是自动调度，版本ID则取版本信息表的最大版本ID+1；如果是刷新按钮，则直接取版本信息表的最大版本ID；
  if((p_version_id is null or p_version_id = '') and (p_refresh_type is null or p_refresh_type = '')) then
    -- 获取版本信息表的版本ID，自动调度，则取版本信息表最大版本ID+1；手动刷新用java传的版本ID
    -- 如果版本信息表没数据，版本ID直接赋值1；否则取版本信息表的最大版本ID
    if exists(select distinct version_id from fin_dm_opt_foi.dm_fol_version_info_t where upper(del_flag) = 'N') then
       select max(nvl(version_id,0))+1 as max_version_id into v_max_version_id from fin_dm_opt_foi.dm_fol_version_info_t where upper(del_flag) = 'N';
    else
       v_max_version_id := 1;
    end if;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 2,
      p_log_cal_log_desc => '传入参数为空， 取版本信息表最大版本ID+1的值：'||v_max_version_id,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
    ) ;

  elseif((p_version_id is not null or p_version_id <> '') and (p_refresh_type is not null or p_refresh_type <> '')) then
 
    v_max_version_id := p_version_id;

    v_dml_row_count := sql%rowcount;	-- 收集数据量

    -- 写入日志
    perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 2,
      p_log_cal_log_desc => '传入版本ID：'||p_version_id||'，传入刷新类型：'||p_refresh_type,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
    ) ;

  end if;

  -- 清理已经写入版本信息表的数据
  delete from fin_dm_opt_foi.dm_fol_version_info_t
   where version_id = nvl(p_version_id,v_max_version_id)
     and version_code = v_max_version_code
     and source_en_name = 'f_dm_fol_route_info_sum_t'
     and refresh_type = nvl(p_refresh_type,'4_AUTO')
     and step = 1
     and upper(del_flag) = 'N'
  ;

  -- 成功数据写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select nvl(p_version_id,v_max_version_id)   as version_id
         , v_max_version_code as version_code
         , 1 as step
         , 'apd_fol_route_price_info_t' as source_en_name
         , '物流航线价格补录表' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
         , '' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
     union all
    select nvl(p_version_id,v_max_version_id)   as version_id
         , v_max_version_code as version_code
         , 2 as step
         , 'f_dm_fol_route_info_sum_t' as source_en_name
         , '航线量汇总函数' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
         , 'version_code 是物流航线价格补录表的版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  ;

  -- 取航线量集成表的数据入到临时表
  insert into scm_lg_shipment_f_tmp1(
         year                   -- 年份
       , period_id              -- 会计期
       , first_asd              -- 首次ASD
       , transport_mode         -- 运输方式（精品海运、Xeneta）
       , max_order_type         -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id               -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark            -- 费率备注
       , eq_20gp_qty            -- 20GP柜型量
       , eq_40gp_qty            -- 40GP柜型量
       , eq_40hq_qty            -- 40HQ柜型量
  )
  select substr(first_asd,1,4)         as year                    -- 年份
       , (to_char(first_asd,'yyyymm'))::int as period_id               -- 会计期
       , first_asd               -- 首次ASD
       , high_quality_flag          as transport_mode  -- 运输方式（精品海运、Xeneta）
       , max_order_release_type_gid as max_order_type  -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , substr(rate_geo_gid,position('.' in rate_geo_gid)+1)                 as price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , sum(eq_20gp_qty*1) as eq_20gp_qty
       , sum(eq_40gp_qty*2) as eq_40gp_qty
       , sum(eq_40hq_qty*2) as eq_40hq_qty
    from fin_dm_opt_foi.dwr_scm_lg_shipment_f
   where to_char(first_asd,'yyyymm') >= '202001'
     and high_quality_flag = '精品海运'
     and upper(max_order_release_type_gid) = 'FROM SUPPLY CENTER'
     and instr(rate_remark,'零星') = 0
     and teu is not null
     and first_asd is not null
   group by first_asd
       , high_quality_flag
       , max_order_release_type_gid
       , rate_geo_gid
       , rate_remark
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 3,
      p_log_cal_log_desc => '航线量集成数据入到临时表 scm_lg_shipment_f_tmp1， 数据量：'||v_dml_row_count,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;

  -- 数据入到临时表
  insert into scm_lg_shipment_f_tmp3(
         year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , supplier_short_name     -- LST（即供应商）
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , container_type          -- 柜型（20GP、40GP、40HQ）
       , container_qty           -- 柜型量
  )
  with scm_lg_shipment_f_tmp2 as(
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , '20GP' as container_type
       , sum(eq_20gp_qty) as container_qty
    from scm_lg_shipment_f_tmp1
   group by year
       , period_id
       , transport_mode
       , max_order_type
       , price_id
       , rate_remark
  union all
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , '40GP' as container_type
       , sum(eq_40gp_qty) as container_qty
    from scm_lg_shipment_f_tmp1
   group by year
       , period_id
       , transport_mode
       , max_order_type
       , price_id
       , rate_remark
  union all
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , '40HQ' as container_type
       , sum(eq_40hq_qty) as container_qty
    from scm_lg_shipment_f_tmp1
   group by year
       , period_id
       , transport_mode
       , max_order_type
       , price_id
       , rate_remark
  )
  -- 只取5家供应商的数据： PIL/MAERSK/CMA/COSCO/EMC
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , 'PIL' as supplier_short_name
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , container_type
       , container_qty
    from scm_lg_shipment_f_tmp2
   where instr(upper(rate_remark),'PIL') > 0
  union all
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , 'MAERSK' as supplier_short_name
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , container_type
       , container_qty
    from scm_lg_shipment_f_tmp2
   where (instr(upper(rate_remark),'MAERSK') > 0 or instr(rate_remark,'马士基') > 0 or instr(upper(rate_remark),'MSK') > 0)
  union all
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , 'CMA' as supplier_short_name
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , container_type
       , container_qty
    from scm_lg_shipment_f_tmp2
   where instr(upper(rate_remark),'CMA') > 0
  union all
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , 'COSCO' as supplier_short_name
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , container_type
       , container_qty
    from scm_lg_shipment_f_tmp2
   where (instr(upper(rate_remark),'COSCO') > 0 or instr(rate_remark,'中远海运') > 0)
  union all
  select year                    -- 年份
       , period_id               -- 会计期
       , transport_mode          -- 运输方式（精品海运、Xeneta）
       , 'EMC' as supplier_short_name
       , max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , rate_remark             -- 费率备注
       , container_type
       , container_qty
    from scm_lg_shipment_f_tmp2
   where (instr(upper(rate_remark),'EMC') > 0 or instr(upper(rate_remark),'EVERGREEN') > 0 or instr(rate_remark,'长荣') > 0 or instr(rate_remark,'长荣海运') > 0)
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 4,
      p_log_cal_log_desc => '5家供应商数据入到临时表 scm_lg_shipment_f_tmp3，数据量：'||v_dml_row_count,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ; 
  
  insert into route_price_info_tmp3(
         version_code          -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , period_id             -- 有效时间（年月）
       , price_id              -- 价格ID，对应集成表字段 rate_geo_gid
       , source_port_name      -- 起始港
       , source_country_name   -- 起始国家
       , dest_port_name        -- 目的港
       , dest_country_name     -- 目的国家
       , region_cn_name        -- 区域
       , supplier_short_name   -- LST（即供应商）
       , price_type            -- 价格类型（包括：PTP价格、Xeneta价格、BAF价格）
       , currency              -- 币种
       , container_type        -- 柜型（20GP、40GP、40HQ）
       , price                 -- 价格
       , status                -- 状态（DRAFT 草稿、FINAL 终稿）
  )
  -- 取价格补录表最大版本的数据
  with max_version_code_tmp as(
  select max(version_code) as max_version_code from fin_dm_opt_foi.apd_fol_route_price_info_t where upper(status) = 'FINAL' and upper(del_flag) = 'N'
  ),
  route_price_info_tmp1 as(
  select t1.version_code            -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , t1.period_id               -- 有效时间（年月）
       , t1.price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , t1.source_port_name        -- 起始港
       , t1.source_country_name     -- 起始国家
       , t1.dest_port_name          -- 目的港
       , t1.dest_country_name       -- 目的国家
       , t1.region_cn_name          -- 区域
       , t1.supplier_short_name     -- LST（即供应商）
       , (case when t1.price_type <> 'Xeneta价格' then 'PTP+BAF价格' else t1.price_type end) as price_type              -- 价格类型（包括：PTP价格、Xeneta价格、BAF价格）
       , t1.currency                -- 币种
       , t1.eq_20gp_price	         -- 20GP价格
       , t1.eq_40gp_price	         -- 40GP价格
       , t1.eq_40hq_price	         -- 40HQ价格
       , t1.status                  -- 状态（DRAFT 草稿、FINAL 终稿）
    from fin_dm_opt_foi.apd_fol_route_price_info_t t1
    join max_version_code_tmp t2
      on t1.version_code = t2.max_version_code
   where upper(t1.del_flag) = 'N'
     and upper(t1.status) = 'FINAL'
  )
  select version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , '20GP' as container_type
       , sum(nvl(eq_20gp_price,0)) as price
       , status
    from route_price_info_tmp1
   group by version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , status
  union all
  select version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , '40GP' as container_type
       , sum(nvl(eq_40gp_price,0)) as price
       , status
    from route_price_info_tmp1
   group by version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , status
  union all
  select version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , '40HQ' as container_type
       , sum(nvl(eq_40hq_price,0)) as price
       , status
    from route_price_info_tmp1
   group by version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , status
  ;
  
  -- 数据入到临时表
  insert into route_price_info_tmp2(
         version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , container_type
       , price
       , status
  )
  select version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , container_type
       , price
       , status
    from route_price_info_tmp3
   where price_type <> 'Xeneta价格'
  ;
  
  -- 数据入到临时表
  insert into route_price_info_tmp2(
         version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , container_type
       , price
       , status
  )
  select version_code
       , period_id
       , price_id
       , source_port_name
       , source_country_name
       , dest_port_name
       , dest_country_name
       , region_cn_name
       , supplier_short_name
       , price_type
       , currency
       , container_type
       , price
       , status
    from route_price_info_tmp3
   where price_type = 'Xeneta价格'
     and price <> 0     -- 需要排除 Xeneta 价格为空值的数据
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 5,
      p_log_cal_log_desc => '物流价格补录表的数据入到临时表 route_price_info_tmp2，数据量：'||v_dml_row_count,--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;
  
  -- 清理数据
  -- delete from fin_dm_opt_foi.dm_fol_route_info_sum_t where version_id = nvl(p_version_id,v_max_version_id);
  
  -- 数据入到目标表
  insert into fin_dm_opt_foi.dm_fol_route_info_sum_t(
         version_id               -- 版本ID（java传版本ID，即版本信息表的version_id）
       , year                     -- 年份
       , period_id                -- 会计期
       , source_location_gid      -- 起运地（港口-国家）
       , source_port_name         -- 起运地港口
       , source_country_name      -- 起运地国家
       , dest_location_gid        -- 目的地（港口-国家）
       , dest_port_name           -- 目的地港口
       , dest_country_name        -- 目的地国家
       , route	                  -- 航线（目的港_国家）
       , region_cn_name           -- 目的地区域
       , transport_mode           -- 运输方式（精品海运、Xeneta）
       , max_order_type           -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , price_id                 -- 价格ID，对应集成表字段 rate_geo_gid
       , supplier_short_name      -- LST（即供应商）
       , price_type               -- 价格类型（包括：PTP价格、Xeneta价格、BAF价格）
       , container_type           -- 柜型（20GP、40GP、40HQ）
       , currency                 -- 币种
       , price                    -- 价格
       , container_qty            -- 柜型量
       , rate_remark              -- 费率备注
       , remark                   -- 备注
       , created_by               -- 创建人
       , creation_date            -- 创建时间
       , last_updated_by          -- 修改人
       , last_update_date         -- 修改时间
       , del_flag                 -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id) as version_id         -- 自动调度时取版本信息表的最大版本ID+1；手动刷新时，java传版本ID
       , t1.year                    -- 年份
       , t1.period_id               -- 会计期
       , t2.source_port_name||'_'||t2.source_country_name as source_location_gid     -- 起运地（港口_国家）
       , t2.source_port_name        -- 起运地港口
       , t2.source_country_name     -- 起运地国家
       , t2.dest_port_name||'_'||t2.dest_country_name as dest_location_gid       -- 目的地（港口_国家）
       , t2.dest_port_name          -- 目的地港口
       , t2.dest_country_name       -- 目的地国家
       , t2.dest_port_name||'_'||t2.dest_country_name as route  -- 航线（目的港_国家）
       , t2.region_cn_name          -- 区域
       , (case when t2.price_type = 'Xeneta价格' then 'Xeneta' else t1.transport_mode end) as transport_mode          -- 运输方式（精品海运、Xeneta）
       , t1.max_order_type          -- 最大order的类型，对应集成表字段 max_order_release_type_gid
       , t1.price_id                -- 价格ID，对应集成表字段 rate_geo_gid
       , t1.supplier_short_name     -- LST（即供应商）
       , t2.price_type              -- 价格类型（包括：PTP价格、Xeneta价格、BAF价格）
       , t1.container_type          -- 柜型（20GP、40GP、40HQ）
       , t2.currency                -- 币种
       , t2.price                   -- 价格
       , t1.container_qty           -- 柜型量
       , t1.rate_remark             -- 费率备注
       , '' as remark
 	     , -1 as created_by
 	     , current_timestamp as creation_date
 	     , -1 as last_updated_by
 	     , current_timestamp as last_update_date
 	     , 'N' as del_flag
    from scm_lg_shipment_f_tmp3 t1
    join route_price_info_tmp2 t2
      on t1.price_id = t2.price_id
     and substr(t1.period_id,1,6) = t2.period_id
     and t1.supplier_short_name = t2.supplier_short_name
     and t1.container_type = t2.container_type
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '版本ID：'||nvl(p_version_id,v_max_version_id)||'，数据入到目标表，数据量：'||v_dml_row_count,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
  ) ;

  -- 成功数据写入到版本信息表，需要更新对应的“执行中”数据状态
  update fin_dm_opt_foi.dm_fol_version_info_t set step = 1
   where version_id = nvl(p_version_id,v_max_version_id)
     and version_code = v_max_version_code
     and source_en_name = 'f_dm_fol_route_info_sum_t'
     and refresh_type = nvl(p_refresh_type,'4_AUTO')
     and upper(del_flag) = 'N'
  ;

  v_dml_row_count := sql%rowcount;	-- 收集数据量

  -- 写入日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
      p_log_version_id => null,                 --版本
      p_log_sp_name => v_sp_name,    --sp名称
      p_log_para_list => '',--参数
      p_log_step_num  => 7,
      p_log_cal_log_desc => '刷新类型：'||nvl(p_refresh_type,'4_AUTO')||'，价格补录表的最大版本'||v_max_version_code||'，价格补录表、航线量汇总函数写入到版本信息表，数据量：'||v_dml_row_count||'，结束运行！',--日志描述
      p_log_formula_sql_txt => null,--错误信息
      p_log_row_count => v_dml_row_count,
      p_log_errbuf => null  --错误编码
  ) ;

  exception
  	when others then

      perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => null,
        p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
        p_log_formula_sql_txt => sqlerrm,--错误信息
		    p_log_row_count => null,
		    p_log_errbuf => sqlstate  --错误编码
      ) ;
	x_success_flag := '2001';	         --2001表示失败

  -- 失败信息写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )
  select nvl(p_version_id,v_max_version_id) as version_id
       , v_max_version_code as version_code
       , 2001 as step
       , 'apd_fol_route_price_info_t' as source_en_name
       , '物流航线价格补录表' as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , '' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
   union all
  select nvl(p_version_id,v_max_version_id) as version_id
       , v_max_version_code as version_code
       , 2001 as step
       , 'f_dm_fol_route_info_sum_t' as source_en_name
       , '航线量汇总函数' as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
       , 'version_code 是物流航线价格补录表的版本编码' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
    ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_route_info_sum_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;

end;
$$
/

