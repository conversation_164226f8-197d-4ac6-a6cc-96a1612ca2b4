-- Name: f_dm_fcst_ict_mon_repl_cost_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_repl_cost_info_t(f_cost_type character varying, f_granularity_type character varying, f_keystr text DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

	/*
创建时间：2024-02-15
创建人  ：QWX1110218
修改人  ：TWX1139790
背景描述：产业成本指数ICT-编码替换关系页面月度非虚化逻辑
参数描述：参数一： F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本；自动调度也会传值；
          参数二： F_GRANULARITY_TYPE 目录树类型:	IRB:重量级团队目录; INDUS:产业目录;  PROD:销售目录；自动调度也会传值；
          参数三： F_KEYSTR	密钥
		      参数四:  F_VERSION_ID 版本号
          参数五： X_RESULT_STATUS 运行状态返回值 '1'为成功，'0'为失败
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_REPL_COST_INFO_T()
变更记录-202503：
①部分会话级临时表由先建表再INSERT INTO的形式调整为CREATE TEMPORARY TABLE XXX AS的形式，缩短代码行数；
②本函数内计算的权重数据落到实体表，方便查询问题时可以直接查询数据库数据；
③将部分重复代码用动态SQL替换了，将原本的IF判断调整为根据动态入参控制的方式，缩短冗余的重复代码。
*/


DECLARE
	V_SP_NAME            VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_REPL_COST_INFO_T';
	V_VERSION_ID         BIGINT;        --版本号
  V_FROM_AMT_TABLE     VARCHAR(100);  -- 来源表
  V_TO_INDEX_TABLE     VARCHAR(100);  -- 指数目标表
  V_TO_QTY_TABLE       VARCHAR(100);  -- 量目标表
  V_TO_WEIGHT_TABLE    VARCHAR(100);  -- 权重目标表
  V_SQL_FROM_PBI_PART  TEXT;	-- 来源表的PBI层级查询字段
  V_GROUP_SQL_FROM_PBI_PART TEXT;
  V_SQL_TO_PBI_PART      TEXT;	-- 目标表的PBI层级字段
  V_GS_DECRYPT_COST_AMT  TEXT;	-- 解密金额（PSP成本不用解密；STD成本需要解密）
  V_GS_ENCRYPT_COST_AMT  TEXT;	-- 加密金额（PSP成本不用加密；STD成本需要加密）
  V_GS_ENCRYPT_SUM_COST_AMT TEXT;
  V_NULL_COST_AMT   TEXT;
  V_COST_AMT        TEXT;
  V_AMT_TYPE        VARCHAR(200);
  V_CODE_REPL_GTS_TYPE  VARCHAR(50); -- 编码替换关系维表中的目录树类型（IRB 重量级团队目录树、INDUS 产业目录树、PROD 销售目录树）
	V_BASE_PERIOD_ID      INT;   -- 默认基期
	V_MAX_REPL_VERSION_ID INT; -- 替换关系维表最大版本ID
	V_SQL         TEXT;
	V_STEP_NUM    INT;
	V_CN          INT;
  V_ANNL_VERSION  BIGINT;
  V_CODE_REPL_TABLE VARCHAR(500);
  V_PBI_CODE VARCHAR(100);

BEGIN

	X_RESULT_STATUS := 'SUCCESS';        --1表示成功
	V_STEP_NUM = 1;

	-- 开始记录日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
      F_SP_NAME => V_SP_NAME,    --SP名称
      F_STEP_NUM => V_STEP_NUM,
      F_CAL_LOG_DESC => '函数 '||V_SP_NAME||' 开始运行',--日志描述
      F_FORMULA_SQL_TXT  => V_SQL,
      F_DML_ROW_COUNT => SQL%ROWCOUNT,
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS'
  );

  -- 创建临时表
  DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP1;
  CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP1(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , LV1_CODE                     VARCHAR(50)     -- 重量级团队LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- 重量级团队LV1名称
       , LV2_CODE                     VARCHAR(50)     -- 重量级团队LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- 重量级团队LV2名称
       , LV3_CODE                     VARCHAR(50)     -- 重量级团队LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- 重量级团队LV3名称
       , LV4_CODE                     VARCHAR(50)     -- 重量级团队LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- 重量级团队LV4名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , SPART_CODE                   VARCHAR(50)     -- SPART编码
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , RMB_AVG_AMT                  NUMERIC         -- 均价
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE,LV4_CODE,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;

  -- 创建临时表
  DROP TABLE IF EXISTS CODE_REPL_INFO_TMP;
  CREATE TEMPORARY TABLE CODE_REPL_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , GTS_TYPE                     VARCHAR(50)     -- 目录树类型（IRB 重量级团队目录树、INDUS 产业目录树、PROD 销售目录树） 24年7月版只有销售目录树的数据
       , LV1_CODE                     VARCHAR(50)     -- LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- LV1名称
       , LV2_CODE                     VARCHAR(50)     -- LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- LV2名称
       , LV3_CODE                     VARCHAR(50)     -- LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- LV3名称
       , LV4_CODE                     VARCHAR(50)     -- LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- LV4名称
       , PROD_CODE                    VARCHAR(50)     -- 产品编码
       , PROD_CN_NAME                 VARCHAR(500)    -- 产品中文名称
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , SPART_CODE                   VARCHAR(50)     -- SPART编码
       , SPART_DESC                   VARCHAR(2000)   -- SPART描述
	   , SOFTWARE_MARK                VARCHAR(50)
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(SPART_CODE,BG_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE)
  ;

  -- 创建临时表
  DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP3;
  CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP3(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- LV0名称
       , LV1_CODE                     VARCHAR(50)     -- LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- LV1名称
       , LV2_CODE                     VARCHAR(50)     -- LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- LV2名称
       , LV3_CODE                     VARCHAR(50)     -- LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- LV3名称
       , LV4_CODE                     VARCHAR(50)     -- LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- LV4名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , SPART_CODE                   VARCHAR(50)     -- SPART编码
       , SPART_DESC                   VARCHAR(2000)   -- SPART描述
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , RMB_AVG_AMT                  NUMERIC         -- 均本
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE,LV4_CODE,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG,REPLACE_RELATION_NAME)
  ;

  -- 创建SPART层指标临时表
  DROP TABLE IF EXISTS REPL_SPART_COST_INFO_ALL_TMP;
  CREATE TEMPORARY TABLE REPL_SPART_COST_INFO_ALL_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , LV1_CODE                     VARCHAR(50)     -- 重量级团队LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- 重量级团队LV1名称
       , LV2_CODE                     VARCHAR(50)     -- 重量级团队LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- 重量级团队LV2名称
       , LV3_CODE                     VARCHAR(50)     -- 重量级团队LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- 重量级团队LV3名称
       , LV4_CODE                     VARCHAR(50)     -- 重量级团队LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- 重量级团队LV4名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , SPART_CODE                   VARCHAR(50)     -- SPART编码
       , SPART_DESC                   VARCHAR(2000)   -- SPART描述
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  ) ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,SPART_CODE,LV4_CODE,GROUP_LEVEL,REPLACE_RELATION_NAME,REGION_CODE,REPOFFICE_CODE,BG_CODE)
  ;

  -- 创建PBI维度LV4层成本指数临时表
  DROP TABLE IF EXISTS REPL_PBI_LV4_COST_INFO_TMP;
  CREATE TEMPORARY TABLE REPL_PBI_LV4_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , LV1_CODE                     VARCHAR(50)     -- 重量级团队LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- 重量级团队LV1名称
       , LV2_CODE                     VARCHAR(50)     -- 重量级团队LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- 重量级团队LV2名称
       , LV3_CODE                     VARCHAR(50)     -- 重量级团队LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- 重量级团队LV3名称
       , LV4_CODE                     VARCHAR(50)     -- 重量级团队LV4编码
       , LV4_CN_NAME                  VARCHAR(200)    -- 重量级团队LV4名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,LV4_CODE,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;

  -- 创建PBI维度LV3层成本指数临时表
  DROP TABLE IF EXISTS REPL_PBI_LV3_COST_INFO_TMP;
  CREATE TEMPORARY TABLE REPL_PBI_LV3_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , LV1_CODE                     VARCHAR(50)     -- 重量级团队LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- 重量级团队LV1名称
       , LV2_CODE                     VARCHAR(50)     -- 重量级团队LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- 重量级团队LV2名称
       , LV3_CODE                     VARCHAR(50)     -- 重量级团队LV3编码
       , LV3_CN_NAME                  VARCHAR(200)    -- 重量级团队LV3名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,LV3_CODE,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;

  -- 创建PBI维度LV2层成本指数临时表
  DROP TABLE IF EXISTS REPL_PBI_LV2_COST_INFO_TMP;
  CREATE TEMPORARY TABLE REPL_PBI_LV2_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , LV1_CODE                     VARCHAR(50)     -- 重量级团队LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- 重量级团队LV1名称
       , LV2_CODE                     VARCHAR(50)     -- 重量级团队LV2编码
       , LV2_CN_NAME                  VARCHAR(200)    -- 重量级团队LV2名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,LV2_CODE,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;

  -- 创建PBI维度LV1层成本指数临时表
  DROP TABLE IF EXISTS REPL_PBI_LV1_COST_INFO_TMP;
  CREATE TEMPORARY TABLE REPL_PBI_LV1_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , LV1_CODE                     VARCHAR(50)     -- 重量级团队LV1编码
       , LV1_CN_NAME                  VARCHAR(200)    -- 重量级团队LV1名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,LV1_CODE,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;

  -- 创建PBI维度LV0层成本指数临时表
  DROP TABLE IF EXISTS REPL_PBI_LV0_COST_INFO_TMP;
  CREATE TEMPORARY TABLE REPL_PBI_LV0_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PERIOD_YEAR                  INT             -- 会计年
       , LV0_CODE                     VARCHAR(50)     -- 重量级团队LV0编码
       , LV0_CN_NAME                  VARCHAR(200)    -- 重量级团队LV0名称
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
	   , SOFTWARE_MARK                VARCHAR(50)
       , VIEW_FLAG                    VARCHAR(50)     -- 路径1：PROD_SPART/路径2：DIMENSION
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                     NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , COST_INDEX                   NUMERIC         -- 成本指数
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,LV0_CODE,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;

  -- 创建临时表
  DROP TABLE IF EXISTS REPL_COST_INFO_TMP;
  CREATE TEMPORARY TABLE REPL_COST_INFO_TMP(
         VERSION_ID                   INT             -- 版本ID
       , PERIOD_YEAR                  INT             -- 会计年
       , PERIOD_ID                    INT             -- 会计期
       , BASE_PERIOD_ID               INT             -- 默认基期
       , PBI_DIM_CODE                 VARCHAR(50)     -- PBI层级编码
       , PBI_DIM_CN_NAME              VARCHAR(200)    -- PBI层级名称
       , GROUP_CODE                   VARCHAR(50)     -- 各层级编码
       , GROUP_CN_NAME                VARCHAR(200)    -- 各层级中文名称
       , GROUP_LEVEL                  VARCHAR(50)     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , COST_INDEX                   NUMERIC         -- 成本指数
       , SHIPMENT_QTY                 NUMERIC         -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                 NUMERIC         -- 标准成本
       , PARENT_CODE                  VARCHAR(50)     -- 父级编码
       , PARENT_CN_NAME               VARCHAR(200)    -- 父级中文名称
       , REPLACE_RELATION_NAME        VARCHAR(200)    -- 替换关系名称
       , REPLACE_RELATION_TYPE        VARCHAR(50)     -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                VARCHAR(50)     -- 关系（ 替换 、收编）
       , CODE_TYPE                    VARCHAR(50)     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , REGION_CODE                  VARCHAR(50)     -- 地区部编码
       , REGION_CN_NAME               VARCHAR(200)    -- 地区部中文名称
       , REPOFFICE_CODE               VARCHAR(50)     -- 代表处编码
       , REPOFFICE_CN_NAME            VARCHAR(200)    -- 代表处中文名称
       , BG_CODE                      VARCHAR(50)     -- BG编码
       , BG_CN_NAME                   VARCHAR(200)    -- BG中文名称
	   , SOFTWARE_MARK                VARCHAR(50)
       , OVERSEA_FLAG                 VARCHAR(10)     -- 国内海外标识
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(PERIOD_ID,PBI_DIM_CODE,GROUP_LEVEL,REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG)
  ;


  -- PSP、STD的PBI维度层级直接用
  V_COST_AMT      := ', RMB_COST_AMT::NUMERIC AS RMB_COST_AMT';  -- PBI维度层级无需加密
  V_NULL_COST_AMT := ', NULL::NUMERIC AS RMB_COST_AMT';  -- 空值金额的处理

  -- 来源表
  V_FROM_AMT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_BASE_DETAIL_SPART_T';  -- 月均价收敛补齐表

  -- 指数目标表
  V_TO_INDEX_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_REPL_COST_IDX_T';  -- 编码替换月度成本指数表

  -- 量目标表
  V_TO_QTY_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_REPL_COST_CV_T';  -- 编码替换月度成本偏差表

  -- 权重目标表
  V_TO_WEIGHT_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_'||F_GRANULARITY_TYPE||'_MON_REPL_WEIGHT_T';  -- 编码替换月度权重表

  IF(F_COST_TYPE = 'PSP') THEN
    V_GS_DECRYPT_COST_AMT := ', NVL(RMB_COST_AMT::NUMERIC,0) AS RMB_COST_AMT, RMB_AVG_AMT';  -- 解密
    V_GS_ENCRYPT_COST_AMT := ', NVL(RMB_COST_AMT::NUMERIC,0) AS RMB_COST_AMT';  -- 加密
    V_AMT_TYPE := '::NUMERIC AS RMB_COST_AMT';

  ELSEIF(F_COST_TYPE = 'STD') THEN
    V_GS_DECRYPT_COST_AMT := ', GS_DECRYPT(RMB_COST_AMT,'''||F_KEYSTR||''',''AES128'',''CBC'',''SHA256'') AS RMB_COST_AMT, GS_DECRYPT(RMB_AVG_AMT,'''||F_KEYSTR||''',''AES128'',''CBC'',''SHA256'') AS RMB_AVG_AMT';  -- 解密
    V_GS_ENCRYPT_COST_AMT := ', GS_ENCRYPT(RMB_COST_AMT,'''||F_KEYSTR||''',''AES128'',''CBC'',''SHA256'') AS RMB_COST_AMT';  -- 只有SPART层级需要加密
    V_AMT_TYPE := '::TEXT AS RMB_COST_AMT';

  END IF;

  IF(F_GRANULARITY_TYPE = 'IRB') THEN
    -- 来源表的查询字段
    V_PBI_CODE := 'PROD_RND_TEAM_CODE';
    V_SQL_FROM_PBI_PART := ', LV0_PROD_RND_TEAM_CODE AS LV0_CODE
                       , LV0_PROD_RD_TEAM_CN_NAME    AS LV0_CN_NAME
                       , LV1_PROD_RND_TEAM_CODE      AS LV1_CODE
                       , LV1_PROD_RD_TEAM_CN_NAME    AS LV1_CN_NAME
                       , LV2_PROD_RND_TEAM_CODE      AS LV2_CODE
                       , LV2_PROD_RD_TEAM_CN_NAME    AS LV2_CN_NAME
                       , LV3_PROD_RND_TEAM_CODE      AS LV3_CODE
                       , LV3_PROD_RD_TEAM_CN_NAME    AS LV3_CN_NAME
                       , LV4_PROD_RND_TEAM_CODE      AS LV4_CODE
                       , LV4_PROD_RD_TEAM_CN_NAME    AS LV4_CN_NAME
                      ';

    V_GROUP_SQL_FROM_PBI_PART := ', LV0_PROD_RND_TEAM_CODE
                       , LV0_PROD_RD_TEAM_CN_NAME
                       , LV1_PROD_RND_TEAM_CODE
                       , LV1_PROD_RD_TEAM_CN_NAME
                       , LV2_PROD_RND_TEAM_CODE
                       , LV2_PROD_RD_TEAM_CN_NAME
                       , LV3_PROD_RND_TEAM_CODE
                       , LV3_PROD_RD_TEAM_CN_NAME
                       , LV4_PROD_RND_TEAM_CODE
                       , LV4_PROD_RD_TEAM_CN_NAME
                      ';

    V_SQL_TO_PBI_PART := ', PROD_RND_TEAM_CODE
                          , PROD_RD_TEAM_CN_NAME
                         ';

    --V_CODE_REPL_GTS_TYPE := 'IRB';

  ELSEIF(F_GRANULARITY_TYPE = 'INDUS') THEN
    -- 来源表的查询字段
    V_PBI_CODE := 'INDUSTRY_CATG_CODE';
    V_SQL_FROM_PBI_PART := ', LV0_INDUSTRY_CATG_CODE   AS LV0_CODE
                       , LV0_INDUSTRY_CATG_CN_NAME     AS LV0_CN_NAME
                       , LV1_INDUSTRY_CATG_CODE        AS LV1_CODE
                       , LV1_INDUSTRY_CATG_CN_NAME     AS LV1_CN_NAME
                       , LV2_INDUSTRY_CATG_CODE        AS LV2_CODE
                       , LV2_INDUSTRY_CATG_CN_NAME     AS LV2_CN_NAME
                       , LV3_INDUSTRY_CATG_CODE        AS LV3_CODE
                       , LV3_INDUSTRY_CATG_CN_NAME     AS LV3_CN_NAME
                       , LV4_INDUSTRY_CATG_CODE        AS LV4_CODE
                       , LV4_INDUSTRY_CATG_CN_NAME     AS LV4_CN_NAME
                      ';

    V_GROUP_SQL_FROM_PBI_PART := ', LV0_INDUSTRY_CATG_CODE
                       , LV0_INDUSTRY_CATG_CN_NAME
                       , LV1_INDUSTRY_CATG_CODE
                       , LV1_INDUSTRY_CATG_CN_NAME
                       , LV2_INDUSTRY_CATG_CODE
                       , LV2_INDUSTRY_CATG_CN_NAME
                       , LV3_INDUSTRY_CATG_CODE
                       , LV3_INDUSTRY_CATG_CN_NAME
                       , LV4_INDUSTRY_CATG_CODE
                       , LV4_INDUSTRY_CATG_CN_NAME
                      ';

    V_SQL_TO_PBI_PART := ', INDUSTRY_CATG_CODE
                          , INDUSTRY_CATG_CN_NAME
                         ';

    --V_CODE_REPL_GTS_TYPE := 'INDUS';

  ELSEIF(F_GRANULARITY_TYPE = 'PROD') THEN
    -- 来源表的查询字段
    V_PBI_CODE := 'PROD_LIST_CODE';
    V_GROUP_SQL_FROM_PBI_PART := ', LV0_PROD_LIST_CODE
                       , LV0_PROD_LIST_CN_NAME
                       , LV1_PROD_LIST_CODE
                       , LV1_PROD_LIST_CN_NAME
                       , LV2_PROD_LIST_CODE
                       , LV2_PROD_LIST_CN_NAME
                       , LV3_PROD_LIST_CODE
                       , LV3_PROD_LIST_CN_NAME
                       , LV4_PROD_LIST_CODE
                       , LV4_PROD_LIST_CN_NAME
                      ';

    V_SQL_FROM_PBI_PART := ', LV0_PROD_LIST_CODE  AS LV0_CODE
                       , LV0_PROD_LIST_CN_NAME    AS LV0_CN_NAME
                       , LV1_PROD_LIST_CODE       AS LV1_CODE
                       , LV1_PROD_LIST_CN_NAME    AS LV1_CN_NAME
                       , LV2_PROD_LIST_CODE       AS LV2_CODE
                       , LV2_PROD_LIST_CN_NAME    AS LV2_CN_NAME
                       , LV3_PROD_LIST_CODE       AS LV3_CODE
                       , LV3_PROD_LIST_CN_NAME    AS LV3_CN_NAME
                       , LV4_PROD_LIST_CODE       AS LV4_CODE
                       , LV4_PROD_LIST_CN_NAME    AS LV4_CN_NAME
                      ';

    V_SQL_TO_PBI_PART := ', PROD_LIST_CODE
                          , PROD_LIST_CN_NAME
                         ';

    V_CODE_REPL_GTS_TYPE := 'PROD';

  END IF;

  IF(F_VERSION_ID IS NULL) THEN
    -- 从版本表取最大版本
    SELECT VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1
    ;
  ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;
  
    -- 从版本表取年度最大版本
    SELECT VERSION_ID INTO V_ANNL_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'ANNUAL'
     ORDER BY LAST_UPDATE_DATE DESC
     LIMIT 1
    ;

  -- 从月均价收敛补齐表或取默认基期，默认基期=T-1年的1月
	V_SQL := 'SELECT (SUBSTR(MAX(PERIOD_ID),1,4)::INT - 1)||''01''
              FROM '||V_FROM_AMT_TABLE||'
             WHERE DEL_FLAG = ''N''
               AND VERSION_ID ='|| V_VERSION_ID
  ;

  EXECUTE V_SQL INTO V_BASE_PERIOD_ID;

	RAISE NOTICE '默认基期:%', V_BASE_PERIOD_ID;

  -- 替换关系维表取最大版本的数据
  SELECT MAX(VERSION_ID) INTO V_MAX_REPL_VERSION_ID
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T  -- 新旧编码替换关系表（24年7月版只有销售目录树的）
   WHERE DEL_FLAG = 'N'
  ;

  -- 版本信息表需要更新 VERSION_TYPE
  IF NOT EXISTS(SELECT * FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T WHERE DEL_FLAG = 'N' AND STATUS = 1 AND UPPER(DATA_TYPE) = 'REPLACE_DIM' AND VERSION_TYPE = 'FINAL' AND VERSION_ID = V_MAX_REPL_VERSION_ID) THEN
    UPDATE FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T SET VERSION_TYPE = 'FINAL', STATUS = 1
     WHERE DEL_FLAG = 'N'
       AND UPPER(DATA_TYPE) = 'REPLACE_DIM'
       AND VERSION_ID = V_MAX_REPL_VERSION_ID
    ;

  END IF;

  V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '版本信息表 '||V_MAX_REPL_VERSION_ID||' 的版本类型(VERSION_TYPE)字段值已更新为 FINAL',--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  -- 清理编码替换-权重表数据
  EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;

  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
      F_SP_NAME => V_SP_NAME,    --SP名称
      F_STEP_NUM => V_STEP_NUM,
      F_CAL_LOG_DESC => '删除表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
      F_FORMULA_SQL_TXT  => '非动态SQL',
      F_DML_ROW_COUNT => SQL%ROWCOUNT,
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS'
  );

  -- 计算所有基础数据
  -- 从月均价收敛补齐表取数路径1（即TOP-SPART）的数据，均价用此表补齐的均价，成本金额、发货量不用补齐
    V_SQL := 'INSERT INTO MON_REPL_COST_INFO_TMP1(
                     VERSION_ID
                   , PERIOD_ID
                   , PERIOD_YEAR
                   , LV0_CODE
                   , LV0_CN_NAME
                   , LV1_CODE
                   , LV1_CN_NAME
                   , LV2_CODE
                   , LV2_CN_NAME
                   , LV3_CODE
                   , LV3_CN_NAME
                   , LV4_CODE
                   , LV4_CN_NAME
                   , REGION_CODE
                   , REGION_CN_NAME
                   , REPOFFICE_CODE
                   , REPOFFICE_CN_NAME
                   , SPART_CODE
                   , BG_CODE
                   , BG_CN_NAME
                   , OVERSEA_FLAG
				   , SOFTWARE_MARK
                   , VIEW_FLAG
                   , PROD_QTY
                   , RMB_COST_AMT
                   , RMB_AVG_AMT
              )
              SELECT VERSION_ID
                   , PERIOD_ID
                   , PERIOD_YEAR
                   '||V_SQL_FROM_PBI_PART||'
                   , REGION_CODE
                   , REGION_CN_NAME
                   , REPOFFICE_CODE
                   , REPOFFICE_CN_NAME
                   , SPART_CODE
                   , BG_CODE
                   , BG_CN_NAME
                   , OVERSEA_FLAG
				   , SOFTWARE_MARK
                   , VIEW_FLAG
                   , NVL(PROD_QTY,0) AS PROD_QTY
                   '||V_GS_DECRYPT_COST_AMT||'
                FROM '||V_FROM_AMT_TABLE||'
               WHERE DEL_FLAG = ''N''
                 AND VERSION_ID = '||V_VERSION_ID||'
                 AND VIEW_FLAG = ''PROD_SPART''
             ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '从月均价收敛补齐表 '||V_FROM_AMT_TABLE||' 取数路径1（即TOP-SPART）的数据入到临时表，最大版本：'||V_VERSION_ID||'，默认基期：'||V_BASE_PERIOD_ID||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'TEST11';

    -- 新旧编码替换关系临时表【需要区分目录树】
    V_SQL := 'INSERT INTO CODE_REPL_INFO_TMP(
                     VERSION_ID
                   , BG_CODE
                   , BG_CN_NAME
                   , GTS_TYPE
                   , LV1_CODE
                   , LV1_CN_NAME
                   , LV2_CODE
                   , LV2_CN_NAME
                   , LV3_CODE
                   , LV3_CN_NAME
                   , LV4_CODE
                   , LV4_CN_NAME
                   , PROD_CODE
                   , PROD_CN_NAME
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , SPART_CODE
                   , SPART_DESC
				   , SOFTWARE_MARK
                   , CODE_TYPE
                   , RELATION_TYPE
              )
    WITH BASE_DIM AS
     (
      --关联出软硬件标识
      SELECT T1.VERSION_ID
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.GTS_TYPE
           , T1.LV1_CODE
           , T1.LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , T1.LV3_CODE
           , T1.LV3_CN_NAME
           , T1.LV4_CODE
           , T1.LV4_CN_NAME
           , T1.PROD_CODE
           , T1.PROD_CN_NAME
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
		   , T1.OLD_SPART_CODE
		   , T1.OLD_SPART_DESC
		   , T1.NEW_SPART_CODE
		   , T1.NEW_SPART_DESC
		   , T1.RELATION_TYPE
           , T2.SOFTWARE_MARK
        FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T T1    /* 新旧编码替换关系表（24年7月版只有销售目录树的）*/
        LEFT JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T T2
          ON T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE
         AND T1.DEL_FLAG = ''N''
         AND T1.GTS_TYPE = '''||V_CODE_REPL_GTS_TYPE||'''
         AND T2.DEL_FLAG = ''N''
         AND T2.GTS_TYPE = '''||V_CODE_REPL_GTS_TYPE||'''
         AND T2.VERSION_ID = '||V_ANNL_VERSION||'
         AND T1.LV1_CODE = T2.LV1_CODE
         AND NVL(T1.LV2_CODE,''S1'') = NVL(T2.LV2_CODE,''S1'')
         AND NVL(T1.LV3_CODE,''S2'') = NVL(T2.LV3_CODE,''S2'')
         AND NVL(T1.LV4_CODE,''S3'') = NVL(T2.LV4_CODE,''S3'')
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.GTS_TYPE = T2.GTS_TYPE
         WHERE T1.VERSION_ID = '||V_MAX_REPL_VERSION_ID||' )
              SELECT VERSION_ID
                   , BG_CODE
                   , BG_CN_NAME
                   , GTS_TYPE
                   , LV1_CODE
                   , LV1_CN_NAME
                   , LV2_CODE
                   , LV2_CN_NAME
                   , LV3_CODE
                   , LV3_CN_NAME
                   , LV4_CODE
                   , LV4_CN_NAME
                   , PROD_CODE
                   , PROD_CN_NAME
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , REGEXP_SPLIT_TO_TABLE(OLD_SPART_CODE,'','') AS SPART_CODE
                   , OLD_SPART_DESC AS SPART_DESC
				   , SOFTWARE_MARK
                   , ''OLD'' AS CODE_TYPE
                   , RELATION_TYPE
                FROM BASE_DIM
              UNION ALL
             SELECT VERSION_ID
                   , BG_CODE
                   , BG_CN_NAME
                   , GTS_TYPE
                   , LV1_CODE
                   , LV1_CN_NAME
                   , LV2_CODE
                   , LV2_CN_NAME
                   , LV3_CODE
                   , LV3_CN_NAME
                   , LV4_CODE
                   , LV4_CN_NAME
                   , PROD_CODE
                   , PROD_CN_NAME
                   , REPLACE_RELATION_NAME
                   , REPLACE_RELATION_TYPE
                   , REGEXP_SPLIT_TO_TABLE(NEW_SPART_CODE,'','') AS SPART_CODE
                   , NEW_SPART_DESC AS SPART_DESC
				   , SOFTWARE_MARK
                   , ''NEW'' AS CODE_TYPE
                   , RELATION_TYPE
                FROM BASE_DIM
    ';

    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '目录树：'||V_CODE_REPL_GTS_TYPE||'，新旧编码替换关系临时表临时表的数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'TEST12';

    -- TOP-SPART关联新旧编码替换关系临时表
    INSERT INTO MON_REPL_COST_INFO_TMP3(
           VERSION_ID                     -- 版本ID
         , PERIOD_ID                      -- 会计期
         , PERIOD_YEAR                    -- 会计年
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , SPART_CODE                     -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                   -- 标准成本
         , RMB_AVG_AMT                    -- 均本
    )
    SELECT DISTINCT T1.VERSION_ID           -- 版本ID
         , T1.PERIOD_ID                     -- 会计期
         , T1.PERIOD_YEAR                   -- 会计年
         , T1.LV0_CODE                      -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                   -- 重量级团队LV0名称
         , T1.LV1_CODE                      -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                   -- 重量级团队LV1名称
         , T1.LV2_CODE                      -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                   -- 重量级团队LV2名称
         , T1.LV3_CODE                      -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                   -- 重量级团队LV3名称
         , T1.LV4_CODE                      -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                   -- 重量级团队LV4名称
         , T1.REGION_CODE                   -- 地区部编码
         , T1.REGION_CN_NAME                -- 地区部中文名称
         , T1.REPOFFICE_CODE                -- 代表处编码
         , T1.REPOFFICE_CN_NAME             -- 代表处中文名称
         , T1.SPART_CODE                    -- SPART编码
         , T2.SPART_DESC                    -- SPART描述
         , T1.BG_CODE                       -- BG编码
         , T1.BG_CN_NAME                    -- BG中文名称
         , T1.OVERSEA_FLAG                  -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                     -- 路径1：PROD_SPART/路径2：DIMENSION
         , T2.REPLACE_RELATION_NAME         -- 替换关系名称
         , T2.REPLACE_RELATION_TYPE         -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T2.RELATION_TYPE                 -- 关系（ 替换 、收编）
         , T2.CODE_TYPE                     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.PROD_QTY                      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT::NUMERIC AS RMB_COST_AMT                  -- 标准成本
         , T1.RMB_AVG_AMT::NUMERIC  AS RMB_AVG_AMT                   -- 均本
      FROM MON_REPL_COST_INFO_TMP1 T1  -- 从月均价收敛补齐表取数路径1（即TOP-SPART）的数据
      JOIN CODE_REPL_INFO_TMP T2  -- 新旧编码替换关系临时表
        ON ((T1.LV1_CODE = T2.LV1_CODE AND T2.LV2_CODE = 'ALL' AND T2.LV3_CODE = 'ALL' AND T2.LV4_CODE = 'ALL') OR
            (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND T2.LV3_CODE = 'ALL' AND T2.LV4_CODE = 'ALL') OR
            (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND T1.LV3_CODE = T2.LV3_CODE AND T2.LV4_CODE = 'ALL') OR
            (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND T1.LV3_CODE = T2.LV3_CODE AND T1.LV4_CODE = T2.LV4_CODE)
           )
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.SPART_CODE = T2.SPART_CODE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '新旧编码替换关系表的版本ID：'||V_MAX_REPL_VERSION_ID||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'TEST13 %', SQL%ROWCOUNT;

    /************************************************************************* 【SPART层】编码替换下拉框维表处理 ************************************************************/

    -- 同个关系名称对应的新旧编码不能缺失任一个，如果有缺失，则不需要展示
  DROP TABLE IF EXISTS ALL_TMP;
  CREATE TEMPORARY TABLE ALL_TMP AS
    WITH CODE_REPL_TMP AS(
    SELECT REPLACE_RELATION_NAME,SOFTWARE_MARK,BG_CODE, COUNT(1) AS REPL_SPART_CN
      FROM CODE_REPL_INFO_TMP
     GROUP BY REPLACE_RELATION_NAME,SOFTWARE_MARK,BG_CODE
    ),
    SPART_COST_INFO_ALL_TMP AS(
    SELECT VERSION_ID
         , PERIOD_ID
         , LV0_CODE
         , LV1_CODE
         , LV2_CODE
         , LV3_CODE
         , LV4_CODE
         , REGION_CODE
         , REPOFFICE_CODE
         , REGEXP_SPLIT_TO_TABLE(SPART_CODE,',') AS SPART_CODE
         , BG_CODE
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
      FROM MON_REPL_COST_INFO_TMP3
    ),
    REPL_COST_INDEX_TMP1 AS(
    SELECT DISTINCT REPLACE_RELATION_NAME,LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE, SPART_CODE, CODE_TYPE, SOFTWARE_MARK,
	       REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG
      FROM SPART_COST_INFO_ALL_TMP
    ),
    REPL_COST_INDEX_TMP AS(
    SELECT REPLACE_RELATION_NAME,LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,SOFTWARE_MARK,
	       REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG
         , COUNT(1) AS INDEX_SPART_CN
      FROM REPL_COST_INDEX_TMP1
     GROUP BY REPLACE_RELATION_NAME,LV0_CODE,LV1_CODE,LV2_CODE,LV3_CODE,LV4_CODE,SOFTWARE_MARK,
	       REGION_CODE,REPOFFICE_CODE,BG_CODE,OVERSEA_FLAG
    )
    SELECT T2.REPLACE_RELATION_NAME
	     , T2.LV0_CODE
         , T2.LV1_CODE
         , T2.LV2_CODE
         , T2.LV3_CODE
         , T2.LV4_CODE
         , T2.REGION_CODE
         , T2.REPOFFICE_CODE
		 , T2.BG_CODE
		 , T2.OVERSEA_FLAG
	     , T2.SOFTWARE_MARK
         , T1.REPL_SPART_CN
         , T2.INDEX_SPART_CN
      FROM CODE_REPL_TMP T1
      LEFT JOIN REPL_COST_INDEX_TMP T2
        ON T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
		AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
		AND T1.BG_CODE = T2.BG_CODE
    ;
    RAISE NOTICE'新旧编码数量比';

  IF (F_COST_TYPE = 'STD' AND  F_GRANULARITY_TYPE = 'PROD') THEN
     V_CODE_REPL_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_STD_CODE_REPL_INFO_DIM_T';
  ELSEIF(F_COST_TYPE = 'PSP' AND  F_GRANULARITY_TYPE = 'PROD') THEN
     V_CODE_REPL_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_CODE_REPL_INFO_DIM_T';
  END IF;
  
  -- 清理目标表数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_CODE_REPL_TABLE;

  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
      F_SP_NAME => V_SP_NAME,    --SP名称
      F_STEP_NUM => V_STEP_NUM,
      F_CAL_LOG_DESC => '删除表：'||V_CODE_REPL_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
      F_FORMULA_SQL_TXT  => '非动态SQL',
      F_DML_ROW_COUNT => SQL%ROWCOUNT,
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS'
  );

      -- 数据入到新旧编码替换关系信息维表目标表
    V_SQL := '
      INSERT INTO '||V_CODE_REPL_TABLE||'(
             REGION_CODE                -- 地区部编码
           , REGION_CN_NAME             -- 地区部中文名称
           , REPOFFICE_CODE             -- 代表处编码
           , REPOFFICE_CN_NAME          -- 代表处中文名称
           , BG_CODE                    -- BG编码
           , BG_CN_NAME                 -- BG中文名称
           , OVERSEA_FLAG               -- 国内海外标识（Y 海外、N 国内、G 全球）
           , GTS_TYPE                   -- 目录树类型（IRB 重量级团队目录树、INDUS 产业目录树、PROD 销售目录树）
           , LV1_CODE                   -- LV1编码
           , LV1_CN_NAME                -- LV1中文名称
           , LV2_CODE                   -- LV2编码
           , LV2_CN_NAME                -- LV2中文名称
           , LV3_CODE                   -- LV3编码
           , LV3_CN_NAME                -- LV3中文名称
           , LV4_CODE                   -- LV4编码
           , LV4_CN_NAME                -- LV4中文名称
           , REPLACE_RELATION_NAME      -- 替换关系名称
           , REPLACE_RELATION_TYPE      -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE              -- 关系（ 替换 、收编）
           , OLD_SPART_CODE             -- 旧SPART编码（多SPART用逗号隔开）
           , OLD_SPART_DESC             -- 旧SPART描述
           , NEW_SPART_CODE             -- 新SPART编码（多SPART用逗号隔开）
           , NEW_SPART_DESC             -- 新SPART描述
		   , SOFTWARE_MARK
           , CREATED_BY                 -- 创建人
           , CREATION_DATE              -- 创建时间
           , LAST_UPDATED_BY            -- 修改人
           , LAST_UPDATE_DATE           -- 修改时间
           , DEL_FLAG                   -- 删除标识(未删除：N，已删除：Y)
      )
      WITH REPL_TMP AS(
			SELECT DISTINCT T1.BG_CODE                       -- BG编码
           , T1.BG_CN_NAME                      -- BG中文名称
           , T1.LV1_CODE                      -- 重量级团队LV1编码
           , T1.LV1_CN_NAME                   -- 重量级团队LV1名称
           , T1.LV2_CODE                      -- 重量级团队LV2编码
           , T1.LV2_CN_NAME                   -- 重量级团队LV2名称
           , T1.LV3_CODE                      -- 重量级团队LV3编码
           , T1.LV3_CN_NAME                   -- 重量级团队LV3名称
           , T1.LV4_CODE                      -- 重量级团队LV4编码
           , T1.LV4_CN_NAME                   -- 重量级团队LV4名称
           , T1.REPLACE_RELATION_NAME         -- 替换关系名称
           , T1.REPLACE_RELATION_TYPE         -- 新老编码替换类型（一对一  、一对多 、多对多）
           , T1.RELATION_TYPE                 -- 关系（ 替换 、收编）
		   , T1.CODE_TYPE
		   , CASE WHEN T1.CODE_TYPE = ''OLD'' THEN T1.SPART_CODE END AS OLD_SPART_CODE
           , CASE WHEN T1.CODE_TYPE = ''OLD'' THEN T1.SPART_DESC END AS OLD_SPART_DESC
           , CASE WHEN T1.CODE_TYPE = ''NEW'' THEN T1.SPART_CODE END AS NEW_SPART_CODE
           , CASE WHEN T1.CODE_TYPE = ''NEW'' THEN T1.SPART_DESC END AS NEW_SPART_DESC
           , T1.REGION_CODE                   -- 地区部编码
           , T1.REGION_CN_NAME                -- 地区部中文名称
           , T1.REPOFFICE_CODE                -- 代表处编码
           , T1.REPOFFICE_CN_NAME             -- 代表处中文名称
           , T1.OVERSEA_FLAG                  -- 国内海外标识
		   , T1.SOFTWARE_MARK
			FROM MON_REPL_COST_INFO_TMP3 T1
            INNER JOIN (SELECT * FROM ALL_TMP WHERE REPL_SPART_CN = INDEX_SPART_CN) T2
            ON T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
			AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 
			AND T1.REGION_CODE = T2.REGION_CODE 
			AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE 
			AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
			AND T1.BG_CODE = T2.BG_CODE 
			AND T1.LV4_CODE = T2.LV4_CODE 
			   ), REPL_TMP_2 AS(
			SELECT DISTINCT BG_CODE              -- BG编码
           , BG_CN_NAME                    -- BG中文名称
           , LV1_CODE                      -- 重量级团队LV1编码
           , LV1_CN_NAME                   -- 重量级团队LV1名称
           , REPLACE_RELATION_NAME         -- 替换关系名称
           , REPLACE_RELATION_TYPE         -- 新老编码替换类型（一对一  、一对多 、多对多）
           , RELATION_TYPE                 -- 关系（ 替换 、收编）
					 , CODE_TYPE
					 , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT OLD_SPART_CODE),'','') AS OLD_SPART_CODE
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT OLD_SPART_DESC),'','') AS OLD_SPART_DESC
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT NEW_SPART_CODE),'','') AS NEW_SPART_CODE
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT NEW_SPART_DESC),'','') AS NEW_SPART_DESC
           , REGION_CODE                   -- 地区部编码
           , REGION_CN_NAME                -- 地区部中文名称
           , REPOFFICE_CODE                -- 代表处编码
           , REPOFFICE_CN_NAME             -- 代表处中文名称
           , OVERSEA_FLAG                  -- 国内海外标识
		   , SOFTWARE_MARK
						FROM REPL_TMP T1
					WHERE REPLACE_RELATION_TYPE <> ''一对一''
					GROUP BY BG_CODE
           , BG_CN_NAME
           , LV1_CODE
           , LV1_CN_NAME
					 , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
					 , CODE_TYPE
					 , REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , OVERSEA_FLAG
		   , SOFTWARE_MARK
			   )
					SELECT
						REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
           , '''||F_GRANULARITY_TYPE||''' AS GTS_TYPE
           , LV1_CODE
           , LV1_CN_NAME
           , LV2_CODE
           , LV2_CN_NAME
           , LV3_CODE
           , LV3_CN_NAME
           , LV4_CODE
           , LV4_CN_NAME
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT OLD_SPART_CODE),'','') AS OLD_SPART_CODE
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT OLD_SPART_DESC),'','') AS OLD_SPART_DESC
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT NEW_SPART_CODE),'','') AS NEW_SPART_CODE
           , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT NEW_SPART_DESC),'','') AS NEW_SPART_DESC
		   , SOFTWARE_MARK
           , -1 AS CREATED_BY
 	         , CURRENT_TIMESTAMP AS CREATION_DATE
 	         , -1 AS LAST_UPDATED_BY
 	         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	         , ''N'' AS DEL_FLAG
		FROM REPL_TMP WHERE REPLACE_RELATION_TYPE = ''一对一''
		GROUP BY REGION_CODE
           , REGION_CN_NAME
           , REPOFFICE_CODE
           , REPOFFICE_CN_NAME
           , BG_CODE
           , BG_CN_NAME
           , OVERSEA_FLAG
           , LV1_CODE
           , LV1_CN_NAME
           , LV2_CODE
           , LV2_CN_NAME
           , LV3_CODE
           , LV3_CN_NAME
           , LV4_CODE
           , LV4_CN_NAME
           , REPLACE_RELATION_NAME
           , REPLACE_RELATION_TYPE
           , RELATION_TYPE
		   , SOFTWARE_MARK
		 UNION ALL
		 SELECT
						T1.REGION_CODE
           , T1.REGION_CN_NAME
           , T1.REPOFFICE_CODE
           , T1.REPOFFICE_CN_NAME
           , T1.BG_CODE
           , T1.BG_CN_NAME
           , T1.OVERSEA_FLAG
           , '''||F_GRANULARITY_TYPE||''' AS GTS_TYPE
           , T1.LV1_CODE
           , T1.LV1_CN_NAME
           , T1.LV2_CODE
           , T1.LV2_CN_NAME
           , T1.LV3_CODE
           , T1.LV3_CN_NAME
           , T1.LV4_CODE
           , T1.LV4_CN_NAME
           , T1.REPLACE_RELATION_NAME
           , T1.REPLACE_RELATION_TYPE
           , T1.RELATION_TYPE
           , T2.OLD_SPART_CODE
           , T2.OLD_SPART_DESC
           , T2.NEW_SPART_CODE
           , T2.NEW_SPART_DESC
		   , T1.SOFTWARE_MARK
           , -1 AS CREATED_BY
 	         , CURRENT_TIMESTAMP AS CREATION_DATE
 	         , -1 AS LAST_UPDATED_BY
 	         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
 	         , ''N'' AS DEL_FLAG
		FROM REPL_TMP T1 JOIN REPL_TMP_2 T2 ON (T1.REGION_CODE = T2.REGION_CODE AND
            T1.REGION_CN_NAME = T2.REGION_CN_NAME AND
            T1.REPOFFICE_CODE = T2.REPOFFICE_CODE AND
            T1.REPOFFICE_CN_NAME = T2.REPOFFICE_CN_NAME AND
            T1.BG_CODE = T2.BG_CODE AND
            T1.BG_CN_NAME = T2.BG_CN_NAME AND
            T1.OVERSEA_FLAG = T2.OVERSEA_FLAG AND
						T1.CODE_TYPE = T2.CODE_TYPE AND
						T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME AND
            T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE AND
            T1.RELATION_TYPE = T2.RELATION_TYPE AND
					  T1.LV1_CODE = T2.LV1_CODE 
			AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK )'         -- 202410版本新增,只取对应软硬件标识的数据进行计算)
      ;
    EXECUTE IMMEDIATE V_SQL;
      RAISE NOTICE'数据入到新旧编码替换关系信息维表目标表（JAVA用的表）';

      V_STEP_NUM := V_STEP_NUM+1;
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
          F_SP_NAME => V_SP_NAME,    --SP名称
          F_STEP_NUM => V_STEP_NUM,
          F_CAL_LOG_DESC => '数据入到新旧编码替换关系信息维表目标表（JAVA用的表），数据量：'||SQL%ROWCOUNT,--日志描述
          F_FORMULA_SQL_TXT  => '非动态SQL',
          F_DML_ROW_COUNT => SQL%ROWCOUNT,
          F_RESULT_STATUS => X_RESULT_STATUS,
          F_ERRBUF => 'SUCCESS'
      );
    
 /************************************************************************* 【SPART层】 ************************************************************/
  V_SQL := '
    -- TOP-SPART-新旧编码替换关系-关联维表下拉框临时表
    DROP TABLE IF EXISTS MON_REPL_COST_INFO_TMP2;
    CREATE TEMPORARY TABLE MON_REPL_COST_INFO_TMP2 AS
    SELECT DISTINCT T1.VERSION_ID           -- 版本ID
         , T1.PERIOD_ID                     -- 会计期
         , T1.PERIOD_YEAR                   -- 会计年
         , T1.LV0_CODE                      -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                   -- 重量级团队LV0名称
         , T1.LV1_CODE                      -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                   -- 重量级团队LV1名称
         , T1.LV2_CODE                      -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                   -- 重量级团队LV2名称
         , T1.LV3_CODE                      -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                   -- 重量级团队LV3名称
         , T1.LV4_CODE                      -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                   -- 重量级团队LV4名称
         , T1.REGION_CODE                   -- 地区部编码
         , T1.REGION_CN_NAME                -- 地区部中文名称
         , T1.REPOFFICE_CODE                -- 代表处编码
         , T1.REPOFFICE_CN_NAME             -- 代表处中文名称
         , T1.SPART_CODE                    -- SPART编码
         , T1.SPART_DESC                    -- SPART描述
         , T1.BG_CODE                       -- BG编码
         , T1.BG_CN_NAME                    -- BG中文名称
         , T1.OVERSEA_FLAG                  -- 国内海外标识
         , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                     -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME         -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE         -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                 -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.PROD_QTY                      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT::NUMERIC AS RMB_COST_AMT                  -- 标准成本
         , T1.RMB_AVG_AMT::NUMERIC  AS RMB_AVG_AMT                   -- 均本
      FROM MON_REPL_COST_INFO_TMP3 T1  -- 从新旧编码替换关系临时表的数据
      INNER JOIN (
                   SELECT DISTINCT REPLACE_RELATION_NAME,SOFTWARE_MARK,REGION_CODE,REPOFFICE_CODE,
                          OVERSEA_FLAG,BG_CODE,REPLACE_RELATION_TYPE,RELATION_TYPE,LV4_CODE
                      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_CODE_REPL_INFO_DIM_T) T2
      ON T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
      AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 
      AND T1.REGION_CODE = T2.REGION_CODE 
      AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE 
      AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
      AND T1.BG_CODE = T2.BG_CODE 
      AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE 
      AND T1.RELATION_TYPE = T2.RELATION_TYPE 
      AND T1.LV4_CODE = T2.LV4_CODE 
    ';
    EXECUTE IMMEDIATE V_SQL;
	
      RAISE NOTICE'TOP-SPART-新旧编码替换关系-关联维表下拉框临时表';

    -- 非虚化新或老编码（单选LV3.5下）
    INSERT INTO REPL_SPART_COST_INFO_ALL_TMP(
         VERSION_ID                     -- 版本ID
       , PERIOD_ID                      -- 会计期
       , BASE_PERIOD_ID                 -- 默认基期
       , PERIOD_YEAR                    -- 会计年
       , LV0_CODE                       -- 重量级团队LV0编码
       , LV0_CN_NAME                    -- 重量级团队LV0名称
       , LV1_CODE                       -- 重量级团队LV1编码
       , LV1_CN_NAME                    -- 重量级团队LV1名称
       , LV2_CODE                       -- 重量级团队LV2编码
       , LV2_CN_NAME                    -- 重量级团队LV2名称
       , LV3_CODE                       -- 重量级团队LV3编码
       , LV3_CN_NAME                    -- 重量级团队LV3名称
       , LV4_CODE                       -- 重量级团队LV4编码
       , LV4_CN_NAME                    -- 重量级团队LV4名称
       , REGION_CODE                    -- 地区部编码
       , REGION_CN_NAME                 -- 地区部中文名称
       , REPOFFICE_CODE                 -- 代表处编码
       , REPOFFICE_CN_NAME              -- 代表处中文名称
       , SPART_CODE                     -- SPART编码
       , SPART_DESC                     -- SPART描述
       , BG_CODE                        -- BG编码
       , BG_CN_NAME                     -- BG中文名称
       , OVERSEA_FLAG                   -- 国内海外标识
       , SOFTWARE_MARK
       , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
       , REPLACE_RELATION_NAME          -- 替换关系名称
       , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                  -- 关系（ 替换 、收编）
       , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , GROUP_LEVEL                    -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                   -- 标准成本
       , COST_INDEX                     -- 成本指数
    )
    -- 默认基期临时表
    WITH REPL_SPART_COST_INFO_TMP1 AS(
    SELECT VERSION_ID                     -- 版本ID
         , PERIOD_ID                      -- 会计期
         , PERIOD_YEAR                    -- 会计年
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , SPART_CODE                     -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
         , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                   -- 标准成本
         , RMB_AVG_AMT                    -- 均本
      FROM MON_REPL_COST_INFO_TMP2  -- TOP-SPART关联新旧编码替换关系临时表
     WHERE PERIOD_ID = V_BASE_PERIOD_ID  -- 默认基期
    ),
    -- 多SPART新或老编码SPART期间（T-1年至T年YTD）临时表
    REPL_SPART_COST_INFO_TMP2 AS(
    SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , SPART_CODE                     -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
         , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
      FROM MON_REPL_COST_INFO_TMP2  -- TOP-SPART关联新旧编码替换关系临时表
     WHERE REPLACE_RELATION_TYPE IN('一对多','多对一','多对多')
       AND SUBSTR(PERIOD_ID,1,4) >= (TO_CHAR(CURRENT_DATE,'YYYY')::NUMERIC)-1  -- T-1年至T年YTD
     GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SPART_CODE
         , SPART_DESC
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
         , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
    ),
    -- 多SPART新或老编码期间（T-1年至T年YTD）临时表
    REPL_SPART_COST_INFO_TMP3 AS(
    SELECT VERSION_ID                     -- 版本ID
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
         , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
      FROM REPL_SPART_COST_INFO_TMP2  -- 多SPART新或老编码SPART期间（T-1年至T年YTD）临时表
     GROUP BY VERSION_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
         , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE

    ),
    -- 多SPART新或老编码SPART期间（T-1年至T年YTD）金额/多SPART新或老编码期间（T-1年至T年YTD）金额
    REPL_SPART_COST_INFO_TMP4 AS(
    SELECT T1.VERSION_ID                     -- 版本ID
         , T1.LV0_CODE                       -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
         , T1.LV1_CODE                       -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
         , T1.LV2_CODE                       -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
         , T1.LV3_CODE                       -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
         , T1.LV4_CODE                       -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
         , T1.REGION_CODE                    -- 地区部编码
         , T1.REGION_CN_NAME                 -- 地区部中文名称
         , T1.REPOFFICE_CODE                 -- 代表处编码
         , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
         , T1.SPART_CODE                     -- SPART编码
         , T1.SPART_DESC                     -- SPART描述
         , T1.BG_CODE                        -- BG编码
         , T1.BG_CN_NAME                     -- BG中文名称
         , T1.OVERSEA_FLAG                   -- 国内海外标识
         , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME          -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.RMB_COST_AMT AS INTERVAL_SPART_COST_AMT  -- 新或老编码SPART期间（T-1年至T年YTD）金额
         , T2.RMB_COST_AMT AS INTERVAL_COST_AMT        -- 新或老编码期间（T-1年至T年YTD）金额
         , ROUND((CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE (T1.RMB_COST_AMT/T2.RMB_COST_AMT) END),10) AS WEIGHT_RATE
      FROM REPL_SPART_COST_INFO_TMP2 T1  -- 多SPART新或老编码SPART期间（T-1年至T年YTD）临时表
      LEFT JOIN REPL_SPART_COST_INFO_TMP3 T2  -- 多SPART新或老编码期间（T-1年至T年YTD）临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.LV4_CODE = T2.LV4_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.CODE_TYPE = T2.CODE_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ),
    -- 多SPART层级计算，先计算每一个新老编码SPART指数
    REPL_SPART_COST_INFO_TMP5 AS(
    SELECT T1.VERSION_ID                     -- 版本ID
         , T1.PERIOD_ID                      -- 会计期
         , T2.PERIOD_ID AS BASE_PERIOD_ID    -- 默认基期
         , T1.PERIOD_YEAR                    -- 会计年
         , T1.LV0_CODE                       -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
         , T1.LV1_CODE                       -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
         , T1.LV2_CODE                       -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
         , T1.LV3_CODE                       -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
         , T1.LV4_CODE                       -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
         , T1.REGION_CODE                    -- 地区部编码
         , T1.REGION_CN_NAME                 -- 地区部中文名称
         , T1.REPOFFICE_CODE                 -- 代表处编码
         , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
         , T1.SPART_CODE                     -- SPART编码
         , T1.SPART_DESC                     -- SPART描述
         , T1.BG_CODE                        -- BG编码
         , T1.BG_CN_NAME                     -- BG中文名称
         , T1.OVERSEA_FLAG                   -- 国内海外标识
         , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME          -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT                   -- 标准成本
         , T1.RMB_AVG_AMT                    -- 均本
         , T2.RMB_AVG_AMT  AS BASE_AVG_COST_AMT   -- 默认基期均本
         , T3.WEIGHT_RATE  AS INTERVAL_SPART_AMT  -- 多SPART新或老编码SPART期间（T-1年至T年YTD）金额
         , (CASE WHEN T2.RMB_AVG_AMT = 0 THEN 0
                 ELSE (ROUND(T1.RMB_AVG_AMT/T2.RMB_AVG_AMT,10)*100*T3.WEIGHT_RATE)
            END) AS COST_INDEX  -- 成本指数
      FROM MON_REPL_COST_INFO_TMP2 T1  -- TOP-SPART关联新旧编码替换关系临时表
      LEFT JOIN REPL_SPART_COST_INFO_TMP1 T2  -- 默认基期临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.LV4_CODE = T2.LV4_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.SPART_CODE = T2.SPART_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.CODE_TYPE = T2.CODE_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
      LEFT JOIN REPL_SPART_COST_INFO_TMP4 T3
        ON T1.VERSION_ID = T3.VERSION_ID
       AND T1.LV0_CODE = T3.LV0_CODE
       AND T1.LV1_CODE = T3.LV1_CODE
       AND T1.LV2_CODE = T3.LV2_CODE
       AND T1.LV3_CODE = T3.LV3_CODE
       AND T1.LV4_CODE = T3.LV4_CODE
       AND T1.REGION_CODE = T3.REGION_CODE
       AND T1.REPOFFICE_CODE = T3.REPOFFICE_CODE
       AND T1.SPART_CODE = T3.SPART_CODE
       AND T1.BG_CODE = T3.BG_CODE
       AND T1.OVERSEA_FLAG = T3.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T3.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T3.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T3.RELATION_TYPE
       AND T1.CODE_TYPE = T3.CODE_TYPE
       AND T1.SOFTWARE_MARK = T3.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
     WHERE T1.REPLACE_RELATION_TYPE IN('一对多','多对一','多对多')
    ),
    -- 然后汇总到关系名称、关系类型、关系，新、老编码用逗号拼接
    REPL_SPART_COST_INFO_TMP6 AS(
    SELECT VERSION_ID                     -- 版本ID
         , PERIOD_ID                      -- 会计期
         , BASE_PERIOD_ID                 -- 默认基期
         , PERIOD_YEAR                    -- 会计年
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , ARRAY_TO_STRING(ARRAY_AGG(DISTINCT SPART_CODE),',') AS SPART_CODE -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
         , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
         , SUM(COST_INDEX)   AS COST_INDEX    -- 成本指数
      FROM REPL_SPART_COST_INFO_TMP5
     GROUP BY VERSION_ID
         , PERIOD_ID
         , BASE_PERIOD_ID
         , PERIOD_YEAR
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , SPART_DESC
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
         , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
    )
    -- 1、单SPART层级计算
    SELECT T1.VERSION_ID                     -- 版本ID
         , T1.PERIOD_ID                      -- 会计期
         , T2.PERIOD_ID AS BASE_PERIOD_ID    -- 默认基期
         , T1.PERIOD_YEAR                    -- 会计年
         , T1.LV0_CODE                       -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                    -- 重量级团队LV0名称
         , T1.LV1_CODE                       -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                    -- 重量级团队LV1名称
         , T1.LV2_CODE                       -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                    -- 重量级团队LV2名称
         , T1.LV3_CODE                       -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                    -- 重量级团队LV3名称
         , T1.LV4_CODE                       -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                    -- 重量级团队LV4名称
         , T1.REGION_CODE                    -- 地区部编码
         , T1.REGION_CN_NAME                 -- 地区部中文名称
         , T1.REPOFFICE_CODE                 -- 代表处编码
         , T1.REPOFFICE_CN_NAME              -- 代表处中文名称
         , T1.SPART_CODE                     -- SPART编码
         , T1.SPART_DESC                     -- SPART描述
         , T1.BG_CODE                        -- BG编码
         , T1.BG_CN_NAME                     -- BG中文名称
         , T1.OVERSEA_FLAG                   -- 国内海外标识
         , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME          -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                  -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , 'SPART' AS GROUP_LEVEL            -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , T1.PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT                   -- 标准成本
         , (CASE WHEN T2.RMB_AVG_AMT = 0 THEN 0
                 ELSE ROUND(T1.RMB_AVG_AMT/T2.RMB_AVG_AMT*100,10)
            END) AS COST_INDEX  -- 成本指数
      FROM MON_REPL_COST_INFO_TMP2 T1  -- TOP-SPART关联新旧编码替换关系临时表
      LEFT JOIN REPL_SPART_COST_INFO_TMP1 T2  -- 默认基期临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.LV4_CODE = T2.LV4_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.SPART_CODE = T2.SPART_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.CODE_TYPE = T2.CODE_TYPE
       AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
     WHERE T1.REPLACE_RELATION_TYPE = '一对一'
     UNION ALL
    -- 2、多SPART已经计算成1条，SPART编码用逗号拼接保存
    SELECT VERSION_ID                     -- 版本ID
         , PERIOD_ID                      -- 会计期
         , BASE_PERIOD_ID                 -- 默认基期
         , PERIOD_YEAR                    -- 会计年
         , LV0_CODE                       -- 重量级团队LV0编码
         , LV0_CN_NAME                    -- 重量级团队LV0名称
         , LV1_CODE                       -- 重量级团队LV1编码
         , LV1_CN_NAME                    -- 重量级团队LV1名称
         , LV2_CODE                       -- 重量级团队LV2编码
         , LV2_CN_NAME                    -- 重量级团队LV2名称
         , LV3_CODE                       -- 重量级团队LV3编码
         , LV3_CN_NAME                    -- 重量级团队LV3名称
         , LV4_CODE                       -- 重量级团队LV4编码
         , LV4_CN_NAME                    -- 重量级团队LV4名称
         , REGION_CODE                    -- 地区部编码
         , REGION_CN_NAME                 -- 地区部中文名称
         , REPOFFICE_CODE                 -- 代表处编码
         , REPOFFICE_CN_NAME              -- 代表处中文名称
         , SPART_CODE                     -- SPART编码
         , SPART_DESC                     -- SPART描述
         , BG_CODE                        -- BG编码
         , BG_CN_NAME                     -- BG中文名称
         , OVERSEA_FLAG                   -- 国内海外标识
         , SOFTWARE_MARK
         , VIEW_FLAG                      -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME          -- 替换关系名称
         , REPLACE_RELATION_TYPE          -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                  -- 关系（ 替换 、收编）
         , CODE_TYPE                      -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , 'SPART' AS GROUP_LEVEL         -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , PROD_QTY                       -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                   -- 标准成本
         , COST_INDEX                     -- 成本指数
      FROM REPL_SPART_COST_INFO_TMP6
    ; 

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'SPART层级（包括单SPART、多SPART）数据入到临时表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'SPART层级（包括单SPART、多SPART）数据入到临时表';

    /************************************************************************* 【PBI层】 ************************************************************/
    -- LV3.5期间（T-1年+T年YTD）
  DROP TABLE IF EXISTS REPL_PBI_LV4_COST_INFO_TMP3;
  CREATE TEMPORARY TABLE REPL_PBI_LV4_COST_INFO_TMP3 AS
    SELECT VERSION_ID                      -- 版本ID
         , BASE_PERIOD_ID                  -- 基期
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , LV3_CODE                        -- 重量级团队LV3编码
         , LV3_CN_NAME                     -- 重量级团队LV3名称
         , LV4_CODE                        -- 重量级团队LV4编码
         , LV4_CN_NAME                     -- 重量级团队LV4名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
      FROM REPL_SPART_COST_INFO_ALL_TMP  -- SPART层级临时表
     GROUP BY VERSION_ID
         , BASE_PERIOD_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , CODE_TYPE
    ;

    -- LV3.5下替换关系权重=LV3.5层级下新老编码替换关系期间（T-1年+T年YTD）的发货量/LV3.5层级新老编码期间（T-1年+T年YTD）的发货量
  V_SQL := '
    INSERT INTO '||V_TO_WEIGHT_TABLE||'(
           VERSION_ID               /*版本ID                                   */
         , BASE_PERIOD_ID           /*基期                                     */
         '||V_SQL_TO_PBI_PART||'
         , GROUP_CODE               /*各层级编码                               */
         , GROUP_CN_NAME            /*各层级中文名称                           */
         , GROUP_LEVEL              /*各层级（SPART/LV4/LV3/LV2/LV1/LV0）      */
         , WEIGHT_RATE              /*权重值                                   */
         , PARENT_CODE              /*父级编码                                 */
         , PARENT_CN_NAME           /*父级中文名称                             */
         , REPLACE_RELATION_NAME    /*替换关系名称                             */
         , REPLACE_RELATION_TYPE    /*替换关系类型（一对一 、一对多 、多对多） */
         , RELATION_TYPE            /*关系（替换、收编）                       */
         , CODE_TYPE                /*编码类型（NEW:新编码  OLD: 旧编码 ）     */
         , REGION_CODE              /*地区部编码                               */
         , REGION_CN_NAME           /*地区部中文名称                           */
         , REPOFFICE_CODE           /*代表处编码                               */
         , REPOFFICE_CN_NAME        /*代表处中文名称                           */
         , BG_CODE                  /*BG编码                                   */
         , BG_CN_NAME               /*BG中文名称                               */
         , OVERSEA_FLAG             /*国内海外标识                             */
		 , SOFTWARE_MARK
         , CREATED_BY               /*创建人                                   */
         , CREATION_DATE            /*创建时间                                 */
         , LAST_UPDATED_BY          /*修改人                                   */
         , LAST_UPDATE_DATE         /*修改时间                                 */
         , DEL_FLAG                 /*删除标识(未删除：N，已删除：Y)           */
    )
    -- LV3.5下替换关系期间（T-1年+T年YTD）临时表
    WITH REPL_PBI_LV4_COST_INFO_TMP2 AS(
    SELECT VERSION_ID                      -- 版本ID
         , BASE_PERIOD_ID                  -- 基期
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , LV3_CODE                        -- 重量级团队LV3编码
         , LV3_CN_NAME                     -- 重量级团队LV3名称
         , LV4_CODE                        -- 重量级团队LV4编码
         , LV4_CN_NAME                     -- 重量级团队LV4名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME           -- 替换关系名称
         , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                   -- 关系（ 替换 、收编）
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
      FROM REPL_SPART_COST_INFO_ALL_TMP  -- SPART层级临时表
     GROUP BY VERSION_ID
         , BASE_PERIOD_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
    )
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.BASE_PERIOD_ID                  -- 基期
         , T1.LV4_CODE                        -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                     -- 重量级团队LV4名称
         , NULL AS GROUP_CODE    
         , NULL AS GROUP_CN_NAME 
         , NULL AS GROUP_LEVEL   
         , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- LV3.5层级下替换关系权重
         , T1.LV4_CODE
         , T1.LV4_CN_NAME
         , T1.REPLACE_RELATION_NAME
         , T1.REPLACE_RELATION_TYPE
         , T1.RELATION_TYPE
         , T1.CODE_TYPE
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
	     , T1.SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_PBI_LV4_COST_INFO_TMP2 T1  -- LV3.5替换关系期间（T-1年+T年YTD）临时表
      LEFT JOIN REPL_PBI_LV4_COST_INFO_TMP3 T2  -- LV3.5期间（T-1年+T年YTD）
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.LV4_CODE = T2.LV4_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK'          -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ;
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'LV3.5下替换关系权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 【PBI维度LV3.5层级成本指数】
  V_SQL := '
    INSERT INTO REPL_PBI_LV4_COST_INFO_TMP(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , BASE_PERIOD_ID                  -- 默认基期
         , PERIOD_YEAR                     -- 会计年
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , LV3_CODE                        -- 重量级团队LV3编码
         , LV3_CN_NAME                     -- 重量级团队LV3名称
         , LV4_CODE                        -- 重量级团队LV4编码
         , LV4_CN_NAME                     -- 重量级团队LV4名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , REPLACE_RELATION_NAME           -- 替换关系名称
         , REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
         , RELATION_TYPE                   -- 关系（ 替换 、收编）
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
         , COST_INDEX                      -- 成本指数
    )
    WITH REPL_PBI_LV4_COST_INFO_TMP1 AS(
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.PERIOD_ID                       -- 会计期
         , T1.BASE_PERIOD_ID                  -- 默认基期
         , T1.PERIOD_YEAR                     -- 会计年
         , T1.LV0_CODE                        -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                     -- 重量级团队LV0名称
         , T1.LV1_CODE                        -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                     -- 重量级团队LV1名称
         , T1.LV2_CODE                        -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                     -- 重量级团队LV2名称
         , T1.LV3_CODE                        -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                     -- 重量级团队LV3名称
         , T1.LV4_CODE                        -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                     -- 重量级团队LV4名称
         , T1.REGION_CODE                     -- 地区部编码
         , T1.REGION_CN_NAME                  -- 地区部中文名称
         , T1.REPOFFICE_CODE                  -- 代表处编码
         , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
         , T1.BG_CODE                         -- BG编码
         , T1.BG_CN_NAME                      -- BG中文名称
         , T1.OVERSEA_FLAG                    -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , NULL AS REPLACE_RELATION_NAME        -- 替换关系名称
         , NULL AS REPLACE_RELATION_TYPE        -- 新老编码替换类型（一对一  、一对多 、多对多）
         , NULL AS RELATION_TYPE                -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , ''LV4'' AS GROUP_LEVEL               -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , SUM(T1.PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
         , ROUND(SUM(T1.COST_INDEX*T2.WEIGHT_RATE),10) AS COST_INDEX  -- 成本指数
      FROM REPL_SPART_COST_INFO_ALL_TMP T1  -- SPART层级成本指数临时表
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2  -- LV3.5下替换关系权重临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV4_CODE = T2.PARENT_CODE
       AND T1.LV4_CODE = T2.'||V_PBI_CODE||'
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME   -- 本层级此编码关联即可，无需GROUP_CODE
       AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
       AND T1.RELATION_TYPE = T2.RELATION_TYPE
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
       WHERE T2.VERSION_ID = '||V_VERSION_ID||'
       AND T2.GROUP_LEVEL IS NULL   -- 取GROUP_LEVEL为空值的版本数据
     GROUP BY T1.VERSION_ID
         , T1.PERIOD_ID
         , T1.BASE_PERIOD_ID
         , T1.PERIOD_YEAR
         , T1.LV0_CODE
         , T1.LV0_CN_NAME
         , T1.LV1_CODE
         , T1.LV1_CN_NAME
         , T1.LV2_CODE
         , T1.LV2_CN_NAME
         , T1.LV3_CODE
         , T1.LV3_CN_NAME
         , T1.LV4_CODE
         , T1.LV4_CN_NAME
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG
         , T1.CODE_TYPE
    ),
    REPL_PBI_LV4_COST_INFO_TMP4 AS(
    SELECT VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , BASE_PERIOD_ID                  -- 默认基期
         , PERIOD_YEAR                     -- 会计年
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , LV3_CODE                        -- 重量级团队LV3编码
         , LV3_CN_NAME                     -- 重量级团队LV3名称
         , LV4_CODE                        -- 重量级团队LV4编码
         , LV4_CN_NAME                     -- 重量级团队LV4名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , COUNT(*) AS CODE_TYPE_CN
      FROM REPL_PBI_LV4_COST_INFO_TMP1
     GROUP BY VERSION_ID
         , PERIOD_ID
         , BASE_PERIOD_ID
         , PERIOD_YEAR
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , LV4_CODE
         , LV4_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , GROUP_LEVEL
    )
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.PERIOD_ID                       -- 会计期
         , T1.BASE_PERIOD_ID                  -- 默认基期
         , T1.PERIOD_YEAR                     -- 会计年
         , T1.LV0_CODE                        -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                     -- 重量级团队LV0名称
         , T1.LV1_CODE                        -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                     -- 重量级团队LV1名称
         , T1.LV2_CODE                        -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                     -- 重量级团队LV2名称
         , T1.LV3_CODE                        -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                     -- 重量级团队LV3名称
         , T1.LV4_CODE                        -- 重量级团队LV4编码
         , T1.LV4_CN_NAME                     -- 重量级团队LV4名称
         , T1.REGION_CODE                     -- 地区部编码
         , T1.REGION_CN_NAME                  -- 地区部中文名称
         , T1.REPOFFICE_CODE                  -- 代表处编码
         , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
         , T1.BG_CODE                         -- BG编码
         , T1.BG_CN_NAME                      -- BG中文名称
         , T1.OVERSEA_FLAG                    -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.REPLACE_RELATION_NAME           -- 替换关系名称
         , T1.REPLACE_RELATION_TYPE           -- 新老编码替换类型（一对一  、一对多 、多对多）
         , T1.RELATION_TYPE                   -- 关系（ 替换 、收编）
         , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , T1.GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , T1.PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , T1.RMB_COST_AMT                    -- 标准成本
         , T1.COST_INDEX                      -- 成本指数 
      FROM REPL_PBI_LV4_COST_INFO_TMP1 T1
     WHERE NOT EXISTS (SELECT T2.VERSION_ID
                            , T2.PERIOD_ID
                            , T2.BASE_PERIOD_ID
                            , T2.PERIOD_YEAR
                            , T2.LV0_CODE
                            , T2.LV0_CN_NAME
                            , T2.LV1_CODE
                            , T2.LV1_CN_NAME
                            , T2.LV2_CODE
                            , T2.LV2_CN_NAME
                            , T2.LV3_CODE
                            , T2.LV3_CN_NAME
                            , T2.LV4_CODE
                            , T2.LV4_CN_NAME
                            , T2.REGION_CODE
                            , T2.REGION_CN_NAME
                            , T2.REPOFFICE_CODE
                            , T2.REPOFFICE_CN_NAME
                            , T2.BG_CODE
                            , T2.BG_CN_NAME
                            , T2.OVERSEA_FLAG
							, T2.SOFTWARE_MARK
                            , T2.VIEW_FLAG
                            , T2.GROUP_LEVEL
                         FROM REPL_PBI_LV4_COST_INFO_TMP4 T2
                        WHERE T1.VERSION_ID     = T2.VERSION_ID
                          AND T1.PERIOD_ID      = T2.PERIOD_ID
                          AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
                          AND T1.LV0_CODE       = T2.LV0_CODE
                          AND T1.LV1_CODE       = T2.LV1_CODE
                          AND T1.LV2_CODE       = T2.LV2_CODE
                          AND T1.LV3_CODE       = T2.LV3_CODE
                          AND T1.LV4_CODE       = T2.LV4_CODE
                          AND T1.REGION_CODE    = T2.REGION_CODE
                          AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
                          AND T1.BG_CODE        = T2.BG_CODE
                          AND T1.OVERSEA_FLAG   = T2.OVERSEA_FLAG
                          AND T1.VIEW_FLAG      = T2.VIEW_FLAG
                          AND T1.GROUP_LEVEL    = T2.GROUP_LEVEL
						  AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
                          AND T2.CODE_TYPE_CN = 1
                      )'
    ;
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'PBI维度新或老编码LV3.5层级数据入到临时表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE 'PBILV3.5层级';

    -- LV3期间（T-1年+T年YTD）
  DROP TABLE IF EXISTS REPL_PBI_LV3_COST_INFO_TMP3;
  CREATE TEMPORARY TABLE REPL_PBI_LV3_COST_INFO_TMP3 AS
    SELECT VERSION_ID                      -- 版本ID
         , BASE_PERIOD_ID                  -- 基期
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , LV3_CODE                        -- 重量级团队LV3编码
         , LV3_CN_NAME                     -- 重量级团队LV3名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
      FROM REPL_PBI_LV4_COST_INFO_TMP3  -- LV3.5期间（T-1年+T年YTD）临时表
     GROUP BY VERSION_ID
         , BASE_PERIOD_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , LV3_CODE
         , LV3_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , CODE_TYPE
    ;

   RAISE NOTICE'REPL_PBI_LV3_COST_INFO_TMP3';

    -- LV3.5层级权重=LV3.5层级新老编码期间（T-1年+T年YTD）的发货量/LV3层级新老编码期间（T-1年+T年YTD）的发货量
  V_SQL := '
    INSERT INTO '||V_TO_WEIGHT_TABLE||'(
           VERSION_ID               /*版本ID                                   */
         , BASE_PERIOD_ID           /*基期                                     */
         '||V_SQL_TO_PBI_PART||'
         , GROUP_CODE               /*各层级编码                               */
         , GROUP_CN_NAME            /*各层级中文名称                           */
         , GROUP_LEVEL              /*各层级（SPART/LV4/LV3/LV2/LV1/LV0）      */
         , WEIGHT_RATE              /*权重值                                   */
         , PARENT_CODE              /*父级编码                                 */
         , PARENT_CN_NAME           /*父级中文名称                             */
         , CODE_TYPE                /*编码类型（NEW:新编码  OLD: 旧编码 ）     */
         , REGION_CODE              /*地区部编码                               */
         , REGION_CN_NAME           /*地区部中文名称                           */
         , REPOFFICE_CODE           /*代表处编码                               */
         , REPOFFICE_CN_NAME        /*代表处中文名称                           */
         , BG_CODE                  /*BG编码                                   */
         , BG_CN_NAME               /*BG中文名称                               */
         , OVERSEA_FLAG             /*国内海外标识                             */
		 , SOFTWARE_MARK
         , CREATED_BY               /*创建人                                   */
         , CREATION_DATE            /*创建时间                                 */
         , LAST_UPDATED_BY          /*修改人                                   */
         , LAST_UPDATE_DATE         /*修改时间                                 */
         , DEL_FLAG                 /*删除标识(未删除：N，已删除：Y)           */
    )
    SELECT T1.VERSION_ID
         , T1.BASE_PERIOD_ID
         , T1.LV4_CODE
         , T1.LV4_CN_NAME
         , T1.LV4_CODE AS GROUP_CODE
         , T1.LV4_CN_NAME AS GROUP_CN_NAME
         , ''LV4'' AS GROUP_LEVEL
         , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- LV3.5层级权重
         , T1.LV3_CODE AS PARENT_CODE
         , T1.LV3_CN_NAME AS PARENT_CN_NAME
         , T1.CODE_TYPE
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
	     , T1.SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_PBI_LV4_COST_INFO_TMP3 T1  -- LV3.5期间（T-1年+T年YTD）临时表
      LEFT JOIN REPL_PBI_LV3_COST_INFO_TMP3 T2  -- LV3期间（T-1年+T年YTD）
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.LV3_CODE = T2.LV3_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK '         -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ;
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'LV3.5层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 【PBI维度LV3层级成本指数】
  V_SQL := '
    INSERT INTO REPL_PBI_LV3_COST_INFO_TMP(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , BASE_PERIOD_ID                  -- 默认基期
         , PERIOD_YEAR                     -- 会计年
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , LV3_CODE                        -- 重量级团队LV3编码
         , LV3_CN_NAME                     -- 重量级团队LV3名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
         , COST_INDEX                      -- 成本指数
    )
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.PERIOD_ID                       -- 会计期
         , T1.BASE_PERIOD_ID                  -- 默认基期
         , T1.PERIOD_YEAR                     -- 会计年
         , T1.LV0_CODE                        -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                     -- 重量级团队LV0名称
         , T1.LV1_CODE                        -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                     -- 重量级团队LV1名称
         , T1.LV2_CODE                        -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                     -- 重量级团队LV2名称
         , T1.LV3_CODE                        -- 重量级团队LV3编码
         , T1.LV3_CN_NAME                     -- 重量级团队LV3名称
         , T1.REGION_CODE                     -- 地区部编码
         , T1.REGION_CN_NAME                  -- 地区部中文名称
         , T1.REPOFFICE_CODE                  -- 代表处编码
         , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
         , T1.BG_CODE                         -- BG编码
         , T1.BG_CN_NAME                      -- BG中文名称
         , T1.OVERSEA_FLAG                    -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , ''LV3'' AS GROUP_LEVEL               -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , SUM(T1.PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
         , ROUND(SUM(T1.COST_INDEX*T2.WEIGHT_RATE),10) AS COST_INDEX  -- 成本指数
      FROM REPL_PBI_LV4_COST_INFO_TMP T1  -- LV4层级成本指数临时表
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2   -- LV4层级权重临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV3_CODE = T2.PARENT_CODE
       AND T1.LV4_CODE = T2.GROUP_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
       WHERE T2.VERSION_ID = '||V_VERSION_ID||'
       AND T2.GROUP_LEVEL = ''LV4''  -- 取GROUP_LEVEL为LV4的版本数据
     GROUP BY T1.VERSION_ID
         , T1.PERIOD_ID
         , T1.BASE_PERIOD_ID
         , T1.PERIOD_YEAR
         , T1.LV0_CODE
         , T1.LV0_CN_NAME
         , T1.LV1_CODE
         , T1.LV1_CN_NAME
         , T1.LV2_CODE
         , T1.LV2_CN_NAME
         , T1.LV3_CODE
         , T1.LV3_CN_NAME
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG
         , T1.CODE_TYPE'
    ;
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'PBI维度新或老编码LV3层级数据入到临时表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- LV2期间（T-1年+T年YTD）
  DROP TABLE IF EXISTS REPL_PBI_LV2_COST_INFO_TMP3;
  CREATE TEMPORARY TABLE REPL_PBI_LV2_COST_INFO_TMP3 AS
    SELECT VERSION_ID                      -- 版本ID
         , BASE_PERIOD_ID                  -- 基期
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
      FROM REPL_PBI_LV3_COST_INFO_TMP3  -- LV3期间（T-1年+T年YTD）临时表
     GROUP BY VERSION_ID
         , BASE_PERIOD_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , LV2_CODE
         , LV2_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , CODE_TYPE
    ;

    -- LV3层级权重=LV3层级新老编码期间（T-1年+T年YTD）的发货量/LV2层级新老编码期间（T-1年+T年YTD）的发货量
  V_SQL := '
    INSERT INTO '||V_TO_WEIGHT_TABLE||'(
           VERSION_ID               /*版本ID                                   */
         , BASE_PERIOD_ID           /*基期                                     */
         '||V_SQL_TO_PBI_PART||'
         , GROUP_CODE               /*各层级编码                               */
         , GROUP_CN_NAME            /*各层级中文名称                           */
         , GROUP_LEVEL              /*各层级（SPART/LV4/LV3/LV2/LV1/LV0）      */
         , WEIGHT_RATE              /*权重值                                   */
         , PARENT_CODE              /*父级编码                                 */
         , PARENT_CN_NAME           /*父级中文名称                             */
         , CODE_TYPE                /*编码类型（NEW:新编码  OLD: 旧编码 ）     */
         , REGION_CODE              /*地区部编码                               */
         , REGION_CN_NAME           /*地区部中文名称                           */
         , REPOFFICE_CODE           /*代表处编码                               */
         , REPOFFICE_CN_NAME        /*代表处中文名称                           */
         , BG_CODE                  /*BG编码                                   */
         , BG_CN_NAME               /*BG中文名称                               */
         , OVERSEA_FLAG             /*国内海外标识                             */
		 , SOFTWARE_MARK
         , CREATED_BY               /*创建人                                   */
         , CREATION_DATE            /*创建时间                                 */
         , LAST_UPDATED_BY          /*修改人                                   */
         , LAST_UPDATE_DATE         /*修改时间                                 */
         , DEL_FLAG                 /*删除标识(未删除：N，已删除：Y)           */
    )
    SELECT T1.VERSION_ID
         , T1.BASE_PERIOD_ID
         , T1.LV3_CODE
         , T1.LV3_CN_NAME
         , T1.LV3_CODE    AS GROUP_CODE
         , T1.LV3_CN_NAME AS GROUP_CN_NAME
         , ''LV3'' AS GROUP_LEVEL
         , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- LV3层级权重
         , T1.LV2_CODE AS PARENT_CODE
         , T1.LV2_CN_NAME AS PARENT_CN_NAME
         , T1.CODE_TYPE
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
	     , T1.SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_PBI_LV3_COST_INFO_TMP3 T1  -- LV3期间（T-1年+T年YTD）临时表
      LEFT JOIN REPL_PBI_LV2_COST_INFO_TMP3 T2  -- LV2期间（T-1年+T年YTD）
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK '         -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ;
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'LV3层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 【PBI维度LV2层级成本指数】
  V_SQL := '
    INSERT INTO REPL_PBI_LV2_COST_INFO_TMP(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , BASE_PERIOD_ID                  -- 默认基期
         , PERIOD_YEAR                     -- 会计年
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , LV2_CODE                        -- 重量级团队LV2编码
         , LV2_CN_NAME                     -- 重量级团队LV2名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
         , COST_INDEX                      -- 成本指数
    )
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.PERIOD_ID                       -- 会计期
         , T1.BASE_PERIOD_ID                  -- 默认基期
         , T1.PERIOD_YEAR                     -- 会计年
         , T1.LV0_CODE                        -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                     -- 重量级团队LV0名称
         , T1.LV1_CODE                        -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                     -- 重量级团队LV1名称
         , T1.LV2_CODE                        -- 重量级团队LV2编码
         , T1.LV2_CN_NAME                     -- 重量级团队LV2名称
         , T1.REGION_CODE                     -- 地区部编码
         , T1.REGION_CN_NAME                  -- 地区部中文名称
         , T1.REPOFFICE_CODE                  -- 代表处编码
         , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
         , T1.BG_CODE                         -- BG编码
         , T1.BG_CN_NAME                      -- BG中文名称
         , T1.OVERSEA_FLAG                    -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , ''LV2'' AS GROUP_LEVEL               -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , SUM(T1.PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
         , ROUND(SUM(T1.COST_INDEX*T2.WEIGHT_RATE),10) AS COST_INDEX  -- 成本指数
      FROM REPL_PBI_LV3_COST_INFO_TMP T1  -- LV3层级成本指数临时表
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2  -- LV3层级权重临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV2_CODE = T2.PARENT_CODE
       AND T1.LV3_CODE = T2.GROUP_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
       WHERE T2.VERSION_ID = '||V_VERSION_ID||'
       AND T2.GROUP_LEVEL = ''LV3''  -- 取GROUP_LEVEL为LV3的版本数据
     GROUP BY T1.VERSION_ID
         , T1.PERIOD_ID
         , T1.BASE_PERIOD_ID
         , T1.PERIOD_YEAR
         , T1.LV0_CODE
         , T1.LV0_CN_NAME
         , T1.LV1_CODE
         , T1.LV1_CN_NAME
         , T1.LV2_CODE
         , T1.LV2_CN_NAME
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG
         , T1.CODE_TYPE
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'PBI维度新或老编码LV2层级数据入到临时表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- LV1期间（T-1年+T年YTD）
  DROP TABLE IF EXISTS REPL_PBI_LV1_COST_INFO_TMP3;
  CREATE TEMPORARY TABLE REPL_PBI_LV1_COST_INFO_TMP3 AS
    SELECT VERSION_ID                      -- 版本ID
         , BASE_PERIOD_ID                  -- 基期
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
      FROM REPL_PBI_LV2_COST_INFO_TMP3  -- LV2期间（T-1年+T年YTD）临时表
     GROUP BY VERSION_ID
         , BASE_PERIOD_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , CODE_TYPE
    ;

    RAISE NOTICE'REPL_PBI_LV1_COST_INFO_TMP3';

    -- LV2层级权重=LV2层级新老编码期间（T-1年+T年YTD）的发货量/LV1层级新老编码期间（T-1年+T年YTD）的发货量
  V_SQL := '
    INSERT INTO '||V_TO_WEIGHT_TABLE||'(
           VERSION_ID               /*版本ID                                   */
         , BASE_PERIOD_ID           /*基期                                     */
         '||V_SQL_TO_PBI_PART||'
         , GROUP_CODE               /*各层级编码                               */
         , GROUP_CN_NAME            /*各层级中文名称                           */
         , GROUP_LEVEL              /*各层级（SPART/LV4/LV3/LV2/LV1/LV0）      */
         , WEIGHT_RATE              /*权重值                                   */
         , PARENT_CODE              /*父级编码                                 */
         , PARENT_CN_NAME           /*父级中文名称                             */
         , CODE_TYPE                /*编码类型（NEW:新编码  OLD: 旧编码 ）     */
         , REGION_CODE              /*地区部编码                               */
         , REGION_CN_NAME           /*地区部中文名称                           */
         , REPOFFICE_CODE           /*代表处编码                               */
         , REPOFFICE_CN_NAME        /*代表处中文名称                           */
         , BG_CODE                  /*BG编码                                   */
         , BG_CN_NAME               /*BG中文名称                               */
         , OVERSEA_FLAG             /*国内海外标识                             */
		 , SOFTWARE_MARK
         , CREATED_BY               /*创建人                                   */
         , CREATION_DATE            /*创建时间                                 */
         , LAST_UPDATED_BY          /*修改人                                   */
         , LAST_UPDATE_DATE         /*修改时间                                 */
         , DEL_FLAG                 /*删除标识(未删除：N，已删除：Y)           */
    )
    SELECT T1.VERSION_ID
         , T1.BASE_PERIOD_ID
         , T1.LV2_CODE
         , T1.LV2_CN_NAME
         , T1.LV2_CODE AS GROUP_CODE
         , T1.LV2_CN_NAME AS GROUP_CN_NAME
         , ''LV2'' AS GROUP_LEVEL
         , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- LV2层级权重
         , T1.LV1_CODE AS PARENT_CODE
         , T1.LV1_CN_NAME AS PARENT_CN_NAME
         , T1.CODE_TYPE
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
	     , T1.SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_PBI_LV2_COST_INFO_TMP3 T1  -- LV2期间（T-1年+T年YTD）临时表
      LEFT JOIN REPL_PBI_LV1_COST_INFO_TMP3 T2  -- LV1期间（T-1年+T年YTD）
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK  '        -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ;
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'LV2层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    RAISE NOTICE'LV2层级权重';

    -- 【PBI维度LV1层级成本指数】
  V_SQL := '
    INSERT INTO REPL_PBI_LV1_COST_INFO_TMP(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , BASE_PERIOD_ID                  -- 默认基期
         , PERIOD_YEAR                     -- 会计年
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , LV1_CODE                        -- 重量级团队LV1编码
         , LV1_CN_NAME                     -- 重量级团队LV1名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
         , COST_INDEX                      -- 成本指数
    )
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.PERIOD_ID                       -- 会计期
         , T1.BASE_PERIOD_ID                  -- 默认基期
         , T1.PERIOD_YEAR                     -- 会计年
         , T1.LV0_CODE                        -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                     -- 重量级团队LV0名称
         , T1.LV1_CODE                        -- 重量级团队LV1编码
         , T1.LV1_CN_NAME                     -- 重量级团队LV1名称
         , T1.REGION_CODE                     -- 地区部编码
         , T1.REGION_CN_NAME                  -- 地区部中文名称
         , T1.REPOFFICE_CODE                  -- 代表处编码
         , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
         , T1.BG_CODE                         -- BG编码
         , T1.BG_CN_NAME                      -- BG中文名称
         , T1.OVERSEA_FLAG                    -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , ''LV1'' AS GROUP_LEVEL               -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , SUM(T1.PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
         , ROUND(SUM(T1.COST_INDEX*T2.WEIGHT_RATE),10) AS COST_INDEX  -- 成本指数
      FROM REPL_PBI_LV2_COST_INFO_TMP T1  -- LV2层级成本指数临时表
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2  -- LV2层级权重临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV2_CODE = T2.GROUP_CODE
       AND T1.LV1_CODE = T2.PARENT_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
       WHERE T2.VERSION_ID = '||V_VERSION_ID||'
       AND T2.GROUP_LEVEL = ''LV2''  -- 取GROUP_LEVEL为LV4的版本数据
     GROUP BY T1.VERSION_ID
         , T1.PERIOD_ID
         , T1.BASE_PERIOD_ID
         , T1.PERIOD_YEAR
         , T1.LV0_CODE
         , T1.LV0_CN_NAME
         , T1.LV1_CODE
         , T1.LV1_CN_NAME
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG
         , T1.CODE_TYPE
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'PBI维度新或老编码LV1层级数据入到临时表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- LV0期间（T-1年+T年YTD）
  DROP TABLE IF EXISTS REPL_PBI_LV0_COST_INFO_TMP3;
  CREATE TEMPORARY TABLE REPL_PBI_LV0_COST_INFO_TMP3 AS
    SELECT VERSION_ID                      -- 版本ID
         , BASE_PERIOD_ID                  -- 基期
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , SUM(PROD_QTY)     AS PROD_QTY      -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(RMB_COST_AMT) AS RMB_COST_AMT  -- 标准成本
      FROM REPL_PBI_LV1_COST_INFO_TMP3  -- LV1期间（T-1年+T年YTD）临时表
     GROUP BY VERSION_ID
         , BASE_PERIOD_ID
         , LV0_CODE
         , LV0_CN_NAME
         , LV1_CODE
         , LV1_CN_NAME
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , VIEW_FLAG
         , CODE_TYPE
    ;

    -- LV1层级权重=LV1层级新老编码期间（T-1年+T年YTD）的发货量/LV0层级新老编码期间（T-1年+T年YTD）的发货量
  V_SQL := '
    INSERT INTO '||V_TO_WEIGHT_TABLE||'(
           VERSION_ID               /*版本ID                                   */
         , BASE_PERIOD_ID           /*基期                                     */
         '||V_SQL_TO_PBI_PART||'
         , GROUP_CODE               /*各层级编码                               */
         , GROUP_CN_NAME            /*各层级中文名称                           */
         , GROUP_LEVEL              /*各层级（SPART/LV4/LV3/LV2/LV1/LV0）      */
         , WEIGHT_RATE              /*权重值                                   */
         , PARENT_CODE              /*父级编码                                 */
         , PARENT_CN_NAME           /*父级中文名称                             */
         , CODE_TYPE                /*编码类型（NEW:新编码  OLD: 旧编码 ）     */
         , REGION_CODE              /*地区部编码                               */
         , REGION_CN_NAME           /*地区部中文名称                           */
         , REPOFFICE_CODE           /*代表处编码                               */
         , REPOFFICE_CN_NAME        /*代表处中文名称                           */
         , BG_CODE                  /*BG编码                                   */
         , BG_CN_NAME               /*BG中文名称                               */
         , OVERSEA_FLAG             /*国内海外标识                             */
		 , SOFTWARE_MARK
         , CREATED_BY               /*创建人                                   */
         , CREATION_DATE            /*创建时间                                 */
         , LAST_UPDATED_BY          /*修改人                                   */
         , LAST_UPDATE_DATE         /*修改时间                                 */
         , DEL_FLAG                 /*删除标识(未删除：N，已删除：Y)           */
    )
    SELECT T1.VERSION_ID
         , T1.BASE_PERIOD_ID
         , T1.LV1_CODE
         , T1.LV1_CN_NAME
         , T1.LV1_CODE AS GROUP_CODE
         , T1.LV1_CN_NAME AS GROUP_CN_NAME
         , ''LV1'' AS GROUP_LEVEL
         , (CASE WHEN T2.RMB_COST_AMT = 0 THEN 0 ELSE ROUND(T1.RMB_COST_AMT/T2.RMB_COST_AMT,10) END) AS WEIGHT_RATE  -- LV1层级权重
         , T1.LV0_CODE AS PARENT_CODE
         , T1.LV0_CN_NAME AS PARENT_CN_NAME
         , T1.CODE_TYPE
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
	     , T1.SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_PBI_LV1_COST_INFO_TMP3 T1  -- LV1期间（T-1年+T年YTD）临时表
      LEFT JOIN REPL_PBI_LV0_COST_INFO_TMP3 T2  -- LV0期间（T-1年+T年YTD）
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV0_CODE = T2.LV0_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'LV1层级权重数据入到结果表：'||V_TO_WEIGHT_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 【PBI维度LV0层级成本指数】
  V_SQL := '
    INSERT INTO REPL_PBI_LV0_COST_INFO_TMP(
           VERSION_ID                      -- 版本ID
         , PERIOD_ID                       -- 会计期
         , BASE_PERIOD_ID                  -- 默认基期
         , PERIOD_YEAR                     -- 会计年
         , LV0_CODE                        -- 重量级团队LV0编码
         , LV0_CN_NAME                     -- 重量级团队LV0名称
         , REGION_CODE                     -- 地区部编码
         , REGION_CN_NAME                  -- 地区部中文名称
         , REPOFFICE_CODE                  -- 代表处编码
         , REPOFFICE_CN_NAME               -- 代表处中文名称
         , BG_CODE                         -- BG编码
         , BG_CN_NAME                      -- BG中文名称
         , OVERSEA_FLAG                    -- 国内海外标识
		 , SOFTWARE_MARK
         , VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , GROUP_LEVEL                     -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , PROD_QTY                        -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , RMB_COST_AMT                    -- 标准成本
         , COST_INDEX                      -- 成本指数
    )
    SELECT T1.VERSION_ID                      -- 版本ID
         , T1.PERIOD_ID                       -- 会计期
         , T1.BASE_PERIOD_ID                  -- 默认基期
         , T1.PERIOD_YEAR                     -- 会计年
         , T1.LV0_CODE                        -- 重量级团队LV0编码
         , T1.LV0_CN_NAME                     -- 重量级团队LV0名称
         , T1.REGION_CODE                     -- 地区部编码
         , T1.REGION_CN_NAME                  -- 地区部中文名称
         , T1.REPOFFICE_CODE                  -- 代表处编码
         , T1.REPOFFICE_CN_NAME               -- 代表处中文名称
         , T1.BG_CODE                         -- BG编码
         , T1.BG_CN_NAME                      -- BG中文名称
         , T1.OVERSEA_FLAG                    -- 国内海外标识
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG                       -- 路径1：PROD_SPART/路径2：DIMENSION
         , T1.CODE_TYPE                       -- 编码类型（NEW:新编码  OLD: 旧编码 ）
         , ''LV0'' AS GROUP_LEVEL               -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
         , SUM(T1.PROD_QTY)     AS PROD_QTY     -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
         , SUM(T1.RMB_COST_AMT) AS RMB_COST_AMT -- 标准成本
         , ROUND(SUM(T1.COST_INDEX*T2.WEIGHT_RATE),10) AS COST_INDEX  -- 成本指数
      FROM REPL_PBI_LV1_COST_INFO_TMP T1  -- LV1层级成本指数临时表
      LEFT JOIN '||V_TO_WEIGHT_TABLE||' T2  -- LV1层级权重临时表
        ON T1.VERSION_ID = T2.VERSION_ID
       AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID
       AND T1.LV0_CODE = T2.PARENT_CODE
       AND T1.LV1_CODE = T2.GROUP_CODE
       AND T1.REGION_CODE = T2.REGION_CODE
       AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
       AND T1.CODE_TYPE = T2.CODE_TYPE
	   AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK          -- 202410版本新增,只取对应软硬件标识的数据进行计算
       WHERE T2.VERSION_ID = '||V_VERSION_ID||'
       AND T2.GROUP_LEVEL = ''LV1''  -- 取GROUP_LEVEL为LV1的版本数据
     GROUP BY T1.VERSION_ID
         , T1.PERIOD_ID
         , T1.BASE_PERIOD_ID
         , T1.PERIOD_YEAR
         , T1.LV0_CODE
         , T1.LV0_CN_NAME
         , T1.REGION_CODE
         , T1.REGION_CN_NAME
         , T1.REPOFFICE_CODE
         , T1.REPOFFICE_CN_NAME
         , T1.BG_CODE
         , T1.BG_CN_NAME
         , T1.OVERSEA_FLAG
		 , T1.SOFTWARE_MARK
         , T1.VIEW_FLAG
         , T1.CODE_TYPE
    ';
    EXECUTE IMMEDIATE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => 'PBI维度新或老编码LV0层级数据入到临时表，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 所有数据先入到临时表
    INSERT INTO REPL_COST_INFO_TMP(
         VERSION_ID                    -- 版本ID
       , PERIOD_YEAR                   -- 会计年
       , PERIOD_ID                     -- 会计期
       , BASE_PERIOD_ID                -- 默认基期
       , PBI_DIM_CODE                  -- PBI层级编码
       , PBI_DIM_CN_NAME               -- PBI层级名称
       , GROUP_CODE                    -- 各层级编码
       , GROUP_CN_NAME                 -- 各层级中文名称
       , GROUP_LEVEL                   -- 各层级（SPART/LV4/LV3/LV2/LV1/LV0）
       , COST_INDEX                    -- 成本指数
       , SHIPMENT_QTY                  -- 业务量，路径2源：PROD_UNIT_QTY，路径1源：PART_QTY
       , RMB_COST_AMT                  -- 标准成本
       , PARENT_CODE                   -- 父级编码
       , PARENT_CN_NAME                -- 父级中文名称
       , REPLACE_RELATION_NAME         -- 替换关系名称
       , REPLACE_RELATION_TYPE         -- 新老编码替换类型（一对一  、一对多 、多对多）
       , RELATION_TYPE                 -- 关系（ 替换 、收编）
       , CODE_TYPE                     -- 编码类型（NEW:新编码  OLD: 旧编码 ）
       , REGION_CODE                   -- 地区部编码
       , REGION_CN_NAME                -- 地区部中文名称
       , REPOFFICE_CODE                -- 代表处编码
       , REPOFFICE_CN_NAME             -- 代表处中文名称
       , BG_CODE                       -- BG编码
       , BG_CN_NAME                    -- BG中文名称
       , OVERSEA_FLAG                  -- 国内海外标识
	   , SOFTWARE_MARK
  )
    /*SPART层级*/
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , LV4_CODE
          , LV4_CN_NAME
          , SPART_CODE AS GROUP_CODE
          , SPART_DESC AS GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PROD_QTY AS SHIPMENT_QTY
          , RMB_COST_AMT
          , LV4_CODE    AS PARENT_CODE
          , LV4_CN_NAME AS PARENT_CN_NAME
          , REPLACE_RELATION_NAME
          , REPLACE_RELATION_TYPE
          , RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
       FROM REPL_SPART_COST_INFO_ALL_TMP
      UNION ALL
     /*LV4层级*/
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , LV4_CODE
          , LV4_CN_NAME
          , LV4_CODE    AS GROUP_CODE
          , LV4_CN_NAME AS GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PROD_QTY AS SHIPMENT_QTY
          , RMB_COST_AMT
          , LV3_CODE    AS PARENT_CODE
          , LV3_CN_NAME AS PARENT_CN_NAME
          , '' AS REPLACE_RELATION_NAME
          , '' AS REPLACE_RELATION_TYPE
          , '' AS RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
       FROM REPL_PBI_LV4_COST_INFO_TMP
      UNION ALL
     /*LV3层级*/
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , LV3_CODE
          , LV3_CN_NAME
          , LV3_CODE    AS GROUP_CODE
          , LV3_CN_NAME AS GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PROD_QTY AS SHIPMENT_QTY
          , RMB_COST_AMT
          , LV2_CODE    AS PARENT_CODE
          , LV2_CN_NAME AS PARENT_CN_NAME
          , '' AS REPLACE_RELATION_NAME
          , '' AS REPLACE_RELATION_TYPE
          , '' AS RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
       FROM REPL_PBI_LV3_COST_INFO_TMP
      UNION ALL
     /*LV2层级*/
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , LV2_CODE
          , LV2_CN_NAME
          , LV2_CODE    AS GROUP_CODE
          , LV2_CN_NAME AS GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PROD_QTY AS SHIPMENT_QTY
          , RMB_COST_AMT
          , LV1_CODE    AS PARENT_CODE
          , LV1_CN_NAME AS PARENT_CN_NAME
          , '' AS REPLACE_RELATION_NAME
          , '' AS REPLACE_RELATION_TYPE
          , '' AS RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
       FROM REPL_PBI_LV2_COST_INFO_TMP
      UNION ALL
     /*LV1层级*/
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , LV1_CODE
          , LV1_CN_NAME
          , LV1_CODE    AS GROUP_CODE
          , LV1_CN_NAME AS GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PROD_QTY AS SHIPMENT_QTY
          , RMB_COST_AMT
          , LV0_CODE    AS PARENT_CODE
          , LV0_CN_NAME AS PARENT_CN_NAME
          , '' AS REPLACE_RELATION_NAME
          , '' AS REPLACE_RELATION_TYPE
          , '' AS RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
       FROM REPL_PBI_LV1_COST_INFO_TMP
      UNION ALL
     /*LV0层级*/
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , LV0_CODE
          , LV0_CN_NAME
          , LV0_CODE    AS GROUP_CODE
          , LV0_CN_NAME AS GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PROD_QTY AS SHIPMENT_QTY
          , RMB_COST_AMT
          , '' AS PARENT_CODE
          , '' AS PARENT_CN_NAME
          , '' AS REPLACE_RELATION_NAME
          , '' AS REPLACE_RELATION_TYPE
          , '' AS RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
       FROM REPL_PBI_LV0_COST_INFO_TMP
    ;

  V_SQL := 'DELETE FROM '||V_TO_INDEX_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;  -- 删除版本数据
  EXECUTE V_SQL;
  
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
      F_SP_NAME => V_SP_NAME,    --SP名称
      F_STEP_NUM => V_STEP_NUM,
      F_CAL_LOG_DESC => '删除月度成本指数 '||V_TO_INDEX_TABLE||' 表 '||V_VERSION_ID||' 版本的数据',--日志描述
      F_FORMULA_SQL_TXT  => '非动态SQL',
      F_DML_ROW_COUNT => SQL%ROWCOUNT,
      F_RESULT_STATUS => X_RESULT_STATUS,
      F_ERRBUF => 'SUCCESS'
    );

    -- 【SPART层级、PBI层级新老编码数据入到成本指数目标表】
    V_SQL := 'INSERT INTO '||V_TO_INDEX_TABLE||'(
              VERSION_ID
            , PERIOD_YEAR
            , PERIOD_ID
            , BASE_PERIOD_ID
            '||V_SQL_TO_PBI_PART||'
            , GROUP_CODE
            , GROUP_CN_NAME
            , GROUP_LEVEL
            , COST_INDEX
            , PARENT_CODE
            , PARENT_CN_NAME
            , REPLACE_RELATION_NAME
            , REPLACE_RELATION_TYPE
            , RELATION_TYPE
            , CODE_TYPE
            , REGION_CODE
            , REGION_CN_NAME
            , REPOFFICE_CODE
            , REPOFFICE_CN_NAME
            , BG_CODE
            , BG_CN_NAME
            , OVERSEA_FLAG
			, SOFTWARE_MARK
            , CREATED_BY
            , CREATION_DATE
            , LAST_UPDATED_BY
            , LAST_UPDATE_DATE
            , DEL_FLAG
     )
     SELECT VERSION_ID
          , PERIOD_YEAR
          , PERIOD_ID
          , BASE_PERIOD_ID
          , PBI_DIM_CODE
          , PBI_DIM_CN_NAME
          , GROUP_CODE
          , GROUP_CN_NAME
          , GROUP_LEVEL
          , COST_INDEX
          , PARENT_CODE
          , PARENT_CN_NAME
          , REPLACE_RELATION_NAME
          , REPLACE_RELATION_TYPE
          , RELATION_TYPE
          , CODE_TYPE
          , REGION_CODE
          , REGION_CN_NAME
          , REPOFFICE_CODE
          , REPOFFICE_CN_NAME
          , BG_CODE
          , BG_CN_NAME
          , OVERSEA_FLAG
		  , SOFTWARE_MARK
          , -1 AS CREATED_BY
          , CURRENT_TIMESTAMP AS CREATION_DATE
          , -1 AS LAST_UPDATED_BY
          , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
          , ''N'' AS DEL_FLAG
       FROM REPL_COST_INFO_TMP
    ';
    EXECUTE V_SQL;
    RAISE NOTICE '数据入到成本指数表';

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '数据入到月度成本指数目标表 '||V_TO_INDEX_TABLE||'，数据量：'||SQL%ROWCOUNT,--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    V_SQL := 'DELETE FROM '||V_TO_QTY_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;  -- 删除版本数据
    EXECUTE V_SQL;

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '删除月度成本偏差 '||V_TO_QTY_TABLE||' 表 '||V_VERSION_ID||' 版本的数据',--日志描述
        F_FORMULA_SQL_TXT  => '非动态SQL',
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

    -- 数据入到成本偏差信息表
    V_SQL := 'INSERT INTO '||V_TO_QTY_TABLE||'(
           VERSION_ID                  /* 版本ID                                          */
         , PERIOD_YEAR                 /* 会计年                                          */
         , PERIOD_ID                   /* 会计月                                          */
         , BASE_PERIOD_ID              /* 基期                                            */
         '||V_SQL_TO_PBI_PART||'
         , GROUP_CODE                  /* 各层级编码                                      */
         , GROUP_CN_NAME               /* 各层级中文名称                                  */
         , GROUP_LEVEL                 /* 各层级（TOP-SPART/SPART/LV4/LV3/LV2/LV1/LV0）   */
         , SHIPMENT_QTY                /* 发货量                                          */
         , RMB_COST_AMT                /* 标准成本                                        */
         , PARENT_CODE                 /* 父级编码                                        */
         , PARENT_CN_NAME              /* 父级中文名称                                    */
         , REPLACE_RELATION_NAME       /* 替换关系名称                                    */
         , REPLACE_RELATION_TYPE       /* 替换关系类型（一对一 、一对多 、多对多）        */
         , RELATION_TYPE               /* 关系（替换、收编）                              */
         , CODE_TYPE                   /* 编码类型（NEW:新编码  OLD: 旧编码  CV:成本偏差）*/
         , REGION_CODE                 /* 地区部编码                                      */
         , REGION_CN_NAME              /* 地区部中文名称                                  */
         , REPOFFICE_CODE              /* 代表处编码                                      */
         , REPOFFICE_CN_NAME           /* 代表处中文名称                                  */
         , BG_CODE                     /* BG编码                                          */
         , BG_CN_NAME                  /* BG中文名称                                      */
         , OVERSEA_FLAG                /* 国内海外标识                                    */
		 , SOFTWARE_MARK
         , CREATED_BY                  /* 创建人                                          */
         , CREATION_DATE               /* 创建时间                                        */
         , LAST_UPDATED_BY             /* 修改人                                          */
         , LAST_UPDATE_DATE            /* 修改时间                                        */
         , DEL_FLAG                    /* 删除标识(未删除：N，已删除：Y)                  */
    )
    /* TOP-SPART层级：根据选定的SPART实时计算成本偏差*/
    SELECT VERSION_ID
         , PERIOD_YEAR
         , PERIOD_ID
         , '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID
         , LV4_CODE
         , LV4_CN_NAME
         , SPART_CODE AS GROUP_CODE
         , '''' AS GROUP_CN_NAME
         , ''TOP-SPART'' AS GROUP_LEVEL
         , PROD_QTY AS SHIPMENT_QTY
         '||V_GS_ENCRYPT_COST_AMT||'
         , LV4_CODE    AS PARENT_CODE
         , LV4_CN_NAME AS PARENT_CN_NAME
         , '''' AS REPLACE_RELATION_NAME
         , '''' AS REPLACE_RELATION_TYPE
         , '''' AS RELATION_TYPE
         , '''' AS CODE_TYPE
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM MON_REPL_COST_INFO_TMP1
     UNION ALL
    /* SPART层级*/
    SELECT VERSION_ID
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE
         , GROUP_CN_NAME
         , GROUP_LEVEL
         , SHIPMENT_QTY
         '||V_GS_ENCRYPT_COST_AMT||'
         , PARENT_CODE
         , PARENT_CN_NAME
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_COST_INFO_TMP  /*SPART层级成本指数临时表*/
     WHERE GROUP_LEVEL = ''SPART''
     UNION ALL
     /*PBI维度层级*/
    SELECT VERSION_ID
         , PERIOD_YEAR
         , PERIOD_ID
         , BASE_PERIOD_ID
         , PBI_DIM_CODE
         , PBI_DIM_CN_NAME
         , GROUP_CODE
         , GROUP_CN_NAME
         , GROUP_LEVEL
         , SHIPMENT_QTY
         , RMB_COST_AMT'||V_AMT_TYPE||'
         , PARENT_CODE
         , PARENT_CN_NAME
         , REPLACE_RELATION_NAME
         , REPLACE_RELATION_TYPE
         , RELATION_TYPE
         , CODE_TYPE
         , REGION_CODE
         , REGION_CN_NAME
         , REPOFFICE_CODE
         , REPOFFICE_CN_NAME
         , BG_CODE
         , BG_CN_NAME
         , OVERSEA_FLAG
		 , SOFTWARE_MARK
         , -1 AS CREATED_BY
         , CURRENT_TIMESTAMP AS CREATION_DATE
         , -1 AS LAST_UPDATED_BY
         , CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
         , ''N'' AS DEL_FLAG
      FROM REPL_COST_INFO_TMP
     WHERE GROUP_LEVEL <> ''SPART''
    ';

    EXECUTE V_SQL;
    RAISE NOTICE '数据入到成本偏差表';

    V_STEP_NUM := V_STEP_NUM+1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
        F_SP_NAME => V_SP_NAME,    --SP名称
        F_STEP_NUM => V_STEP_NUM,
        F_CAL_LOG_DESC => '数据入到月度成本偏差目标表 '||V_TO_QTY_TABLE||'，数据量：'||SQL%ROWCOUNT||'，运行结束！',--日志描述
        F_FORMULA_SQL_TXT  => V_SQL,
        F_DML_ROW_COUNT => SQL%ROWCOUNT,
        F_RESULT_STATUS => X_RESULT_STATUS,
        F_ERRBUF => 'SUCCESS'
    );

  --收集统计信息
	V_SQL := 'ANALYSE '||V_TO_INDEX_TABLE;
	EXECUTE V_SQL;

	V_SQL := 'ANALYSE '||V_TO_QTY_TABLE;
	EXECUTE V_SQL;
	
	V_SQL := 'ANALYSE '||V_TO_WEIGHT_TABLE;
	EXECUTE V_SQL;

  EXCEPTION
  	WHEN OTHERS THEN

      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(
         F_SP_NAME => V_SP_NAME,    -- SP名称
         F_STEP_NUM => V_STEP_NUM,
         F_CAL_LOG_DESC => V_SP_NAME||'：运行错误',-- 日志描述
         F_FORMULA_SQL_TXT  => V_SQL,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_RESULT_STATUS => '0',
         F_ERRBUF => SQLSTATE  -- 错误编码
      ) ;

      X_RESULT_STATUS := 'FAILED';

END

$$
/

