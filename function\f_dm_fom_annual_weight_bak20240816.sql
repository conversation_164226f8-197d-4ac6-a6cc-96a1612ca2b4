-- Name: f_dm_fom_annual_weight_bak20240816; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_annual_weight_bak20240816(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-12-08
  创建人  ：唐钦
  修改：2024年3月7日
  背景描述：分视角权重表(年度分析-一览表)
  参数描述：x_result_status ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_ANNUAL_WEIGHT()
*/
DECLARE
  V_SP_NAME    VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_ANNUAL_WEIGHT'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_KEYSTR VARCHAR(50) := F_KEYSTR;
  V_SQL TEXT;
  V_FROM_TABLE VARCHAR(500);   -- 来源表
  V_IN_AMT VARCHAR(500);   -- 金额字段
  
BEGIN
  X_RESULT_STATUS = '1';
 -- 取版本号
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION_ID
        FROM DM_FOM_ITEM_EXPS_DTL_T
        ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE  
    V_VERSION_ID := F_VERSION_ID;
   END IF;   
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  --1.清空各层级权重表(年度分析)数据:
  DELETE FROM FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T WHERE VERSION_ID = V_VERSION_ID AND CALIBER_FLAG = F_CALIBER_FLAG;
  
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T表版本号为：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  IF F_CALIBER_FLAG = 'M' THEN 
  -- 如果是自制数据的话，需要建临时表对数据进行解密
   DROP TABLE IF EXISTS MADE_DECRYPT_TMP;
   CREATE TEMPORARY TABLE MADE_DECRYPT_TMP(
      VERSION_ID    bigint,
      PERIOD_YEAR  bigint,
      LV0_CODE    varchar(50),
      LV0_CN_NAME    varchar(200),
      LV1_CODE    varchar(50),
      LV1_CN_NAME    varchar(200),
      BUSSINESS_OBJECT_CODE    varchar(50),
      BUSSINESS_OBJECT_CN_NAME    varchar(200),
      SHIPPING_OBJECT_CODE     varchar(50),
      SHIPPING_OBJECT_CN_NAME    varchar(50),
      MANUFACTURE_OBJECT_CODE     varchar(50),
      MANUFACTURE_OBJECT_CN_NAME    varchar(50),
      ITEM_CODE    varchar(50),
      ITEM_CN_NAME    varchar(1000),
      CALIBER_FLAG  varchar(2),
      RMB_MADE_AMT NUMERIC,
      APPEND_FLAG varchar(2)
   )
   ON COMMIT PRESERVE ROWS
   DISTRIBUTE BY HASH(ITEM_CODE);

  -- 解密数据，并插入临时表
  INSERT INTO MADE_DECRYPT_TMP(
      VERSION_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      ITEM_CODE,
      ITEM_CN_NAME,
      CALIBER_FLAG,
      RMB_MADE_AMT,
      APPEND_FLAG)
   SELECT VERSION_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      BUSSINESS_OBJECT_CODE,
      BUSSINESS_OBJECT_CN_NAME,
      SHIPPING_OBJECT_CODE,
      SHIPPING_OBJECT_CN_NAME,
      MANUFACTURE_OBJECT_CODE,
      MANUFACTURE_OBJECT_CN_NAME,
      ITEM_CODE,
      ITEM_CN_NAME,
      CALIBER_FLAG,
      CAST(GS_DECRYPT(RMB_MADE_AMT, V_KEYSTR, 'AES128', 'CBC', 'SHA256') AS NUMBER) AS RMB_MADE_AMT,  -- 解密
      APPEND_FLAG
    FROM FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T
    WHERE VERSION_ID = V_VERSION_ID
    AND CALIBER_FLAG = 'M'   -- 自制数据
    AND APPEND_FLAG = 'N';   -- 只取实际数据

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM =>  V_STEP_NUM,
   F_CAL_LOG_DESC => '自制数据-加解密数据解密完成，取到版本号：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，并已插入临时表',
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');  
   
  -- 如果是EMS数据的话，直接跳过
  ELSIF F_CALIBER_FLAG = 'E' THEN NULL;
  END IF;
   
  --创建年度发货额临时表
    DROP TABLE IF EXISTS SUM_COST_TMP;
    CREATE TEMPORARY TABLE SUM_COST_TMP (
        PERIOD_YEAR BIGINT,
        GROUP_CODE CHARACTER VARYING(50),
        GROUP_CN_NAME CHARACTER VARYING(2000),
        GROUP_LEVEL CHARACTER VARYING(50),
        RMB_COST_AMT NUMERIC,
        PARENT_CODE VARCHAR(50),
        PARENT_CN_NAME VARCHAR(1000),
        MANUFACTURE_OBJECT_CODE VARCHAR(50),
        MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),
        SHIPPING_OBJECT_CODE VARCHAR(50),
        SHIPPING_OBJECT_CN_NAME VARCHAR(200),
        BUSSINESS_OBJECT_CODE VARCHAR(50),
        BUSSINESS_OBJECT_CN_NAME VARCHAR(200),
        LV1_CODE VARCHAR(50),
        LV1_CN_NAME VARCHAR(200),
        LV0_CODE VARCHAR(50),
        LV0_CN_NAME VARCHAR(200)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE);
    
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '年度发货额临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');     

  IF F_CALIBER_FLAG = 'M' THEN 
     V_FROM_TABLE := 'MADE_DECRYPT_TMP';
     V_IN_AMT := 'RMB_MADE_AMT';
  ELSIF F_CALIBER_FLAG = 'E' THEN 
        V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOM_ANNL_COST_T';
        V_IN_AMT := 'RMB_EMS_AMT';
  END IF;

V_SQL := '
  -- ITEM层级数据插入临时表
  INSERT INTO SUM_COST_TMP(
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV0_CODE,
         LV0_CN_NAME
            )
  SELECT PERIOD_YEAR,
         ITEM_CODE AS GROUP_CODE,
         ITEM_CN_NAME AS GROUP_CN_NAME,
         ''ITEM'' AS GROUP_LEVEL,
         '||V_IN_AMT||' AS RMB_COST_AMT,
         CASE WHEN (MANUFACTURE_OBJECT_CODE IS NULL AND SHIPPING_OBJECT_CODE IS NULL)
              THEN BUSSINESS_OBJECT_CODE
              ELSE MANUFACTURE_OBJECT_CODE END AS PARENT_CODE,
         CASE WHEN (MANUFACTURE_OBJECT_CODE IS NULL AND SHIPPING_OBJECT_CODE IS NULL)
              THEN BUSSINESS_OBJECT_CN_NAME
              ELSE MANUFACTURE_OBJECT_CN_NAME END AS PARENT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV0_CODE,
         LV0_CN_NAME
      FROM '||V_FROM_TABLE||'
      WHERE VERSION_ID = '||V_VERSION_ID||'
      AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''
      AND APPEND_FLAG = ''N''   -- 只取实际数据
--      AND '||V_IN_AMT||' > 0    -- 取大于0的金额数据进行计算（2024/03/07修改）
      ';
      
  EXECUTE IMMEDIATE V_SQL;
               
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '取到版本号：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，ITEM层级数据插入临时表',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');     
      
  -- 其余层级数据插入临时表
    INSERT INTO SUM_COST_TMP(
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV0_CODE,
         LV0_CN_NAME
            )
  WITH LEV_COST_TMP AS(
  -- 制造对象层级
    SELECT PERIOD_YEAR,
           MANUFACTURE_OBJECT_CODE AS GROUP_CODE,
           MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'MANUFACTURE_OBJECT' AS GROUP_LEVEL,
           RMB_COST_AMT,
           SHIPPING_OBJECT_CODE AS PARENT_CODE,
           SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,
           MANUFACTURE_OBJECT_CODE,
           MANUFACTURE_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV0_CODE,
           LV0_CN_NAME
        FROM SUM_COST_TMP
        WHERE (MANUFACTURE_OBJECT_CODE IS NOT NULL AND SHIPPING_OBJECT_CODE IS NOT NULL)  -- 取制造对象/发货对象不为空的数据
    UNION ALL
  -- 发货对象层级
    SELECT PERIOD_YEAR,
           SHIPPING_OBJECT_CODE AS GROUP_CODE,
           SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'SHIPPING_OBJECT' AS GROUP_LEVEL,
           RMB_COST_AMT,
           BUSSINESS_OBJECT_CODE AS PARENT_CODE,
           BUSSINESS_OBJECT_CN_NAME AS PARENT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           SHIPPING_OBJECT_CODE,
           SHIPPING_OBJECT_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV0_CODE,
           LV0_CN_NAME
        FROM SUM_COST_TMP
        WHERE (MANUFACTURE_OBJECT_CODE IS NOT NULL AND SHIPPING_OBJECT_CODE IS NOT NULL)  -- 取制造对象/发货对象不为空的数据
    UNION ALL
  -- 经营对象层级
    SELECT PERIOD_YEAR,
           BUSSINESS_OBJECT_CODE AS GROUP_CODE,
           BUSSINESS_OBJECT_CN_NAME AS GROUP_CN_NAME,
           'BUSSINESS_OBJECT' AS GROUP_LEVEL,
           RMB_COST_AMT,
           LV1_CODE AS PARENT_CODE,
           LV1_CN_NAME AS PARENT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS SHIPPING_OBJECT_CODE,
           NULL AS SHIPPING_OBJECT_CN_NAME,
           BUSSINESS_OBJECT_CODE,
           BUSSINESS_OBJECT_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV0_CODE,
           LV0_CN_NAME
        FROM SUM_COST_TMP
    UNION ALL
  -- LV1层级
    SELECT PERIOD_YEAR,
           LV1_CODE AS GROUP_CODE,
           LV1_CN_NAME AS GROUP_CN_NAME,
           'LV1' AS GROUP_LEVEL,
           RMB_COST_AMT,
           LV0_CODE AS PARENT_CODE,
           LV0_CN_NAME AS PARENT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS SHIPPING_OBJECT_CODE,
           NULL AS SHIPPING_OBJECT_CN_NAME,
           NULL AS BUSSINESS_OBJECT_CODE,
           NULL AS BUSSINESS_OBJECT_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV0_CODE,
           LV0_CN_NAME
        FROM SUM_COST_TMP
    UNION ALL
  -- LV0层级
    SELECT PERIOD_YEAR,
           LV0_CODE AS GROUP_CODE,
           LV0_CN_NAME AS GROUP_CN_NAME,
           'LV0' AS GROUP_LEVEL,
           RMB_COST_AMT,
           LV0_CODE AS PARENT_CODE,
           LV0_CN_NAME AS PARENT_CN_NAME,
           NULL AS MANUFACTURE_OBJECT_CODE,
           NULL AS MANUFACTURE_OBJECT_CN_NAME,
           NULL AS SHIPPING_OBJECT_CODE,
           NULL AS SHIPPING_OBJECT_CN_NAME,
           NULL AS BUSSINESS_OBJECT_CODE,
           NULL AS BUSSINESS_OBJECT_CN_NAME,
           NULL AS LV1_CODE,
           NULL AS LV1_CN_NAME,
           LV0_CODE,
           LV0_CN_NAME
        FROM SUM_COST_TMP
  )
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SUM(RMB_COST_AMT) AS RMB_COST_AMT,
         PARENT_CODE,
         PARENT_CN_NAME,
         MANUFACTURE_OBJECT_CODE,
         MANUFACTURE_OBJECT_CN_NAME,
         SHIPPING_OBJECT_CODE,
         SHIPPING_OBJECT_CN_NAME,
         BUSSINESS_OBJECT_CODE,
         BUSSINESS_OBJECT_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV0_CODE,
         LV0_CN_NAME
      FROM LEV_COST_TMP
      GROUP BY PERIOD_YEAR,
               GROUP_CODE,
               GROUP_CN_NAME,
               GROUP_LEVEL,
               PARENT_CODE,
               PARENT_CN_NAME,
               MANUFACTURE_OBJECT_CODE,
               MANUFACTURE_OBJECT_CN_NAME,
               SHIPPING_OBJECT_CODE,
               SHIPPING_OBJECT_CN_NAME,
               BUSSINESS_OBJECT_CODE,
               BUSSINESS_OBJECT_CN_NAME,
               LV1_CODE,
               LV1_CN_NAME,
               LV0_CODE,
               LV0_CN_NAME;
               
    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '取到版本号：'||V_VERSION_ID||'，且数据口径为：'||F_CALIBER_FLAG||'，其余各层级数据插入临时表',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');   
      
   -- 插入各视角、各层级的权重数据到分视角权重表(年度分析-一览表)
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T(
--                ID,
                VERSION_ID,
                PERIOD_YEAR,
                PERIOD_YEAR_TYPE,
                LV0_CODE,
                LV0_CN_NAME,
                LV1_CODE,
                LV1_CN_NAME,
                BUSSINESS_OBJECT_CODE,
                BUSSINESS_OBJECT_CN_NAME,
                SHIPPING_OBJECT_CODE,
                SHIPPING_OBJECT_CN_NAME,
                MANUFACTURE_OBJECT_CODE,
                MANUFACTURE_OBJECT_CN_NAME,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                WEIGHT_RATE,
                PARENT_CODE,
                PARENT_CN_NAME,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                APPEND_FLAG,
                CALIBER_FLAG
        )                        
        
    -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  WITH PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )    
    -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT DISTINCT T2.PERIOD_YEAR,
              T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
              T1.SHIPPING_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CN_NAME,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CN_NAME,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME
          FROM SUM_COST_TMP T1,PERIOD_YEAR_TMP T2
  )    
    -- 各视角下各层级的权重逻辑计算
       SELECT -- FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_S.NEXTVAL AS ID,
              V_VERSION_ID AS VERSION_ID,
              T1.PERIOD_YEAR,
              T1.PERIOD_YEAR AS PERIOD_YEAR_TYPE,
              T1.LV0_CODE,
              T1.LV0_CN_NAME,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.BUSSINESS_OBJECT_CODE,
              T1.BUSSINESS_OBJECT_CN_NAME,
              T1.SHIPPING_OBJECT_CODE,
              T1.SHIPPING_OBJECT_CN_NAME,
              T1.MANUFACTURE_OBJECT_CODE,
              T1.MANUFACTURE_OBJECT_CN_NAME,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              DECODE(T2.RMB_COST_AMT, NULL, 0, T2.RMB_COST_AMT / NULLIF(T2.PARENT_AMT,0)) AS WEIGHT_RATE,  -- 原始有数据，则计算，维度补齐的情况，则赋0
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              'N' AS DEL_FLAG,
              DECODE(T2.RMB_COST_AMT, NULL, 'Y','N') AS APPEND_FLAG,  --补齐标识：Y为补齐，N为原始
              F_CALIBER_FLAG AS CALIBER_FLAG
          FROM CONTIN_DIM_TMP T1
          LEFT JOIN (
              SELECT SS.PERIOD_YEAR,
                     SS.GROUP_CODE,
                     SS.GROUP_CN_NAME,
                     SS.GROUP_LEVEL,
                     SS.RMB_COST_AMT,
                     CASE
                       WHEN SS.GROUP_LEVEL = 'ITEM' THEN
                        SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.LV0_CODE,SS.LV1_CODE,SS.BUSSINESS_OBJECT_CODE,SS.SHIPPING_OBJECT_CODE,SS.MANUFACTURE_OBJECT_CODE)
                       WHEN SS.GROUP_LEVEL = 'MANUFACTURE_OBJECT' THEN
                        SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.LV0_CODE,SS.LV1_CODE,SS.BUSSINESS_OBJECT_CODE,SS.SHIPPING_OBJECT_CODE)
                       WHEN SS.GROUP_LEVEL = 'SHIPPING_OBJECT' THEN
                        SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.LV0_CODE,SS.LV1_CODE,SS.BUSSINESS_OBJECT_CODE)
                       WHEN SS.GROUP_LEVEL = 'BUSSINESS_OBJECT' THEN
                        SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.LV0_CODE,SS.LV1_CODE)
                       WHEN SS.GROUP_LEVEL = 'LV1' THEN
                        SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.LV0_CODE)
                       WHEN SS.GROUP_LEVEL = 'LV0' THEN
                        SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.LV0_CODE)
                     END AS PARENT_AMT,
                     SS.PARENT_CODE,
                     SS.PARENT_CN_NAME,
                     SS.LV0_CODE,
                     SS.LV0_CN_NAME,
                     SS.LV1_CODE,
                     SS.LV1_CN_NAME,
                     SS.BUSSINESS_OBJECT_CODE,
                     SS.BUSSINESS_OBJECT_CN_NAME,
                     SS.SHIPPING_OBJECT_CODE,
                     SS.SHIPPING_OBJECT_CN_NAME,
                     SS.MANUFACTURE_OBJECT_CODE,
                     SS.MANUFACTURE_OBJECT_CN_NAME
                 FROM SUM_COST_TMP SS
                      ) T2
          ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
          AND T1.GROUP_CODE = T2.GROUP_CODE
          AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
          AND T1.PARENT_CODE = T2.PARENT_CODE
          AND NVL(T1.LV0_CODE,'SNULL0') = NVL(T2.LV0_CODE,'SNULL0')
          AND NVL(T1.LV1_CODE,'SNULL1') = NVL(T2.LV1_CODE,'SNULL1')
          AND NVL(T1.BUSSINESS_OBJECT_CODE,'SNULL2') = NVL(T2.BUSSINESS_OBJECT_CODE,'SNULL2')
          AND NVL(T1.SHIPPING_OBJECT_CODE,'SNULL3') = NVL(T2.SHIPPING_OBJECT_CODE,'SNULL3')
          AND NVL(T1.MANUFACTURE_OBJECT_CODE,'SNULL4') = NVL(T2.MANUFACTURE_OBJECT_CODE,'SNULL4')
          WHERE T1.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2
          ;
 
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID||' 的权重数据到DM_FOM_ANNUAL_WEIGHT_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');    
     
     --收集统计信息
  ANALYZE FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集FIN_DM_OPT_FOI.DM_FOM_ANNUAL_WEIGHT_T统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

