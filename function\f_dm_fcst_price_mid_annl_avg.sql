-- Name: f_dm_fcst_price_mid_annl_avg; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_mid_annl_avg(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年12月版本
  创建人  ：唐钦
  背景描述：根据月累计均本补齐数据，取每年12月数据作为年度数据，并重新作补齐处理
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MID_ANNL_AVG('');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_MID_ANNL_AVG'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; -- 年度版本号ID
  V_MON_VERSION_ID BIGINT; -- 月度版本号ID
  V_BEFORE_PERIOD INT ;   -- 取T-3年
  V_SQL TEXT; --执行语句
  V_FROM_TABLE VARCHAR(100);
  V_TO_TABLE VARCHAR(100);
   V_YEAR           INT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
        if MONTH(CURRENT_TIMESTAMP) = 1
  then  select YEAR(CURRENT_TIMESTAMP)-4||12 into V_BEFORE_PERIOD ;
        select YEAR(NOW()) -1 into V_YEAR ;
  ELSE
        select YEAR(CURRENT_TIMESTAMP)-3||12 into V_BEFORE_PERIOD  ;
        select YEAR(NOW()) into V_YEAR ;
  END IF ;
   
  -- 取出年度版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  -- 取最新月度版本号
    SELECT VERSION_ID INTO V_MON_VERSION_ID   -- 月度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  -- 取最新月度版本号
    SELECT VERSION_ID INTO V_MON_VERSION_ID   -- 月度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID||'最新月度版本号为：'||V_MON_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 清空维表数据
    EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T';

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空：DM_FCST_PRICE_ANNL_AVG_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 创建金额临时表
  DROP TABLE IF EXISTS FCST_PRICE_AVG_AMT_TMP;
  CREATE TEMPORARY TABLE FCST_PRICE_AVG_AMT_TMP (
         PERIOD_YEAR                            INT,
         LV0_PROD_LIST_CODE                     VARCHAR(50),
         LV1_PROD_LIST_CODE                     VARCHAR(50),
         LV2_PROD_LIST_CODE                     VARCHAR(50),
         LV3_PROD_LIST_CODE                     VARCHAR(50),
         LV4_PROD_LIST_CODE                     VARCHAR(50),
         LV0_PROD_LIST_CN_NAME                  VARCHAR(200),
         LV1_PROD_LIST_CN_NAME                  VARCHAR(200),
         LV2_PROD_LIST_CN_NAME                  VARCHAR(200),
         LV3_PROD_LIST_CN_NAME                  VARCHAR(200),
         LV4_PROD_LIST_CN_NAME                  VARCHAR(200),
         SPART_CODE                             VARCHAR(50),
         SPART_CN_NAME                          VARCHAR(2000),
         USD_PNP_AMT                            NUMERIC,
         USD_PNP_AVG                            NUMERIC,
         OVERSEA_FLAG                           VARCHAR(50),
		 REGION_CODE                            VARCHAR(50),
		 REGION_CN_NAME                         VARCHAR(300),
		 REPOFFICE_CODE                         VARCHAR(50),
		 REPOFFICE_CN_NAME                      VARCHAR(300),
		 SIGN_TOP_CUST_CATEGORY_CODE            VARCHAR(50),
		 SIGN_TOP_CUST_CATEGORY_CN_NAME         VARCHAR(300),
		 SIGN_SUBSIDIARY_CUSTCATG_CN_NAME       VARCHAR(300),
		 VIEW_FLAG                              VARCHAR(50),
         BG_CODE                                VARCHAR(50),
         BG_CN_NAME                             VARCHAR(300),
		 NULL_FLAG                              VARCHAR(10),
		 APD_FLAG                               VARCHAR(10)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(SPART_CODE);

  DROP TABLE IF EXISTS FCST_PRICE_AVG_APPEND_TMP;
  CREATE TEMPORARY TABLE FCST_PRICE_AVG_APPEND_TMP(
         PERIOD_YEAR INT,
         LV0_PROD_LIST_CODE CHARACTER VARYING(50),
         LV1_PROD_LIST_CODE CHARACTER VARYING(50),
         LV2_PROD_LIST_CODE CHARACTER VARYING(50),
         LV3_PROD_LIST_CODE CHARACTER VARYING(50),
         LV4_PROD_LIST_CODE CHARACTER VARYING(50),
         LV0_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV1_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV2_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV3_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         LV4_PROD_LIST_CN_NAME CHARACTER VARYING(200),
         OVERSEA_FLAG CHARACTER VARYING(20),
         REGION_CODE CHARACTER VARYING(50),
         REGION_CN_NAME CHARACTER VARYING(200),
         REGION_NAME_ABBR CHARACTER VARYING(200),
         REPOFFICE_CODE CHARACTER VARYING(50),
         REPOFFICE_CN_NAME CHARACTER VARYING(200),
         REPOFFICE_NAME_ABBR CHARACTER VARYING(200),
         SIGN_TOP_CUST_CATEGORY_CODE CHARACTER VARYING(50),
         SIGN_TOP_CUST_CATEGORY_CN_NAME CHARACTER VARYING(200),
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME CHARACTER VARYING(100),
         SPART_CODE CHARACTER VARYING(40),
         SPART_CN_NAME CHARACTER VARYING(2000),
         USD_PNP_AMT NUMERIC,
         USD_PNP_AVG NUMERIC,
         VIEW_FLAG CHARACTER VARYING(20),
         APD_FLAG CHARACTER VARYING(5),
         APPEND_YEAR INT,
         BG_CODE CHARACTER VARYING(50),
         BG_CN_NAME CHARACTER VARYING(200),
         ENABLE_FLAG CHARACTER VARYING(2)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE, LV4_PROD_LIST_CODE, REPOFFICE_CODE);

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '创建临时表成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 将SPART层级的T-3年至T年的金额和均本数据插入临时表
  INSERT INTO FCST_PRICE_AVG_AMT_TMP(
         PERIOD_YEAR,                     
         LV0_PROD_LIST_CODE,              
         LV1_PROD_LIST_CODE,              
         LV2_PROD_LIST_CODE,              
         LV3_PROD_LIST_CODE,              
         LV4_PROD_LIST_CODE,              
         LV0_PROD_LIST_CN_NAME,           
         LV1_PROD_LIST_CN_NAME,           
         LV2_PROD_LIST_CN_NAME,           
         LV3_PROD_LIST_CN_NAME,           
         LV4_PROD_LIST_CN_NAME,           
         SPART_CODE,                      
         SPART_CN_NAME,                   
         USD_PNP_AMT,                     
         USD_PNP_AVG,                     
         OVERSEA_FLAG,                    
         REGION_CODE,                     
         REGION_CN_NAME,                  
         REPOFFICE_CODE,                  
         REPOFFICE_CN_NAME,               
         SIGN_TOP_CUST_CATEGORY_CODE,     
         SIGN_TOP_CUST_CATEGORY_CN_NAME,  
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,                       
         BG_CODE,                         
         BG_CN_NAME,
		 NULL_FLAG,
		 APD_FLAG
    )
  WITH PERIOD_DIM_TMP AS(
  SELECT DISTINCT VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         SPART_CODE,
         OVERSEA_FLAG,
         REGION_CODE,
         REPOFFICE_CODE,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         BG_CODE,
         MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR,LV0_PROD_LIST_CODE,LV1_PROD_LIST_CODE,LV2_PROD_LIST_CODE,LV3_PROD_LIST_CODE,LV4_PROD_LIST_CODE,SPART_CODE,OVERSEA_FLAG,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,VIEW_FLAG,BG_CODE) AS PERIOD_ID
     FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_DECODE_DTL_T     -- 取到每年的最大月份
     WHERE VERSION_ID = V_MON_VERSION_ID
  ),
  SPART_AMT_TMP AS(
  -- 从SPART层级月累计收敛表取到T-3年至T年最大月份的金额和数据
  SELECT T1.VERSION_ID,
	     T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CODE,
         T1.LV4_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CN_NAME,
         T1.SPART_CODE,
         T1.SPART_CN_NAME,
         T1.USD_PNP_AMT,
         T1.USD_PNP_AVG,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MID_DECODE_DTL_T T1   -- 只取T-3年至T年最大月份的均本数据
	  INNER JOIN PERIOD_DIM_TMP T2 
	  ON T1.VERSION_ID = T2.VERSION_ID
      AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
      AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
      AND T1.SPART_CODE = T2.SPART_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
	  AND T1.PERIOD_ID = T2.PERIOD_ID
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.VERSION_ID = V_MON_VERSION_ID
    ),
  TOP_SPART_AMT_AVG_TMP AS(
  -- 将SPART层级的数据，与TOP_SPART维表关联，取既为TOP_SPART的数据，又是当年权重值为前95%的数据
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CODE,
         T1.LV4_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CN_NAME,
         T1.SPART_CODE,
         T1.SPART_CN_NAME,
         T1.USD_PNP_AMT,
         T1.USD_PNP_AVG,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
      FROM SPART_AMT_TMP T1
      INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_TOP_SPART_INFO_T T2   --TOP_SPART筛选结果表
      ON T1.VERSION_ID = T2.VERSION_ID
      AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
      AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
      AND T1.SPART_CODE = T2.TOP_SPART_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
	  AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
      WHERE T2.IS_TOP_FLAG = 'Y'     -- 取被筛选为TOP_SPART的编码数据，本年再次被筛选为当前权重前95%的数据
      AND T2.YTD_TOP_FLAG = 'Y'      -- 取在TOP_SPART筛选范围内
    ),
  -- 生成T-3年到T年的所有年份, 所有的发散维
  CROSS_JOIN_TEMP AS (
  SELECT DISTINCT T2.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CODE,
         T1.LV4_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CN_NAME,
         T1.SPART_CODE,
         T1.SPART_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME
      FROM TOP_SPART_AMT_AVG_TMP T1,
     (     
       SELECT CAST(GENERATE_SERIES(V_YEAR-3,
                                   V_YEAR,
                                   1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   ) T2   -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）
    )
  -- 将取得的数据补齐T-3年到T年的所有维度数据
  SELECT T1.PERIOD_YEAR,                     
         T1.LV0_PROD_LIST_CODE,              
         T1.LV1_PROD_LIST_CODE,              
         T1.LV2_PROD_LIST_CODE,              
         T1.LV3_PROD_LIST_CODE,              
         T1.LV4_PROD_LIST_CODE,              
         T1.LV0_PROD_LIST_CN_NAME,           
         T1.LV1_PROD_LIST_CN_NAME,           
         T1.LV2_PROD_LIST_CN_NAME,           
         T1.LV3_PROD_LIST_CN_NAME,           
         T1.LV4_PROD_LIST_CN_NAME,           
         T1.SPART_CODE,                      
         T1.SPART_CN_NAME,                   
         T2.USD_PNP_AMT,                     
         T2.USD_PNP_AVG,                     
         T1.OVERSEA_FLAG,                    
         T1.REGION_CODE,                     
         T1.REGION_CN_NAME,                  
         T1.REPOFFICE_CODE,                  
         T1.REPOFFICE_CN_NAME,               
         T1.SIGN_TOP_CUST_CATEGORY_CODE,     
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,  
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,                       
         T1.BG_CODE,                         
         T1.BG_CN_NAME,
		 DECODE(T2.USD_PNP_AVG, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
         CASE WHEN T2.USD_PNP_AVG IS NULL THEN 'Y'
--              WHEN T2.USD_PNP_AVG = 0 THEN 'Y'
         ELSE 'N' END AS APD_FLAG --补齐标识：Y为补齐，N为原始
	  FROM CROSS_JOIN_TEMP T1
	  LEFT JOIN TOP_SPART_AMT_AVG_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
      AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
      AND T1.SPART_CODE = T2.SPART_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5');
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将SPART层级的T-3年至T年的金额和均本数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  -- 将补齐后的年均本数据插入临时表
  INSERT INTO FCST_PRICE_AVG_APPEND_TMP(
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APD_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
    )
  -- 将前面年份缺失的数据进行补齐（EG：2021、2022数据缺失，2023、2024有值的情况）
  WITH BASE_AVG_TMP AS(
  SELECT PERIOD_YEAR,                     
         LV0_PROD_LIST_CODE,              
         LV1_PROD_LIST_CODE,              
         LV2_PROD_LIST_CODE,              
         LV3_PROD_LIST_CODE,              
         LV4_PROD_LIST_CODE,              
         LV0_PROD_LIST_CN_NAME,           
         LV1_PROD_LIST_CN_NAME,           
         LV2_PROD_LIST_CN_NAME,           
         LV3_PROD_LIST_CN_NAME,           
         LV4_PROD_LIST_CN_NAME,           
         SPART_CODE,                      
         SPART_CN_NAME,                   
         USD_PNP_AMT,                     
         USD_PNP_AVG,                     
         OVERSEA_FLAG,                    
         REGION_CODE,                     
         REGION_CN_NAME,                  
         REPOFFICE_CODE,                  
         REPOFFICE_CN_NAME,               
         SIGN_TOP_CUST_CATEGORY_CODE,     
         SIGN_TOP_CUST_CATEGORY_CN_NAME,  
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,                       
         BG_CODE,                         
         BG_CN_NAME,
		 APD_FLAG,
		 SUM(NULL_FLAG) OVER(PARTITION BY LV0_PROD_LIST_CODE,LV1_PROD_LIST_CODE,LV2_PROD_LIST_CODE,LV3_PROD_LIST_CODE,LV4_PROD_LIST_CODE,SPART_CODE,OVERSEA_FLAG,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,VIEW_FLAG,BG_CODE ORDER BY PERIOD_YEAR) AS AVG_AMT_FLAG --均价标识: 为空不参与累计加1
      FROM FCST_PRICE_AVG_AMT_TMP
    )
  SELECT PERIOD_YEAR,                  
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         NVL(USD_PNP_AVG, AVG_AMT_2) AS USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APD_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 'Y' AS ENABLE_FLAG
       FROM (SELECT T1.PERIOD_YEAR,                  
                    T1.LV0_PROD_LIST_CODE,            
                    T1.LV1_PROD_LIST_CODE,            
                    T1.LV2_PROD_LIST_CODE,            
                    T1.LV3_PROD_LIST_CODE,            
                    T1.LV4_PROD_LIST_CODE,            
                    T1.LV0_PROD_LIST_CN_NAME,            
                    T1.LV1_PROD_LIST_CN_NAME,            
                    T1.LV2_PROD_LIST_CN_NAME,            
                    T1.LV3_PROD_LIST_CN_NAME,            
                    T1.LV4_PROD_LIST_CN_NAME,    
                    T1.SPART_CODE,                   
                    T1.SPART_CN_NAME, 
                    T1.USD_PNP_AMT,
                    T1.USD_PNP_AVG,
                    T2.AVG_AMT_2,
                    T2.PERIOD_YEAR_2,
                    T1.APD_FLAG,
                    T1.VIEW_FLAG,
                    CASE WHEN T1.APD_FLAG = 'Y' AND T2.AVG_AMT_2 IS NOT NULL THEN
                           T2.PERIOD_YEAR_2 ELSE NULL END AS APPEND_YEAR,
                    T1.OVERSEA_FLAG,
                    T1.REGION_CODE,                  
                    T1.REGION_CN_NAME,               
                    T1.REPOFFICE_CODE,               
                    T1.REPOFFICE_CN_NAME,            
                    T1.BG_CODE,                      
                    T1.BG_CN_NAME,
                    T1.SIGN_TOP_CUST_CATEGORY_CODE,
                    T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
                    T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                FROM BASE_AVG_TMP T1
                LEFT JOIN (SELECT DISTINCT P.LV0_PROD_LIST_CODE,
                                  P.LV1_PROD_LIST_CODE,
                                  P.LV2_PROD_LIST_CODE,
                                  P.LV3_PROD_LIST_CODE,
                                  P.LV4_PROD_LIST_CODE,
                                  P.SPART_CODE,
                                  FIRST_VALUE(P.PERIOD_YEAR) OVER(PARTITION BY P.VIEW_FLAG, P.OVERSEA_FLAG, P.REGION_CODE, P.REPOFFICE_CODE, P.BG_CODE, P.SIGN_TOP_CUST_CATEGORY_CODE,P.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,P.LV1_PROD_LIST_CODE,P.LV2_PROD_LIST_CODE,P.LV3_PROD_LIST_CODE,P.LV4_PROD_LIST_CODE,P.SPART_CODE ORDER BY P.PERIOD_YEAR ASC) AS PERIOD_YEAR_2, --有均价的首条会计期
                                  FIRST_VALUE(P.USD_PNP_AVG) OVER(PARTITION BY P.VIEW_FLAG, P.OVERSEA_FLAG, P.REGION_CODE, P.REPOFFICE_CODE, P.BG_CODE, P.SIGN_TOP_CUST_CATEGORY_CODE,P.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,P.LV1_PROD_LIST_CODE,P.LV2_PROD_LIST_CODE,P.LV3_PROD_LIST_CODE,P.LV4_PROD_LIST_CODE,P.SPART_CODE ORDER BY P.PERIOD_YEAR ASC) AS AVG_AMT_2, --有均价的首条补齐均价
                                  P.VIEW_FLAG,
                                  P.OVERSEA_FLAG,
                                  P.REGION_CODE,
                                  P.REPOFFICE_CODE,
                                  P.BG_CODE,
                                  P.SIGN_TOP_CUST_CATEGORY_CODE,
                                  P.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                              FROM BASE_AVG_TMP P
                              WHERE P.AVG_AMT_FLAG > 0) T2
                              ON T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
                              AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
                              AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
                              AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
                              AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
                              AND T1.SPART_CODE = T2.SPART_CODE
                              AND T1.VIEW_FLAG = T2.VIEW_FLAG
                              AND T1.BG_CODE = T2.BG_CODE
                              AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
                              AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
                              AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
                              AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
                              AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
                             AND T1.PERIOD_YEAR < T2.PERIOD_YEAR_2) S;                          

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将前面年份缺失的数据进行补齐（EG：2021、2022数据缺失，2023、2024有值的情况），并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将补齐后的年均本数据插入临时表
  INSERT INTO FCST_PRICE_AVG_APPEND_TMP(
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APD_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
    )
  -- 将中间年份缺失的数据进行补齐（EG：2021、数据有值，2022、2023数据缺失，2024数据有值的情况）
  WITH NULL_AVG_DIM_TMP AS(
  -- 将还有未补齐的数据取出来
  SELECT PERIOD_YEAR,                     
         LV0_PROD_LIST_CODE,              
         LV1_PROD_LIST_CODE,              
         LV2_PROD_LIST_CODE,              
         LV3_PROD_LIST_CODE,              
         LV4_PROD_LIST_CODE,
         SPART_CODE,
         OVERSEA_FLAG,                    
         REGION_CODE,
         REPOFFICE_CODE,
         SIGN_TOP_CUST_CATEGORY_CODE, 
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,                       
         BG_CODE,
         'Y' AS NULL_APD_FLAG,
         MIN(PERIOD_YEAR) OVER(PARTITION BY LV0_PROD_LIST_CODE,LV1_PROD_LIST_CODE,LV2_PROD_LIST_CODE,LV3_PROD_LIST_CODE,LV4_PROD_LIST_CODE,SPART_CODE,OVERSEA_FLAG,REGION_CODE,REPOFFICE_CODE,SIGN_TOP_CUST_CATEGORY_CODE,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,VIEW_FLAG,BG_CODE) AS MIN_YEAR
      FROM FCST_PRICE_AVG_APPEND_TMP
      WHERE USD_PNP_AVG IS NULL
  ),
  BASE_AVG_TMP AS(
  SELECT T1.PERIOD_YEAR,                     
         T1.LV0_PROD_LIST_CODE,              
         T1.LV1_PROD_LIST_CODE,              
         T1.LV2_PROD_LIST_CODE,              
         T1.LV3_PROD_LIST_CODE,              
         T1.LV4_PROD_LIST_CODE,              
         T1.LV0_PROD_LIST_CN_NAME,           
         T1.LV1_PROD_LIST_CN_NAME,           
         T1.LV2_PROD_LIST_CN_NAME,           
         T1.LV3_PROD_LIST_CN_NAME,           
         T1.LV4_PROD_LIST_CN_NAME,           
         T1.SPART_CODE,                      
         T1.SPART_CN_NAME,                   
         T1.USD_PNP_AMT,                     
         T1.USD_PNP_AVG,                     
         T1.OVERSEA_FLAG,                    
         T1.REGION_CODE,                     
         T1.REGION_CN_NAME,                  
         T1.REPOFFICE_CODE,                  
         T1.REPOFFICE_CN_NAME,               
         T1.SIGN_TOP_CUST_CATEGORY_CODE,     
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,  
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,                       
         T1.BG_CODE,                         
         T1.BG_CN_NAME,
		 T1.APD_FLAG,
		 SUM(DECODE(T1.USD_PNP_AVG, NULL, 0, 1)) OVER(PARTITION BY T1.LV0_PROD_LIST_CODE,T1.LV1_PROD_LIST_CODE,T1.LV2_PROD_LIST_CODE,T1.LV3_PROD_LIST_CODE,T1.LV4_PROD_LIST_CODE,T1.SPART_CODE,T1.OVERSEA_FLAG,T1.REGION_CODE,T1.REPOFFICE_CODE,T1.SIGN_TOP_CUST_CATEGORY_CODE,T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,T1.VIEW_FLAG,T1.BG_CODE ORDER BY T1.PERIOD_YEAR) AS AVG_AMT_FLAG --均价标识: 为空不参与累计加1
      FROM FCST_PRICE_AVG_APPEND_TMP T1
	  INNER JOIN (SELECT * FROM NULL_AVG_DIM_TMP WHERE PERIOD_YEAR = MIN_YEAR) T2
	  ON T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
      AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
      AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
      AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
      AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
      AND T1.SPART_CODE = T2.SPART_CODE
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.PERIOD_YEAR >= T2.MIN_YEAR   -- 取年份大于等于缺失数据年份的结果值
    )
  SELECT PERIOD_YEAR,                  
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         NVL(USD_PNP_AVG, AVG_AMT_2) AS USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APD_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 'Y' AS ENABLE_FLAG
       FROM (SELECT T1.PERIOD_YEAR,                  
                    T1.LV0_PROD_LIST_CODE,            
                    T1.LV1_PROD_LIST_CODE,            
                    T1.LV2_PROD_LIST_CODE,            
                    T1.LV3_PROD_LIST_CODE,            
                    T1.LV4_PROD_LIST_CODE,            
                    T1.LV0_PROD_LIST_CN_NAME,            
                    T1.LV1_PROD_LIST_CN_NAME,            
                    T1.LV2_PROD_LIST_CN_NAME,            
                    T1.LV3_PROD_LIST_CN_NAME,            
                    T1.LV4_PROD_LIST_CN_NAME,    
                    T1.SPART_CODE,                   
                    T1.SPART_CN_NAME, 
                    T1.USD_PNP_AMT,
                    T1.USD_PNP_AVG,
                    T2.AVG_AMT_2,
                    T2.PERIOD_YEAR_2,
                    T1.APD_FLAG,
                    T1.VIEW_FLAG,
                    CASE WHEN T1.APD_FLAG = 'Y' AND T2.AVG_AMT_2 IS NOT NULL THEN
                           T2.PERIOD_YEAR_2 ELSE NULL END AS APPEND_YEAR,
                    T1.OVERSEA_FLAG,
                    T1.REGION_CODE,                  
                    T1.REGION_CN_NAME,               
                    T1.REPOFFICE_CODE,               
                    T1.REPOFFICE_CN_NAME,            
                    T1.BG_CODE,                      
                    T1.BG_CN_NAME,
                    T1.SIGN_TOP_CUST_CATEGORY_CODE,
                    T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
                    T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                FROM BASE_AVG_TMP T1
                LEFT JOIN (SELECT DISTINCT P.LV0_PROD_LIST_CODE,
                                  P.LV1_PROD_LIST_CODE,
                                  P.LV2_PROD_LIST_CODE,
                                  P.LV3_PROD_LIST_CODE,
                                  P.LV4_PROD_LIST_CODE,
                                  P.SPART_CODE,
                                  FIRST_VALUE(P.PERIOD_YEAR) OVER(PARTITION BY P.VIEW_FLAG, P.OVERSEA_FLAG, P.REGION_CODE, P.REPOFFICE_CODE, P.BG_CODE, P.SIGN_TOP_CUST_CATEGORY_CODE,P.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,P.LV1_PROD_LIST_CODE,P.LV2_PROD_LIST_CODE,P.LV3_PROD_LIST_CODE,P.LV4_PROD_LIST_CODE,P.SPART_CODE ORDER BY P.PERIOD_YEAR ASC) AS PERIOD_YEAR_2, --有均价的首条会计期
                                  FIRST_VALUE(P.USD_PNP_AVG) OVER(PARTITION BY P.VIEW_FLAG, P.OVERSEA_FLAG, P.REGION_CODE, P.REPOFFICE_CODE, P.BG_CODE, P.SIGN_TOP_CUST_CATEGORY_CODE,P.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,P.LV1_PROD_LIST_CODE,P.LV2_PROD_LIST_CODE,P.LV3_PROD_LIST_CODE,P.LV4_PROD_LIST_CODE,P.SPART_CODE ORDER BY P.PERIOD_YEAR ASC) AS AVG_AMT_2, --有均价的首条补齐均价
                                  P.VIEW_FLAG,
                                  P.OVERSEA_FLAG,
                                  P.REGION_CODE,
                                  P.REPOFFICE_CODE,
                                  P.BG_CODE,
                                  P.SIGN_TOP_CUST_CATEGORY_CODE,
                                  P.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
                              FROM BASE_AVG_TMP P
                              WHERE P.AVG_AMT_FLAG > 0) T2
                              ON T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
                              AND T1.LV1_PROD_LIST_CODE = T2.LV1_PROD_LIST_CODE
                              AND T1.LV2_PROD_LIST_CODE = T2.LV2_PROD_LIST_CODE
                              AND T1.LV3_PROD_LIST_CODE = T2.LV3_PROD_LIST_CODE
                              AND T1.LV4_PROD_LIST_CODE = T2.LV4_PROD_LIST_CODE
                              AND T1.SPART_CODE = T2.SPART_CODE
                              AND T1.VIEW_FLAG = T2.VIEW_FLAG
                              AND T1.BG_CODE = T2.BG_CODE
                              AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
                              AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
                              AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
                              AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
                              AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
                             AND T1.PERIOD_YEAR < T2.PERIOD_YEAR_2) S
       WHERE APD_FLAG = 'Y';    -- 只取需要重新补齐的数据

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将中间年份缺失的数据进行补齐（EG：2021、数据有值，2022、2023数据缺失，2024数据有值的情况），并插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将结果值插入结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T(
         VERSION_ID,
		 PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG,
		 CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT V_VERSION_ID AS VERSION_ID,
		 PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV1_PROD_LIST_CODE,
         LV2_PROD_LIST_CODE,
         LV3_PROD_LIST_CODE,
         LV4_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CN_NAME,
         SPART_CODE,
         SPART_CN_NAME,
         USD_PNP_AMT,
         USD_PNP_AVG,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APD_FLAG,
         APPEND_YEAR,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG,
		 -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
	  FROM FCST_PRICE_AVG_APPEND_TMP 
	  WHERE USD_PNP_AVG IS NOT NULL;   -- 剔除掉不需要的数据（第一次补齐时未被补齐的部分数据）
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将补齐数据插入结果表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   --3.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AVG_T';

  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FCST_PRICE_ANNL_AVG_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

