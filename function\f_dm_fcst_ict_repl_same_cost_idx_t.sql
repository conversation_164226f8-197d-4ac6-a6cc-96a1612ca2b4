-- Name: f_dm_fcst_ict_repl_same_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_repl_same_cost_idx_t(f_cost_type character varying, f_granularity_type character varying, f_ytd_flag character varying, f_keystr text DEFAULT NULL::text, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
---来源表
--维表
新老编码替换关系表	DM_FCST_ICT_CODE_REPL_INFO_T
新老编码软硬件标识  DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T

--补齐均本表
重量级团队目录		DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_BASE_DETAIL_SPART_T   --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_BASE_DETAIL_SPART_T    --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_T     --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_BASE_DETAIL_SPART_T   --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_BASE_DETAIL_SPART_T    --STD PROD

--累积均本表
重量级团队目录		DM_FCST_ICT_PSP_IRB_REPL_SUM_DETAIL_SPART_T		--PSP IRB
产业目录：			DM_FCST_ICT_PSP_INDUS_REPL_SUM_DETAIL_SPART_T   --PSP INDUS
销售目录：			DM_FCST_ICT_PSP_PROD_REPL_SUM_DETAIL_SPART_T    --PSP PROD
重量级团队目录		DM_FCST_ICT_STD_IRB_REPL_SUM_DETAIL_SPART_T     --STD IRB
产业目录：			DM_FCST_ICT_STD_INDUS_REPL_SUM_DETAIL_SPART_T   --STD INDUS
销售目录：			DM_FCST_ICT_STD_PROD_REPL_SUM_DETAIL_SPART_T    --STD PROD

---结果表
--权重表
重量级团队目录		DM_FCST_ICT_PSP_IRB_REPL_WEIGHT_T
产业目录：			DM_FCST_ICT_PSP_INDUS_REPL_WEIGHT_T
销售目录：			DM_FCST_ICT_PSP_PROD_REPL_WEIGHT_T
重量级团队目录		DM_FCST_ICT_STD_IRB_REPL_WEIGHT_T
产业目录：			DM_FCST_ICT_STD_INDUS_REPL_WEIGHT_T
销售目录：			DM_FCST_ICT_STD_PROD_REPL_WEIGHT_T

--均本表
PSP: DM_FCST_ICT_PSP_PROD_REPL_SPART_MON_AVG_T
STD: DM_FCST_ICT_STD_PROD_REPL_SPART_MON_AVG_T

--累积均本表
PSP: DM_FCST_ICT_PSP_PROD_REPL_SPART_YTD_AVG_T
STD: DM_FCST_ICT_STD_PROD_REPL_SPART_YTD_AVG_T

--指数表
销售目录：			DM_FCST_ICT_PSP_PROD_REPL_SAME_COST_IDX_T	--本版本有效
销售目录：			DM_FCST_ICT_STD_PROD_REPL_SAME_COST_IDX_T	--本版本有效

--累计指数表
销售目录：			DM_FCST_ICT_PSP_PROD_REPL_SAME_YTD_COST_IDX_T	--本版本有效
销售目录：			DM_FCST_ICT_STD_PROD_REPL_SAME_YTD_COST_IDX_T	--本版本有效

--PSP
SELECT F_DM_FCST_ICT_REPL_SAME_COST_IDX_T('PSP','PROD','MON','','');	--一个版本数据
SELECT F_DM_FCST_ICT_REPL_SAME_COST_IDX_T('PSP','PROD','YTD','','');	--月累计一个版本数据

--STD
SELECT F_DM_FCST_ICT_REPL_SAME_COST_IDX_T('STD','PROD','MON','密钥','');	--一个版本数据
SELECT F_DM_FCST_ICT_REPL_SAME_COST_IDX_T('STD','PROD','YTD','密钥','');	--月累计一个版本数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                 VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_REPL_SAME_COST_IDX_T';
  V_VERSION                 VARCHAR(10);
  V_EXCEPTION_FLAG          INT;
  V_PBI_PART                TEXT; --PBI字段,判断PBI字段名
  V_TO_RMB_AVG_AMT_SQL		TEXT; --加密字段
  V_RMB_AMT_SQL				TEXT; --解密金额字段
  V_FROM_AVG_TABLE          VARCHAR(200); --金额来源表
  V_TO_REPL_SPART_AVG_TABLE VARCHAR(200); --组合均本表
  V_TO_WEIGHT_TABLE         VARCHAR(200); --权重目标表
  V_TO_INDEX_TABLE          VARCHAR(200); --指数目标表
  V_FROM_MON_AVG_TABLE      VARCHAR(200);
  V_FROM_YTD_AVG_TABLE      VARCHAR(200);
  V_TO_REPL_YTD_AVG_TABLE   VARCHAR(200);
  V_TO_REPL_MON_AVG_TABLE   VARCHAR(200);
  V_TO_MON_INDEX_TABLE      VARCHAR(200);
  V_TO_YTD_INDEX_TABLE      VARCHAR(200);
  V_PROD_PART               TEXT; --PBI重量级团队字段
  V_YEAR                    VARCHAR(50); --去年到今年
  V_BASE_PERIOD_ID          INT := TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),'YYYY')-1||'01'; --基期会计期
  V_CODE_REPL_GTS_TYPE      VARCHAR(50) := F_GRANULARITY_TYPE;
  V_REPL_VERSION            VARCHAR(50); --编码替换表版本号
  V_ANNL_VERSION            VARCHAR(50); --年度版本号(软硬件标识表所需)
  V_SQL                     TEXT;

BEGIN

X_RESULT_STATUS :='1';

 --写入日志
 V_EXCEPTION_FLAG	:= 0;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 0,
  F_CAL_LOG_DESC => '开始执行',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
--表判断
  V_FROM_MON_AVG_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_BASE_DETAIL_SPART_T';
  V_FROM_YTD_AVG_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_REPL_SUM_DETAIL_SPART_T';
  V_TO_REPL_MON_AVG_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_REPL_SPART_MON_AVG_T';
  V_TO_REPL_YTD_AVG_TABLE	:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_REPL_SPART_YTD_AVG_T';
  V_TO_WEIGHT_TABLE			:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_REPL_WEIGHT_T';
  V_TO_MON_INDEX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_REPL_SAME_COST_IDX_T';
  V_TO_YTD_INDEX_TABLE		:= 'FIN_DM_OPT_FOI.DM_FCST_ICT_'|| F_COST_TYPE ||'_'||F_GRANULARITY_TYPE||'_REPL_SAME_YTD_COST_IDX_T';
  
  V_YEAR := TO_CHAR((YEAR(CURRENT_DATE) - 1) || '-' || YEAR(CURRENT_DATE));
  
  IF F_YTD_FLAG = 'YTD' THEN
    V_FROM_AVG_TABLE          := V_FROM_YTD_AVG_TABLE;
    V_TO_REPL_SPART_AVG_TABLE := V_TO_REPL_YTD_AVG_TABLE;
    V_TO_INDEX_TABLE          := V_TO_YTD_INDEX_TABLE;
  ELSIF F_YTD_FLAG = 'MON' THEN
    V_FROM_AVG_TABLE          := V_FROM_MON_AVG_TABLE;
    V_TO_REPL_SPART_AVG_TABLE := V_TO_REPL_MON_AVG_TABLE;
    V_TO_INDEX_TABLE          := V_TO_MON_INDEX_TABLE;
  END IF;
  
  IF F_COST_TYPE = 'PSP' THEN
    V_RMB_AMT_SQL := 'RMB_AVG_AMT,RMB_COST_AMT,';
	V_TO_RMB_AVG_AMT_SQL := 'RMB_AVG_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_RMB_AMT_SQL := 'TO_NUMBER(GS_DECRYPT(RMB_AVG_AMT,
	                    '''||F_KEYSTR||''',
	                    ''aes128'',
	                    ''cbc'',
	                    ''sha256'')) AS RMB_AVG_AMT,
					TO_NUMBER(GS_DECRYPT(RMB_COST_AMT,
	                    '''||F_KEYSTR||''',
	                    ''aes128'',
	                    ''cbc'',
	                    ''sha256'')) AS RMB_COST_AMT,';		
	V_TO_RMB_AVG_AMT_SQL := '(GS_ENCRYPT(RMB_AVG_AMT,
	                    '''||F_KEYSTR||''',
	                    ''aes128'',
	                    ''cbc'',
	                    ''sha256'')) AS RMB_AVG_AMT,';
  END IF;
  
  IF F_GRANULARITY_TYPE = 'IRB' THEN 
    V_PBI_PART := '
      LV0_PROD_RND_TEAM_CODE,
      LV1_PROD_RND_TEAM_CODE,
      LV2_PROD_RND_TEAM_CODE,
      LV3_PROD_RND_TEAM_CODE,
      LV4_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RD_TEAM_CN_NAME,
      LV3_PROD_RD_TEAM_CN_NAME,
      LV4_PROD_RD_TEAM_CN_NAME,
      ';
	V_PROD_PART:='
		PROD_RND_TEAM_CODE,
		PROD_RD_TEAM_CN_NAME,
	';
  ELSIF F_GRANULARITY_TYPE = 'PROD' THEN 
    V_PBI_PART      := '
      LV0_PROD_LIST_CODE,
      LV1_PROD_LIST_CODE,
      LV2_PROD_LIST_CODE,
      LV3_PROD_LIST_CODE,
      LV4_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME,
      LV1_PROD_LIST_CN_NAME,
      LV2_PROD_LIST_CN_NAME,
      LV3_PROD_LIST_CN_NAME,
      LV4_PROD_LIST_CN_NAME,
      ';
	  
	V_PROD_PART:='
		PROD_LIST_CODE,
		PROD_LIST_CN_NAME,
	';
	
  ELSIF F_GRANULARITY_TYPE = 'INDUS' THEN 
    V_PBI_PART := '
      LV0_INDUSTRY_CATG_CODE,
      LV1_INDUSTRY_CATG_CODE,
      LV2_INDUSTRY_CATG_CODE,
      LV3_INDUSTRY_CATG_CODE,
      LV4_INDUSTRY_CATG_CODE,
      LV0_INDUSTRY_CATG_CN_NAME,
      LV1_INDUSTRY_CATG_CN_NAME,
      LV2_INDUSTRY_CATG_CN_NAME,
      LV3_INDUSTRY_CATG_CN_NAME,
      LV4_INDUSTRY_CATG_CN_NAME,
      ';

	V_PROD_PART:='
		INDUSTRY_CATG_CODE,
		INDUSTRY_CATG_CN_NAME,
	';
  END IF;

----版本号取值
--替代关系维表版本号取值 
  SELECT MAX(VERSION_ID) INTO V_REPL_VERSION
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T  
   WHERE DEL_FLAG = 'N';
   
--年度版本号取值
  SELECT VERSION_ID
    INTO V_ANNL_VERSION
    FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 1
     AND UPPER(DATA_TYPE) = 'ANNUAL'
   ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;

--月度版本号取值
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 1;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '取得本次执行数据成本类型：'||F_COST_TYPE||'，目录树：'||F_GRANULARITY_TYPE||'，版本号：'||V_VERSION,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--金额表解密
--建临时中间表
DROP TABLE IF EXISTS DM_BASE_AMT_TEMP;
CREATE TEMPORARY TABLE DM_BASE_AMT_TEMP(
 LV0_CODE			VARCHAR(50),
 LV1_CODE   		VARCHAR(50),
 LV2_CODE   		VARCHAR(50),
 LV3_CODE   		VARCHAR(50),
 LV4_CODE   		VARCHAR(50),
 LV0_CN_NAME		VARCHAR(200),
 LV1_CN_NAME   		VARCHAR(200),
 LV2_CN_NAME   		VARCHAR(200),
 LV3_CN_NAME   		VARCHAR(200),
 LV4_CN_NAME   		VARCHAR(200),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) ,
 RMB_AVG_AMT		NUMERIC,
 RMB_COST_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) ,
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) ,
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) ,
 OVERSEA_FLAG		VARCHAR(10),    
 SOFTWARE_MARK		VARCHAR(20),
 APPEND_FLAG		VARCHAR(5)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

  V_SQL :='
  INSERT INTO DM_BASE_AMT_TEMP
    (LV0_CODE,
     LV1_CODE,
     LV2_CODE,
     LV3_CODE,
     LV4_CODE,
     LV0_CN_NAME,
     LV1_CN_NAME,
     LV2_CN_NAME,
     LV3_CN_NAME,
     LV4_CN_NAME,
     PERIOD_YEAR,
     PERIOD_ID,
     SPART_CODE,
     SPART_CN_NAME,
     RMB_AVG_AMT,
     RMB_COST_AMT,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     SOFTWARE_MARK,
	 APPEND_FLAG)
    SELECT '||V_PBI_PART||'
           PERIOD_YEAR,
           PERIOD_ID,
           SPART_CODE,
           SPART_CN_NAME,
           '||V_RMB_AMT_SQL||'
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           SOFTWARE_MARK,
		   APPEND_FLAG
      FROM '||V_FROM_AVG_TABLE||';';
  EXECUTE V_SQL;

 --写入日志
 V_EXCEPTION_FLAG	:= 2;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => '基础金额解密插数完成',
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 

--维表建表
  DROP TABLE IF EXISTS REPL_FULL_DIM;
  CREATE TEMPORARY TABLE REPL_FULL_DIM(
    VERSION_ID                   INT          ,   -- 版本ID
    BG_CODE                      VARCHAR(50)  ,   -- BG编码
    BG_CN_NAME                   VARCHAR(200) ,   -- BG中文名称
    GTS_TYPE                     VARCHAR(50)  ,   -- 目录树类型（IRB 重量级团队目录树、INDUS 产业目录树、PROD 销售目录树） 24年7月版只有销售目录树的数据
    LV1_CODE                     VARCHAR(50)  ,   -- LV1编码
    LV1_CN_NAME                  VARCHAR(200) ,   -- LV1名称
    LV2_CODE                     VARCHAR(50)  ,   -- LV2编码
    LV2_CN_NAME                  VARCHAR(200) ,   -- LV2名称
    LV3_CODE                     VARCHAR(50)  ,   -- LV3编码
    LV3_CN_NAME                  VARCHAR(200) ,   -- LV3名称
    LV4_CODE                     VARCHAR(50)  ,   -- LV4编码
    LV4_CN_NAME                  VARCHAR(200) ,   -- LV4名称
    PROD_CODE                    VARCHAR(50)  ,   -- 产品编码
    PROD_CN_NAME                 VARCHAR(200) ,   -- 产品中文名称
    REPLACE_RELATION_NAME        VARCHAR(1000) ,   -- 替换关系名称
    REPLACE_RELATION_TYPE        VARCHAR(50)  ,   -- 新老编码替换类型（一对一  、一对多 、多对多）
    SPART_CODE                   VARCHAR(50)  ,   -- SPART编码
    SPART_DESC                   VARCHAR(2000),   -- SPART描述
    CODE_TYPE                    VARCHAR(50)  ,   -- 编码类型（NEW:新编码  OLD: 旧编码 ）
    RELATION_TYPE                VARCHAR(50) ,    -- 关系（ 替换 、收编）
	SOFTWARE_MARK				 VARCHAR(50) 	--软硬件标识
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(SPART_CODE,BG_CODE,REPLACE_RELATION_NAME)
  ;

  --扩展原新老替代关系表,得到完整新老编码维度
  INSERT INTO REPL_FULL_DIM
    (BG_CODE,
     BG_CN_NAME,
     GTS_TYPE,
     LV1_CODE,
     LV1_CN_NAME,
     LV2_CODE,
     LV2_CN_NAME,
     LV3_CODE,
     LV3_CN_NAME,
     LV4_CODE,
     LV4_CN_NAME,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     SPART_CODE,
     SPART_DESC,
     CODE_TYPE,
     RELATION_TYPE,
	 SOFTWARE_MARK)
    WITH BASE_DIM AS
     (
      --关联出软硬件标识
      SELECT T1.BG_CODE,
              T1.BG_CN_NAME,
              T1.GTS_TYPE,
              T1.LV1_CODE,
              T1.LV1_CN_NAME,
              T1.LV2_CODE,
              T1.LV2_CN_NAME,
              T1.LV3_CODE,
              T1.LV3_CN_NAME,
              T1.LV4_CODE,
              T1.LV4_CN_NAME,
              T1.REPLACE_RELATION_NAME,
              T1.REPLACE_RELATION_TYPE,
              T1.OLD_SPART_CODE,
              T1.OLD_SPART_DESC,
              T1.NEW_SPART_CODE,
              T1.NEW_SPART_DESC,
              T1.RELATION_TYPE,
              T2.SOFTWARE_MARK
        FROM FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_INFO_T T1
        LEFT JOIN FIN_DM_OPT_FOI.DM_FCST_ICT_CODE_REPL_SOFTWARE_INFO_T T2
          ON T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
         AND T1.DEL_FLAG = 'N'
         AND T1.VERSION_ID = V_REPL_VERSION
         AND T1.GTS_TYPE = V_CODE_REPL_GTS_TYPE
         AND T2.DEL_FLAG = 'N'
         AND T2.GTS_TYPE = V_CODE_REPL_GTS_TYPE
         AND T2.VERSION_ID = V_ANNL_VERSION
         AND T1.LV1_CODE = T2.LV1_CODE
         AND T1.LV2_CODE = T2.LV2_CODE
         AND T1.LV3_CODE = T2.LV3_CODE
         AND T1.LV4_CODE = T2.LV4_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.GTS_TYPE = T2.GTS_TYPE
         AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
         AND T1.RELATION_TYPE = T2.RELATION_TYPE)
    SELECT BG_CODE,
           BG_CN_NAME,
           GTS_TYPE,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           REGEXP_SPLIT_TO_TABLE(OLD_SPART_CODE, ',') AS SPART_CODE,
           OLD_SPART_DESC AS SPART_DESC,
           'OLD' AS CODE_TYPE,
           RELATION_TYPE,
           SOFTWARE_MARK
      FROM BASE_DIM
    UNION ALL
    SELECT BG_CODE,
           BG_CN_NAME,
           GTS_TYPE,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           REGEXP_SPLIT_TO_TABLE(NEW_SPART_CODE, ',') AS SPART_CODE,
           NEW_SPART_DESC AS SPART_DESC,
           'NEW' AS CODE_TYPE,
           RELATION_TYPE,
           SOFTWARE_MARK
      FROM BASE_DIM;
	 
 --写入日志
 V_EXCEPTION_FLAG	:= 3;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '软硬件标识关联完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	 
--维表建表
  DROP TABLE IF EXISTS REPL_MONTH_SPART_AMT_TEMP;
  CREATE TEMPORARY TABLE REPL_MONTH_SPART_AMT_TEMP(
	PERIOD_ID				INT,
	PERIOD_YEAR				INT,
	LV0_CODE				VARCHAR(50),
	LV0_CN_NAME				VARCHAR(200),
	LV1_CODE				VARCHAR(50),
	LV1_CN_NAME				VARCHAR(200),
	LV2_CODE				VARCHAR(50),
	LV2_CN_NAME     		VARCHAR(200),
	LV3_CODE				VARCHAR(50),
	LV3_CN_NAME     		VARCHAR(200),
	LV4_CODE				VARCHAR(50),
	LV4_CN_NAME         	VARCHAR(200),
	REGION_CODE         	VARCHAR(50),
	REGION_CN_NAME      	VARCHAR(200),
	REPOFFICE_CODE      	VARCHAR(50),
	REPOFFICE_CN_NAME   	VARCHAR(200),
	SPART_CODE          	VARCHAR(50),
	SPART_DESC          	VARCHAR(200),
	BG_CODE					VARCHAR(50),
	BG_CN_NAME              VARCHAR(200),
	OVERSEA_FLAG            VARCHAR(50),
	REPLACE_RELATION_NAME   VARCHAR(200),
	REPLACE_RELATION_TYPE   VARCHAR(200),
	RELATION_TYPE           VARCHAR(200),
	CODE_TYPE               VARCHAR(5),
	PROD_QTY                NUMERIC,
	RMB_COST_AMT			NUMERIC,
	RMB_AVG_AMT				NUMERIC,
	APPEND_FLAG				VARCHAR(5),
	SOFTWARE_MARK			VARCHAR(20)
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(SPART_CODE,BG_CODE,REPLACE_RELATION_NAME)
  ;
	 
--关联得到属于拥有替代关系的数据
 INSERT INTO REPL_MONTH_SPART_AMT_TEMP
  (PERIOD_ID,
   PERIOD_YEAR,
   LV0_CODE,
   LV0_CN_NAME,
   LV1_CODE,
   LV1_CN_NAME,
   LV2_CODE,
   LV2_CN_NAME,
   LV3_CODE,
   LV3_CN_NAME,
   LV4_CODE,
   LV4_CN_NAME,
   REGION_CODE,
   REGION_CN_NAME,
   REPOFFICE_CODE,
   REPOFFICE_CN_NAME,
   SPART_CODE,
   SPART_DESC,
   BG_CODE,
   BG_CN_NAME,
   OVERSEA_FLAG,
   REPLACE_RELATION_NAME,	 -- 替换关系名称
   REPLACE_RELATION_TYPE,    -- 新老编码替换类型(一对一,一对多,多对多)
   RELATION_TYPE,            -- 关系(替换 、收编)
   CODE_TYPE,                -- 编码类型(NEW:新编码  OLD:旧编码)
   RMB_COST_AMT,
   RMB_AVG_AMT,
   APPEND_FLAG,
   SOFTWARE_MARK)
  SELECT DISTINCT T1.PERIOD_ID,
                  T1.PERIOD_YEAR,
                  T1.LV0_CODE,
                  T1.LV0_CN_NAME,
                  T1.LV1_CODE,
                  T1.LV1_CN_NAME,
                  T1.LV2_CODE,
                  T1.LV2_CN_NAME,
                  T1.LV3_CODE,
                  T1.LV3_CN_NAME,
                  T1.LV4_CODE,
                  T1.LV4_CN_NAME,
                  T1.REGION_CODE,
                  T1.REGION_CN_NAME,
                  T1.REPOFFICE_CODE,
                  T1.REPOFFICE_CN_NAME,
                  T1.SPART_CODE,
                  T2.SPART_DESC,
                  T1.BG_CODE,
                  T1.BG_CN_NAME,
                  T1.OVERSEA_FLAG,
                  T2.REPLACE_RELATION_NAME,
                  T2.REPLACE_RELATION_TYPE,
                  T2.RELATION_TYPE,
                  T2.CODE_TYPE,
                  T1.RMB_COST_AMT,
                  T1.RMB_AVG_AMT,
				  T1.APPEND_FLAG,
				  T1.SOFTWARE_MARK
    FROM DM_BASE_AMT_TEMP T1 
    JOIN REPL_FULL_DIM T2 -- 新旧编码替换关系临时表
      ON ((T1.LV1_CODE = T2.LV1_CODE AND T2.LV2_CODE = 'ALL' AND
         T2.LV3_CODE = 'ALL' AND T2.LV4_CODE = 'ALL') OR
         (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND
         T2.LV3_CODE = 'ALL' AND T2.LV4_CODE = 'ALL') OR
         (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND
         T1.LV3_CODE = T2.LV3_CODE AND T2.LV4_CODE = 'ALL') OR
         (T1.LV1_CODE = T2.LV1_CODE AND T1.LV2_CODE = T2.LV2_CODE AND
         T1.LV3_CODE = T2.LV3_CODE AND T1.LV4_CODE = T2.LV4_CODE))
     AND T1.BG_CODE = T2.BG_CODE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
     AND T1.SPART_CODE = T2.SPART_CODE;
	 
 --写入日志
 V_EXCEPTION_FLAG	:= 4;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => '替代关系基础数据关联完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	 
  DROP TABLE IF EXISTS REPL_SPART_AMT_TEMP;
  CREATE TEMPORARY TABLE REPL_SPART_AMT_TEMP(
	PERIOD_ID				INT,
	PERIOD_YEAR				INT,
	LV0_CODE				VARCHAR(50),
	LV0_CN_NAME				VARCHAR(200),
	LV1_CODE				VARCHAR(50),
	LV1_CN_NAME				VARCHAR(200),
	LV2_CODE				VARCHAR(50),
	LV2_CN_NAME     		VARCHAR(200),
	LV3_CODE				VARCHAR(50),
	LV3_CN_NAME     		VARCHAR(200),
	LV4_CODE				VARCHAR(50),
	LV4_CN_NAME         	VARCHAR(200),
	SPART_CODE				VARCHAR(50),
	RMB_COST_AMT			NUMERIC,
	RMB_AVG_AMT				NUMERIC,
	APPEND_FLAG				VARCHAR(5),
	REGION_CODE         	VARCHAR(50),
	REGION_CN_NAME      	VARCHAR(200),
	REPOFFICE_CODE      	VARCHAR(50),
	REPOFFICE_CN_NAME   	VARCHAR(200),
	BG_CODE					VARCHAR(50),
	BG_CN_NAME				VARCHAR(200),
	OVERSEA_FLAG			VARCHAR(50),
	REPLACE_RELATION_NAME   VARCHAR(200),
	REPLACE_RELATION_TYPE   VARCHAR(200),
	RELATION_TYPE           VARCHAR(200),
	CODE_TYPE               VARCHAR(5),
	SOFTWARE_MARK			VARCHAR(20)
  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(SPART_CODE,BG_CODE,REPLACE_RELATION_NAME)
  ;
  
--计算每个替代关系中的组合均本
  WITH BASE_MONTH_WEIGHT AS
   (SELECT PERIOD_ID,
           PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           SPART_CODE,
           SPART_DESC,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           CODE_TYPE,
           (RMB_COST_AMT /
           NULLIF(SUM(RMB_COST_AMT) OVER(PARTITION BY PERIOD_ID,
                        LV4_CODE,
                        REGION_CODE,
                        REPOFFICE_CODE,
                        BG_CODE,
                        OVERSEA_FLAG,
                        REPLACE_RELATION_NAME,
                        REPLACE_RELATION_TYPE,
                        RELATION_TYPE,
                        CODE_TYPE),
                   0)) * RMB_AVG_AMT AS REPL_SPART_AVG,
           RMB_AVG_AMT,
		   RMB_COST_AMT,
		   SOFTWARE_MARK
      FROM REPL_MONTH_SPART_AMT_TEMP) 
   INSERT INTO REPL_SPART_AMT_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      LV2_CODE,
      LV2_CN_NAME,
      LV3_CODE,
      LV3_CN_NAME,
      LV4_CODE,
      LV4_CN_NAME,
      SPART_CODE,
      RMB_AVG_AMT,
	  RMB_COST_AMT,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
      REPLACE_RELATION_NAME,
      REPLACE_RELATION_TYPE,
      RELATION_TYPE,
      CODE_TYPE,
	  SOFTWARE_MARK)
  SELECT PERIOD_ID,
         PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         ARRAY_TO_STRING(ARRAY_AGG(DISTINCT SPART_CODE), ',') AS SPART_CODE,
         SUM(REPL_SPART_AVG) AS RMB_AVG_AMT,
		 SUM(RMB_COST_AMT) AS RMB_COST_AMT,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
		 SOFTWARE_MARK
    FROM BASE_MONTH_WEIGHT
   GROUP BY PERIOD_ID,
            PERIOD_YEAR,
            LV0_CODE,
            LV0_CN_NAME,
            LV1_CODE,
            LV1_CN_NAME,
            LV2_CODE,
            LV2_CN_NAME,
            LV3_CODE,
            LV3_CN_NAME,
            LV4_CODE,
            LV4_CN_NAME,
            REGION_CODE,
            REGION_CN_NAME,
            REPOFFICE_CODE,
            REPOFFICE_CN_NAME,
            BG_CODE,
            BG_CN_NAME,
            OVERSEA_FLAG,
            REPLACE_RELATION_NAME,
            REPLACE_RELATION_TYPE,
            RELATION_TYPE,
            CODE_TYPE,
			SOFTWARE_MARK;
			
 --写入日志
 V_EXCEPTION_FLAG	:= 5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '组合均本计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
			
  V_SQL:='
   INSERT INTO '||V_TO_REPL_SPART_AVG_TABLE||'
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      LV2_CODE,
      LV2_CN_NAME,
      LV3_CODE,
      LV3_CN_NAME,
      LV4_CODE,
      LV4_CN_NAME,
      SPART_CODE,
      RMB_AVG_AMT,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
      REPLACE_RELATION_NAME,
      REPLACE_RELATION_TYPE,
      RELATION_TYPE,
      CODE_TYPE,
      CREATED_BY,
      CREATION_DATE,
      LAST_UPDATED_BY,
      LAST_UPDATE_DATE,
      DEL_FLAG,
	  SOFTWARE_MARK)
  SELECT PERIOD_ID,
         PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         LV4_CODE,
         LV4_CN_NAME,
         SPART_CODE,
         '||V_TO_RMB_AVG_AMT_SQL||'
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         BG_CODE,
         BG_CN_NAME,
         OVERSEA_FLAG,
         REPLACE_RELATION_NAME,
         REPLACE_RELATION_TYPE,
         RELATION_TYPE,
         CODE_TYPE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
		 SOFTWARE_MARK
    FROM REPL_SPART_AMT_TEMP;';
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 6;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => '组合均本结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 


  DROP TABLE IF EXISTS DM_REPL_COST_INDEX_TEMP ;
  CREATE TEMPORARY TABLE DM_REPL_COST_INDEX_TEMP
   (PERIOD_ID				INT,
	PERIOD_YEAR				INT,
	LV0_CODE				VARCHAR(50),
	LV0_CN_NAME				VARCHAR(200),
	LV1_CODE				VARCHAR(50),
	LV1_CN_NAME				VARCHAR(200),
	LV2_CODE				VARCHAR(50),
	LV2_CN_NAME     		VARCHAR(200),
	LV3_CODE				VARCHAR(50),
	LV3_CN_NAME     		VARCHAR(200),
	LV4_CODE				VARCHAR(50),
	LV4_CN_NAME         	VARCHAR(200),
	PROD_CODE				VARCHAR(50),
	PROD_CN_NAME			VARCHAR(200),
    GROUP_CODE				VARCHAR(50),
    GROUP_CN_NAME			VARCHAR(200),
	GROUP_LEVEL				VARCHAR(50),
    COST_INDEX				NUMERIC,
	PARENT_CODE				VARCHAR(50),
	PARENT_CN_NAME			VARCHAR(200),
	REGION_CODE         	VARCHAR(50),
	REGION_CN_NAME      	VARCHAR(200),
	REPOFFICE_CODE      	VARCHAR(50),
	REPOFFICE_CN_NAME   	VARCHAR(200),
	BG_CODE					VARCHAR(50),
	BG_CN_NAME				VARCHAR(200),
	OVERSEA_FLAG			VARCHAR(50),
	REPLACE_RELATION_NAME   VARCHAR(200),
	REPLACE_RELATION_TYPE   VARCHAR(200),
	RELATION_TYPE           VARCHAR(200),
	SOFTWARE_MARK			VARCHAR(20)
	  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE)
  ;
	
--SPART层级(替代关系层级)指数
--旧编码基期均本
  WITH BASE_PERIOD_AVG AS
   (SELECT PERIOD_ID,
           LV4_CODE,
           LV4_CN_NAME,
           SPART_CODE,
           RMB_AVG_AMT,
           REGION_CODE,
           REPOFFICE_CODE,
           BG_CODE,
           OVERSEA_FLAG,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
		   SOFTWARE_MARK
      FROM REPL_SPART_AMT_TEMP
     WHERE CODE_TYPE = 'OLD'
       AND PERIOD_ID = V_BASE_PERIOD_ID)
   INSERT INTO DM_REPL_COST_INDEX_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      LV2_CODE,
      LV2_CN_NAME,
      LV3_CODE,
      LV3_CN_NAME,
      LV4_CODE,
      LV4_CN_NAME,
	  PROD_CODE,
	  PROD_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
	  GROUP_LEVEL,
      COST_INDEX,
	  PARENT_CODE,
	  PARENT_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
      REPLACE_RELATION_NAME,
      REPLACE_RELATION_TYPE,
      RELATION_TYPE,
	  SOFTWARE_MARK)
  SELECT T1.PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV2_CODE,
         T1.LV2_CN_NAME,
         T1.LV3_CODE,
         T1.LV3_CN_NAME,
         T1.LV4_CODE,
         T1.LV4_CN_NAME,
		 T1.LV4_CODE AS PROD_CODE,
		 T1.LV4_CN_NAME AS PROD_CN_NAME,
         T1.SPART_CODE AS GROUP_CODE,
         '' AS GROUP_CN_NAME,
		 'SPART' AS GROUP_LEVEL,
         T1.RMB_AVG_AMT / NULLIF(T2.RMB_AVG_AMT, 0) * 100 AS COST_INDEX,
		 T1.LV4_CODE AS PARENT_CODE,
		 T1.LV4_CN_NAME AS PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REPLACE_RELATION_NAME,
         T1.REPLACE_RELATION_TYPE,
         T1.RELATION_TYPE,
		 T1.SOFTWARE_MARK
    FROM REPL_SPART_AMT_TEMP T1
    LEFT JOIN BASE_PERIOD_AVG T2
      ON T1.LV4_CODE = T2.LV4_CODE
     AND T1.CODE_TYPE = 'NEW'
	 AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
     AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
     AND T1.RELATION_TYPE = T2.RELATION_TYPE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   WHERE T2.SPART_CODE IS NOT NULL
     AND T1.CODE_TYPE = 'NEW';
   
 --写入日志
 V_EXCEPTION_FLAG	:= 7;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => 'SPART层级指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	  
  DROP TABLE IF EXISTS REPL_WEIGHT_TEMP ;
  CREATE TEMPORARY TABLE REPL_WEIGHT_TEMP
   (PERIOD_ID				INT,
	PERIOD_YEAR				INT,
	LV0_CODE				VARCHAR(50),
	LV0_CN_NAME				VARCHAR(200),
	LV1_CODE				VARCHAR(50),
	LV1_CN_NAME				VARCHAR(200),
	LV2_CODE				VARCHAR(50),
	LV2_CN_NAME     		VARCHAR(200),
	LV3_CODE				VARCHAR(50),
	LV3_CN_NAME     		VARCHAR(200),
	LV4_CODE				VARCHAR(50),
	LV4_CN_NAME         	VARCHAR(200),
	PROD_CODE				VARCHAR(50),
	PROD_CN_NAME			VARCHAR(200),
    GROUP_CODE				VARCHAR(50),
    GROUP_CN_NAME			VARCHAR(200),
	GROUP_LEVEL				VARCHAR(50),
    WEIGHT_RATE				NUMERIC,
	PARENT_CODE				VARCHAR(50),
	PARENT_CN_NAME			VARCHAR(200),
	REGION_CODE         	VARCHAR(50),
	REGION_CN_NAME      	VARCHAR(200),
	REPOFFICE_CODE      	VARCHAR(50),
	REPOFFICE_CN_NAME   	VARCHAR(200),
	BG_CODE					VARCHAR(50),
	BG_CN_NAME				VARCHAR(200),
	OVERSEA_FLAG			VARCHAR(50),
	REPLACE_RELATION_NAME   VARCHAR(200),
	REPLACE_RELATION_TYPE   VARCHAR(200),
	RELATION_TYPE           VARCHAR(200),
	SOFTWARE_MARK			VARCHAR(20)
	  )ON COMMIT PRESERVE ROWS DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE)
  ; 
	  
--------计算每层权重
  INSERT INTO REPL_WEIGHT_TEMP
    (LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     LV2_CODE,
     LV2_CN_NAME,
     LV3_CODE,
     LV3_CN_NAME,
     LV4_CODE,
     LV4_CN_NAME,
	 PROD_CODE,
	 PROD_CN_NAME,
	 GROUP_CODE,
	 GROUP_CN_NAME,
     GROUP_LEVEL,
     WEIGHT_RATE,
	 PARENT_CODE,
	 PARENT_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 REPLACE_RELATION_NAME,
	 REPLACE_RELATION_TYPE,
	 RELATION_TYPE,
	 SOFTWARE_MARK)
	 
  WITH BASE_AMT_TEMP AS
   (SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
           ARRAY_TO_STRING(ARRAY_AGG(DISTINCT SPART_CODE), ',') AS SPART_CODE,
		   SUM(RMB_COST_AMT) AS RMB_COST_AMT,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           SOFTWARE_MARK
      FROM REPL_MONTH_SPART_AMT_TEMP
     WHERE APPEND_FLAG = 'N'
       AND PERIOD_YEAR BETWEEN (YEAR(NOW()) - 1) AND (YEAR(NOW()))
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              LV2_CODE,
              LV2_CN_NAME,
              LV3_CODE,
              LV3_CN_NAME,
              LV4_CODE,
              LV4_CN_NAME,
              REGION_CODE,
              REGION_CN_NAME,
              REPOFFICE_CODE,
              REPOFFICE_CN_NAME,
              BG_CODE,
              BG_CN_NAME,
              OVERSEA_FLAG,
              REPLACE_RELATION_NAME,
              REPLACE_RELATION_TYPE,
              RELATION_TYPE,
              SOFTWARE_MARK)
--替代关系/LV4
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
		   LV4_CODE AS PROD_CODE,
		   LV4_CN_NAME AS PROD_CN_NAME,
           SPART_CODE AS GROUP_CODE,
		   '' AS GROUP_CN_NAME,
           'SPART' AS GROUP_LEVEL,
           RMB_COST_AMT /
           NULLIF(SUM(RMB_COST_AMT) OVER(PARTITION BY LV4_CODE,
                       REGION_CODE,
                       REPOFFICE_CODE,
                       BG_CODE,
                       OVERSEA_FLAG,
					   SOFTWARE_MARK),
                  0) AS WEIGHT_RATE,
           LV4_CODE AS PARENT_CODE,
		   LV4_CN_NAME AS PARENT_CN_NAME,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   REPLACE_RELATION_NAME,
		   REPLACE_RELATION_TYPE,
		   RELATION_TYPE,
		   SOFTWARE_MARK
      FROM BASE_AMT_TEMP
	UNION ALL
--LV4/LV3
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
		   LV4_CODE AS PROD_CODE,
		   LV4_CN_NAME AS PROD_CN_NAME,
           LV4_CODE AS GROUP_CODE,
		   LV4_CN_NAME AS GROUP_CN_NAME,
           'LV4' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV3_CODE,
                       REGION_CODE,
                       REPOFFICE_CODE,
                       BG_CODE,
                       OVERSEA_FLAG,
					   SOFTWARE_MARK),
                  0) AS WEIGHT_RATE,
           LV3_CODE AS PARENT_CODE,
		   LV3_CN_NAME AS PARENT_CN_NAME,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   '' AS REPLACE_RELATION_NAME,
		   '' AS REPLACE_RELATION_TYPE,
		   '' AS RELATION_TYPE,
		   SOFTWARE_MARK
      FROM BASE_AMT_TEMP
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              LV2_CODE,
              LV2_CN_NAME,
              LV3_CODE,
              LV3_CN_NAME,
              LV4_CODE,
              LV4_CN_NAME,
              REGION_CODE,
              REGION_CN_NAME,
              REPOFFICE_CODE,
              REPOFFICE_CN_NAME,
              BG_CODE,
              BG_CN_NAME,
              OVERSEA_FLAG,
			  SOFTWARE_MARK
	UNION ALL
--LV3/LV2
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           '' AS LV4_CODE,
           '' AS LV4_CN_NAME,
           LV3_CODE AS PROD_CODE,
		   LV3_CN_NAME AS PROD_CN_NAME,
		   LV3_CODE AS GROUP_CODE,
		   LV3_CN_NAME AS GROUP_CN_NAME,
           'LV3' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV2_CODE,
                       REGION_CODE,
                       REPOFFICE_CODE,
                       BG_CODE,
                       OVERSEA_FLAG,
					   SOFTWARE_MARK),
                  0) AS WEIGHT_RATE,
           LV2_CODE AS PARENT_CODE,
		   LV2_CN_NAME AS PARENT_CN_NAME,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   '' AS REPLACE_RELATION_NAME,
		   '' AS REPLACE_RELATION_TYPE,
		   '' AS RELATION_TYPE,
		   SOFTWARE_MARK
      FROM BASE_AMT_TEMP
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              LV2_CODE,
              LV2_CN_NAME,
              LV3_CODE,
              LV3_CN_NAME,
              REGION_CODE,
              REGION_CN_NAME,
              REPOFFICE_CODE,
              REPOFFICE_CN_NAME,
              BG_CODE,
              BG_CN_NAME,
              OVERSEA_FLAG,
			  SOFTWARE_MARK
	UNION ALL
--LV2/LV1
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           '' AS LV3_CODE,
           '' AS LV3_CN_NAME,
           '' AS LV4_CODE,
           '' AS LV4_CN_NAME,
           LV2_CODE AS PROD_CODE,
		   LV2_CN_NAME AS PROD_CN_NAME,
		   LV2_CODE AS GROUP_CODE,
		   LV2_CN_NAME AS GROUP_CN_NAME,
           'LV2' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV1_CODE,
                       REGION_CODE,
                       REPOFFICE_CODE,
                       BG_CODE,
                       OVERSEA_FLAG,
					   SOFTWARE_MARK),
                  0) AS WEIGHT_RATE,
           LV1_CODE AS PARENT_CODE,
		   LV1_CN_NAME AS PARENT_CN_NAME,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   '' AS REPLACE_RELATION_NAME,
		   '' AS REPLACE_RELATION_TYPE,
		   '' AS RELATION_TYPE,
		   SOFTWARE_MARK
      FROM BASE_AMT_TEMP
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              LV2_CODE,
              LV2_CN_NAME,
              REGION_CODE,
              REGION_CN_NAME,
              REPOFFICE_CODE,
              REPOFFICE_CN_NAME,
              BG_CODE,
              BG_CN_NAME,
              OVERSEA_FLAG,
			  SOFTWARE_MARK
	UNION ALL
--LV1/LV0
    SELECT LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           '' AS LV2_CODE,
           '' AS LV2_CN_NAME,
           '' AS LV3_CODE,
           '' AS LV3_CN_NAME,
           '' AS LV4_CODE,
           '' AS LV4_CN_NAME,
           LV1_CODE AS PROD_CODE,
		   LV1_CN_NAME AS PROD_CN_NAME,
		   LV1_CODE AS GROUP_CODE,
		   LV1_CN_NAME AS GROUP_CN_NAME,
           'LV1' AS GROUP_LEVEL,
           SUM(RMB_COST_AMT) /
           NULLIF(SUM(SUM(RMB_COST_AMT)) OVER(PARTITION BY LV0_CODE,
                       REGION_CODE,
                       REPOFFICE_CODE,
                       BG_CODE,
                       OVERSEA_FLAG,
					   SOFTWARE_MARK),
                  0) AS WEIGHT_RATE,
           LV0_CODE AS PARENT_CODE,
		   LV0_CN_NAME AS PARENT_CN_NAME,
		   REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   '' AS REPLACE_RELATION_NAME,
		   '' AS REPLACE_RELATION_TYPE,
		   '' AS RELATION_TYPE,
		   SOFTWARE_MARK
      FROM BASE_AMT_TEMP
     GROUP BY LV0_CODE,
              LV0_CN_NAME,
              LV1_CODE,
              LV1_CN_NAME,
              REGION_CODE,
              REGION_CN_NAME,
              REPOFFICE_CODE,
              REPOFFICE_CN_NAME,
              BG_CODE,
              BG_CN_NAME,
              OVERSEA_FLAG,
			  SOFTWARE_MARK;
			  
 --写入日志
 V_EXCEPTION_FLAG	:= 8;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '权重计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
 V_SQL:='
 DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION||';
 
  INSERT INTO '||V_TO_WEIGHT_TABLE||'
    (VERSION_ID,
     PERIOD_YEAR,
     PROD_CODE,
	 PROD_CN_NAME,
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     WEIGHT_RATE,
     PARENT_CODE,
     PARENT_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
	 SOFTWARE_MARK,
	 REPLACE_RELATION_NAME,
	 REPLACE_RELATION_TYPE,
	 RELATION_TYPE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '''||V_YEAR||''' AS PERIOD_YEAR,
           PROD_CODE,
		   PROD_CN_NAME,
		   GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           WEIGHT_RATE,
           PARENT_CODE,
           PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM REPL_WEIGHT_TEMP;
	  ';

EXECUTE V_SQL;	  

 --写入日志
 V_EXCEPTION_FLAG	:= 8.5;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8.5,
  F_CAL_LOG_DESC => '权重结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			  
			  
--计算同基指数
--LV4
  WITH BASE_INDEX AS
   (SELECT PERIOD_ID,
           PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           LV4_CODE,
           LV4_CN_NAME,
           PROD_CODE,
           PROD_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_INDEX,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
		   SOFTWARE_MARK
      FROM DM_REPL_COST_INDEX_TEMP
     WHERE GROUP_LEVEL = 'SPART'),
  BASE_WEIGHT AS
   (SELECT PROD_CODE,
           PROD_CN_NAME,
           GROUP_CODE,
		   GROUP_CN_NAME,
           GROUP_LEVEL,
           WEIGHT_RATE,
           REGION_CODE,
           REPOFFICE_CODE,
           BG_CODE,
           OVERSEA_FLAG,
		   REPLACE_RELATION_NAME,
		   REPLACE_RELATION_TYPE,
		   RELATION_TYPE,
		   SOFTWARE_MARK
      FROM REPL_WEIGHT_TEMP
     WHERE GROUP_LEVEL = 'SPART') 
   INSERT INTO DM_REPL_COST_INDEX_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      LV2_CODE,
      LV2_CN_NAME,
      LV3_CODE,
      LV3_CN_NAME,
      PROD_CODE,
      PROD_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
	  PARENT_CODE,
	  PARENT_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
	  SOFTWARE_MARK)
  SELECT T1.PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV2_CODE,
         T1.LV2_CN_NAME,
         T1.LV3_CODE,
         T1.LV3_CN_NAME,
         T1.LV4_CODE AS PROD_CODE,
         T1.LV4_CN_NAME AS PROD_CN_NAME,
         T1.LV4_CODE AS GROUP_CODE,
         T1.LV4_CN_NAME AS GROUP_CN_NAME,
         'LV4' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 T1.LV3_CODE AS PARENT_CODE,
		 T1.LV3_CN_NAME AS PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
		 T1.SOFTWARE_MARK
    FROM BASE_INDEX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.PROD_CODE = T2.PROD_CODE
     AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND T1.REPLACE_RELATION_NAME = T2.REPLACE_RELATION_NAME
     AND T1.REPLACE_RELATION_TYPE = T2.REPLACE_RELATION_TYPE
     AND T1.RELATION_TYPE = T2.RELATION_TYPE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   GROUP BY T1.PERIOD_ID,
            T1.PERIOD_YEAR,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.LV2_CODE,
            T1.LV2_CN_NAME,
            T1.LV3_CODE,
            T1.LV3_CN_NAME,
            T1.LV4_CODE,
            T1.LV4_CN_NAME,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
			T1.SOFTWARE_MARK;
			
 --写入日志
 V_EXCEPTION_FLAG	:= 9;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 9,
  F_CAL_LOG_DESC => 'LV4指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
----LV3
  WITH BASE_INDEX AS
   (SELECT PERIOD_ID,
           PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           LV3_CODE,
           LV3_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_INDEX,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM DM_REPL_COST_INDEX_TEMP
     WHERE GROUP_LEVEL = 'LV4'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
		   GROUP_CN_NAME,
           WEIGHT_RATE,
		   PARENT_CODE,
           REGION_CODE,
           REPOFFICE_CODE,
           BG_CODE,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM REPL_WEIGHT_TEMP
     WHERE GROUP_LEVEL = 'LV4') 
   INSERT INTO DM_REPL_COST_INDEX_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      LV2_CODE,
      LV2_CN_NAME,
      PROD_CODE,
      PROD_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
	  PARENT_CODE,
	  PARENT_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
	  SOFTWARE_MARK)
  SELECT T1.PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV2_CODE,
         T1.LV2_CN_NAME,
         T1.LV3_CODE AS PROD_CODE,
         T1.LV3_CN_NAME AS PROD_CN_NAME,
         T1.LV3_CODE AS GROUP_CODE,
         T1.LV3_CN_NAME AS GROUP_CN_NAME,
         'LV3' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 T1.LV2_CODE AS PARENT_CODE,
		 T1.LV2_CN_NAME AS PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
		 T1.SOFTWARE_MARK
    FROM BASE_INDEX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   GROUP BY T1.PERIOD_ID,
            T1.PERIOD_YEAR,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.LV2_CODE,
            T1.LV2_CN_NAME,
            T1.LV3_CODE,
            T1.LV3_CN_NAME,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
			T1.SOFTWARE_MARK;
			
 --写入日志
 V_EXCEPTION_FLAG	:= 10;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 10,
  F_CAL_LOG_DESC => 'LV3指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
----LV2
  WITH BASE_INDEX AS
   (SELECT PERIOD_ID,
           PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           LV2_CODE,
           LV2_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_INDEX,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM DM_REPL_COST_INDEX_TEMP
     WHERE GROUP_LEVEL = 'LV3'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
		   GROUP_CN_NAME,
           WEIGHT_RATE,
		   PARENT_CODE,
           REGION_CODE,
           REPOFFICE_CODE,
           BG_CODE,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM REPL_WEIGHT_TEMP
     WHERE GROUP_LEVEL = 'LV3') 
   INSERT INTO DM_REPL_COST_INDEX_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      LV1_CODE,
      LV1_CN_NAME,
      PROD_CODE,
      PROD_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
	  PARENT_CODE,
	  PARENT_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
	  SOFTWARE_MARK)
  SELECT T1.PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE,
         T1.LV1_CN_NAME,
         T1.LV2_CODE AS PROD_CODE,
         T1.LV2_CN_NAME AS PROD_CN_NAME,
         T1.LV2_CODE AS GROUP_CODE,
         T1.LV2_CN_NAME AS GROUP_CN_NAME,
         'LV2' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 T1.LV1_CODE AS PARENT_CODE,
		 T1.LV1_CN_NAME AS PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
		 T1.SOFTWARE_MARK
    FROM BASE_INDEX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   GROUP BY T1.PERIOD_ID,
            T1.PERIOD_YEAR,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.LV2_CODE,
            T1.LV2_CN_NAME,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
			T1.SOFTWARE_MARK;
			
 --写入日志
 V_EXCEPTION_FLAG	:= 11;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 11,
  F_CAL_LOG_DESC => 'LV2指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
			
----LV1
  WITH BASE_INDEX AS
   (SELECT PERIOD_ID,
           PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           LV1_CODE,
           LV1_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_INDEX,
		   PARENT_CODE,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM DM_REPL_COST_INDEX_TEMP
     WHERE GROUP_LEVEL = 'LV2'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
		   GROUP_CN_NAME,
           WEIGHT_RATE,
		   PARENT_CODE,
           REGION_CODE,
           REPOFFICE_CODE,
           BG_CODE,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM REPL_WEIGHT_TEMP
     WHERE GROUP_LEVEL = 'LV2') 
   INSERT INTO DM_REPL_COST_INDEX_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      LV0_CODE,
      LV0_CN_NAME,
      PROD_CODE,
      PROD_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
	  PARENT_CODE,
	  PARENT_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
	  SOFTWARE_MARK)
  SELECT T1.PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.LV0_CODE,
         T1.LV0_CN_NAME,
         T1.LV1_CODE AS PROD_CODE,
         T1.LV1_CN_NAME AS PROD_CN_NAME,
         T1.LV1_CODE AS GROUP_CODE,
         T1.LV1_CN_NAME AS GROUP_CN_NAME,
         'LV1' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 T1.LV0_CODE AS PARENT_CODE,
		 T1.LV0_CN_NAME AS PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
		 T1.SOFTWARE_MARK
    FROM BASE_INDEX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   GROUP BY T1.PERIOD_ID,
            T1.PERIOD_YEAR,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.LV1_CODE,
            T1.LV1_CN_NAME,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
			T1.SOFTWARE_MARK;
			
 --写入日志
 V_EXCEPTION_FLAG	:= 12;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 12,
  F_CAL_LOG_DESC => 'LV1指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
			
			
----LV0
  WITH BASE_INDEX AS
   (SELECT PERIOD_ID,
           PERIOD_YEAR,
           LV0_CODE,
           LV0_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           COST_INDEX,
		   PARENT_CODE,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM DM_REPL_COST_INDEX_TEMP
     WHERE GROUP_LEVEL = 'LV1'),
  BASE_WEIGHT AS
   (SELECT GROUP_CODE,
		   GROUP_CN_NAME,
           WEIGHT_RATE,
		   PARENT_CODE,
           REGION_CODE,
           REPOFFICE_CODE,
           BG_CODE,
           OVERSEA_FLAG,
		   SOFTWARE_MARK
      FROM REPL_WEIGHT_TEMP
     WHERE GROUP_LEVEL = 'LV1') 
   INSERT INTO DM_REPL_COST_INDEX_TEMP
     (PERIOD_ID,
      PERIOD_YEAR,
      PROD_CODE,
      PROD_CN_NAME,
      GROUP_CODE,
      GROUP_CN_NAME,
      GROUP_LEVEL,
      COST_INDEX,
	  PARENT_CODE,
	  PARENT_CN_NAME,
      REGION_CODE,
      REGION_CN_NAME,
      REPOFFICE_CODE,
      REPOFFICE_CN_NAME,
      BG_CODE,
      BG_CN_NAME,
      OVERSEA_FLAG,
	  SOFTWARE_MARK)
  SELECT T1.PERIOD_ID,
         T1.PERIOD_YEAR,
         T1.LV0_CODE AS PROD_CODE,
         T1.LV0_CN_NAME AS PROD_CN_NAME,
         T1.LV0_CODE AS GROUP_CODE,
         T1.LV0_CN_NAME AS GROUP_CN_NAME,
         'LV0' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
		 '' AS PARENT_CODE,
		 '' AS PARENT_CN_NAME,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.BG_CODE,
         T1.BG_CN_NAME,
         T1.OVERSEA_FLAG,
		 T1.SOFTWARE_MARK
    FROM BASE_INDEX T1
    LEFT JOIN BASE_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.REGION_CODE = T2.REGION_CODE
     AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
	 AND T1.PARENT_CODE = T2.PARENT_CODE
	 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
   GROUP BY T1.PERIOD_ID,
            T1.PERIOD_YEAR,
            T1.LV0_CODE,
            T1.LV0_CN_NAME,
            T1.REGION_CODE,
            T1.REGION_CN_NAME,
            T1.REPOFFICE_CODE,
            T1.REPOFFICE_CN_NAME,
            T1.BG_CODE,
            T1.BG_CN_NAME,
            T1.OVERSEA_FLAG,
			T1.SOFTWARE_MARK;
			
 --写入日志
 V_EXCEPTION_FLAG	:= 13;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 13,
  F_CAL_LOG_DESC => 'LV0指数计算完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
	

V_SQL:='	

DELETE FROM '||V_TO_INDEX_TABLE||' WHERE VERSION_ID = '||V_VERSION||';

  INSERT INTO '||V_TO_INDEX_TABLE||'
    (VERSION_ID,
     BASE_PERIOD_ID,
     PERIOD_ID,
     PERIOD_YEAR,
     '||V_PROD_PART||'
     GROUP_CODE,
     GROUP_CN_NAME,
     GROUP_LEVEL,
     COST_INDEX,
     PARENT_CODE,
     PARENT_CN_NAME,
     REGION_CODE,
     REGION_CN_NAME,
     REPOFFICE_CODE,
     REPOFFICE_CN_NAME,
     BG_CODE,
     BG_CN_NAME,
     OVERSEA_FLAG,
     REPLACE_RELATION_NAME,
     REPLACE_RELATION_TYPE,
     RELATION_TYPE,
	 SOFTWARE_MARK,
	 CODE_TYPE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
    SELECT '||V_VERSION||' AS VERSION_ID,
           '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
           PERIOD_ID,
           PERIOD_YEAR,
           PROD_CODE,
           PROD_CN_NAME,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           COST_INDEX,
           PARENT_CODE,
           PARENT_CN_NAME,
           REGION_CODE,
           REGION_CN_NAME,
           REPOFFICE_CODE,
           REPOFFICE_CN_NAME,
           BG_CODE,
           BG_CN_NAME,
           OVERSEA_FLAG,
           REPLACE_RELATION_NAME,
           REPLACE_RELATION_TYPE,
           RELATION_TYPE,
		   SOFTWARE_MARK,
		   ''NEW'' AS CODE_TYPE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM DM_REPL_COST_INDEX_TEMP;
	  ';
  EXECUTE V_SQL;
  
 --写入日志
 V_EXCEPTION_FLAG	:= 13;
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 13,
  F_CAL_LOG_DESC => '指数结果表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_RESULT_STATUS,
  F_ERRBUF => 'SUCCESS'); 
  
  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_EXCEPTION_FLAG,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_EXCEPTION_FLAG||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 
$$
/

