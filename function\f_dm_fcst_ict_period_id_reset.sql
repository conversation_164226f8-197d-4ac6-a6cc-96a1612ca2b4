-- Name: f_dm_fcst_ict_period_id_reset; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_period_id_reset(parameters_v text, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
DECLARE

	v_table varchar(50);
	v_table_month varchar(50);
	v_table_temp varchar(100);
	v_table_month_temp varchar(100);
	v_column1 varchar(50);
	v_column2 varchar(50);
	v_conditions text;
	v_conditions_0 text;
	v_conditions_1 text;
	v_conditions_2 text;
	v_conditions_3 text;
	v_conditions_4 text;
	v_conditions_5 text;
	v_conditions_6 text;
	v_conditions_over text:='';
	v_sql text:='';
	v_sql_m text:='';
	v_sql_temp text:='';
	v_num varchar(20):= to_char(now(),'YYYYMMDD');
	v_round int := 1;		
	parameters_in jsonb:=parameters_v::jsonb;
	
	v_error_num int := 0;
	
	--传入参，解析JSONB格式入参
	--页面
	v_page_type varchar(50);
	
	v_page_type_month  varchar(50);
	--成本类型
	v_cost_type varchar(50);
	--PBI目录树
	v_granularity_type varchar(10);
	
	V_VERSION_ID	INT;--	版本ID
	V_PERIOD_YEAR	INT;--	会计年
	V_PERIOD_ID	INT;--	会计月
	--V_PROD_RND_TEAM_CODE	VARCHAR(50);--	重量级团队编码
	--V_PROD_RD_TEAM_CN_NAME	VARCHAR(200);--	重量级团队中文名称
	V_DIMENSION_CODE	VARCHAR(200);--	量纲编码
	--V_DIMENSION_CN_NAME	VARCHAR(200);--	量纲中文名称
	V_DIMENSION_SUBCATEGORY_CODE	VARCHAR(200);--	量纲子类编码
	--V_DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200);--	量纲子类中文名称
	V_DIMENSION_SUB_DETAIL_CODE	VARCHAR(200);--	量纲子类明细编码
	--V_DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200);--	量纲子类明细中文名称
	V_GROUP_CODE	VARCHAR(200);--	各层级编码
	--V_GROUP_CN_NAME	VARCHAR(200);--	各层级中文名称
	V_GROUP_LEVEL	VARCHAR(50);--	各层级（SPART/DIMENSION/SUBCATEGORY/SUB_DETAIL/LV4/LV3/LV2/LV1/LV0）
	V_GROUP_LEVEL_NEXT	VARCHAR(50);
	--V_COST_INDEX	NUMERIC;--	成本指数值
	--V_PARENT_CODE	VARCHAR(200);--	父级编码
	--V_PARENT_CN_NAME	VARCHAR(200);--	父级中文名称
	V_MAIN_FLAG	VARCHAR(2);--	是否主力编码（Y:是；N:不是）
	V_VIEW_FLAG	VARCHAR(20);--	路径FLAG：PROD_SPART(路径一)；DIMENSION(路径二)
	--V_APPEND_FLAG	VARCHAR(2);--	是否补齐字段
	V_REGION_CODE	VARCHAR(50);--	地区部编码
	--V_REGION_CN_NAME	VARCHAR(200);--	地区部中文名称
	V_REPOFFICE_CODE	VARCHAR(50);--	代表处编码
	--V_REPOFFICE_CN_NAME	VARCHAR(200);--	代表处中文名称
	V_BG_CODE	VARCHAR(50);--	BG编码
	--V_BG_CN_NAME	VARCHAR(200);--	BG中文名称
	V_OVERSEA_FLAG	VARCHAR(10);--	国内海外标识
	V_CREATED_BY	BIGINT:=-1;--	创建人
	V_CREATION_DATE	TIMESTAMP:=now();--	创建时间
	V_LAST_UPDATED_BY	BIGINT:=-1;--	修改人
	V_LAST_UPDATE_DATE	TIMESTAMP:=now();--	修改时间
	V_DEL_FLAG	VARCHAR(2):= 'N';--	删除标识(未删除：N，已删除：Y)
	V_CODE_ATTRIBUTES	VARCHAR(20);--	编码属性
	V_BASE_PERIOD_ID INT; --默认基期
	
	V_BASE_PERIOD_ID_BEFORE INT; --生成数据前默认基期
	
	V_SOFTWARE_MARK  VARCHAR(30); --软硬件标识
	
	V_CUSTOM_ID  VARCHAR(200); --汇总组合
	V_COMBIDLIST TEXT ;    --汇总组合

	V_SPART_CODE VARCHAR(200);
	
	V_OLD_SPART_CODE VARCHAR(200);
	V_NEW_SPART_CODE VARCHAR(200);
	V_REPLACE_RELATION_NAME VARCHAR(200);
	V_REPLACE_RELATION_TYPE VARCHAR(50);
	V_RELATION_TYPE VARCHAR(50);
	
	V_PROD_RND_TEAM_CODE_LIST text; --prodRndTeamCodeList 存放LV0到LV4的数据
	V_PROD_RND_TEAM_CODE_LIST_A VARCHAR[]; --设置数组类型
	
	V_GROUP_CODE_LIST text; --groupCodeList 存放SPART和量纲数据
	V_PARENT_CODE_LIST text;
	
	v_cost_type_a VARCHAR[];
	
	v_cost_type_v varchar(10);
	
	v_n int:=2;  --循环次数,说明：编码替代页面的成本类型可以是STD和PSP
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_PERIOD_ID_RESET'; --存储过程名称
	V_STEP_MUM   BIGINT := 0; --步骤号
BEGIN
--日志开始
PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
	 
--解析参数 解析JSONB格式入参
	v_page_type := parameters_in->>'pageType';
	v_cost_type:= parameters_in->>'costType';
	v_granularity_type:= parameters_in->>'granularityType';
	--v_begin_date:=parameters_in->>'BEGIN_DATE';
	--raise notice '%',v_granularity_type;
	V_VERSION_ID:= parameters_in->>'versionId';
	
	--V_PERIOD_YEAR:= parameters_in->>'PERIOD_YEAR';
	--V_PERIOD_ID:= parameters_in->>'PERIOD_ID';
	--V_PROD_RND_TEAM_CODE:= parameters_in->>'PROD_RND_TEAM_CODE';
	--V_PROD_RD_TEAM_CN_NAME:= parameters_in->>'PROD_RD_TEAM_CN_NAME';
	V_DIMENSION_CODE:= parameters_in->>'dimensionCode';
	--V_DIMENSION_CN_NAME:= parameters_in->>'DIMENSION_CN_NAME';
	V_DIMENSION_SUBCATEGORY_CODE:= parameters_in->>'dimensionSubCategoryCode';
	--V_DIMENSION_SUBCATEGORY_CN_NAME:= parameters_in->>'DIMENSION_SUBCATEGORY_CN_NAME';
	V_DIMENSION_SUB_DETAIL_CODE:= parameters_in->>'dimensionSubDetailCode';
	--V_DIMENSION_SUB_DETAIL_CN_NAME:= parameters_in->>'DIMENSION_SUB_DETAIL_CN_NAME';
	--V_GROUP_CODE:= parameters_in->>'GROUP_CODE';
	--V_GROUP_CN_NAME:= parameters_in->>'GROUP_CN_NAME';
	V_GROUP_LEVEL:= parameters_in->>'groupLevel';
	--V_COST_INDEX:= parameters_in->>'COST_INDEX';
	--V_PARENT_CODE:= parameters_in->>'PARENT_CODE';
	--V_PARENT_CN_NAME:= parameters_in->>'PARENT_CN_NAME';
	V_MAIN_FLAG:= parameters_in->>'mainFlag';
	V_VIEW_FLAG:= parameters_in->>'viewFlag';
	--V_APPEND_FLAG:= parameters_in->>'APPEND_FLAG';
	V_REGION_CODE:= parameters_in->>'regionCode';
	--V_REGION_CN_NAME:= parameters_in->>'REGION_CN_NAME';
	V_REPOFFICE_CODE:= parameters_in->>'repofficeCode';
	--V_REPOFFICE_CN_NAME:= parameters_in->>'REPOFFICE_CN_NAME';
	V_BG_CODE:= parameters_in->>'bgCode';
	--V_BG_CN_NAME:= parameters_in->>'BG_CN_NAME';
	V_OVERSEA_FLAG:= parameters_in->>'overseaFlag';
	V_CODE_ATTRIBUTES:= parameters_in->>'codeAttributes';
	V_BASE_PERIOD_ID:= parameters_in->>'basePeriodId';
	V_SOFTWARE_MARK := parameters_in->>'softwareMark';
	
	
	V_CUSTOM_ID := parameters_in->> 'customIdList';  
	V_COMBIDLIST := parameters_in->> 'combIdList';     --汇总组合
	
	V_SPART_CODE := parameters_in->>'spartCode';
	
	V_OLD_SPART_CODE := parameters_in->>'oldSpartCode';
	V_NEW_SPART_CODE := parameters_in->>'newSpartCode';
	V_REPLACE_RELATION_NAME := parameters_in->>'replaceRelationName';
	V_REPLACE_RELATION_TYPE := parameters_in->>'replaceRelationType';
	V_RELATION_TYPE := parameters_in->>'relationType';
	
	V_PROD_RND_TEAM_CODE_LIST := parameters_in->>'prodRndTeamCodeList'; --prodRndTeamCodeList
	
	V_GROUP_CODE_LIST := parameters_in->>'groupCodeList'; --groupCodeList
	V_PARENT_CODE_LIST := parameters_in->>'groupCodeList'; --parentCodeList
	
	--勾稽管理的cost_type是数组类型，处理数组类型数据
	v_cost_type := replace(replace(replace(replace(v_cost_type,'"',''),' ',''),']',''),'[','');
	V_PROD_RND_TEAM_CODE_LIST := replace(replace(replace(replace(V_PROD_RND_TEAM_CODE_LIST,'"',''),' ',''),']',''),'[','');
	V_GROUP_CODE_LIST := replace(replace(replace(replace(V_GROUP_CODE_LIST,'"',''),' ',''),']',''),'[','');
	V_PARENT_CODE_LIST := replace(replace(replace(replace(V_PARENT_CODE_LIST,'"',''),' ',''),']',''),'[','');
	
	raise notice 'V_PROD_RND_TEAM_CODE_LIST:%',V_PROD_RND_TEAM_CODE_LIST;
	raise notice 'V_GROUP_CODE_LIST:%',V_GROUP_CODE_LIST;
	raise notice 'V_PARENT_CODE_LIST:%',V_PARENT_CODE_LIST;

	--校验传参必填字段是否为空
	if v_cost_type is null or v_cost_type = '' or v_granularity_type is null or v_granularity_type = '' 
	   or V_REGION_CODE is null or V_REGION_CODE = '' or V_REPOFFICE_CODE is null or V_REPOFFICE_CODE = '' 
		 or V_OVERSEA_FLAG is null or V_OVERSEA_FLAG = '' or V_BG_CODE is null or V_BG_CODE = ''
		 or V_BASE_PERIOD_ID is null or V_BASE_PERIOD_ID = '' OR V_SOFTWARE_MARK is  null or V_SOFTWARE_MARK = ''	THEN
		 
	 V_STEP_MUM := V_STEP_MUM + 1 ;
	  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '必填参数为空，请检查参数是否有值！',
		 F_ERRBUF => 'ERROR');
		return '必填参数为空';
	end if;
		
	raise notice '汇总组合标识：%',V_COMBIDLIST;
		
	--确定公用字段
	if v_granularity_type = 'IRB' then 
		v_column1 := 'PROD_RND_TEAM_CODE';
		v_column2 := 'PROD_RD_TEAM_CN_NAME';
	elsif v_granularity_type = 'INDUS' then 
		v_column1 := 'INDUSTRY_CATG_CODE';
		v_column2 := 'INDUSTRY_CATG_CN_NAME';
	elsif v_granularity_type = 'PROD' then 
		v_column1 := 'PROD_LIST_CODE';
		v_column2 := 'PROD_LIST_CN_NAME';
	end if;
	
	v_cost_type_a := regexp_split_to_array(v_cost_type,',');
	V_PROD_RND_TEAM_CODE_LIST_A := regexp_split_to_array(V_PROD_RND_TEAM_CODE_LIST,',');
		
	--定义标签，如果存在多个成本类型，循环执行，处理勾稽管理里面存在成本类型选择多个的场景
	<<calculated>>
	for n in 1..v_n LOOP
	  v_cost_type_v := v_cost_type_a[n];
		
		--场景：PSP和STD对应LV层级数组
		--if v_page_type = 'MIX' then 
		--	V_PROD_RND_TEAM_CODE_LIST := V_PROD_RND_TEAM_CODE_LIST_A[n];
		--end if;
		
		raise notice '%', v_cost_type_v;

		IF v_cost_type_v IS NULL OR v_cost_type_v = '' THEN
			EXIT; 
			
		ELSIF v_cost_type_v = 'STD' THEN
				v_software_mark = 'HARDWARE';
		END IF; 

	--通用条件
	v_conditions = 'VERSION_ID ='''||V_VERSION_ID||''''
								 ' and REGION_CODE ='''||V_REGION_CODE||''''
								 ' and REPOFFICE_CODE ='''||V_REPOFFICE_CODE||''''
								 ' and BG_CODE ='''||V_BG_CODE||''''
								 ' and OVERSEA_FLAG ='''||V_OVERSEA_FLAG||''''
								 ' and SOFTWARE_MARK = '''||V_SOFTWARE_MARK||''''
								 --' and MAIN_FLAG ='''||V_MAIN_FLAG||''''
								 --' and VIEW_FLAG ='''||V_VIEW_FLAG||''''
								 ;
	
	--创建临时表存放计算后的数据 28 23
	v_sql_temp := 'CREATE TEMPORARY TABLE table_temp (
    VERSION_ID	INT
		,PERIOD_YEAR	INT
		,PERIOD_ID	INT
		,column1	VARCHAR(50)
		,column2	VARCHAR(200)
		,GROUP_CODE	VARCHAR(200)
		,GROUP_CN_NAME	VARCHAR(200)
		,GROUP_LEVEL	VARCHAR(50)
		,COST_INDEX	NUMERIC
		,PARENT_CODE	VARCHAR(200)
		,PARENT_CN_NAME	VARCHAR(200)
		$
		,REGION_CODE	VARCHAR(50)
		,REGION_CN_NAME	VARCHAR(200)
		,REPOFFICE_CODE	VARCHAR(50)
		,REPOFFICE_CN_NAME	VARCHAR(200)
		,BG_CODE	VARCHAR(50)
		,BG_CN_NAME	VARCHAR(200)
		,OVERSEA_FLAG	VARCHAR(10)
		,BASE_PERIOD_ID INT
		,SOFTWARE_MARK VARCHAR(30)
		,GRANULARITY_TYPE VARCHAR(30)
		
		)
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN';
	
	--抽出公共的sql
	v_conditions_0 :='VERSION_ID
		,PERIOD_YEAR
		,PERIOD_ID
		,column1
		,column2
		,GROUP_CODE
		,GROUP_CN_NAME
		,GROUP_LEVEL
		,PARENT_CODE
		,PARENT_CN_NAME
		$
		,REGION_CODE
		,REGION_CN_NAME
		,REPOFFICE_CODE
		,REPOFFICE_CN_NAME
		,SOFTWARE_MARK
		,BG_CODE
		,BG_CN_NAME
		,OVERSEA_FLAG
		,BASE_PERIOD_ID
		,COST_INDEX';
		
	
	v_conditions_4 :='VERSION_ID
		,PERIOD_YEAR
		,PERIOD_ID
		,column1
		,column2
		,GROUP_CODE
		,GROUP_CN_NAME
		,GROUP_LEVEL
		,PARENT_CODE
		,PARENT_CN_NAME
		$
		,REGION_CODE
		,REGION_CN_NAME
		,REPOFFICE_CODE
		,REPOFFICE_CN_NAME
		,SOFTWARE_MARK
		,GRANULARITY_TYPE
		,BG_CODE
		,BG_CN_NAME
		,OVERSEA_FLAG
		,BASE_PERIOD_ID
		,COST_INDEX';
		
		
		
	v_conditions_2:='a.VERSION_ID
		,a.PERIOD_YEAR
		,a.PERIOD_ID
		,a.column1
		,a.column2
		,a.GROUP_CODE
		,a.GROUP_CN_NAME
		,a.GROUP_LEVEL
		,a.PARENT_CODE
		,a.PARENT_CN_NAME
		$
		,a.REGION_CODE
		,a.REGION_CN_NAME
		,a.REPOFFICE_CODE
		,a.REPOFFICE_CN_NAME
		,a.SOFTWARE_MARK
		,'''||V_GRANULARITY_TYPE||''' as GRANULARITY_TYPE
		,a.BG_CODE
		,a.BG_CN_NAME
		,a.OVERSEA_FLAG';
	
	v_conditions_5:='a.VERSION_ID
		,a.PERIOD_YEAR
		,a.PERIOD_ID
		,a.column1
		,a.column2
		,a.GROUP_CODE
		,a.GROUP_CN_NAME
		,a.GROUP_LEVEL
		,a.PARENT_CODE
		,a.PARENT_CN_NAME
		$
		,a.REGION_CODE
		,a.REGION_CN_NAME
		,a.REPOFFICE_CODE
		,a.REPOFFICE_CN_NAME
		,a.SOFTWARE_MARK
		,a.GRANULARITY_TYPE
		,a.BG_CODE
		,a.BG_CN_NAME
		,a.OVERSEA_FLAG';
	
	v_conditions_6:='a.VERSION_ID
		,a.PERIOD_YEAR
		,a.PERIOD_ID
		,a.column1
		,a.column2
		,a.GROUP_CODE
		,a.GROUP_CN_NAME
		,a.GROUP_LEVEL
		,a.PARENT_CODE
		,a.PARENT_CN_NAME
		$
		,a.REGION_CODE
		,a.REGION_CN_NAME
		,a.REPOFFICE_CODE
		,a.REPOFFICE_CN_NAME
		,a.SOFTWARE_MARK
		,a.BG_CODE
		,a.BG_CN_NAME
		,a.OVERSEA_FLAG';
	

	if V_COMBIDLIST = '[]' OR V_COMBIDLIST IS NULL	THEN 

	
	v_sql_temp := replace(replace(v_sql_temp,'column1',v_column1),'column2',v_column2);
	v_conditions_0 := replace(replace(v_conditions_0,'column1',v_column1),'column2',v_column2);
	v_conditions_4 := replace(replace(v_conditions_4,'column1',v_column1),'column2',v_column2);
	v_conditions_2 := replace(replace(v_conditions_2,'column1',v_column1),'column2',v_column2);
	v_conditions_5 := replace(replace(v_conditions_5,'column1',v_column1),'column2',v_column2);
	v_conditions_6 := replace(replace(v_conditions_6,'column1',v_column1),'column2',v_column2);
	raise notice '非汇总组合层替换完成';
	
	
	
	elsif V_COMBIDLIST <>  '[]' THEN 
	v_sql_temp := replace(replace(replace(replace(v_sql_temp,',column1	VARCHAR(50)',''),',column2	VARCHAR(200)',''),',PARENT_CODE	VARCHAR(200)',''),',PARENT_CN_NAME	VARCHAR(200)','');
	v_conditions_0 := replace(replace(replace(replace(v_conditions_0,',column1',''),',column2',''),',PARENT_CODE',''),',PARENT_CN_NAME','');
	v_conditions_4 := replace(replace(replace(replace(v_conditions_4,',column1',''),',column2',''),',PARENT_CODE',''),',PARENT_CN_NAME','');
	v_conditions_2 := replace(replace(replace(replace(v_conditions_2,',a.column1',''),',a.column2',''),',a.PARENT_CODE',''),',a.PARENT_CN_NAME','');
	v_conditions_5 := replace(replace(replace(replace(v_conditions_5,',a.column1',''),',a.column2',''),',a.PARENT_CODE',''),',a.PARENT_CN_NAME','');
	v_conditions_6 := replace(replace(replace(replace(v_conditions_6,',a.column1',''),',a.column2',''),',a.PARENT_CODE',''),',a.PARENT_CN_NAME','');
	raise notice '汇总组合层替换完成';
	
		
	
	end if  ;  --汇总组合不需要PBI字段
	
	
-- 	if v_cost_type_v = 'STD' then 
-- 		goto calculated;
-- 	end if;
	
	
	--return 'duandian';
	--编码替代如果选择SPART层级，并且REPLACE_RELATION_NAME则跑月度分析数据
	V_NEW_SPART_CODE := replace(replace(replace(replace(V_NEW_SPART_CODE,'"',''),' ',''),']',''),'[','');
	V_OLD_SPART_CODE := replace(replace(replace(replace(V_OLD_SPART_CODE,'"',''),' ',''),']',''),'[','');
	
	V_REPLACE_RELATION_NAME:=TRIM(V_REPLACE_RELATION_NAME); --去空格
	V_NEW_SPART_CODE:=TRIM(V_NEW_SPART_CODE);
	V_OLD_SPART_CODE:=TRIM(V_OLD_SPART_CODE);
	if v_page_type = 'CODE_REPLACE' and (V_REPLACE_RELATION_NAME is null or V_REPLACE_RELATION_NAME = '') and (V_NEW_SPART_CODE is not null or V_OLD_SPART_CODE is not null)  then 
		v_page_type_month:='true';
	else 
		v_page_type_month:='false';
	end if;
	
	
	raise notice 'v_page_type_month :%',v_page_type_month;
	
	--校验传入是哪个页面， （月度分析+勾稽管理）/编码替代 --1. 月度分析 2. 勾稽管理 3. 编码替代
	if (v_page_type = 'MONTH' AND (V_COMBIDLIST = '[]'  OR V_COMBIDLIST IS NULL) )  or (v_page_type = 'MIX'  and (v_combidlist = '[]' OR V_COMBIDLIST IS NULL )) or (v_page_type = 'CODE_REPLACE' and v_page_type_month = 'true') then
		
		--根据传入的成本类型和PBI目录树确定物理表
		v_table:='DM_FCST_ICT_'||v_cost_type_v||'_'||v_granularity_type||'_MON_COST_IDX_T';
		v_table_temp:='TEMP_'||v_table||'_'||v_num;
		--v_sql_temp:=replace(v_sql_temp,'table_temp',v_table_temp);
		if v_page_type = 'MONTH'  then 
			v_table_month:='';
			v_table_month_temp:='';
		else  --勾稽管理，编码替代要跑MON和YTD -- 10月版本新增
			-- FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_IRB_YTD_COST_IDX_T
			-- FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_PROD_YTD_COST_IDX_T
			-- FIN_DM_OPT_FOI.DM_FCST_ICT_PSP_INDUS_YTD_COST_IDX_T
			-- FIN_DM_OPT_FOI.DM_FCST_ICT_STD_IRB_YTD_COST_IDX_T
			-- FIN_DM_OPT_FOI.DM_FCST_ICT_STD_PROD_YTD_COST_IDX_T
			-- FIN_DM_OPT_FOI.DM_FCST_ICT_STD_INDUS_YTD_COST_IDX_T
			v_table_month:='DM_FCST_ICT_'||v_cost_type_v||'_'||v_granularity_type||'_YTD_COST_IDX_T';
			v_table_month_temp:='TEMP_'||v_table_month||'_'||v_num;
			
		end if;
		
		if V_MAIN_FLAG is null or V_MAIN_FLAG = '' then 
			V_STEP_MUM := V_STEP_MUM + 1;
			PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
			(F_SP_NAME => V_SP_NAME,
			 F_STEP_NUM => V_STEP_MUM,
			 F_CAL_LOG_DESC => '异常传值：MAIN_FLAG为空,页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
			 F_ERRBUF => 'ERROR');
			--return '异常传值：MAIN_FLAG为空';
		else 
			v_conditions := v_conditions||' and MAIN_FLAG ='''||V_MAIN_FLAG||'''';
		end if;
		
		if V_VIEW_FLAG is null or V_VIEW_FLAG = '' then 
			V_STEP_MUM := V_STEP_MUM + 1;
			PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
			(F_SP_NAME => V_SP_NAME,
			 F_STEP_NUM => V_STEP_MUM,
			 F_CAL_LOG_DESC => '异常传值：VIEW_FLAG为空,页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
			 F_ERRBUF => 'ERROR');
			--return '异常传值：VIEW_FLAG为空';
		else 
			v_conditions := v_conditions||' and VIEW_FLAG ='''||V_VIEW_FLAG||'''';
		end if;
																	
		--组合临时表脚本
		v_sql_temp := replace(v_sql_temp,'$',',DIMENSION_CODE	VARCHAR(200)
		,DIMENSION_CN_NAME	VARCHAR(200)
		,DIMENSION_SUBCATEGORY_CODE	VARCHAR(200)
		,DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200)
		,DIMENSION_SUB_DETAIL_CODE	VARCHAR(200)
		,DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200)
		,CODE_ATTRIBUTES	VARCHAR(20)
		,MAIN_FLAG	VARCHAR(2)
		,VIEW_FLAG	VARCHAR(20)
		,APPEND_FLAG	VARCHAR(2)');
		
		v_conditions_0 := replace(v_conditions_0,'$',',DIMENSION_CODE
		,DIMENSION_CN_NAME
		,DIMENSION_SUBCATEGORY_CODE
		,DIMENSION_SUBCATEGORY_CN_NAME
		,DIMENSION_SUB_DETAIL_CODE
		,DIMENSION_SUB_DETAIL_CN_NAME
		,CODE_ATTRIBUTES 
		,MAIN_FLAG
		,VIEW_FLAG
		,APPEND_FLAG');
		
		v_conditions_4 := replace(v_conditions_4,'$',',DIMENSION_CODE
		,DIMENSION_CN_NAME
		,DIMENSION_SUBCATEGORY_CODE
		,DIMENSION_SUBCATEGORY_CN_NAME
		,DIMENSION_SUB_DETAIL_CODE
		,DIMENSION_SUB_DETAIL_CN_NAME
		,CODE_ATTRIBUTES 
		,MAIN_FLAG
		,VIEW_FLAG
		,APPEND_FLAG');
		
		v_conditions_2 := replace(v_conditions_2,'$',',a.DIMENSION_CODE
		,a.DIMENSION_CN_NAME
		,a.DIMENSION_SUBCATEGORY_CODE
		,a.DIMENSION_SUBCATEGORY_CN_NAME
		,a.DIMENSION_SUB_DETAIL_CODE
		,a.DIMENSION_SUB_DETAIL_CN_NAME
		,a.CODE_ATTRIBUTES 
		,a.MAIN_FLAG
		,a.VIEW_FLAG
		,a.APPEND_FLAG');
		
		v_conditions_5 := replace(v_conditions_5,'$',',a.DIMENSION_CODE
		,a.DIMENSION_CN_NAME
		,a.DIMENSION_SUBCATEGORY_CODE
		,a.DIMENSION_SUBCATEGORY_CN_NAME
		,a.DIMENSION_SUB_DETAIL_CODE
		,a.DIMENSION_SUB_DETAIL_CN_NAME
		,a.CODE_ATTRIBUTES 
		,a.MAIN_FLAG
		,a.VIEW_FLAG
		,a.APPEND_FLAG');
		
		v_conditions_6 := replace(v_conditions_6,'$',',a.DIMENSION_CODE
		,a.DIMENSION_CN_NAME
		,a.DIMENSION_SUBCATEGORY_CODE
		,a.DIMENSION_SUBCATEGORY_CN_NAME
		,a.DIMENSION_SUB_DETAIL_CODE
		,a.DIMENSION_SUB_DETAIL_CN_NAME
		,a.CODE_ATTRIBUTES 
		,a.MAIN_FLAG
		,a.VIEW_FLAG
		,a.APPEND_FLAG');
		
		
		v_conditions_3:='on (a.VERSION_ID = b.VERSION_ID and a.'||v_column1||' = b.'||v_column1||' and nvl(a.DIMENSION_CODE,''NULLS'') = nvl(b.DIMENSION_CODE,''NULLS'') and nvl(a.DIMENSION_SUBCATEGORY_CODE,''NULLS'') = nvl(b.DIMENSION_SUBCATEGORY_CODE,''NULLS'') and nvl(a.DIMENSION_SUB_DETAIL_CODE,''NULLS'') = nvl(b.DIMENSION_SUB_DETAIL_CODE,''NULLS'') and a.GROUP_CODE = b.GROUP_CODE and a.GROUP_LEVEL = b.GROUP_LEVEL and nvl(a.PARENT_CODE,''NULLS'') = nvl(b.PARENT_CODE,''NULLS'') and a.SOFTWARE_MARK = b.SOFTWARE_MARK and a.MAIN_FLAG = b.MAIN_FLAG and a.VIEW_FLAG = b.VIEW_FLAG and nvl(a.APPEND_FLAG,''NULLS'') = nvl(b.APPEND_FLAG,''NULLS'')  and a.REGION_CODE = b.REGION_CODE and a.REPOFFICE_CODE = b.REPOFFICE_CODE and a.BG_CODE = b.BG_CODE and a.OVERSEA_FLAG = b.OVERSEA_FLAG and nvl(a.CODE_ATTRIBUTES,''NULLS'') = nvl(b.CODE_ATTRIBUTES,''NULLS'') conditions_4) ';
																	
		
	elsif v_page_type = 'CODE_REPLACE' and v_page_type_month = 'false' then --编码替代
		
		--根据传入的成本类型和PBI目录树确定物理表
		v_table:='DM_FCST_ICT_'||v_cost_type_v||'_'||v_granularity_type||'_MON_REPL_COST_IDX_T';
		v_table_month:='DM_FCST_ICT_'||v_cost_type_v||'_'||v_granularity_type||'_YTD_REPL_COST_IDX_T';
		v_table_temp:='TEMP_'||v_table||'_'||v_num;
		v_table_month_temp:='TEMP_'||v_table_month||'_'||v_num;
		--v_sql_temp:=replace(v_sql_temp,'table_temp',v_table_temp);
																	
		raise notice '%',v_conditions;
		
		

		
		--return 'duandian';
		--组合临时表脚本
		v_sql_temp := replace(v_sql_temp,'$',',REPLACE_RELATION_NAME VARCHAR(200)
		,REPLACE_RELATION_TYPE	VARCHAR(50)
		,RELATION_TYPE	VARCHAR(50)
		,CODE_TYPE	VARCHAR(50)');
		
		v_conditions_0 := replace(v_conditions_0,'$',',REPLACE_RELATION_NAME
		,REPLACE_RELATION_TYPE
		,RELATION_TYPE
		,CODE_TYPE');
		
		v_conditions_4 := replace(v_conditions_4,'$',',REPLACE_RELATION_NAME
		,REPLACE_RELATION_TYPE
		,RELATION_TYPE
		,CODE_TYPE');
		
		v_conditions_2 := replace(v_conditions_2,'$',',a.REPLACE_RELATION_NAME
		,a.REPLACE_RELATION_TYPE
		,a.RELATION_TYPE
		,a.CODE_TYPE');
		
		v_conditions_5 := replace(v_conditions_5,'$',',a.REPLACE_RELATION_NAME
		,a.REPLACE_RELATION_TYPE
		,a.RELATION_TYPE
		,a.CODE_TYPE');
		
		v_conditions_6 := replace(v_conditions_6,'$',',a.REPLACE_RELATION_NAME
		,a.REPLACE_RELATION_TYPE
		,a.RELATION_TYPE
		,a.CODE_TYPE');
		
		v_conditions_3:='on (a.VERSION_ID = b.VERSION_ID and a.'||v_column1||' = b.'||v_column1||' and nvl(a.REPLACE_RELATION_NAME,''NULLS'') = nvl(b.REPLACE_RELATION_NAME,''NULLS'') and nvl(a.REPLACE_RELATION_TYPE,''NULLS'') = nvl(b.REPLACE_RELATION_TYPE,''NULLS'') and nvl(a.RELATION_TYPE,''NULLS'') = nvl(b.RELATION_TYPE,''NULLS'') and a.GROUP_CODE = b.GROUP_CODE and a.GROUP_LEVEL = b.GROUP_LEVEL and nvl(a.PARENT_CODE,''NULLS'') = nvl(b.PARENT_CODE,''NULLS'') and nvl(a.CODE_TYPE,''NULLS'') = nvl(b.CODE_TYPE,''NULLS'') and a.REGION_CODE = b.REGION_CODE and a.REPOFFICE_CODE = b.REPOFFICE_CODE and a.BG_CODE = b.BG_CODE and a.OVERSEA_FLAG = b.OVERSEA_FLAG conditions_4) ';
		
	elsif v_page_type in ('MONTH','MIX') AND V_COMBIDLIST <> '[]' then --汇总组合	
	
		--根据传入的成本类型和PBI目录树确定物理表
		v_table:='DM_FCST_ICT_'||v_cost_type_v||'_CUS_MON_COST_IDX_T';
		v_table_month:='DM_FCST_ICT_'||v_cost_type_v||'_CUS_YTD_COST_IDX_T';
		v_table_temp:='TEMP_'||v_table||'_'||v_num;
		v_table_month_temp:='TEMP_'||v_table_month||'_'||v_num;
		
		--组合临时表脚本
		v_sql_temp := replace(v_sql_temp,'$','
		,CODE_ATTRIBUTES	VARCHAR(20)
		,MAIN_FLAG	VARCHAR(2)
		,YTD_FLAG	VARCHAR(2)
		,CUSTOM_ID BIGINT
		,CUSTOM_CN_NAME CHARACTER VARYING(200)');
		
		v_conditions_0 := replace(v_conditions_0,'$','
		,CODE_ATTRIBUTES 
		,MAIN_FLAG
		,YTD_FLAG
		,CUSTOM_ID
		,CUSTOM_CN_NAME');
		
		v_conditions_4 := replace(v_conditions_4,'$','
		,CODE_ATTRIBUTES 
		,MAIN_FLAG
		,YTD_FLAG
		,CUSTOM_ID
		,CUSTOM_CN_NAME');
		
		v_conditions_2 := replace(v_conditions_2,'$','
		,a.CODE_ATTRIBUTES 
		,a.MAIN_FLAG
		,a.YTD_FLAG
		,a.CUSTOM_ID
		,a.CUSTOM_CN_NAME');
		
		v_conditions_5 := replace(v_conditions_5,'$','
		,a.CODE_ATTRIBUTES 
		,a.MAIN_FLAG
		,a.YTD_FLAG
		,a.CUSTOM_ID
		,a.CUSTOM_CN_NAME');
		
		v_conditions_6 := replace(v_conditions_6,'$','
		,a.CODE_ATTRIBUTES 
		,a.MAIN_FLAG
		,a.YTD_FLAG
		,a.CUSTOM_ID
		,a.CUSTOM_CN_NAME');
		
		v_conditions_3:='on (a.VERSION_ID = b.VERSION_ID  and nvl(a.YTD_FLAG,''A'') = nvl(b.YTD_FLAG,''A'') and nvl(a.CUSTOM_ID,0) = nvl(b.CUSTOM_ID,''0'') and nvl(a.CUSTOM_CN_NAME,''A'') = nvl(b.CUSTOM_CN_NAME,''A'')
			and a.GROUP_CODE = b.GROUP_CODE and a.GROUP_LEVEL = b.GROUP_LEVEL and a.SOFTWARE_MARK = b.SOFTWARE_MARK and a.MAIN_FLAG = b.MAIN_FLAG   and a.REGION_CODE = b.REGION_CODE and a.REPOFFICE_CODE = b.REPOFFICE_CODE and a.BG_CODE = b.BG_CODE and a.OVERSEA_FLAG = b.OVERSEA_FLAG and nvl(a.CODE_ATTRIBUTES,''NULLS'') = nvl(b.CODE_ATTRIBUTES,''NULLS'') conditions_4) ';
		
		
	ELSE
		V_STEP_MUM := V_STEP_MUM + 1;
		PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '传入页面有误！请检查参数pageType,页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
		 F_ERRBUF => 'ERROR');
		return '传入页面有误！';
	end if;
		
	<<calculated_code_replace>>
	if v_table is null or v_table = '' then 
		v_table:= v_table_month;
		v_table_temp:=v_table_month_temp;
		v_table_month:='';

	end if;
	
	raise notice 'v_round: %',v_round;
	
	
	<<table_replace>>
	if v_page_type = 'CODE_REPLACE' and v_page_type_month = 'false'  and v_round = 2 then
	--根据传入的成本类型和PBI目录树确定物理表
	v_table:='DM_FCST_ICT_'||v_cost_type_v||'_'||v_granularity_type||'_REPL_SAME_COST_IDX_T';
	v_table_month:='DM_FCST_ICT_'||v_cost_type_v||'_'||v_granularity_type||'_REPL_SAME_YTD_COST_IDX_T';
	v_table_temp:='TEMP_'||v_table||'_'||v_num;
	v_table_month_temp:='TEMP_'||v_table_month||'_'||v_num;
	--v_sql_temp:=replace(v_sql_temp,'table_temp',v_table_temp);
	v_round := 3;																
	raise notice '%',v_conditions;
	raise notice '编码替换循环';
	end if ;
	
	--根据传入的version_id 找到默认基期 V_BASE_PERIOD_ID_BEFORE 
	v_sql := 'SELECT MAX(period_year)-1||''01'' FROM '||v_table||' WHERE VERSION_ID = '||V_VERSION_ID;
	
	EXECUTE IMMEDIATE v_sql INTO V_BASE_PERIOD_ID_BEFORE;
	
	--raise notice '%',v_sql;
	--raise notice '%',V_BASE_PERIOD_ID_BEFORE;
	
	--判断传入的数据是否是默认基期
	if V_BASE_PERIOD_ID_BEFORE = '01' then 
		V_STEP_MUM := V_STEP_MUM + 1;
		PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '基础表'||v_table||'没找到该版本的基期数据',
		 F_ERRBUF => 'ERROR');
		raise notice '基础表 % 没找到该版本的基期数据', v_table;
		goto calculated;
	elsif V_BASE_PERIOD_ID = V_BASE_PERIOD_ID_BEFORE then 
		V_STEP_MUM := V_STEP_MUM + 1;
		PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '基础表'||v_table||'传入的基期为本基期年，无需处理数据',
		 F_ERRBUF => 'ERROR');
		raise notice '基础表 % 传入的基期为本基期年，无需处理数据', v_table;
		goto calculated;
	end if;
	
	raise notice '%',v_table_temp;
	raise notice '%',v_table;
	
	
	V_STEP_MUM:=V_STEP_MUM+1;
	--创建临时表
	raise notice '%',replace(v_sql_temp,'table_temp',v_table_temp);
	EXECUTE IMMEDIATE 'DROP TABLE IF EXISTS '||v_table_temp;
	EXECUTE IMMEDIATE replace(v_sql_temp,'table_temp',v_table_temp);
	V_STEP_MUM := V_STEP_MUM + 1;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '创建临时表：'||v_table_temp||',页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
		 F_FORMULA_SQL_TXT => '创建临时表脚本：'||replace(v_sql_temp,'table_temp',v_table_temp),
		 F_ERRBUF => 'SUCCESS');
	
	raise notice '临时表创建成功';


if v_combidlist = '[]' or v_combidlist is null THEN  --汇总组合之外的逻辑
	
	--根据最后层级来确定计算范围  各层级（SPART/DIMENSION/SUBCATEGORY/SUB_DETAIL/LV4/LV3/LV2/LV1/LV0）
	if V_GROUP_LEVEL = 'SPART' then 
		if v_page_type = 'CODE_REPLACE' and V_REPLACE_RELATION_NAME is not null then 
			v_conditions := v_conditions||' and REPLACE_RELATION_NAME ='''||V_REPLACE_RELATION_NAME||''''
																	||' and REPLACE_RELATION_TYPE ='''||V_REPLACE_RELATION_TYPE||''''
																	||' and RELATION_TYPE ='''||V_RELATION_TYPE||'''';
																	
			v_conditions_over := ' and (CODE_TYPE = ''NEW'' and GROUP_CODE = '''||V_NEW_SPART_CODE||''')'
													||' or (CODE_TYPE = ''OLD'' and GROUP_CODE = '''||V_OLD_SPART_CODE||''')';
		else 
			V_SPART_CODE := replace(replace(replace(replace(V_GROUP_CODE_LIST,'"',''),' ',''),']',''),'[','');
			v_conditions_over := 'and '||v_column1||' = any(regexp_split_to_array('''||V_PROD_RND_TEAM_CODE_LIST||''','','')) and (GROUP_LEVEL = '''||V_GROUP_LEVEL||''' and GROUP_CODE = any(regexp_split_to_array('''||V_SPART_CODE||''','','')))';
		end if;
		
	elsif V_GROUP_LEVEL = 'DIMENSION' then 
		--V_DIMENSION_CODE := replace(replace(replace(replace(V_GROUP_CODE_LIST,'"',''),' ',''),']',''),'[','');
		v_conditions_over := ' and DIMENSION_CODE = '''||V_DIMENSION_CODE||''' and (GROUP_CODE = any(regexp_split_to_array('''||V_GROUP_CODE_LIST||''','','')) or PARENT_CODE = any(regexp_split_to_array('''||V_GROUP_CODE_LIST||''','','')))';
		
	elsif V_GROUP_LEVEL = 'SUBCATEGORY' then 
	   -- V_DIMENSION_SUBCATEGORY_CODE := replace(replace(replace(replace(V_GROUP_CODE_LIST,'"',''),' ',''),']',''),'[','');
		v_conditions_over := ' # and DIMENSION_SUBCATEGORY_CODE = '''||V_DIMENSION_SUBCATEGORY_CODE||''' and (GROUP_CODE = any(regexp_split_to_array('''||V_GROUP_CODE_LIST||''','','')) or PARENT_CODE = any(regexp_split_to_array('''||V_GROUP_CODE_LIST||''','','')))';
		
		if V_DIMENSION_CODE is not null and V_DIMENSION_CODE <> '' then 
			v_conditions_over := replace(v_conditions_over,'#',' and DIMENSION_CODE = '''||V_DIMENSION_CODE||'''');
		else 
			v_conditions_over := replace(v_conditions_over,'#','');
		end if;
		
	elsif V_GROUP_LEVEL = 'SUB_DETAIL' then 
		--V_DIMENSION_SUB_DETAIL_CODE := replace(replace(replace(replace(V_GROUP_CODE_LIST,'"',''),' ',''),']',''),'[','');
		v_conditions_over := ' # $ and DIMENSION_SUB_DETAIL_CODE = '''||V_DIMENSION_SUB_DETAIL_CODE||''' and (GROUP_CODE = '''||V_GROUP_CODE_LIST||''' or PARENT_CODE = '''||V_GROUP_CODE_LIST||''')';
		
		if V_DIMENSION_CODE is not null and V_DIMENSION_CODE <> '' then 
			v_conditions_over := replace(v_conditions_over,'#',' and DIMENSION_CODE = '''||V_DIMENSION_CODE||'''');
		else 
			v_conditions_over := replace(v_conditions_over,'#','');
		end if;
		
		if V_DIMENSION_SUBCATEGORY_CODE is not null and V_DIMENSION_SUBCATEGORY_CODE <> '' then 
			v_conditions_over := replace(v_conditions_over,'$',' and DIMENSION_SUBCATEGORY_CODE = '''||V_DIMENSION_SUBCATEGORY_CODE||'''');
		else 
			v_conditions_over := replace(v_conditions_over,'$','');
		end if;
		
	elsif V_GROUP_LEVEL in ('LV4','LV3','LV2','LV1','LV0') then 
		
		
		--根据传入层级定义下钻的层级
		if V_GROUP_LEVEL = 'LV4' then 
			V_GROUP_LEVEL_NEXT := 'SPART';
		elsif V_GROUP_LEVEL = 'LV3' then 
			V_GROUP_LEVEL_NEXT := 'LV4';
		elsif V_GROUP_LEVEL = 'LV2' then 
			V_GROUP_LEVEL_NEXT := 'LV3';
		elsif V_GROUP_LEVEL = 'LV1' then 
			V_GROUP_LEVEL_NEXT := 'LV2';
		elsif V_GROUP_LEVEL = 'LV0' then 
			V_GROUP_LEVEL_NEXT := 'LV1';
		end if;
		
		--group_code在不同的group_level有重复数据，结合group_level来联合取值
		if v_page_type = 'CODE_REPLACE' then
					
			v_conditions_over := ' and (GROUP_LEVEL = '''||V_GROUP_LEVEL||''' and GROUP_CODE = any(regexp_split_to_array('''||V_PROD_RND_TEAM_CODE_LIST||''','','')))';
			
			if V_REPLACE_RELATION_NAME is not null then 
				v_conditions := v_conditions||' and REPLACE_RELATION_NAME ='''||V_REPLACE_RELATION_NAME||''''
																		||' and REPLACE_RELATION_TYPE ='''||V_REPLACE_RELATION_TYPE||''''
																		||' and RELATION_TYPE ='''||V_RELATION_TYPE||'''';
																		
				v_conditions_over := ' and (CODE_TYPE = ''NEW'' and GROUP_CODE = '''||V_NEW_SPART_CODE||''')'
													||' or (CODE_TYPE = ''OLD'' and GROUP_CODE = '''||V_OLD_SPART_CODE||''')';
													
			end if;
			
		elsif (v_page_type = 'MONTH' and v_combidlist = '[]') or (v_page_type = 'MIX'  and v_combidlist = '[]') then 
			v_conditions_over := ' and ((GROUP_LEVEL = '''||V_GROUP_LEVEL||''' and GROUP_CODE = any(regexp_split_to_array('''||V_GROUP_CODE_LIST||''','',''))) or'||
													 ' (GROUP_LEVEL = '''||V_GROUP_LEVEL_NEXT||''' and PARENT_CODE = any(regexp_split_to_array('''||V_PARENT_CODE_LIST||''','',''))))';
		

		end if;
		
	else
		V_STEP_MUM := V_STEP_MUM + 1;
		PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '层级传入异常,页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
		 F_ERRBUF => 'ERROR');
		return '层级传入异常';
	end if;
	
	--只算新旧SPART
	if v_page_type = 'CODE_REPLACE' and v_page_type_month = 'true' then 
		v_conditions_over := ' and (GROUP_LEVEL = ''SPART'' and (GROUP_CODE = '''||V_NEW_SPART_CODE||''' or GROUP_CODE = '''||V_OLD_SPART_CODE||'''))';
	end if;
	
	
	
elsif v_combidlist <> '[]' THEN  --汇总组合自己的逻辑

		V_GROUP_CODE := replace(replace(v_combidlist,']',''),'[','');
		
		raise notice 'group_code赋值成功';
		
		v_conditions_over := ' and (GROUP_LEVEL = '''||V_GROUP_LEVEL||''' and (GROUP_CODE = '''||V_GROUP_CODE||''' ))';
	
end if;
	
	
	raise notice '%', V_GROUP_LEVEL;
	
	<<calculated_month>>
	--插入计算结果插入临时表
	v_sql:='INSERT INTO '||v_table_temp||'( conditions_4 ) 
					with tempa as (select conditions_0 from '||v_table||' where '||v_conditions||' and BASE_PERIOD_ID = '''||V_BASE_PERIOD_ID_BEFORE||''''||v_conditions_over||
					'),	tempb as (select conditions_0 from tempa where PERIOD_ID = '''||V_BASE_PERIOD_ID||''') select conditions_2 ,b.PERIOD_ID,case when nvl(b.COST_INDEX,0) = 0 then 0 else a.COST_INDEX*100/b.COST_INDEX end from tempa a left join tempb b '||replace(v_conditions_3,'conditions_4','');
	
	v_sql:=replace(replace(v_sql,'conditions_0',v_conditions_0),'conditions_2',v_conditions_2);
		v_sql:=replace(v_sql,'conditions_4',v_conditions_4);

		
	V_STEP_MUM:=V_STEP_MUM+1;
	raise notice '%',v_sql;
	EXECUTE IMMEDIATE v_sql;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '插入临时表：'||v_table_temp||',页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
		 F_FORMULA_SQL_TXT => '插入临时表：'||v_sql,
		 F_DML_ROW_COUNT => SQL%ROWCOUNT,
		 F_ERRBUF => 'SUCCESS');
		

	
	--EXECUTE IMMEDIATE v_sql into v_error_num;
	--raise notice '%', v_error_num;
	
	--将临时表数据回填给正式表
	
	if v_table like '%INDUS%' or  v_table like '%IRB%' or v_table like '%PROD%' THEN 
	
	v_sql:='MERGE INTO '||v_table||' b using '||v_table_temp||' a '||replace(v_conditions_3,'conditions_4',' and a.BASE_PERIOD_ID = b.BASE_PERIOD_ID')||
	' when not matched then insert('||v_conditions_0||',CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,LAST_UPDATE_DATE,DEL_FLAG) values('||v_conditions_6||',a.BASE_PERIOD_ID,a.COST_INDEX,'''||V_CREATED_BY||''','''||V_CREATION_DATE||''','''||V_LAST_UPDATED_BY||''','''||V_LAST_UPDATE_DATE||''','''||V_DEL_FLAG||''')';
	
	ELSIF  v_table like '%CUS%' THEN
	v_sql:='MERGE INTO '||v_table||' b using '||v_table_temp||' a '||replace(v_conditions_3,'conditions_4',' and a.BASE_PERIOD_ID = b.BASE_PERIOD_ID')||
	' when not matched then insert('||v_conditions_4||',CREATED_BY,CREATION_DATE,LAST_UPDATED_BY,LAST_UPDATE_DATE,DEL_FLAG) values('||v_conditions_5||',a.BASE_PERIOD_ID,a.COST_INDEX,'''||V_CREATED_BY||''','''||V_CREATION_DATE||''','''||V_LAST_UPDATED_BY||''','''||V_LAST_UPDATE_DATE||''','''||V_DEL_FLAG||''')';
	
	END IF;
		
	V_STEP_MUM:=V_STEP_MUM+1;
	raise notice '%',v_sql;
	EXECUTE IMMEDIATE v_sql;
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => '插入正式表：'||v_table||',页面：'||v_page_type||',是否汇总组合：'||v_combidlist||',是否虚化:'||V_CUSTOM_ID,
		 F_FORMULA_SQL_TXT => '插入正式表：'||v_sql,
		 F_DML_ROW_COUNT => SQL%ROWCOUNT,
		 F_ERRBUF => 'SUCCESS');

	v_table:='';

	--处理编码替代插入两张数据表，即MONTH和YTD的
	if v_table_month is not null then 
		goto calculated_code_replace;
	end if;
	
     raise notice 'v_table_month:%',v_table_month;
	
	 if v_page_type = 'CODE_REPLACE' and v_page_type_month = 'false'  and v_round = 1  and (v_table_month = '' or v_table_month is null ) then
	 v_round := 2;
	 raise notice 'v_round:%',v_round;
	   goto table_replace;
	end if;
	
	END LOOP;
	
	raise notice '所有逻辑执行完成';
	
 RETURN 'SUCCESS';

EXCEPTION
 WHEN OTHERS THEN
 X_SUCCESS_FLAG := '执行错误，调用异常退出！';
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
		(F_SP_NAME => V_SP_NAME,
		 F_STEP_NUM => V_STEP_MUM,
		 F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
		 --F_FORMULA_SQL_TXT => '插入正式表：'||v_sql,
		 F_ERRBUF => SQLSTATE||':'||SQLERRM);
END$$
/

