-- Name: f_dm_fcst_price_annl_amp; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_annl_amp(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年12月版本
  创建人：twx1139790
  背景描述：根据年累计均本补齐数据，计算SPART层级涨跌幅数据，再与权重表关联，计算得到上层级涨跌幅数据
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FCST_PRICE_ANNL_AMP('');
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_ANNL_AMP'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_MON_VERSION BIGINT; --年度版本号ID
  V_YEAR INT ;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
    if MONTH(CURRENT_TIMESTAMP) = 1
  then V_YEAR = YEAR(NOW()) -1;
  ELSE
  V_YEAR = YEAR(NOW());
  END IF ;
   
  -- 取出年度版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'ANNUAL'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

    SELECT VERSION_ID INTO V_MON_VERSION   -- 月度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'MONTH'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID||'，最新月度版本号为：'||V_MON_VERSION,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T WHERE GROUP_LEVEL <> ''SPART''';   -- 删除涨跌幅临时表不为SPART层级的其余层级数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T WHERE VERSION_ID = '||V_VERSION_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除：DM_FCST_PRICE_ANNL_WEIGHT_T/DM_FCST_PRICE_ANNL_AMP_T，版本号为：'||V_VERSION_ID||'的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --------------------------------------------------------------- 将月度累计页面的权重数据插入年度权重表 ------------------------------------------------------------------------------------
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         ABSOLUTE_WEIGHT,
         USD_PNP_AMT,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         WEIGHT_RATE,
         ABSOLUTE_WEIGHT,
         USD_PNP_AMT,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         DECODE(USD_PNP_AMT,NULL,'Y','N') AS APPEND_FLAG,
         DECODE(GROUP_LEVEL,'LV0',GROUP_CODE,PARENT_CODE) AS PARENT_CODE,
         DECODE(GROUP_LEVEL,'LV0',GROUP_CN_NAME,PARENT_CN_NAME) AS PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
	 FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_WEIGHT_T
	 WHERE VERSION_ID = V_MON_VERSION;   -- 取最新的月度版本号数据
   
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '将版本号为：'||V_VERSION_ID||'，权重数据插入年度权重表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --------------------------------------------------------------------其余层级涨跌幅逻辑计算--------------------------------------------------------------------------------------------
  -- LV4层级涨跌幅逻辑处理
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
  )
  WITH LV4_WEIGHT_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.LV4_PROD_LIST_CODE,
         T1.LV4_PROD_LIST_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP * T2.WEIGHT_RATE AS WEIGHT_AMP,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 T1.ENABLE_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T T1 
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T T2
	  ON  T1.VERSION_ID = T2.VERSION_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE 
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'SPART'    -- 取LV4层级的子级：SPART
	  AND T1.ENABLE_FLAG = 'Y'        -- 逻辑新增SPART层级涨跌幅<100，才是计算所需数值，所以只取有效标识的数据
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE,
         LV4_PROD_LIST_CN_NAME,
         LV4_PROD_LIST_CODE AS GROUP_CODE,
         LV4_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV4' AS GROUP_LEVEL,
         NVL(SUM(WEIGHT_AMP),0) AS ANNUAL_AMP,
         LV3_PROD_LIST_CODE AS PARENT_CODE,
         LV3_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
	  FROM LV4_WEIGHT_AMP_TMP
	  GROUP BY PERIOD_YEAR,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME,
               LV1_PROD_LIST_CODE,
               LV1_PROD_LIST_CN_NAME,
               LV2_PROD_LIST_CODE,
               LV2_PROD_LIST_CN_NAME,
               LV3_PROD_LIST_CODE,
               LV3_PROD_LIST_CN_NAME,
               LV4_PROD_LIST_CODE,
               LV4_PROD_LIST_CN_NAME,
               OVERSEA_FLAG,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               SIGN_TOP_CUST_CATEGORY_CODE,
               SIGN_TOP_CUST_CATEGORY_CN_NAME,
               SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               VIEW_FLAG,
               BG_CODE,
               BG_CN_NAME,
			   ENABLE_FLAG;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的层级为：LV4的数据到DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- LV3层级涨跌幅逻辑处理
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
  )
  WITH LV3_WEIGHT_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.LV3_PROD_LIST_CODE,
         T1.LV3_PROD_LIST_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP * T2.WEIGHT_RATE AS WEIGHT_AMP,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 T1.ENABLE_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T T1 
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T T2
	  ON  T1.VERSION_ID = T2.VERSION_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE 
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'LV4'    -- 取LV3层级的子级：LV4
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE,
         LV3_PROD_LIST_CN_NAME,
         LV3_PROD_LIST_CODE AS GROUP_CODE,
         LV3_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV3' AS GROUP_LEVEL,
         NVL(SUM(WEIGHT_AMP),0) AS ANNUAL_AMP,
         LV2_PROD_LIST_CODE AS PARENT_CODE,
         LV2_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
	  FROM LV3_WEIGHT_AMP_TMP
	  GROUP BY PERIOD_YEAR,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME,
               LV1_PROD_LIST_CODE,
               LV1_PROD_LIST_CN_NAME,
               LV2_PROD_LIST_CODE,
               LV2_PROD_LIST_CN_NAME,
               LV3_PROD_LIST_CODE,
               LV3_PROD_LIST_CN_NAME,
               OVERSEA_FLAG,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               SIGN_TOP_CUST_CATEGORY_CODE,
               SIGN_TOP_CUST_CATEGORY_CN_NAME,
               SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               VIEW_FLAG,
               BG_CODE,
               BG_CN_NAME,
			   ENABLE_FLAG;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的层级为：LV3的数据到DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- LV2层级涨跌幅逻辑处理
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
  )
  WITH LV2_WEIGHT_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.LV2_PROD_LIST_CODE,
         T1.LV2_PROD_LIST_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP * T2.WEIGHT_RATE AS WEIGHT_AMP,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 T1.ENABLE_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T T1 
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T T2
	  ON  T1.VERSION_ID = T2.VERSION_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE 
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'LV3'    -- 取LV2层级的子级：LV3
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE,
         LV2_PROD_LIST_CN_NAME,
         LV2_PROD_LIST_CODE AS GROUP_CODE,
         LV2_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV2' AS GROUP_LEVEL,
         NVL(SUM(WEIGHT_AMP),0) AS ANNUAL_AMP,
         LV1_PROD_LIST_CODE AS PARENT_CODE,
         LV1_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
	  FROM LV2_WEIGHT_AMP_TMP
	  GROUP BY PERIOD_YEAR,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME,
               LV1_PROD_LIST_CODE,
               LV1_PROD_LIST_CN_NAME,
               LV2_PROD_LIST_CODE,
               LV2_PROD_LIST_CN_NAME,
               OVERSEA_FLAG,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               SIGN_TOP_CUST_CATEGORY_CODE,
               SIGN_TOP_CUST_CATEGORY_CN_NAME,
               SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               VIEW_FLAG,
               BG_CODE,
               BG_CN_NAME,
			   ENABLE_FLAG;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的层级为：LV2的数据到DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- LV1层级涨跌幅逻辑处理
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
  )
  WITH LV1_WEIGHT_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.LV1_PROD_LIST_CODE,
         T1.LV1_PROD_LIST_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP * T2.WEIGHT_RATE AS WEIGHT_AMP,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 T1.ENABLE_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T T1 
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T T2
	  ON  T1.VERSION_ID = T2.VERSION_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE 
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'LV2'    -- 取LV1层级的子级：LV2
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE,
         LV1_PROD_LIST_CN_NAME,
         LV1_PROD_LIST_CODE AS GROUP_CODE,
         LV1_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV1' AS GROUP_LEVEL,
         NVL(SUM(WEIGHT_AMP),0) AS ANNUAL_AMP,
         LV0_PROD_LIST_CODE AS PARENT_CODE,
         LV0_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
	  FROM LV1_WEIGHT_AMP_TMP
	  GROUP BY PERIOD_YEAR,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME,
               LV1_PROD_LIST_CODE,
               LV1_PROD_LIST_CN_NAME,
               OVERSEA_FLAG,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               SIGN_TOP_CUST_CATEGORY_CODE,
               SIGN_TOP_CUST_CATEGORY_CN_NAME,
               SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               VIEW_FLAG,
               BG_CODE,
               BG_CN_NAME,
			   ENABLE_FLAG;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的层级为：LV1的数据到DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- LV0层级涨跌幅逻辑处理
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         PARENT_CODE,
         PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
  )
  WITH LV0_WEIGHT_AMP_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.ANNUAL_AMP * T2.WEIGHT_RATE AS WEIGHT_AMP,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.OVERSEA_FLAG,
         T1.REGION_CODE,
         T1.REGION_CN_NAME,
         T1.REPOFFICE_CODE,
         T1.REPOFFICE_CN_NAME,
         T1.SIGN_TOP_CUST_CATEGORY_CODE,
         T1.SIGN_TOP_CUST_CATEGORY_CN_NAME,
         T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         T1.VIEW_FLAG,
         T1.BG_CODE,
         T1.BG_CN_NAME,
		 T1.ENABLE_FLAG
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T T1 
	  INNER JOIN FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_WEIGHT_T T2
	  ON  T1.VERSION_ID = T2.VERSION_ID
	  AND T1.GROUP_CODE = T2.GROUP_CODE 
	  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.PARENT_CODE = T2.PARENT_CODE
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.BG_CODE = T2.BG_CODE
      AND NVL(T1.OVERSEA_FLAG,'S1') = NVL(T2.OVERSEA_FLAG,'S1')
      AND NVL(T1.REGION_CODE,'S2') = NVL(T2.REGION_CODE,'S2')
      AND NVL(T1.REPOFFICE_CODE,'S3') = NVL(T2.REPOFFICE_CODE,'S3')
      AND NVL(T1.SIGN_TOP_CUST_CATEGORY_CODE,'S4') = NVL(T2.SIGN_TOP_CUST_CATEGORY_CODE,'S4')
      AND NVL(T1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5') = NVL(T2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'S5')
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'LV1'    -- 取LV0层级的子级：LV1
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME,
         LV0_PROD_LIST_CODE AS GROUP_CODE,
         LV0_PROD_LIST_CN_NAME AS GROUP_CN_NAME,
         'LV0' AS GROUP_LEVEL,
         NVL(SUM(WEIGHT_AMP),0) AS ANNUAL_AMP,
         LV0_PROD_LIST_CODE AS PARENT_CODE,
         LV0_PROD_LIST_CN_NAME AS PARENT_CN_NAME,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         BG_CODE,
         BG_CN_NAME,
		 ENABLE_FLAG
	  FROM LV0_WEIGHT_AMP_TMP
	  GROUP BY PERIOD_YEAR,
               LV0_PROD_LIST_CODE,
               LV0_PROD_LIST_CN_NAME,
               OVERSEA_FLAG,
               REGION_CODE,
               REGION_CN_NAME,
               REPOFFICE_CODE,
               REPOFFICE_CN_NAME,
               SIGN_TOP_CUST_CATEGORY_CODE,
               SIGN_TOP_CUST_CATEGORY_CN_NAME,
               SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
               VIEW_FLAG,
               BG_CODE,
               BG_CN_NAME,
			   ENABLE_FLAG;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的层级为：LV0的数据到DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将涨跌幅数据插入结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
  )
  SELECT VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ANNUAL_AMP,
         OVERSEA_FLAG,
         REGION_CODE,
         REGION_CN_NAME,
         REPOFFICE_CODE,
         REPOFFICE_CN_NAME,
         SIGN_TOP_CUST_CATEGORY_CODE,
         SIGN_TOP_CUST_CATEGORY_CN_NAME,
         SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,
         VIEW_FLAG,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
         BG_CODE,
         BG_CN_NAME
	  FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T
	  WHERE ENABLE_FLAG = 'Y';   -- 取有效标识的数据插入结果表
	  
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的所有层级数据到DM_FCST_PRICE_ANNL_AMP_T',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T';
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FCST_PRICE_ANNL_AMP_T';
  DBMS_OUTPUT.PUT_LINE('收集统计信息成功'); 

  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FCST_PRICE_ANNL_MID_GROUP_AMP_T/DM_FCST_PRICE_ANNL_AMP_T统计信息完成!');

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
  
END$$
/

