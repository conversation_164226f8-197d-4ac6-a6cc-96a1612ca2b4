-- Name: f_dm_fom_version_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_version_info_t(refresh_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
创建时间：2024/01/03
创建人  ：许灿烽
REFRESH_TYPE   --刷新类型：TOP(规格品)/FORECAST(预测量表)
背景描述：前端点击配置页面的"刷新按钮"后，会先更新版本号。传参是 TOP 则刷新 DATA_TYPE IN ('DIM_DMS','DIM_TREE','DIM_RESOURCE','DIM_WIP','MONTH').传参是 FORECAST ，则刷新 DATA_TYPE = 'DATA_TYPE'
制造量纲维表      DIM_DMS
制造产品维度树    DIM_TREE
资源代码类型维表 DIM_RESOURCE
任务令交易量表  DIM_WIP
预测量表        FORECAST
参数描述:x_result_status :是否成功
来源表:DM_FOM_VERSION_INFO_T 制造单领域版本表
目标表:FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
事例：FIN_DM_OPT_FOI.F_DM_FOM_VERSION_INFO_T()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_VERSION_INFO_T'; --存储过程名称
  V_TO_TABLE VARCHAR(50) := 'FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T'; -- 目标表
  V_STEP_MUM   BIGINT := 0; --步骤号
  
BEGIN
  X_RESULT_STATUS = '1';
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

--更新 版本号
IF REFRESH_TYPE = 'TOP' THEN 
UPDATE FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
SET STATUS = 1,LAST_UPDATE_DATE = CURRENT_TIMESTAMP
WHERE UPPER(DATA_TYPE) IN ('DIM_DMS','DIM_TREE','DIM_RESOURCE','DIM_WIP','MONTH')
AND STATUS = 0;

ELSIF REFRESH_TYPE = 'FORECAST' THEN 
UPDATE FIN_DM_OPT_FOI.DM_FOM_VERSION_INFO_T
SET STATUS = 1,LAST_UPDATE_DATE = CURRENT_TIMESTAMP
WHERE UPPER(DATA_TYPE) IN ('FORECAST')
AND STATUS = 0;

END IF;


  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '更新版本表的版本号状态',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');

  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

