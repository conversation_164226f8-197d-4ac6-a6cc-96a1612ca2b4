-- Name: f_dm_foc_annual_amp_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_annual_amp_t(f_industry_flag character varying, f_cost_type character varying, f_dimension_type character varying, f_keystr character varying DEFAULT NULL::character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/*
  创建时间：2023-12-18
  创建人  ：唐钦
  修改时间：2024年4月18日14点44分
  修改人：唐钦
  背景描述：分视角年度涨跌幅表(年度分析-柱状图)
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_AMP_T()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_ANNUAL_AMP_T'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_YEAR  BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
    
    -- 7月版本新增
    V_SQL                      TEXT;
    V_FROM_TABLE_1   VARCHAR2(500);
    V_FROM_TABLE_2   VARCHAR2(500);
    V_TO_TABLE   VARCHAR2(500);
    V_MID_TABLE   VARCHAR2(500);
    V_SEQUENCE   VARCHAR(500);
    V_PROD_RND_TEAM   VARCHAR2(500);
    V_LV1_PROD_TEAM VARCHAR2(500);
    V_LV2_PROD_TEAM VARCHAR2(500);
    V_IN_LV1_PROD_TEAM VARCHAR2(500);
    V_IN_LV2_PROD_TEAM VARCHAR2(500);
    V_LV3_PROD_RND_TEAM_CODE   VARCHAR2(500);
    V_LV3_PROD_RD_TEAM_CN_NAME   VARCHAR2(500);
    V_SQL_PROFITS_NAME   TEXT; -- 盈利层级归属逻辑
    V_IN_LV3_PROD_RND_TEAM   VARCHAR2(500);
    V_L1_L2   VARCHAR2(500);
    V_IN_L1_L2   VARCHAR2(500);
    V_SQL_CEG_PARENT   TEXT;
    V_SQL_CEGUP_PARENT   TEXT;
    V_PROFITS_NAME VARCHAR2(200);
    V_IN_PROFITS_NAME VARCHAR2(500);
    
    -- 9月版本新增
    V_BASE_LEVEL VARCHAR2(500);   -- 基础层级（采购成本/制造成本）字段
    V_IN_BASE_LEVEL VARCHAR2(500);   -- 基础层级（采购成本/制造成本）字段
    V_DMS_CODE VARCHAR2(500);
    V_DMS_NAME VARCHAR2(500);
    V_IN_DMS_CODE VARCHAR2(500);
    V_IN_DMS_NAME VARCHAR2(500);
    V_REL_PROD_RND_TEAM_CODE TEXT;   -- 重量级团队关联逻辑
    V_REL_DMS_CODE TEXT;   -- 量纲层级关联逻辑
    V_REL_PROFITS_NAME TEXT;   -- 盈利层级关联逻辑
    V_SQL_PROD_RND_TEAM_CODE TEXT;   -- 重量级团队CODE归属逻辑
    V_SQL_PROD_RD_TEAM_CN_NAME TEXT;   -- 重量级团队中文名称归属逻辑
    V_SQL_DMS_CODE TEXT;   -- 量纲层级编码归属逻辑
    V_SQL_DMS_NAME TEXT;   -- 量纲层级中文名称归属逻辑
    V_SQL_PARENT TEXT;   -- 父层级CODE逻辑
    V_GROUP_TOTAL TEXT;   -- GROUP层级的3个字段逻辑
    V_CHILD_LEVEL VARCHAR2(500);   -- 子类层级条件
    V_DMS_TOTAL VARCHAR2(500); 
    V_LEVEL_NUM BIGINT;   -- 循环时不同颗粒度对应的层级数值不同
    V_DMS_TOTAL_BAK VARCHAR(500);   -- 备份
    V_DMS_CODE_BAK VARCHAR(500);   -- 备份
    V_DMS_NAME_BAK VARCHAR(500);   -- 备份
    V_PROFITS_NAME_BAK VARCHAR(500);   -- 备份
    V_L1_L2_BAK VARCHAR(500);   -- 备份
    V_TMP_TABLE VARCHAR(500); 
    V_TAB_LV3_PROD VARCHAR(500); 
    V_TAB_DMS_CODE VARCHAR(500); 
    V_TAB_DMS_NAME VARCHAR(500); 
    V_TAB_L1_L2 VARCHAR(500); 
    V_REL_LV3_PROD_RND_TEAM_CODE VARCHAR(500); 
    
 -- 11月版本新增
    V_BEGIN_NUM INT;   -- 不同成本类型，循环开始的值不一致
    V_BASE_LEVEL_TABLE TEXT;
    V_SQL_AMT VARCHAR(500); 
    V_BASE_CODE VARCHAR(500); 
    V_REL_BASE_LEVEL TEXT;
    V_SQL_CEG_PARENT_NAME TEXT;
    V_SQL_CEGUP_PARENT_NAME TEXT;
    
 -- 1月版本新增
    V_SPART_CODE VARCHAR(200);
    V_SPART_CODE_BAK VARCHAR(200);
    V_SPART_NAME VARCHAR(200);
    V_SPART_NAME_BAK VARCHAR(200);
    V_REL_SPART VARCHAR(500);
    V_IN_SPART VARCHAR(200);
    
 -- 202407月版本新增
    V_DIFF_COLUMN_CODE VARCHAR(200);
    V_DIFF_COLUMN_CODE_BAK VARCHAR(200);
    V_DIFF_COLUMN_NAME VARCHAR(200);
    V_DIFF_COLUMN_NAME_BAK VARCHAR(200);
    V_REL_DIFF_COLUMN VARCHAR(500);
    V_IN_DIFF_COLUMN VARCHAR(200);
    V_VERSION_TABLE VARCHAR(100);
    V_SQL_PARA VARCHAR(500);
    V_TABLE_DIFF TEXT;
    V_SQL_YTD TEXT;
    V_SQL_CONDITION1 VARCHAR(500);
    
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

 -- 判断入参的成本类型是采购成本，还是制造成本的类型(11月版本需求新增)
  IF F_COST_TYPE = 'P' THEN  -- 采购成本类型
     V_BEGIN_NUM := 1;
     V_SQL_AMT := '
             CAST(GS_DECRYPT( RMB_AVG_AMT,'''||V_KEYSTR||''',''AES128'',''CBC'',''SHA256'' ) AS NUMERIC) AS RMB_AVG_AMT,';
     V_REL_BASE_LEVEL := '';
     V_SQL_PARENT := 'CATEGORY_CODE AS PARENT_CODE,
                      CATEGORY_CN_NAME AS PARENT_CN_NAME,';
     V_BASE_CODE := '
             L3_CEG_CODE, 
             L4_CEG_CODE,
             CATEGORY_CODE,';
     V_BASE_LEVEL := '
             L3_CEG_CODE,
             L3_CEG_SHORT_CN_NAME,
             L4_CEG_CODE,
             L4_CEG_SHORT_CN_NAME,
             CATEGORY_CODE,
             CATEGORY_CN_NAME,'; 
     V_IN_BASE_LEVEL := '
                   T1.L3_CEG_CODE,
                   T1.L3_CEG_SHORT_CN_NAME,
                   T1.L4_CEG_CODE,
                   T1.L4_CEG_SHORT_CN_NAME,
                   T1.CATEGORY_CODE,
                   T1.CATEGORY_CN_NAME,';
     V_BASE_LEVEL_TABLE := '
              L3_CEG_CODE    VARCHAR(50),
              L3_CEG_SHORT_CN_NAME    VARCHAR(500),
              L4_CEG_CODE    VARCHAR(50),
              L4_CEG_SHORT_CN_NAME    VARCHAR(500),
              CATEGORY_CODE VARCHAR(50),
              CATEGORY_CN_NAME VARCHAR(500),';

 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG IN ('I','REPL') THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'REPL' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_MTD_AVG_T';--来源表1(年YTD均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_YTD_AMP_T';   --采购成本-年度YTD涨跌幅
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_PUR_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'YTD_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
            V_SQL_CONDITION1 := 'AND OVERSEA_FLAG = ''G'' AND LV0_PROD_LIST_CODE = ''GR''';   -- 只取全球/集团的数据
            V_SQL_YTD := 'AND PERIOD_ID IN (
                          SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM '||V_FROM_TABLE_1||'
                             WHERE APPEND_FLAG = ''N''        -- 当前年份有数据的，取每年非补齐的最大月份数据
                             AND CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''MM'') AS INT)   -- 只取每年1月-每年YTD月的数据值
                          UNION ALL 
                            SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM '||V_FROM_TABLE_1||'
                             WHERE PERIOD_YEAR IN (  
                                                   SELECT PERIOD_YEAR FROM(   
                                                   SELECT PERIOD_YEAR,SUM(APP_FLAG) AS APP_FLAG FROM(   
                                                   SELECT PERIOD_YEAR,DECODE(APPEND_FLAG,''Y'',0,1) AS APP_FLAG FROM '||V_FROM_TABLE_1||'
                                                          WHERE CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''MM'') AS INT)   -- 只取每年1月-每年YTD月的数据值
                                                   ) GROUP BY PERIOD_YEAR     -- 为0的时候，表示整年无数据
                                                   )WHERE APP_FLAG = 0
                                                   )    -- 整年没有数据，即取最大月份的补齐数据
                         )';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'PFT_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'DMS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 12;
     ELSE
       NULL;
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ANNUAL_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'ENERGY_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'ENERGY_PFT_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'ENERGY_DMS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 12;   -- 202405版本，新增视角12
     ELSE
       NULL;
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ANNUAL_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'IAS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'IAS_PFT_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'IAS_DMS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 12;   -- 202405版本，新增视角12
     ELSE
       NULL;
     END IF;
  END IF;
     
 -- 判断入参的成本类型是采购成本，还是制造成本的类型(11月版本需求新增)
  ELSIF F_COST_TYPE = 'M' THEN  -- 制造成本类型
     V_BEGIN_NUM := 2;
     V_SQL_AMT := '
             RMB_AVG_AMT,';
     V_SQL_PARENT := 'MANUFACTURE_OBJECT_CODE AS PARENT_CODE,
                      MANUFACTURE_OBJECT_CN_NAME AS PARENT_CN_NAME,';
     V_REL_BASE_LEVEL := '
                 AND NVL(T1.SHIPPING_OBJECT_CODE,''SNULL00'') = NVL(T2.SHIPPING_OBJECT_CODE,''SNULL00'')
                 AND NVL(T1.MANUFACTURE_OBJECT_CODE,''SNULL01'') = NVL(T2.MANUFACTURE_OBJECT_CODE,''SNULL01'') ';
     V_BASE_CODE := '
             SHIPPING_OBJECT_CODE, 
             MANUFACTURE_OBJECT_CODE,';
     V_BASE_LEVEL := '
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,';
     V_IN_BASE_LEVEL := '
                   T1.SHIPPING_OBJECT_CODE,
                   T1.SHIPPING_OBJECT_CN_NAME,
                   T1.MANUFACTURE_OBJECT_CODE,
                   T1.MANUFACTURE_OBJECT_CN_NAME,';
     V_BASE_LEVEL_TABLE := '
             SHIPPING_OBJECT_CODE VARCHAR(200),
             SHIPPING_OBJECT_CN_NAME VARCHAR(200),
             MANUFACTURE_OBJECT_CODE VARCHAR(200),
             MANUFACTURE_OBJECT_CN_NAME VARCHAR(200),';
     
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG IN ('I','REPL') THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'REPL' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_MTD_AVG_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_YTD_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_MADE_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'YTD_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
            V_SQL_CONDITION1 := 'AND OVERSEA_FLAG = ''G'' AND LV0_PROD_LIST_CODE = ''GR''';   -- 只取全球/集团的数据
            V_SQL_YTD := 'AND PERIOD_ID IN (
                          SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM '||V_FROM_TABLE_1||'
                             WHERE APPEND_FLAG = ''N''        -- 当前年份有数据的，取每年非补齐的最大月份数据
                             AND CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''MM'') AS INT)   -- 只取每年1月-每年YTD月的数据值
                          UNION ALL 
                            SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM '||V_FROM_TABLE_1||'
                             WHERE PERIOD_YEAR IN (  
                                                   SELECT PERIOD_YEAR FROM(   
                                                   SELECT PERIOD_YEAR,SUM(APP_FLAG) AS APP_FLAG FROM(   
                                                   SELECT PERIOD_YEAR,DECODE(APPEND_FLAG,''Y'',0,1) AS APP_FLAG FROM '||V_FROM_TABLE_1||'
                                                          WHERE CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-1),''MM'') AS INT)   -- 只取每年1月-每年YTD月的数据值
                                                   ) GROUP BY PERIOD_YEAR     -- 为0的时候，表示整年无数据
                                                   )WHERE APP_FLAG = 0
                                                   )    -- 整年没有数据，即取最大月份的补齐数据
                         )';
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'PFT_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'DMS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 12;
     ELSE
       NULL;
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_ANNUAL_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'ENERGY_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'ENERGY_PFT_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'ENERGY_DMS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 12;   -- 202405版本，新增视角12
     ELSE
       NULL;
     END IF;
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
    -- 判断入参是要走通用颗粒度的逻辑还是盈利颗粒度的逻辑，处理来源表和目标表的变量
     IF F_DIMENSION_TYPE = 'U' THEN -- 通用颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_ANNUAL_AMP_T';--目标表
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'IAS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'P' THEN -- 盈利颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'IAS_PFT_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 8;
        ELSIF F_DIMENSION_TYPE = 'D' THEN -- 量纲颗粒度
            V_FROM_TABLE_1 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_VIEW_ANNL_COST_T';--来源表1(年均本)
            V_FROM_TABLE_2 := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_ANNUAL_WEIGHT_T'; --来源表2（单年权重）
            V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_ANNUAL_AMP_T'; --目标表 
            V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MID_GROUP_AMP_T';   -- 中间表
            V_TMP_TABLE := 'IAS_DMS_DECRYPT_AVG_TMP';
            V_LEVEL_NUM := 12;   -- 202405版本，新增视角12
     ELSE
       NULL;
     END IF;
  END IF;
  END IF;
   
  --版本号赋值
  IF F_VERSION_ID IS NULL THEN   -- 版本号未入参，取版本信息表版本号
     V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  ELSE   -- 否则，以入参版本为主
     V_VERSION_ID := F_VERSION_ID;
  END IF;
   
  --1.清空分视角下ITEM的年均本基础表(中间表)数据:
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_MID_TABLE;
  DBMS_OUTPUT.PUT_LINE('清空基础表表数据');
  
  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空'||V_MID_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 重置变量入参
  -- 通用颗粒度公用变量
      V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
      V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
      V_PROD_RND_TEAM := '
                         PROD_RND_TEAM_CODE,
                         PROD_RD_TEAM_CN_NAME,';
      V_TAB_LV3_PROD := ' LV3_PROD_RND_TEAM_CODE VARCHAR(50),
                          LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),';
      V_REL_LV3_PROD_RND_TEAM_CODE := ' AND NVL(T1.LV3_PROD_RND_TEAM_CODE,''SNULL3'') = NVL(T2.LV3_PROD_RND_TEAM_CODE,''SNULL3'') ';
      V_IN_LV3_PROD_RND_TEAM := 'T1.LV3_PROD_RND_TEAM_CODE,
                                 T1.LV3_PROD_RD_TEAM_CN_NAME,';        
  -- 盈利颗粒度公用变量
      V_L1_L2 := 'L1_NAME, 
                  L2_NAME,';
      V_IN_L1_L2 := 'T1.L1_NAME, 
                     T1.L2_NAME,';
      V_PROFITS_NAME := ' PROFITS_NAME,'; 
      V_SQL_PROFITS_NAME := '
                            CASE T1.VIEW_FLAG WHEN ''3'' THEN T1.L1_NAME 
                                              WHEN ''4'' THEN T1.L2_NAME 
                                              ELSE NULL
                            END AS PROFITS_NAME,';
  -- 量纲颗粒度公用变量
      V_DMS_TOTAL := '
                      DMS_CODE,
                      DMS_CN_NAME,';
      V_SQL_DMS_CODE := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CODE 
                              WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CODE
                         ELSE T1.DIMENSION_SUB_DETAIL_CODE
                         END AS DMS_CODE,';
      V_SQL_DMS_NAME := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.DIMENSION_CN_NAME 
                              WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
                         ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
                         END AS DMS_CN_NAME,';
                         
  -- 当产业项目标识为：E时且颗粒度为：量纲时，加COA层级变量/IAS且不为盈利颗粒度时，加LV4层级变量
    IF F_INDUSTRY_FLAG = 'E' AND F_DIMENSION_TYPE = 'D' THEN 
       V_TABLE_DIFF := 'COA_CODE VARCHAR(200),
                        COA_CN_NAME VARCHAR(200),';
       V_DIFF_COLUMN_CODE := 'COA_CODE,';
       V_DIFF_COLUMN_NAME := 'COA_CN_NAME,';
       V_IN_DIFF_COLUMN := 'T1.COA_CODE,T1.COA_CN_NAME,';
       V_REL_DIFF_COLUMN := ' AND NVL(T1.COA_CODE,''S10'') = NVL(T2.COA_CODE,''S10'') ';
       V_DIFF_COLUMN_CODE_BAK := V_DIFF_COLUMN_CODE;
       V_DIFF_COLUMN_NAME_BAK := V_DIFF_COLUMN_NAME;   -- 变量值备份
    ELSIF F_INDUSTRY_FLAG = 'IAS' AND F_DIMENSION_TYPE <> 'P' THEN 
       V_TABLE_DIFF := 'LV4_PROD_RND_TEAM_CODE VARCHAR(50),
                        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),';
       V_DIFF_COLUMN_CODE := 'LV4_PROD_RND_TEAM_CODE,';
       V_DIFF_COLUMN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
       V_IN_DIFF_COLUMN := 'T1.LV4_PROD_RND_TEAM_CODE,T1.LV4_PROD_RD_TEAM_CN_NAME,';
       V_REL_DIFF_COLUMN := ' AND NVL(T1.LV4_PROD_RND_TEAM_CODE,''S10'') = NVL(T2.LV4_PROD_RND_TEAM_CODE,''S10'') ';
    ELSE NULL;
    END IF;

  --通用颗粒度的维度时，不需要L1和L2字段
    IF F_DIMENSION_TYPE = 'U' THEN 
         V_L1_L2 := '';
         V_IN_L1_L2 := '';
         V_PROFITS_NAME := '';
         V_SQL_PROFITS_NAME := '';
         V_DMS_TOTAL := ''; 
         V_SQL_DMS_CODE := ''; 
         V_SQL_DMS_NAME := ''; 
       IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
           V_SQL_PARA := '                 
                       CASE T1.VIEW_FLAG WHEN ''0'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                         WHEN ''1'' THEN T1.LV1_PROD_RND_TEAM_CODE
                                         WHEN ''2'' THEN T1.LV2_PROD_RND_TEAM_CODE
                                         WHEN ''3'' THEN T1.LV3_PROD_RND_TEAM_CODE
                       ELSE T1.LV4_PROD_RND_TEAM_CODE
                       ';
           V_SQL_CEG_PARENT := V_SQL_PARA||'END AS PARENT_CODE,';   -- 通用层级的父层级CODE
           V_SQL_PROD_RND_TEAM_CODE := V_SQL_PARA||'END AS PROD_RND_TEAM_CODE,';    
           V_SQL_PARA := '
                       CASE T1.VIEW_FLAG WHEN ''0'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                         WHEN ''1'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                         WHEN ''2'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                         WHEN ''3'' THEN T1.LV3_PROD_RD_TEAM_CN_NAME
                       ELSE T1.LV4_PROD_RD_TEAM_CN_NAME
                       ';   -- 重定义
           V_SQL_CEG_PARENT_NAME := V_SQL_PARA||'END AS PARENT_CN_NAME,';   -- 通用层级的父层级CODE
           V_SQL_PROD_RD_TEAM_CN_NAME := V_SQL_PARA||'END AS PROD_RD_TEAM_CN_NAME,';
       ELSE
           V_SQL_PARA := '                 
                       CASE T1.VIEW_FLAG WHEN ''0'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                         WHEN ''1'' THEN T1.LV1_PROD_RND_TEAM_CODE
                                         WHEN ''2'' THEN T1.LV2_PROD_RND_TEAM_CODE
                       ELSE T1.LV3_PROD_RND_TEAM_CODE
                       ';
           V_SQL_CEG_PARENT := V_SQL_PARA||'END AS PARENT_CODE,';   -- 通用层级的父层级CODE
           V_SQL_PROD_RND_TEAM_CODE := V_SQL_PARA||'END AS PROD_RND_TEAM_CODE,';    
           V_SQL_PARA := '
                       CASE T1.VIEW_FLAG WHEN ''0'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                         WHEN ''1'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                         WHEN ''2'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                       ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                       ';   -- 重定义
           V_SQL_CEG_PARENT_NAME := V_SQL_PARA||'END AS PARENT_CN_NAME,';   -- 通用层级的父层级CODE
           V_SQL_PROD_RD_TEAM_CN_NAME := V_SQL_PARA||'END AS PROD_RD_TEAM_CN_NAME,';
       END IF;

  --盈利颗粒度的维度时，不需要LV3字段
    ELSIF F_DIMENSION_TYPE = 'P' THEN
         V_LV3_PROD_RND_TEAM_CODE := '';
         V_LV3_PROD_RD_TEAM_CN_NAME := '';
         V_DMS_TOTAL := ''; 
         V_SQL_DMS_CODE := ''; 
         V_TAB_LV3_PROD := '';
         V_SQL_DMS_NAME := ''; 
         V_REL_LV3_PROD_RND_TEAM_CODE := '';
         V_IN_LV3_PROD_RND_TEAM := '';
         V_TAB_L1_L2 := 'L1_NAME VARCHAR(200), 
                         L2_NAME VARCHAR(200),';
         V_REL_PROFITS_NAME := ' AND NVL(T1.L1_NAME,''SNULL1'') = NVL(T2.L1_NAME,''SNULL1'')
                                 AND NVL(T1.L2_NAME,''SNULL2'') = NVL(T2.L2_NAME,''SNULL2'') ';                
         V_SQL_PROD_RND_TEAM_CODE := '                 
                            T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';    
         V_SQL_PROD_RD_TEAM_CN_NAME:='
                            T1.LV2_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';
         
  --量纲颗粒度的维度时，不需要盈利字段
    ELSIF F_DIMENSION_TYPE = 'D' THEN
         V_L1_L2 := '';
         V_IN_L1_L2 := '';
         V_PROFITS_NAME := '';
         V_SQL_PROFITS_NAME := '';
         V_SPART_CODE := 'SPART_CODE,';
         V_SPART_NAME := 'SPART_CN_NAME,';
         V_IN_SPART := 'T1.SPART_CODE,T1.SPART_CN_NAME,';
         V_REL_SPART := ' AND NVL(T1.SPART_CODE,''SNULL9'') = NVL(T2.SPART_CODE,''SNULL9'') ';
         V_REL_DMS_CODE := ' AND NVL(T1.DIMENSION_CODE,''SNULL1'') = NVL(T2.DIMENSION_CODE,''SNULL1'')
                             AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''SNULL2'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''SNULL2'')
                             AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''SNULL3'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''SNULL3'') ';  
         V_DMS_CODE := '
                        DIMENSION_CODE,
                        DIMENSION_SUBCATEGORY_CODE,
                        DIMENSION_SUB_DETAIL_CODE,';
         V_DMS_NAME:= '
                       DIMENSION_CN_NAME,
                       DIMENSION_SUBCATEGORY_CN_NAME,
                       DIMENSION_SUB_DETAIL_CN_NAME,';
         V_IN_DMS_CODE := '
                        T1.DIMENSION_CODE,
                        T1.DIMENSION_SUBCATEGORY_CODE,
                        T1.DIMENSION_SUB_DETAIL_CODE,';
         V_IN_DMS_NAME:= '
                       T1.DIMENSION_CN_NAME,
                       T1.DIMENSION_SUBCATEGORY_CN_NAME,
                       T1.DIMENSION_SUB_DETAIL_CN_NAME,';
         V_TAB_DMS_CODE := '
                        SPART_CODE VARCHAR(200),
                        DIMENSION_CODE VARCHAR(500),
                        DIMENSION_SUBCATEGORY_CODE VARCHAR(500),
                        DIMENSION_SUB_DETAIL_CODE VARCHAR(500),';
         V_TAB_DMS_NAME:= '
                       SPART_CN_NAME VARCHAR(200),
                       DIMENSION_CN_NAME VARCHAR(2000),
                       DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(2000),
                       DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(2000),';
      IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
         V_SQL_PROD_RND_TEAM_CODE := '                 
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RND_TEAM_CODE    -- 202405月版本新增视角12
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';    
         V_SQL_PROD_RD_TEAM_CN_NAME:='
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RD_TEAM_CN_NAME    -- 202405月版本新增视角12
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PROD_RD_TEAM_CN_NAME,';
      ELSE   -- ICT/数字能源
         V_SQL_PROD_RND_TEAM_CODE := '                 
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                            ELSE T1.LV3_PROD_RND_TEAM_CODE
                            END AS PROD_RND_TEAM_CODE,';    
         V_SQL_PROD_RD_TEAM_CN_NAME:='
                            CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                                 WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                            END AS PROD_RD_TEAM_CN_NAME,';
    END IF;
    ELSE NULL;
    END IF;
                     
  -- 创建存放加解密均本数据的会话级临时表
      V_SQL := '
    DROP TABLE IF EXISTS '|| V_TMP_TABLE ||';
    CREATE TEMPORARY TABLE '||V_TMP_TABLE||' (
      PERIOD_YEAR BIGINT,
      LV0_PROD_RND_TEAM_CODE VARCHAR(50),
      LV0_PROD_RD_TEAM_CN_NAME VARCHAR(500),
      LV1_PROD_RND_TEAM_CODE VARCHAR(50),
      LV1_PROD_RD_TEAM_CN_NAME VARCHAR(500),
      LV2_PROD_RND_TEAM_CODE VARCHAR(50),
      LV2_PROD_RD_TEAM_CN_NAME VARCHAR(500),
      '||V_TAB_LV3_PROD
      ||V_TABLE_DIFF
      ||V_TAB_DMS_CODE
      ||V_TAB_DMS_NAME
      ||V_TAB_L1_L2
      ||V_BASE_LEVEL_TABLE||'                 
      GROUP_CODE VARCHAR(200),
      GROUP_CN_NAME VARCHAR(2000),  
      GROUP_LEVEL VARCHAR(50),
      RMB_AVG_AMT NUMERIC, 
      PARENT_CODE VARCHAR(200),
      PARENT_CN_NAME VARCHAR(500),
      VIEW_FLAG VARCHAR(2),
      CALIBER_FLAG VARCHAR(2),
      OVERSEA_FLAG VARCHAR(2),
      LV0_PROD_LIST_CODE VARCHAR(50),
      LV0_PROD_LIST_CN_NAME VARCHAR(500)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(PARENT_CODE,GROUP_CODE)';
    EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE('加解密均本临时表创建表成功');

    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '加解密均本临时表创建完成',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');  
     
  -- 将ITEM层级的年均本数解密后放入临时表
    V_SQL := '
  INSERT INTO '||V_TMP_TABLE||'(
      PERIOD_YEAR,
      LV0_PROD_RND_TEAM_CODE,
      LV0_PROD_RD_TEAM_CN_NAME,
      LV1_PROD_RND_TEAM_CODE,
      LV1_PROD_RD_TEAM_CN_NAME,
      LV2_PROD_RND_TEAM_CODE,
      LV2_PROD_RD_TEAM_CN_NAME,
      '||V_LV3_PROD_RND_TEAM_CODE
      ||V_LV3_PROD_RD_TEAM_CN_NAME
      ||V_DMS_CODE
      ||V_DMS_NAME
      ||V_SPART_CODE
      ||V_SPART_NAME
      ||V_DIFF_COLUMN_CODE
      ||V_DIFF_COLUMN_NAME
      ||V_L1_L2
      ||V_BASE_LEVEL||'                 
      GROUP_CODE,
      GROUP_CN_NAME,  
      GROUP_LEVEL,
      RMB_AVG_AMT, 
      PARENT_CODE,
      PARENT_CN_NAME,
      VIEW_FLAG,
      CALIBER_FLAG,
      OVERSEA_FLAG,
      LV0_PROD_LIST_CODE,
      LV0_PROD_LIST_CN_NAME)
     
   SELECT PERIOD_YEAR,
          LV0_PROD_RND_TEAM_CODE,
          LV0_PROD_RD_TEAM_CN_NAME,
          LV1_PROD_RND_TEAM_CODE,
          LV1_PROD_RD_TEAM_CN_NAME,
          LV2_PROD_RND_TEAM_CODE,
          LV2_PROD_RD_TEAM_CN_NAME,
          '||V_LV3_PROD_RND_TEAM_CODE
          ||V_LV3_PROD_RD_TEAM_CN_NAME
          ||V_DMS_CODE
          ||V_DMS_NAME
          ||V_SPART_CODE
          ||V_SPART_NAME
          ||V_DIFF_COLUMN_CODE
          ||V_DIFF_COLUMN_NAME
          ||V_L1_L2
          ||V_BASE_LEVEL||'             
          ITEM_CODE AS GROUP_CODE,
          ITEM_CN_NAME AS GROUP_CN_NAME,  
          ''ITEM'' AS GROUP_LEVEL,
          '||V_SQL_AMT||'  -- 采购成本：解密年均本数据
          '||V_SQL_PARENT||'
          VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME
       FROM '||V_FROM_TABLE_1
             ||' WHERE VERSION_ID = '||V_VERSION_ID||'
             '||V_SQL_YTD||'
             '||V_SQL_CONDITION1;
             
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL;
  DBMS_OUTPUT.PUT_LINE('加解密均本数据放入临时表');

    --写入日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM =>  V_STEP_NUM,
     F_CAL_LOG_DESC => '加解密均本数据放入临时表',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');  
     
  -- 将ITEM层级的年度涨跌幅数据插入中间表
    V_SQL := '
  INSERT INTO '||V_MID_TABLE||'(
              VERSION_ID,
              PERIOD_YEAR,
              LV0_PROD_RND_TEAM_CODE,
              LV0_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,
              '||V_LV3_PROD_RND_TEAM_CODE
              ||V_LV3_PROD_RD_TEAM_CN_NAME
              ||V_DMS_CODE
              ||V_DMS_NAME
              ||V_SPART_CODE
              ||V_SPART_NAME
              ||V_DIFF_COLUMN_CODE
              ||V_DIFF_COLUMN_NAME
              ||V_L1_L2
              ||V_PROD_RND_TEAM
              ||V_DMS_TOTAL
              ||V_PROFITS_NAME
              ||V_BASE_LEVEL||'
              GROUP_CODE,
              GROUP_CN_NAME,
              GROUP_LEVEL,
              ANNUAL_AMP,
              PARENT_CODE,
              PARENT_CN_NAME,
              CREATED_BY,
              CREATION_DATE,
              LAST_UPDATED_BY,
              LAST_UPDATE_DATE,
              DEL_FLAG,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG,
              LV0_PROD_LIST_CODE,
              LV0_PROD_LIST_CN_NAME
            )
  -- ITEM层级年度涨跌幅的计算逻辑
  -- 将年均本数据按年份行转列 
  WITH BY_YEAR_AVG_TMP AS(
        SELECT LV0_PROD_RND_TEAM_CODE,
               LV1_PROD_RND_TEAM_CODE,
               LV2_PROD_RND_TEAM_CODE,
               '||V_LV3_PROD_RND_TEAM_CODE
               ||V_DMS_CODE
               ||V_SPART_CODE
               ||V_DIFF_COLUMN_CODE
               ||V_L1_L2
               ||V_BASE_CODE||'
               GROUP_CODE,
               GROUP_LEVEL,
               PARENT_CODE,
               SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-3||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_THREE_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-2||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_TWO_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR-1||' THEN RMB_AVG_AMT ELSE 0 END) AS LAST_YEAR_AVG,
               SUM(CASE WHEN PERIOD_YEAR = '||V_YEAR||' THEN RMB_AVG_AMT ELSE 0 END) AS YEAR_AVG,
               VIEW_FLAG,
               CALIBER_FLAG,
               OVERSEA_FLAG,
               LV0_PROD_LIST_CODE
           FROM '||V_TMP_TABLE||'  
           GROUP BY LV0_PROD_RND_TEAM_CODE,
                    LV1_PROD_RND_TEAM_CODE,
                    LV2_PROD_RND_TEAM_CODE,
                    '||V_LV3_PROD_RND_TEAM_CODE
                    ||V_DMS_CODE
                    ||V_SPART_CODE
                    ||V_DIFF_COLUMN_CODE
                    ||V_L1_L2
                    ||V_BASE_CODE||'    
                    GROUP_CODE,
                    GROUP_LEVEL,
                    PARENT_CODE,
                    VIEW_FLAG,
                    CALIBER_FLAG,
                    OVERSEA_FLAG,
                    LV0_PROD_LIST_CODE
        )
  , YEAR_ANNUAL_AMP_TMP AS(
      SELECT '||V_YEAR-2||' AS PERIOD_YEAR,
              LV0_PROD_RND_TEAM_CODE,
              LV1_PROD_RND_TEAM_CODE,
              LV2_PROD_RND_TEAM_CODE,
              '||V_LV3_PROD_RND_TEAM_CODE
              ||V_L1_L2
              ||V_DMS_CODE
              ||V_SPART_CODE
              ||V_DIFF_COLUMN_CODE
              ||V_BASE_CODE||'    
              GROUP_CODE,
              GROUP_LEVEL,
              ((LAST_TWO_YEAR_AVG/NULLIF(LAST_THREE_YEAR_AVG,0))-1) AS ANNUAL_AMP,
              PARENT_CODE,
              VIEW_FLAG,
              CALIBER_FLAG,
              OVERSEA_FLAG,
              LV0_PROD_LIST_CODE
          FROM BY_YEAR_AVG_TMP
      UNION ALL
      SELECT '||V_YEAR-1||' AS PERIOD_YEAR,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             '||V_LV3_PROD_RND_TEAM_CODE
             ||V_L1_L2
             ||V_DMS_CODE
             ||V_SPART_CODE
             ||V_DIFF_COLUMN_CODE
             ||V_BASE_CODE||'    
             GROUP_CODE,
             GROUP_LEVEL,
             ((LAST_YEAR_AVG/NULLIF(LAST_TWO_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE
         FROM BY_YEAR_AVG_TMP   
      UNION ALL
      SELECT '||V_YEAR||' AS PERIOD_YEAR,
             LV0_PROD_RND_TEAM_CODE,
             LV1_PROD_RND_TEAM_CODE,
             LV2_PROD_RND_TEAM_CODE,
             '||V_LV3_PROD_RND_TEAM_CODE
             ||V_L1_L2
             ||V_DMS_CODE
             ||V_SPART_CODE
             ||V_DIFF_COLUMN_CODE
             ||V_BASE_CODE||'    
             GROUP_CODE,
             GROUP_LEVEL,
             ((YEAR_AVG/NULLIF(LAST_YEAR_AVG,0))-1) AS ANNUAL_AMP,
             PARENT_CODE,
             VIEW_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE
         FROM BY_YEAR_AVG_TMP 
              )
  -- 计算ITEM层级的年度涨跌幅数据
       SELECT '
              ||V_VERSION_ID ||' AS VERSION_ID,
              T1.PERIOD_YEAR,
              T1.LV0_PROD_RND_TEAM_CODE,
              T1.LV0_PROD_RD_TEAM_CN_NAME,
              T1.LV1_PROD_RND_TEAM_CODE,
              T1.LV1_PROD_RD_TEAM_CN_NAME,
              T1.LV2_PROD_RND_TEAM_CODE,
              T1.LV2_PROD_RD_TEAM_CN_NAME,
              '||V_IN_LV3_PROD_RND_TEAM
              ||V_IN_DMS_CODE
              ||V_IN_DMS_NAME
              ||V_IN_SPART
              ||V_IN_DIFF_COLUMN
              ||V_IN_L1_L2
              ||V_SQL_PROD_RND_TEAM_CODE
              ||V_SQL_PROD_RD_TEAM_CN_NAME
              ||V_SQL_DMS_CODE
              ||V_SQL_DMS_NAME
              ||V_SQL_PROFITS_NAME
              ||V_IN_BASE_LEVEL||'
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              NVL(T2.ANNUAL_AMP,0) AS ANNUAL_AMP,
              T1.PARENT_CODE,
              T1.PARENT_CN_NAME,
              -1 AS CREATED_BY,
              CURRENT_TIMESTAMP AS CREATION_DATE,
              -1 AS LAST_UPDATED_BY,
              CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
              ''N'' AS DEL_FLAG,
              T1.VIEW_FLAG,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME
           FROM YEAR_ANNUAL_AMP_TMP T2
           INNER JOIN '||V_TMP_TABLE||' T1
           ON  T1.PERIOD_YEAR = T2.PERIOD_YEAR
           AND NVL(T1.LV0_PROD_RND_TEAM_CODE,''SNULL0'') = NVL(T2.LV0_PROD_RND_TEAM_CODE,''SNULL0'') 
           AND NVL(T1.LV1_PROD_RND_TEAM_CODE,''SNULL1'') = NVL(T2.LV1_PROD_RND_TEAM_CODE,''SNULL1'') 
           AND NVL(T1.LV2_PROD_RND_TEAM_CODE,''SNULL2'') = NVL(T2.LV2_PROD_RND_TEAM_CODE,''SNULL2'') 
           '||V_REL_LV3_PROD_RND_TEAM_CODE
           ||V_REL_DMS_CODE
           ||V_REL_SPART
           ||V_REL_DIFF_COLUMN
           ||V_REL_PROFITS_NAME
           ||V_REL_BASE_LEVEL
           ||' AND T1.GROUP_CODE =T2.GROUP_CODE
		   AND T1.PARENT_CODE = T2.PARENT_CODE     -- 20250113新增
           AND T1.VIEW_FLAG = T2.VIEW_FLAG
           AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
           AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
           AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
           AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
           ';           
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       EXECUTE IMMEDIATE V_SQL;      
       DBMS_OUTPUT.PUT_LINE('ITEM层级涨跌幅数据插入表成功');
                                        
   --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||' 的ITEM层级的数据到'||V_MID_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
     
  --重置公用变量入参
    V_LV1_PROD_TEAM := 'LV1_PROD_RND_TEAM_CODE,
                        LV1_PROD_RD_TEAM_CN_NAME,
                       ';
    V_LV2_PROD_TEAM := 'LV2_PROD_RND_TEAM_CODE,
                        LV2_PROD_RD_TEAM_CN_NAME,
                       ';
    V_IN_LV1_PROD_TEAM := 'T1.LV1_PROD_RND_TEAM_CODE,
                           T1.LV1_PROD_RD_TEAM_CN_NAME,
                       ';
    V_IN_LV2_PROD_TEAM := 'T1.LV2_PROD_RND_TEAM_CODE,
                           T1.LV2_PROD_RD_TEAM_CN_NAME,
                       ';
    V_REL_PROD_RND_TEAM_CODE := ' AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE ';
  IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
     V_REL_DIFF_COLUMN := '';
  END IF;
   
  --通用颗粒度的维度时，不需要L1和L2字段
  IF F_DIMENSION_TYPE = 'U' THEN 
     V_REL_PROFITS_NAME := '';
     V_REL_DMS_CODE := '';
     IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407版本新增IAS
        V_SQL_CEGUP_PARENT := '
                              CASE WHEN T1.VIEW_FLAG = ''0'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                   WHEN T1.VIEW_FLAG = ''1'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                   WHEN T1.VIEW_FLAG = ''2'' THEN T1.LV1_PROD_RND_TEAM_CODE 
                                   WHEN T1.VIEW_FLAG = ''3'' THEN T1.LV2_PROD_RND_TEAM_CODE 
                              ELSE T1.LV3_PROD_RND_TEAM_CODE
                              END AS PARENT_CODE,';             
        V_SQL_CEGUP_PARENT_NAME := '
                              CASE WHEN T1.VIEW_FLAG = ''0'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                   WHEN T1.VIEW_FLAG = ''1'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                   WHEN T1.VIEW_FLAG = ''2'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME 
                                   WHEN T1.VIEW_FLAG = ''3'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME 
                              ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                              END AS PARENT_CN_NAME,';     
     ELSE   -- ICT/数字能源
        V_SQL_CEGUP_PARENT := '
                              CASE WHEN T1.VIEW_FLAG = ''0'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                   WHEN T1.VIEW_FLAG = ''1'' THEN T1.LV0_PROD_RND_TEAM_CODE
                                   WHEN T1.VIEW_FLAG = ''2'' THEN T1.LV1_PROD_RND_TEAM_CODE 
                              ELSE T1.LV2_PROD_RND_TEAM_CODE
                              END AS PARENT_CODE,';             
        V_SQL_CEGUP_PARENT_NAME := '
                              CASE WHEN T1.VIEW_FLAG = ''0'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                   WHEN T1.VIEW_FLAG = ''1'' THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                                   WHEN T1.VIEW_FLAG = ''2'' THEN T1.LV1_PROD_RD_TEAM_CN_NAME 
                              ELSE T1.LV2_PROD_RD_TEAM_CN_NAME
                              END AS PARENT_CN_NAME,';             
     END IF;
  -- 盈利颗粒度的维度时，不需要LV3字段         
  ELSIF F_DIMENSION_TYPE = 'P' THEN 
     V_IN_LV3_PROD_RND_TEAM := '';
     V_REL_DMS_CODE := '';
     V_PROFITS_NAME_BAK := V_PROFITS_NAME;
     V_L1_L2_BAK := V_L1_L2;
  -- 202403版本，删除盈利颗粒度0、1、2等视角逻辑
     V_SQL_CEG_PARENT := '
                    CASE WHEN T1.VIEW_FLAG = ''3'' THEN T1.L1_NAME
                    ELSE T1.L2_NAME
                    END AS PARENT_CODE,';
     V_SQL_CEG_PARENT_NAME := '
                    CASE WHEN T1.VIEW_FLAG = ''3'' THEN T1.L1_NAME
                    ELSE T1.L2_NAME
                    END AS PARENT_CN_NAME,';
     V_SQL_CEGUP_PARENT := '
                    CASE WHEN T1.VIEW_FLAG = ''3'' THEN T1.LV2_PROD_RND_TEAM_CODE
                    ELSE T1.L1_NAME
                    END AS PARENT_CODE,';
     V_SQL_CEGUP_PARENT_NAME := '
                    CASE WHEN T1.VIEW_FLAG = ''3'' THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                    ELSE T1.L1_NAME
                    END AS PARENT_CN_NAME,';
  --量纲颗粒度的维度时，不需要盈利字段
  ELSIF F_DIMENSION_TYPE = 'D' THEN
     V_REL_PROFITS_NAME := '';
     V_DMS_TOTAL_BAK := V_DMS_TOTAL;
     V_DMS_CODE_BAK := V_DMS_CODE;
     V_DMS_NAME_BAK := V_DMS_NAME;
     V_SPART_CODE_BAK := V_SPART_CODE;
     V_SPART_NAME_BAK := V_SPART_NAME;
     V_SQL_CEG_PARENT := ' CASE WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.SPART_CODE   -- 202405版本，新增视角12
                                ELSE T1.DMS_CODE END AS PARENT_CODE, ';
     V_SQL_CEG_PARENT_NAME := ' CASE WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.SPART_CN_NAME   -- 202405版本，新增视角12
                                ELSE T1.DMS_CN_NAME END AS PARENT_CN_NAME, ';
     V_SQL_CEGUP_PARENT := '
                    CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RND_TEAM_CODE
                         WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CODE
                         WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.DIMENSION_SUB_DETAIL_CODE   -- 202405版本，新增视角12
                    ELSE T1.DIMENSION_SUBCATEGORY_CODE
                    END AS PARENT_CODE,';
     V_SQL_CEGUP_PARENT_NAME := '
                    CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN T1.PROD_RD_TEAM_CN_NAME
                         WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN T1.DIMENSION_CN_NAME
                         WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.DIMENSION_SUB_DETAIL_CN_NAME   -- 202405版本，新增视角12
                    ELSE T1.DIMENSION_SUBCATEGORY_CN_NAME
                    END AS PARENT_CN_NAME,';
  END IF;                                            
    
  -- 对于不同颗粒度、不同视角的不同层级进行循环计算
  FOR LEVEL_NUM IN V_BEGIN_NUM .. V_LEVEL_NUM LOOP
  -- 品类层级涨跌幅计算
  IF LEVEL_NUM = 1 AND F_COST_TYPE = 'P' THEN 
      V_GROUP_TOTAL := '
                        T1.CATEGORY_CODE AS GROUP_CODE,
                        T1.CATEGORY_CN_NAME AS GROUP_CN_NAME,
                        ''CATEGORY'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''ITEM''';
      V_SQL_PARENT := ' T1.L4_CEG_CODE AS PARENT_CODE,
                        T1.L4_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,';
  -- 模块层级涨跌幅计算
  ELSIF LEVEL_NUM = 2 AND F_COST_TYPE = 'P' THEN 
      V_BASE_LEVEL := '
                     L3_CEG_CODE,
                     L3_CEG_SHORT_CN_NAME,
                     L4_CEG_CODE,
                     L4_CEG_SHORT_CN_NAME,';
      V_IN_BASE_LEVEL := '
                     T1.L3_CEG_CODE,
                     T1.L3_CEG_SHORT_CN_NAME,
                     T1.L4_CEG_CODE,
                     T1.L4_CEG_SHORT_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.L4_CEG_CODE AS GROUP_CODE,
                        T1.L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                        ''MODL'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''CATEGORY''';
      V_SQL_PARENT := ' T1.L3_CEG_CODE AS PARENT_CODE,
                        T1.L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,';
  -- 专家团层级涨跌幅计算
  ELSIF LEVEL_NUM = 3 AND F_COST_TYPE = 'P' THEN 
      V_BASE_LEVEL := '
                     L3_CEG_CODE,
                     L3_CEG_SHORT_CN_NAME,';
      V_IN_BASE_LEVEL := '
                     T1.L3_CEG_CODE,
                     T1.L3_CEG_SHORT_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.L3_CEG_CODE AS GROUP_CODE,
                        T1.L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,
                        ''CEG'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''MODL''';  
      V_SQL_PARENT := V_SQL_CEG_PARENT||V_SQL_CEG_PARENT_NAME;   -- 专家团层级的父层级维度不一致
  -- 制造对象层级涨跌幅计算
  ELSIF LEVEL_NUM = 2 AND F_COST_TYPE = 'M' THEN 
      V_GROUP_TOTAL := '
                        T1.MANUFACTURE_OBJECT_CODE AS GROUP_CODE,
                        T1.MANUFACTURE_OBJECT_CN_NAME AS GROUP_CN_NAME,
                        ''MANUFACTURE_OBJECT'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''ITEM''';
      V_SQL_PARENT := ' T1.SHIPPING_OBJECT_CODE AS PARENT_CODE,
                        T1.SHIPPING_OBJECT_CN_NAME AS PARENT_CN_NAME,';
  -- 发货对象层级涨跌幅计算
  ELSIF LEVEL_NUM = 3 AND F_COST_TYPE = 'M' THEN 
      V_BASE_LEVEL := '
                     SHIPPING_OBJECT_CODE,
                     SHIPPING_OBJECT_CN_NAME,';
      V_IN_BASE_LEVEL := '
                     T1.SHIPPING_OBJECT_CODE,
                     T1.SHIPPING_OBJECT_CN_NAME,';
      V_GROUP_TOTAL := '
                        T1.SHIPPING_OBJECT_CODE AS GROUP_CODE,
                        T1.SHIPPING_OBJECT_CN_NAME AS GROUP_CN_NAME,
                        ''SHIPPING_OBJECT'' AS GROUP_LEVEL,';
      V_CHILD_LEVEL := '''MANUFACTURE_OBJECT''';  
      V_REL_BASE_LEVEL := '
                 AND NVL(T1.SHIPPING_OBJECT_CODE,''SNULL00'') = NVL(T2.SHIPPING_OBJECT_CODE,''SNULL00'')';
      V_SQL_PARENT := V_SQL_CEG_PARENT||V_SQL_CEG_PARENT_NAME;   -- 专家团层级的父层级维度不一致
  -- 不同颗粒度，不同视角，专家团层级依据不同视角对应的上一层级往上卷积至不同数据层级
  ELSIF LEVEL_NUM = 4 THEN 
    V_BASE_LEVEL := '';
    V_IN_BASE_LEVEL := '';
    IF F_COST_TYPE = 'P' THEN   -- 采购成本
      V_CHILD_LEVEL := '''CEG'''; 
    ELSIF F_COST_TYPE = 'M' THEN   -- 制造成本
      V_CHILD_LEVEL := '''SHIPPING_OBJECT'''; 
      V_REL_BASE_LEVEL := '';
    END IF;
      V_SQL_PARENT := V_SQL_CEGUP_PARENT||V_SQL_CEGUP_PARENT_NAME;
    IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_GROUP_TOTAL := 'T1.PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG = ''0'' THEN ''LV0''
                             WHEN T1.VIEW_FLAG = ''1'' THEN ''LV1''
                             WHEN T1.VIEW_FLAG = ''2'' THEN ''LV2''
                             WHEN T1.VIEW_FLAG = ''3'' THEN ''LV3''
                        ELSE ''LV4''
                        END AS GROUP_LEVEL,'; 
    ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_GROUP_TOTAL := 'CASE WHEN T1.VIEW_FLAG IN (''3'',''4'') THEN T1.PROFITS_NAME
                             ELSE T1.PROD_RND_TEAM_CODE
                        END AS GROUP_CODE,
                        CASE WHEN T1.VIEW_FLAG IN (''3'',''4'') THEN T1.PROFITS_NAME
                             ELSE T1.PROD_RD_TEAM_CN_NAME
                        END AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG = ''3'' THEN ''L1''
                             ELSE ''L2''
                        END AS GROUP_LEVEL,';            
    ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_GROUP_TOTAL := 'CASE WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.SPART_CODE       -- 202405版本，新增视角12
                             ELSE T1.DMS_CODE END AS GROUP_CODE,
                        CASE WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN T1.SPART_CN_NAME    -- 202405版本，新增视角12
                             ELSE T1.DMS_CN_NAME END AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG IN (''0'',''3'',''6'') THEN ''DIMENSION''
                             WHEN T1.VIEW_FLAG IN (''1'',''4'',''7'') THEN ''SUBCATEGORY''
                             WHEN T1.VIEW_FLAG IN (''9'',''10'',''11'',''12'') THEN ''SPART''           -- 202405版本，新增视角12
                             ELSE ''SUB_DETAIL''
                        END AS GROUP_LEVEL,'; --不同视角层级值不同，需定义
    END IF;
  -- 通用：LV2层级/盈利：L1层级/量纲：SUBCATEGORY层级
  ELSIF LEVEL_NUM = 5 THEN 
    IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_CHILD_LEVEL := '''LV4''';  
      V_DIFF_COLUMN_CODE := '';
      V_DIFF_COLUMN_NAME := '';
      V_IN_DIFF_COLUMN := '';   -- LV4重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV3_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';    
      V_GROUP_TOTAL := 'T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV3'' AS GROUP_LEVEL,
                        ';
    ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_CHILD_LEVEL := '''L2''';  
      V_L1_L2 := 'L1_NAME,';
      V_IN_L1_L2 := 'T1.L1_NAME,';
      V_SQL_PARENT := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.L1_NAME AS GROUP_CODE,
                        T1.L1_NAME AS GROUP_CN_NAME,
                        ''L1'' AS GROUP_LEVEL,
                        ';  
      V_SQL_PROFITS_NAME := 'T1.L1_NAME AS PROFITS_NAME,';
    ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''SPART''';  
      V_GROUP_TOTAL := 'T1.DIMENSION_SUB_DETAIL_CODE AS GROUP_CODE,
                        T1.DIMENSION_SUB_DETAIL_CN_NAME AS GROUP_CN_NAME,
                        ''SUB_DETAIL'' AS GROUP_LEVEL,'; --不同视角层级值不同，需定义
      V_SQL_PARENT := ' T1.DIMENSION_SUBCATEGORY_CODE AS PARENT_CODE,
                        T1.DIMENSION_SUBCATEGORY_CN_NAME AS PARENT_CN_NAME,';
      V_SQL_DMS_CODE := 'T1.DIMENSION_SUB_DETAIL_CODE AS DMS_CODE,';  
      V_SQL_DMS_NAME := 'T1.DIMENSION_SUB_DETAIL_CN_NAME AS DMS_CN_NAME,';    
      V_SPART_CODE := '';
      V_SPART_NAME := '';
      V_IN_SPART := '';
    END IF;
  -- 通用：LV1层级/盈利：LV2层级/量纲：DIMENSION层级
  ELSIF LEVEL_NUM = 6 THEN 
    IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度 
      V_CHILD_LEVEL := '''LV3''';  
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM := '';   -- LV3重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV2_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';    
      V_GROUP_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV2'' AS GROUP_LEVEL,
                        ';
    ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_CHILD_LEVEL := '''L1''';  
      V_L1_L2 := '';
      V_IN_L1_L2 := '';
      V_SQL_PARENT := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV2'' AS GROUP_LEVEL,
                        ';  
      V_PROFITS_NAME := '';
      V_SQL_PROFITS_NAME := '';
    ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''SUB_DETAIL''';  
      V_DMS_CODE := 'DIMENSION_CODE,
                     DIMENSION_SUBCATEGORY_CODE,';
      V_DMS_NAME := 'DIMENSION_CN_NAME,
                     DIMENSION_SUBCATEGORY_CN_NAME,';
      V_IN_DMS_CODE := 'T1.DIMENSION_CODE,
                        T1.DIMENSION_SUBCATEGORY_CODE,';
      V_IN_DMS_NAME := 'T1.DIMENSION_CN_NAME,
                        T1.DIMENSION_SUBCATEGORY_CN_NAME,';
      V_SQL_PARENT := 'T1.DIMENSION_CODE AS PARENT_CODE,
                       T1.DIMENSION_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.DIMENSION_SUBCATEGORY_CODE AS GROUP_CODE,
                        T1.DIMENSION_SUBCATEGORY_CN_NAME AS GROUP_CN_NAME,
                        ''SUBCATEGORY'' AS GROUP_LEVEL,
                        ';  
      V_SQL_DMS_CODE := 'T1.DIMENSION_SUBCATEGORY_CODE AS DMS_CODE,';  
      V_SQL_DMS_NAME := 'T1.DIMENSION_SUBCATEGORY_CN_NAME AS DMS_CN_NAME,';    
    END IF;
  -- 通用：ICT层级/盈利：LV1层级/量纲：不同视角对应不同的重量级团队层级
  ELSIF LEVEL_NUM = 7 THEN 
    IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_CHILD_LEVEL := '''LV2''';  
      V_LV2_PROD_TEAM := '';
      V_IN_LV2_PROD_TEAM := '';   -- LV2重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';    
      V_GROUP_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV1'' AS GROUP_LEVEL,
                        ';
    ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_CHILD_LEVEL := '''LV2''';  
      V_LV2_PROD_TEAM := '';
      V_IN_LV2_PROD_TEAM := '';   -- LV2重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV1'' AS GROUP_LEVEL,
                        ';  
    ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''SUBCATEGORY''';  
      V_DMS_CODE := 'DIMENSION_CODE,';
      V_DMS_NAME := 'DIMENSION_CN_NAME,';
      V_IN_DMS_CODE := 'T1.DIMENSION_CODE,';
      V_IN_DMS_NAME := 'T1.DIMENSION_CN_NAME,';
  IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
      V_SQL_PARENT := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CODE
                       ELSE T1.LV3_PROD_RND_TEAM_CODE
                       END AS PARENT_CODE,
                       CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CN_NAME
                       ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                       END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
  ELSIF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
      V_SQL_PARENT := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                       ELSE T1.LV3_PROD_RND_TEAM_CODE
                       END AS PARENT_CODE,
                       CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                       ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                       END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
      V_SQL_PARENT := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RND_TEAM_CODE
                       ELSE T1.LV3_PROD_RND_TEAM_CODE
                       END AS PARENT_CODE,
                       CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV2_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV4_PROD_RD_TEAM_CN_NAME
                       ELSE T1.LV3_PROD_RD_TEAM_CN_NAME
                       END AS PARENT_CN_NAME,';   -- 量纲层级的父层级
  END IF;
      V_GROUP_TOTAL := 'T1.DIMENSION_CODE AS GROUP_CODE,
                        T1.DIMENSION_CN_NAME AS GROUP_CN_NAME,
                        ''DIMENSION'' AS GROUP_LEVEL,
                        ';  
      V_SQL_DMS_CODE := 'T1.DIMENSION_CODE AS DMS_CODE,';  
      V_SQL_DMS_NAME := 'T1.DIMENSION_CN_NAME AS DMS_CN_NAME,';      
    END IF;
  -- 通用：ICT层级/盈利：ICT层级/量纲：COA层级
  ELSIF LEVEL_NUM = 8 THEN 
    IF F_DIMENSION_TYPE = 'U' THEN   -- 通用颗粒度
      V_CHILD_LEVEL := '''LV1''';  
      V_LV1_PROD_TEAM := '';
      V_IN_LV1_PROD_TEAM := '';   -- LV1重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';    
      V_GROUP_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV0'' AS GROUP_LEVEL,
                        ';
    ELSIF F_DIMENSION_TYPE = 'P' THEN   -- 盈利颗粒度
      V_CHILD_LEVEL := '''LV1''';  
      V_LV1_PROD_TEAM := '';
      V_IN_LV1_PROD_TEAM := '';   -- LV1重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV0'' AS GROUP_LEVEL,
                        ';  
    ELSIF F_DIMENSION_TYPE = 'D' THEN   -- 量纲颗粒度
      V_CHILD_LEVEL := '''DIMENSION''';  
      V_DMS_CODE := '';
      V_DMS_NAME := '';
      V_IN_DMS_CODE := '';
      V_IN_DMS_NAME := '';
      V_SQL_DMS_CODE := '';  
      V_SQL_DMS_NAME := '';        
      V_DMS_TOTAL := '';
      V_SQL_PARENT := 'CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV0_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RND_TEAM_CODE
                            WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV3_PROD_RND_TEAM_CODE   -- 202405版本，新增视角12
                            ELSE T1.LV2_PROD_RND_TEAM_CODE
                       END AS PARENT_CODE,
                       CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN T1.LV0_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN T1.LV1_PROD_RD_TEAM_CN_NAME
                            WHEN T1.VIEW_FLAG IN (''12'') THEN T1.LV3_PROD_RD_TEAM_CN_NAME   -- 202405版本，新增视角12
                            ELSE T1.LV2_PROD_RD_TEAM_CN_NAME
                       END AS PARENT_CN_NAME,';
   IF F_INDUSTRY_FLAG = 'E' THEN   -- 数字能源
      V_GROUP_TOTAL := 'CASE WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CODE                  -- 202405版本，新增视角12
                               ELSE T1.PROD_RND_TEAM_CODE END AS GROUP_CODE,
                        CASE WHEN T1.VIEW_FLAG IN (''12'') THEN T1.COA_CN_NAME
                             ELSE T1.PROD_RD_TEAM_CN_NAME END AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN ''LV1''
                             WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
                             WHEN T1.VIEW_FLAG IN (''12'') THEN ''COA''
                             ELSE ''LV3''
                        END AS GROUP_LEVEL,';  
   ELSIF F_INDUSTRY_FLAG = 'I' THEN   -- ICT
      V_GROUP_TOTAL := 'T1.PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN ''LV1''
                             WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
                             ELSE ''LV3''
                        END AS GROUP_LEVEL,';  
   ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
      V_GROUP_TOTAL := 'T1.PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        CASE WHEN T1.VIEW_FLAG IN (''0'',''1'',''2'',''9'') THEN ''LV1''
                             WHEN T1.VIEW_FLAG IN (''3'',''4'',''5'',''10'') THEN ''LV2''
                             WHEN T1.VIEW_FLAG IN (''12'') THEN ''LV4''
                             ELSE ''LV3''
                        END AS GROUP_LEVEL,';  
   END IF;
    END IF;
  -- LV3重量级团队层级
  ELSIF LEVEL_NUM = 9 THEN  
    IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
      V_CHILD_LEVEL := '''LV4''';  
    ELSE
      V_CHILD_LEVEL := '''COA''';  
    END IF;
      V_DIFF_COLUMN_CODE := '';
      V_DIFF_COLUMN_NAME := '';
      V_IN_DIFF_COLUMN := '';   -- COA层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV3_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV3_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV2_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV3'' AS GROUP_LEVEL,
                        ';  
  -- LV2重量级团队层级
  ELSIF LEVEL_NUM = 10 THEN  
      V_CHILD_LEVEL := '''LV3''';  
      V_LV3_PROD_RND_TEAM_CODE := '';
      V_LV3_PROD_RD_TEAM_CN_NAME := '';
      V_IN_LV3_PROD_RND_TEAM := '';   -- LV3重量级团队层级置空
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV2_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV2_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV1_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV2'' AS GROUP_LEVEL,
                        ';  
  -- 量纲：LV1层级
  ELSIF LEVEL_NUM = 11 THEN   
      V_CHILD_LEVEL := '''LV2''';  
      V_LV2_PROD_TEAM := '';
      V_IN_LV2_PROD_TEAM := '';   -- LV2重量级团队层级置空  
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV1_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV1_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV1'' AS GROUP_LEVEL,
                        ';    
  -- 量纲：ICT层级
  ELSIF LEVEL_NUM = 12 THEN   
      V_CHILD_LEVEL := '''LV1''';  
      V_LV1_PROD_TEAM := '';
      V_IN_LV1_PROD_TEAM := '';   -- LV1重量级团队层级置空  
      V_SQL_PROD_RND_TEAM_CODE := 'T1.LV0_PROD_RND_TEAM_CODE AS PROD_RND_TEAM_CODE,';
      V_SQL_PROD_RD_TEAM_CN_NAME := 'T1.LV0_PROD_RD_TEAM_CN_NAME AS PROD_RD_TEAM_CN_NAME,';  
      V_SQL_PARENT := 'T1.LV0_PROD_RND_TEAM_CODE AS PARENT_CODE,
                       T1.LV0_PROD_RD_TEAM_CN_NAME AS PARENT_CN_NAME,';
      V_GROUP_TOTAL := 'T1.LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
                        T1.LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,
                        ''LV0'' AS GROUP_LEVEL,
                        ';    
  END IF;
  
  -- 年度涨跌幅主逻辑SQL
  V_SQL := '
     INSERT INTO '||V_MID_TABLE||'(
                  VERSION_ID,
                  PERIOD_YEAR,
                  LV0_PROD_RND_TEAM_CODE,
                  LV0_PROD_RD_TEAM_CN_NAME,
                  '||V_LV1_PROD_TEAM
                  ||V_LV2_PROD_TEAM
                  ||V_LV3_PROD_RND_TEAM_CODE
                  ||V_LV3_PROD_RD_TEAM_CN_NAME
                  ||V_DMS_CODE
                  ||V_DMS_NAME
                  ||V_L1_L2
                  ||V_PROD_RND_TEAM
                  ||V_DMS_TOTAL
                  ||V_SPART_CODE
                  ||V_SPART_NAME
                  ||V_DIFF_COLUMN_CODE
                  ||V_DIFF_COLUMN_NAME
                  ||V_PROFITS_NAME
                  ||V_BASE_LEVEL||'
                  GROUP_CODE,
                  GROUP_CN_NAME,
                  GROUP_LEVEL,
                  ANNUAL_AMP,
                  PARENT_CODE,
                  PARENT_CN_NAME,
                  CREATED_BY,
                  CREATION_DATE,
                  LAST_UPDATED_BY,
                  LAST_UPDATE_DATE,
                  DEL_FLAG,
                  VIEW_FLAG,
                  CALIBER_FLAG,
                  OVERSEA_FLAG,
                  LV0_PROD_LIST_CODE,
                  LV0_PROD_LIST_CN_NAME
                 )
-- 子层级数据分视角按层级进行计算（关联单年权重表和涨跌幅临时表）
    WITH BY_YEAR_TMP AS(
         SELECT T1.PERIOD_YEAR,
                T1.LV0_PROD_RND_TEAM_CODE,
                T1.LV0_PROD_RD_TEAM_CN_NAME,
                '||V_IN_LV1_PROD_TEAM
                ||V_IN_LV2_PROD_TEAM
                ||V_IN_LV3_PROD_RND_TEAM
                ||V_IN_DMS_CODE
                ||V_IN_DMS_NAME
                ||V_IN_L1_L2
                ||V_SQL_PROD_RND_TEAM_CODE
                ||V_SQL_PROD_RD_TEAM_CN_NAME
                ||V_SQL_DMS_CODE
                ||V_SQL_DMS_NAME
                ||V_IN_SPART
                ||V_IN_DIFF_COLUMN
                ||V_SQL_PROFITS_NAME
                ||V_IN_BASE_LEVEL
                ||'T1.GROUP_CODE,
                T1.GROUP_CN_NAME,
                T1.GROUP_LEVEL,
                T1.ANNUAL_AMP * T2.WEIGHT_RATE AS ANNUAL_AMP_WEIGHT,
                T1.PARENT_CODE,
                T1.PARENT_CN_NAME,
                T1.VIEW_FLAG,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME
             FROM '||V_MID_TABLE||' T1
             INNER JOIN '||V_FROM_TABLE_2||' T2
             ON T1.VERSION_ID = T2.VERSION_ID
             AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
             '||V_REL_PROD_RND_TEAM_CODE
             ||V_REL_DMS_CODE
             ||V_REL_SPART
             ||V_REL_DIFF_COLUMN
             ||V_REL_PROFITS_NAME
             ||V_REL_BASE_LEVEL
             ||' AND T1.GROUP_CODE =T2.GROUP_CODE
             AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
             AND T1.VIEW_FLAG = T2.VIEW_FLAG
             AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
             AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
             AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
             AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
             WHERE T1.VERSION_ID = '||V_VERSION_ID||'
             AND T1.GROUP_LEVEL = '||V_CHILD_LEVEL||'
            )
 -- 子层级数据分视角往上卷积（按层级卷积上表计算结果）
         SELECT '
                ||V_VERSION_ID||' AS VERSION_ID,
                T1.PERIOD_YEAR,
                T1.LV0_PROD_RND_TEAM_CODE,
                T1.LV0_PROD_RD_TEAM_CN_NAME,
                '||V_IN_LV1_PROD_TEAM
                ||V_IN_LV2_PROD_TEAM
                ||V_IN_LV3_PROD_RND_TEAM
                ||V_IN_DMS_CODE
                ||V_IN_DMS_NAME
                ||V_L1_L2
                ||V_PROD_RND_TEAM
                ||V_DMS_TOTAL
                ||V_IN_SPART
                ||V_IN_DIFF_COLUMN
                ||V_PROFITS_NAME
                ||V_BASE_LEVEL
                ||V_GROUP_TOTAL||'
                NVL(SUM(T1.ANNUAL_AMP_WEIGHT),0) AS ANNUAL_AMP,
                '||V_SQL_PARENT||'
                -1 AS CREATED_BY,
                CURRENT_TIMESTAMP AS CREATION_DATE,
                -1 AS LAST_UPDATED_BY,
                CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                ''N'' AS DEL_FLAG,
                T1.VIEW_FLAG,
                T1.CALIBER_FLAG,
                T1.OVERSEA_FLAG,
                T1.LV0_PROD_LIST_CODE,
                T1.LV0_PROD_LIST_CN_NAME
             FROM BY_YEAR_TMP T1
             GROUP BY T1.PERIOD_YEAR,
                      T1.LV0_PROD_RND_TEAM_CODE,
                      T1.LV0_PROD_RD_TEAM_CN_NAME,
                      '||V_IN_LV3_PROD_RND_TEAM
                      ||V_IN_L1_L2
                      ||V_IN_LV1_PROD_TEAM
                      ||V_IN_LV2_PROD_TEAM
                      ||V_IN_DMS_CODE
                      ||V_IN_DMS_NAME
                      ||V_IN_SPART
                      ||V_IN_DIFF_COLUMN
                      ||V_L1_L2
                      ||V_PROD_RND_TEAM
                      ||V_DMS_TOTAL
                      ||V_PROFITS_NAME
                      ||V_BASE_LEVEL
                      ||'T1.VIEW_FLAG,
                      T1.CALIBER_FLAG,
                      T1.OVERSEA_FLAG,
                      T1.LV0_PROD_LIST_CODE,
                      T1.LV0_PROD_LIST_CN_NAME'
                      ;
                                             
       DBMS_OUTPUT.PUT_LINE(V_SQL);
       EXECUTE IMMEDIATE V_SQL;  
       DBMS_OUTPUT.PUT_LINE('第'||LEVEL_NUM||'次循环，不同层级涨跌幅数据插入表成功');

    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入版本号为：'||V_VERSION_ID ||' 的第'||LEVEL_NUM||'次循环，颗粒度为：'||F_DIMENSION_TYPE||' 的数据到'||V_MID_TABLE,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                                                 
   END LOOP;   -- 结束循环
 
 -- 删除分视角年度涨跌幅表同版本的数据
   EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
 
 --8.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除VERSION_ID= '||V_VERSION_ID ||' 的分视角年度涨跌幅（'||V_TO_TABLE||'）数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 重置变量入参
    IF F_COST_TYPE = 'M' THEN
        V_BASE_LEVEL := '
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,';
    ELSE NULL;
    END IF;
 
 -- 替代指数插数逻辑不一致
 IF F_INDUSTRY_FLAG = 'REPL' THEN 
  V_SQL := '
    INSERT INTO '||V_TO_TABLE||'(
                VERSION_ID,
                PERIOD_YEAR,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                ANNUAL_AMP,
                CALIBER_FLAG,
                VIEW_FLAG,
                DATA_TYPE,
                PARENT_CODE,
                PARENT_CN_NAME,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG
               )

 -- 临时表中全量的数据，并得到对应的状态字段值
        SELECT T1.VERSION_ID,
               T1.PERIOD_YEAR,
               T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               T1.ANNUAL_AMP,
               T1.CALIBER_FLAG,
               T1.VIEW_FLAG,
               '' YTD'' AS DATA_TYPE,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG
           FROM '||V_MID_TABLE||' T1
           WHERE T1.VERSION_ID = '||V_VERSION_ID||'
           AND T1.GROUP_LEVEL IN (''LV3'',''LV2'',''LV1'',''LV0'')';
                         
        EXECUTE IMMEDIATE V_SQL;    
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       DBMS_OUTPUT.PUT_LINE('把临时表数据插入结果表'); 
 ELSE 
 -- 插入所有数据到分视角年度涨跌幅表
 V_SQL := '
    INSERT INTO '||V_TO_TABLE||'(
                VERSION_ID,
                PERIOD_YEAR,
                PROD_RND_TEAM_CODE,
                PROD_RD_TEAM_CN_NAME,
                '||V_DMS_TOTAL_BAK
                ||V_DMS_CODE_BAK
                ||V_DMS_NAME_BAK
                ||V_SPART_CODE_BAK
                ||V_SPART_NAME_BAK
                ||V_DIFF_COLUMN_CODE_BAK
                ||V_DIFF_COLUMN_NAME_BAK
                ||V_PROFITS_NAME_BAK
                ||V_L1_L2_BAK
                ||V_BASE_LEVEL
                ||'GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                ANNUAL_AMP,
                PARENT_CODE,
                PARENT_CN_NAME,
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG,
                VIEW_FLAG,
                CALIBER_FLAG,
                OVERSEA_FLAG,
                LV0_PROD_LIST_CODE,
                LV0_PROD_LIST_CN_NAME
               )

 -- 临时表中全量的数据，并得到对应的状态字段值
        SELECT T1.VERSION_ID,
               T1.PERIOD_YEAR,
               T1.PROD_RND_TEAM_CODE,
               T1.PROD_RD_TEAM_CN_NAME,
               '||V_DMS_TOTAL_BAK
               ||V_DMS_CODE_BAK
               ||V_DMS_NAME_BAK
               ||V_SPART_CODE_BAK
               ||V_SPART_NAME_BAK
               ||V_DIFF_COLUMN_CODE_BAK
               ||V_DIFF_COLUMN_NAME_BAK
               ||V_PROFITS_NAME_BAK
               ||V_L1_L2_BAK
               ||V_BASE_LEVEL
               ||'T1.GROUP_CODE,
               T1.GROUP_CN_NAME,
               T1.GROUP_LEVEL,
               T1.ANNUAL_AMP,
               T1.PARENT_CODE,
               T1.PARENT_CN_NAME,
               -1 AS CREATED_BY,
               CURRENT_TIMESTAMP AS CREATION_DATE,
               -1 AS LAST_UPDATED_BY,
               CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
               ''N'' AS DEL_FLAG,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.OVERSEA_FLAG,
               T1.LV0_PROD_LIST_CODE,
               T1.LV0_PROD_LIST_CN_NAME
           FROM '||V_MID_TABLE||' T1
           WHERE T1.VERSION_ID = '||V_VERSION_ID;
                         
        EXECUTE IMMEDIATE V_SQL;    
       DBMS_OUTPUT.PUT_LINE(V_SQL); 
       DBMS_OUTPUT.PUT_LINE('把临时表数据插入结果表'); 
    
    END IF;
                                  
--9.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入VERSION_ID= '||V_VERSION_ID ||' 的数据到分视角年度涨跌幅表（'||V_TO_TABLE||'）',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');            
   
 -- 10.收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_MID_TABLE;
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  DBMS_OUTPUT.PUT_LINE('收集统计信息成功'); 
  
  --11.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_MID_TABLE||'/'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END
$$
/

