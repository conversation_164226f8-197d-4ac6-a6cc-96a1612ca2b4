-- Name: f_dm_foi_group_lev_weight_t_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_group_lev_weight_t_bak(f_cate_version bigint, f_item_version bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2023-2-1
创建人  ：黄心蕊 hwx1187045
背景描述：权重表数据同步
参数描述：参数一(f_cate_version)：TOP品类清单最新版本号
					参数二(f_item_version)：规格品清单最新版本号
					参数二(x_success_flag)  ：运行状态返回值-成功或者失败
事例    ：select FIN_DM_OPT_FOI.f_dm_foi_group_lev_weight_t(11,12) --一个版本的数据
****************************************************************************************************************************************************************/
 declare 
 declare 
  v_sp_name           varchar(50):= 'FIN_DM_OPT_FOI.F_DM_FOI_GROUP_LEV_WEIGHT_T';
  v_version           bigint; 
  v_dml_row_count     number default 0 ;
  v_LV2_amt           numeric;
  v_cate_version      bigint;
  V_PERIOD            VARCHAR(20) := YEAR(CURRENT_DATE)-3||'-'||YEAR(CURRENT_DATE) ; /*权重表区间值字段‘综合四年’*/
  v_exception_flag    varchar(50):='1';--异常定点
  v_part1_public      TEXT := NULL;   -- 函数公共部分
  v_execute_sql       TEXT := NULL;   -- 执行SQL
  V_INSERT_SQL_BACK   TEXT := NULL;   -- 备份SQL
  V_INSERT_SQL        TEXT := NULL;   -- TOP插数SQL;
  V_LV3_PART          TEXT := NULL;   --LV3逻辑部分
  V_CATE_TYPE_PART    TEXT := NULL;   --品类分类部分
  V_TOP_TYPE_PART     TEXT := NULL;   --TOPN部分
begin
  x_success_flag := '1';
  
 -- 将查询到的数据放到变量中的公共sql
 V_PART1_PUBLIC := '
                    SELECT VALUE 
                        FROM FIN_DM_OPT_FOI.DM_FOI_PLAN_VAR_PARA_T
                        WHERE ENABLE_FLAG = ''Y''
                        AND UPPER(PARA_NAME) = ''$PARA_NAME$'';
                      ';  
                      
 -- 无论是季调还是月调，都通过sql从变量参数表（dm_foi_plan_var_para_t）表中取出对应的规格品版本号
	IF f_cate_version IN (-1,0)
  AND f_item_version IN (-1,0) THEN
  V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-CATEGORY');  -- TOP品类版本号
  EXECUTE V_EXECUTE_SQL INTO V_CATE_VERSION;
  V_EXECUTE_SQL := REPLACE( V_PART1_PUBLIC,'$PARA_NAME$','VERSION_ID-ITEM');  -- 规格品版本号
  EXECUTE V_EXECUTE_SQL INTO V_VERSION;                        
  
-- 业务在前台配置页面调整规格品数据时，Java传参，优先取规格品版本号，若无，则取TOP品类版本号作为生成本次数据的版本号   
    else v_cate_version := f_cate_version;
         v_version := f_item_version; 
end if;
  
 begin
  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 v_exception_flag :='1'; 
--删除重跑
delete from FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t where version_id = v_version;

  --1.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 1,
  F_CAL_LOG_DESC => '删除权重表同版本数据,并取到version_id='||v_version,
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

---------------------------------------------------------------

select nullif(sum(receive_amt_cny) ,0 )
  into v_LV2_amt
  from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t
 where upper(group_level) = 'LV2'
   and continuity_type = '含连续性影响'
   and version_id = v_version
   and append_flag = 'N';

v_exception_flag :='2';
-----------配置页面的topN，四年全品类金额占ICT金额
insert into FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t
  (id,
   period,
   period_type,
   version_id,
   group_code,
   group_cn_name,
   group_level,
   parent_code,
   parent_level,
   weight,
   l2_ceg_cn_name,
   l3_ceg_short_cn_name,
   l4_ceg_short_cn_name,
   l2_ceg_code,
   l3_ceg_code,
   l4_ceg_code,
   top_type,
   top_flag,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag,
   l4_ceg_cn_name,
   l3_ceg_cn_name 
	 )
  select FIN_DM_OPT_FOI.dm_foi_group_lev_weight_s.nextval as id,
         V_PERIOD as period, --区间值为四年
         'S' as period_type, --区间类型为综合四年
         v_version as version_id, --版本
         t1.group_code,
         t1.group_cn_name,
         t1.group_level,
         t1.l4_ceg_code as parent_code,
         'LV2' as parent_level,
         t1.weight,
         t1.l2_ceg_cn_name,
         t1.l3_ceg_short_cn_name,
         t1.l4_ceg_short_cn_name,
         t1.l2_ceg_code,
         t1.l3_ceg_code,
         t1.l4_ceg_code,
         case
           when t1.top_type_number <= 50 then
            'TOP50'
           when t1.top_type_number <= 100 then
            'TOP100'
           when t1.top_type_number <= 300 then
            'TOP300'
           else
            ''
         end as top_type,
         '' as top_flag,
         -1 as created_by,
         current_timestamp as creation_date,
         -1 as last_updated_by,
         current_timestamp as last_update_date,
         'N' as del_flag,
				 t1.l4_ceg_cn_name,
				 t1.l3_ceg_cn_name
    from (select rsp.group_level,
                 rsp.group_code,
                 rsp.group_cn_name,
                 row_number()over(order by sum(rsp.receive_amt_cny) desc) as top_type_number,
                 sum(rsp.receive_amt_cny) / v_LV2_amt as weight,
                 rsp.l4_ceg_code,
                 rsp.l4_ceg_short_cn_name,
                 rsp.l3_ceg_code,
                 rsp.l3_ceg_short_cn_name,
                 rsp.l2_ceg_code,
                 rsp.l2_ceg_cn_name,
				 rsp.l4_ceg_cn_name,
				 rsp.l3_ceg_cn_name
            from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t rsp
           where upper(rsp.group_level) = 'CATEGORY' 
             and rsp.version_id = v_version
			 and rsp.append_flag = 'N'
           group by rsp.group_level,
                    rsp.group_code,
                    rsp.group_cn_name,
                    rsp.l4_ceg_code,
                    rsp.l4_ceg_short_cn_name,
                    rsp.l3_ceg_code,
                    rsp.l3_ceg_short_cn_name,
                    rsp.l2_ceg_code,
                    rsp.l2_ceg_cn_name,
					rsp.l4_ceg_cn_name,
					rsp.l3_ceg_cn_name
		 ) t1;

 --2.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 2,
  F_CAL_LOG_DESC => 'top类别权重插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

 v_exception_flag :='3'; 
--权重饼图处理,供应商层级逻辑处理
----RAISE notice '供应商层级运算';
insert into FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t
  (id,
   period,
   period_type,
   version_id,
   group_code,
   group_cn_name,
   group_level,
   parent_code,
   parent_level,
   weight,
   item_code,
   item_name,
   category_code,
   category_name,
   l2_ceg_cn_name,
   l3_ceg_short_cn_name,
   l4_ceg_short_cn_name,
   l2_ceg_code,
   l3_ceg_code,
   l4_ceg_code,
   top_type,
   top_flag,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag,
   l4_ceg_cn_name,
   l3_ceg_cn_name)

select FIN_DM_OPT_FOI.dm_foi_group_lev_weight_s.nextval as id,
       V_PERIOD as period,
       'S' as period_type,
       v_version as version_id,
       rsp.group_code,
       rsp.group_cn_name,
       rsp.group_level,
       rsp.item_code as parent_code,
       'ITEM' as parent_level,
       sum(rsp.receive_amt_cny) / nullif(item.item_amt, 0) as weight,
       rsp.item_code,
       rsp.item_name,
       rsp.category_code,
       rsp.category_name,
       rsp.l2_ceg_cn_name,
       rsp.l3_ceg_short_cn_name,
       rsp.l4_ceg_short_cn_name,
       rsp.l2_ceg_code,
       rsp.l3_ceg_code,
       rsp.l4_ceg_code,
       '' as top_type,
       CASE
         WHEN ITEM.TOP_FLAG = 'Y' THEN			/*202309版本加入非规格品下供应商权重*/
          'Y'
         ELSE
          'N'
       END AS top_flag,
       -1 as created_by,
       current_timestamp as creation_date,
       -1 as last_updated_by,
       current_timestamp as last_update_date,
       'N' as del_flag,
       rsp.l4_ceg_cn_name,
       rsp.l3_ceg_cn_name
  from (select rsp.group_code,
               rsp.group_cn_name,
               rsp.group_level,
               rsp.receive_amt_cny,
               rsp.item_code,
               rsp.item_name,
               rsp.category_code,
               rsp.category_name,
               rsp.l2_ceg_cn_name,
               rsp.l3_ceg_short_cn_name,
               rsp.l4_ceg_short_cn_name,
               rsp.l2_ceg_code,
               rsp.l3_ceg_code,
               rsp.l4_ceg_code,
               rsp.l4_ceg_cn_name,
               rsp.l3_ceg_cn_name
          from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t rsp
         where upper(rsp.group_level) = 'SUPPLIER'
           and version_id = v_version
           and rsp.append_flag = 'N') rsp
  left join ( --取每个规格品的四年总额作为供应商层级权重的分母
             select group_code AS ITEM_CODE,
                     sum(receive_amt_cny) as item_amt,
                     TOP_FLAG
               from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t
              where upper(group_level) = 'ITEM'
                and version_id = v_version
                and period_id < to_char(current_date, 'yyyymm') --小于当前月份的会计期，只取实际数
              group by group_code, TOP_FLAG) item
    on (item.ITEM_CODE = rsp.item_code) --对应父子级关系
 group by item.item_amt,
          item.TOP_FLAG,
          rsp.group_code,
          rsp.group_cn_name,
          rsp.group_level,
          rsp.item_code,
          rsp.item_name,
          rsp.category_code,
          rsp.category_name,
          rsp.l2_ceg_cn_name,
          rsp.l3_ceg_short_cn_name,
          rsp.l3_ceg_cn_name,
          rsp.l4_ceg_short_cn_name,
          rsp.l4_ceg_cn_name,
          rsp.l2_ceg_code,
          rsp.l3_ceg_code,
          rsp.l4_ceg_code;


 --3.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 3,
  F_CAL_LOG_DESC => '供应商权重插入完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

v_exception_flag = '4';
DROP TABLE IF EXISTS DM_FOI_GROUP_LEV_WEIGHT_TMP1;
CREATE TEMPORARY TABLE DM_FOI_GROUP_LEV_WEIGHT_TMP1 (
	PERIOD CHARACTER VARYING(50),
	PERIOD_TYPE CHARACTER VARYING(50),
	VERSION_ID BIGINT,
	GROUP_CODE CHARACTER VARYING(50),
	GROUP_CN_NAME CHARACTER VARYING(2000),
	GROUP_LEVEL CHARACTER VARYING(50),
	PARENT_LEVEL CHARACTER VARYING(50),
	WEIGHT NUMERIC,
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_NAME CHARACTER VARYING(2000),
	CATEGORY_CODE CHARACTER VARYING(50),
	CATEGORY_NAME CHARACTER VARYING(200),
	L4_CEG_CODE CHARACTER VARYING(200),
	L4_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
	L3_CEG_CODE CHARACTER VARYING(200),
	L3_CEG_SHORT_CN_NAME CHARACTER VARYING(200),
	L2_CEG_CODE CHARACTER VARYING(200),
	L2_CEG_CN_NAME CHARACTER VARYING(200),
	TOP_TYPE CHARACTER VARYING(50),
	TOP_FLAG CHARACTER VARYING(50),
	CREATION_DATE TIMESTAMP WITHOUT TIME ZONE,
	L4_CEG_CN_NAME CHARACTER VARYING(255),
	L3_CEG_CN_NAME CHARACTER VARYING(255),
	PARENT_CODE CHARACTER VARYING(255),
	CONTINUITY_TYPE CHARACTER VARYING(50)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY REPLICATION;

  --4,写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 4,
  F_CAL_LOG_DESC => '创建权重临时表成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
	
	v_exception_flag = '5';
	--RAISE NOTICE '取数金额表临时表创表';
DROP TABLE IF EXISTS DM_TOP_AMT_TMP;
CREATE TEMPORARY TABLE DM_TOP_AMT_TMP (
   GROUP_CODE CHARACTER VARYING(50),
   GROUP_CN_NAME CHARACTER VARYING(2000),
   ITEM_AMT NUMERIC, 
   CATEGORY_CODE CHARACTER VARYING(50),
   CATEGORY_NAME CHARACTER VARYING(2000),
   CATEGORY_AMT NUMERIC, 
   L4_CEG_CODE CHARACTER VARYING(50),
   L4_CEG_SHORT_CN_NAME CHARACTER VARYING(2000),
   L4_CEG_CN_NAME CHARACTER VARYING(2000),
   L4_AMT NUMERIC, 
   L3_CEG_CODE CHARACTER VARYING(50),
   L3_CEG_SHORT_CN_NAME CHARACTER VARYING(2000),
   L3_CEG_CN_NAME CHARACTER VARYING(2000),
   L3_AMT NUMERIC, 
   L2_CEG_CODE CHARACTER VARYING(50),
   L2_CEG_CN_NAME CHARACTER VARYING(2000),
   L2_AMT NUMERIC
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY REPLICATION;

  --5.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 5,
  F_CAL_LOG_DESC => '取数金额临时表成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

v_exception_flag = '6';
--RAISE NOTICE '取数金额表插数';
INSERT INTO DM_TOP_AMT_TMP
(  GROUP_CODE,
   GROUP_CN_NAME,
   ITEM_AMT,
   CATEGORY_CODE,
   CATEGORY_NAME,
   CATEGORY_AMT,
   L4_CEG_CODE,
   L4_CEG_SHORT_CN_NAME,
   L4_CEG_CN_NAME,
   L4_AMT,
   L3_CEG_CODE,
   L3_CEG_SHORT_CN_NAME,
   L3_CEG_CN_NAME,
   L3_AMT,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   L2_AMT
)
   select trsp.group_code,
          trsp.group_cn_name,
          trsp.item_amt, --规格品层级四年总额
          trsp.category_code,
          trsp.category_name,
          sum(trsp.item_amt) over(partition by trsp.category_code) as category_amt, --top品类层级四年总额
          trsp.l4_ceg_code,
          trsp.l4_ceg_short_cn_name,
          trsp.l4_ceg_cn_name,
          sum(trsp.item_amt) over(partition by trsp.l4_ceg_code) as l4_amt, --模块层级四年总额
          trsp.l3_ceg_code,
          trsp.l3_ceg_short_cn_name,
          trsp.l3_ceg_cn_name,
          sum(trsp.item_amt) over(partition by trsp.l3_ceg_code) as l3_amt, --专家团层级四年总额
          trsp.l2_ceg_code,
          trsp.l2_ceg_cn_name,
          sum(trsp.item_amt) over(partition by trsp.l2_ceg_code) as l2_amt --采购组织层级四年总额
     from (select t.group_cn_name,
                  t.group_code,
                  sum(receive_amt_cny) as item_amt,
                  t.category_code,
                  t.category_name,
                  t.l4_ceg_code,
                  t.l4_ceg_short_cn_name,
                  t.l4_ceg_cn_name,
                  t.l3_ceg_code,
                  t.l3_ceg_short_cn_name,
                  t.l3_ceg_cn_name,
                  t.l2_ceg_code,
                  t.l2_ceg_cn_name
             from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t t
            where upper(t.group_level) = 'ITEM'
              and t.top_flag = 'Y'
              and t.version_id = v_version
              and t.append_flag = 'N'
            group by t.group_cn_name,
                     t.group_code,
                     t.category_code,
                     t.category_name,
                     t.l4_ceg_code,
                     t.l4_ceg_short_cn_name,
                     t.l4_ceg_cn_name,
                     t.l3_ceg_code,
                     t.l3_ceg_short_cn_name,
                     t.l3_ceg_cn_name,
                     t.l2_ceg_code,
                     t.l2_ceg_cn_name
					 ) trsp;

  --6.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 6,
  F_CAL_LOG_DESC => '取数金额临时表插数成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

V_INSERT_SQL := '
INSERT INTO DM_FOI_GROUP_LEV_WEIGHT_TMP1
  (PERIOD,
   PERIOD_TYPE,
   VERSION_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PARENT_LEVEL,
   WEIGHT,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L2_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   L3_CEG_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   L4_CEG_CN_NAME,
   L2_CEG_CODE,
   L3_CEG_CODE,
   L4_CEG_CODE,
   TOP_TYPE,
   TOP_FLAG,
   CREATION_DATE,
   CONTINUITY_TYPE
	 )
SELECT DISTINCT '''||V_PERIOD||''' AS PERIOD,
			 ''S'' AS PERIOD_TYPE,
			 '||V_VERSION ||' AS VERSION_ID, 
			 TT.GROUP_CODE,
			 TT.GROUP_CN_NAME,
			 TT.GROUP_LEVEL,
			 $V_PARENT_CODE$,
			 $V_PARENT_LEVEL$,
			 $V_WEIGHT$, 
			 TT.CATEGORY_CODE,
			 TT.CATEGORY_NAME,
			 TT.L2_CEG_CN_NAME,
			 TT.L3_CEG_SHORT_CN_NAME,
			 TT.L3_CEG_CN_NAME,
			 TT.L4_CEG_SHORT_CN_NAME,
			 TT.L4_CEG_CN_NAME,
			 TT.L2_CEG_CODE,
			 TT.L3_CEG_CODE,
			 TT.L4_CEG_CODE,
			 '''' AS TOP_TYPE,
       ''Y'' AS TOP_FLAG,
       CURRENT_TIMESTAMP AS CREATION_DATE,
			 '''' AS CONTINUITY_TYPE
FROM DM_TOP_AMT_TMP TT';
	
V_INSERT_SQL_BACK := V_INSERT_SQL; --备份
--ITEM/CATEGORY
V_EXCEPTION_FLAG :='7';
--RAISE NOTICE 'ITEM/品类';
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_LEVEL','''ITEM''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_CODE$','TT.CATEGORY_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_LEVEL$','''CATEGORY''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_WEIGHT$','TT.ITEM_AMT / NULLIF(TT.CATEGORY_AMT,0)');
V_EXECUTE_SQL:= V_INSERT_SQL;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--7.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 7,
  F_CAL_LOG_DESC => 'ITEM/品类权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--CATEGORY/LV4
V_EXCEPTION_FLAG :='8';
V_INSERT_SQL := V_INSERT_SQL_BACK;--重置公共部分
--RAISE NOTICE '品类/LV4';
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.CATEGORY_CODE','''''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.CATEGORY_NAME','''''');
V_INSERT_SQL_BACK := V_INSERT_SQL; --更新备份
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CODE','TT.CATEGORY_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CN_NAME','TT.CATEGORY_NAME');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_LEVEL','''CATEGORY''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_CODE$','TT.L4_CEG_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_LEVEL$','''LV4''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_WEIGHT$','TT.CATEGORY_AMT / NULLIF(TT.L4_AMT,0)');
V_EXECUTE_SQL:= V_INSERT_SQL;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--8.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 8,
  F_CAL_LOG_DESC => '品类/LV4权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--CATEGORY/LV3
V_EXCEPTION_FLAG :='9';
--RAISE NOTICE '品类/LV3';
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'''LV4''','''LV3''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.CATEGORY_AMT / NULLIF(TT.L4_AMT,0)','TT.CATEGORY_AMT / NULLIF(TT.L3_AMT,0)');--替换CATEGORY/LV4的字段值
V_EXECUTE_SQL:= V_INSERT_SQL;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--9.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 9,
  F_CAL_LOG_DESC => '品类/LV3权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--CATEGORY/LV2
--RAISE NOTICE '品类/LV2';
V_EXCEPTION_FLAG :='10';
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'''LV3''','''LV2''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.CATEGORY_AMT / NULLIF(TT.L3_AMT,0)','TT.CATEGORY_AMT / NULLIF(TT.L2_AMT,0)');--替换CATEGORY/LV3的字段值
V_EXECUTE_SQL:= V_INSERT_SQL;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--12.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 10,
  F_CAL_LOG_DESC => '品类/LV2权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--LV4/LV3
V_EXCEPTION_FLAG :='11';
V_INSERT_SQL := V_INSERT_SQL_BACK;--重置公共部分
--RAISE NOTICE 'LV4/LV3';
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L4_CEG_SHORT_CN_NAME','''''');
V_INSERT_SQL_BACK := V_INSERT_SQL;--更新备份
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CODE','TT.L4_CEG_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CN_NAME','TT.L4_CEG_SHORT_CN_NAME');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_LEVEL','''LV4''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_CODE$','TT.L3_CEG_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_LEVEL$','''LV3''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_WEIGHT$','TT.L4_AMT / NULLIF(TT.L3_AMT,0)');
V_EXECUTE_SQL:= V_INSERT_SQL;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--11.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 11,
  F_CAL_LOG_DESC => 'LV4/LV3权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--LV4/LV2
V_EXCEPTION_FLAG :='12';
--RAISE NOTICE 'LV4/LV2';
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L4_CEG_CN_NAME','''''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'''LV3''','''LV2''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L4_AMT / NULLIF(TT.L3_AMT,0)','TT.L4_AMT / NULLIF(TT.L2_AMT,0)');
V_EXECUTE_SQL:= V_INSERT_SQL;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--12.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 12,
  F_CAL_LOG_DESC => 'LV4/LV2权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--LV3/LV2 是否含连续性影响打标
--RAISE NOTICE 'LV3/LV2';
V_LV3_PART := ' INNER JOIN (SELECT DISTINCT GROUP_CODE, CONTINUITY_TYPE
											       FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T
														WHERE VERSION_ID = '||V_VERSION||'
															AND TOP_FLAG = ''Y''
															AND CONTINUITY_TYPE IS NOT NULL
															AND GROUP_LEVEL = ''LV3''
														UNION ALL
													 SELECT DISTINCT GROUP_CODE, ''含连续性影响'' AS CONTINUITY_TYPE
														 FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T
														WHERE VERSION_ID = '||V_VERSION||'
															AND TOP_FLAG = ''Y''
															AND GROUP_LEVEL = ''LV3'') T2
									ON TT.L3_CEG_CODE = T2.GROUP_CODE
							 GROUP BY TT.L3_CEG_CODE,
											  TT.L3_CEG_SHORT_CN_NAME,
												TT.L3_CEG_CN_NAME,
												TT.L3_AMT,
												T2.CONTINUITY_TYPE,
												TT.L2_CEG_CODE,
												TT.L2_CEG_CN_NAME
												';--定义LV3是否受连续性影响部分
V_EXCEPTION_FLAG :='13';
V_INSERT_SQL := V_INSERT_SQL_BACK;--重置公共部分
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L4_CEG_CODE','''''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L4_CEG_CN_NAME','''''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L3_CEG_SHORT_CN_NAME','''''');
V_INSERT_SQL_BACK := V_INSERT_SQL;--更新备份
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CODE','TT.L3_CEG_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CN_NAME','TT.L3_CEG_SHORT_CN_NAME');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_LEVEL','''LV3''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_CODE$','TT.L2_CEG_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_LEVEL$','''LV2''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_WEIGHT$','TT.L3_AMT/SUM(TT.L3_AMT)OVER(PARTITION BY T2.CONTINUITY_TYPE)');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,''''' AS CONTINUITY_TYPE','T2.CONTINUITY_TYPE');
V_EXECUTE_SQL:= V_INSERT_SQL||V_LV3_PART;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--13.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 13,
  F_CAL_LOG_DESC => 'LV3/LV2权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--品类/品类分类
V_EXCEPTION_FLAG :='14';
--RAISE NOTICE '品类/品类分类';
V_CATE_TYPE_PART := ' INNER JOIN FIN_DM_OPT_FOI.DM_FOI_TOP_CATE_INFO_T T2
												 ON (TT.CATEGORY_CODE = T2.CATEGORY_CODE AND
													  	TT.CATEGORY_NAME = T2.CATEGORY_NAME)
										  WHERE T2.VERSION_ID = '||V_CATE_VERSION||'
											  AND T2.CATEGORY_TYPE IS NOT NULL
											GROUP BY TT.CATEGORY_CODE,
															 TT.CATEGORY_NAME,
															 T2.CATEGORY_TYPE,
															 TT.CATEGORY_AMT,
															 TT.L2_CEG_CN_NAME,
															 TT.L2_CEG_CODE';
V_INSERT_SQL := V_INSERT_SQL_BACK ;--重置公共部分
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L3_CEG_CODE','''''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.L3_CEG_CN_NAME','''''');
V_INSERT_SQL_BACK := V_INSERT_SQL;--更新备份
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CODE','TT.CATEGORY_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CN_NAME','TT.CATEGORY_NAME');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_LEVEL','''CATEGORY''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_CODE$','T2.CATEGORY_TYPE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_LEVEL$','''CATEGORY_TYPE''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_WEIGHT$','TT.CATEGORY_AMT/SUM(TT.CATEGORY_AMT)OVER(PARTITION BY T2.CATEGORY_TYPE)');
V_EXECUTE_SQL:= V_INSERT_SQL||V_CATE_TYPE_PART;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--14.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 14,
  F_CAL_LOG_DESC => '品类/品类分类权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

--品类/TOPN
V_EXCEPTION_FLAG :='15';
--RAISE NOTICE '品类/TOPN';
 V_TOP_TYPE_PART := ' INNER JOIN (SELECT GROUP_CODE, ''TOP50'' AS TOP_TYPE, VERSION_ID
										   FROM FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
								 		  WHERE VERSION_ID = '||v_version||'
											  AND TOP_FLAG IS NULL
											  AND TOP_TYPE = ''TOP50''
										  UNION ALL
										 SELECT GROUP_CODE, ''TOP100''AS TOP_TYPE, VERSION_ID
										   FROM FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
										  WHERE VERSION_ID = '||v_version||'
											  AND TOP_FLAG IS NULL
											  AND TOP_TYPE <> ''TOP300''
										  UNION ALL
									   SELECT GROUP_CODE, ''TOP300'' AS TOP_TYPE, VERSION_ID
										   FROM FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
										  WHERE VERSION_ID = '||v_version||'
											  AND TOP_FLAG IS NULL
										 	  AND TOP_TYPE IS NOT NULL) T2
												 ON (TT.CATEGORY_CODE = T2.GROUP_CODE)
										  GROUP BY TT.CATEGORY_CODE,
															 TT.CATEGORY_NAME,
															 TT.CATEGORY_AMT,
															 T2.TOP_TYPE,
															 TT.L2_CEG_CN_NAME,
															 TT.L2_CEG_CODE
														 ';
V_INSERT_SQL := V_INSERT_SQL_BACK ;--重置公共部分
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CODE','TT.CATEGORY_CODE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_CN_NAME','TT.CATEGORY_NAME');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'TT.GROUP_LEVEL','''CATEGORY''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_CODE$','T2.TOP_TYPE');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_PARENT_LEVEL$','''TOP_TYPE''');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,'$V_WEIGHT$','TT.CATEGORY_AMT/SUM(TT.CATEGORY_AMT) OVER(PARTITION BY T2.TOP_TYPE)');
V_INSERT_SQL := REPLACE(V_INSERT_SQL,''''' AS TOP_TYPE','T2.TOP_TYPE');
V_EXECUTE_SQL:= V_INSERT_SQL||V_TOP_TYPE_PART;
EXECUTE IMMEDIATE V_EXECUTE_SQL;

--15.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 15,
  F_CAL_LOG_DESC => '品类/TOPN权重插数成功',
	F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

V_EXCEPTION_FLAG = '5';
--RAISE NOTICE '权重表插数';
INSERT INTO FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
(ID,
PERIOD,
PERIOD_TYPE,
VERSION_ID,
GROUP_CODE,
GROUP_CN_NAME,
GROUP_LEVEL,
PARENT_LEVEL,
WEIGHT,
ITEM_CODE,
ITEM_NAME,
CATEGORY_CODE,
CATEGORY_NAME,
L4_CEG_CODE,
L4_CEG_SHORT_CN_NAME,
L3_CEG_CODE,
L3_CEG_SHORT_CN_NAME,
L2_CEG_CODE,
L2_CEG_CN_NAME,
TOP_TYPE,
TOP_FLAG,
CREATED_BY,
CREATION_DATE,
LAST_UPDATED_BY,
LAST_UPDATE_DATE,
DEL_FLAG,
L4_CEG_CN_NAME,
L3_CEG_CN_NAME,
PARENT_CODE,
CONTINUITY_TYPE
)
SELECT FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_S.NEXTVAL AS ID,
			 T1.PERIOD,
			 T1.PERIOD_TYPE,
			 v_version AS VERSION_ID,
			 T1.GROUP_CODE,
			 T1.GROUP_CN_NAME,
			 T1.GROUP_LEVEL,
			 T1.PARENT_LEVEL,
			 T1.WEIGHT,
			 T1.ITEM_CODE,
			 T1.ITEM_NAME,
			 T1.CATEGORY_CODE,
			 T1.CATEGORY_NAME,
			 T1.L4_CEG_CODE,
			 T1.L4_CEG_SHORT_CN_NAME,
			 T1.L3_CEG_CODE,
			 T1.L3_CEG_SHORT_CN_NAME,
			 T1.L2_CEG_CODE,
			 T1.L2_CEG_CN_NAME,
			 T1.TOP_TYPE,
			 T1.TOP_FLAG,
			 -1 AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 -1 AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 'N' AS DEL_FLAG,
			 T1.L4_CEG_CN_NAME,
			 T1.L3_CEG_CN_NAME,
			 T1.PARENT_CODE,
			 T1.CONTINUITY_TYPE
FROM DM_FOI_GROUP_LEV_WEIGHT_TMP1 T1;

--16.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 16,
  F_CAL_LOG_DESC => 'TOP类权重插入成功',
  F_FORMULA_SQL_TXT => V_EXECUTE_SQL,--本段执行逻辑SQL
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

v_exception_flag :='17';
----------全品类清单，单年全品类占ICT--------------
insert into FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t
  (id,
   period,
   period_type,
   version_id,
   group_code,
   group_cn_name,
   group_level,
   parent_code,
   parent_level,
   weight,
   l2_ceg_cn_name,
   l3_ceg_short_cn_name,
   l4_ceg_short_cn_name,
   l2_ceg_code,
   l3_ceg_code,
   l4_ceg_code,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag,
   l4_ceg_cn_name,
   l3_ceg_cn_name)
  select FIN_DM_OPT_FOI.dm_foi_group_lev_weight_s.nextval as id,
         rsp.year as period,
         'U' as period_type,
         v_version as version_id,
         rsp.group_code,
         rsp.group_cn_name,
         rsp.group_level,
         rsp.l4_ceg_code as parent_code,
         'LV2' as parent_level,
         sum(rsp.receive_amt_cny) / nullif(LV2.LV2_amt, 0) as weight,
         rsp.l2_ceg_cn_name,
         rsp.l3_ceg_short_cn_name,
         rsp.l4_ceg_short_cn_name,
         rsp.l2_ceg_code,
         rsp.l3_ceg_code,
         rsp.l4_ceg_code,
         -1 as created_by,
         current_timestamp as creation_date,
         -1 as last_updated_by,
         current_timestamp as last_update_date,
         'N' as del_flag,
         rsp.l4_ceg_cn_name,
         rsp.l3_ceg_cn_name
    from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t rsp
    left join (select t1.year, sum(t1.receive_amt_cny) as LV2_amt
                 from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t t1
                where t1.version_id = v_version
								  and t1.append_flag = 'N' 
                  and upper(t1.group_level) = 'LV2'
									AND T1.CONTINUITY_TYPE = '含连续性影响'
                group by t1.year) LV2
      on rsp.year = LV2.year
   where upper(rsp.group_level) = 'CATEGORY'
     and rsp.append_flag = 'N'
     and rsp.version_id = v_version
   group by rsp.year,
            rsp.group_code,
            rsp.group_cn_name,
            rsp.group_level,
            rsp.l4_ceg_code,
            rsp.l4_ceg_short_cn_name,
            rsp.l3_ceg_code,
            rsp.l3_ceg_short_cn_name,
            rsp.l2_ceg_code,
            rsp.l2_ceg_cn_name,
            LV2.LV2_amt,
            rsp.l4_ceg_cn_name,
            rsp.l3_ceg_cn_name;


 --17.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 17,
  F_CAL_LOG_DESC => '全品类清单权重插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
 
v_exception_flag :='18';
TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP;
 --7.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 18,
  F_CAL_LOG_DESC => 'ITEM单年金额临时表清空完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
	
v_exception_flag :='19'; 
INSERT INTO FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP
(year  ,
 group_code ,
 group_cn_name ,
 iten_amt ,
 category_code ,
 category_name ,
 l2_ceg_cn_name ,
 l3_ceg_short_cn_name ,
 l4_ceg_short_cn_name ,
 l2_ceg_code ,
 l3_ceg_code ,
 l4_ceg_code ,
 version_id ,
 l4_ceg_cn_name ,
 l3_ceg_cn_name 
)
 select t.year,
         t.group_code,
         t.group_cn_name,
         sum(t.receive_amt_cny) as iten_amt,
         t.category_code,
         t.category_name,
         t.l2_ceg_cn_name,
         t.l3_ceg_short_cn_name,
         t.l4_ceg_short_cn_name,
         t.l2_ceg_code,
         t.l3_ceg_code,
         t.l4_ceg_code,
         t.version_id,
         t.l4_ceg_cn_name,
         t.l3_ceg_cn_name
    from FIN_DM_OPT_FOI.dm_foi_lev_rec_amt_t t
   where upper(t.group_level) = 'ITEM'
     and t.version_id = v_version
     and t.append_flag = 'N'
     and t.parent_code in
               (select distinct tc.category_code
                  from FIN_DM_OPT_FOI.dm_foi_top_cate_info_t tc
                 where tc.version_id = v_cate_version)--top品类清单版本号
   group by t.year,
            t.group_code,
            t.group_cn_name,
            t.category_code,
            t.category_name,
            t.l2_ceg_cn_name,
            t.l3_ceg_short_cn_name,
            t.l4_ceg_short_cn_name,
            t.l2_ceg_code,
            t.l3_ceg_code,
            t.l4_ceg_code,
            t.version_id,
            t.l4_ceg_cn_name,
            t.l3_ceg_cn_name;
 
  --8.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 19,
  F_CAL_LOG_DESC => '规格品清单临时表插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

v_exception_flag :='20'; 
----RAISE notice '规格品清单权重插数';
insert into FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t
  (id,
   period,
   period_type,
   version_id,
   group_code,
   group_cn_name,
   group_level,
   parent_code,
   parent_level,
   weight,
   category_code,
   category_name,
   l2_ceg_cn_name,
   l3_ceg_short_cn_name,
   l4_ceg_short_cn_name,
   l2_ceg_code,
   l3_ceg_code,
   l4_ceg_code,
   created_by,
   creation_date,
   last_updated_by,
   last_update_date,
   del_flag,
   l4_ceg_cn_name,
   l3_ceg_cn_name)
  select FIN_DM_OPT_FOI.dm_foi_group_lev_weight_s.nextval as id,
         tt.period,
         tt.period_type,
         tt.version_id,
         tt.group_code,
         tt.group_cn_name,
         tt.group_level,
         tt.parent_code,
         tt.parent_level,
         tt.weight,
         tt.category_code,
         tt.category_name,
         tt.l2_ceg_cn_name,
         tt.l3_ceg_short_cn_name,
         tt.l4_ceg_short_cn_name,
         tt.l2_ceg_code,
         tt.l3_ceg_code,
         tt.l4_ceg_code,
         -1 as created_by,
         current_timestamp as creation_date,
         -1 as last_updated_by,
         current_timestamp as last_update_date,
         'N' as del_flag,
         tt.l4_ceg_cn_name,
         tt.l3_ceg_cn_name
    from (--单年权重
          select to_char(it.year) as period,
                  'U' as period_type,
                  v_version as version_id,
                  it.group_code,
                  it.group_cn_name,
                  'ITEM' as group_level,
                  it.category_code as parent_code,
                  'CATEGORY' as parent_level,
                  it.iten_amt / nullif(sum(it.iten_amt)over(partition by it.category_code, it.year),0) as weight,
                  it.category_code,
                  it.category_name,
                  it.l2_ceg_cn_name,
                  it.l3_ceg_short_cn_name,
                  it.l4_ceg_short_cn_name,
                  it.l2_ceg_code,
                  it.l3_ceg_code,
                  it.l4_ceg_code,
                  it.l4_ceg_cn_name,
                  it.l3_ceg_cn_name
            from FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP it
          union all --四年综合
          select distinct  V_PERIOD as period, --区间值为四年
                           'S' as period_type,
                           v_version as version_id,
                           it.group_code,
                           it.group_cn_name,
                           'ITEM' as group_level,
                           it.category_code as parent_code,
                           'CATEGORY' as parent_level,
                           sum(it.iten_amt)over(partition by it.group_code, it.category_code) / nullif(sum(it.iten_amt) over(partition by it.category_code), 0) as weight,
                           it.category_code,
                           it.category_name,
                           it.l2_ceg_cn_name,
                           it.l3_ceg_short_cn_name,
                           it.l4_ceg_short_cn_name,
                           it.l2_ceg_code,
                           it.l3_ceg_code,
                           it.l4_ceg_code,
                           it.l4_ceg_cn_name,
                           it.l3_ceg_cn_name
            from FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP it) tt;

--9.写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 20,
  F_CAL_LOG_DESC => '规格品清单权重插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
	
	v_exception_flag :='21'; 
	----RAISE notice '规格品清单临时表清空';
	TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_ITEM_AMT_TMP;
 --10.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 21,
  F_CAL_LOG_DESC => '临时表数据清空完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');

  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
  (ID,
   PERIOD,
   PERIOD_TYPE,
   VERSION_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PARENT_LEVEL,
   WEIGHT,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L2_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L3_CEG_CODE,
   L4_CEG_CODE,
   TOP_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   L4_CEG_CN_NAME,
   L3_CEG_CN_NAME)
SELECT FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_S.NEXTVAL AS ID,
       V_PERIOD AS PERIOD,
       'S' AS PERIOD_TYPE,
       V_VERSION AS VERSION_ID,
       RSP.GROUP_CODE,
       RSP.GROUP_CN_NAME,
       'SUPPLIER' AS GROUP_LEVEL,
       RSP.CATEGORY_CODE AS PARENT_CODE,
       'CATE_SUPPLIER' AS PARENT_LEVEL,
       SUM(RSP.RECEIVE_AMT_CNY) / SUM(SUM(RSP.RECEIVE_AMT_CNY)) OVER(PARTITION BY CATEGORY_CODE) AS WEIGHT,
       RSP.CATEGORY_CODE,
       RSP.CATEGORY_NAME,
       RSP.L2_CEG_CN_NAME,
       RSP.L3_CEG_SHORT_CN_NAME,
       RSP.L4_CEG_SHORT_CN_NAME,
       RSP.L2_CEG_CODE,
       RSP.L3_CEG_CODE,
       RSP.L4_CEG_CODE,
       'Y' AS TOP_FLAG,
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       'N' AS DEL_FLAG,
       RSP.L4_CEG_CN_NAME,
       RSP.L3_CEG_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T RSP
 WHERE UPPER(RSP.GROUP_LEVEL) = 'SUPPLIER'
   AND RSP.VERSION_ID = V_VERSION
   AND RSP.TOP_FLAG = 'Y'
   AND RSP.APPEND_FLAG = 'N'
 GROUP BY RSP.GROUP_CODE,
          RSP.GROUP_CN_NAME,
          RSP.CATEGORY_CODE,
          RSP.CATEGORY_NAME,
          RSP.L2_CEG_CN_NAME,
          RSP.L3_CEG_SHORT_CN_NAME,
          RSP.L3_CEG_CN_NAME,
          RSP.L4_CEG_SHORT_CN_NAME,
          RSP.L4_CEG_CN_NAME,
          RSP.L2_CEG_CODE,
          RSP.L3_CEG_CODE,
          RSP.L4_CEG_CODE;
		  
 --22.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 22,
  F_CAL_LOG_DESC => '供应商到品类权重插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
		  
		  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_T
  (ID,
   PERIOD,
   PERIOD_TYPE,
   VERSION_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PARENT_LEVEL,
   WEIGHT,
   CATEGORY_CODE,
   CATEGORY_NAME,
   L2_CEG_CN_NAME,
   L3_CEG_SHORT_CN_NAME,
   L4_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L3_CEG_CODE,
   L4_CEG_CODE,
   TOP_FLAG,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   L4_CEG_CN_NAME,
   L3_CEG_CN_NAME)
SELECT FIN_DM_OPT_FOI.DM_FOI_GROUP_LEV_WEIGHT_S.NEXTVAL AS ID,
       V_PERIOD AS PERIOD,
       'S' AS PERIOD_TYPE,
       V_VERSION AS VERSION_ID,
	   RSP.ITEM_CODE AS GROUP_CODE,
	   RSP.ITEM_NAME AS GROUP_CN_NAME,
       'ITEM' AS GROUP_LEVEL,
       RSP.GROUP_CODE AS PARENT_CODE,
       'SUPPLIER' AS PARENT_LEVEL,
       SUM(RSP.RECEIVE_AMT_CNY) / SUM(SUM(RSP.RECEIVE_AMT_CNY)) OVER(PARTITION BY RSP.GROUP_CODE,RSP.CATEGORY_CODE) AS WEIGHT,
       RSP.CATEGORY_CODE,
       RSP.CATEGORY_NAME,
       RSP.L2_CEG_CN_NAME,
       RSP.L3_CEG_SHORT_CN_NAME,
       RSP.L4_CEG_SHORT_CN_NAME,
       RSP.L2_CEG_CODE,
       RSP.L3_CEG_CODE,
       RSP.L4_CEG_CODE,
       'Y' AS TOP_FLAG,
       -1 AS CREATED_BY,
       CURRENT_TIMESTAMP AS CREATION_DATE,
       -1 AS LAST_UPDATED_BY,
       CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
       'N' AS DEL_FLAG,
       RSP.L4_CEG_CN_NAME,
       RSP.L3_CEG_CN_NAME
  FROM FIN_DM_OPT_FOI.DM_FOI_LEV_REC_AMT_T RSP
 WHERE UPPER(RSP.GROUP_LEVEL) = 'SUPPLIER'
   AND RSP.VERSION_ID = V_VERSION
   AND RSP.TOP_FLAG = 'Y'
   AND RSP.APPEND_FLAG = 'N'
 GROUP BY RSP.GROUP_CODE,
          RSP.GROUP_CN_NAME,
          RSP.CATEGORY_CODE,
          RSP.CATEGORY_NAME,
		  RSP.ITEM_CODE,
		  RSP.ITEM_NAME,
          RSP.L2_CEG_CN_NAME,
          RSP.L3_CEG_SHORT_CN_NAME,
          RSP.L3_CEG_CN_NAME,
          RSP.L4_CEG_SHORT_CN_NAME,
          RSP.L4_CEG_CN_NAME,
          RSP.L2_CEG_CODE,
          RSP.L3_CEG_CODE,
          RSP.L4_CEG_CODE;
  
 --22.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 22,
  F_CAL_LOG_DESC => 'ITEM到供应商权重插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
  
 --11.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => 22,
  F_CAL_LOG_DESC => 'FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t插数完成',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => x_success_flag,
  F_ERRBUF => 'SUCCESS');
   	
-- 收集信息
  ANALYSE FIN_DM_OPT_FOI.dm_foi_group_lev_weight_t;
  
return 'SUCCESS' ;

--处理异常信息
exception
  when others then
  x_success_flag := 0;
  
  perform FIN_DM_OPT_FOI.f_dm_foi_cal_log_t
  (f_sp_name => v_sp_name, 
   f_step_num => v_exception_flag,
   f_cal_log_desc => v_sp_name||'第'||v_exception_flag||'步运行失败', 
   f_result_status => x_success_flag, 
   f_errbuf => sqlstate||':'||sqlerrm
   );
	 
end; end; 
$$
/

