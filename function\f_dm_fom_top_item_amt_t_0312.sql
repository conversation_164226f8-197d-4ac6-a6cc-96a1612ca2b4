-- Name: f_dm_fom_top_item_amt_t_0312; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_top_item_amt_t_0312(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-06
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-规格品金额初始化 
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_KEYSTR)：绝密数据解密密钥串
		  参数三(F_VERSION_ID)：维度类型（U：通用/P：盈利颗粒度）
		  参数四(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 制造单领域金额表
		FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T 规格品清单
目标表：FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_AMT_T 规格品金额表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_AMT_T('E','',''); --EMS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_AMT_T_0312('M','密钥串',''); --自制一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME      VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_AMT_T_0312';
  V_VERSION      BIGINT; --版本号
  V_STEP_NUM     INT := 0; --函数步骤号
  V_KEYSTR       VARCHAR(100) := F_KEYSTR; --解密密钥串
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;	  

BEGIN 

X_SUCCESS_FLAG := '1';

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
DELETE FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_AMT_T_0312 WHERE CALIBER_FLAG = V_CALIBER_FLAG;

  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '删除'''||V_CALIBER_FLAG||''' 口径数据成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');

  V_STEP_NUM := V_STEP_NUM + 1;
--1.建临时表，承载制造单领域金额
DROP TABLE IF EXISTS DM_DECRYP_AMT_TEMP;
CREATE TEMPORARY TABLE DM_DECRYP_AMT_TEMP(
	PERIOD_YEAR CHARACTER VARYING(50),
	PERIOD_ID CHARACTER VARYING(50),
	LV0_CODE CHARACTER VARYING(50),
	LV0_CN_NAME CHARACTER VARYING(200),
	LV1_CODE CHARACTER VARYING(50),
	LV1_CN_NAME CHARACTER VARYING(200),
	BUSSINESS_OBJECT_CODE CHARACTER VARYING(50),
	BUSSINESS_OBJECT_CN_NAME CHARACTER VARYING(200),
	SHIPPING_OBJECT_CODE CHARACTER VARYING(50),
	SHIPPING_OBJECT_CN_NAME CHARACTER VARYING(50),
	MANUFACTURE_OBJECT_CODE CHARACTER VARYING(50),
	MANUFACTURE_OBJECT_CN_NAME CHARACTER VARYING(50),
	ITEM_CODE CHARACTER VARYING(50),
	ITEM_CN_NAME CHARACTER VARYING(1000),
	CALIBER_FLAG CHARACTER VARYING(2),
	RMB_COST_AMT  NUMERIC,
	TRANSACTION_QUANTITY NUMERIC
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  IF V_CALIBER_FLAG = 'E' THEN
  
	--2.1 根据业务口径入参为'E'，将EMS金额落表，无需解密
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AMT,
       TRANSACTION_QUANTITY)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
             RMB_EMS_AMT,
             TRANSACTION_QUANTITY
        FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T_0312  --20240312
       WHERE CALIBER_FLAG = V_CALIBER_FLAG
	     AND PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE)-2 AND YEAR(CURRENT_DATE);
	   
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => 'EMS金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
  
	--2.2 将EMS全量金额与规格品清单关联，得到规格品金额
	V_STEP_NUM := V_STEP_NUM + 1;	
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_AMT_T_0312  	--20240312
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_EMS_AMT,
       ACTUAL_QTY,
	   CALIBER_FLAG,
	   CREATED_BY,
	   CREATION_DATE,
	   LAST_UPDATED_BY,
	   LAST_UPDATE_DATE,
	   DEL_FLAG)
      SELECT V_VERSION AS VERSION_ID,
             T1.PERIOD_YEAR,
             T1.PERIOD_ID,
             T1.LV0_CODE,
             T1.LV0_CN_NAME,
             T1.LV1_CODE,
             T1.LV1_CN_NAME,
             T1.BUSSINESS_OBJECT_CODE,
             T1.BUSSINESS_OBJECT_CN_NAME,
             T1.SHIPPING_OBJECT_CODE,
             T1.SHIPPING_OBJECT_CN_NAME,
             T1.MANUFACTURE_OBJECT_CODE,
             T1.MANUFACTURE_OBJECT_CN_NAME,
             T1.ITEM_CODE,
             T1.ITEM_CN_NAME,
             T1.RMB_COST_AMT,
             T1.TRANSACTION_QUANTITY,
			 V_CALIBER_FLAG AS CALIBER_FLAG,
			 '-1' AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 '-1' AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 'N' AS DEL_FLAG
        FROM DM_DECRYP_AMT_TEMP T1
        JOIN FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T T2
          ON T1.ITEM_CODE = T2.TOP_ITEM_CODE
         AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
         AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
         AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
             NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
         AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
		     NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
		 AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
		     NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD')
       WHERE T2.IS_TOP_FLAG = 'Y'
         AND T1.CALIBER_FLAG = V_CALIBER_FLAG
         AND T2.CALIBER_FLAG = V_CALIBER_FLAG
         AND T2.VERSION_ID = V_VERSION;
		 
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => 'EMS规格品金额表落表成功',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
  
  ELSIF V_CALIBER_FLAG = 'M' THEN
  
	--2.1 根据业务口径入参为'M'，将自制金额落表，需解密
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AMT,
       TRANSACTION_QUANTITY)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
           --  TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,
           --                       V_KEYSTR,
           --                       'aes128',
           --                       'cbc',
           --                       'sha256')) 
		   RMB_MADE_AMT AS RMB_COST_AMT, --20240312
             TRANSACTION_QUANTITY
        FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T_0312 --20240312
       WHERE CALIBER_FLAG = V_CALIBER_FLAG
	     AND PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE)-2 AND YEAR(CURRENT_DATE);
	   
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '自制金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
  
	--2.2 将自制全量金额与规格品清单关联，得到规格品金额
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_AMT_T_0312
      (VERSION_ID,
       PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_MADE_AMT,
       ACTUAL_QTY,
	   CALIBER_FLAG,
	   CREATED_BY,
	   CREATION_DATE,
	   LAST_UPDATED_BY,
	   LAST_UPDATE_DATE,
	   DEL_FLAG)
      SELECT V_VERSION AS VERSION_ID,
             T1.PERIOD_YEAR,
             T1.PERIOD_ID,
             T1.LV0_CODE,
             T1.LV0_CN_NAME,
             T1.LV1_CODE,
             T1.LV1_CN_NAME,
             T1.BUSSINESS_OBJECT_CODE,
             T1.BUSSINESS_OBJECT_CN_NAME,
             T1.SHIPPING_OBJECT_CODE,
             T1.SHIPPING_OBJECT_CN_NAME,
             T1.MANUFACTURE_OBJECT_CODE,
             T1.MANUFACTURE_OBJECT_CN_NAME,
             T1.ITEM_CODE,
             T1.ITEM_CN_NAME,
          --   GS_ENCRYPT(T1.RMB_COST_AMT,
          --              V_KEYSTR,
          --              'AES128',
          --              'CBC',
          --              'SHA256'),
		  T1.RMB_COST_AMT, --20240312
             T1.TRANSACTION_QUANTITY,
			 V_CALIBER_FLAG AS CALIBER_FLAG,
			 '-1' AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 '-1' AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 'N' AS DEL_FLAG
        FROM DM_DECRYP_AMT_TEMP T1
        JOIN FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T T2
          ON T1.ITEM_CODE = T2.top_item_code
         AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
         AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
         AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
             NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
         AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
		     NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
		 AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
		     NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD')
       WHERE T2.IS_TOP_FLAG = 'Y'
         AND T1.caliber_flag = V_CALIBER_FLAG
         AND T2.caliber_flag = V_CALIBER_FLAG
         AND T2.VERSION_ID = V_VERSION;
		 
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '自制规格品金额表落表成功',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
  END IF;
		
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '规格品金额表函数执行完成',
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	
	
RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

END$$
/

