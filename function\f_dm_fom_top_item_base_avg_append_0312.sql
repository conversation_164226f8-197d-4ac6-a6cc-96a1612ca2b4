-- Name: f_dm_fom_top_item_base_avg_append_0312; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_top_item_base_avg_append_0312(f_caliber_flag character varying, f_keystr character varying DEFAULT NULL::character varying, f_item_version bigint DEFAULT NULL::bigint, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2023-12-07
创建人  ：黄心蕊 HWX1187045
背景描述：月度分析-规格品均本补齐初始化 
参数描述：参数一(F_CALIBER_FLAG)：'E'为EMS，'M'为自制
		  参数二(F_KEYSTR)：绝密数据解密密钥串
		  参数三(F_ITEM_VERSION): 执行版本号
		  参数四(X_SUCCESS_FLAG)：运行状态返回值 ‘1’为成功，‘0’为失败
来源表：FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T 制造单领域金额表
		FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T 规格品清单
目标表：FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_BASE_AVG_T 规格品均本表
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_BASE_AVG_APPEND('E','',''); --EMS一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_BASE_AVG_APPEND_0312('M','密钥串',''); --自制一个版本的数据
****************************************************************************************************************************************************************/

DECLARE
  V_SP_NAME    VARCHAR(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_TOP_ITEM_BASE_AVG_APPEND_0312';
  V_STEP_NUM   INT := 0; --步骤号
  V_BEGIN_DATE TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP) - 2) || '01','YYYYMM');
  V_VERSION    INT;
  V_KEYSTR       VARCHAR(100) := F_KEYSTR; --解密密钥串
  V_CALIBER_FLAG VARCHAR(2) := F_CALIBER_FLAG;	 
  
BEGIN

--日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 0,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  V_STEP_NUM := V_STEP_NUM + 1;
  --版本号入参判断，当入参为空，取规格品清单最新版本号
  IF F_ITEM_VERSION IS NULL THEN
    SELECT VERSION_ID INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
    --入参不为空，则以入参为版本号
  ELSE
    V_VERSION := F_ITEM_VERSION;
  END IF;
  
  --写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '取得本次执行版本号：'||V_VERSION,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS');
  
  --1.清空供应商实际数补齐表的数据
 DELETE FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T_0312 WHERE CALIBER_FLAG = V_CALIBER_FLAG; --20240312
  
  --1.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到本次执行版本号'||V_VERSION||'，删除'''||V_CALIBER_FLAG||'''口径数据成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
  V_STEP_NUM := V_STEP_NUM + 1;
--1.建临时表，承载制造单领域金额
DROP TABLE IF EXISTS DM_DECRYP_ITEM_AMT_TEMP;
CREATE TEMPORARY TABLE DM_DECRYP_ITEM_AMT_TEMP(
	PERIOD_YEAR VARCHAR(50),
	PERIOD_ID VARCHAR(50),
	LV0_CODE VARCHAR(50),
	LV0_CN_NAME VARCHAR(200),
	LV1_CODE VARCHAR(50),
	LV1_CN_NAME VARCHAR(200),
	BUSSINESS_OBJECT_CODE VARCHAR(50),
	BUSSINESS_OBJECT_CN_NAME VARCHAR(200),
	SHIPPING_OBJECT_CODE VARCHAR(50),
	SHIPPING_OBJECT_CN_NAME VARCHAR(50),
	MANUFACTURE_OBJECT_CODE VARCHAR(50),
	MANUFACTURE_OBJECT_CN_NAME VARCHAR(50),
	ITEM_CODE VARCHAR(50),
	ITEM_CN_NAME VARCHAR(1000),
	CALIBER_FLAG VARCHAR(2),
	RMB_COST_AMT  NUMERIC,
	ACTUAL_QTY NUMERIC
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;
  
--写入日志
 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
 (F_SP_NAME => V_SP_NAME,
  F_STEP_NUM => V_STEP_NUM,
  F_CAL_LOG_DESC => '临时表创建成功',
  F_DML_ROW_COUNT => SQL%ROWCOUNT,
  F_RESULT_STATUS => X_SUCCESS_FLAG,
  F_ERRBUF => 'SUCCESS'); 
  
  IF V_CALIBER_FLAG = 'E' THEN
  
	--2.1 根据业务口径入参为'E'，将EMS金额落表，无需解密
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_ITEM_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AMT,
       ACTUAL_QTY)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
             RMB_EMS_AMT,
             TRANSACTION_QUANTITY
        FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T_0312
       WHERE CALIBER_FLAG = V_CALIBER_FLAG
	     AND PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE)-2 AND YEAR(CURRENT_DATE);
	   
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => 'EMS金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
  ELSIF V_CALIBER_FLAG = 'M' THEN
  
	--2.1 根据业务口径入参为'M'，将自制金额落表，需解密
	V_STEP_NUM := V_STEP_NUM + 1;
    INSERT INTO DM_DECRYP_ITEM_AMT_TEMP
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       CALIBER_FLAG,
       RMB_COST_AMT,
       ACTUAL_QTY)
      SELECT PERIOD_YEAR,
             PERIOD_ID,
             LV0_CODE,
             LV0_CN_NAME,
             LV1_CODE,
             LV1_CN_NAME,
             BUSSINESS_OBJECT_CODE,
             BUSSINESS_OBJECT_CN_NAME,
             SHIPPING_OBJECT_CODE,
             SHIPPING_OBJECT_CN_NAME,
             MANUFACTURE_OBJECT_CODE,
             MANUFACTURE_OBJECT_CN_NAME,
             ITEM_CODE,
             ITEM_CN_NAME,
             CALIBER_FLAG,
            -- TO_NUMBER(GS_DECRYPT(RMB_MADE_AMT,
            --                      V_KEYSTR,
            --                      'aes128',
            --                      'cbc',
            --                      'sha256')) AS RMB_COST_AMT,
			RMB_MADE_AMT AS RMB_COST_AMT, --20240312
             TRANSACTION_QUANTITY
        FROM FIN_DM_OPT_FOI.DM_FOM_ITEM_EXPS_DTL_T_0312
       WHERE CALIBER_FLAG = V_CALIBER_FLAG
	     AND PERIOD_YEAR BETWEEN YEAR(CURRENT_DATE)-2 AND YEAR(CURRENT_DATE);
	   
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '自制金额落表',
	  F_DML_ROW_COUNT => SQL%ROWCOUNT,
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 
	  
  END IF ;

  V_STEP_NUM := V_STEP_NUM +1;
  DROP TABLE IF EXISTS DM_ITEM_APPEND_TEMP;
  CREATE TEMPORARY TABLE DM_ITEM_APPEND_TEMP(
	PERIOD_YEAR VARCHAR(50),
	PERIOD_ID VARCHAR(50),
	LV0_CODE VARCHAR(50),
	LV0_CN_NAME VARCHAR(200),
	LV1_CODE VARCHAR(50),
	LV1_CN_NAME VARCHAR(200),
	BUSSINESS_OBJECT_CODE VARCHAR(50),
	BUSSINESS_OBJECT_CN_NAME VARCHAR(200),
	SHIPPING_OBJECT_CODE VARCHAR(50),
	SHIPPING_OBJECT_CN_NAME VARCHAR(50),
	MANUFACTURE_OBJECT_CODE VARCHAR(50),
	MANUFACTURE_OBJECT_CN_NAME VARCHAR(50),
	ITEM_CODE VARCHAR(50),
	ITEM_CN_NAME	VARCHAR(2000),
	COST_AVG NUMERIC,
	APPEND_FLAG VARCHAR(2),
	CREATION_DATE TIMESTAMP
	)
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY REPLICATION;
  
	--写入日志
	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	 (F_SP_NAME => V_SP_NAME,
	  F_STEP_NUM => V_STEP_NUM,
	  F_CAL_LOG_DESC => '数据补齐临时表建表完成',
	  F_RESULT_STATUS => X_SUCCESS_FLAG,
	  F_ERRBUF => 'SUCCESS'); 

 V_STEP_NUM:=V_STEP_NUM+1;
 --2.插入新补齐的均价实际数
  WITH DIM_ITEM_TEMP AS
   (
    --取实际数所有物理维，包含LV0,LV1,经营对象,发货对象,制造对象,ITEM
    SELECT DISTINCT LV0_CODE,
                     LV0_CN_NAME,
                     LV1_CODE,
                     LV1_CN_NAME,
                     BUSSINESS_OBJECT_CODE,
                     BUSSINESS_OBJECT_CN_NAME,
                     SHIPPING_OBJECT_CODE,
                     SHIPPING_OBJECT_CN_NAME,
                     MANUFACTURE_OBJECT_CODE,
                     MANUFACTURE_OBJECT_CN_NAME,
                     ITEM_CODE,
                     ITEM_CN_NAME
      FROM DM_DECRYP_ITEM_AMT_TEMP),
  
  PERIOD_DIM_TEMP AS
   (
    --生成连续月份, 一年前首月至当前系统实际月, (当前系统实际月 = 当前系统月-1)
    SELECT TO_CHAR(ADD_MONTHS(V_BEGIN_DATE, NUM.VAL - 1), 'YYYYMM') AS PERIOD_ID
      FROM GENERATE_SERIES(1,
                            TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                    V_BEGIN_DATE,
                                                    CURRENT_TIMESTAMP)),
                            1) NUM(VAL)),
  
  CROSS_JOIN_TEMP AS
   (
    --生成连续年月的LV0,LV1,经营对象,发货对象,制造对象,ITEM维
    SELECT A.LV0_CODE,
            A.LV0_CN_NAME,
            A.LV1_CODE,
            A.LV1_CN_NAME,
            A.BUSSINESS_OBJECT_CODE,
            A.BUSSINESS_OBJECT_CN_NAME,
            A.SHIPPING_OBJECT_CODE,
            A.SHIPPING_OBJECT_CN_NAME,
            A.MANUFACTURE_OBJECT_CODE,
            A.MANUFACTURE_OBJECT_CN_NAME,
            A.ITEM_CODE,
            A.ITEM_CN_NAME,
			SUBSTR(B.PERIOD_ID, 1, 4) AS YEAR,
            B.PERIOD_ID
      FROM DIM_ITEM_TEMP A, PERIOD_DIM_TEMP B),
  
  ITEM_AVG_TEMP AS
   (
    --按LV0,LV1,经营对象,发货对象,制造对象,ITEM, 会计期, 计算均价=成本额/实际量
    SELECT PERIOD_YEAR,
            PERIOD_ID,
            LV0_CODE,
            LV0_CN_NAME,
            LV1_CODE,
            LV1_CN_NAME,
            BUSSINESS_OBJECT_CODE,
            BUSSINESS_OBJECT_CN_NAME,
            SHIPPING_OBJECT_CODE,
            SHIPPING_OBJECT_CN_NAME,
            MANUFACTURE_OBJECT_CODE,
            MANUFACTURE_OBJECT_CN_NAME,
            ITEM_CODE,
            ITEM_CN_NAME,
			SUM(RMB_COST_AMT) / NULLIF(SUM(ACTUAL_QTY),0) AS AVG_AMT,
            'N' AS APD_FLAG
      FROM DM_DECRYP_ITEM_AMT_TEMP
     GROUP BY PERIOD_YEAR,
               PERIOD_ID,
               LV0_CODE,
               LV0_CN_NAME,
               LV1_CODE,
               LV1_CN_NAME,
               BUSSINESS_OBJECT_CODE,
               BUSSINESS_OBJECT_CN_NAME,
               SHIPPING_OBJECT_CODE,
               SHIPPING_OBJECT_CN_NAME,
               MANUFACTURE_OBJECT_CODE,
               MANUFACTURE_OBJECT_CN_NAME,
               ITEM_CODE,
               ITEM_CN_NAME),
  
  FORWARD_FILLER_TEMP AS
   (
    --按照所有维度，向前寻找会计期补齐均价
    SELECT 	SS.LV0_CODE,
			SS.LV0_CN_NAME,
            SS.LV1_CODE,
            SS.LV1_CN_NAME,
            SS.BUSSINESS_OBJECT_CODE,
            SS.BUSSINESS_OBJECT_CN_NAME,
            SS.SHIPPING_OBJECT_CODE,
            SS.SHIPPING_OBJECT_CN_NAME,
            SS.MANUFACTURE_OBJECT_CODE,
            SS.MANUFACTURE_OBJECT_CN_NAME,
            SS.ITEM_CODE,
            SS.ITEM_CN_NAME,
            SS.YEAR,
            SS.PERIOD_ID,
            SS.AVG_AMT,
            FIRST_VALUE(SS.AVG_AMT) OVER(PARTITION BY NVL(SS.LV0_CODE,'LV0'), NVL(SS.LV1_CODE,'LV1'), NVL(SS.BUSSINESS_OBJECT_CODE,'BOD'), NVL(SS.SHIPPING_OBJECT_CODE,'SOD'),NVL(SS.MANUFACTURE_OBJECT_CODE,'MOD'),SS.ITEM_CODE, SS.AVG_AMT_FLAG ORDER BY SS.PERIOD_ID) AS AVG_AMT_2, --新补齐的均价字段
            SS.AVG_AMT_FLAG,
            SS.APD_FLAG
      FROM (SELECT S.LV0_CODE,
                    S.LV0_CN_NAME,
                    S.LV1_CODE,
                    S.LV1_CN_NAME,
                    S.BUSSINESS_OBJECT_CODE,
                    S.BUSSINESS_OBJECT_CN_NAME,
                    S.SHIPPING_OBJECT_CODE,
                    S.SHIPPING_OBJECT_CN_NAME,
                    S.MANUFACTURE_OBJECT_CODE,
                    S.MANUFACTURE_OBJECT_CN_NAME,
                    S.ITEM_CODE,
                    S.ITEM_CN_NAME,
                    S.YEAR,
                    S.PERIOD_ID,
                    S.AVG_AMT,
                    SUM(S.NULL_FLAG) OVER(PARTITION BY NVL(S.LV0_CODE,'LV0'), NVL(S.LV1_CODE,'LV1'), NVL(S.BUSSINESS_OBJECT_CODE,'BOD'), NVL(S.SHIPPING_OBJECT_CODE,'SOD'),NVL(S.MANUFACTURE_OBJECT_CODE,'MOD'), S.ITEM_CODE ORDER BY S.PERIOD_ID) AS AVG_AMT_FLAG, --均价标识: 为空不参与累计加1
                    S.APD_FLAG
               FROM (SELECT T1.LV0_CODE,
                            T1.LV0_CN_NAME,
                            T1.LV1_CODE,
                            T1.LV1_CN_NAME,
                            T1.BUSSINESS_OBJECT_CODE,
                            T1.BUSSINESS_OBJECT_CN_NAME,
                            T1.SHIPPING_OBJECT_CODE,
                            T1.SHIPPING_OBJECT_CN_NAME,
                            T1.MANUFACTURE_OBJECT_CODE,
                            T1.MANUFACTURE_OBJECT_CN_NAME,
                            T1.ITEM_CODE,
                            T1.ITEM_CN_NAME,
							T1.YEAR,
                            T1.PERIOD_ID,
                            T2.AVG_AMT,
                            DECODE(T2.AVG_AMT, NULL, 0, 1) AS NULL_FLAG, --空标识, 用于sum开窗累计
                            NVL(T2.APD_FLAG, 'Y') AS APD_FLAG --补齐标识：Y为补齐，N为原始存在
                       FROM CROSS_JOIN_TEMP T1
                       LEFT JOIN ITEM_AVG_TEMP T2
                         ON T1.ITEM_CODE = T2.ITEM_CODE
                        AND T1.PERIOD_ID = T2.PERIOD_ID
                        AND NVL(T1.LV0_CODE,'LV0') = NVL(T2.LV0_CODE,'LV0')
                        AND NVL(T1.LV1_CODE,'LV1') = NVL(T2.LV1_CODE,'LV1')
                        AND NVL(T1.BUSSINESS_OBJECT_CODE,'BOD') = NVL(T2.BUSSINESS_OBJECT_CODE,'BOD') 
                        AND NVL(T1.SHIPPING_OBJECT_CODE,'SOD') = NVL(T2.SHIPPING_OBJECT_CODE,'SOD')
                        AND NVL(T1.MANUFACTURE_OBJECT_CODE,'MOD') =
                            NVL(T2.MANUFACTURE_OBJECT_CODE,'MOD') ) S) SS)
  
  INSERT INTO DM_ITEM_APPEND_TEMP
    (PERIOD_YEAR,
     PERIOD_ID,
     LV0_CODE,
     LV0_CN_NAME,
     LV1_CODE,
     LV1_CN_NAME,
     BUSSINESS_OBJECT_CODE,
     BUSSINESS_OBJECT_CN_NAME,
     SHIPPING_OBJECT_CODE,
     SHIPPING_OBJECT_CN_NAME,
     MANUFACTURE_OBJECT_CODE,
     MANUFACTURE_OBJECT_CN_NAME,
     ITEM_CODE,
     ITEM_CN_NAME,
     COST_AVG,
     CREATION_DATE,
     APPEND_FLAG)
  --向后补齐均价
    SELECT S.YEAR,
           S.PERIOD_ID,
           S.LV0_CODE,
           S.LV0_CN_NAME,
           S.LV1_CODE,
           S.LV1_CN_NAME,
           S.BUSSINESS_OBJECT_CODE,
           S.BUSSINESS_OBJECT_CN_NAME,
           S.SHIPPING_OBJECT_CODE,
           S.SHIPPING_OBJECT_CN_NAME,
           S.MANUFACTURE_OBJECT_CODE,
           S.MANUFACTURE_OBJECT_CN_NAME,
           S.ITEM_CODE,
           S.ITEM_CN_NAME,
           NVL(S.AVG_AMT_2, S.AVG_AMT_3) AS COST_AVG,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           S.APD_FLAG AS APPEND_FLAG
      FROM (SELECT T1.LV0_CODE,
                   T1.LV0_CN_NAME,
                   T1.LV1_CODE,
                   T1.LV1_CN_NAME,
                   T1.BUSSINESS_OBJECT_CODE,
                   T1.BUSSINESS_OBJECT_CN_NAME,
                   T1.SHIPPING_OBJECT_CODE,
                   T1.SHIPPING_OBJECT_CN_NAME,
                   T1.MANUFACTURE_OBJECT_CODE,
                   T1.MANUFACTURE_OBJECT_CN_NAME,
                   T1.ITEM_CODE,
                   T1.ITEM_CN_NAME,
                   T1.YEAR,
                   T1.PERIOD_ID,
                   T1.AVG_AMT_2,
                   T2.AVG_AMT_3,
                   T1.APD_FLAG
              FROM FORWARD_FILLER_TEMP T1
              LEFT JOIN (SELECT DISTINCT S.LV0_CODE,
                                        S.LV1_CODE,
                                        S.BUSSINESS_OBJECT_CODE,
                                        S.SHIPPING_OBJECT_CODE,
                                        S.MANUFACTURE_OBJECT_CODE,
                                        S.ITEM_CODE,
                                        FIRST_VALUE(S.PERIOD_ID) OVER(PARTITION BY NVL(S.LV0_CODE, 'LV0'), NVL(S.LV1_CODE, 'LV1'), NVL(S.BUSSINESS_OBJECT_CODE, 'BOD'), NVL(S.SHIPPING_OBJECT_CODE, 'SOD'), NVL(S.MANUFACTURE_OBJECT_CODE, 'MOD'), S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS PERIOD_ID, --有均价的首条会计期
                                        FIRST_VALUE(S.AVG_AMT_2) OVER(PARTITION BY NVL(S.LV0_CODE, 'LV0'), NVL(S.LV1_CODE, 'LV1'), NVL(S.BUSSINESS_OBJECT_CODE, 'BOD'), NVL(S.SHIPPING_OBJECT_CODE, 'SOD'), NVL(S.MANUFACTURE_OBJECT_CODE, 'MOD'), S.ITEM_CODE ORDER BY S.PERIOD_ID ASC) AS AVG_AMT_3 --有均价的首条补齐均价
                          FROM FORWARD_FILLER_TEMP S
                         WHERE S.AVG_AMT_FLAG > 0) T2
                ON T1.ITEM_CODE = T2.ITEM_CODE
               AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
               AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
               AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
                   NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
               AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
                   NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
               AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
                   NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD')
               AND T1.PERIOD_ID < T2.PERIOD_ID) S;

  --2.写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入新补齐的到临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');		
   
V_STEP_NUM:=V_STEP_NUM+1;

  IF V_CALIBER_FLAG = 'M' THEN
    --补齐后均本关联规格品清单，根据TOP_FLAG区分规格品与非规格品数据，EMS数据不加密
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T_0312 --20240312
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_MADE_AVG,
	   CALIBER_FLAG,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       APPEND_FLAG,
       TOP_FLAG)
      SELECT T1.PERIOD_YEAR,
             T1.PERIOD_ID,
             T1.LV0_CODE,
             T1.LV0_CN_NAME,
             T1.LV1_CODE,
             T1.LV1_CN_NAME,
             T1.BUSSINESS_OBJECT_CODE,
             T1.BUSSINESS_OBJECT_CN_NAME,
             T1.SHIPPING_OBJECT_CODE,
             T1.SHIPPING_OBJECT_CN_NAME,
             T1.MANUFACTURE_OBJECT_CODE,
             T1.MANUFACTURE_OBJECT_CN_NAME,
             T1.ITEM_CODE,
             T1.ITEM_CN_NAME,
             --GS_ENCRYPT(T1.COST_AVG, V_KEYSTR, 'AES128', 'CBC', 'SHA256') AS RMB_MADE_AVG,
			 T1.COST_AVG AS RMB_MADE_AVG, --20240312
			 V_CALIBER_FLAG AS CALIBER_FLAG,
             '-1' AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             '-1' AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             'N' AS DEL_FLAG,
             T1.APPEND_FLAG,
             CASE
               WHEN T2.VERSION_ID IS NULL THEN 'N'
               ELSE 'Y'
             END AS TOP_FLAG
        FROM DM_ITEM_APPEND_TEMP T1
        LEFT JOIN (SELECT DISTINCT LV0_CODE,
                                   LV1_CODE,
                                   BUSSINESS_OBJECT_CODE,
                                   SHIPPING_OBJECT_CODE,
                                   MANUFACTURE_OBJECT_CODE,
                                   TOP_ITEM_CODE,
								   VERSION_ID
                     FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
                    WHERE VERSION_ID = V_VERSION
                      AND IS_TOP_FLAG = 'Y'
					  AND CALIBER_FLAG = V_CALIBER_FLAG ) T2
          ON T1.ITEM_CODE = T2.TOP_ITEM_CODE
         AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
         AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
         AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
             NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
         AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
		     NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
		 AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
		     NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD');
		 
	 
  --写入日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T中EMS数据插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
	 
  ELSIF V_CALIBER_FLAG = 'E' THEN
    --补齐后均本关联规格品清单，根据TOP_FLAG区分规格品与非规格品数据，MADE数据需加密
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T_0312
      (PERIOD_YEAR,
       PERIOD_ID,
       LV0_CODE,
       LV0_CN_NAME,
       LV1_CODE,
       LV1_CN_NAME,
       BUSSINESS_OBJECT_CODE,
       BUSSINESS_OBJECT_CN_NAME,
       SHIPPING_OBJECT_CODE,
       SHIPPING_OBJECT_CN_NAME,
       MANUFACTURE_OBJECT_CODE,
       MANUFACTURE_OBJECT_CN_NAME,
       ITEM_CODE,
       ITEM_CN_NAME,
       RMB_EMS_AVG,
	   CALIBER_FLAG,
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       APPEND_FLAG,
       TOP_FLAG)
      SELECT T1.PERIOD_YEAR,
             T1.PERIOD_ID,
             T1.LV0_CODE,
             T1.LV0_CN_NAME,
             T1.LV1_CODE,
             T1.LV1_CN_NAME,
             T1.BUSSINESS_OBJECT_CODE,
             T1.BUSSINESS_OBJECT_CN_NAME,
             T1.SHIPPING_OBJECT_CODE,
             T1.SHIPPING_OBJECT_CN_NAME,
             T1.MANUFACTURE_OBJECT_CODE,
             T1.MANUFACTURE_OBJECT_CN_NAME,
             T1.ITEM_CODE,
             T1.ITEM_CN_NAME,
             T1.COST_AVG,
			 V_CALIBER_FLAG AS CALIBER_FLAG,
             '-1' AS CREATED_BY,
             CURRENT_TIMESTAMP AS CREATION_DATE,
             '-1' AS LAST_UPDATED_BY,
             CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
             'N' AS DEL_FLAG,
             T1.APPEND_FLAG,
             CASE
               WHEN T2.VERSION_ID IS NULL THEN 'N'
               ELSE 'Y'
             END AS TOP_FLAG
        FROM DM_ITEM_APPEND_TEMP T1
        LEFT JOIN (SELECT DISTINCT LV0_CODE,
                                   LV1_CODE,
                                   BUSSINESS_OBJECT_CODE,
                                   SHIPPING_OBJECT_CODE,
                                   MANUFACTURE_OBJECT_CODE,
                                   TOP_ITEM_CODE,
								   VERSION_ID
                     FROM FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_INFO_T
                    WHERE VERSION_ID = V_VERSION
                      AND IS_TOP_FLAG = 'Y'
											AND CALIBER_FLAG = V_CALIBER_FLAG) T2
          ON T1.ITEM_CODE = T2.TOP_ITEM_CODE
         AND NVL(T1.LV0_CODE, 'LV0') = NVL(T2.LV0_CODE, 'LV0')
         AND NVL(T1.LV1_CODE, 'LV1') = NVL(T2.LV1_CODE, 'LV1')
         AND NVL(T1.BUSSINESS_OBJECT_CODE, 'BOD') =
             NVL(T2.BUSSINESS_OBJECT_CODE, 'BOD')
         AND NVL(T1.SHIPPING_OBJECT_CODE, 'SOD') =
		     NVL(T2.SHIPPING_OBJECT_CODE, 'SOD')
		 AND NVL(T1.MANUFACTURE_OBJECT_CODE, 'MOD') =
		     NVL(T2.MANUFACTURE_OBJECT_CODE, 'MOD');
	 
  --写入日志
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'FIN_DM_OPT_FOI.DM_FOM_TOP_ITEM_BASE_AVG_T自制数据插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
	 
END IF ;

  
 RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := 0;
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_SUCCESS_FLAG, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

