-- Name: f_dm_fol_air_annual_price_index_trend_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_air_annual_price_index_trend_info_t(p_version_id integer DEFAULT NULL::integer, p_refresh_type character varying DEFAULT NULL::character varying, p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建人  ：zwx1275798
创建时间：2024年8月27日
背景描述：物流空运价格指数年度趋势表，仅计算精品空运                                           
参数描述： p_version_id   逻辑：1、自动调度，取航线量汇总表的最大版本ID；2、刷新（页面的刷新价格表）：取java传版本ID；
           p_version_code 逻辑：1、自动调度，取价格补录头表的最大版本code；2、刷新（页面的刷新价格表）：取java传版本code；
           p_refresh_type 逻辑：1、自动调度，直接赋值为 4_AUTO；2、刷新（页面的刷新价格表）：取java传的值（1_刷新价格表 ）
		   x_success_flag:返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_air_annual_price_index_trend_info_t()
变更记录：202503 zwx1275798 代码优化(代码由1971行缩减至1351行)：
                                         1、将所有with as 临时表修改为temporary会话临时表
                                         2、temporary会话临时表的表结构创建语句删除，将insert temporary语句修改为创建temporary表的逻辑语句
										 3、将最小粒度的成本和是否精品：ALL（精品+货代）的逻辑放在最开始从汇总表取数的地方，删除后面每个指标单独计算ALL的代码。
										 3、将各层级成本、货量、收益，需多次计算的逻辑进行整合，精简逻辑，去除冗余代码

*/

declare
	v_sp_name varchar(200) := 'fin_dm_opt_foi.f_dm_fol_air_annual_price_index_trend_info_t('||p_version_id||','''||p_refresh_type||''')';
	v_tbl_name varchar(200) := 'fin_dm_opt_foi.dm_fol_air_annual_price_index_trend_info_t';
	v_dml_row_count  int default 0 ;
	v_max_version_id int;  -- 最大版本ID
	v_price_version_code varchar(30);  -- 价格补录表的版本code
	v_current_year   int;  -- 当前年

begin

  set enable_force_vector_engine to on;
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '物流空运价格指数年度趋势表 '||v_tbl_name||'：开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;   			

       --从 物流空运价格指数年度趋势表 中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_air_annual_price_index_trend_info_t t1 
		where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_air_version_info_t t2 where  t2.step='2001' and t2.transport_mode = '精品空运');
		
       -- 如果是传version_id调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then 
        select  p_version_id into v_max_version_id ;
        else 
        select max(version_id) as max_version_id into v_max_version_id       
		from fin_dm_opt_foi.dm_fol_air_route_info_sum_t;	
        end if 
        ;			

	-- 如果p_version_code为空，则取 物流空运价格补录表头表中 的最大版本编码，如果p_version_code不为空，则取传入的p_version_code   
		if (p_version_code is null or p_version_code = '') then
        select max(version_code) as max_version_code into v_price_version_code 
		from fin_dm_opt_foi.apd_fol_air_route_price_heaer_t 
		where upper(status)='FINAL';
		else 
		select  p_version_code into v_price_version_code ;
		end if
          ; 

    -- 清理已经写入版本信息表的数据
    delete from fin_dm_opt_foi.dm_fol_air_version_info_t
     where version_id = v_max_version_id
       and source_en_name = 'f_dm_fol_air_annual_price_index_trend_info_t'
       and refresh_type = nvl(p_refresh_type,'4_AUTO')
       and step = 2
       and upper(del_flag) = 'N'
    ;

    -- 将执行步骤：2  执行中 插入版本信息表中的
    insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
         version_id           -- 版本ID（自动生成）
       , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
       , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
       , source_en_name       -- 来源英文描述（可以是表名、函数名等）
       , source_cn_name       -- 来源中文描述
       , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
	   , transport_mode       -- 运输方式（精品空运、精品海运）
       , remark               -- 备注
       , created_by           -- 创建人
       , creation_date        -- 创建时间
       , last_updated_by      -- 修改人
       , last_update_date     -- 修改时间
       , del_flag             -- 是否删除
  )

	select v_max_version_id   as version_id
       , v_price_version_code as version_code
       , 2 as step
       , 'f_dm_fol_air_annual_price_index_trend_info_t' as source_en_name
       , '物流空运价格指数年度趋势函数'           as source_cn_name
       , nvl(p_refresh_type,'4_AUTO') as refresh_type
	   , '精品空运' as transport_mode
       , 'version_code 是物流航线价格补录表的版本编码' as remark
	     , -1 as created_by
	     , current_timestamp as creation_date
	     , -1 as last_updated_by
	     , current_timestamp as last_update_date
	     , 'N' as del_flag
  ;

  -- 取航线量价汇总表中最大年份
  select (substr(max(period_id),1,4))::int as max_year into v_current_year
    from fin_dm_opt_foi.dm_fol_air_route_info_sum_t
   where version_id = v_max_version_id
     and transport_mode = '精品空运'
     and upper(del_flag) = 'N'
  ;
  
  -- 获取2020年至当前年的年份
     drop table if exists all_year_tmp;
     create temporary table all_year_tmp
	  as
    select to_char(generate_series,'YYYY')  as year		        
  from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 year')
  where to_char(generate_series,'YYYY') <= v_current_year
;
 
               --从 航线量表 取出 version_id 为最大版本
	    drop table if exists air_annual_trend_route_tmp;
        create temporary table air_annual_trend_route_tmp
	      as
		select  version_id
		       ,year
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,currency
			   ,sum(price) as price
			   ,sum(price*container_qty) as amount
			   ,sum(container_qty) as container_qty      
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality 
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and transport_mode = '精品空运'
		  and version_id = v_max_version_id
		  and price <> 0	
		  and container_qty<> 0
		group by version_id
		       ,year
		       ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,currency      
			   ,Huawei_group    
			   ,service_level   
			   ,is_high_quality 	
		union all
		-- 不区分是否精品：ALL=精品+货代
		select  version_id
		       ,year
	           ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,currency
			   ,sum(price) as price
			   ,sum(price*container_qty) as amount
			   ,sum(container_qty) as container_qty      
			   ,Huawei_group    
			   ,service_level   
			   ,'ALL' as is_high_quality 
		from  fin_dm_opt_foi.dm_fol_air_route_info_sum_t
		where 1=1
		  and transport_mode = '精品空运'
		  and version_id = v_max_version_id
		  and price <> 0	
		  and container_qty<> 0
		group by version_id
		       ,year
		       ,period_id
			   ,active_period
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_port_name
               ,dest_port_name
	           ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,currency      
			   ,Huawei_group    
			   ,service_level   	
             ;
			 
			  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '【 air_annual_trend_route_tmp 航线量临时表，数据量：'||v_dml_row_count||',version_id为'||v_max_version_id,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		 		 
		   -- 供应商的货量	
        drop table if exists supplier_qty_tmp;
        create temporary table supplier_qty_tmp
	      as		   
		select version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name
              ,dest_port_name
	          ,dest_country_name
              ,supplier_short_name
			  ,currency
			  ,sum(container_qty) as supplier_qty
			  ,Huawei_group    
			  ,service_level   
			  ,is_high_quality
    	  from  air_annual_trend_route_tmp t1		  
		  group by version_id
                  ,year
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_port_name
                  ,dest_port_name
	              ,dest_country_name
                  ,supplier_short_name
				  ,currency
				  ,Huawei_group    
			      ,service_level   
			      ,is_high_quality
				  ;
				  
				  -- 航线的货量
		drop table if exists route_qty_tmp;
        create temporary table route_qty_tmp
	      as	
		select version_id
              ,year
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name
              ,dest_port_name
	          ,dest_country_name
			  ,currency
			  ,sum(container_qty) as route_qty
			  ,Huawei_group    
			  ,service_level   
			  ,is_high_quality
    	  from  air_annual_trend_route_tmp t1		
		  group by version_id
                  ,year
				  ,transport_mode
				  ,region_cn_name
				  ,route
			      ,source_port_name
                  ,dest_port_name
	              ,dest_country_name
				  ,currency
				  ,Huawei_group    
			      ,service_level   
			      ,is_high_quality
				  ;
				  
				  -- 区域的货量
		drop table if exists region_qty_tmp;
        create temporary table region_qty_tmp
	      as	
		select version_id
              ,year
			  ,transport_mode
			  ,region_cn_name
			  ,currency
			  ,sum(container_qty) as region_qty
			  ,Huawei_group    
			  ,service_level   
			  ,is_high_quality
    	  from  air_annual_trend_route_tmp t1		
		  group by version_id
                  ,year
				  ,transport_mode
				  ,region_cn_name
				  ,currency
				  ,Huawei_group    
			      ,service_level   
			      ,is_high_quality
				  ;
				  
		-- 运输方式的货量
		drop table if exists transport_qty_tmp;
        create temporary table transport_qty_tmp
	      as	
		select version_id
		      ,year		  
			  ,transport_mode
			  ,currency
			  ,sum(container_qty) as transport_qty
			  ,Huawei_group   
			  ,service_level  
			  ,is_high_quality
    	  from  air_annual_trend_route_tmp t1		
		  group by version_id
		          ,year
				  ,transport_mode
				  ,currency
				  ,Huawei_group   
			      ,service_level  
			      ,is_high_quality
				  ;
 
             -- 计算各层级的货量占比
	    drop table if exists air_annual_trend_qty_tmp;
        create temporary table air_annual_trend_qty_tmp
	         as
		-- 供应商占航线的货量占比：供应商的当年货量/航线的当年货量
		select t1.version_id
		      ,t1.year
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.source_port_name     
              ,t1.dest_port_name       
	          ,t1.dest_country_name 
              ,t1.supplier_short_name
			  ,t1.currency
			  ,'04'   as level_code
  			  ,'供应商' as level_desc
			  ,round(t1.supplier_qty/t2.route_qty,10) as W_i
			  ,t1.Huawei_group   
              ,t1.service_level  
			  ,t1.is_high_quality
	    from supplier_qty_tmp t1
		left join route_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 where t2.route_qty <> 0
		 union all
		 -- 航线占区域的货量占比：航线的当年货量/区域的当年货量
		select t1.version_id
		      ,t1.year
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.source_port_name     
              ,t1.dest_port_name       
	          ,t1.dest_country_name
			  ,null as supplier_short_name
			  ,t1.currency
			  ,'03'   as level_code
  			  ,'航线' as level_desc
			  ,round(t1.route_qty/t2.region_qty,10) as W_i
			  ,t1.Huawei_group   
              ,t1.service_level  
			  ,t1.is_high_quality
	    from route_qty_tmp t1
		left join region_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 where t2.region_qty <> 0
		 union all
		 -- 区域占运输方式的货量占比 
		 select t1.version_id
		      ,t1.year		  
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,null as route
			  ,null as source_port_name     
              ,null as dest_port_name       
	          ,null as dest_country_name
			  ,null as supplier_short_name
			  ,t1.currency
			  ,'02'   as level_code
  			  ,'区域' as level_desc
			  ,round(t1.region_qty/t2.transport_qty,10) as W_i
			  ,t1.Huawei_group   
              ,t1.service_level  
			  ,t1.is_high_quality
	    from region_qty_tmp t1
		left join transport_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 where t2.transport_qty <> 0
		 ; 
		 
		  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 3,
            p_log_cal_log_desc => '【 air_annual_trend_qty_tmp 供应商占航线的货量占比，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		 
		 -- 卷积到供应商层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]} 
		 drop table if exists supplier_price_tmp;
        create temporary table supplier_price_tmp
	         as
		select version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,supplier_short_name
			  ,currency
			  ,sum(price) as price
			  ,sum(amount) as amount
			  ,sum(container_qty) as  all_qty
              ,Huawei_group       
              ,service_level      
              ,is_high_quality 
    	  from air_annual_trend_route_tmp		
		  group by version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,supplier_short_name
			  ,currency	
              ,Huawei_group       
              ,service_level      
              ,is_high_quality
			  ;
			  
			  -- 卷积到航线层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}
	    drop table if exists route_price_tmp;
        create temporary table route_price_tmp
	         as
		select version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,currency
			  ,sum(price) as price
			  ,sum(amount) as amount
			  ,sum(container_qty) as all_qty
              ,Huawei_group       
              ,service_level      
              ,is_high_quality 
    	  from air_annual_trend_route_tmp		
		  group by version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_port_name     
              ,dest_port_name       
	          ,dest_country_name
			  ,currency	
              ,Huawei_group       
              ,service_level      
              ,is_high_quality
			  ;
			  
			  -- 卷积到区域层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}
			  drop table if exists region_price_tmp;
              create temporary table region_price_tmp
	           as
		     select version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,currency
			  ,sum(price) as price
			  ,sum(amount) as amount
			  ,sum(container_qty) as all_qty
              ,Huawei_group       
              ,service_level      
              ,is_high_quality 
    	  from air_annual_trend_route_tmp		
		  group by version_id
		      ,year
			  ,transport_mode
			  ,region_cn_name
			  ,currency	
              ,Huawei_group       
              ,service_level      
              ,is_high_quality
		         ;
				 
				 -- 卷积到运输方式层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}/ 运输方式层级下当年的货量 
			drop table if exists transport_price_tmp;
            create temporary table transport_price_tmp
	         as
		   select version_id
		      ,year
			  ,transport_mode
			  ,currency
			  ,sum(price) as price
			  ,sum(amount) as amount
			  ,sum(container_qty) as all_qty
              ,Huawei_group       
              ,service_level      
              ,is_high_quality 
    	  from air_annual_trend_route_tmp		
		  group by version_id
		      ,year
			  ,transport_mode
			  ,currency	
              ,Huawei_group       
              ,service_level      
              ,is_high_quality
			  ;
 
               -- 计算精品空运个层级的均价
	    drop table if exists air_annual_trend_price_tmp;
        create temporary table air_annual_trend_price_tmp
	         as
		-- 供应商层级的均价：卷积到供应商层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}/ 供应商层级下当年的货量	
		select t1.version_id 
            ,t1.year                 
            ,t1.transport_mode     
            ,t1.region_cn_name     
            ,t1.route              
            ,t1.source_port_name     
            ,t1.dest_port_name       
	        ,t1.dest_country_name  
            ,t1.supplier_short_name
            ,t1.currency           
            ,t1.price              
            ,t1.all_qty as container_qty                                    
			,'04'       as level_code
  			,'供应商'   as level_desc
			,round(t1.amount/t1.all_qty,10) as avg_price
			,t1.Huawei_group       
            ,t1.service_level      
            ,t1.is_high_quality 
			,t2.W_i as weight
	    from supplier_price_tmp t1
		left join air_annual_trend_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.year = t2.year	
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route=t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.supplier_short_name=t2.supplier_short_name
		 and t1.currency = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 where t2.level_code = '04'
		 union all
		 -- 航线层级的均价：卷积到航线层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}/ 航线层级下当年的货量 
		select t1.version_id 
            ,t1.year                 
            ,t1.transport_mode     
            ,t1.region_cn_name     
            ,t1.route              
            ,t1.source_port_name     
            ,t1.dest_port_name       
	        ,t1.dest_country_name  
			,null as supplier_short_name
            ,t1.currency           
            ,t1.price              
            ,t1.all_qty as container_qty                                    
			,'03'       as level_code
  			,'航线'   as level_desc
			,round(t1.amount/t1.all_qty,10)  as avg_price
			,t1.Huawei_group       
            ,t1.service_level      
            ,t1.is_high_quality
			,t2.W_i as weight
	    from route_price_tmp t1
		left join air_annual_trend_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.year = t2.year	
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route=t2.route
		 and t1.source_port_name  = t2.source_port_name  
         and t1.dest_port_name    = t2.dest_port_name    
	     and t1.dest_country_name = t2.dest_country_name
		 and t1.currency = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 where t2.level_code = '03'
		 union all 
		 -- 卷积到区域层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}/ 区域层级下当年的货量 
		select t1.version_id 
            ,t1.year                 
            ,t1.transport_mode     
            ,t1.region_cn_name   
            ,null as route              
            ,null as source_port_name     
            ,null as dest_port_name       
	        ,null as dest_country_name  
			,null as supplier_short_name			
            ,t1.currency           
            ,t1.price              
            ,t1.all_qty as container_qty                                    
			,'02'       as level_code
  			,'区域'   as level_desc
			,round(t1.amount/t1.all_qty,10)  as avg_price
			,t1.Huawei_group       
            ,t1.service_level      
            ,t1.is_high_quality
			,t2.W_i as weight
	    from region_price_tmp t1
		left join air_annual_trend_qty_tmp t2
		  on t1.version_id = t2.version_id
		 and t1.year = t2.year	
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.currency = t2.currency
		 and t1.Huawei_group = t2.Huawei_group
		 and t1.service_level = t2.service_level
		 and t1.is_high_quality = t2.is_high_quality
		 where t2.level_code = '02'
		union all
		-- 运输方式层级的均价：	卷积到运输方式层级卷积到年{卷积到月[当日的（华为PTP价+ FUL价格）×当日的货量]}/ 运输方式层级下当年的货量 
		select t1.version_id 
            ,t1.year                 
            ,t1.transport_mode   
            ,null as region_cn_name   
            ,null as route              
            ,null as source_port_name     
            ,null as dest_port_name       
	        ,null as dest_country_name  
			,null as supplier_short_name				
            ,t1.currency           
            ,t1.price              
            ,t1.all_qty as container_qty                                    
			,'01'       as level_code
  			,'运输方式'   as level_desc
			,round(t1.amount/t1.all_qty,10)  as avg_price
			,t1.Huawei_group       
            ,t1.service_level      
            ,t1.is_high_quality
			,null as weight
	    from transport_price_tmp t1
		;
		
		    v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 6,
            p_log_cal_log_desc => '【 air_annual_trend_price_tmp 各层级的均价，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
			
             -- 缺失年份打标识
		  drop table if exists year_tmp;
          create temporary table year_tmp
	         as
                select v_max_version_id as version_id
                 , t1.year as all_year
                 , t2.year as miss_year
                 , (case when t1.year = t2.year then 'N' else 'Y' end) as year_miss_flag
              from all_year_tmp t1
              left join (select distinct version_id,year from air_annual_trend_route_tmp) t2
                on t1.year = t2.year
                ;
				
			-- 识别缺失年份	
		  drop table if exists cn_year_tmp;
          create temporary table cn_year_tmp
	         as
                 select version_id
                 , all_year
				 , miss_year
                 , year_miss_flag
                 , count(miss_year) over(partition by version_id order by all_year, miss_year) as cn
                 from year_tmp
           ;
		  
            -- 补齐缺失年份的数据（从2020年到当前年），缺失年份打标识，记录用的哪个年份补录的
			 drop table if exists apd_year_tmp;
          create temporary table apd_year_tmp
	         as
			   -- 往前补齐年份
               select version_id
               , all_year, miss_year
               , year_miss_flag
               , cn
               , (max(miss_year) over(partition by version_id, cn))::int as apd_year
              from cn_year_tmp
			  ;
         
		  drop table if exists air_apd_avg_price_tmp;
          create temporary table air_apd_avg_price_tmp
	         as
             -- 非缺失年份数据
             select version_id  
               ,year			 
               ,transport_mode     
               ,region_cn_name     
               ,route              
               ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name 
               ,supplier_short_name
               ,currency           
               ,price              
               ,container_qty      
               ,level_code         
               ,level_desc         
               ,avg_price            
               ,Huawei_group       
               ,service_level      
               ,is_high_quality  
               ,weight			   
			   , 'N'  as year_miss_flag   -- 缺失年份标识
               , null as apd_year         -- 补齐年份（即缺失年份用的哪年补齐）
		   from air_annual_trend_price_tmp
		   union all
		     -- 缺失年份数据
		   select t1.version_id  
               ,cast(t1.all_year as int) as year			 
               ,t2.transport_mode     
               ,t2.region_cn_name     
               ,t2.route              
               ,t2.source_port_name     
               ,t2.dest_port_name       
	           ,t2.dest_country_name  
               ,t2.supplier_short_name
               ,t2.currency           
               ,t2.price              
               ,t2.container_qty      
               ,t2.level_code         
               ,t2.level_desc         
               ,t2.avg_price            
               ,t2.Huawei_group       
               ,t2.service_level      
               ,t2.is_high_quality
               ,t2.weight			   
			   ,t1.year_miss_flag   -- 缺失年份标识
               ,t1.apd_year         -- 补齐年份（即缺失年份用的哪年补齐）
		   from apd_year_tmp t1
		   left join air_annual_trend_price_tmp t2
		    on t1.version_id = t2.version_id
		   and t1.apd_year = t2.year
		   where t1.year_miss_flag = 'Y'  -- 取缺失年份
		   ;
		   
		   v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 9,
            p_log_cal_log_desc => '【 air_apd_avg_price_tmp 均价补齐，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
		 
		  -- 当前年的数据
		  drop table if exists curr_tmp1;
          create temporary table curr_tmp1
	         as
		   select version_id  
               ,year			 
               ,transport_mode     
               ,region_cn_name     
               ,route              
               ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name  
               ,supplier_short_name
               ,currency           
               ,price              
               ,container_qty      
               ,level_code         
               ,level_desc         
               ,avg_price            
               ,Huawei_group       
               ,service_level      
               ,is_high_quality 
               ,weight			   
			   ,year_miss_flag   -- 缺失年份标识
               ,apd_year         -- 补齐年份（即缺失年份用的哪年补齐）
			   ,'Y' as current_year_flag -- 当年标识（Y 当年 N 上一年）
			from air_apd_avg_price_tmp
		   ;
		   
		    -- 上一年数据
		  drop table if exists last_tmp2;
          create temporary table last_tmp2
	         as
		   select version_id  
               ,year+1 as year			 
               ,transport_mode     
               ,region_cn_name     
               ,route              
               ,source_port_name     
               ,dest_port_name       
	           ,dest_country_name
               ,supplier_short_name
               ,currency           
               ,price              
               ,container_qty      
               ,level_code         
               ,level_desc         
               ,avg_price            
               ,Huawei_group       
               ,service_level      
               ,is_high_quality 
               ,weight			   
			   ,year_miss_flag   -- 缺失年份标识
               ,apd_year         -- 补齐年份（即缺失年份用的哪年补齐）
			   ,'N' as current_year_flag -- 当年标识（Y 当年 N 上一年）
			from air_apd_avg_price_tmp
		      ;
		   
		   -- 供应商、航线的年际涨跌幅以及均价权重信息
		   drop table if exists price_index_tmp1;
          create temporary table price_index_tmp1
	         as
		   select t1.version_id  
               ,t1.year			 
               ,t1.transport_mode     
               ,t1.region_cn_name     
               ,t1.route              
               ,t1.source_port_name     
               ,t1.dest_port_name       
	           ,t1.dest_country_name  
               ,t1.supplier_short_name
               ,t1.currency           
               ,t1.price              
               ,t1.container_qty      
               ,t1.level_code         
               ,t1.level_desc         
               ,t1.avg_price            
               ,t1.Huawei_group       
               ,t1.service_level      
               ,t1.is_high_quality 
               ,t1.weight	
               , (case when t1.level_code in('03','04')
                       then round(t1.avg_price/t2.avg_price-1,10)
                       end) as price_index  			   
			   ,t1.year_miss_flag   -- 缺失年份标识
               ,(case when t1.year_miss_flag = 'Y' and t1.year_miss_flag = 'Y' 
			          then null
                      else t2.apd_year
                       end) as apd_year         -- 补齐年份（即缺失年份用的哪年补齐） 
			   , (case when t1.year_miss_flag = 'Y' and t2.year_miss_flag = 'N' 
			           then 1  -- 当年无数据，上年有数，无法计算涨跌幅
                       when t1.year_miss_flag = 'Y' and t2.year_miss_flag = 'Y' 
					   then 2  -- 当年、上年无数据，无法计算涨跌幅
                       when t1.year_miss_flag = 'N' and t2.year_miss_flag = 'N' 
					   then 3  -- 可以计算涨跌幅
                       when t1.year_miss_flag = 'N' and t2.year_miss_flag = 'Y' and t2.apd_year is null 
					   then 4  -- 当年有数，上年无数据，且上年无法用历史年均本补齐，无法计算涨跌幅
                       when t1.year_miss_flag = 'N' and t2.year_miss_flag = 'Y' and t2.apd_year is not null 
					   then 5  -- 当年有数，上年无数据，且上年已用历史年均本补齐，可以计算涨跌幅
          end) as year_avg_miss_code   -- 年均价缺失情况编码			   
			from curr_tmp1 t1
			left join last_tmp2 t2
			   on t1.version_id = t2.version_id
		      and t1.year = t2.year	
		      and t1.transport_mode = t2.transport_mode
		      and nvl(t1.region_cn_name,'SNULL') = nvl(t2.region_cn_name,'SNULL')
		      and nvl(t1.route,'SNULL') = nvl(t2.route,'SNULL')
			  and nvl(t1.source_port_name,'SNULL') = nvl(t2.source_port_name,'SNULL')
			  and nvl(t1.dest_port_name,'SNULL') = nvl(t2.dest_port_name,'SNULL')
			  and nvl(t1.dest_country_name,'SNULL') = nvl(t2.dest_country_name,'SNULL')
		      and nvl(t1.supplier_short_name,'SNULL') = nvl(t2.supplier_short_name,'SNULL')
		      and nvl(t1.currency,'SNULL') = nvl(t2.currency,'SNULL')
	          and t1.level_code = t2.level_code
		      and t1.Huawei_group = t2.Huawei_group
		      and t1.service_level = t2.service_level
		      and t1.is_high_quality = t2.is_high_quality
		     ;
 
                v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 10,
            p_log_cal_log_desc => '【 price_index_tmp1 供应商、航线的年纪涨跌幅以及均价权重信息，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
 
               -- 计算区域的年际涨跌幅
		  drop table if exists region_price_index_tmp;
          create temporary table region_price_index_tmp
	         as			
				select  version_id  
                  ,year			 
                  ,transport_mode     
                  ,region_cn_name                      
                  ,currency           
                  ,'02' as level_code         
                  ,'区域' level_desc                   
                  ,Huawei_group       
                  ,service_level      
                  ,is_high_quality 	
                  ,sum(price_index*weight) as price_index			   
				from price_index_tmp1
				where level_code = '03'
				group by  version_id  
                  ,year			 
                  ,transport_mode     
                  ,region_cn_name                      
                  ,currency
				  ,Huawei_group       
                  ,service_level      
                  ,is_high_quality
				  ;
				  
				  -- 插入供应商层级、航线层级、区域层级的数据
		  drop table if exists price_index_tmp2;
          create temporary table price_index_tmp2
	         as             
				 select t1.version_id  
                  ,t1.year			 
                  ,t1.transport_mode     
                  ,t1.region_cn_name     
                  ,t1.route  
                  ,t1.source_port_name     
                  ,t1.dest_port_name       
	              ,t1.dest_country_name 				  
                  ,t1.supplier_short_name
                  ,t1.currency           
                  ,t1.price              
                  ,t1.container_qty      
                  ,t1.level_code         
                  ,t1.level_desc         
                  ,t1.avg_price            
                  ,t1.Huawei_group       
                  ,t1.service_level      
                  ,t1.is_high_quality 
                  ,t1.weight		
                  ,t2.price_index			   
			      ,t1.year_miss_flag    
                  ,t1.apd_year
			      ,t1.year_avg_miss_code
                from price_index_tmp1 t1
				left join region_price_index_tmp t2
                  on t1.version_id = t2.version_id
		         and t1.year = t2.year	
		         and t1.transport_mode = t2.transport_mode
		         and t1.region_cn_name = t2.region_cn_name
		         and t1.currency = t2.currency
	             and t1.level_code = t2.level_code
		         and t1.Huawei_group = t2.Huawei_group
		         and t1.service_level = t2.service_level
		         and t1.is_high_quality = t2.is_high_quality
                 where t1.level_code = '02'
				 union all
				 -- 插入供应商和航线的年际涨跌幅
				 select  version_id  
                   ,year			 
                   ,transport_mode     
                   ,region_cn_name     
                   ,route              
                   ,source_port_name     
                   ,dest_port_name       
	               ,dest_country_name  
                   ,supplier_short_name
                   ,currency           
                   ,price              
                   ,container_qty      
                   ,level_code         
                   ,level_desc         
                   ,avg_price            
                   ,Huawei_group       
                   ,service_level      
                   ,is_high_quality 
                   ,weight		
                   ,price_index			   
			       ,year_miss_flag    
                   ,apd_year
			       ,year_avg_miss_code
				 from price_index_tmp1
				 where level_code in('03','04')
				 ;
				 
				 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 11,
            p_log_cal_log_desc => '【 price_index_tmp2 区域的年际涨跌幅，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
				 
				 -- 计算运输方式的年际涨跌幅
		  drop table if exists transport_price_index_tmp;
          create temporary table transport_price_index_tmp
	         as    				
				select  version_id  
                  ,year			 
                  ,transport_mode                          
                  ,currency           
                  ,'01' as level_code         
                  ,'运输方式' level_desc                   
                  ,Huawei_group       
                  ,service_level      
                  ,is_high_quality 	
                  ,sum(price_index*weight) as price_index			   
				from price_index_tmp2
				where level_code = '02'
				group by  version_id  
                  ,year			 
                  ,transport_mode                        
                  ,currency
				  ,Huawei_group       
                  ,service_level      
                  ,is_high_quality
				  ;
				  
				  -- 插入运输方式层级的数据
               insert into price_index_tmp2(
		            version_id  
                   ,year			 
                   ,transport_mode     
                   ,region_cn_name     
                   ,route              
                   ,source_port_name     
                   ,dest_port_name       
	               ,dest_country_name 
                   ,supplier_short_name
                   ,currency           
                   ,price              
                   ,container_qty      
                   ,level_code         
                   ,level_desc         
                   ,avg_price            
                   ,Huawei_group       
                   ,service_level      
                   ,is_high_quality 
                   ,weight		
                   ,price_index			   
			       ,year_miss_flag    
                   ,apd_year
			       ,year_avg_miss_code
		            )					
				 select t1.version_id  
                  ,t1.year			 
                  ,t1.transport_mode     
                  ,t1.region_cn_name     
                  ,t1.route              
                  ,t1.source_port_name     
                  ,t1.dest_port_name       
	              ,t1.dest_country_name
                  ,t1.supplier_short_name
                  ,t1.currency           
                  ,t1.price              
                  ,t1.container_qty      
                  ,t1.level_code         
                  ,t1.level_desc         
                  ,t1.avg_price            
                  ,t1.Huawei_group       
                  ,t1.service_level      
                  ,t1.is_high_quality 
                  ,t1.weight		
                  ,t2.price_index			   
			      ,t1.year_miss_flag    
                  ,t1.apd_year
			      ,t1.year_avg_miss_code
                from price_index_tmp1 t1
				left join transport_price_index_tmp t2
                  on t1.version_id = t2.version_id
		         and t1.year = t2.year	
		         and t1.transport_mode = t2.transport_mode		         
		         and t1.currency = t2.currency
	             and t1.level_code = t2.level_code
		         and t1.Huawei_group = t2.Huawei_group
		         and t1.service_level = t2.service_level
		         and t1.is_high_quality = t2.is_high_quality
                 where t1.level_code = '01'	
				 ;
				 
				  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 12,
            p_log_cal_log_desc => '【 price_index_tmp2 运输方式的年际涨跌幅，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
    
	          delete from fin_dm_opt_foi.dm_fol_air_annual_price_index_trend_info_t  where version_id = v_max_version_id;
	
	            -- 将结果插入目标表
				insert into fin_dm_opt_foi.dm_fol_air_annual_price_index_trend_info_t(
				   version_id
                  ,year
                  ,base_year
                  ,transport_mode
                  ,region_cn_name
                  ,route
                  ,source_port_name     
                  ,dest_port_name       
	              ,dest_country_name
                  ,supplier_short_name
                  ,level_code
                  ,level_desc
                  ,currency
                  ,price
                  ,container_qty
                  ,avg_price
                  ,weight
                  ,price_index
                  ,year_miss_flag
                  ,apd_year
                  ,year_avg_miss_code
                  ,Huawei_group
                  ,service_level
                  ,is_high_quality
                  ,remark
                  ,created_by
                  ,creation_date
                  ,last_updated_by
                  ,last_update_date
                  ,del_flag
				)
               select version_id
                  ,year
                  ,year-1 as base_year
                  ,transport_mode
                  ,region_cn_name
                  ,route
                  ,source_port_name     
                  ,dest_port_name       
	              ,dest_country_name
                  ,supplier_short_name
                  ,level_code
                  ,level_desc
                  ,currency
                  ,price
                  ,container_qty
                  ,avg_price
                  ,(case when year_avg_miss_code in(1,2) 
				         then null
                         else weight
                          end) as weight
                  ,(case when year_avg_miss_code in(1,2) 
				         then null
                         else sum(price_index)
                          end) as price_index
                  ,year_miss_flag
                  ,apd_year
                  ,year_avg_miss_code
                  ,Huawei_group
                  ,service_level
                  ,is_high_quality
                  , '' as remark
	              , -1 as created_by
	              , current_timestamp as creation_date
	              , -1 as last_updated_by
	              , current_timestamp as last_update_date
	              , 'N' as del_flag
				 from price_index_tmp2
                 where year_avg_miss_code is not null  -- 排除当年与去年没关联上的数据
				 group by version_id
                  ,year
                  ,transport_mode
                  ,region_cn_name
                  ,route
                  ,source_port_name     
                  ,dest_port_name       
	              ,dest_country_name
                  ,supplier_short_name
                  ,level_code
                  ,level_desc
                  ,currency
                  ,price
                  ,container_qty
                  ,avg_price
				  ,weight
				  ,year_miss_flag
                  ,apd_year
                  ,year_avg_miss_code
                  ,Huawei_group
                  ,service_level
                  ,is_high_quality
				  ;
        
		        v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 13,
            p_log_cal_log_desc => '【 dm_fol_air_annual_price_index_trend_info_t 将结果插入目标表，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;

               -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_air_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 1 
		 and source_en_name = 'f_dm_fol_air_annual_price_index_trend_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');	 
     
              --将版本信息表中的执行步骤改为：1 成功
		update fin_dm_opt_foi.dm_fol_air_version_info_t 
		set step = 1 
		where source_en_name = 'f_dm_fol_air_annual_price_index_trend_info_t' 
		  and version_id = v_max_version_id
		  and step = 2
		  and refresh_type = nvl(p_refresh_type,'4_AUTO');
		  
		   v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 14,
            p_log_cal_log_desc => '【 dm_fol_air_version_info_t  成功数据写入到版本信息表，数据量：：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
         
		   --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_air_annual_price_index_trend_info_t;
  analyse fin_dm_opt_foi.dm_fol_air_version_info_t;
	 
	     exception
    when others then
       perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败
	
	-- 失败数据写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_air_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
		 , transport_mode       -- 运输方式（精品空运、精品海运）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )   
    select v_max_version_id   as version_id
         , v_price_version_code as version_code
         , 2001 as step
         , 'f_dm_fol_air_annual_price_index_trend_info_t' as source_en_name
         , '物流空运价格指数年度趋势函数' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
		 , '精品空运' as transport_mode
         , 'version_code为物流价格补录表的最大版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  ;
  
end;
$$
/

