-- Name: f_dm_foc_total_month_result; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_total_month_result(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近修改时间: 2024年6月20日17点22分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间：2023-10-30
创建人  ：黄心蕊 hwx1187045
背景描述：总成本-权重图&指数图&指数同环比图数据初始化
修改时间：2023-12-22
修改人  ：黄心蕊 hwx1187045
背景描述：202401版本量纲新增SPART层级
参数描述：参数一(F_DIMENSION_TYPE)：维度入参，'U'为通用颗粒度，'P'为盈利颗粒度，'D'为量纲颗粒度
		  参数二(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败
通用颗粒度	  
来源表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_ACTUAL_COST_T 		--总成本热力图表
         FIN_DM_OPT_FOI.DM_FOC_MONTH_COST_IDX_T			--采购指数表
		 FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_T	--制造指数表
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_MONTH_WEIGHT_T		--总成本权重表
		 FIN_DM_OPT_FOI.DM_FOC_TOTAL_MONTH_COST_IDX_T	--总成本指数表
		 FIN_DM_OPT_FOI.DM_FOC_TOTAL_MONTH_RATE_T		--总成本指数同环比表
盈利颗粒度	  
来源表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ACTUAL_COST_T 		--总成本热力图表
         FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_COST_IDX_T			--采购指数表
		 FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_T	--制造指数表
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MONTH_WEIGHT_T		--总成本权重表
		 FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MONTH_COST_IDX_T	--总成本指数表
		 FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MONTH_RATE_T		--总成本指数同环比表
量纲颗粒度	  
来源表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ACTUAL_COST_T 		--总成本热力图表
         FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T			--采购指数表
		 FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_T	--制造指数表
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MONTH_WEIGHT_T		--总成本权重表
		 FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MONTH_COST_IDX_T	--总成本指数表
		 FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MONTH_RATE_T		--总成本指数同环比表
		 
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_MONTH_RESULT('U'); --通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_MONTH_RESULT('P'); --盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_MONTH_RESULT('D'); --盈利颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME             VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_MONTH_RESULT';
  V_VERSION             BIGINT;
  V_STEP_NUM            BIGINT := 0; --函数步骤号
  V_FROM_TABLE          VARCHAR(100);
  V_TO_WEIGHT_TABLE     TEXT := NULL;
  V_TO_IDX_TABLE        TEXT := NULL;
  V_TO_YOY_TABLE        TEXT := NULL;
  V_PERIOD_YEAR         VARCHAR(100) DEFAULT TO_CHAR(YEAR(CURRENT_DATE) - 1 || '-' ||YEAR(CURRENT_DATE));
  V_BETWEEN_YEAR        TEXT := NULL;
  V_DIM_PART            TEXT; --查询字段 不同颗粒度维度字段不同
  V_WEIGHT_TYPE         TEXT := NULL;
  V_PARTITION_PART      TEXT := NULL;
  V_SQL                 TEXT;
  V_BASE_PERIOD_ID      VARCHAR(100) DEFAULT TO_NUMBER(YEAR(CURRENT_DATE) - 1 || '01');
  V_DIM_LEVEL_PART      TEXT; --查询层级 不同颗粒度所需维度层级不同
  V_SQL_DIM_PART        TEXT := NULL;
  V_JOIN_SQL1           TEXT := NULL;
  V_JOIN_SQL2           TEXT := NULL;
  V_FROM_PUR_IDX_TABLE  TEXT := NULL;
  V_FROM_MADE_IDX_TABLE TEXT := NULL;
  V_WEIGHT_RATE 				TEXT := NULL;
  V_MIN_PERIOD					 VARCHAR(100);
  V_MAX_PERIOD					 VARCHAR(100);
  

BEGIN

--1.变量定义
 --1.1 来源表及目标表定义
   V_BETWEEN_YEAR:=' (YEAR(CURRENT_DATE)-1,YEAR(CURRENT_DATE)) ';
   
  IF F_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度
    V_DIM_LEVEL_PART      := ' (''LV0'',''LV1'',''LV2'',''LV3'',''LV4'') ';		--202405版本 ICT更新为LV0
																--202407版本 IAS新增LV4层级
	V_WEIGHT_RATE 		  := ' SUM(TOTAL_AMT) / 
								NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY VIEW_FLAG, CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																			GROUP_LEVEL,PARENT_CODE), 0) AS WEIGHT_RATE ,';
	
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_MADE_MID_MONTH_ITEM_T;

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_MID_MONTH_ITEM_T;
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_MID_MONTH_ITEM_T;
		
	  END IF;
																			
  
  ELSIF F_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
	
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_MID_MONTH_ITEM_T;

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_MID_MONTH_ITEM_T;
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_MID_MONTH_ITEM_T;
		
	  END IF;
	
    V_DIM_PART            := 'L1_NAME,L2_NAME,';
    V_PARTITION_PART      := 'NVL(L1_NAME,''L1''),NVL(L2_NAME,''L2''),';
    V_DIM_LEVEL_PART      := ' (''LV0'',''LV1'',''LV2'',''L1'',''L2'') ';	--202405版本 ICT更新为LV0
    V_SQL_DIM_PART        := 'T1.L1_NAME,T1.L2_NAME,';
	
    V_JOIN_SQL1           := 'AND NVL(T1.L1_NAME || T1.L2_NAME, ''AAA'') =
                          NVL(DECODE(T1.L1_NAME, '''' , '''', T2.L1_NAME) ||
                              DECODE(T1.L2_NAME, '''' , '''', T2.L2_NAME),''AAA'')';
    V_JOIN_SQL2           := 'AND NVL(T1.L1_NAME || T1.L2_NAME, ''AAA'') =
                          NVL(DECODE(T1.L1_NAME, '''' , '''', T3.L1_NAME) ||
                              DECODE(T1.L2_NAME, '''' , '''', T3.L2_NAME),''AAA'')';
	V_WEIGHT_RATE 		  := ' CASE WHEN GROUP_LEVEL IN (''L1'',''L2'') 
									THEN SUM(TOTAL_AMT) / 
											NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																						VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																						PARENT_CODE), 0) 
									ELSE SUM(TOTAL_AMT) / 
											NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																						VIEW_FLAG, GROUP_LEVEL,
																						PARENT_CODE), 0) END AS WEIGHT_RATE ,';

  
  ELSIF F_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
	
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_MID_MONTH_ITEM_T;
		
		V_DIM_PART            := 'DIMENSION_CODE,DIMENSION_CN_NAME,
								  DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
								  DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
								  SPART_CODE , SPART_CN_NAME,';  --202401版本新增SPART层级
		V_PARTITION_PART      := 'NVL(DIMENSION_CODE,''D1''),
								  NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
								  NVL(DIMENSION_SUB_DETAIL_CODE,''D3''), 
								  NVL(SPART_CODE,''D4''), ';  --202401版本新增SPART层级
		V_DIM_LEVEL_PART      := ' (''LV0'',''LV1'',''LV2'',''LV3'',	--202405版本 ICT更新为LV0
									''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'' ) '; --202401版本新增SPART层级
		V_SQL_DIM_PART        := 'T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,
								 T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,
								 T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,
								 T1.SPART_CODE , T1.SPART_CN_NAME,';  --202401版本新增SPART层级
								 
		V_JOIN_SQL1           := 'AND NVL(T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE|| T1.SPART_CODE ,''AAA'') =
								 NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE)||
								 DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE),
								 ''AAA'')';		--202401版本新增SPART层级
		V_JOIN_SQL2           := 'AND NVL( T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE||T1.SPART_CODE,''AAA'') =
								   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T3.DIMENSION_CODE) ||
								   DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T3.DIMENSION_SUBCATEGORY_CODE) ||
								   DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T3.DIMENSION_SUB_DETAIL_CODE)||
								   DECODE(T1.SPART_CODE,'''','''',T3.SPART_CODE),
								   ''AAA'')';  --202401版本新增SPART层级
								   
		V_WEIGHT_RATE 		  := ' CASE WHEN GROUP_LEVEL IN (''SPART'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
																							NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
																							PARENT_CODE), 0)   --202401版本新增SPART层级
										WHEN GROUP_LEVEL IN (''SUB_DETAIL'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
																							PARENT_CODE), 0) 
										WHEN GROUP_LEVEL IN (''SUBCATEGORY'',''DIMENSION'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																							PARENT_CODE),0) 
										ELSE SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,
																							PARENT_CODE), 0) END AS WEIGHT_RATE ,';
		

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_MID_MONTH_ITEM_T;
		
		V_DIM_PART            := 'DIMENSION_CODE,DIMENSION_CN_NAME,
								  DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
								  DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
								  SPART_CODE , SPART_CN_NAME,  --202401版本新增SPART层级
								  COA_CODE,COA_CN_NAME,';  	--202405版本 数字能源新增COA层级
		V_PARTITION_PART      := 'NVL(DIMENSION_CODE,''D1''),
								  NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
								  NVL(DIMENSION_SUB_DETAIL_CODE,''D3''), 
								  NVL(SPART_CODE,''D4''), 
								  NVL(COA_CODE,''D5''), ';  --202401版本新增SPART层级
													--202405版本 数字能源新增COA层级
		V_DIM_LEVEL_PART      := ' (''LV0'',''LV1'',''LV2'',''LV3'',			--202405版本 ICT更新为LV0
									''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'',
									''COA'' ) '; --202401版本新增SPART层级
												--202405版本 数字能源新增COA层级
		V_SQL_DIM_PART        := 'T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,
								 T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,
								 T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,
								 T1.SPART_CODE , T1.SPART_CN_NAME,
								 T1.COA_CODE , T1.COA_CN_NAME,
								 ';  --202401版本新增SPART层级
									--202405版本 数字能源新增COA层级
		V_JOIN_SQL1           := 'AND NVL(T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE|| 
										T1.SPART_CODE||
										T1.COA_CODE,''AAA'') =
								 NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE)||
								 DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE)||
								 DECODE(T1.COA_CODE,'''','''',T2.COA_CODE),
								 ''AAA'')';		--202401版本新增SPART层级
										--202405版本 数字能源新增COA层级
		V_JOIN_SQL2           := 'AND NVL( T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE||
											T1.SPART_CODE||
											T1.COA_CODE,''AAA'') =
								   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T3.DIMENSION_CODE) ||
								   DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T3.DIMENSION_SUBCATEGORY_CODE) ||
								   DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T3.DIMENSION_SUB_DETAIL_CODE)||
								   DECODE(T1.SPART_CODE,'''','''',T3.SPART_CODE)||
								   DECODE(T1.COA_CODE,'''','''',T3.COA_CODE),
								   ''AAA'')';  --202401版本新增SPART层级
										--202405版本 数字能源新增COA层级
		V_WEIGHT_RATE 		  := ' CASE WHEN GROUP_LEVEL IN (''SPART'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																							NVL(DIMENSION_CODE,''D1''),
																							NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
																							NVL(COA_CODE,''D3''),	--202405版本 数字能源新增COA层级
																							PARENT_CODE), 0)   --202401版本新增SPART层级
										WHEN GROUP_LEVEL IN (''SUB_DETAIL'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																							NVL(DIMENSION_CODE,''D1''),
																							NVL(COA_CODE,''D3''),	--202405版本 数字能源新增COA层级
																							PARENT_CODE), 0) 
										WHEN GROUP_LEVEL IN (''SUBCATEGORY'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																							NVL(COA_CODE,''D3''),	--202405版本 数字能源新增COA层级
																							PARENT_CODE), 0) 
										WHEN GROUP_LEVEL IN (''DIMENSION'',''COA'') 	--202405版本 数字能源新增COA层级
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																							PARENT_CODE),0) 	
										ELSE SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,
																							PARENT_CODE), 0) END AS WEIGHT_RATE ,';
																							
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN --202407版本 新增IAS部分
	  /*表定义*/
		V_FROM_TABLE          := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ACTUAL_COST_T';
		V_TO_WEIGHT_TABLE     := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_MONTH_WEIGHT_T';
		V_TO_IDX_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_MONTH_COST_IDX_T';
		V_TO_YOY_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_MONTH_RATE_T';
		V_FROM_PUR_IDX_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MONTH_COST_IDX_T';
		V_FROM_MADE_IDX_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MONTH_COST_IDX_T';
		
		SELECT MIN(PERIOD_ID),MAX(PERIOD_ID) INTO V_MIN_PERIOD,V_MAX_PERIOD
		FROM FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_MID_MONTH_ITEM_T;
		
		V_DIM_PART            := 'DIMENSION_CODE,DIMENSION_CN_NAME,
								  DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
								  DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
								  SPART_CODE , SPART_CN_NAME,';  
		V_PARTITION_PART      := 'NVL(DIMENSION_CODE,''D1''),
								  NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
								  NVL(DIMENSION_SUB_DETAIL_CODE,''D3''), 
								  NVL(SPART_CODE,''D4''), ';  
		V_DIM_LEVEL_PART      := ' (''LV0'',''LV1'',''LV2'',''LV3'',''LV4'',		--202407版本 IAS新增LV4层级
									''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'' ) '; 
		V_SQL_DIM_PART        := 'T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,
								 T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,
								 T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,
								 T1.SPART_CODE , T1.SPART_CN_NAME,';  
								 
		V_JOIN_SQL1           := 'AND NVL(T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE|| T1.SPART_CODE ,''AAA'') =
								 NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE)||
								 DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE),
								 ''AAA'')';		
		V_JOIN_SQL2           := 'AND NVL( T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE||T1.SPART_CODE,''AAA'') =
								   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T3.DIMENSION_CODE) ||
								   DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T3.DIMENSION_SUBCATEGORY_CODE) ||
								   DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T3.DIMENSION_SUB_DETAIL_CODE)||
								   DECODE(T1.SPART_CODE,'''','''',T3.SPART_CODE),
								   ''AAA'')';  
								   
		V_WEIGHT_RATE 		  := ' CASE WHEN GROUP_LEVEL IN (''SPART'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
																							NVL(DIMENSION_SUBCATEGORY_CODE,''D2''),
																							PARENT_CODE), 0)   --202401版本新增SPART层级
										WHEN GROUP_LEVEL IN (''SUB_DETAIL'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,NVL(DIMENSION_CODE,''D1''),
																							PARENT_CODE), 0) 
										WHEN GROUP_LEVEL IN (''SUBCATEGORY'',''DIMENSION'') 
										THEN SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,PROD_RND_TEAM_CODE,
																							PARENT_CODE),0) 
										ELSE SUM(TOTAL_AMT) / 
												NULLIF(SUM(SUM(TOTAL_AMT))OVER(PARTITION BY CALIBER_FLAG,OVERSEA_FLAG, LV0_PROD_LIST_CODE,
																							VIEW_FLAG,GROUP_LEVEL,
																							PARENT_CODE), 0) END AS WEIGHT_RATE ,';
		
	  END IF;

	
  END IF;

  
 --1.2 版本号定义
 /* SELECT VERSION_ID INTO V_VERSION
    FROM FIN_DM_OPT_FOI.DM_FOC_MADE_TOP_ITEM_INFO_T
   ORDER BY LAST_UPDATE_DATE DESC
   LIMIT 1;*/
   
 	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑
	ELSIF F_INDUSTRY_FLAG = 'E' THEN --202405版本 新增数字能源逻辑
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	END IF ; 
	
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||F_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
--2.成本分布图计算
--2.1成本分布图表同版本数据删除
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL:='DELETE FROM '||V_TO_WEIGHT_TABLE||' WHERE VERSION_ID = '||V_VERSION;
	dbms_output.put_line(V_SQL);
  EXECUTE V_SQL;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除'||V_TO_WEIGHT_TABLE||'同版本数据完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
--2.2 权重表插数
  
  FOR V_DIM_NUM IN 1 .. 3 LOOP
    V_STEP_NUM := V_STEP_NUM + 1;
    IF V_DIM_NUM = 1 THEN
      V_WEIGHT_TYPE := 'SUM(PUR_AMT) / NULLIF(SUM(TOTAL_AMT), 0) AS WEIGHT_RATE, ''P'' AS COST_TYPE, ';
      
    ELSIF V_DIM_NUM = 2 THEN
      V_WEIGHT_TYPE := 'SUM(MADE_AMT) / NULLIF(SUM(TOTAL_AMT), 0) AS WEIGHT_RATE, ''M'' AS COST_TYPE, ';
      
    ELSIF V_DIM_NUM = 3 THEN
      V_WEIGHT_TYPE := V_WEIGHT_RATE|| '''T'' AS COST_TYPE, ';
    END IF;
   
V_SQL:= '
INSERT INTO '||V_TO_WEIGHT_TABLE||'
  (VERSION_ID,
   PERIOD_YEAR,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_DIM_PART||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   WEIGHT_RATE,
   COST_TYPE,
   PARENT_CODE,
   PARENT_CN_NAME,
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME)

SELECT '||V_VERSION||' AS VERSION_ID,
       '''||V_PERIOD_YEAR||''' AS PERIOD_YEAR,
       PROD_RND_TEAM_CODE,
       PROD_RND_TEAM_CN_NAME,
	   '||V_DIM_PART||'
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       '||V_WEIGHT_TYPE||'
       PARENT_CODE,
       PARENT_CN_NAME,
       ''-1'' AS CREATED_BY,
       CURRENT_DATE AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_DATE AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       VIEW_FLAG,
       CALIBER_FLAG,
       OVERSEA_FLAG,
       LV0_PROD_LIST_CODE,
       LV0_PROD_LIST_CN_NAME
  FROM '||V_FROM_TABLE||'
 WHERE APPEND_FLAG = ''N''
   AND VERSION_ID = '||V_VERSION||'
   AND TOTAL_AMT <> 0 
   AND PERIOD_YEAR IN '||V_BETWEEN_YEAR||'
 GROUP BY VIEW_FLAG,
          CALIBER_FLAG,
          OVERSEA_FLAG,
          LV0_PROD_LIST_CODE,
          LV0_PROD_LIST_CN_NAME,
		  '||V_DIM_PART||'
          PARENT_CODE,
          PARENT_CN_NAME,
          PROD_RND_TEAM_CODE,
          PROD_RND_TEAM_CN_NAME,
          GROUP_CODE,
          GROUP_CN_NAME,
          GROUP_LEVEL,
          VERSION_ID; ';

	dbms_output.put_line(V_SQL);
	EXECUTE V_SQL;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||V_DIM_NUM||'权重计算完成',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

END LOOP;

/*指数表删数*/
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL:= 'DELETE FROM '||V_TO_IDX_TABLE||' WHERE VERSION_ID ='||V_VERSION;
	dbms_output.put_line(V_SQL);
   EXECUTE V_SQL;

/*指数表插数*/
  V_STEP_NUM := V_STEP_NUM + 1;
  DBMS_OUTPUT.PUT_LINE('指数跑数');
V_SQL:='
--采购指数部分取数
WITH PUR_IDX AS
 (SELECT T1.PERIOD_ID,
         T1.PROD_RND_TEAM_CODE,
         T1.PROD_RND_TEAM_CN_NAME,
		 '||V_SQL_DIM_PART||'
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.COST_INDEX * T2.WEIGHT_RATE AS PUR_IDX,
         T1.PARENT_CODE,
         T2.PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.APPEND_FLAG,
         T1.CALIBER_FLAG,
         T1.OVERSEA_FLAG,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME
    FROM '||V_FROM_PUR_IDX_TABLE||' T1
    JOIN '||V_TO_WEIGHT_TABLE||' T2
    ON T1.VERSION_ID||T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
       T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
       T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
       T2.VERSION_ID|| T2.PROD_RND_TEAM_CODE || T2.GROUP_CODE ||
       T2.GROUP_LEVEL || T2.PARENT_CODE || T2.VIEW_FLAG || T2.CALIBER_FLAG ||
       T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE '||V_JOIN_SQL1||'
   WHERE T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID ||'
     AND T1.SCENARIO_FLAG = ''S''
	 AND T2.COST_TYPE = ''P''
     AND T1.VERSION_ID = '||V_VERSION||'
     AND T1.GROUP_LEVEL IN '||V_DIM_LEVEL_PART||'
	 AND T1.PERIOD_ID BETWEEN '||V_MIN_PERIOD||' AND '||V_MAX_PERIOD||'
	 ),

--制造指数部分取数
MADE_IDX AS
 (SELECT T1.PERIOD_ID,
         T1.PROD_RND_TEAM_CODE,
         T1.PROD_RND_TEAM_CN_NAME,
		 '||V_SQL_DIM_PART||'
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.COST_INDEX * T2.WEIGHT_RATE AS MADE_IDX,
         T1.PARENT_CODE,
         T2.PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.APPEND_FLAG,
         T1.CALIBER_FLAG,
         T1.OVERSEA_FLAG,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME
    FROM '||V_FROM_MADE_IDX_TABLE||' T1
    JOIN '||V_TO_WEIGHT_TABLE||' T2
    ON T1.VERSION_ID||T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
       T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
       T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
       T2.VERSION_ID|| T2.PROD_RND_TEAM_CODE || T2.GROUP_CODE ||
       T2.GROUP_LEVEL || T2.PARENT_CODE || T2.VIEW_FLAG || T2.CALIBER_FLAG ||
       T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE '||V_JOIN_SQL1||'
   WHERE T1.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
	 AND T2.COST_TYPE = ''M''
     AND T1.VERSION_ID = '||V_VERSION||'
     AND T1.GROUP_LEVEL IN '||V_DIM_LEVEL_PART||'
	 AND T1.PERIOD_ID BETWEEN '||V_MIN_PERIOD||' AND '||V_MAX_PERIOD||'
	 ) ,

--取全量维度及会计期	 
FULL_DIM AS (
SELECT DISTINCT T1.PERIOD_ID,
         T1.PROD_RND_TEAM_CODE,
         T1.PROD_RND_TEAM_CN_NAME,
		 '||V_SQL_DIM_PART||'
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.APPEND_FLAG,
         T1.CALIBER_FLAG,
         T1.OVERSEA_FLAG,
         T1.LV0_PROD_LIST_CODE,
         T1.LV0_PROD_LIST_CN_NAME
  FROM (
SELECT DISTINCT PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
  FROM PUR_IDX  
  UNION ALL
SELECT DISTINCT PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         VIEW_FLAG,
         APPEND_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME 
  FROM MADE_IDX ) T1
)

 INSERT INTO '||V_TO_IDX_TABLE||'
   (VERSION_ID,
    PERIOD_YEAR,
    PERIOD_ID,
    BASE_PERIOD_ID,
    PROD_RND_TEAM_CODE,
    PROD_RND_TEAM_CN_NAME,
	'||V_DIM_PART||'
    GROUP_CODE,
    GROUP_CN_NAME,
    GROUP_LEVEL,
    COST_INDEX,
    PARENT_CODE,
    PARENT_CN_NAME,
    CREATED_BY,
    CREATION_DATE,
    LAST_UPDATED_BY,
    LAST_UPDATE_DATE,
    DEL_FLAG,
    VIEW_FLAG,
    CALIBER_FLAG,
    OVERSEA_FLAG,
    LV0_PROD_LIST_CODE,
    LV0_PROD_LIST_CN_NAME)
SELECT '||V_VERSION||' AS VERSION_ID,
       SUBSTR(T1.PERIOD_ID, 1, 4) AS PERIOD_YEAR,
       T1.PERIOD_ID,
       '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
       T1.PROD_RND_TEAM_CODE,
       T1.PROD_RND_TEAM_CN_NAME,
	   '||V_SQL_DIM_PART||'
       T1.GROUP_CODE,
       T1.GROUP_CN_NAME,
       T1.GROUP_LEVEL,
       NVL(T2.PUR_IDX,0) + NVL(T3.MADE_IDX,0) AS COST_INDEX,
       T1.PARENT_CODE,
       T1.PARENT_CN_NAME,
       ''-1'' AS CREATED_BY,
       CURRENT_DATE AS CREATION_DATE,
       ''-1'' AS LAST_UPDATED_BY,
       CURRENT_DATE AS LAST_UPDATE_DATE,
       ''N'' AS DEL_FLAG,
       T1.VIEW_FLAG,
       T1.CALIBER_FLAG,
       T1.OVERSEA_FLAG,
       T1.LV0_PROD_LIST_CODE,
       T1.LV0_PROD_LIST_CN_NAME
  FROM FULL_DIM T1
  LEFT JOIN PUR_IDX T2
      ON T1.PERIOD_ID || T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
       T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
       T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
       T2.PERIOD_ID || T2.PROD_RND_TEAM_CODE || T2.GROUP_CODE ||
       T2.GROUP_LEVEL || T2.PARENT_CODE || T2.VIEW_FLAG || T2.CALIBER_FLAG ||
       T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE '||V_JOIN_SQL1||'
  LEFT JOIN MADE_IDX T3
    ON T1.PERIOD_ID || T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
       T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
       T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
       T3.PERIOD_ID || T3.PROD_RND_TEAM_CODE || T3.GROUP_CODE ||
       T3.GROUP_LEVEL || T3.PARENT_CODE || T3.VIEW_FLAG || T3.CALIBER_FLAG ||
       T3.OVERSEA_FLAG || T3.LV0_PROD_LIST_CODE '||V_JOIN_SQL2||'
';
  DBMS_OUTPUT.PUT_LINE(V_SQL);
 EXECUTE V_SQL;
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_FORMULA_SQL_TXT => V_SQL,
   F_CAL_LOG_DESC => V_TO_IDX_TABLE||'指数表插数成功',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
/*同环比删数*/
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL:= 'DELETE FROM '||V_TO_YOY_TABLE||' WHERE VERSION_ID = '||V_VERSION;
   EXECUTE V_SQL;
   
     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '同环比表删数成功',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  
/*同环比插数*/
  V_STEP_NUM := V_STEP_NUM + 1;
    DBMS_OUTPUT.PUT_LINE('同环比跑数');
	V_SQL:='
	WITH LEV_INDEX AS
	 (SELECT VIEW_FLAG,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_DIM_PART||'
			 PERIOD_YEAR,
			 PERIOD_ID,
			 SUBSTR(PERIOD_ID, 5, 2) AS MONTH_DAY,
			 PARENT_CODE,
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 COST_INDEX,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM '||V_TO_IDX_TABLE||'
	   WHERE VERSION_ID = '||V_VERSION||'
		 AND BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'),
     
	BASE_YOY AS
	 (SELECT VIEW_FLAG,
			 PERIOD_YEAR,
			 PERIOD_ID,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_DIM_PART||'
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 COST_INDEX,
			 LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PARTITION_PART||' 
																									CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									GROUP_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_PERIOD_ID,
			 LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PARTITION_PART||' 
																									 CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									 GROUP_CODE, MONTH_DAY ORDER BY PERIOD_ID) AS YOY_COST_INDEX,
			 LAG(PERIOD_ID, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PARTITION_PART||' 
																									CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									GROUP_CODE ORDER BY PERIOD_ID) AS POP_PERIOD_ID,
			 LAG(COST_INDEX, 1, NULL) OVER(PARTITION BY CALIBER_FLAG,VIEW_FLAG, PROD_RND_TEAM_CODE, '||V_PARTITION_PART||' 
																									 CALIBER_FLAG,OVERSEA_FLAG,LV0_PROD_LIST_CODE,LV0_PROD_LIST_CN_NAME,GROUP_LEVEL,
																									 GROUP_CODE ORDER BY PERIOD_ID) AS POP_COST_INDEX,
			 PARENT_CODE,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM LEV_INDEX )	
    
	INSERT INTO '||V_TO_YOY_TABLE||'
	  (VERSION_ID,
	   PERIOD_YEAR,
	   PERIOD_ID,
	   BASE_PERIOD_ID,
	   PROD_RND_TEAM_CODE,
	   PROD_RND_TEAM_CN_NAME,
	   '||V_DIM_PART||'
	   GROUP_CODE,
	   GROUP_CN_NAME,
	   GROUP_LEVEL,
	   RATE,
	   RATE_FLAG,
	   PARENT_CODE,
	   CREATED_BY,
	   CREATION_DATE,
	   LAST_UPDATED_BY,
	   LAST_UPDATE_DATE,
	   DEL_FLAG,
	   VIEW_FLAG,
	   CALIBER_FLAG,
	   OVERSEA_FLAG,  
	   LV0_PROD_LIST_CODE, 
	   LV0_PROD_LIST_CN_NAME)
	  SELECT 
	  '||V_VERSION||' AS VERSION_ID,
			 PERIOD_YEAR,
			 PERIOD_ID,
			 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_DIM_PART||'
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 ((COST_INDEX / NULLIF(YOY_COST_INDEX,0)) - 1) AS RATE,
			 ''YOY'' AS RATE_FLAG,
			 PARENT_CODE,
			 ''-1'' AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 ''-1'' AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 ''N'' AS DEL_FLAG,
			 VIEW_FLAG,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM BASE_YOY  
	   WHERE YOY_COST_INDEX IS NOT NULL
	  UNION ALL
	  SELECT 
	      '||V_VERSION||' AS VERSION_ID,
			 PERIOD_YEAR,
			 PERIOD_ID,
			 '||V_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
			 PROD_RND_TEAM_CODE,
			 PROD_RND_TEAM_CN_NAME,
			 '||V_DIM_PART||'
			 GROUP_CODE,
			 GROUP_CN_NAME,
			 GROUP_LEVEL,
			 ((COST_INDEX / NULLIF(POP_COST_INDEX,0)) - 1) AS RATE,
			 ''POP'' AS RATE_FLAG,
			 PARENT_CODE,
			 ''-1'' AS CREATED_BY,
			 CURRENT_TIMESTAMP AS CREATION_DATE,
			 ''-1'' AS LAST_UPDATED_BY,
			 CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			 ''N'' AS DEL_FLAG,
			 VIEW_FLAG,
			 CALIBER_FLAG,
			 OVERSEA_FLAG,  
			 LV0_PROD_LIST_CODE, 
			 LV0_PROD_LIST_CN_NAME
		FROM BASE_YOY
	   WHERE POP_COST_INDEX IS NOT NULL;';
dbms_output.put_line(V_SQL);
 EXECUTE V_SQL;
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_FORMULA_SQL_TXT => V_SQL,
   F_CAL_LOG_DESC => V_TO_YOY_TABLE||'同环比插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  
	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 



$$
/

