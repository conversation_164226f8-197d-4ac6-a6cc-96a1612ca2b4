-- Name: f_dm_foc_reverse_annual_weight; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_reverse_annual_weight(f_industry_flag character varying, f_keystr character varying, f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2023-09-14
  创建人  ：唐钦
  背景描述：通用颗粒度下，反向的3个视角，年度分析页面：单年权重、涨跌幅、状态码逻辑计算
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REVERSE_ANNUAL()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_REVERSE_ANNUAL_WEIGHT'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_KEYSTR  varchar2(50) := F_KEYSTR;  -- 密钥入参
  V_FROM_TABLE varchar2(200) ;
  V_SQL_CONDITION TEXT;   -- 筛选条件SQL
  V_SQL TEXT; --执行语句
  V_YEAR BIGINT := YEAR(CURRENT_TIMESTAMP);   -- 当前年份
  V_LV1_PROD_RND_TEAM VARCHAR(300);
  V_LV2_PROD_RND_TEAM VARCHAR(300);
  V_LV3_PROD_RND_TEAM VARCHAR(300);
  V_LV4_PROD_RND_TEAM VARCHAR(300);   -- 202407新增IAS-LV4层级
  V_CEG_TOTAL TEXT;
  V_MODL_TOTAL TEXT;
  V_CATG_TOTAL TEXT;
  V_MODL_TOTAL_BAK TEXT;
  V_CATG_TOTAL_BAK TEXT;
  V_GROUP_TOTAL TEXT;
  V_PUR_TOTAL TEXT;
  V_PARENT_CODE TEXT;
  V_SQL_DECRYPT TEXT;
  V_PARENT_AMT TEXT;
  V_IN_MODL_TOTAL TEXT;
  V_IN_CATG_TOTAL TEXT;
  V_VIEW_NUM BIGINT;
  
  -- 202405版本新增
  V_VERSION_TABLE varchar(100);
  V_TO_TABLE varchar(100);
  -- 202407版本新增
  V_VIEW_FIXED BIGINT;   -- 固定视角值
  V_BEGIN_NUM INT;
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
 -- 判断产业项目为ICT还是数字能源
  IF F_INDUSTRY_FLAG = 'I' THEN   -- ICT  
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_ITEM_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_REV_ANNUAL_WEIGHT_T';
  ELSIF F_INDUSTRY_FLAG = 'E' THEN   -- 202405版本新增数字能源 
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_ITEM_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_REV_ANNUAL_WEIGHT_T';
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_ITEM_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_REV_ANNUAL_WEIGHT_T';
  END IF;
   
  --版本号入参判断，当入参为空，取TOP规格品清单最新版本号
  IF F_VERSION_ID IS NULL THEN
     V_SQL := '
       SELECT VERSION_ID 
       FROM
           '||V_VERSION_TABLE||'
       WHERE
           DEL_FLAG = ''N''
           AND STATUS = 1
           AND UPPER(DATA_TYPE) = ''CATEGORY''
           ORDER BY LAST_UPDATE_DATE DESC
           LIMIT 1';
    EXECUTE IMMEDIATE V_SQL INTO V_VERSION_ID;
  --入参不为空，则以入参为版本号
  ELSE 
    V_VERSION_ID := F_VERSION_ID;
  END IF;
    
  --1.删除目标表数据:

     EXECUTE IMMEDIATE 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID;
     
--1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除产业标识为：'||F_INDUSTRY_FLAG||'，版本号为：'||V_VERSION_ID||'的'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 创建临时表
  DROP TABLE IF EXISTS DECRYP_AMT_TMP;
  CREATE TEMPORARY TABLE DECRYP_AMT_TMP(
        VERSION_ID BIGINT,
        VIEW_FLAG VARCHAR(2),
        PERIOD_YEAR BIGINT,
        PUR_CODE VARCHAR(50),
        PUR_CN_NAME VARCHAR(500),
        GROUP_CODE VARCHAR(50),
        GROUP_CN_NAME VARCHAR(2000),
        GROUP_LEVEL VARCHAR(50),
        PARENT_CODE VARCHAR(50),
        LV0_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV0_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV1_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV1_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV2_PROD_RND_TEAM_CODE    VARCHAR(50),
        LV2_PROD_RD_TEAM_CN_NAME    VARCHAR(500),
        LV3_PROD_RND_TEAM_CODE VARCHAR(50),
        LV3_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        LV4_PROD_RND_TEAM_CODE VARCHAR(50),
        LV4_PROD_RD_TEAM_CN_NAME VARCHAR(500),
        L3_CEG_CODE    VARCHAR(50),
        L3_CEG_SHORT_CN_NAME    VARCHAR(500),
        L4_CEG_CODE    VARCHAR(50),
        L4_CEG_SHORT_CN_NAME    VARCHAR(500),
        CATEGORY_CODE VARCHAR(50),
        CATEGORY_CN_NAME VARCHAR(500),
        ITEM_CODE VARCHAR(50),
        ITEM_CN_NAME VARCHAR(500),
        RMB_COST_AMT NUMERIC,
        CALIBER_FLAG VARCHAR(2),
        OVERSEA_FLAG VARCHAR(2),
        LV0_PROD_LIST_CODE VARCHAR(50),
        LV0_PROD_LIST_CN_NAME VARCHAR(500)
    )
    ON COMMIT PRESERVE ROWS
    DISTRIBUTE BY HASH(GROUP_CODE,PARENT_CODE);
    DBMS_OUTPUT.PUT_LINE( '解密临时表创建成功' );
        
  -- 定义变量
    V_LV1_PROD_RND_TEAM := 'LV1_PROD_RND_TEAM_CODE ,
                            LV1_PROD_RD_TEAM_CN_NAME ,';
    V_LV2_PROD_RND_TEAM := 'LV2_PROD_RND_TEAM_CODE ,
                            LV2_PROD_RD_TEAM_CN_NAME ,';
    V_CEG_TOTAL := 'L3_CEG_CODE ,
                    L3_CEG_SHORT_CN_NAME ,';
    V_MODL_TOTAL := 'L4_CEG_CODE ,
                     L4_CEG_SHORT_CN_NAME ,';
    V_CATG_TOTAL := 'CATEGORY_CODE,
                     CATEGORY_CN_NAME,';    
    V_MODL_TOTAL_BAK := V_MODL_TOTAL;            
    V_CATG_TOTAL_BAK := V_CATG_TOTAL;    
    V_IN_CATG_TOTAL := 'CATEGORY_CODE,
                        CATEGORY_CN_NAME,';
    V_IN_MODL_TOTAL := 'L4_CEG_CODE,
                        L4_CEG_SHORT_CN_NAME,
                        '; 
                                                                                           
  IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
     V_BEGIN_NUM := 1;   -- 最细粒度-LV4层级
     V_VIEW_FIXED := 7;   -- 最细粒度视角
     V_LV4_PROD_RND_TEAM := 'LV4_PROD_RND_TEAM_CODE ,
                             LV4_PROD_RD_TEAM_CN_NAME ,';
     V_PARENT_CODE := 'LV4_PROD_RND_TEAM_CODE AS PARENT_CODE ,';
  ELSE   -- ICT/数字能源
     V_BEGIN_NUM := 2;   -- 最细粒度-LV3层级
     V_VIEW_FIXED := 3;
     V_PARENT_CODE := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE ,';
  END IF;

    FOR VIEW_NUM IN 4 .. 6 LOOP
    -- 将视角4：view_flag = 3的ITEM层级的总金额数据取出来存放到会话级临时表
        IF VIEW_NUM = 4 THEN 
           V_VIEW_NUM := 6;
           V_PUR_TOTAL := 'CATEGORY_CODE AS PUR_CODE,
                           CATEGORY_CN_NAME AS PUR_CN_NAME,';            
           V_SQL_DECRYPT := 'GS_DECRYPT(RMB_COST_AMT, '''||V_KEYSTR||''', ''AES128'', ''CBC'', ''SHA256'') AS ';
           V_SQL_CONDITION := 'AND ONLY_ITEM_FLAG = ''N''
                               AND REVIEW_ITEM_FLAG = 0  -- 取非底层数据审视范围的数据
                               AND VIEW_FLAG = '''||V_VIEW_FIXED||'''';
        ELSIF VIEW_NUM = 5 THEN 
           V_VIEW_NUM := 5;
           V_CATG_TOTAL_BAK := '';    
           V_PUR_TOTAL := 'L4_CEG_CODE AS PUR_CODE,
                           L4_CEG_SHORT_CN_NAME AS PUR_CN_NAME,';
           V_FROM_TABLE := 'DECRYP_AMT_TMP';            
           V_SQL_DECRYPT := '';
           V_SQL_CONDITION := 'AND VIEW_FLAG = ''6''';
        ELSIF VIEW_NUM = 6 THEN 
           V_VIEW_NUM := 4;
           V_MODL_TOTAL_BAK := '';            
           V_CATG_TOTAL_BAK := '';
           V_PUR_TOTAL := 'L3_CEG_CODE AS PUR_CODE,
                           L3_CEG_SHORT_CN_NAME AS PUR_CN_NAME,';
           V_FROM_TABLE := 'DECRYP_AMT_TMP';    
           V_SQL_DECRYPT := '';
        END IF;                                     
    V_SQL := '
    INSERT INTO DECRYP_AMT_TMP(
           VERSION_ID,
           VIEW_FLAG,
           PERIOD_YEAR ,
           PUR_CODE ,
           PUR_CN_NAME ,
           GROUP_CODE ,
           GROUP_CN_NAME ,
           GROUP_LEVEL ,
           PARENT_CODE ,
           LV0_PROD_RND_TEAM_CODE ,
           LV0_PROD_RD_TEAM_CN_NAME ,
           LV1_PROD_RND_TEAM_CODE ,
           LV1_PROD_RD_TEAM_CN_NAME ,
           LV2_PROD_RND_TEAM_CODE ,
           LV2_PROD_RD_TEAM_CN_NAME ,
           LV3_PROD_RND_TEAM_CODE ,
           LV3_PROD_RD_TEAM_CN_NAME ,
           '||V_LV4_PROD_RND_TEAM||'
           L3_CEG_CODE ,
           L3_CEG_SHORT_CN_NAME ,
           '||V_MODL_TOTAL_BAK
           ||V_CATG_TOTAL_BAK||'
           ITEM_CODE,
           ITEM_CN_NAME,
           RMB_COST_AMT ,
           CALIBER_FLAG ,
           OVERSEA_FLAG ,
           LV0_PROD_LIST_CODE ,
           LV0_PROD_LIST_CN_NAME )
    SELECT VERSION_ID ,
           '''||V_VIEW_NUM||''' AS VIEW_FLAG,
           PERIOD_YEAR ,
           '||V_PUR_TOTAL||'
           ITEM_CODE AS GROUP_CODE ,
           ITEM_CN_NAME AS GROUP_CN_NAME ,
           ''ITEM'' AS GROUP_LEVEL ,
           '||V_PARENT_CODE||'
           LV0_PROD_RND_TEAM_CODE ,
           LV0_PROD_RD_TEAM_CN_NAME ,
           LV1_PROD_RND_TEAM_CODE ,
           LV1_PROD_RD_TEAM_CN_NAME ,
           LV2_PROD_RND_TEAM_CODE ,
           LV2_PROD_RD_TEAM_CN_NAME ,
           LV3_PROD_RND_TEAM_CODE ,
           LV3_PROD_RD_TEAM_CN_NAME ,
           '||V_LV4_PROD_RND_TEAM||'
           L3_CEG_CODE ,
           L3_CEG_SHORT_CN_NAME ,
           '||V_MODL_TOTAL_BAK
           ||V_CATG_TOTAL_BAK||'
           ITEM_CODE,
           ITEM_CN_NAME,
           '||V_SQL_DECRYPT||'RMB_COST_AMT ,    -- 解密
           CALIBER_FLAG ,
           OVERSEA_FLAG ,
           LV0_PROD_LIST_CODE ,
           LV0_PROD_LIST_CN_NAME     
        FROM '||V_FROM_TABLE||' 
        WHERE VERSION_ID = '||V_VERSION_ID||' 
        '||V_SQL_CONDITION;
    
    DBMS_OUTPUT.PUT_LINE(V_SQL);
    EXECUTE IMMEDIATE V_SQL; 
    DBMS_OUTPUT.PUT_LINE( 'VIEW_FLAG='||V_VIEW_FIXED||'的总金额数据已解密完成，并插入临时表视角为：'||VIEW_NUM );
    END LOOP;
    
    -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '临时表创建成功，并将ITEM层级总金额数据解密完成，并按4/5/6，3个视角插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
     
  -- 重定义变量
    V_PUR_TOTAL := 'CASE WHEN VIEW_FLAG = ''4'' THEN L3_CEG_CODE
                         WHEN VIEW_FLAG = ''5'' THEN L4_CEG_CODE
                         WHEN VIEW_FLAG = ''6'' THEN CATEGORY_CODE
                    END AS PUR_CODE,
                    CASE WHEN VIEW_FLAG = ''4'' THEN L3_CEG_SHORT_CN_NAME
                         WHEN VIEW_FLAG = ''5'' THEN L4_CEG_SHORT_CN_NAME
                         WHEN VIEW_FLAG = ''6'' THEN CATEGORY_CN_NAME
                    END AS PUR_CN_NAME,
                                      ';
    V_LV3_PROD_RND_TEAM := 'LV3_PROD_RND_TEAM_CODE ,
                            LV3_PROD_RD_TEAM_CN_NAME ,';
    V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''ITEM'' ';
 -- 分层级将数据插入临时表
 FOR LEVEL_NUM IN V_BEGIN_NUM .. 8 LOOP
     IF LEVEL_NUM = 1 THEN -- LV4层级
        V_GROUP_TOTAL := 'LV4_PROD_RND_TEAM_CODE AS GROUP_CODE,
                          LV4_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,    
                         ''LV4'' AS GROUP_LEVEL,
                                            ';
        V_PARENT_CODE := 'LV3_PROD_RND_TEAM_CODE AS PARENT_CODE,
                         ';                                            
     ELSIF LEVEL_NUM = 2 THEN -- LV3层级
        V_GROUP_TOTAL := 'LV3_PROD_RND_TEAM_CODE AS GROUP_CODE,
                          LV3_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,    
                         ''LV3'' AS GROUP_LEVEL,
                                            ';
        V_PARENT_CODE := 'LV2_PROD_RND_TEAM_CODE AS PARENT_CODE,
                         ';
        V_LV4_PROD_RND_TEAM := '';
        IF F_INDUSTRY_FLAG = 'IAS' THEN   -- 202407新增IAS
           V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''LV4'' ';
        ELSE   -- ICT/数字能源
            V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''ITEM'' ';
        END IF;
     ELSIF LEVEL_NUM = 3 THEN -- LV2层级  
        V_GROUP_TOTAL := 'LV2_PROD_RND_TEAM_CODE AS GROUP_CODE,
                          LV2_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,    
                         ''LV2'' AS GROUP_LEVEL,
                         ';
        V_PARENT_CODE := 'LV1_PROD_RND_TEAM_CODE AS PARENT_CODE,
                         ';        
        V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''LV3'' ';    
        V_LV3_PROD_RND_TEAM := '';      
     ELSIF LEVEL_NUM = 4 THEN -- LV1层级  
        V_GROUP_TOTAL := 'LV1_PROD_RND_TEAM_CODE AS GROUP_CODE,
                          LV1_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,    
                         ''LV1'' AS GROUP_LEVEL,
                                                    ';
        V_PARENT_CODE := 'CASE WHEN VIEW_FLAG = ''4'' THEN L3_CEG_CODE
                               WHEN VIEW_FLAG = ''5'' THEN L4_CEG_CODE
                               WHEN VIEW_FLAG = ''6'' THEN CATEGORY_CODE
                          END AS PARENT_CODE,
                                        ';        
        V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''LV2'' ';    
        V_CATG_TOTAL := 'CASE WHEN VIEW_FLAG = ''6'' THEN CATEGORY_CODE
                         ELSE '''' END AS CATEGORY_CODE,
                         CASE WHEN VIEW_FLAG = ''6'' THEN CATEGORY_CN_NAME
                         ELSE '''' END AS CATEGORY_CN_NAME,
                                         ';    
        V_MODL_TOTAL := 'CASE WHEN VIEW_FLAG = ''4'' THEN ''''
                         ELSE L4_CEG_CODE END AS L4_CEG_CODE,
                         CASE WHEN VIEW_FLAG = ''4'' THEN ''''
                         ELSE L4_CEG_SHORT_CN_NAME END AS L4_CEG_SHORT_CN_NAME,
                                                 ';                                     
        V_LV2_PROD_RND_TEAM := '';
     ELSIF LEVEL_NUM = 5 THEN -- 品类层级  
        V_PUR_TOTAL := 'CATEGORY_CODE AS PUR_CODE,
                        CATEGORY_CN_NAME AS PUR_CN_NAME,
                       ';
        V_GROUP_TOTAL := 'CATEGORY_CODE AS GROUP_CODE,
                          CATEGORY_CN_NAME AS GROUP_CN_NAME,    
                         ''CATEGORY'' AS GROUP_LEVEL,
                         ';
        V_PARENT_CODE := 'L4_CEG_CODE AS PARENT_CODE,
                         ';        
        V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''LV1''
                            AND VIEW_FLAG = ''6''    ';        
        V_LV1_PROD_RND_TEAM := '';                                       
     ELSIF LEVEL_NUM = 6 THEN -- 模块层级  
        V_PUR_TOTAL := 'L4_CEG_CODE AS PUR_CODE,
                        L4_CEG_SHORT_CN_NAME AS PUR_CN_NAME,
                       ';
        V_GROUP_TOTAL := 'L4_CEG_CODE AS GROUP_CODE,
                          L4_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,    
                         ''MODL'' AS GROUP_LEVEL,
                         ';
        V_PARENT_CODE := 'L3_CEG_CODE AS PARENT_CODE,
                         ';        
        V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''LV1''
                           AND VIEW_FLAG IN (''6'',''5'')    ';                     
        V_CATG_TOTAL := '';    
        V_IN_CATG_TOTAL := '';    
     ELSIF LEVEL_NUM = 7 THEN -- 专家团层级  
        V_PUR_TOTAL := 'L3_CEG_CODE AS PUR_CODE,
                        L3_CEG_SHORT_CN_NAME AS PUR_CN_NAME,
                        ';
        V_GROUP_TOTAL := 'L3_CEG_CODE AS GROUP_CODE,
                          L3_CEG_SHORT_CN_NAME AS GROUP_CN_NAME,    
                         ''CEG'' AS GROUP_LEVEL,
                         ';
        V_PARENT_CODE := 'LV0_PROD_RND_TEAM_CODE    AS PARENT_CODE,
                         ';        
        V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''LV1''
                         ';        
        V_MODL_TOTAL := '';    
        V_IN_MODL_TOTAL := '';    
     ELSIF LEVEL_NUM = 8 THEN -- ICT层级  
        V_PUR_TOTAL := 'LV0_PROD_RND_TEAM_CODE AS PUR_CODE,
                        LV0_PROD_RD_TEAM_CN_NAME AS PUR_CN_NAME,
                        ';
        V_GROUP_TOTAL := 'LV0_PROD_RND_TEAM_CODE AS GROUP_CODE,
                          LV0_PROD_RD_TEAM_CN_NAME AS GROUP_CN_NAME,    
                         ''LV0'' AS GROUP_LEVEL,
                         ';
         V_PARENT_CODE := 'LV0_PROD_RND_TEAM_CODE    AS PARENT_CODE,
                          ';        
         V_SQL_CONDITION := 'WHERE GROUP_LEVEL = ''CEG''
                          ';
         V_CEG_TOTAL := '';                   
     END IF;                                             
    
    V_SQL := '
      INSERT INTO DECRYP_AMT_TMP(
                  VERSION_ID,
                  VIEW_FLAG,
                  PERIOD_YEAR ,
                  PUR_CODE ,
                  PUR_CN_NAME ,
                  GROUP_CODE ,
                  GROUP_CN_NAME ,
                  GROUP_LEVEL ,
                  PARENT_CODE ,
                  LV0_PROD_RND_TEAM_CODE ,
                  LV0_PROD_RD_TEAM_CN_NAME ,
                  '||V_LV1_PROD_RND_TEAM 
                  ||V_LV2_PROD_RND_TEAM
                  ||V_LV3_PROD_RND_TEAM
                  ||V_CEG_TOTAL
                  ||V_IN_MODL_TOTAL
                  ||V_IN_CATG_TOTAL||'
                  RMB_COST_AMT ,
                  CALIBER_FLAG ,
                  OVERSEA_FLAG ,
                  LV0_PROD_LIST_CODE ,
                  LV0_PROD_LIST_CN_NAME )
            SELECT VERSION_ID,
                   VIEW_FLAG,
                   PERIOD_YEAR ,
                   '||V_PUR_TOTAL
                   ||V_GROUP_TOTAL
                   ||V_PARENT_CODE
                   ||'LV0_PROD_RND_TEAM_CODE ,
                   LV0_PROD_RD_TEAM_CN_NAME ,
                   '||V_LV1_PROD_RND_TEAM 
                   ||V_LV2_PROD_RND_TEAM
                   ||V_LV3_PROD_RND_TEAM
                   ||V_CEG_TOTAL
                   ||V_MODL_TOTAL
                   ||V_CATG_TOTAL||'
                   SUM(RMB_COST_AMT) AS RMB_COST_AMT,
                   CALIBER_FLAG ,
                   OVERSEA_FLAG ,
                   LV0_PROD_LIST_CODE ,
                   LV0_PROD_LIST_CN_NAME
                FROM DECRYP_AMT_TMP  
                '||V_SQL_CONDITION||'
                AND RMB_COST_AMT <> 0    -- 剔除金额为0的数据（ITEM层级除外）
                GROUP BY VERSION_ID,
                         VIEW_FLAG,
                         PERIOD_YEAR ,
                         LV0_PROD_RND_TEAM_CODE ,
                         LV0_PROD_RD_TEAM_CN_NAME ,
                         '||V_LV1_PROD_RND_TEAM 
                         ||V_LV2_PROD_RND_TEAM
                         ||V_LV3_PROD_RND_TEAM
                         ||V_LV4_PROD_RND_TEAM
                         ||V_CEG_TOTAL
                         ||V_IN_MODL_TOTAL
                         ||V_IN_CATG_TOTAL||'
                         CALIBER_FLAG ,
                         OVERSEA_FLAG ,
                         LV0_PROD_LIST_CODE ,
                         LV0_PROD_LIST_CN_NAME';

     DBMS_OUTPUT.PUT_LINE(V_SQL);
     EXECUTE IMMEDIATE V_SQL;      
     DBMS_OUTPUT.PUT_LINE('第'||LEVEL_NUM||'次循环成功');
                    
   -- 写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||LEVEL_NUM||'次循环，产业标识为：'||F_INDUSTRY_FLAG||'，对应卷积后的数据插入临时表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');                 
      END LOOP;   -- 结束循环  
            
    -- 分为2类不同层级，进行循环：1：('ITEM','LV4','LV3','LV2','LV1'）、2：（'CATEGORY','MODL','CEG','LV0'）
        FOR GRO_NUM IN 1 .. 2 LOOP
            IF GRO_NUM = 1 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.PUR_CODE,SS.GROUP_LEVEL,SS.PARENT_CODE,SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_SQL_CONDITION := ' 
                WHERE GROUP_LEVEL IN (''ITEM'',''LV4'',''LV3'',''LV2'',''LV1'')   -- 202407新增LV4层级
                AND RMB_COST_AMT <> 0 ';  
            ELSIF GRO_NUM = 2 THEN
                V_PARENT_AMT := '
                SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.GROUP_LEVEL,SS.PARENT_CODE,SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE) AS PARENT_AMT, ';
                V_SQL_CONDITION := ' 
                WHERE GROUP_LEVEL IN (''CATEGORY'',''MODL'',''CEG'',''LV0'') 
                AND RMB_COST_AMT <> 0 ';  -- 9月版本：增加模块层级
            END IF;
  
    -- 插入各视角、各层级的权重数据到反向视角单年权重表
    V_SQL := '
    INSERT INTO '||V_TO_TABLE||'(
             VERSION_ID,
             PERIOD_YEAR,
             PUR_CODE,
             PUR_CN_NAME,
             GROUP_CODE,
             GROUP_CN_NAME,
             GROUP_LEVEL,
             WEIGHT_RATE,
             ABSOLUTE_WEIGHT, 
             ABSOLUTE_PARENT_AMT,
             PARENT_CODE,
             CREATED_BY,
             CREATION_DATE,
             LAST_UPDATED_BY,
             LAST_UPDATE_DATE,
             DEL_FLAG,
             VIEW_FLAG,
             APPEND_FLAG,
             CALIBER_FLAG,
             OVERSEA_FLAG,
             LV0_PROD_LIST_CODE,
             LV0_PROD_LIST_CN_NAME
        )    
    -- 生成连续年份, 三年前的年份+当年（若当年为1月时，当年年份不含）   
  WITH PERIOD_YEAR_TMP AS(     
           SELECT CAST(GENERATE_SERIES(YEAR(CURRENT_TIMESTAMP)-3,
                              YEAR(CURRENT_TIMESTAMP),
                              1) AS BIGINT) AS PERIOD_YEAR FROM DUAL
                   )    
 
    -- 生成连续年的发散维
 , CONTIN_DIM_TMP AS(
       SELECT DISTINCT T2.PERIOD_YEAR,
              T1.PUR_CODE ,
              T1.PUR_CN_NAME ,
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              T1.PARENT_CODE,
              T1.VIEW_FLAG,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME
           FROM DECRYP_AMT_TMP T1,PERIOD_YEAR_TMP T2  
           '||V_SQL_CONDITION||'
  )
    -- 各视角下各层级的权重逻辑计算
                    SELECT '
                           ||V_VERSION_ID||' AS VERSION_ID,
                           T1.PERIOD_YEAR,
                           T1.PUR_CODE ,
                           T1.PUR_CN_NAME ,
                           T1.GROUP_CODE,
                           T1.GROUP_CN_NAME,
                           T1.GROUP_LEVEL,
                           DECODE(T2.RMB_COST_AMT, NULL, 0, T2.RMB_COST_AMT / NULLIF(T2.PARENT_AMT,0)) AS WEIGHT_RATE,  -- 相对权重值，原始有数据，则计算，维度补齐的情况，则赋0
                           DECODE(T2.RMB_COST_AMT, NULL, 0, T2.RMB_COST_AMT / NULLIF(T2.ABSOLUTE_PARENT_AMT,0)) AS ABSOLUTE_WEIGHT,  -- 绝对权重值，原始有数据，则计算，维度补齐的情况，则赋0
                           T2.ABSOLUTE_PARENT_AMT,
                           T1.PARENT_CODE,
                           -1 AS CREATED_BY,
                           CURRENT_TIMESTAMP AS CREATION_DATE,
                           -1 AS LAST_UPDATED_BY,
                           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
                           ''N'' AS DEL_FLAG,
                           T1.VIEW_FLAG,
                           DECODE(T2.RMB_COST_AMT, NULL, ''Y'',''N'') AS APPEND_FLAG,  --补齐标识：Y为补齐，N为原始
                           T1.CALIBER_FLAG,
                           T1.OVERSEA_FLAG,
                           T1.LV0_PROD_LIST_CODE,
                           T1.LV0_PROD_LIST_CN_NAME
                        FROM CONTIN_DIM_TMP T1
                        LEFT JOIN(
                                    SELECT SS.PERIOD_YEAR,
                                           SS.PUR_CODE,
                                           SS.PUR_CN_NAME,
                                           SS.GROUP_CODE,
                                           SS.GROUP_CN_NAME,
                                           SS.GROUP_LEVEL,
                                           SS.RMB_COST_AMT,
                                           '||V_PARENT_AMT||'
                                           SUM(SS.RMB_COST_AMT) OVER(PARTITION BY SS.PERIOD_YEAR,SS.VIEW_FLAG,SS.CALIBER_FLAG,SS.OVERSEA_FLAG,SS.LV0_PROD_LIST_CODE,SS.GROUP_LEVEL) AS ABSOLUTE_PARENT_AMT,
                                           SS.PARENT_CODE,
                                           SS.VIEW_FLAG,
                                           SS.CALIBER_FLAG,
                                           SS.OVERSEA_FLAG,
                                           SS.LV0_PROD_LIST_CODE,
                                           SS.LV0_PROD_LIST_CN_NAME
                                        FROM DECRYP_AMT_TMP SS
                                        '||V_SQL_CONDITION||'        
                                        ) T2
                        ON NVL(T1.PUR_CODE,''SNULL'') = NVL(T2.PUR_CODE,''SNULL'')
                        AND NVL(T1.PUR_CN_NAME,''SNULL1'') = NVL(T2.PUR_CN_NAME,''SNULL1'')
                        AND T1.GROUP_CODE = T2.GROUP_CODE
                        AND T1.GROUP_LEVEL =T2.GROUP_LEVEL
                        AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
                        AND T1.VIEW_FLAG = T2.VIEW_FLAG
                        AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
                        AND T1.PARENT_CODE = T2.PARENT_CODE
                        AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
                        AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
                        WHERE T1.PERIOD_YEAR >= YEAR(CURRENT_TIMESTAMP)-2
                                            ';
            DBMS_OUTPUT.PUT_LINE(V_SQL);      
            EXECUTE IMMEDIATE V_SQL; 
            DBMS_OUTPUT.PUT_LINE('权重计算成功');                
                                            
    --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第'||GRO_NUM||'次循环，插入产业标识为：'||F_INDUSTRY_FLAG||'，版本号为：'||V_VERSION_ID||' 的权重数据到:'||V_TO_TABLE||'表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
    END LOOP;             
    
  ANALYSE FIN_DM_OPT_FOI.DM_FOC_REV_ANNUAL_WEIGHT_T;
  
  --日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集统计信息完成!');
 
  RETURN 'SUCCESS';
 
 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );
  
END$$
/

