-- Name: f_dm_foi_fcst_sum_t_bak; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foi_fcst_sum_t_bak(OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$                
  /*
  创建时间：2023-06-19
  创建人  ：唐钦
  背景描述：全品类item预测汇总表,然后调用该函数将相对应的数据生成导入到目标表中
  参数描述：x_success_flag ：是否成功
  事例    ：select FIN_DM_OPT_FOI.f_dm_foi_fcst_sum_t()
  
  */
  
  -- ICT采购计划量接口    FOI_DWK_GRP_ICT_PURCH_EXPEND_I
  -- 采购预估价接口            FOI_DWK_GRP_PROCOST_PRICE_REL_I
  
  DECLARE V_SP_NAME VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOI_FCST_SUM_T';
  V_DML_ROW_COUNT    NUMBER DEFAULT 0;
  V_PERIOD_BEGIN     BIGINT := CAST(TO_CHAR(CURRENT_TIMESTAMP, 'YYYYMM') AS BIGINT);  -- 开始月份（当月） 
  V_PERIOD_END       BIGINT := CAST((TO_CHAR(CURRENT_TIMESTAMP, 'YYYY') || 12) AS BIGINT);  -- 结束月份（当年12月）
  V_MAX_PURCH_PERIOD BIGINT;
  V_MAX_UNIT_PERIOD  BIGINT;
  V_STEP_NUM         BIGINT := 0;

BEGIN
  X_SUCCESS_FLAG := '1';
  SELECT CAST(MAX(SRC_PERIOD_ID) AS BIGINT)
    INTO V_MAX_PURCH_PERIOD
    FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_ICT_PURCH_EXPEND_I; -- 取计划量表最大会计期
  SELECT CAST(MAX(PERIOD_ID) AS BIGINT)
    INTO V_MAX_UNIT_PERIOD
    FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I; -- 取预估价表最大会计期

  --0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => v_sp_name,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => v_sp_name || '开始执行');

  -- 支持重跑，清除目标表要插入预测数据
  TRUNCATE TABLE FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T;

  --1.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => 1,
   F_CAL_LOG_DESC => '清空FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');

  -- 插入目标表数据

  -- 取出所有存在于历史数下单&到货明细表里的item_code字段及关联关系
  WITH OPT_DIM_HIS_TMP AS
   (SELECT ITEM_CODE, -- ITEM编码
           ITEM_NAME, -- ITEM中文名称
           CATEGORY_CODE, -- 品类编码
           CATEGORY_NAME, -- 品类中文名称
           L4_CEG_CODE, -- 模块编码
           L4_CEG_SHORT_CN_NAME, -- 模块（GROUP LV4简称）
           L4_CEG_CN_NAME, -- 模块中文名称
           L3_CEG_CODE, -- 专家团编码
           L3_CEG_SHORT_CN_NAME, -- 专家团（GROUP LV3简称）
           L3_CEG_CN_NAME, -- 专家团中文名称
           L2_CEG_CODE, -- 采购组织编码
           L2_CEG_CN_NAME -- 采购组织中文名称
      FROM FIN_DM_OPT_FOI.DM_DIM_FOI_ITEM_CATG_MODL_CEG_T -- 维度关联表数据
      WHERE ITEM_CODE IN
           (SELECT DISTINCT ITEM_CODE
               FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T))
              
  -- 取出满足取数逻辑的计划量数据
  ,
  FIN_DM_OPT_FOI_ITEM_QTY_TMP AS
   (SELECT I.PURCH_ARRIVE_MON, -- 采购到货月份
           I.SRC_ITEM_CODE, -- ITEM编码
           I.ITEM_SUBTYPE_CODE, -- 品类编码
           I.ITEM_SUBTYPE_CN_NAME, -- 品类中文名称
           SUM(I.PURCH_QTY) AS PURCH_QTY -- 采购数量卷积至ITEM层级
      FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_ICT_PURCH_EXPEND_I I -- ICT采购计划量接口表
     INNER JOIN OPT_DIM_HIS_TMP DIM
        ON I.SRC_ITEM_CODE = DIM.ITEM_CODE
     WHERE I.SRC_PERIOD_ID = V_MAX_PURCH_PERIOD -- 取最大会计期
       AND I.PURCH_ARRIVE_MON BETWEEN V_PERIOD_BEGIN AND V_PERIOD_END -- 取规定月份的数据
       AND I.SRC_ITEM_CODE IS NOT NULL
       AND I.ITEM_SUBTYPE_CODE IS NOT NULL
       AND I.DEL_FLAG = 'N'
     GROUP BY I.PURCH_ARRIVE_MON,
              I.SRC_ITEM_CODE,
              I.ITEM_SUBTYPE_CODE,
              I.ITEM_SUBTYPE_CN_NAME)

  -- 取出满足取数逻辑的所有item的预估价数据
,
  FIN_DM_OPT_FOI_ITEM_UNIT_TMP AS
   (SELECT I.FCST_PERIOD_ID, 
           I.ITEM_CODE, 
           I.RMB_UNIT_PRICE, 
           I.CURRENCY_CODE
      FROM FIN_DM_OPT_FOI.FOI_DWK_GRP_PROCOST_PRICE_REL_I I -- 采购预估价接口表
     INNER JOIN OPT_DIM_HIS_TMP DIM
        ON I.ITEM_CODE = DIM.ITEM_CODE
     WHERE I.FCST_PERIOD_ID BETWEEN V_PERIOD_BEGIN AND V_PERIOD_END
       AND I.PERIOD_ID = V_MAX_UNIT_PERIOD -- 取预估价表的最大会计期 
       AND I.ITEM_CODE IS NOT NULL
    )
  -- 取补齐的上一个月的历史数数据
  ,
  OPT_HIS_MAX_PERIOD_TMP AS
    (SELECT PERIOD_ID,                                  -- 会计期
            ITEM_CODE,                                  -- ITEM编码        
            AVG_PRICE_CNY                               -- 均价
         FROM FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_APPEND_T       -- 历史数补录数据明细表
         WHERE PERIOD_ID = CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP, -1),'YYYYMM') AS BIGINT)
    )
  
  -- 取出所有满足item_code能与维度关联表的item_code匹配的计划量和单价,并根据有量无价的补齐规则对数据进行补齐
  ,
  OPT_ITEM_QTY_UNIT_TMP AS
       (SELECT NVL(T1.PURCH_ARRIVE_MON,T2.FCST_PERIOD_ID) AS PERIOD_ID,                 -- 采购到货月份
               NVL(T1.SRC_ITEM_CODE,T2.ITEM_CODE) AS ITEM_CODE,                         -- ITEM编码
               T3.ITEM_NAME,                                                            -- ITEM中文名称
               T3.CATEGORY_CODE,                                                        -- 品类编码
               T3.CATEGORY_NAME,                                                        -- 品类中文名称
               T3.L4_CEG_CODE,                                                          -- 模块编码
               T3.L4_CEG_SHORT_CN_NAME,                                                 -- 模块（GROUP LV4简称）
               T3.L4_CEG_CN_NAME,                                                       -- 模块中文名称
               T3.L3_CEG_CODE,                                                          -- 专家团编码
               T3.L3_CEG_SHORT_CN_NAME,                                                 -- 专家团（GROUP LV3简称）
               T3.L3_CEG_CN_NAME,                                                       -- 专家团中文名称
               T3.L2_CEG_CODE,                                                          -- 采购组织编码
               T3.L2_CEG_CN_NAME,                                                       -- 采购组织中文名称
               NVL(T1.PURCH_QTY,0) AS PURCH_QTY,                                        -- 当计划量有数据的时候，取计划量数据，否则取0
               CASE WHEN NVL(T1.PURCH_QTY,0) > 0 AND NVL(T2.RMB_UNIT_PRICE,0) = 0       -- 取上个月实际数的均价作为预估价
                 THEN T4.AVG_PRICE_CNY 
               ELSE T2.RMB_UNIT_PRICE END AS RMB_UNIT_PRICE                             -- 当预估价有数据的时候，取预估价数据，否则取0
           FROM FIN_DM_OPT_FOI_ITEM_QTY_TMP T1                                                 
           FULL JOIN FIN_DM_OPT_FOI_ITEM_UNIT_TMP T2                                          --  计划量数据和预估价数据全外关联       
           ON T1.SRC_ITEM_CODE = T2.ITEM_CODE
           AND T1.PURCH_ARRIVE_MON = T2.FCST_PERIOD_ID
           INNER JOIN OPT_DIM_HIS_TMP T3
           ON T3.ITEM_CODE = T1.SRC_ITEM_CODE
           LEFT JOIN OPT_HIS_MAX_PERIOD_TMP T4                                          -- 历史数下单&到货明细表临时表
           ON T4.ITEM_CODE  = T1.SRC_ITEM_CODE)
  
  -- 插入数据到表中
  INSERT INTO FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T
   (ID, -- 主键
    YEAR, -- 年份
    PERIOD_ID, -- 会计期（'YYYYMM'）
    ITEM_CODE, -- ITEM编码
    ITEM_NAME, -- ITEM中文名称
    CATEGORY_CODE, -- 品类编码
    CATEGORY_NAME, -- 品类中文名称
    L4_CEG_CODE, -- 模块编码
    L4_CEG_SHORT_CN_NAME, -- 模块（GROUP LV4简称）
    L4_CEG_CN_NAME, -- 模块中文名称
    L3_CEG_CODE, -- 专家团编码
    L3_CEG_SHORT_CN_NAME, -- 专家团（GROUP LV3简称）
    L3_CEG_CN_NAME, -- 专家团中文名称
    L2_CEG_CODE, -- 生产采购编码
    L2_CEG_CN_NAME, -- 生产采购中文名称
    RECEIVE_QTY, -- 到货总数量
    RECEIVE_AMT_USD, -- 到货总金额(USD)
    RECEIVE_AMT_CNY, -- 到货总金额(CNY)         
    CREATED_BY, -- 创建人
    CREATION_DATE, -- 创建时间
    LAST_UPDATED_BY, -- 修改人
    LAST_UPDATE_DATE, -- 修改时间
    DEL_FLAG, -- 删除标识(未删除：N，已删除：Y)
    APPEND_FLAG, -- 是否补录(Y:是补录数据、N：真实数据)
    AVG_PRICE_CNY, -- 到货均价(CNY)
    AVG_PRICE_USD -- 到货均价(USD)
    )
  
  -- 取出全品类下所有ITEM，有价有量，有量无价的总数量和总金额，以及有价无量的均价
  SELECT FIN_DM_OPT_FOI.DM_FOI_ITEM_FCST_SUM_S.NEXTVAL,
         SUBSTR(T.PERIOD_ID, 1, 4) AS YEAR,
         T.PERIOD_ID, -- 会计期
         T.ITEM_CODE, -- ITEM编码
         T.ITEM_NAME, -- ITEM中文名称
         T.CATEGORY_CODE, -- 品类编码
         T.CATEGORY_NAME, -- 品类中文名称
         T.L4_CEG_CODE,
         T.L4_CEG_SHORT_CN_NAME,
         T.L4_CEG_CN_NAME,
         T.L3_CEG_CODE,
         T.L3_CEG_SHORT_CN_NAME,
         T.L3_CEG_CN_NAME,
         T.L2_CEG_CODE,
         T.L2_CEG_CN_NAME,
         T.PURCH_QTY AS RECEIVE_QTY, -- 采购数量 
         '' AS RECEIVE_AMT_USD, -- 预测总额（USD）
         T.PURCH_QTY * T.RMB_UNIT_PRICE AS RECEIVE_AMT_CNY, -- 预测总额（RMB）
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,
         CASE
           WHEN T.PURCH_QTY = 0 AND T.RMB_UNIT_PRICE > 0 THEN
            'Y'
           ELSE
            'N'
         END AS APPEND_FLAG,
         T.RMB_UNIT_PRICE AS AVG_PRICE_CNY, -- 人民币单价
         '' AS AVG_PRICE_USD -- 美元单价   
    FROM OPT_ITEM_QTY_UNIT_TMP T; 


  --2.写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC  => '插入预测数到FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_SUCCESS_FLAG,
   F_ERRBUF => 'SUCCESS');
   
-- 收集信息
ANALYSE FIN_DM_OPT_FOI.DM_FOI_FCST_SUM_T;

  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME      => V_SP_NAME,
   F_STEP_NUM     => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME || '运行结束');
   
   RETURN 'SUCCESS';
   
EXCEPTION
  WHEN OTHERS THEN
    X_SUCCESS_FLAG := 0;
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME       => V_SP_NAME,
     F_CAL_LOG_DESC  => V_SP_NAME || '运行失败',
     F_RESULT_STATUS => X_SUCCESS_FLAG,
     F_ERRBUF        => SQLSTATE || ':' ||SQLERRM);
 
END; $$
/

