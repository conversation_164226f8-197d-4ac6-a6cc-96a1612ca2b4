-- Name: f_dm_fcst_price_base_cus_mon_cost_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_cost_idx_t(f_custom_id character varying, f_version_id integer DEFAULT NULL::integer, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 	/*
创建时间：2024-11-12
创建人  ：朱雅欣
背景描述：虚化后月累计权重表 关联  月度累计指数表 取虚化后的权重乘以spart层级的月度指数 收敛到虚化层级
参数描述：参数一： F_CUSTOM_ID 组合ID
          参数二:  F_VERSION_ID 版本ID
          参数三:  x_result_status 返回状态 运行状态返回值 ‘1’为成功，‘0’为失败
来源表  ：月度累计指数表      ：FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_COST_IDX_T
          虚化后月累计权重表  ：FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T
          
结果表  ：虚化后月累计指数表  ：FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T

事例    ：select fin_dm_opt_foi.f_dm_fcst_price_base_cus_mon_cost_idx_t()

*/
DECLARE
  V_SP_NAME    VARCHAR(400) := 'FIN_DM_OPT_FOI.F_DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T';
  V_version_id BIGINT;      --版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  V_BASE_PERIOD_ID          INT ; --基期会计期
  
BEGIN   
  
    IF (F_VERSION_ID IS NULL OR F_VERSION_ID = '')  THEN
SELECT max(VERSION_ID) as VERSION_ID INTO V_VERSION_ID
      FROM FIN_DM_OPT_FOI.DM_FCST_PRICE_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
	   ;
 ELSE
    V_VERSION_ID := F_VERSION_ID;
  END IF;
  
       if MONTH(CURRENT_TIMESTAMP) = 1
  then  select TO_NUMBER((YEAR(CURRENT_DATE) - 2) || '12') into V_BASE_PERIOD_ID ;
  ELSE
        select TO_NUMBER((YEAR(CURRENT_DATE) - 1) || '12') into V_BASE_PERIOD_ID  ;
  END IF ;
  
  delete from FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T WHERE VERSION_ID = V_VERSION_ID AND CUSTOM_ID = F_CUSTOM_ID ;
  
      -- 虚化后月累计权重表 关联  月度累计指数表 取虚化后的权重乘以spart层级的月度指数 收敛到虚化层级
  INSERT INTO FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T(
              VERSION_ID                           /*版本ID*/
             ,PERIOD_YEAR                          /*会计年*/
             ,PERIOD_ID                            /*会计月*/
			 ,BASE_PERIOD_ID                       /*基期月份*/
             ,CUSTOM_ID                            /*虚化组合ID*/
             ,CUSTOM_CN_NAME                       /*虚化组合名称*/
             ,GROUP_CODE                           /*层级编码*/
             ,GROUP_CN_NAME                        /*层级中文名称*/
             ,GROUP_LEVEL                          /*层级描述（LV0:ICT、LV1：LV1层级、LV2：LV2层级、LV3：LV3层级、LV4：LV4层级、SPART：SPART层级）*/
             ,COST_INDEX                           /*指数值*/
             ,OVERSEA_FLAG                         /*国内海外标识*/
             ,REGION_CODE                          /*地区部编码*/
             ,REGION_CN_NAME                       /*地区部名称*/
             ,REPOFFICE_CODE                       /*代表处编码*/
             ,REPOFFICE_CN_NAME                    /*代表处名称*/
             ,SIGN_TOP_CUST_CATEGORY_CODE          /*签约客户_大T系统部编码*/
             ,SIGN_TOP_CUST_CATEGORY_CN_NAME       /*签约客户_大T系统部名称*/
             ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME     /*签约客户_子网系统部名称*/
             ,VIEW_FLAG                            /*视角标识，用于区分不同视角下的数据*/
             ,PARENT_CODE                          /*父层级编码*/
             ,PARENT_CN_NAME                       /*父层级中文名称*/
             ,BG_CODE                              /*BG编码*/
             ,BG_CN_NAME                           /*BG中文名称*/
             ,CREATED_BY                           /*创建人*/
             ,CREATION_DATE                        /*创建时间*/
             ,LAST_UPDATED_BY                      /*修改人*/
             ,LAST_UPDATE_DATE                     /*修改时间*/
             ,DEL_FLAG                             /*删除标识(未删除：N，已删除：Y)*/
	           )  
  with PRICE_BASE_CUS_MON_WEIGHT_TMP AS (
  select VERSION_ID                        /*版本ID*/
            ,PERIOD_YEAR                       /*会计年*/
            ,CUSTOM_ID                         /*虚化组合ID*/
            ,CUSTOM_CN_NAME                    /*虚化组合名称*/
			,LV4_PROD_LIST_CODE	               /*LV3.5重量级团队编码*/
            ,LV4_PROD_LIST_CN_NAME             /*LV3.5重量级团队中文名称*/
            ,GROUP_CODE                        /*层级编码*/
            ,GROUP_CN_NAME                     /*层级中文名称*/
            ,GROUP_LEVEL                       /*层级描述*/
            ,WEIGHT_RATE                       /*权重*/
            ,OVERSEA_FLAG                      /*国内海外标识*/
            ,REGION_CODE                       /*地区部编码*/
            ,REGION_CN_NAME                    /*地区部名称*/
            ,REPOFFICE_CODE                    /*代表处编码*/
            ,REPOFFICE_CN_NAME                 /*代表处名称*/
            ,SIGN_TOP_CUST_CATEGORY_CODE       /*签约客户_大T系统部编码*/
            ,SIGN_TOP_CUST_CATEGORY_CN_NAME    /*签约客户_大T系统部名称*/
            ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME  /*签约客户_子网系统部名称*/
            ,VIEW_FLAG                         /*视角标识，用于区分不同视角下的数据*/
            ,PARENT_CODE                       /*父层级编码*/
            ,PARENT_CN_NAME                    /*父层级中文名称*/
            ,BG_CODE                           /*BG编码*/
            ,BG_CN_NAME                        /*BG中文名称*/
	  from FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_WEIGHT_T
	  where CUSTOM_ID = f_custom_id
        and VERSION_ID = V_VERSION_ID
    ),
	PRICE_MON_COST_IDX_TMP AS (
    select VERSION_ID                        /*版本ID*/
          ,PERIOD_YEAR                       /*会计年*/
          ,PERIOD_ID                         /*会计月*/
          ,BASE_PERIOD_ID                    /*基期月份*/
          ,GROUP_CODE                        /*层级编码*/
          ,GROUP_CN_NAME                     /*层级中文名称*/
          ,GROUP_LEVEL                       /*层级描述*/
          ,COST_INDEX                        /*指数值*/
          ,OVERSEA_FLAG                      /*国内海外标识*/
          ,REGION_CODE                       /*地区部编码*/
          ,REGION_CN_NAME                    /*地区部名称*/
          ,REPOFFICE_CODE                    /*代表处编码*/
          ,REPOFFICE_CN_NAME                 /*代表处名称*/
          ,SIGN_TOP_CUST_CATEGORY_CODE       /*签约客户_大T系统部编码*/
          ,SIGN_TOP_CUST_CATEGORY_CN_NAME    /*签约客户_大T系统部名称*/
          ,SIGN_SUBSIDIARY_CUSTCATG_CN_NAME  /*签约客户_子网系统部名称*/
          ,VIEW_FLAG                         /*视角标识，用于区分不同视角下的数据*/
          ,PARENT_CODE                       /*父层级编码*/
          ,PARENT_CN_NAME                    /*父层级中文名称*/
		  ,BG_CODE                           /*BG编码*/
          ,BG_CN_NAME                        /*BG中文名称*/
	from FIN_DM_OPT_FOI.DM_FCST_PRICE_MON_COST_IDX_T
	where VERSION_ID = V_VERSION_ID
	  and GROUP_LEVEL = 'SPART'
	  and DEL_FLAG  = 'N'
	  and BASE_PERIOD_ID = V_BASE_PERIOD_ID
	  )
	  select t1.VERSION_ID
            ,t1.PERIOD_YEAR
            ,t2.PERIOD_ID
			,t2.BASE_PERIOD_ID
            ,t1.CUSTOM_ID
            ,t1.CUSTOM_CN_NAME
            ,t1.GROUP_CODE
            ,t1.GROUP_CN_NAME
            ,t1.GROUP_LEVEL
            ,sum(t2.COST_INDEX*t1.WEIGHT_RATE) as COST_INDEX
            ,t1.OVERSEA_FLAG
            ,t1.REGION_CODE
            ,t1.REGION_CN_NAME
            ,t1.REPOFFICE_CODE
            ,t1.REPOFFICE_CN_NAME
            ,t1.SIGN_TOP_CUST_CATEGORY_CODE
            ,t1.SIGN_TOP_CUST_CATEGORY_CN_NAME
            ,t1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
            ,t1.VIEW_FLAG
            ,t1.PARENT_CODE
            ,t1.PARENT_CN_NAME
            ,t1.BG_CODE
            ,t1.BG_CN_NAME
            ,-1 AS CREATED_BY
	        ,CURRENT_TIMESTAMP AS CREATION_DATE
	        ,-1 AS LAST_UPDATED_BY
	        ,CURRENT_TIMESTAMP AS LAST_UPDATE_DATE
	        ,'N' AS DEL_FLAG		 
	  from PRICE_BASE_CUS_MON_WEIGHT_TMP t1
	  left join PRICE_MON_COST_IDX_TMP   t2
	   on t1.VERSION_ID  = t2.VERSION_ID
	  and t1.PERIOD_YEAR = t2.PERIOD_YEAR
	  and t1.LV4_PROD_LIST_CODE = t2.PARENT_CODE
	  and t1.GROUP_CODE  = t2.GROUP_CODE
	  and t1.GROUP_LEVEL = t2.GROUP_LEVEL
	  and NVL(t1.OVERSEA_FLAG,'SNULL')                     = NVL(t2.OVERSEA_FLAG,'SNULL')
	  and NVL(t1.REGION_CODE,'SNULL')                      = NVL(t2.REGION_CODE,'SNULL')
	  and NVL(t1.REPOFFICE_CODE,'SNULL')                   = NVL(t2.REPOFFICE_CODE,'SNULL')
      and NVL(t1.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')      = NVL(t2.SIGN_TOP_CUST_CATEGORY_CODE,'SNULL')
	  and NVL(t1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL') = NVL(t2.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME,'SNULL')
	  and NVL(t1.VIEW_FLAG,'SNULL')                        = NVL(t2.VIEW_FLAG,'SNULL')
	  and NVL(t1.BG_CODE,'SNULL')                          = NVL(t2.BG_CODE,'SNULL')
	  WHERE T2.COST_INDEX IS NOT NULL
	  group by t1.VERSION_ID
            ,t1.PERIOD_YEAR
            ,t2.PERIOD_ID
			,t2.BASE_PERIOD_ID
            ,t1.CUSTOM_ID
            ,t1.CUSTOM_CN_NAME
			,t1.GROUP_CODE
			,t1.GROUP_CN_NAME
            ,t1.GROUP_LEVEL
            ,t1.OVERSEA_FLAG
            ,t1.REGION_CODE
            ,t1.REGION_CN_NAME
            ,t1.REPOFFICE_CODE
            ,t1.REPOFFICE_CN_NAME
            ,t1.SIGN_TOP_CUST_CATEGORY_CODE
            ,t1.SIGN_TOP_CUST_CATEGORY_CN_NAME
            ,t1.SIGN_SUBSIDIARY_CUSTCATG_CN_NAME
            ,t1.VIEW_FLAG
            ,t1.PARENT_CODE
            ,t1.PARENT_CN_NAME
            ,t1.BG_CODE
            ,t1.BG_CN_NAME
			;
	  
	     --写入日志
        V_STEP_MUM := V_STEP_MUM + 1;
        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '虚化后月累计指数表 计算完成,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID,
         F_DML_ROW_COUNT => SQL%ROWCOUNT,
         F_FORMULA_SQL_TXT => NULL,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS'); 
	  
	  
	     RETURN 'SUCCESS';
 
          --收集统计信息
        ANALYSE FIN_DM_OPT_FOI.DM_FCST_PRICE_BASE_CUS_MON_COST_IDX_T;


        EXCEPTION
          WHEN OTHERS THEN
          X_RESULT_STATUS := '0';
          
            PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
          (F_SP_NAME => V_SP_NAME, 
           F_STEP_NUM => V_STEP_MUM,
           F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_MUM||'步'||'运行失败,CUSTOM_ID:'||F_CUSTOM_ID||',VERSION_ID'||V_VERSION_ID, 
           F_RESULT_STATUS => X_RESULT_STATUS, 
           F_ERRBUF => SQLSTATE||':'||SQLERRM
           );



end;
$$
/

