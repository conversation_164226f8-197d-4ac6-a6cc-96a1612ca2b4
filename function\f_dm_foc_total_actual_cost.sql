-- Name: f_dm_foc_total_actual_cost; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_total_actual_cost(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
最近修改时间: 2024年6月20日16点27分
修改人   ：黄心蕊 hwx1187045
修改内容： 202407版本 新增IAS部分,及通用颗粒度和量纲颗粒度IAS新增LV4层级
修改时间： 2024年4月22日10点48分
修改人   ：黄心蕊 hwx1187045
修改内容： 202405版本 新增数字能源部分,及量纲增加COA层级
创建时间：2023-10-28
创建人  ：黄心蕊 hwx1187045
背景描述：总成本-成本分布图&热力图
修改时间：2023-12-22
修改人  ：黄心蕊 hwx1187045
修改内容：202401版本量纲新增SPART层级
参数描述：参数一(F_DIMENSION_TYPE)：维度入参，'U'为通用颗粒度，'P'为盈利颗粒度，'D'为量纲颗粒度
		  参数二(X_RESULT_STATUS)：运行状态返回值 ‘1’为成功，‘0’为失败	  
通用颗粒度
来源表： FIN_DM_OPT_FOI.DM_FOC_ACTUAL_COST_T		--采购热力图表
         FIN_DM_OPT_FOI.DM_FOC_MADE_ACTUAL_COST_T	--制造热力图表
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_ACTUAL_COST_T 	--总成本热力图（成本分布图）表
盈利颗粒度
来源表： FIN_DM_OPT_FOI.DM_FOC_PFT_ACTUAL_COST_T		--采购热力图表
         FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ACTUAL_COST_T	--制造热力图表
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ACTUAL_COST_T 	--总成本热力图（成本分布图）表
通用颗粒度
来源表： FIN_DM_OPT_FOI.DM_FOC_DMS_ACTUAL_COST_T		--采购热力图表
         FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ACTUAL_COST_T	--制造热力图表
目标表： FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ACTUAL_COST_T 	--总成本热力图（成本分布图）表

事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ACTUAL_COST('U'); --通用颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ACTUAL_COST('P'); --盈利颗粒度一个版本的数据
		  SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ACTUAL_COST('D'); --盈利颗粒度一个版本的数据
****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME          VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOTAL_ACTUAL_COST';
  V_VERSION          BIGINT;
  V_STEP_NUM         BIGINT := 0; --函数步骤号
  V_BEGIN_DATE       TIMESTAMP := TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP) - 2) || '01',
                                          'YYYYMM'); --三年前首月
  V_PUR_FROM_TABLE   VARCHAR(100);
  V_MADE_FROM_TABLE  VARCHAR(100);
  V_TO_TABLE         VARCHAR(100);
  V_TOTAL_ACTUAL_AMT VARCHAR(100);
  V_DIM_PART         TEXT; --查询字段 不同颗粒度维度字段不同
  V_DIM_LEVEL_PART   TEXT; --查询层级 不同颗粒度所需维度层级不同
  V_SQL_DIM_PART     TEXT;
  V_JOIN_SQL1        TEXT;
  V_JOIN_SQL2        TEXT;
  V_SQL              TEXT;

BEGIN

--1.变量定义
 --1.1 来源表及目标表定义
 
   IF F_DIMENSION_TYPE = 'U' THEN
    -- 通用颗粒度

    V_DIM_LEVEL_PART  := ' (''LV0'',''LV1'',''LV2'',''LV3'',''LV4'') ';	--202407版本 IAS新增LV4层级
																		
	
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
	    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ACTUAL_COST_T';
	    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_ACTUAL_COST_T';
	    V_TOTAL_ACTUAL_AMT :='UNI_TOTAL_ACTUAL_AMT';
	    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_ACTUAL_COST_T';

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_ACTUAL_COST_T';
	    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_ACTUAL_COST_T';
	    V_TOTAL_ACTUAL_AMT :='UNI_ENERGY_TOTAL_ACTUAL_AMT';
	    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_ACTUAL_COST_T';
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
	  /*表定义*/
	    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_ACTUAL_COST_T';
	    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_ACTUAL_COST_T';
	    V_TOTAL_ACTUAL_AMT :='UNI_IAS_TOTAL_ACTUAL_AMT';
	    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_ACTUAL_COST_T';
		
	  END IF;
    
  
  ELSIF F_DIMENSION_TYPE = 'P' THEN
    -- 盈利颗粒度
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_PFT_ACTUAL_COST_T';
		V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MADE_PFT_ACTUAL_COST_T';
		V_TOTAL_ACTUAL_AMT :='PFT_TOTAL_ACTUAL_AMT';
		V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_PFT_ACTUAL_COST_T';

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_ACTUAL_COST_T';
	    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_PFT_ACTUAL_COST_T';
	    V_TOTAL_ACTUAL_AMT :='PFT_ENERGY_TOTAL_ACTUAL_AMT';
	    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_PFT_ACTUAL_COST_T';
		
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN  --202407版本 新增IAS部分
	  /*表定义*/
	    V_PUR_FROM_TABLE  := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_ACTUAL_COST_T';
	    V_MADE_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_PFT_ACTUAL_COST_T';
	    V_TOTAL_ACTUAL_AMT :='PFT_IAS_TOTAL_ACTUAL_AMT';
	    V_TO_TABLE        := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_PFT_ACTUAL_COST_T';
		
	  END IF;
	
	
	V_DIM_PART       := 'L1_NAME,L2_NAME,';
	V_DIM_LEVEL_PART := ' (''LV0'',''LV1'',''LV2'',''L1'',''L2'') ';	--202405版本 ICT修改为LV0
	V_SQL_DIM_PART   := 'T1.L1_NAME,T1.L2_NAME,';
    
    V_JOIN_SQL1       := 'AND NVL(T1.L1_NAME || T1.L2_NAME, ''AAA'') =
                          NVL(DECODE(T1.L1_NAME, '''' , '''', T2.L1_NAME) ||
                              DECODE(T1.L2_NAME, '''' , '''', T2.L2_NAME),''AAA'')';
							  
    V_JOIN_SQL2       := 'AND NVL(T1.L1_NAME || T3.L2_NAME, ''AAA'') =
                          NVL(DECODE(T1.L1_NAME, '''' , '''', T3.L1_NAME) ||
                              DECODE(T1.L2_NAME, '''' , '''', T3.L2_NAME),''AAA'')';
  
  ELSIF F_DIMENSION_TYPE = 'D' THEN
    -- 量纲颗粒度
	
	  IF F_INDUSTRY_FLAG = 'I' THEN 
	  /*表定义*/
		V_PUR_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_DMS_ACTUAL_COST_T';
		V_MADE_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_MADE_DMS_ACTUAL_COST_T';
		V_TOTAL_ACTUAL_AMT	:= 'DMS_TOTAL_ACTUAL_AMT';
		V_TO_TABLE			:= 'FIN_DM_OPT_FOI.DM_FOC_TOTAL_DMS_ACTUAL_COST_T';
		V_DIM_PART			:= 'DIMENSION_CODE,DIMENSION_CN_NAME,
								DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
								DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
								SPART_CODE , SPART_CN_NAME,'; --202401版本新增SPART层级
								
		V_SQL_DIM_PART    := 'T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,
							 T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,
							 T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,
							 T1.SPART_CODE , T1.SPART_CN_NAME,'; --202401版本新增SPART层级
							 
		V_DIM_LEVEL_PART  := ' (''LV0'',''LV1'',''LV2'',''LV3'',''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'') '; --202401版本新增SPART层级
										--202405版本 ICT修改为LV0
		
		V_JOIN_SQL1        := 'AND NVL(T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE
								||T1.SPART_CODE,''AAA'') =
							   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
								 DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE),
								 ''AAA'')'; --202401版本新增SPART层级
								 
		V_JOIN_SQL2        := 'AND NVL( T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE
								||T1.SPART_CODE,''AAA'') =
							   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T3.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T3.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T3.DIMENSION_SUB_DETAIL_CODE) ||
								 DECODE(T1.SPART_CODE,'''','''',T3.SPART_CODE) ,
								 ''AAA'')';  --202401版本新增SPART层级
		

	  ELSIF F_INDUSTRY_FLAG = 'E' THEN  --202405版本 新增数字能源部分来源数据
	  /*表定义*/
	    V_PUR_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_ACTUAL_COST_T';
	    V_MADE_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MADE_DMS_ACTUAL_COST_T';
	    V_TOTAL_ACTUAL_AMT	:= 'DMS_ENERGY_TOTAL_ACTUAL_AMT';
	    V_TO_TABLE			:= 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOTAL_DMS_ACTUAL_COST_T';
		V_DIM_PART			:= 'DIMENSION_CODE,DIMENSION_CN_NAME,
								DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
								DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
								SPART_CODE , SPART_CN_NAME, --202401版本新增SPART层级
								COA_CODE,COA_CN_NAME,'; 	--202405版本 数字能源新增COA层级
								
		V_SQL_DIM_PART		:= 'T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,
							 T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,
							 T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,
							 T1.SPART_CODE , T1.SPART_CN_NAME,	--202401版本新增SPART层级
							 T1.COA_CODE,T1.COA_CN_NAME,'; 	--202405版本 数字能源新增COA层级
							 
		V_DIM_LEVEL_PART	:= ' (''LV0'',''LV1'',''LV2'',''LV3'',''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'',''COA'') '; --202401版本新增SPART层级
											--202405版本 ICT修改为LV0																					--202405版本 数字能源新增COA层级
																												
		V_JOIN_SQL1			:= 'AND NVL(T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE
								||T1.SPART_CODE||T1.COA_CODE,''AAA'') =
							   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
								 DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE)||
								 DECODE(T1.COA_CODE,'''','''',T2.COA_CODE),		--202405版本 数字能源新增COA层级
								 ''AAA'')'; --202401版本新增SPART层级
								 
		V_JOIN_SQL2			:= 'AND NVL( T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE
								||T1.SPART_CODE||T1.COA_CODE,''AAA'') =
							   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T3.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T3.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T3.DIMENSION_SUB_DETAIL_CODE) ||
								 DECODE(T1.SPART_CODE,'''','''',T3.SPART_CODE)||
								 DECODE(T1.COA_CODE,'''','''',T3.COA_CODE),	--202405版本 数字能源新增COA层级
								 ''AAA'')';  --202401版本新增SPART层级
								 
	  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN 
	  /*表定义*/
		V_PUR_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_ACTUAL_COST_T';
		V_MADE_FROM_TABLE	:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_MADE_DMS_ACTUAL_COST_T';
		V_TOTAL_ACTUAL_AMT	:= 'DMS_IAS_TOTAL_ACTUAL_AMT';
		V_TO_TABLE			:= 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOTAL_DMS_ACTUAL_COST_T';
		V_DIM_PART			:= 'DIMENSION_CODE,DIMENSION_CN_NAME,
								DIMENSION_SUBCATEGORY_CODE,DIMENSION_SUBCATEGORY_CN_NAME,
								DIMENSION_SUB_DETAIL_CODE,DIMENSION_SUB_DETAIL_CN_NAME,
								SPART_CODE , SPART_CN_NAME,'; 
								
		V_SQL_DIM_PART    := 'T1.DIMENSION_CODE,T1.DIMENSION_CN_NAME,
							 T1.DIMENSION_SUBCATEGORY_CODE,T1.DIMENSION_SUBCATEGORY_CN_NAME,
							 T1.DIMENSION_SUB_DETAIL_CODE,T1.DIMENSION_SUB_DETAIL_CN_NAME,
							 T1.SPART_CODE , T1.SPART_CN_NAME,'; 
							 
		V_DIM_LEVEL_PART  := ' (''LV0'',''LV1'',''LV2'',''LV3'',''LV4'',''DIMENSION'',''SUBCATEGORY'',''SUB_DETAIL'',''SPART'') '; 
										--202407版本  IAS新增LV4层级
		
		V_JOIN_SQL1        := 'AND NVL(T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE
								||T1.SPART_CODE,''AAA'') =
							   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T2.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T2.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T2.DIMENSION_SUB_DETAIL_CODE) ||
								 DECODE(T1.SPART_CODE,'''','''',T2.SPART_CODE),
								 ''AAA'')'; 
								 
		V_JOIN_SQL2        := 'AND NVL( T1.DIMENSION_CODE || T1.DIMENSION_SUBCATEGORY_CODE || T1.DIMENSION_SUB_DETAIL_CODE
								||T1.SPART_CODE,''AAA'') =
							   NVL(DECODE(T1.DIMENSION_CODE, '''', '''', T3.DIMENSION_CODE) ||
								 DECODE(T1.DIMENSION_SUBCATEGORY_CODE,'''','''',T3.DIMENSION_SUBCATEGORY_CODE) ||
								 DECODE(T1.DIMENSION_SUB_DETAIL_CODE,'''','''',T3.DIMENSION_SUB_DETAIL_CODE) ||
								 DECODE(T1.SPART_CODE,'''','''',T3.SPART_CODE) ,
								 ''AAA'')';  																			
																												
	  END IF;
	
  
  END IF;

  
 --1.2 版本号定义
   
 	IF F_INDUSTRY_FLAG = 'I' THEN
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	 --20240327 修改版本号取数逻辑
	ELSIF F_INDUSTRY_FLAG = 'E' THEN	--202405版本 新增数字能源逻辑
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_ENERGY_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
	ELSIF F_INDUSTRY_FLAG = 'IAS' THEN	--202407版本 新增IAS部分
		SELECT VERSION_ID
		  INTO V_VERSION
		  FROM FIN_DM_OPT_FOI.DM_FOC_IAS_VERSION_INFO_T
		 WHERE DEL_FLAG = 'N'
		   AND STATUS = 1
		   AND UPPER(DATA_TYPE) = 'ITEM'
		 ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;	
		 
	END IF ; 
	
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取得本次执行数据产业项目标识：'||F_INDUSTRY_FLAG||'，颗粒度：'||F_DIMENSION_TYPE||'，以及版本号：'||V_VERSION ,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

   
--2.成本分布图计算
--2.1成本分布图表同版本数据删除
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL:='DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION;
  EXECUTE V_SQL;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除'||V_TO_TABLE||'同版本数据完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  
--2.2采购维表插数
  V_STEP_NUM := V_STEP_NUM + 1;
V_SQL:= '
DROP TABLE IF EXISTS '||V_TOTAL_ACTUAL_AMT||';
CREATE TEMPORARY TABLE '||V_TOTAL_ACTUAL_AMT||'(
  PERIOD_YEAR	BIGINT,
  PERIOD_ID	BIGINT,
  PROD_RND_TEAM_CODE	VARCHAR(50),
  PROD_RND_TEAM_CN_NAME	VARCHAR(200),
  L1_NAME VARCHAR(200),
  L2_NAME VARCHAR(200),
  DIMENSION_CODE	VARCHAR(500),
  DIMENSION_CN_NAME	VARCHAR(2000),
  DIMENSION_SUBCATEGORY_CODE	VARCHAR(500),
  DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(2000),
  DIMENSION_SUB_DETAIL_CODE	VARCHAR(500),
  DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(2000),
  SPART_CODE VARCHAR(500),
  SPART_CN_NAME VARCHAR(500), --202401版本新增SPART层级
  COA_CODE VARCHAR(50),
  COA_CN_NAME VARCHAR(600),		--202405版本 加入COA层级
  GROUP_CODE	VARCHAR(50),
  GROUP_CN_NAME	VARCHAR(1000),
  GROUP_LEVEL	VARCHAR(50),
  PUR_AMT	NUMERIC,
  PUR_WEIGHT	NUMERIC,
  MADE_AMT	NUMERIC,
  MADE_WEIGHT	NUMERIC,
  TOTAL_AMT	NUMERIC,
  PARENT_CODE	VARCHAR(50),
  PARENT_CN_NAME	VARCHAR(1000),
  VIEW_FLAG	VARCHAR(2),
  CALIBER_FLAG	VARCHAR(2),
  OVERSEA_FLAG	VARCHAR(2),
  LV0_PROD_LIST_CODE	VARCHAR(50),
  LV0_PROD_LIST_CN_NAME	VARCHAR(200),
  APPEND_FLAG VARCHAR(2)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY ROUNDROBIN;';

DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

  V_STEP_NUM := V_STEP_NUM + 1;
V_SQL:='
WITH PUR_AMT AS
 (SELECT PERIOD_YEAR,
         PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ACTUAL_COST_AMT,
         PARENT_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_PUR_FROM_TABLE||'
   WHERE VERSION_ID = '||V_VERSION||'
     AND GROUP_LEVEL IN '||V_DIM_LEVEL_PART||'
	 ),

MADE_AMT AS
 (SELECT PERIOD_YEAR,
         PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ACTUAL_COST_AMT,
         PARENT_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM '||V_MADE_FROM_TABLE||'
   WHERE VERSION_ID = '||V_VERSION||'
     AND GROUP_LEVEL IN '||V_DIM_LEVEL_PART||'
	 ),
	 
BASE_DB AS
 (SELECT PERIOD_YEAR,
         PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ACTUAL_COST_AMT,
         PARENT_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM PUR_AMT
  UNION ALL
  SELECT PERIOD_YEAR,
         PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         ACTUAL_COST_AMT,
         PARENT_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM MADE_AMT),
	
TOTAL_COST_TEMP AS
 (SELECT PERIOD_YEAR,
         PERIOD_ID,
         PROD_RND_TEAM_CODE,
         PROD_RND_TEAM_CN_NAME,
		 '||V_DIM_PART||'
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         SUM(ACTUAL_COST_AMT) AS ACTUAL_COST_AMT,
         PARENT_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
         OVERSEA_FLAG,
         LV0_PROD_LIST_CODE,
         LV0_PROD_LIST_CN_NAME
    FROM BASE_DB
   GROUP BY PERIOD_YEAR,
            PERIOD_ID,
            PROD_RND_TEAM_CODE,
            PROD_RND_TEAM_CN_NAME,
			'||V_DIM_PART||'
            GROUP_CODE,
            GROUP_CN_NAME,
            GROUP_LEVEL,
            PARENT_CODE,
            VIEW_FLAG,
            CALIBER_FLAG,
            OVERSEA_FLAG,
            LV0_PROD_LIST_CODE,
            LV0_PROD_LIST_CN_NAME)
			
INSERT INTO '||V_TOTAL_ACTUAL_AMT||'
  (PERIOD_YEAR,
   PERIOD_ID,
   PROD_RND_TEAM_CODE,
   PROD_RND_TEAM_CN_NAME,
   '||V_DIM_PART||'
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PUR_AMT,
   PUR_WEIGHT,
   MADE_AMT,
   MADE_WEIGHT,
   TOTAL_AMT,
   PARENT_CODE,
   VIEW_FLAG,
   CALIBER_FLAG,
   OVERSEA_FLAG,
   LV0_PROD_LIST_CODE,
   LV0_PROD_LIST_CN_NAME,
   APPEND_FLAG)
   
SELECT T1.PERIOD_YEAR,
       T1.PERIOD_ID,
       T1.PROD_RND_TEAM_CODE,
       T1.PROD_RND_TEAM_CN_NAME,
	   '||V_SQL_DIM_PART||'
       T1.GROUP_CODE,
       T1.GROUP_CN_NAME,
       T1.GROUP_LEVEL,
       T2.ACTUAL_COST_AMT AS PUR_AMT,
       NVL(T2.ACTUAL_COST_AMT / NULLIF(T1.ACTUAL_COST_AMT, 0), 0) AS PUR_WEIGHT,
       T3.ACTUAL_COST_AMT AS MADE_AMT,
       NVL(T3.ACTUAL_COST_AMT / NULLIF(T1.ACTUAL_COST_AMT, 0), 0) AS MADE_WEIGHT,
       T1.ACTUAL_COST_AMT AS TOTAL_AMT,
       T1.PARENT_CODE,
       T1.VIEW_FLAG,
       T1.CALIBER_FLAG,
       T1.OVERSEA_FLAG,
       T1.LV0_PROD_LIST_CODE,
       T1.LV0_PROD_LIST_CN_NAME,
	   ''N'' AS APPEND_FLAG
  FROM TOTAL_COST_TEMP T1
  LEFT JOIN PUR_AMT T2
    ON T1.PERIOD_ID || T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
       T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
       T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
       T2.PERIOD_ID || T2.PROD_RND_TEAM_CODE || T2.GROUP_CODE ||
       T2.GROUP_LEVEL || T2.PARENT_CODE || T2.VIEW_FLAG || T2.CALIBER_FLAG ||
       T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE '||V_JOIN_SQL1||'
  LEFT JOIN MADE_AMT T3
    ON T1.PERIOD_ID || T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
       T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
       T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
       T3.PERIOD_ID || T3.PROD_RND_TEAM_CODE || T3.GROUP_CODE ||
       T3.GROUP_LEVEL || T3.PARENT_CODE || T3.VIEW_FLAG || T3.CALIBER_FLAG ||
       T3.OVERSEA_FLAG || T3.LV0_PROD_LIST_CODE '||V_JOIN_SQL2||' ;
';
dbms_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_TO_TABLE||'采购维度插数成功',
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
--2.2每月金额补齐
  V_STEP_NUM := V_STEP_NUM + 1;
	V_SQL:='
    WITH ACTUAL_AMT_TEMP AS
     (--取到完整的不同颗粒度物理维
      SELECT DISTINCT PROD_RND_TEAM_CODE,
                      PROD_RND_TEAM_CN_NAME,
                      '||V_DIM_PART||'
                      GROUP_CODE,
                      GROUP_CN_NAME,
                      GROUP_LEVEL,
                      PARENT_CODE,
                      VIEW_FLAG,
                      CALIBER_FLAG,
                      OVERSEA_FLAG,
                      LV0_PROD_LIST_CODE,
                      LV0_PROD_LIST_CN_NAME            
        FROM '||V_TOTAL_ACTUAL_AMT||' 
		),
    
    PERIOD_DIM_TEMP AS
     (--生成连续月份, 两年前第1月至当前系统月(不含)
      SELECT CAST(TO_CHAR(ADD_MONTHS('''||V_BEGIN_DATE||''',NUM.VAL - 1),''YYYYMM'') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,'''||
                                                      V_BEGIN_DATE||''',
                                                      CURRENT_TIMESTAMP)),
                              1) NUM(VAL)),
							  
    CROSS_JOIN_TEMP AS
     (
      --生成连续年月的物理发散维
      SELECT CAST(SUBSTR(B.PERIOD_ID, 1, 4) AS BIGINT) AS PERIOD_YEAR,
              B.PERIOD_ID,
              T1.VIEW_FLAG,
              T1.PROD_RND_TEAM_CODE,
              T1.PROD_RND_TEAM_CN_NAME,
              '||V_SQL_DIM_PART||'
              T1.GROUP_CODE,
              T1.GROUP_CN_NAME,
              T1.GROUP_LEVEL,
              T1.PARENT_CODE,
              T1.CALIBER_FLAG,
              T1.OVERSEA_FLAG,
              T1.LV0_PROD_LIST_CODE,
              T1.LV0_PROD_LIST_CN_NAME 
        FROM ACTUAL_AMT_TEMP T1, PERIOD_DIM_TEMP B)
	INSERT INTO '||V_TO_TABLE||'
	  (--ID,
	   VERSION_ID,
	   PERIOD_YEAR,
	   PERIOD_ID,
	   VIEW_FLAG,
	   PROD_RND_TEAM_CODE,
	   PROD_RND_TEAM_CN_NAME,
	   '||V_DIM_PART||'
	   GROUP_CODE,
	   GROUP_CN_NAME,
	   GROUP_LEVEL,
	   PUR_AMT,
	   PUR_WEIGHT,
	   MADE_AMT,
	   MADE_WEIGHT,
	   TOTAL_AMT,
	   APPEND_FLAG,
	   PARENT_CODE,
	   PARENT_CN_NAME,
	   CREATED_BY,
	   CREATION_DATE,
	   LAST_UPDATED_BY,
	   LAST_UPDATE_DATE,
	   DEL_FLAG,
	   CALIBER_FLAG,
	   OVERSEA_FLAG,
	   LV0_PROD_LIST_CODE,
	   LV0_PROD_LIST_CN_NAME)

		SELECT '||V_VERSION||' AS VERSION_ID,
				T1.PERIOD_YEAR,
		       T1.PERIOD_ID,
		       T1.VIEW_FLAG,
		       T1.PROD_RND_TEAM_CODE,
		       T1.PROD_RND_TEAM_CN_NAME,
		       '||V_SQL_DIM_PART||'
		       T1.GROUP_CODE,
		       T1.GROUP_CN_NAME,
		       T1.GROUP_LEVEL,
			   NVL(T2.PUR_AMT,0) AS PUR_AMT,
			   NVL(T2.PUR_WEIGHT,0) AS PUR_WEIGHT,
			   NVL(T2.MADE_AMT,0) AS MADE_AMT,
			   NVL(T2.MADE_WEIGHT,0) AS MADE_WEIGHT,
			   NVL(T2.TOTAL_AMT,0) AS TOTAL_AMT,
			   DECODE(T2.TOTAL_AMT,'''',''Y'',''N'') AS APPEND_FLAG,
		       T1.PARENT_CODE,
			   T3.GROUP_CN_NAME AS PARENT_CN_NAME,
			   ''-1'' AS CREATED_BY,
			   CURRENT_TIMESTAMP AS CREATION_DATE,
			   ''-1'' AS LAST_UPDATED_BY,
			   CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
			   ''N'' AS DEL_FLAG,
		       T1.CALIBER_FLAG,
		       T1.OVERSEA_FLAG,
		       T1.LV0_PROD_LIST_CODE,
		       T1.LV0_PROD_LIST_CN_NAME 
		  FROM CROSS_JOIN_TEMP T1
		  LEFT JOIN '||V_TOTAL_ACTUAL_AMT||' T2
		    ON T1.PERIOD_ID || T1.PROD_RND_TEAM_CODE || T1.GROUP_CODE ||
               T1.GROUP_LEVEL || T1.PARENT_CODE || T1.VIEW_FLAG || T1.CALIBER_FLAG ||
               T1.OVERSEA_FLAG || T1.LV0_PROD_LIST_CODE =
               T2.PERIOD_ID || T2.PROD_RND_TEAM_CODE || T2.GROUP_CODE ||
               T2.GROUP_LEVEL || T2.PARENT_CODE || T2.VIEW_FLAG || T2.CALIBER_FLAG ||
               T2.OVERSEA_FLAG || T2.LV0_PROD_LIST_CODE '||V_JOIN_SQL1||'
		  LEFT JOIN (SELECT DISTINCT GROUP_CODE,GROUP_CN_NAME
					   FROM ACTUAL_AMT_TEMP) T3
				ON T1.PARENT_CODE = T3.GROUP_CODE;';
				
dbms_OUTPUT.PUT_LINE(V_SQL);
 EXECUTE V_SQL;
 
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_FORMULA_SQL_TXT => V_SQL,
   F_CAL_LOG_DESC => V_TO_TABLE||'数据插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   

  RETURN 'SUCCESS';

 EXCEPTION
   WHEN OTHERS THEN
   X_RESULT_STATUS := '0';
   
   
 	PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME, 
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => V_SP_NAME||'第'||V_STEP_NUM||'步'||'运行失败', 
    F_RESULT_STATUS => X_RESULT_STATUS, 
    F_ERRBUF => SQLSTATE||':'||SQLERRM
    );

 END; 



$$
/

