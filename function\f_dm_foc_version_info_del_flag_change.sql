-- Name: f_dm_foc_version_info_del_flag_change; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_version_info_del_flag_change(x_del_flag character varying DEFAULT NULL::character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
创建时间:2023年12月13日
创建人  :李志勇
最后修改时间:2023年12月26日-2024年3月27
最后修改人:李志勇
背景描述:修改版本信息表中当月生成的最大版本号（年度和月度），其对应del_flag设置为 Y 或者N
参数描述: x_del_flag :更改后的标志  x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(版本信息表)
目标表:FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T(版本信息表)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_VERSION_INFO_DEL_FLAG_CHANGE('Y')
*/
DECLARE
    V_SP_NAME              VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_VERSION_INFO_DEL_FLAG_CHANGE'; --存储过程名称
    V_STEP_MUM             BIGINT        := 0; --步骤号
    V_SQL                  TEXT; --SQL逻辑
    V_CNT_CATEGORY         BIGINT;
    V_CNT_ITEM             BIGINT;
    V_DEL_FLAG             VARCHAR(5)    := x_del_flag;
    V_FROM_TABLE           varchar(100)  := 'FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T';
    V_CATEGORY_MAX_VERSION int8;
    V_ITEM_MAX_VERSION     int8;

BEGIN
    X_RESULT_STATUS = 'SUCCESS';

    --1、开始日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => V_SP_NAME || '开始执行');

    /*检查当前正在刷数的版本号是否有多个*/
    IF x_del_flag IS NULL THEN
        V_SQL := 'SELECT COUNT(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''CATEGORY''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)
          AND del_flag = ''N''
          AND is_running = ''Y''';
        EXECUTE IMMEDIATE V_SQL INTO V_CNT_CATEGORY;

        V_SQL := ' SELECT COUNT(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ITEM''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)
          AND del_flag = ''N''
		  AND is_running = ''Y''';
        EXECUTE IMMEDIATE V_SQL INTO V_CNT_ITEM;

        IF V_CNT_CATEGORY <> 1 OR V_CNT_ITEM <> 1 THEN
         /*错误信息写入日志表*/
	         V_STEP_MUM := V_STEP_MUM + 1;
	    	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	        (F_SP_NAME => V_SP_NAME,
	         F_STEP_NUM => V_STEP_MUM,
	         F_CAL_LOG_DESC => 'ERROR:当月版本号存在多个有效且正在跑数的版本号。CATEGORY数量：'||V_CNT_CATEGORY||'，ITEM数量：'||V_CNT_ITEM,
	         F_DML_ROW_COUNT => 0,
	         F_RESULT_STATUS => X_RESULT_STATUS,
	         F_ERRBUF => 'SUCCESS');
	         
	         /*返回错误告警*/
            x_result_status = 'FAILED';
            RETURN 'ERROR:当月版本号存在多个有效版本号.';
		ELSE
			 /*正确信息写入日志表*/
	         V_STEP_MUM := V_STEP_MUM + 1;
	    	 PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
	        (F_SP_NAME => V_SP_NAME,
	         F_STEP_NUM => V_STEP_MUM,
	         F_CAL_LOG_DESC => '当前正在刷数版本号数量正确，CATEGORY数量ITEM数量分别只有1个',
	         F_DML_ROW_COUNT => 0,
	         F_RESULT_STATUS => X_RESULT_STATUS,
	         F_ERRBUF => 'SUCCESS');
			 RETURN 'SUCCESS';
        END IF;

    ELSE
        /*2、获取当月最大版本号*/
        V_SQL := ' SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''CATEGORY''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)';
        EXECUTE IMMEDIATE V_SQL INTO V_CATEGORY_MAX_VERSION;

        V_SQL := 'SELECT MAX(version_id)
        FROM ' || V_FROM_TABLE || '
        WHERE DATA_TYPE = ''ITEM''
          AND SUBSTR(CREATION_DATE, 1, 7) = SUBSTR(CURRENT_DATE, 1, 7)';
        EXECUTE IMMEDIATE V_SQL INTO V_ITEM_MAX_VERSION;

        --3、屏蔽/开放当期版本号,取最大
        IF V_DEL_FLAG = 'N' THEN --放开当期版本号
            V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET del_flag         = ''' || V_DEL_FLAG || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_CATEGORY_MAX_VERSION || '
              AND del_flag = ''Y''';
            EXECUTE IMMEDIATE V_SQL;


            V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET del_flag         = ''' || V_DEL_FLAG || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_ITEM_MAX_VERSION || '
              AND del_flag = ''Y''';
            EXECUTE IMMEDIATE V_SQL;

        ELSE
            IF V_DEL_FLAG = 'Y' THEN --屏蔽当期版本号
                V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET del_flag         = ''' || V_DEL_FLAG || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_CATEGORY_MAX_VERSION || '
              AND del_flag = ''N''';
                EXECUTE IMMEDIATE V_SQL;

                V_SQL := 'UPDATE ' || V_FROM_TABLE || '
            SET del_flag         = ''' || V_DEL_FLAG || ''',
                last_update_date = current_timestamp
            WHERE VERSION_ID = ' || V_ITEM_MAX_VERSION || '
              AND del_flag = ''N''';
                EXECUTE IMMEDIATE V_SQL;


            END IF;
        END IF;

    END IF;

    --写入日志
    V_STEP_MUM := V_STEP_MUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
        (F_SP_NAME => V_SP_NAME,
         F_STEP_NUM => V_STEP_MUM,
         F_CAL_LOG_DESC => '版本号状态修改完成。' || 'CATEGROY本月最大版本号: ' || V_CATEGORY_MAX_VERSION ||
                           '状态修改成: ' || V_DEL_FLAG || ';    ITEM本月最大版本号: ' || V_ITEM_MAX_VERSION ||
                           '状态修改成: ' || V_DEL_FLAG,
         F_DML_ROW_COUNT => 0,
         F_RESULT_STATUS => X_RESULT_STATUS,
         F_ERRBUF => 'SUCCESS');

    RETURN 'SUCCESS';

EXCEPTION
    WHEN OTHERS THEN
        X_RESULT_STATUS := 'FAILED';

        PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
            (F_SP_NAME => V_SP_NAME,
             F_CAL_LOG_DESC => V_SP_NAME || '运行失败',
             F_RESULT_STATUS => X_RESULT_STATUS,
             F_ERRBUF => SQLSTATE || ':' || SQLERRM
            );
 
END
$$
/

