-- Name: f_point_dm_foi_price_index_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_point_dm_foi_price_index_t(f_caliber_flag character varying, f_cate_version bigint, f_item_version bigint, f_change_base_period_id numeric, f_group_code character varying, f_group_cn_name character varying, f_column_flag character varying, f_customization_flag character varying, f_group_level character varying, OUT x_result_status character varying)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
/***************************************************************************************************************************************************************
创建时间：2024年2月23日9点54分
创建人  ：唐钦 twx1139790
最新修改时间：2024年6月26日
修改人：唐钦
背景描述：指数表切换基期后的指数计算
参数描述：
                    参数一(f_caliber_flag)：           口径标识（I：采购ICT、E：数字能源、IAS：IAS、EAST_CHINA_PQC：华东采购）
                    参数二(f_cate_version)：           Top品类清单最新版本
                    参数三(f_item_version)：           导入通用数据版本（TOP品类规格品清单最新版本）
                    参数四(f_change_base_period_id)：  切换基期的参数
                    参数五(f_group_code)：             入参层级编码
                    参数六(f_group_cn_name)：          入参层级中文名称
                    参数七(f_column_flag):             连续性影响参数/是否含集团代采参数
                    参数八(f_customization_flag):      自选组合有效标识
                    参数九(f_group_level):             入参编码/入参中文名称的层级
                    参数十(x_result_status)：           运行状态返回值-成功或者失败

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME               VARCHAR(50) := 'FIN_DM_OPT_FOI.F_POINT_DM_FOI_PRICE_INDEX_T';
  V_STEP_NUM              BIGINT := 0;
  V_VERSION_ID            BIGINT := F_ITEM_VERSION;
  V_BASE_PERIOD_ID        INT;
  V_CHANGE_BASE_PERIOD_ID INT := F_CHANGE_BASE_PERIOD_ID;
  V_GROUP_CODE            VARCHAR(50) := F_GROUP_CODE;
  V_GROUP_CN_NAME         VARCHAR(50) := F_GROUP_CN_NAME;
  V_COLUMN_FLAG           VARCHAR(50) := F_COLUMN_FLAG;
  V_CHILD_CODE            TEXT;
  V_SQL_PART1             TEXT;
  V_SQL_PART2             TEXT;
  V_SQL_PART3             TEXT;
  V_SQL                   TEXT;
  V_CUSTOMIZATION_FLAG    VARCHAR(100) := CASE WHEN F_CUSTOMIZATION_FLAG IS NULL THEN 'N' ELSE F_CUSTOMIZATION_FLAG END;--自定义组合的标识，若为空的话置N
  V_FIELD_NAME            VARCHAR(100);   -- 用于确定传入的CODE是什么层级，取对应层级的字段
  V_CATEGORY              VARCHAR(200);
  V_FROM_TABLE            VARCHAR(200);
  V_LEVEL_TABLE           VARCHAR(200);
  V_COMB_TABLE            VARCHAR(200);
  V_MID_TABLE             VARCHAR(200);
  V_VERSION_TABLE         VARCHAR(200);
  V_TO_TABLE              VARCHAR(200);
  V_BASE_VALUE            VARCHAR(500);
  V_COMPUTE_VALUE         VARCHAR(200);  
  V_SQL_CONDITION         VARCHAR(500);    -- 筛选条件
  V_COLUMN                VARCHAR(50);  
  V_CUSTOM_FLAG           VARCHAR(500);
  V_INDEX_FLAG            VARCHAR(500);
  -- 202407版本新增
  V_SQL_CALIBER VARCHAR(200);
  V_CALIBER VARCHAR(100);
  V_IN_CALIBER VARCHAR(200);
  V_REL_CONDITION VARCHAR(200);
  
BEGIN
    X_RESULT_STATUS = '1';

--0.日志开始
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');

  --校验GROUP_CN_NAME和GROUP_CODE不能同时有值
  IF V_GROUP_CODE IS NOT NULL AND
     V_GROUP_CN_NAME IS NOT NULL THEN
     RETURN '输入两个参数了！';
  END IF;
  
  -- 通过判断不同入参，对变量、参数赋值
  IF F_CALIBER_FLAG = 'I' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_CATE_GROUP_WEIGHT_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T';  -- 采购ICT结果表
     V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T_TEMP';  -- 采购ICT中间临时表
     V_LEVEL_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_HIS_ORD_REC_REPORT_T';  -- 采购ICT：取出所有层级的CODE
     V_COMB_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_FOI_CUSTOM_CATG_COMB_T';  -- 采购ICT：自选组合维表
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_LEV_ANNUAL_AMP_T';  -- 采购ICT：表里取出默认基期值
     V_COLUMN := 'CONTINUITY_TYPE';
  ELSIF F_CALIBER_FLAG = 'E' THEN
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_CATE_GROUP_WEIGHT_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T';  -- 数字能源结果表
     V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_PRICE_INDEX_TEMP';  -- 数字能源中间临时表
     V_LEVEL_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_HIS_ORD_REC_REPORT_T';  -- 数字能源：取出所有层级的CODE
     V_COMB_TABLE := 'FIN_DM_OPT_FOI.DM_DIM_FOI_ENERGY_CUSTOM_CATG_COMB_T';  -- 数字能源：自选组合维表
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_LEV_ANNUAL_AMP_T';  -- 数字能源：表里取出默认基期值
     V_COLUMN := 'GROUP_PUR_FLAG';
  ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') THEN   -- 202407版本新增IAS/华东采购
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_CATE_GROUP_WEIGHT_T';
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_PRICE_INDEX_T';  -- 数字能源结果表
     V_MID_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_PRICE_INDEX_TEMP';  -- 数字能源中间临时表
     V_LEVEL_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_HIS_ORD_REC_REPORT_T';  -- 数字能源：取出所有层级的CODE
     V_COMB_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_CUSTOM_CATG_COMB_T';  -- 数字能源：自选组合维表
     V_VERSION_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_LEV_ANNUAL_AMP_T';  -- 数字能源：表里取出默认基期值
     V_COLUMN := 'GROUP_PUR_FLAG';
     V_CALIBER := 'CALIBER_FLAG,';
     V_IN_CALIBER := ''''||F_CALIBER_FLAG||''' AS CALIBER_FLAG,';
     V_SQL_CALIBER := ' AND CALIBER_FLAG = '''||F_CALIBER_FLAG||'''';
     V_REL_CONDITION := 'AND NVL(T1.CALIBER_FLAG,''SNULL1'') = NVL(T2.CALIBER_FLAG,''SNULL1'')';
  END IF;
  
  -- 默认基期取值（取年度涨跌幅最新版本数据的最大年份-3，再拼上'01'）
  V_SQL := '
  SELECT MAX(PERIOD_YEAR)-3||''01''
     FROM '||V_VERSION_TABLE||'
     WHERE VERSION_ID = '||F_CATE_VERSION||'
     '||V_SQL_CALIBER;
  EXECUTE IMMEDIATE V_SQL INTO V_BASE_PERIOD_ID;
  DBMS_OUTPUT.PUT_LINE('取到默认基期：'||V_BASE_PERIOD_ID);
     
 -- 若输入的切换后基期为默认基期，输出提示
  IF V_CHANGE_BASE_PERIOD_ID = V_BASE_PERIOD_ID THEN  
       RETURN '输入的切换基期后的月份与默认基期一致啦，函数不可调用！';
  END IF;
    
  --清空临时表
  EXECUTE IMMEDIATE 'TRUNCATE TABLE '||V_MID_TABLE;
  DBMS_OUTPUT.PUT_LINE('清空临时表');

--1.写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除指数表数据完成,切换到基期为'||V_CHANGE_BASE_PERIOD_ID||',版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

  -- 判断数据为正常层级的数据计算时，赋值
   IF V_CUSTOMIZATION_FLAG = 'N' AND V_GROUP_CODE IS NOT NULL THEN
  V_SQL := '
     WITH ALL_LEVEL_TEMP AS(
--从维表中取出所有L2,L3,L4,CATEGORY的维度信息)
     SELECT DISTINCT L2_CEG_CODE,
                     L2_CEG_CN_NAME,
                     L3_CEG_CODE,
                     L3_CEG_CN_NAME,
                     L3_CEG_SHORT_CN_NAME,
                     L4_CEG_CODE,
                     L4_CEG_CN_NAME,
                     L4_CEG_SHORT_CN_NAME,
                     CATEGORY_CODE,
                     CATEGORY_NAME,
                     ITEM_CODE,
                     ITEM_NAME,
                     SUPPLIER_CODE,
                     SUPPLIER_CN_NAME
                     FROM '||V_LEVEL_TABLE||'
                     WHERE DEL_FLAG = ''N''
                     '||V_SQL_CALIBER||'
                     ),
     LEVEL_FLAG_TEMP AS
             (
  --根据维度CODE赋予层级标识LEVEL_FLAG
     SELECT DISTINCT L2_CEG_CODE    AS LEVEL_CODE,
                     L2_CEG_CN_NAME AS LEVEL_CN_NAME,
                     ''LV2'' AS GROUP_LEVEL,
                      1             AS LEVEL_FLAG
               FROM ALL_LEVEL_TEMP 
     UNION ALL 
     SELECT DISTINCT L3_CEG_CODE    AS LEVEL_CODE,
                     L3_CEG_CN_NAME AS LEVEL_CN_NAME,
                     ''LV3'' AS GROUP_LEVEL,
                      2             AS LEVEL_FLAG
               FROM ALL_LEVEL_TEMP 
     UNION ALL 
     SELECT DISTINCT L4_CEG_CODE    AS LEVEL_CODE,
                     L4_CEG_CN_NAME AS LEVEL_CN_NAME,
                     ''LV4'' AS GROUP_LEVEL,
                     3              AS LEVEL_FLAG
               FROM ALL_LEVEL_TEMP 
     UNION ALL 
     SELECT DISTINCT CATEGORY_CODE AS LEVEL_CODE,
                     CATEGORY_NAME AS LEVEL_CN_NAME,
                     ''CATEGORY'' AS GROUP_LEVEL,
                     4             AS LEVEL_FLAG
               FROM ALL_LEVEL_TEMP
     UNION ALL 
     SELECT DISTINCT ITEM_CODE AS LEVEL_CODE,
                     ITEM_NAME AS LEVEL_CN_NAME,
                     ''ITEM'' AS GROUP_LEVEL,
                     5             AS LEVEL_FLAG
               FROM ALL_LEVEL_TEMP
     UNION ALL 
     SELECT DISTINCT SUPPLIER_CODE AS LEVEL_CODE,
                     SUPPLIER_CN_NAME AS LEVEL_CN_NAME,
                     ''SUPPLIER'' AS GROUP_LEVEL,
                     6             AS LEVEL_FLAG
               FROM ALL_LEVEL_TEMP)

--根据F_GROUP_CODE入参, 带出层级标识, 并且取出对应字段
     SELECT DISTINCT DECODE(LEVEL_FLAG, 1, ''L2_CEG_CODE'', 2, ''L3_CEG_CODE'', 3, ''L4_CEG_CODE'', 4, ''PARENT_CODE'', 5, ''ITEM'', 6, ''GROUP_CODE'') AS FIELD_NAME    
         FROM LEVEL_FLAG_TEMP
         WHERE LEVEL_CODE = '''||V_GROUP_CODE||'''
         AND GROUP_LEVEL = '''||F_GROUP_LEVEL||'''';
   EXECUTE IMMEDIATE V_SQL INTO V_FIELD_NAME;
   DBMS_OUTPUT.PUT_LINE('插入变量：'||V_FIELD_NAME);
   ELSE NULL;
   END IF;
   
  --连续性不为空并且对LV2做处理，其他层级的直接取
  IF F_GROUP_LEVEL = 'LV2' THEN
    IF F_CALIBER_FLAG = 'EAST_CHINA_PQC' THEN   -- 华东采购无连续性/集团采购标识
    V_SQL_PART1 := ' AND UPPER(GROUP_LEVEL) IN (''LV2'',''LV3'',''LV4'',''CATEGORY'',''CATE_SUPPLIER'',''SUPPLIER'')';
    ELSE
    V_SQL_PART1 := ' AND UPPER(GROUP_LEVEL) IN (''LV2'',''LV3'',''LV4'',''CATEGORY'',''CATE_SUPPLIER'',''SUPPLIER'')
                     AND (CASE WHEN T.GROUP_LEVEL= ''LV2'' THEN T.'||V_COLUMN||' = '''||V_COLUMN_FLAG||''' ELSE TRUE END)';
    END IF;
  ELSIF V_COLUMN_FLAG IS NULL THEN   --连续性为空的话就不对LV2做处理
    V_SQL_PART1 := '';
  END IF;

  IF V_GROUP_CODE IS NOT NULL THEN  --此逻辑为月度分析页面的逻辑
  -- 判断入参CODE在指数表里是否还有子级数据
     V_SQL := 'SELECT COUNT(1)
                  FROM '||V_TO_TABLE||' T1
                  WHERE T1.PARENT_CODE = '''||V_GROUP_CODE||'''
                  AND T1.VERSION_ID = '||V_VERSION_ID||'
                  '||V_SQL_CALIBER;
     EXECUTE IMMEDIATE V_SQL INTO V_INDEX_FLAG;
  -- 判断入参CODE在组选组合维表里是否存在
     V_SQL := 'SELECT COUNT(1)
                  FROM '||V_COMB_TABLE||'
                  WHERE CUSTOMIZATION_ID :: TEXT = '''||V_GROUP_CODE||'''
                  AND ENABLE_FLAG = ''Y''
                  '||V_SQL_CALIBER;
     EXECUTE IMMEDIATE V_SQL INTO V_CUSTOM_FLAG;

    -- 判断不同条件下的处理逻辑
      IF V_INDEX_FLAG <> 0 AND
         V_CUSTOMIZATION_FLAG = 'N' THEN
     --计算GROUP_CODE的指数和PARENT_CODE=GROUP_CODE的子类的指数
         V_SQL_PART2 := ' AND T1.GROUP_CODE = '''||V_GROUP_CODE||''' OR T1.'||V_FIELD_NAME||' = ''' ||V_GROUP_CODE||''' AND T1.GROUP_LEVEL NOT IN (''CUSTOMIZATION'')';
         V_SQL_PART3 := '';
     --如果GROUP_CODE存在自定义维表里，并且CUSTOMIZATION_FLAG='Y'的情况下 可以取到自定义组合GROUP_CODE的唯一
       ELSIF V_CUSTOM_FLAG <> 0 AND V_CUSTOMIZATION_FLAG = 'Y' THEN
     --提取自定义组合的子类和自定义组合本身的GROUP_CODE
         V_CHILD_CODE :='
                        ,CHILD_CODE_DATA AS(
                        SELECT CATEGORY_CODE 
                        FROM '||V_COMB_TABLE||' 
                        WHERE CUSTOMIZATION_ID = '''||V_GROUP_CODE||''' AND ENABLE_FLAG=''Y''
                        '||V_SQL_CALIBER||'
                        UNION ALL
                        SELECT '''||V_GROUP_CODE||'''
                        )';
         V_FIELD_NAME := 'OTHER';
     --自定义组合的子类的指数计算和自定义组合的计算
         V_SQL_PART2 :=
                ' AND EXISTS (SELECT 1 FROM CHILD_CODE_DATA T2 WHERE T1.GROUP_CODE = T2.CATEGORY_CODE ) 
                  AND CASE WHEN T1.GROUP_CODE= '''||V_GROUP_CODE||''' THEN T1.GROUP_LEVEL= ''CUSTOMIZATION'' ELSE TRUE END';
       ELSE
   --品类的指数计算（包括供应商/ITEM->品类）
         V_SQL_PART2 := ' AND T1.GROUP_CODE = '''||V_GROUP_CODE||''' AND T1.GROUP_LEVEL <> ''CUSTOMIZATION''';
       END IF;
  --计算GROUP_CN_NAME不为空的计算
  ELSIF F_GROUP_CN_NAME IS NOT NULL THEN
  --判断是否为TOP品类
    V_FIELD_NAME := 'OTHER';
    V_SQL_PART2 := ' AND EXISTS (SELECT 1 FROM CHILD_CODE_DATA T2 WHERE T1.GROUP_CODE = T2.CATEGORY_CODE ) AND T1.GROUP_LEVEL <> ''CUSTOMIZATION'' ';
  --计算基期切换后GROUP_CN_NAME不为空的处理
   V_SQL_PART3      := '
   UNION ALL
   SELECT T1.YEAR,
          T1.PERIOD_ID,
          '||V_CHANGE_BASE_PERIOD_ID||',
          T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
          T1.GROUP_LEVEL,
          T1.PARENT_CODE,
          T1.PARENT_CN_NAME,
          (T1.PRICE_INDEX/T2.PRICE_INDEX)*100 AS PRICE_INDEX,
          T1.L4_CEG_CODE,
          T1.L4_CEG_SHORT_CN_NAME,
          T1.L3_CEG_CODE,
          T1.L3_CEG_SHORT_CN_NAME,
          T1.L2_CEG_CODE,
          T1.L2_CEG_CN_NAME,
          T1.L4_CEG_CN_NAME,
          T1.L3_CEG_CN_NAME,
         '||V_IN_CALIBER||'
          -1 AS CREATED_BY,
          CURRENT_TIMESTAMP AS CREATION_DATE,
          -1 AS LAST_UPDATED_BY,
          CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
          ''N'' AS DEL_FLAG,
          '||V_VERSION_ID||',
          '||V_COLUMN||'
      FROM PRICE_DATA_TEMP T1
      JOIN (SELECT T1.GROUP_CODE,T1.GROUP_CN_NAME, T1.PRICE_INDEX
               FROM PRICE_DATA_TEMP T1
               WHERE T1.PERIOD_ID = '||V_CHANGE_BASE_PERIOD_ID||'
            )T2
      ON T1.GROUP_CN_NAME = T2.GROUP_CN_NAME
      WHERE T2.PRICE_INDEX <> 0 
      AND T1.GROUP_CN_NAME = '''||V_GROUP_CN_NAME||'''';

  --TOP品类和TOP品类的子类的指数计算以及品类分类和品类分类的子类的指数计算
    IF (V_GROUP_CN_NAME ~ 'TOP') THEN
  --提取TOP品类的子类的GROUP_CODE
         V_CHILD_CODE :='
                   ,CHILD_CODE_DATA AS(
                            SELECT DISTINCT GROUP_CODE AS CATEGORY_CODE
                               FROM '||V_FROM_TABLE||'
                               WHERE VERSION_ID = '||V_VERSION_ID||' 
                               AND PARENT_TYPE = ''TOP_TYPE''  -- TOP分类类型数据
                               AND PARENT_CODE = '''||F_GROUP_CN_NAME||'''
                               '||V_SQL_CALIBER||')';   --TOP50/TOP100 或TOP300
  --提取品类分类的子类的GROUP_CODE        
    ELSE
      V_CHILD_CODE :='
                ,CHILD_CODE_DATA AS(
                         SELECT DISTINCT GROUP_CODE AS CATEGORY_CODE
                               FROM '||V_FROM_TABLE||'
                               WHERE VERSION_ID = '||V_VERSION_ID||' 
                               AND PARENT_TYPE = ''CATEGORY_TYPE''  -- 品类分类类型数据
                               AND PARENT_CODE = '''||F_GROUP_CN_NAME||'''
                               '||V_SQL_CALIBER||')';
    END IF;
  END IF;

  -- 输入的CODE为ITEM层级时，不执行V_SQL语句，不计算指数，只计算点降
  IF V_FIELD_NAME <> 'ITEM' THEN

  --插入临时表以及提取某个版版本初始基期的数据为公共部分
  V_SQL := '
INSERT INTO '||V_MID_TABLE||'
  (
   YEAR,
   PERIOD_ID,
   BASE_PERIOD_ID,
   GROUP_CODE,
   GROUP_CN_NAME,
   GROUP_LEVEL,
   PARENT_CODE,
   PARENT_CN_NAME,
   PRICE_INDEX,
   L4_CEG_CODE,
   L4_CEG_SHORT_CN_NAME,
   L3_CEG_CODE,
   L3_CEG_SHORT_CN_NAME,
   L2_CEG_CODE,
   L2_CEG_CN_NAME,
   L4_CEG_CN_NAME,
   L3_CEG_CN_NAME,
   '||V_CALIBER||'
   CREATED_BY,
   CREATION_DATE,
   LAST_UPDATED_BY,
   LAST_UPDATE_DATE,
   DEL_FLAG,
   VERSION_ID,
   '||V_COLUMN||')
WITH PRICE_DATA_TEMP AS (
  SELECT  T.YEAR,
          T.PERIOD_ID,
          T.BASE_PERIOD_ID,
          T.GROUP_CODE,
          T.GROUP_CN_NAME,
          T.GROUP_LEVEL,
          T.PARENT_CODE,
          T.PARENT_CN_NAME,
          T.PRICE_INDEX,
          T.L4_CEG_CODE,
          T.L4_CEG_SHORT_CN_NAME,
          T.L3_CEG_CODE,
          T.L3_CEG_SHORT_CN_NAME,
          T.L2_CEG_CODE,
          T.L2_CEG_CN_NAME,
          T.L4_CEG_CN_NAME,
          T.L3_CEG_CN_NAME,
          T.VERSION_ID,
          T.'||V_COLUMN||'
  FROM '||V_TO_TABLE||' T
  WHERE VERSION_ID = '||V_VERSION_ID||'
  AND T.BASE_PERIOD_ID = '||V_BASE_PERIOD_ID||'
  AND T.DEL_FLAG = ''N''
  '||V_SQL_CALIBER||'
  '||V_SQL_PART1||')
  '||V_CHILD_CODE||'
  SELECT T1.YEAR,
         T1.PERIOD_ID,
         '||V_CHANGE_BASE_PERIOD_ID||',
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         (T1.PRICE_INDEX/T2.PRICE_INDEX)*100 AS PRICE_INDEX,
         T1.L4_CEG_CODE,
         T1.L4_CEG_SHORT_CN_NAME,
         T1.L3_CEG_CODE,
         T1.L3_CEG_SHORT_CN_NAME,
         T1.L2_CEG_CODE,
         T1.L2_CEG_CN_NAME,
         T1.L4_CEG_CN_NAME,
         T1.L3_CEG_CN_NAME,
         '||V_IN_CALIBER||'
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         ''N'' AS DEL_FLAG,
         '||V_VERSION_ID||',
         '||V_COLUMN||'
      FROM PRICE_DATA_TEMP T1
      INNER JOIN (SELECT T1.GROUP_CODE,T1.GROUP_CN_NAME,T1.GROUP_LEVEL,T1.PARENT_CODE, T1.PRICE_INDEX
                      FROM PRICE_DATA_TEMP T1
                      WHERE T1.PERIOD_ID = '||V_CHANGE_BASE_PERIOD_ID||'
                  )T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
      WHERE T2.PRICE_INDEX <> 0'||V_SQL_PART2||V_SQL_PART3;
  
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
--写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '当入参CODE为：'||NVL(V_GROUP_CODE,V_GROUP_CN_NAME)||'时，版本号为：'||V_VERSION_ID||'，切换基期到'||V_CHANGE_BASE_PERIOD_ID||'，插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
 
  -- 当传入的CODE层级是LV2层级时，删除结果表以下数据
    IF F_GROUP_LEVEL = 'LV2' AND V_COLUMN_FLAG IS NOT NULL THEN
      V_SQL := 'DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||'
                                             AND BASE_PERIOD_ID = '||V_CHANGE_BASE_PERIOD_ID||'
                                             AND UPPER(GROUP_LEVEL) IN(''LV3'', ''LV4'', ''CATEGORY'')
                                             '||V_SQL_CALIBER;
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;

--写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '当入参CODE为：LV2层级时，版本号为：'||V_VERSION_ID||'，所有子级数据删数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
    END IF;

--插入到指数表目标表
V_SQL := '
INSERT INTO '||V_TO_TABLE||'
      (
       YEAR,
       PERIOD_ID,
       BASE_PERIOD_ID,
       GROUP_CODE,
       GROUP_CN_NAME,
       GROUP_LEVEL,
       PRICE_INDEX,
       L4_CEG_CODE,
       L4_CEG_SHORT_CN_NAME,
       L3_CEG_CODE,
       L3_CEG_SHORT_CN_NAME,
       L2_CEG_CODE,
       L2_CEG_CN_NAME,
       VERSION_ID,
       '||V_CALIBER||'
       CREATED_BY,
       CREATION_DATE,
       LAST_UPDATED_BY,
       LAST_UPDATE_DATE,
       DEL_FLAG,
       L4_CEG_CN_NAME,
       L3_CEG_CN_NAME,
       PARENT_CODE,
       PARENT_CN_NAME,
       '||V_COLUMN||')
    SELECT YEAR,
           PERIOD_ID,
           BASE_PERIOD_ID,
           GROUP_CODE,
           GROUP_CN_NAME,
           GROUP_LEVEL,
           PRICE_INDEX,
           L4_CEG_CODE,
           L4_CEG_SHORT_CN_NAME,
           L3_CEG_CODE,
           L3_CEG_SHORT_CN_NAME,
           L2_CEG_CODE,
           L2_CEG_CN_NAME,
           VERSION_ID,
           '||V_CALIBER||'
           CREATED_BY,
           CREATION_DATE,
           LAST_UPDATED_BY,
           LAST_UPDATE_DATE,
           DEL_FLAG,
           L4_CEG_CN_NAME,
           L3_CEG_CN_NAME,
           PARENT_CODE,
           PARENT_CN_NAME,
           '||V_COLUMN||'
FROM '||V_MID_TABLE||' T1
--排除原先指数表里已经存在相同版本的数据情况，而不存在的就插入
WHERE NOT EXISTS (SELECT 1 FROM '||V_TO_TABLE||' T2 
                  WHERE T1.YEAR = T2.YEAR AND T1.PERIOD_ID = T2.PERIOD_ID 
                  AND T1.BASE_PERIOD_ID = T2.BASE_PERIOD_ID 
                  AND NVL(T1.GROUP_CODE,''S1'') = NVL(T2.GROUP_CODE,''S1'') 
                  AND NVL(T1.GROUP_CN_NAME,''S2'') = NVL(T2.GROUP_CN_NAME,''S2'') 
                  AND T1.VERSION_ID = T2.VERSION_ID 
                  AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
                  AND NVL(T1.PARENT_CODE,''SNULL'') = NVL(T2.PARENT_CODE,''SNULL'')
                  AND NVL(T1.'||V_COLUMN||',''S3'') = NVL(T2.'||V_COLUMN||',''S3'')
                  '||V_REL_CONDITION||')';
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;
                  
--写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '"FIN_DM_OPT_FOI".DM_FOI_PRICE_INDEX_T插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
  END IF;      -- 判断输入的CODE是否为ITEM层级，结束判断     

--------------------------------------------------------------------------点降基期切换逻辑-------------------------------------------------------------------------
 -- 变量重定义
  IF F_CALIBER_FLAG = 'I' AND V_FIELD_NAME = 'ITEM' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ITEM_SUP_AVG_T ';
  ELSIF F_CALIBER_FLAG = 'I' AND V_FIELD_NAME <> 'ITEM' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_T';
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T ';
  ELSIF F_CALIBER_FLAG = 'E' AND V_FIELD_NAME = 'ITEM' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_POINT_AMP_T'; 
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_ITEM_SUP_AVG_T ';
  ELSIF F_CALIBER_FLAG = 'E' AND V_FIELD_NAME <> 'ITEM' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_POINT_AMP_T'; 
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T ';
  ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') AND V_FIELD_NAME = 'ITEM' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_POINT_AMP_T'; 
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_ITEM_SUP_AVG_T ';
  ELSIF F_CALIBER_FLAG IN ('IAS','EAST_CHINA_PQC') AND V_FIELD_NAME <> 'ITEM' THEN
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_POINT_AMP_T'; 
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOI_IAS_ECPQC_MONTH_PRICE_INDEX_T ';
  END IF;

 -- 删除点降表数据
  V_SQL := '
  DELETE FROM '||V_TO_TABLE||' WHERE VERSION_ID = '||V_VERSION_ID||'
                               AND BASE_PERIOD_ID = '||V_CHANGE_BASE_PERIOD_ID||'
                               AND GROUP_CODE = '''||V_GROUP_CODE||'''
                               '||V_SQL_CALIBER;
  EXECUTE IMMEDIATE V_SQL;

--1.写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '产业标识为：'||F_CALIBER_FLAG||'，删除点降表数据完成,切换到基期为'||V_CHANGE_BASE_PERIOD_ID||',版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');   
  
 -- 输入CODE为ITEM层级时，从到货金额表取均本数据计算，否则从指数表取数计算
 IF V_GROUP_CODE IS NOT NULL AND V_CUSTOMIZATION_FLAG = 'N' THEN   
    IF V_FIELD_NAME = 'ITEM' THEN
        V_CATEGORY := 'CATEGORY_CODE,
                       CATEGORY_NAME,';
        V_BASE_VALUE := ' AVG_RECEIVE_AMT AS BASE_VALUE,';
        V_COMPUTE_VALUE := ' NVL((T1.AVG_RECEIVE_AMT/NULLIF(T2.BASE_VALUE,0)-1)*100,0)';
        V_SQL_CONDITION := '';
    ELSE 
        V_CATEGORY := '';
        V_BASE_VALUE := ' PRICE_INDEX AS BASE_PRICE_INDEX,';
        V_COMPUTE_VALUE := ' NVL((T1.PRICE_INDEX/NULLIF(T2.BASE_PRICE_INDEX,0)-1)*100,0)';
        V_SQL_CONDITION := ' AND T1.BASE_PERIOD_ID = '''||V_CHANGE_BASE_PERIOD_ID||'''';   -- 取对应基期月份的数据
    END IF;
 
 -- 计算切换基期后的点降数据
   V_SQL := '
   INSERT INTO '||V_TO_TABLE||'(
                VERSION_ID,
                YEAR,
                PERIOD_ID,
                BASE_PERIOD_ID,
                GROUP_CODE,
                GROUP_CN_NAME,
                GROUP_LEVEL,
                POINT_AMP,
               '||V_CATEGORY||'
                L4_CEG_CODE,
                L4_CEG_SHORT_CN_NAME,
                L4_CEG_CN_NAME,
                L3_CEG_CODE,
                L3_CEG_SHORT_CN_NAME,
                L3_CEG_CN_NAME,
                L2_CEG_CODE,
                L2_CEG_CN_NAME,
                PARENT_CODE,
                PARENT_CN_NAME,
                '||V_COLUMN||',
                '||V_CALIBER||'
                CREATED_BY,
                CREATION_DATE,
                LAST_UPDATED_BY,
                LAST_UPDATE_DATE,
                DEL_FLAG
                )
   SELECT T1.VERSION_ID,
          T1.YEAR,
          T1.PERIOD_ID,
          '||V_CHANGE_BASE_PERIOD_ID||' AS BASE_PERIOD_ID,
          T1.GROUP_CODE,
          T1.GROUP_CN_NAME,
          T1.GROUP_LEVEL,
          '||V_COMPUTE_VALUE||' AS POINT_AMP ,  -- 计算切换基期后的点降结果值
          '||V_CATEGORY||'
          T1.L4_CEG_CODE,
          T1.L4_CEG_SHORT_CN_NAME,
          T1.L4_CEG_CN_NAME,
          T1.L3_CEG_CODE,
          T1.L3_CEG_SHORT_CN_NAME,
          T1.L3_CEG_CN_NAME,
          T1.L2_CEG_CODE,
          T1.L2_CEG_CN_NAME,
          T1.PARENT_CODE,
          T1.PARENT_CN_NAME,
          T1.'||V_COLUMN||',
          '||V_IN_CALIBER||'
          -1 AS CREATED_BY,
          CURRENT_TIMESTAMP AS CREATION_DATE,
          -1 AS LAST_UPDATED_BY,
          CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
          ''N'' AS DEL_FLAG
        FROM '||V_FROM_TABLE||' T1
        LEFT JOIN(
                  SELECT PERIOD_ID,
                         VERSION_ID,
                         GROUP_CODE,
                         GROUP_LEVEL,
                         '||V_BASE_VALUE||'  -- 取切换后的基期的数据
                         PARENT_CODE,
                         '||V_COLUMN||'
                      FROM '||V_FROM_TABLE||' T1
                      WHERE PERIOD_ID = '||V_CHANGE_BASE_PERIOD_ID  -- 取基期月份的数据作为分母
                      ||V_SQL_CONDITION||'
                      '||V_SQL_CALIBER||'
                      AND GROUP_CODE = '''||V_GROUP_CODE||'''  -- 取GROUP_CODE为入参变量的数据
                      AND GROUP_LEVEL IN (''LV2'',''LV3'',''LV4'',''CATEGORY'',''ITEM'')
                      AND VERSION_ID = '||V_VERSION_ID||'
                  ) T2
        ON  T1.VERSION_ID = T2.VERSION_ID
        AND T1.GROUP_CODE = T2.GROUP_CODE
        AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
        AND NVL(T1.PARENT_CODE,''SNULL0'') = NVL(T2.PARENT_CODE,''SNULL0'')
        AND NVL(T1.'||V_COLUMN||',''SNULL'') = NVL(T2.'||V_COLUMN||',''SNULL'')
        WHERE T1.VERSION_ID = '||V_VERSION_ID
        ||V_SQL_CONDITION||'  -- 限制基期为切换后的基期
        AND T1.GROUP_LEVEL IN (''LV2'',''LV3'',''LV4'',''CATEGORY'',''ITEM'')
        AND T1.GROUP_CODE = '''||V_GROUP_CODE||'''
        '||V_SQL_CALIBER;
               
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL; 
  END IF; 

  
   -- 写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => 'FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_T点降数据插数完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
--3.写入日志
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '调用FIN_DM_OPT_FOI.F_POINT_DM_FOI_PRICE_INDEX_T完成'||'，：'||NVL(V_GROUP_CODE,V_GROUP_CN_NAME),
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');

    --4.收集信息
  IF F_CALIBER_FLAG = 'I' THEN
    ANALYSE FIN_DM_OPT_FOI.DM_FOI_PRICE_INDEX_T;   -- 指数表
    ANALYSE FIN_DM_OPT_FOI.DM_FOI_POINT_AMP_T;     -- 点降表
  ELSIF F_CALIBER_FLAG = 'E' THEN
    ANALYSE FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_PRICE_INDEX_T;   -- 指数表
    ANALYSE FIN_DM_OPT_FOI.DM_FOI_ENERGY_MONTH_POINT_AMP_T;     -- 点降表
  END IF;

    RETURN 'SUCCESS';
 
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END$$
/

