-- Name: f_dm_fcst_ict_mon_mid_cost_red_idx_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fcst_ict_mon_mid_cost_red_idx_t(f_cost_type character varying, f_keystr text, f_version_id character varying, OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$


/***************************************************************************************************************************************************************

------降成本指数中间表 (计算SPART层级)

参数描述: 参数一: F_COST_TYPE 成本类型:PSP PSP成本; STD: 标准成本
		参数二: F_KEYSTR	密钥
		参数三: F_VERSION_ID 版本号
		参数四: X_RESULT_STATUS 运行状态返回值 ‘1’为成功，‘0’为失败

--------------------来源表
----------分子
----累积月均本表
取数方式:取全量 
重量级团队目录-PSP:		DM_FCST_ICT_PSP_IRB_SUM_DETAIL_SPART_T	--PSP IRB                                               
重量级团队目录-STD:		DM_FCST_ICT_STD_IRB_SUM_DETAIL_SPART_T	--STD IRB
----补齐月均本表
取数方式:取全量 
重量级团队目录-PSP:		DM_FCST_ICT_PSP_IRB_BASE_DETAIL_SPART_T	--PSP IRB                                               
重量级团队目录-STD:		DM_FCST_ICT_STD_IRB_BASE_DETAIL_SPART_T	--STD IRB
----------分母
----年均本数据
取数方式:取近三年+去'全选'
重量级团队目录-PSP:		DM_FCST_ICT_PSP_IRB_ANNL_AVG_T		--PSP IRB
重量级团队目录-STD:		DM_FCST_ICT_STD_IRB_ANNL_AVG_T		--PSP IRB
--TOP_SPART关联
取数方式:最新版本
重量级团队目录-PSP:		DM_FCST_ICT_PSP_IRB_TOP_SPART_INFO_T	--PSP IRB
重量级团队目录-STD:		DM_FCST_ICT_STD_IRB_TOP_SPART_INFO_T	--PSP IRB
--------------------目标表
--降成本指数中间表
重量级团队目录-PSP:		DM_FCST_ICT_PSP_MON_MID_COST_RED_IDX_T	--PSP
重量级团队目录-STD:		DM_FCST_ICT_STD_MON_MID_COST_RED_IDX_T	--STD
--事例
SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_RED_IDX_T('PSP','','');	--PSP一个版本数据
SELECT FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_RED_IDX_T('STD','密钥','');	--标准成本一个版本数据

****************************************************************************************************************************************************************/
DECLARE
  V_SP_NAME                  VARCHAR(200) := 'FIN_DM_OPT_FOI.F_DM_FCST_ICT_MON_MID_COST_RED_IDX_T';
  V_VERSION                  VARCHAR(20);
  V_RED_DIM_VERSION			 VARCHAR(10);
  V_EXCEPTION_FLAG           VARCHAR(2);
  V_FROM_ANNL_AVG_TABLE      VARCHAR(200);
  V_FROM_TOP_SPART_TABLE     VARCHAR(200);
  V_FROM_MONTH_YTD_AVG_TABLE VARCHAR(200);
  V_FROM_MONTH_AVG_TABLE     VARCHAR(200);
  V_TO_TABLE                 VARCHAR(200);
  V_FROM_ANNL_TEMP_TABLE     VARCHAR(200);
  V_COST_AMT                 TEXT;
  V_RMB_AVG_AMT              TEXT;
  V_JOIN_DIMENSION_CODE3     TEXT;
  V_JOIN_DIMENSION_CODE      TEXT;
  V_SQL_DIMENSION_PART       TEXT;
  V_DIMENSION_PART           TEXT;
  V_JOIN_OTHER_DIM_PART3     TEXT;
  V_JOIN_PBI_CODE3           TEXT;
  V_JOIN_OTHER_DIM_PART      TEXT;
  V_SQL_OTHER_DIM_PART       TEXT;
  V_OTHER_DIM_PART           TEXT;
  V_JOIN_PBI_CODE            TEXT;
  V_SQL_PBI_PART             TEXT;
  V_PBI_PART                 TEXT;
  V_SQL                      TEXT;
  V_SP_NUM				  	  BIGINT := 0; --步骤号

BEGIN 

RAISE NOTICE '取版本号';
--版本表
  IF F_VERSION_ID IS NULL THEN
    SELECT VERSION_ID
      INTO V_VERSION
      FROM FIN_DM_OPT_FOI.DM_FCST_ICT_VERSION_INFO_T
     WHERE DEL_FLAG = 'N'
       AND STATUS = 1
       AND UPPER(DATA_TYPE) = 'MONTH'
     ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;
  ELSE
    V_VERSION := F_VERSION_ID;
  END IF;
  

----变量赋值
--表名赋值
  V_FROM_ANNL_AVG_TABLE      := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_IRB_ANNL_AVG_T';
  V_FROM_TOP_SPART_TABLE     := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_IRB_TOP_SPART_INFO_T';
  V_FROM_MONTH_YTD_AVG_TABLE := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_IRB_SUM_DETAIL_SPART_T';
  V_FROM_MONTH_AVG_TABLE     := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_IRB_BASE_DETAIL_SPART_T';
  V_TO_TABLE                 := 'FIN_DM_OPT_FOI.DM_FCST_ICT_'||F_COST_TYPE||'_MON_MID_COST_RED_IDX_T';
  
  V_PBI_PART:='
			LV0_PROD_RND_TEAM_CODE,
			LV1_PROD_RND_TEAM_CODE,
			LV2_PROD_RND_TEAM_CODE,
			LV3_PROD_RND_TEAM_CODE,
			LV4_PROD_RND_TEAM_CODE,
			LV0_PROD_RD_TEAM_CN_NAME,
			LV1_PROD_RD_TEAM_CN_NAME,
			LV2_PROD_RD_TEAM_CN_NAME,
			LV3_PROD_RD_TEAM_CN_NAME,
			LV4_PROD_RD_TEAM_CN_NAME,
			';
			
  V_SQL_PBI_PART:='
			T1.LV0_PROD_RND_TEAM_CODE,
			T1.LV1_PROD_RND_TEAM_CODE,
			T1.LV2_PROD_RND_TEAM_CODE,
			T1.LV3_PROD_RND_TEAM_CODE,
			T1.LV4_PROD_RND_TEAM_CODE,
			T1.LV0_PROD_RD_TEAM_CN_NAME,
			T1.LV1_PROD_RD_TEAM_CN_NAME,
			T1.LV2_PROD_RD_TEAM_CN_NAME,
			T1.LV3_PROD_RD_TEAM_CN_NAME,
			T1.LV4_PROD_RD_TEAM_CN_NAME,
			';
  V_JOIN_PBI_CODE:='
			AND T1.LV0_PROD_RND_TEAM_CODE	= T2.LV0_PROD_RND_TEAM_CODE
			AND T1.LV1_PROD_RND_TEAM_CODE   = T2.LV1_PROD_RND_TEAM_CODE
			AND T1.LV2_PROD_RND_TEAM_CODE   = T2.LV2_PROD_RND_TEAM_CODE
			AND T1.LV3_PROD_RND_TEAM_CODE   = T2.LV3_PROD_RND_TEAM_CODE
			AND T1.LV4_PROD_RND_TEAM_CODE   = T2.LV4_PROD_RND_TEAM_CODE
			';
  V_OTHER_DIM_PART:='
			REGION_CODE,
			REGION_CN_NAME,
			REPOFFICE_CODE,
			REPOFFICE_CN_NAME,
			BG_CODE,
			BG_CN_NAME,
			OVERSEA_FLAG,
			';
  V_SQL_OTHER_DIM_PART:='
			T1.REGION_CODE,
			T1.REGION_CN_NAME,
			T1.REPOFFICE_CODE,
			T1.REPOFFICE_CN_NAME,
			T1.BG_CODE,
			T1.BG_CN_NAME,
			T1.OVERSEA_FLAG,
			';
  V_JOIN_OTHER_DIM_PART:='
		AND T1.REGION_CODE = T2.REGION_CODE
		AND T1.REPOFFICE_CODE = T2.REPOFFICE_CODE
		AND T1.BG_CODE = T2.BG_CODE
		AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG 
		';
		
  V_JOIN_PBI_CODE3:='
			AND T1.LV0_PROD_RND_TEAM_CODE	= T3.LV0_PROD_RND_TEAM_CODE
			AND T1.LV1_PROD_RND_TEAM_CODE   = T3.LV1_PROD_RND_TEAM_CODE
			AND T1.LV2_PROD_RND_TEAM_CODE   = T3.LV2_PROD_RND_TEAM_CODE
			AND T1.LV3_PROD_RND_TEAM_CODE   = T3.LV3_PROD_RND_TEAM_CODE
			AND T1.LV4_PROD_RND_TEAM_CODE   = T3.LV4_PROD_RND_TEAM_CODE
			';
			
  V_JOIN_OTHER_DIM_PART3:='
		AND T1.REGION_CODE = 	T3.REGION_CODE
		AND T1.REPOFFICE_CODE = T3.REPOFFICE_CODE
		AND T1.BG_CODE = 		T3.BG_CODE
		AND T1.OVERSEA_FLAG =	T3.OVERSEA_FLAG 
		';
		
  V_DIMENSION_PART:='
					DIMENSION_CODE,
					DIMENSION_CN_NAME,
					DIMENSION_SUBCATEGORY_CODE,
					DIMENSION_SUBCATEGORY_CN_NAME,
					DIMENSION_SUB_DETAIL_CODE,
					DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_SQL_DIMENSION_PART:='
					T1.DIMENSION_CODE,
					T1.DIMENSION_CN_NAME,
					T1.DIMENSION_SUBCATEGORY_CODE,
					T1.DIMENSION_SUBCATEGORY_CN_NAME,
					T1.DIMENSION_SUB_DETAIL_CODE,
					T1.DIMENSION_SUB_DETAIL_CN_NAME,
					';
  V_JOIN_DIMENSION_CODE:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T2.DIMENSION_CODE,''DC'')		
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';
  V_JOIN_DIMENSION_CODE3:='
					AND NVL(T1.DIMENSION_CODE,''DC'') = NVL(T3.DIMENSION_CODE,''DC'')		
					AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,''DSC'') = NVL(T3.DIMENSION_SUBCATEGORY_CODE,''DSC'')
					AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,''DSDC'') = NVL(T3.DIMENSION_SUB_DETAIL_CODE,''DSDC'')
					';
			
			
--是否解密判断
  IF F_COST_TYPE = 'PSP' THEN
    V_COST_AMT              := 'RMB_AVG_AMT,';
    V_FROM_ANNL_TEMP_TABLE  := 'DM_TOP_ANNL_AVG_TEMP';
    V_RMB_AVG_AMT           := 'RMB_AVG_AMT,';
  ELSIF F_COST_TYPE = 'STD' THEN
    V_COST_AMT				:= 'STD_COST_AVG,';
    V_FROM_ANNL_TEMP_TABLE	:= 'DM_DECRYPT_TOP_ANNL_AVG_TEMP';
    V_RMB_AVG_AMT			:= '
							   TO_NUMBER(GS_DECRYPT(RMB_AVG_AMT,
									 '''||F_KEYSTR||''',
									 ''aes128'',
									 ''cbc'',
									 ''sha256'')) AS RMB_AVG_AMT,
							';
  END IF;

 
RAISE NOTICE '年均本关联，落表';
--建表     
DROP TABLE IF EXISTS DM_TOP_ANNL_AVG_TEMP;
CREATE TEMPORARY TABLE DM_TOP_ANNL_AVG_TEMP(
 LV0_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV1_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV2_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV3_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV4_PROD_RND_TEAM_CODE		VARCHAR(50),	
 LV0_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
 LV1_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV2_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV3_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV4_PROD_RD_TEAM_CN_NAME	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 STD_COST_AVG		VARCHAR(2000) ,
 RMB_AVG_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),		--是否主力编码 
 CODE_ATTRIBUTES    VARCHAR(20)   ,
 SOFTWARE_MARK      VARCHAR(30) 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);
	  	  
--落表
V_SQL:='
  INSERT INTO DM_TOP_ANNL_AVG_TEMP
    (PERIOD_YEAR,
     '||V_PBI_PART||V_DIMENSION_PART||' 
	 SPART_CODE,
     SPART_CN_NAME,
     '||V_COST_AMT||' 
	 '||V_OTHER_DIM_PART||' 
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 VIEW_FLAG,
	 SOFTWARE_MARK)
    SELECT T1.PERIOD_YEAR,
           '||V_SQL_PBI_PART||V_SQL_DIMENSION_PART||' 
		   T1.SPART_CODE,
           T1.SPART_CN_NAME,
           T1.RMB_AVG_AMT,		--20240809修改
           '||V_SQL_OTHER_DIM_PART||' 
		   T2.MAIN_FLAG,
           T2.CODE_ATTRIBUTES,
		   T1.VIEW_FLAG,
		   T1.SOFTWARE_MARK --202410版本 新增软硬件标识
      FROM '||V_FROM_ANNL_AVG_TABLE||' T1
      LEFT JOIN '||V_FROM_TOP_SPART_TABLE||' T2
        ON T1.SPART_CODE = T2.TOP_SPART_CODE
		AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK 		--202410版本 新增软硬件标识
		AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
		AND T1.VIEW_FLAG = ''PROD_SPART''
     '||V_JOIN_PBI_CODE||V_JOIN_OTHER_DIM_PART||'
     WHERE T1.PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 3) AND
           YEAR(CURRENT_DATE)
       AND T2.VERSION_ID = '||V_VERSION||'
	   AND T1.YTD_FLAG = ''N''
	   AND T1.VIEW_FLAG = ''PROD_SPART''
       AND (T1.CODE_ATTRIBUTES <> ''全选'' OR T1.MAIN_FLAG = ''N'')
	   AND T2.DOUBLE_FLAG = ''Y''
       AND T2.IS_TOP_FLAG = ''Y''
  UNION ALL
    SELECT T1.PERIOD_YEAR,
           '||V_SQL_PBI_PART||V_SQL_DIMENSION_PART||' 
		   T1.SPART_CODE,
           T1.SPART_CN_NAME,
           T1.RMB_AVG_AMT,		--20240809修改
           '||V_SQL_OTHER_DIM_PART||'
		   ''N'' AS MAIN_FLAG,
           '''' AS CODE_ATTRIBUTES,
		   T1.VIEW_FLAG,
		   T1.SOFTWARE_MARK
      FROM '||V_FROM_ANNL_AVG_TABLE||' T1
     WHERE T1.PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 3) AND
           YEAR(CURRENT_DATE)
	   AND T1.YTD_FLAG = ''N''
       AND T1.VIEW_FLAG = ''DIMENSION'';
	   ';

	   
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

  --写入日志
  V_SP_NUM := V_SP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => '年均本表插数完成,成本类型：'||f_cost_type,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 
 

IF F_COST_TYPE = 'STD' THEN 
RAISE NOTICE '年均本(STD解密)落表 ';
--建表
DROP TABLE IF EXISTS DM_DECRYPT_TOP_ANNL_AVG_TEMP;
CREATE TEMPORARY TABLE DM_DECRYPT_TOP_ANNL_AVG_TEMP(
 LV0_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV1_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV2_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV3_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV4_PROD_RND_TEAM_CODE		VARCHAR(50),	
 LV0_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
 LV1_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV2_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV3_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV4_PROD_RD_TEAM_CN_NAME	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_AVG_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 MAIN_FLAG			VARCHAR(1),		--是否主力编码 
 CODE_ATTRIBUTES    VARCHAR(20)  ,
 SOFTWARE_MARK       VARCHAR(30) 
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--解密落表
V_SQL:='
  INSERT INTO DM_DECRYPT_TOP_ANNL_AVG_TEMP
    (PERIOD_YEAR,
     '||V_PBI_PART||V_DIMENSION_PART||'
	 SPART_CODE,
     SPART_CN_NAME,
	 RMB_AVG_AMT,
	 '||V_OTHER_DIM_PART||' 
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 VIEW_FLAG,
	 SOFTWARE_MARK)
    SELECT PERIOD_YEAR,
           '||V_PBI_PART||V_DIMENSION_PART||' 
		   SPART_CODE,
           SPART_CN_NAME,
           TO_NUMBER(GS_DECRYPT(STD_COST_AVG,
                     '''||F_KEYSTR||''',
                     ''aes128'',
                     ''cbc'',
                     ''sha256'')) AS RMB_AVG_AMT,
           '||V_OTHER_DIM_PART||' 
		   MAIN_FLAG,
           CODE_ATTRIBUTES,
		   VIEW_FLAG,
		   SOFTWARE_MARK
      FROM DM_TOP_ANNL_AVG_TEMP 

';
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;


  --写入日志
  V_SP_NUM := V_SP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => '年均本表插数完成,成本类型：'||f_cost_type,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

END IF ;

RAISE NOTICE '月均本(STD解密)落表';
--建表
DROP TABLE IF EXISTS DM_TOP_MONTH_AVG_TEMP;
CREATE TEMPORARY TABLE DM_TOP_MONTH_AVG_TEMP(
 LV0_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV1_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV2_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV3_PROD_RND_TEAM_CODE		VARCHAR(50),
 LV4_PROD_RND_TEAM_CODE		VARCHAR(50),	
 LV0_PROD_RD_TEAM_CN_NAME	VARCHAR(200),
 LV1_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV2_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV3_PROD_RD_TEAM_CN_NAME  	VARCHAR(200),
 LV4_PROD_RD_TEAM_CN_NAME	VARCHAR(200), 
 DIMENSION_CODE	VARCHAR(50),
 DIMENSION_CN_NAME	VARCHAR(200),
 DIMENSION_SUBCATEGORY_CODE	VARCHAR(50),
 DIMENSION_SUBCATEGORY_CN_NAME	VARCHAR(200),
 DIMENSION_SUB_DETAIL_CODE	VARCHAR(50),
 DIMENSION_SUB_DETAIL_CN_NAME	VARCHAR(200),
 VIEW_FLAG			VARCHAR(20),
 PERIOD_YEAR		INT,
 PERIOD_ID			INT,
 SPART_CODE			VARCHAR(50)  ,
 SPART_CN_NAME		VARCHAR(200) , 
 RMB_AVG_AMT		NUMERIC,
 REGION_CODE		VARCHAR(50)  ,
 REGION_CN_NAME		VARCHAR(200) , 
 REPOFFICE_CODE		VARCHAR(50)  ,
 REPOFFICE_CN_NAME	VARCHAR(200) , 
 BG_CODE			VARCHAR(50)  ,
 BG_CN_NAME         VARCHAR(200) , 
 OVERSEA_FLAG		VARCHAR(10),
 AVG_TYPE			VARCHAR(10),	
 MAIN_FLAG			VARCHAR(1),		--是否主力编码 
 CODE_ATTRIBUTES    VARCHAR(20),
 SOFTWARE_MARK 		VARCHAR(30)
)
ON COMMIT PRESERVE ROWS
DISTRIBUTE BY HASH(SPART_CODE,OVERSEA_FLAG);

--落表
V_SQL:='
  INSERT INTO DM_TOP_MONTH_AVG_TEMP
    (PERIOD_ID,
     PERIOD_YEAR,
     '||V_PBI_PART||V_DIMENSION_PART||'
	 SPART_CODE,
     SPART_CN_NAME,
	 RMB_AVG_AMT,
	 '||V_OTHER_DIM_PART||'
	 AVG_TYPE,
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 VIEW_FLAG,
	 SOFTWARE_MARK)
    SELECT PERIOD_ID,
           PERIOD_YEAR,
           '||V_PBI_PART||V_DIMENSION_PART||'
		   SPART_CODE,
           SPART_CN_NAME,
		   '||V_RMB_AVG_AMT||'		--STD解密 PSP不解密
           '||V_OTHER_DIM_PART||' 
		   ''YTD'' AS AVG_TYPE,
		   MAIN_FLAG,
           CODE_ATTRIBUTES,
		   VIEW_FLAG,
		   SOFTWARE_MARK
      FROM '||V_FROM_MONTH_YTD_AVG_TABLE||'
  UNION ALL
    SELECT PERIOD_ID,
           PERIOD_YEAR,
           '||V_PBI_PART||V_DIMENSION_PART||'
		   SPART_CODE,
           SPART_CN_NAME,
		   '||V_RMB_AVG_AMT||'
           '||V_OTHER_DIM_PART||' 
		   ''MONTH'' AS AVG_TYPE,
		   MAIN_FLAG,
           CODE_ATTRIBUTES,
		   VIEW_FLAG,
		   SOFTWARE_MARK
      FROM '||V_FROM_MONTH_AVG_TABLE||' 
     WHERE PERIOD_YEAR BETWEEN (YEAR(CURRENT_DATE) - 2) AND
           YEAR(CURRENT_DATE)
';
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

  --写入日志
  V_SP_NUM := V_SP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => '月均本表插数完成,成本类型：'||f_cost_type,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

RAISE NOTICE '清空结果表';
----清空结果表
V_SQL:= 'TRUNCATE TABLE '||V_TO_TABLE;
EXECUTE V_SQL;

RAISE NOTICE '月均本关联年均本+计算';
--月均本关联年均本 计算全量数据 + 
--循环取数 主力编码(区分编码属性) + 
--循环+1取数 主力编码(写死编码属性'全选')

  SELECT VERSION_ID
    INTO V_RED_DIM_VERSION
    FROM DM_FCST_ICT_VERSION_INFO_T
   WHERE DEL_FLAG = 'N'
     AND STATUS = 0
     AND UPPER(DATA_TYPE) = 'RED_DIM'
   ORDER BY LAST_UPDATE_DATE DESC LIMIT 1;

V_SQL:='
    WITH BASE_DB AS
     (SELECT T1.PERIOD_ID,
             T1.PERIOD_YEAR,
             '||V_SQL_PBI_PART||V_SQL_DIMENSION_PART||' 
			 T1.SPART_CODE,
             T1.SPART_CN_NAME,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.SPART_CODE,T1.DIMENSION_SUB_DETAIL_CODE) AS GROUP_CODE,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.SPART_CN_NAME,T1.DIMENSION_SUB_DETAIL_CN_NAME) AS GROUP_CN_NAME,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',''SPART'',''SUB_DETAIL'') AS GROUP_LEVEL,
             NVL(T1.RMB_AVG_AMT / NULLIF(T3.RMB_AVG_AMT, 0) * 100,0) AS COST_INDEX,
             NVL(T2.RMB_AVG_AMT / NULLIF(T3.RMB_AVG_AMT, 0) * 100,0) AS YTD_COST_INDEX,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.LV4_PROD_RND_TEAM_CODE,T1.DIMENSION_SUBCATEGORY_CODE) AS PARENT_CODE,
			 DECODE(T1.VIEW_FLAG,''PROD_SPART'',T1.LV4_PROD_RD_TEAM_CN_NAME,T1.DIMENSION_SUBCATEGORY_CN_NAME) AS PARENT_CN_NAME,
             '||V_SQL_OTHER_DIM_PART||' 
			 T1.MAIN_FLAG,
             T1.CODE_ATTRIBUTES,
			 T1.VIEW_FLAG,
			 T1.SOFTWARE_MARK
        FROM DM_TOP_MONTH_AVG_TEMP T1 
        LEFT JOIN DM_TOP_MONTH_AVG_TEMP T2 
          ON NVL(T1.SPART_CODE,''SC'') = NVL(T2.SPART_CODE,''SC'')
         AND T1.PERIOD_YEAR = T2.PERIOD_YEAR
		 AND T1.SOFTWARE_MARK = T2.SOFTWARE_MARK
		 AND T1.PERIOD_ID = T2.PERIOD_ID
		 AND T2.AVG_TYPE = ''YTD''
		 AND T1.AVG_TYPE = ''MONTH''
		 AND T1.VIEW_FLAG = T2.VIEW_FLAG
		 AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T2.MAIN_FLAG,''MF'')
		 AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T2.CODE_ATTRIBUTES,''CA'')
		'||V_JOIN_PBI_CODE||V_JOIN_OTHER_DIM_PART||V_JOIN_DIMENSION_CODE||'
        LEFT JOIN '||V_FROM_ANNL_TEMP_TABLE||' T3
          ON NVL(T1.SPART_CODE,''SC'') = NVL(T3.SPART_CODE,''SC'')
         AND (TO_NUMBER(T1.PERIOD_YEAR)-1) = T3.PERIOD_YEAR 	--20240809修改
		 AND T1.VIEW_FLAG = T3.VIEW_FLAG
		 AND T1.SOFTWARE_MARK = T3.SOFTWARE_MARK
		 AND NVL(T1.MAIN_FLAG,''MF'') = NVL(T3.MAIN_FLAG,''MF'')
		 AND NVL(T1.CODE_ATTRIBUTES,''CA'') = NVL(T3.CODE_ATTRIBUTES,''CA'')
		'||V_JOIN_PBI_CODE3||V_JOIN_OTHER_DIM_PART3||V_JOIN_DIMENSION_CODE3||'
		WHERE T2.AVG_TYPE = ''YTD'' AND T1.AVG_TYPE = ''MONTH''
		)
		  
		
  INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     PERIOD_ID,
     PERIOD_YEAR,
	 PROD_RND_TEAM_CODE,
	 PROD_RD_TEAM_CN_NAME,
     '||V_PBI_PART||V_DIMENSION_PART||' 
	 SPART_CODE,
     SPART_CN_NAME,
	 GROUP_CODE,
	 GROUP_CN_NAME,
	 GROUP_LEVEL,
     COST_INDEX,
     YTD_COST_INDEX,
	 VIEW_FLAG,
	 PARENT_CODE,
	 PARENT_CN_NAME,
     '||V_OTHER_DIM_PART||' 
	 MAIN_FLAG,
     CODE_ATTRIBUTES,
	 SOFTWARE_MARK,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG)
  --普通全量数据
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_ID,
           PERIOD_YEAR,
		   LV4_PROD_RND_TEAM_CODE,
		   LV4_PROD_RD_TEAM_CN_NAME,
           '||V_PBI_PART||V_DIMENSION_PART||' 
		   SPART_CODE,
           SPART_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_INDEX,
           YTD_COST_INDEX,
		   VIEW_FLAG,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           '||V_OTHER_DIM_PART||'
		   ''N'' AS MAIN_FLAG,
           '''' AS CODE_ATTRIBUTES,
		   SOFTWARE_MARK,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_DB
    UNION ALL
   --主力编码数据
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_ID,
           PERIOD_YEAR,
		   LV4_PROD_RND_TEAM_CODE,
		   LV4_PROD_RD_TEAM_CN_NAME,
           '||V_PBI_PART||V_DIMENSION_PART||' 
		   SPART_CODE,
           SPART_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_INDEX,
           YTD_COST_INDEX,
		   VIEW_FLAG,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           '||V_OTHER_DIM_PART||'
		   ''Y'' AS MAIN_FLAG,
           CODE_ATTRIBUTES,
		   SOFTWARE_MARK,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_DB
     WHERE MAIN_FLAG = ''Y''
	   AND VIEW_FLAG = ''PROD_SPART''
    UNION ALL
  --主力编码-编码属性‘全选’
    SELECT '||V_VERSION||' AS VERSION_ID,
           PERIOD_ID,
           PERIOD_YEAR,
		   LV4_PROD_RND_TEAM_CODE,
		   LV4_PROD_RD_TEAM_CN_NAME,
           '||V_PBI_PART||V_DIMENSION_PART||' 
		   SPART_CODE,
           SPART_CN_NAME,
		   GROUP_CODE,
		   GROUP_CN_NAME,
		   GROUP_LEVEL,
           COST_INDEX,
           YTD_COST_INDEX,
		   VIEW_FLAG,
		   PARENT_CODE,
		   PARENT_CN_NAME,
           '||V_OTHER_DIM_PART||'
		   ''Y'' AS MAIN_FLAG,
           ''全选'' AS CODE_ATTRIBUTES,
		   SOFTWARE_MARK,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG
      FROM BASE_DB
     WHERE MAIN_FLAG = ''Y''
	   AND VIEW_FLAG = ''PROD_SPART'';
	   ';
	   
DBMS_OUTPUT.PUT_LINE(V_SQL);
EXECUTE V_SQL;

  --写入日志
  V_SP_NUM := V_SP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => '表插数完成,成本类型：'||f_cost_type,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS'); 

RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_STEP_NUM => V_SP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'第'||V_SP_NUM||'步'||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );

 END; 

$$
/

