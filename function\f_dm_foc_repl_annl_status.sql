-- Name: f_dm_foc_repl_annl_status; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_repl_annl_status(f_version_id bigint DEFAULT NULL::bigint, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
  创建时间：2024年9月14日
  创建人  ：唐钦
  背景描述：根据年均本和权重值，计算研发替代指数逻辑
  参数描述：X_RESULT_STATUS ：是否成功
  事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_REPL_ANNL_STATUS();
  变更示例-202503：
  修复状态码问题，去除视角关联
*/
DECLARE
  V_SP_NAME    VARCHAR2(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_REPL_ANNL_STATUS'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_VERSION_ID BIGINT; --版本号ID
  V_SQL TEXT; --执行语句
  V_YEAR INT := YEAR(CURRENT_DATE);
  V_GROUP_LEVEL VARCHAR(200);
  V_LAST_YEAR_FLAG VARCHAR2(100); 
  V_YEAR_FLAG VARCHAR2(100);
  V_YEAR_APPEND VARCHAR2(100);
  
BEGIN
  X_RESULT_STATUS = '1';
  
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
   
  -- 取出对应的版本号
  IF F_VERSION_ID IS NULL THEN 
    SELECT VERSION_ID INTO V_VERSION_ID   -- 年度版本号
    FROM
        FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1;
  ELSE V_VERSION_ID := F_VERSION_ID;
  END IF;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '取到最新年度版本号为：'||V_VERSION_ID,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 删除对应版本的结果表数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T WHERE VERSION_ID = '||V_VERSION_ID;
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_STATUS_T WHERE VERSION_ID = '||V_VERSION_ID;

  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除版本号为：'||V_VERSION_ID||'的FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T的表',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
   -- 创建临时表
     DROP TABLE IF EXISTS FOC_REPL_BASE_TMP;
     CREATE TEMPORARY TABLE FOC_REPL_BASE_TMP (
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         REPLACEMENT_GROUP_ID          VARCHAR(50),
         REPLACEMENT_GROUP_CN_NAME     VARCHAR(500),
         LAST_THREE_YEAR_FLAG          VARCHAR(50),
         LAST_THREE_APPEND_YEAR        VARCHAR(50),
         LAST_TWO_YEAR_FLAG            VARCHAR(50),
         LAST_TWO_APPEND_YEAR          VARCHAR(50),
         LAST_YEAR_FLAG                VARCHAR(50),
         LAST_APPEND_YEAR              VARCHAR(50),
         CURRENT_YEAR_FLAG             VARCHAR(50),
         CURRENT_APPEND_YEAR           VARCHAR(50),
         VIEW_FLAG                     VARCHAR(50),
         CALIBER_FLAG                  VARCHAR(50),
         DATA_TYPE                     VARCHAR(50)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(REPLACEMENT_GROUP_ID);
     
     DROP TABLE IF EXISTS FOC_REPL_STATUS_TMP;
     CREATE TEMPORARY TABLE FOC_REPL_STATUS_TMP (
         PERIOD_YEAR                   INT,
         LV0_CODE                      VARCHAR(50),
         LV0_CN_NAME                   VARCHAR(200),
         LV1_CODE                      VARCHAR(50),
         LV1_CN_NAME                   VARCHAR(200),
         LV2_CODE                      VARCHAR(50),
         LV2_CN_NAME                   VARCHAR(200),
         LV3_CODE                      VARCHAR(50),
         LV3_CN_NAME                   VARCHAR(200),
         REPLACEMENT_GROUP_ID          VARCHAR(50),
         REPLACEMENT_GROUP_CN_NAME     VARCHAR(500),
         STATUS_CODE                   INT,
         APPEND_YEAR                   INT,
         VIEW_FLAG                     VARCHAR(50),
         CALIBER_FLAG                  VARCHAR(50),
         DATA_TYPE                     VARCHAR(50)
     )
     ON COMMIT PRESERVE ROWS
     DISTRIBUTE BY HASH(REPLACEMENT_GROUP_ID);
     DBMS_OUTPUT.PUT_LINE('创建临时表成功');
     
  --写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '最细粒度层级缺失情况临时表创建完成',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 将基础数据计算得到，并插入临时表
  INSERT INTO FOC_REPL_BASE_TMP(
         LV0_CODE,                 
         LV0_CN_NAME,              
         LV1_CODE,                 
         LV1_CN_NAME,              
         LV2_CODE,                 
         LV2_CN_NAME,              
         LV3_CODE,                 
         LV3_CN_NAME,              
         REPLACEMENT_GROUP_ID,     
         REPLACEMENT_GROUP_CN_NAME,
         LAST_THREE_YEAR_FLAG,     
         LAST_THREE_APPEND_YEAR,   
         LAST_TWO_YEAR_FLAG,       
         LAST_TWO_APPEND_YEAR,     
         LAST_YEAR_FLAG,           
         LAST_APPEND_YEAR,         
         CURRENT_YEAR_FLAG,        
         CURRENT_APPEND_YEAR,      
         VIEW_FLAG,                
         CALIBER_FLAG,             
         DATA_TYPE 
  )
    SELECT LV0_PROD_RND_TEAM_CODE,                 
         LV0_PROD_RD_TEAM_CN_NAME,              
         LV1_PROD_RND_TEAM_CODE,                 
         LV1_PROD_RD_TEAM_CN_NAME,              
         LV2_PROD_RND_TEAM_CODE,                 
         LV2_PROD_RD_TEAM_CN_NAME,              
         LV3_PROD_RND_TEAM_CODE,                 
         LV3_PROD_RD_TEAM_CN_NAME,              
         REPLACEMENT_GROUP_ID,     
         REPLACEMENT_GROUP_CN_NAME,
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
         VIEW_FLAG,                
         CALIBER_FLAG,             
         'TOTAL' AS DATA_TYPE
     FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T
     WHERE VERSION_ID = V_VERSION_ID
      AND PERIOD_ID IN (
                          SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T
                             WHERE APPEND_FLAG = 'N'        -- 当前年份有数据的，取每年非补齐的最大月份数据
                          UNION ALL 
                            SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T
                             WHERE PERIOD_YEAR IN (  
                                                   SELECT PERIOD_YEAR FROM(   
                                                   SELECT PERIOD_YEAR,SUM(APP_FLAG) AS APP_FLAG FROM(   
                                                   SELECT PERIOD_YEAR,DECODE(APPEND_FLAG,'Y',0,1) AS APP_FLAG FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T 
                                                   ) GROUP BY PERIOD_YEAR     -- 为0的时候，表示整年无数据
                                                   )WHERE APP_FLAG = 0
                                                   )    -- 整年没有数据，即取最大月份的补齐数据
                         )
     GROUP BY LV0_PROD_RND_TEAM_CODE,                 
              LV0_PROD_RD_TEAM_CN_NAME,              
              LV1_PROD_RND_TEAM_CODE,                 
              LV1_PROD_RD_TEAM_CN_NAME,              
              LV2_PROD_RND_TEAM_CODE,                 
              LV2_PROD_RD_TEAM_CN_NAME,              
              LV3_PROD_RND_TEAM_CODE,                 
              LV3_PROD_RD_TEAM_CN_NAME,              
              REPLACEMENT_GROUP_ID,     
              REPLACEMENT_GROUP_CN_NAME,
              VIEW_FLAG,                
              CALIBER_FLAG
  UNION ALL 
  SELECT LV0_PROD_RND_TEAM_CODE,                 
         LV0_PROD_RD_TEAM_CN_NAME,              
         LV1_PROD_RND_TEAM_CODE,                 
         LV1_PROD_RD_TEAM_CN_NAME,              
         LV2_PROD_RND_TEAM_CODE,                 
         LV2_PROD_RD_TEAM_CN_NAME,              
         LV3_PROD_RND_TEAM_CODE,                 
         LV3_PROD_RD_TEAM_CN_NAME,             
         REPLACEMENT_GROUP_ID,     
         REPLACEMENT_GROUP_CN_NAME,
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_THREE_YEAR_FLAG,  -- 为当年-3年时，赋予该年数据为补齐时赋1，否则赋0
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-3 THEN APPEND_YEAR ELSE 0 END) AS LAST_THREE_APPEND_YEAR,                                  -- 为当年-3年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_TWO_YEAR_FLAG,       -- 为当年-2年时，赋予该年数据为补齐时赋1，否则赋0
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-2 THEN APPEND_YEAR ELSE 0 END) AS LAST_TWO_APPEND_YEAR,                                      -- 为当年-2年时，取到对应向前补齐数据的补齐年份，若为原始数据或是向后补齐的，则为NULL
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS LAST_YEAR_FLAG,  
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR-1 THEN APPEND_YEAR ELSE 0 END) AS LAST_APPEND_YEAR, 
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN DECODE(APPEND_FLAG,'Y',1,0) ELSE 0 END) AS CURRENT_YEAR_FLAG,  
         SUM( CASE WHEN PERIOD_YEAR = V_YEAR THEN APPEND_YEAR ELSE 0 END) AS CURRENT_APPEND_YEAR,
         VIEW_FLAG,                
         CALIBER_FLAG,             
         'YTD' AS DATA_TYPE
     FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T
     WHERE VERSION_ID = V_VERSION_ID
      AND PERIOD_ID IN (
                          SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T
                             WHERE APPEND_FLAG = 'N'        -- 当前年份有数据的，取每年非补齐的最大月份数据
                             AND CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND MONTH(CURRENT_TIMESTAMP)-1   -- 只取每年1月-每年YTD月的数据值
                          UNION ALL 
                            SELECT DISTINCT MAX(PERIOD_ID) OVER(PARTITION BY PERIOD_YEAR)
                             FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T
                             WHERE PERIOD_YEAR IN (  
                                                   SELECT PERIOD_YEAR FROM(   
                                                   SELECT PERIOD_YEAR,SUM(APP_FLAG) AS APP_FLAG FROM(   
                                                   SELECT PERIOD_YEAR,DECODE(APPEND_FLAG,'Y',0,1) AS APP_FLAG FROM FIN_DM_OPT_FOI.DM_FOC_REPL_MTD_AVG_T 
                                                          WHERE CAST(SUBSTR(PERIOD_ID,5,2) AS INT) BETWEEN 1 AND MONTH(CURRENT_TIMESTAMP)-1   -- 只取每年1月-每年YTD月的数据值
                                                   ) GROUP BY PERIOD_YEAR     -- 为0的时候，表示整年无数据
                                                   )WHERE APP_FLAG = 0
                                                   )    -- 整年没有数据，即取最大月份的补齐数据
                         )
     GROUP BY LV0_PROD_RND_TEAM_CODE,                 
              LV0_PROD_RD_TEAM_CN_NAME,              
              LV1_PROD_RND_TEAM_CODE,                 
              LV1_PROD_RD_TEAM_CN_NAME,              
              LV2_PROD_RND_TEAM_CODE,                 
              LV2_PROD_RD_TEAM_CN_NAME,              
              LV3_PROD_RND_TEAM_CODE,                 
              LV3_PROD_RD_TEAM_CN_NAME,               
              REPLACEMENT_GROUP_ID,     
              REPLACEMENT_GROUP_CN_NAME,
              VIEW_FLAG,                
              CALIBER_FLAG;
              
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级缺失数据情况插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  -- 最细粒度层级状态码逻辑
  -- 对最细粒度层级的年份进行循环 
  FOR YEAR_FLAG IN V_YEAR-2 .. V_YEAR LOOP                      
        
    IF YEAR_FLAG = V_YEAR-2 THEN
        V_LAST_YEAR_FLAG := 'LAST_THREE_YEAR_FLAG';
        V_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_THREE_APPEND_YEAR';
    
    ELSIF YEAR_FLAG = V_YEAR-1 THEN
        V_LAST_YEAR_FLAG := 'LAST_TWO_YEAR_FLAG';
        V_YEAR_FLAG := 'LAST_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_TWO_APPEND_YEAR';
        
    ELSIF YEAR_FLAG = V_YEAR THEN
        V_LAST_YEAR_FLAG := 'LAST_YEAR_FLAG';
        V_YEAR_FLAG := 'CURRENT_YEAR_FLAG';
        V_YEAR_APPEND := 'LAST_APPEND_YEAR';    
    ELSE NULL;
    END IF;
   
  V_SQL := '
  INSERT INTO FOC_REPL_STATUS_TMP(
         PERIOD_YEAR,              
         LV0_CODE,                 
         LV0_CN_NAME,              
         LV1_CODE,                 
         LV1_CN_NAME,              
         LV2_CODE,                 
         LV2_CN_NAME,              
         LV3_CODE,                 
         LV3_CN_NAME,              
         REPLACEMENT_GROUP_ID,    
         REPLACEMENT_GROUP_CN_NAME,
         STATUS_CODE,              
         APPEND_YEAR,              
         VIEW_FLAG,                
         CALIBER_FLAG,             
         DATA_TYPE                
    )
  SELECT '||YEAR_FLAG||' AS PERIOD_YEAR,
         LV0_CODE,                     
         LV0_CN_NAME,                  
         LV1_CODE,                     
         LV1_CN_NAME,                  
         LV2_CODE,                     
         LV2_CN_NAME,                  
         LV3_CODE,                     
         LV3_CN_NAME, 
         REPLACEMENT_GROUP_ID,    
         REPLACEMENT_GROUP_CN_NAME,
         CASE WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 1 THEN 1
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 1 THEN 2
              WHEN '||V_LAST_YEAR_FLAG||' = 0 AND '||V_YEAR_FLAG||' = 0 THEN 3
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' = 0 THEN 4
              WHEN '||V_LAST_YEAR_FLAG||' = 1 AND '||V_YEAR_FLAG||' = 0 AND '||V_YEAR_APPEND||' <> 0 THEN 5
         END AS STATUS_CODE,
         '||V_YEAR_APPEND||' AS APPEND_YEAR,
         VIEW_FLAG,                
         CALIBER_FLAG,             
         DATA_TYPE
      FROM FOC_REPL_BASE_TMP';
      
  DBMS_OUTPUT.PUT_LINE(V_SQL);
  EXECUTE IMMEDIATE V_SQL;  
       
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级全维度缺失状态码插入FOC_REPL_STATUS_TMP表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');         
    END LOOP;
    
  -- 将最细粒度状态码数据插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
		 APPEND_YEAR,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT V_VERSION_ID AS VERSION_ID,
         PERIOD_YEAR,
         REPLACEMENT_GROUP_ID AS GROUP_CODE,
         REPLACEMENT_GROUP_CN_NAME AS GROUP_CN_NAME,
         'BIND' AS GROUP_LEVEL, 
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         LV3_CODE AS PARENT_CODE,
         LV3_CN_NAME AS PARENT_CN_NAME,
		 SUBSTR(APPEND_YEAR,1,4) AS APPEND_YEAR,
         -1 AS CREATED_BY, 
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FOC_REPL_STATUS_TMP;
    
  --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '最细粒度层级全维度缺失状态码插入结果表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');   
    
  -- 将LV3层级状态码数据插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
		 APPEND_YEAR
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT PERIOD_YEAR,
         LV3_CODE AS GROUP_CODE,
         LV3_CN_NAME AS GROUP_CN_NAME,
         'LV3' AS GROUP_LEVEL,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4,    -- 值=0，说明子级都为4，赋2
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         LV2_CODE AS PARENT_CODE,
         LV2_CN_NAME AS PARENT_CN_NAME
     FROM FOC_REPL_STATUS_TMP T1
     GROUP BY PERIOD_YEAR,
	          LV0_CODE,                     
              LV0_CN_NAME,                  
              LV1_CODE,                     
              LV1_CN_NAME,                  
              LV2_CODE,                     
              LV2_CN_NAME,                  
              LV3_CODE,                     
              LV3_CN_NAME, 
              VIEW_FLAG,                
              CALIBER_FLAG,             
              DATA_TYPE 
            )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_4 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,  
         NULL AS APPEND_YEAR
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T T1
      LEFT JOIN BASEUP_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.DATA_TYPE = T2.DATA_TYPE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      WHERE T1.GROUP_LEVEL = 'LV3'   -- 取LV3层级的数据
      AND T1.VERSION_ID = V_VERSION_ID ;       
  
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的LV3层级缺失状态码插入DM_FOC_REPL_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  -- 将LV2层级状态码数据插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
		 APPEND_YEAR
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT PERIOD_YEAR,
         LV2_CODE AS GROUP_CODE,
         LV2_CN_NAME AS GROUP_CN_NAME,
         'LV2' AS GROUP_LEVEL,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4,    -- 值=0，说明子级都为4，赋2
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         LV1_CODE AS PARENT_CODE,
         LV1_CN_NAME AS PARENT_CN_NAME
     FROM FOC_REPL_STATUS_TMP T1
     GROUP BY PERIOD_YEAR,
	          LV0_CODE,                     
              LV0_CN_NAME,                  
              LV1_CODE,                     
              LV1_CN_NAME,                  
              LV2_CODE,                     
              LV2_CN_NAME,    
              VIEW_FLAG,                
              CALIBER_FLAG,             
              DATA_TYPE 
            )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_4 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,  
         NULL AS APPEND_YEAR
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T T1
      LEFT JOIN BASEUP_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.DATA_TYPE = T2.DATA_TYPE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      WHERE T1.GROUP_LEVEL = 'LV2'   -- 取LV2层级的数据
      AND T1.VERSION_ID = V_VERSION_ID ;       
  
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的LV2层级缺失状态码插入DM_FOC_REPL_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  -- 将LV1层级状态码数据插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
		 APPEND_YEAR
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT PERIOD_YEAR,
         LV1_CODE AS GROUP_CODE,
         LV1_CN_NAME AS GROUP_CN_NAME,
         'LV1' AS GROUP_LEVEL,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4,    -- 值=0，说明子级都为4，赋2
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         LV0_CODE AS PARENT_CODE,
         LV0_CN_NAME AS PARENT_CN_NAME
     FROM FOC_REPL_STATUS_TMP T1
     GROUP BY PERIOD_YEAR,
	          LV0_CODE,                     
              LV0_CN_NAME,                  
              LV1_CODE,                     
              LV1_CN_NAME,   
              VIEW_FLAG,                
              CALIBER_FLAG,             
              DATA_TYPE 
            )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_4 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,  
         NULL AS APPEND_YEAR
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T T1
      LEFT JOIN BASEUP_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.DATA_TYPE = T2.DATA_TYPE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      WHERE T1.GROUP_LEVEL = 'LV1'   -- 取LV2层级的数据
      AND T1.VERSION_ID = V_VERSION_ID ;       
  
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的LV1层级缺失状态码插入DM_FOC_REPL_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  -- 将LV0层级状态码数据插入到结果表
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T(
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG,
		 APPEND_YEAR
  )
  WITH BASEUP_STATUS_TMP AS(
  SELECT PERIOD_YEAR,
         LV0_CODE AS GROUP_CODE,
         LV0_CN_NAME AS GROUP_CN_NAME,
         'LV0' AS GROUP_LEVEL,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,4,0,1)) AS STATUS_4,    -- 值=0，说明子级都为4，赋2
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         LV0_CODE AS PARENT_CODE,
         LV0_CN_NAME AS PARENT_CN_NAME
     FROM FOC_REPL_STATUS_TMP T1
     GROUP BY PERIOD_YEAR,
	          LV0_CODE,                     
              LV0_CN_NAME,                  
              LV1_CODE,                     
              LV1_CN_NAME,   
              VIEW_FLAG,                
              CALIBER_FLAG,             
              DATA_TYPE 
            )
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_4 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
         T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG,  
         NULL AS APPEND_YEAR
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_AMP_T T1
      LEFT JOIN BASEUP_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
      AND T1.VIEW_FLAG = T2.VIEW_FLAG
      AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
      AND T1.DATA_TYPE = T2.DATA_TYPE
      AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
      WHERE T1.GROUP_LEVEL = 'LV0'   -- 取LV1层级的数据
      AND T1.VERSION_ID = V_VERSION_ID ;       
  
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的LV0层级缺失状态码插入DM_FOC_REPL_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  
    
  ------------------------------------------------------------------------------标准成本状态码逻辑--------------------------------------------------------------------------------------
  -- 清空临时表
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FOC_REPL_STATUS_TMP';

  -- 用于标准成本计算的基础缺失状态码插入临时表
  INSERT INTO FOC_REPL_STATUS_TMP(
         PERIOD_YEAR,
         LV0_CODE,
         LV0_CN_NAME,
         LV1_CODE,
         LV1_CN_NAME,
         LV2_CODE,
         LV2_CN_NAME,
         LV3_CODE,
         LV3_CN_NAME,
         STATUS_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
		 DATA_TYPE
         )
  WITH BASE_STATUS_TMP AS(
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         STATUS_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
  		 'YTD' AS DATA_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_SAME_TOTAL_YTD_STATUS_T T1   -- 同编码-YTD数据
	  WHERE T1.VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'LV3'
  UNION ALL
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         STATUS_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
  	     'TOTAL' AS DATA_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOC_TOTAL_ANNUAL_STATUS_CODE_T T1   -- 同编码-整年数据
	  WHERE VERSION_ID = V_VERSION_ID
	  AND OVERSEA_FLAG = 'G'   -- 取全球的数据
	  AND LV0_PROD_LIST_CODE = 'GR'   -- 取集团的数据
	  AND T1.GROUP_LEVEL = 'LV3'
  UNION ALL
  SELECT PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         PARENT_CODE,
         PARENT_CN_NAME,
         STATUS_CODE,
         VIEW_FLAG,
         CALIBER_FLAG,
  	     DATA_TYPE
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T T1   -- 研发替代-YTD+整年数据
      WHERE VERSION_ID = V_VERSION_ID
	  AND T1.GROUP_LEVEL = 'LV3'
    )
  SELECT T1.PERIOD_YEAR,
         T2.LV0_PROD_RND_TEAM_CODE AS LV0_CODE,
         T2.LV0_PROD_RD_TEAM_CN_NAME AS LV0_CN_NAME,
         T2.LV1_PROD_RND_TEAM_CODE AS LV1_CODE,
         T2.LV1_PROD_RD_TEAM_CN_NAME AS LV1_CN_NAME,
         T2.LV2_PROD_RND_TEAM_CODE AS LV2_CODE,
         T2.LV2_PROD_RD_TEAM_CN_NAME AS LV2_CN_NAME,
         T2.LV3_PROD_RND_TEAM_CODE AS LV3_CODE,
         T2.LV3_PROD_RD_TEAM_CN_NAME AS LV3_CN_NAME,
         T1.STATUS_CODE,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
  	     T1.DATA_TYPE
      FROM BASE_STATUS_TMP T1   -- 研发替代-YTD+整年数据
	  LEFT JOIN (
	        SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_REPL_VIEW_INFO_D WHERE VERSION_ID = V_VERSION_ID AND DATA_FLAG = 'STANDARD'
	                ) T2
	  ON T1.GROUP_LEVEL = T2.GROUP_LEVEL
	  AND T1.GROUP_CODE = T2.LV3_PROD_RND_TEAM_CODE
	  AND T1.PARENT_CODE = T2.LV2_PROD_RND_TEAM_CODE
	  AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
	  AND T1.VIEW_FLAG = T2.VIEW_FLAG;
	  
   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '用于标准成本计算的，版本号为：'||V_VERSION_ID||'的基础缺失状态码插入临时表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_STATUS_T (
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH ITEM_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV3_CODE AS GROUP_CODE, 
         T1.LV2_CODE AS PARENT_CODE,
         T1.LV2_CN_NAME AS PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         T1.DATA_TYPE,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2    -- 值=0，说明子级都为2，赋2
      FROM FOC_REPL_STATUS_TMP T1
      GROUP BY T1.LV3_CODE,
               T1.LV2_CODE,
               T1.LV1_CODE,
               T1.LV2_CN_NAME,
               T1.LV1_CN_NAME,
			   T1.PERIOD_YEAR,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.DATA_TYPE
	)
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
		 T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T T1
      LEFT JOIN ITEM_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
--      AND NVL(T1.VIEW_FLAG,'SNULL') = NVL(T2.VIEW_FLAG,'SNULL')          -- 基础数据只有视角4，不需要关联视角（20250310修改）
      AND NVL(T1.CALIBER_FLAG,'SNULL') = NVL(T2.CALIBER_FLAG,'SNULL') 
      AND T1.DATA_TYPE = T2.DATA_TYPE
      WHERE T1.GROUP_LEVEL = 'LV3'
      AND T1.VERSION_ID = V_VERSION_ID;

   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的标准成本的LV3层级缺失状态码插入DM_FOC_REPL_STANDARD_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_STATUS_T (
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH ITEM_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV2_CODE AS GROUP_CODE, 
         T1.LV1_CODE AS PARENT_CODE,
         T1.LV1_CN_NAME AS PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         T1.DATA_TYPE,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2    -- 值=0，说明子级都为2，赋2
      FROM FOC_REPL_STATUS_TMP T1
      GROUP BY T1.LV2_CODE,
               T1.LV1_CODE,
               T1.LV1_CN_NAME,
			   T1.PERIOD_YEAR,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.DATA_TYPE
	)
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
		 T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T T1
      LEFT JOIN ITEM_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
--      AND NVL(T1.VIEW_FLAG,'SNULL') = NVL(T2.VIEW_FLAG,'SNULL')          -- 基础数据只有视角4，不需要关联视角（20250310修改）
      AND NVL(T1.CALIBER_FLAG,'SNULL') = NVL(T2.CALIBER_FLAG,'SNULL') 
      AND T1.DATA_TYPE = T2.DATA_TYPE
      WHERE T1.GROUP_LEVEL = 'LV2'
      AND T1.VERSION_ID = V_VERSION_ID;

   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的标准成本的LV2层级缺失状态码插入DM_FOC_REPL_STANDARD_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_STATUS_T (
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH ITEM_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV1_CODE AS GROUP_CODE, 
         T1.LV0_CODE AS PARENT_CODE,
         T1.LV0_CN_NAME AS PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         T1.DATA_TYPE,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2    -- 值=0，说明子级都为2，赋2
      FROM FOC_REPL_STATUS_TMP T1
      GROUP BY T1.LV1_CODE,
               T1.LV0_CODE,
               T1.LV0_CN_NAME,
			   T1.PERIOD_YEAR,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.DATA_TYPE
	)
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
		 T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T T1
      LEFT JOIN ITEM_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
--      AND NVL(T1.VIEW_FLAG,'SNULL') = NVL(T2.VIEW_FLAG,'SNULL')          -- 基础数据只有视角4，不需要关联视角（20250310修改）
      AND NVL(T1.CALIBER_FLAG,'SNULL') = NVL(T2.CALIBER_FLAG,'SNULL') 
      AND T1.DATA_TYPE = T2.DATA_TYPE
      WHERE T1.GROUP_LEVEL = 'LV1'
      AND T1.VERSION_ID = V_VERSION_ID;

   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的标准成本的LV1层级缺失状态码插入DM_FOC_REPL_STANDARD_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_STATUS_T (
         VERSION_ID,
         PERIOD_YEAR,
         GROUP_CODE,
         GROUP_CN_NAME,
         GROUP_LEVEL,
         STATUS_CODE,
         CALIBER_FLAG,
         VIEW_FLAG,
         DATA_TYPE,
         PARENT_CODE,
         PARENT_CN_NAME,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  WITH ITEM_STATUS_TMP AS(
  SELECT T1.PERIOD_YEAR,
         T1.LV0_CODE AS GROUP_CODE, 
         T1.LV0_CODE AS PARENT_CODE,
         T1.LV0_CN_NAME AS PARENT_CN_NAME,
         T1.VIEW_FLAG,
         T1.CALIBER_FLAG,
         T1.DATA_TYPE,
         SUM(CASE WHEN T1.STATUS_CODE IN (3,5) THEN 1 ELSE 0 END) AS STATUS_NORMAL,   -- 值大于1，则赋3
         SUM(DECODE(T1.STATUS_CODE,1,0,1)) AS STATUS_1,     -- 值=0，说明子级都为1，赋1
         SUM(DECODE(T1.STATUS_CODE,2,0,1)) AS STATUS_2    -- 值=0，说明子级都为2，赋2
      FROM FOC_REPL_STATUS_TMP T1
      GROUP BY T1.LV0_CODE,
               T1.LV0_CN_NAME,
			   T1.PERIOD_YEAR,
               T1.VIEW_FLAG,
               T1.CALIBER_FLAG,
               T1.DATA_TYPE
	)
  SELECT V_VERSION_ID AS VERSION_ID,
         T1.PERIOD_YEAR,
         T1.GROUP_CODE,
         T1.GROUP_CN_NAME,
         T1.GROUP_LEVEL,
         CASE WHEN T2.STATUS_NORMAL > 0 THEN 3
              WHEN T2.STATUS_1 = 0 THEN 1
              WHEN T2.STATUS_2 = 0 THEN 2
         ELSE 4 END AS STATUS_CODE,
		 T1.CALIBER_FLAG,
         T1.VIEW_FLAG,
         T1.DATA_TYPE,
         T1.PARENT_CODE,
         T1.PARENT_CN_NAME,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_AMP_T T1
      LEFT JOIN ITEM_STATUS_TMP T2
      ON T1.PERIOD_YEAR = T2.PERIOD_YEAR
      AND NVL(T1.GROUP_CODE,'SNULL') = NVL(T2.GROUP_CODE,'SNULL')
      AND NVL(T1.PARENT_CODE,'SNULL') = NVL(T2.PARENT_CODE,'SNULL')
--      AND NVL(T1.VIEW_FLAG,'SNULL') = NVL(T2.VIEW_FLAG,'SNULL')          -- 基础数据只有视角4，不需要关联视角（20250310修改）
      AND NVL(T1.CALIBER_FLAG,'SNULL') = NVL(T2.CALIBER_FLAG,'SNULL') 
      AND T1.DATA_TYPE = T2.DATA_TYPE
      WHERE T1.GROUP_LEVEL = 'LV0'
      AND T1.VERSION_ID = V_VERSION_ID;

   --写入日志
   V_STEP_NUM := V_STEP_NUM + 1;
   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
   (F_SP_NAME => V_SP_NAME,
    F_STEP_NUM => V_STEP_NUM,
    F_CAL_LOG_DESC => '版本号为：'||V_VERSION_ID||'的标准成本的LV0层级缺失状态码插入DM_FOC_REPL_STANDARD_ANNL_STATUS_T表',
    F_DML_ROW_COUNT => SQL%ROWCOUNT,
    F_RESULT_STATUS => X_RESULT_STATUS,
    F_ERRBUF => 'SUCCESS');  

    -- 收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_ANNL_STATUS_T';
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOI.DM_FOC_REPL_STANDARD_ANNL_STATUS_T';
  
  --3.日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOC_REPL_ANNL_STATUS_T/DM_FOC_REPL_STANDARD_ANNL_STATUS_T统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END$$
/

