-- Name: f_dm_fom_source_primary_add; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fom_source_primary_add(f_data_flag character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
最近修改时间:2024年11月7日18点05分
修改内容:202411版本 资源吸收替代任务令吸收明细表

修改时间:2024年4月15日14点20分
修改内容:临时修改,纳入终端数据,加主键

  创建时间：2023-12-5
  创建人  ：唐钦
  背景描述：上游数据添加主键，为JAVA加解密做处理
            涉及表：
            --标准成本吸收收敛(自制历史 金额) 
            standard_cst_abs_aggre_t_tmp(和上游/补录数据完全保持一致的表)/standard_cst_abs_aggre_t(新增一个PRIMARY_ID字段，其余保持一致)/dm_fom_standard_cst_abs_aggre_t
            --任务令吸收明细(只包含最新月)
            rtd_cst_wip_trans_ae_detail_i_tmp/rtd_cst_wip_trans_ae_detail_i/fom_rtd_cst_wip_trans_ae_detail_i
  参数描述：f_data_flag : 数据口径(标准成本吸收收敛(历史数据)：HIS/任务令吸收明细（本月最新数据）：CURR；清空数据：TRUNCATE_HIS/TRUNCATE_CURR)
            x_success_flag ：是否成功
  事例    ：select fin_dm_opt_foi.f_dm_fom_source_primary_add('CURR')
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOM_SOURCE_PRIMARY_ADD'; --存储过程名称
  V_STEP_NUM   BIGINT := 0; --步骤号
  V_DATA_FLAG VARCHAR(50) := F_DATA_FLAG;   -- 数据类型标识
  V_LAST_MONTH VARCHAR(50);  -- 上个月月份
  V_SQL TEXT;
  
BEGIN
  X_RESULT_STATUS = '1'; 

  -- 开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行');
  
IF V_DATA_FLAG <> 'TRUNCATE_HIS' AND V_DATA_FLAG <> 'TRUNCATE_CURR' THEN
  -- 删除目标表数据
  IF V_DATA_FLAG = 'HIS' THEN  -- 标准成本吸收收敛表
    /*SELECT DISTINCT CAST(CONCAT(SUBSTR(PERIOD_ID,1,4), SUBSTR(PERIOD_ID,6,2)) AS BIGINT) AS PERIOD_ID INTO V_LAST_MONTH FROM STANDARD_CST_ABS_AGGRE_T_TMP;
    EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T WHERE PERIOD_ID = '''||V_LAST_MONTH||'''';  -- 删除与插入数据月份相同的数据
    EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.STANDARD_CST_JAVA_ABS_AGGRE_T WHERE PERIOD_ID = '''||V_LAST_MONTH||'''';  -- 删除与插入数据月份相同的数据
	*/
	--EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T ';
	--EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.STANDARD_CST_JAVA_ABS_AGGRE_T ';  --202405版本 临时修改 新加入终端数据
	NULL;	--202405版本 临时修改 不清空原数据新加入终端数据
  ELSIF V_DATA_FLAG = 'CURR' THEN  -- 任务令吸收明细表
   /* SELECT DISTINCT PERIOD_NAME INTO V_LAST_MONTH FROM RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP;
    EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I WHERE PERIOD_NAME = '''||V_LAST_MONTH||'''';  -- 删除与插入数据月份相同的数据
    EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.RTD_CST_JAVA_WIP_TRANS_AE_DETAIL_I WHERE PERIOD_NAME = '''||V_LAST_MONTH||'''';  -- 删除与插入数据月份相同的数据
	*/--旧版逻辑,已弃用
	
	/*SELECT DISTINCT TO_CHAR(TRANSACTION_DATE,'YYYYMM') INTO V_LAST_MONTH FROM DWL_INV_MFG_RESOUCE_TRANS_TMP;
	EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_RESOUCE_TRANS_T WHERE TO_CHAR(TRANSACTION_DATE,'YYYYMM') = '''||V_LAST_MONTH||'''';  --202411版本第二次刷数使用 删除与插入数据月份相同的数据
	*/
	--EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_RESOUCE_TRANS_T';  --202411版本上线使用 
	NULL;
	
  END IF;
  
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '删除数据类型标识为：'||V_DATA_FLAG||',且月份为：'''||V_LAST_MONTH||'''的表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  -- 判断入参值，刷新哪张表数据
  IF V_DATA_FLAG = 'HIS' THEN
    -- 标准成本吸收收敛表
    INSERT INTO FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T
      ( -- 加主键的表
       ITEM,
       PERIOD_ID,
       HOMEMADE_EMS,
       RESOURCE_TYPE,
       AMOUNT,
       DEL_FLAG)
      SELECT ITEM,
             CAST(CONCAT(SUBSTR(PERIOD_ID, 1, 4), SUBSTR(PERIOD_ID, 6, 2)) AS
                  BIGINT) AS PERIOD_ID,
             HOMEMADE_EMS,
             RESOURCE_TYPE,
             AMOUNT,
             'N'
        FROM FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T_TMP;
  
    INSERT INTO FIN_DM_OPT_FOI.DM_FOM_STANDARD_CST_ABS_AGGRE_T
      ( -- 逻辑处理需要的表
       ITEM,
       PERIOD_ID,
       HOMEMADE_EMS,
       RESOURCE_TYPE,
       PRIMARY_ID)
      SELECT ITEM, PERIOD_ID, HOMEMADE_EMS, RESOURCE_TYPE, PRIMARY_ID
        FROM FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T; --202405版本 临时修改
    --   WHERE PERIOD_ID = V_LAST_MONTH;
	   
  ELSIF V_DATA_FLAG = 'CURR' THEN
   /* -- 任务令吸收明细表
    INSERT INTO FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I
      ( -- 插入数据,增加 PRIMARY_ID
       COA_ACCOUNT_CODE,
       GC_TRANSACTION_AMOUNT,
       PERIOD_NAME,
       UNIT_CODE,
       ORGANIZATION_CODE,
       GC_SUB_ELEMENT_RATE,
       USAGE_RATE_OR_AMOUNT,
       RESOURCE_CODE,
       RESOURCE_DESCRIPTION,
       TRANSACTION_UOM_CODE,
       WIP_ORDER_NUMBER,
       ASSEMBLY_ITEM_CODE,
       OPERATION_CODE,
       RESOURCE_OVERHEAD_CODE,
       DEL_FLAG)
      SELECT COA_ACCOUNT_CODE,
             GC_TRANSACTION_AMOUNT,
             PERIOD_NAME,
             UNIT_CODE,
             ORGANIZATION_CODE,
             GC_SUB_ELEMENT_RATE,
             USAGE_RATE_OR_AMOUNT,
             RESOURCE_CODE,
             RESOURCE_DESCRIPTION,
             TRANSACTION_UOM_CODE,
             WIP_ORDER_NUMBER,
             ASSEMBLY_ITEM_CODE,
             OPERATION_CODE,
             RESOURCE_OVERHEAD_CODE,
             'N'
        FROM FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP;
		*/ 
	
  -- 202411版本 资源吸收 T表从TMP表拿到完整数据,自动加主键,以便后续JAVA表直取主键及金额字段后加密金额
	INSERT INTO FIN_DM_OPT_FOI.DWL_INV_MFG_RESOUCE_TRANS_T
	  (ASSEMBLY_ITEM_CODE,
	   TABSORPTION_AMOUNT,
	   ABSORPTION_ACCOUNT,
	   COA_DEPT_KEY,
	   TRANSACTION_DATE,
	   HOMEMADE_TYPE,
	   APD_MANUFACTURE_PROD_LV0,
	   APD_MANUFACTURE_PROD_LV1,
	   APD_OPERATE_OBJECT,
	   APD_SHIPMENT_OBJECT,
	   APD_MANUFACTURE_OBJECT,
	   APD_RESOUCE_TYPE,
	   DEL_FLAG)
	  SELECT ASSEMBLY_ITEM_CODE,
			 TABSORPTION_AMOUNT,
			 ABSORPTION_ACCOUNT,
			 COA_DEPT_KEY,
			 TRANSACTION_DATE,
			 HOMEMADE_TYPE,
			 APD_MANUFACTURE_PROD_LV0,
			 APD_MANUFACTURE_PROD_LV1,
			 APD_OPERATE_OBJECT,
			 APD_SHIPMENT_OBJECT,
			 APD_MANUFACTURE_OBJECT,
			 APD_RESOUCE_TYPE,
			 'N'
		FROM FIN_DM_OPT_FOI.DWL_INV_MFG_RESOUCE_TRANS_TMP;

  
 /*   INSERT INTO FIN_DM_OPT_FOI.FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I
      ( -- 逻辑处理需要的表
       COA_ACCOUNT_CODE,
       PERIOD_NAME,
       UNIT_CODE,
       ORGANIZATION_CODE,
       GC_SUB_ELEMENT_RATE,
       USAGE_RATE_OR_AMOUNT,
       RESOURCE_CODE,
       RESOURCE_DESCRIPTION,
       TRANSACTION_UOM_CODE,
       WIP_ORDER_NUMBER,
       ASSEMBLY_ITEM_CODE,
       OPERATION_CODE,
       PRIMARY_ID,
       RESOURCE_OVERHEAD_CODE)
      SELECT COA_ACCOUNT_CODE,
             PERIOD_NAME,
             UNIT_CODE,
             ORGANIZATION_CODE,
             GC_SUB_ELEMENT_RATE,
             USAGE_RATE_OR_AMOUNT,
             RESOURCE_CODE,
             RESOURCE_DESCRIPTION,
             TRANSACTION_UOM_CODE,
             WIP_ORDER_NUMBER,
             ASSEMBLY_ITEM_CODE,
             OPERATION_CODE,
             PRIMARY_ID,
             RESOURCE_OVERHEAD_CODE
        FROM FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I
       WHERE PERIOD_NAME = V_LAST_MONTH;*/
	   
	   
  INSERT INTO FIN_DM_OPT_FOI.FOM_DWL_INV_MFG_RESOUCE_TRANS_T 
  --202411版本 后续关联逻辑使用表,不含金额字段,金额由后续关联JAVA已加密金额表得到
    (ASSEMBLY_ITEM_CODE,
     PRIMARY_ID,
     ABSORPTION_ACCOUNT,
     COA_DEPT_KEY,
     TRANSACTION_DATE,
     HOMEMADE_TYPE,
     APD_MANUFACTURE_PROD_LV0,
     APD_MANUFACTURE_PROD_LV1,
     APD_OPERATE_OBJECT,
     APD_SHIPMENT_OBJECT,
     APD_MANUFACTURE_OBJECT,
     APD_RESOUCE_TYPE,
     LAST_UPDATE_DATE)
    SELECT ASSEMBLY_ITEM_CODE,								
           PRIMARY_ID,                                      
           ABSORPTION_ACCOUNT,                              
           COA_DEPT_KEY,                                    
           TRANSACTION_DATE,                                
           HOMEMADE_TYPE,                                   
           APD_MANUFACTURE_PROD_LV0,                        
           APD_MANUFACTURE_PROD_LV1,                        
           APD_OPERATE_OBJECT,                              
           APD_SHIPMENT_OBJECT,                             
           APD_MANUFACTURE_OBJECT,                          
           APD_RESOUCE_TYPE,                                
           NOW() AS LAST_UPDATE_DATE                        
      FROM FIN_DM_OPT_FOI.DWL_INV_MFG_RESOUCE_TRANS_T;      
	   
	   
  END IF;
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '插入数据类型标识为：'||V_DATA_FLAG||',且月份为：'''||V_LAST_MONTH||'''的表数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
 
  -- 删除数据
ELSIF V_DATA_FLAG = 'TRUNCATE_HIS' THEN  --标准成本吸收收敛 (历史)
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T_TMP';  -- 上游保持一致的表
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.STANDARD_CST_ABS_AGGRE_T';  -- 加主键的表
     
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空数据类型标识为：'||V_DATA_FLAG||'的上游表和加主键表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
ELSIF V_DATA_FLAG = 'TRUNCATE_CURR' THEN  --任务令吸收明细 (新增)
   /*  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP';  -- 上游保持一致的表
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.RTD_CST_WIP_TRANS_AE_DETAIL_I';  -- 加主键的表
	 */
	 --202411版本
  /*   EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DWL_INV_MFG_RESOUCE_TRANS_TMP';  -- TMP金额明文表 
     EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOI.DWL_INV_MFG_RESOUCE_TRANS_T';  -- 加主键的表
	 */  --上线完成后恢复
	
  NULL;	--临时方案
  -- 写入日志
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '清空数据类型标识为：'||V_DATA_FLAG||'的上游表和加主键表的数据',
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
END IF;
   
  -- 收集统计信息
  IF V_DATA_FLAG = 'HIS' THEN  
     EXECUTE IMMEDIATE 'ANALYZE STANDARD_CST_ABS_AGGRE_T_TMP';
     EXECUTE IMMEDIATE 'ANALYZE STANDARD_CST_ABS_AGGRE_T';
     EXECUTE IMMEDIATE 'ANALYZE DM_FOM_STANDARD_CST_ABS_AGGRE_T';
  ELSIF V_DATA_FLAG = 'CURR' THEN  
    /* EXECUTE IMMEDIATE 'ANALYZE RTD_CST_WIP_TRANS_AE_DETAIL_I_TMP';
     EXECUTE IMMEDIATE 'ANALYZE RTD_CST_WIP_TRANS_AE_DETAIL_I';
     EXECUTE IMMEDIATE 'ANALYZE FOM_RTD_CST_WIP_TRANS_AE_DETAIL_I';
	 */
	 --202411版本
     EXECUTE IMMEDIATE 'ANALYZE DWL_INV_MFG_RESOUCE_TRANS_TMP';
     EXECUTE IMMEDIATE 'ANALYZE DWL_INV_MFG_RESOUCE_TRANS_T';
     EXECUTE IMMEDIATE 'ANALYZE FOM_DWL_INV_MFG_RESOUCE_TRANS_T';
  END IF;
  
  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 统计信息完成!');
 
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
   
END


$$
/

