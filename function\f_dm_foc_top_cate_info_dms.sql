-- Name: f_dm_foc_top_cate_info_dms; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_top_cate_info_dms(f_industry_flag character varying, f_dimension_type character varying, OUT x_result_status character varying)
 RETURNS character varying
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/*
最后修改人:罗若文
背景描述：1. 分视角统计TOP品类的权重; 2.给前95%品类打上TOP标识 3.增加对数字能源的适配  4.增加对IAS的适配
修改： 量纲增加 SPART_CODE 和 SPART_CN_NAME 字段 许灿烽 20231220
		增加数字能源适配
参数描述:f_dimension_type : 维度类型(通用颗粒度：U,盈利颗粒度：P), x_result_status :是否成功
来源表:FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_CATE_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_CATE_T(量纲颗粒度)
目标表:FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T(通用颗粒度),FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_CATE_INFO_T(盈利颗粒度),FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_CATE_INFO_T(量纲颗粒度)
事例：SELECT FIN_DM_OPT_FOI.F_DM_FOC_TOP_CATE_INFO()
*/
DECLARE
  V_SP_NAME    VARCHAR2(500) := 'FIN_DM_OPT_FOI.F_DM_FOC_TOP_CATE_INFO_DMS'; --存储过程名称
  V_VERSION_ID BIGINT; --新的版本号, 取自 FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T
  V_VERSION_NAME VARCHAR2(50) := TO_CHAR(CURRENT_TIMESTAMP,'YYYY-MM'); --新的版本中文名称
  V_DIM_VERSION_ID BIGINT; --品类-专家团最新的版本号
  V_STEP_MUM   BIGINT := 0; --步骤号
  
  -- 7月版本需求新增
  V_SQL        TEXT;   --SQL逻辑
  V_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_L1_NAME VARCHAR(100);
  V_L2_NAME VARCHAR(100);
  V_IN_LV3_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV3_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_LV4_PROD_RND_TEAM_CODE VARCHAR(50);
  V_IN_LV4_PROD_RD_TEAM_CN_NAME VARCHAR(100);
  V_IN_L1_NAME VARCHAR(100);
  V_IN_L2_NAME VARCHAR(100);
  V_INSERT_LV3_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_LV4_PROD_RND_TEAM_CODE VARCHAR(500);
  V_INSERT_L1_NAME VARCHAR(500);
  V_INSERT_L2_NAME VARCHAR(500);
  V_FROM_TABLE VARCHAR(100); -- 来源表
  V_TO_TABLE VARCHAR(100); -- 目标表
  
  -- 9月版本需求新增
  V_DIMENSION_CODE VARCHAR(100);
  V_DIMENSION_CN_NAME VARCHAR(100);
  V_DIMENSION_EN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_CODE VARCHAR(100);
  V_IN_DIMENSION_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CODE VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CODE VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_CN_NAME VARCHAR(100);
  V_IN_DIMENSION_SUB_DETAIL_EN_NAME VARCHAR(100);
  V_INSERT_DIMENSION_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUBCATEGORY_CODE VARCHAR(500);
  V_INSERT_DIMENSION_SUB_DETAIL_CODE VARCHAR(500);

--202401月版本需求新增SPART
  V_SPART_CODE VARCHAR(50);
  V_SPART_CN_NAME VARCHAR(50);
  V_IN_SPART_CODE VARCHAR(50);
  V_IN_SPART_CN_NAME VARCHAR(50);
  V_INSERT_SPART_CODE VARCHAR(100);
  
--202405月版本新增COA层
  V_COA_CODE VARCHAR(200);
  V_COA_CN_NAME VARCHAR(200);
  V_IN_COA_CODE VARCHAR(200);
  V_IN_COA_CN_NAME VARCHAR(200);
  V_INSERT_COA_CODE VARCHAR(200);
  V_INSERT_COA_CN_NAME VARCHAR(200);
  
BEGIN
  X_RESULT_STATUS = '1';
     
  --0.开始日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'开始执行,颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG);

  --对入参合法性判断
  IF F_DIMENSION_TYPE NOT IN ('U','P','D') THEN
     DBMS_OUTPUT.PUT_LINE ('入参有误，请重新输入！');
     RETURN '0';
  END IF;
  
  IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'I' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_TOP_CATE_INFO_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'I' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_PFT_TOP_CATE_INFO_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_CATE_T_DMS';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_DMS_TOP_CATE_INFO_T'; --目标表 
	 
	 
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'E' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_TOP_CATE_INFO_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'E' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_PFT_TOP_CATE_INFO_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_MID_MONTH_CATE_T_DMS';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_ENERGY_DMS_TOP_CATE_INFO_T'; --目标表 
  
  ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 通用颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_TOP_CATE_INFO_T';--目标表
  ELSIF F_DIMENSION_TYPE = 'P' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 盈利颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_MID_MONTH_CATE_T';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_PFT_TOP_CATE_INFO_T'; --目标表
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN -- 量纲颗粒度
     V_FROM_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_MID_MONTH_CATE_T_DMS';--来源表
     V_TO_TABLE := 'FIN_DM_OPT_FOI.DM_FOC_IAS_DMS_TOP_CATE_INFO_T'; --目标表 
  
  ELSE
    NULL;
  END IF;
  
--判断版本表
  IF F_INDUSTRY_FLAG = 'I' THEN 
 
	
  --查询最新版本号的品类专家团映射关系; 往目标表里插数
SELECT T.VERSION_ID
    INTO V_DIM_VERSION_ID
    FROM DM_FOC_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

		 
  ELSIF F_INDUSTRY_FLAG = 'E' THEN

  --查询最新版本号的品类专家团映射关系; 往目标表里插数
SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID
    FROM DM_FOC_ENERGY_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_ENERGY_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 

	 
  ELSIF F_INDUSTRY_FLAG = 'IAS' THEN
  --查询最新版本号的品类专家团映射关系; 往目标表里插数
SELECT T.VERSION_ID 
    INTO V_DIM_VERSION_ID
    FROM DM_FOC_IAS_VERSION_INFO_T T
   WHERE UPPER(T.DATA_TYPE) = 'DIMENSION'
     AND UPPER(DEL_FLAG) = 'N'
     AND STATUS = 1
	 ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
	 
  --新版本号赋值
  SELECT VERSION_ID INTO V_VERSION_ID
    FROM
       DM_FOC_IAS_VERSION_INFO_T
    WHERE
        DEL_FLAG = 'N'
        AND STATUS = 1
        AND UPPER(DATA_TYPE) = 'CATEGORY'
        ORDER BY LAST_UPDATE_DATE DESC
        LIMIT 1; 
  ELSE 
     NULL ;	 
	 
	 
  END IF;
		
    DBMS_OUTPUT.PUT_LINE('-------新版本号赋值------------'); 

  --删除当前会计期版本的TOP规格品数据, 支持单月重刷
  V_SQL := 'DELETE FROM '||V_TO_TABLE||' T WHERE T.VERSION_ID = '''||V_VERSION_ID||'''';
  EXECUTE IMMEDIATE V_SQL ;
    DBMS_OUTPUT.PUT_LINE('-------删除对应会计期数据------------'); 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '删除TOP品类清单的数据, 删除版本='||V_VERSION_ID||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  
  --创建临时表, 用于插入权重数据(总地区(全球)总BG(集团))
  DROP TABLE IF EXISTS DM_FOC_TOP_CATE_INFO_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_TOP_CATE_INFO_TEMP
  (
    VERSION_ID BIGINT,
    VERSION_NAME VARCHAR2(50),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR	VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(100),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(100),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(100),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
	LV4_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(100),
    L2_NAME VARCHAR(100),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(100),
    DIMENSION_CN_NAME    VARCHAR(100),
    DIMENSION_EN_NAME    VARCHAR(100),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(100),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(100),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(100),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(100),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(200),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(200),
	--202401月版本需求新增SPART
	SPART_CODE VARCHAR(50),
	SPART_CN_NAME VARCHAR(50),
	--202405月版本需求新增COA
	COA_CODE VARCHAR(50),
	COA_CN_NAME VARCHAR(600),
    L3_CEG_CODE VARCHAR(50),
    L3_CEG_CN_NAME VARCHAR(200),
    L3_CEG_SHORT_CN_NAME VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE VARCHAR(50),
    CATEGORY_CN_NAME VARCHAR(200),
    WEIGHT_RATE NUMERIC,
    DOUBLE_FLAG VARCHAR2(2),
    CALIBER_FLAG VARCHAR2(2),
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  
  --创建临时表, 用于插入权重数据--分地区(全球、国内、海外)分BG去计算TOP品类权重 (9月版本需求新增)
  DROP TABLE IF EXISTS DM_FOC_WEIGHT_CATE_INFO_TEMP;
  CREATE TEMPORARY TABLE DM_FOC_WEIGHT_CATE_INFO_TEMP
  (
    VERSION_ID BIGINT,
    VERSION_NAME VARCHAR2(50),
    VIEW_FLAG VARCHAR2(2),
    PERIOD_YEAR	VARCHAR(50),
    LV0_PROD_RND_TEAM_CODE VARCHAR(50),
    LV0_PROD_RD_TEAM_CN_NAME VARCHAR(100),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(100),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(100),
    LV3_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV3_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
	LV4_PROD_RND_TEAM_CODE  VARCHAR(50),
    LV4_PROD_RD_TEAM_CN_NAME    VARCHAR(200),
    L1_NAME VARCHAR(100),
    L2_NAME VARCHAR(100),
    --9月版本需求新增量纲
    DIMENSION_CODE    VARCHAR(100),
    DIMENSION_CN_NAME    VARCHAR(100),
    DIMENSION_EN_NAME    VARCHAR(100),
    DIMENSION_SUBCATEGORY_CODE    VARCHAR(100),
    DIMENSION_SUBCATEGORY_CN_NAME    VARCHAR(100),
    DIMENSION_SUBCATEGORY_EN_NAME    VARCHAR(100),
    DIMENSION_SUB_DETAIL_CODE    VARCHAR(100),
    DIMENSION_SUB_DETAIL_CN_NAME    VARCHAR(200),
    DIMENSION_SUB_DETAIL_EN_NAME    VARCHAR(200),
	--202401月版本需求新增SPART
	SPART_CODE VARCHAR(50),
	SPART_CN_NAME VARCHAR(50),
	--202405月版本需求新增COA
	COA_CODE VARCHAR(50),
	COA_CN_NAME VARCHAR(600),
    L3_CEG_CODE VARCHAR(50),
    L3_CEG_CN_NAME VARCHAR(200),
    L3_CEG_SHORT_CN_NAME VARCHAR(200),
    L4_CEG_CODE    VARCHAR(50), -- 9月版本需求新增
    L4_CEG_CN_NAME    VARCHAR(200),
    L4_CEG_SHORT_CN_NAME    VARCHAR(200),
    CATEGORY_CODE VARCHAR(50),
    CATEGORY_CN_NAME VARCHAR(200),
    WEIGHT_RATE NUMERIC,
    DOUBLE_FLAG VARCHAR2(2),
    CALIBER_FLAG VARCHAR2(2),
    OVERSEA_FLAG VARCHAR(2),
    LV0_PROD_LIST_CODE VARCHAR(50),
    LV0_PROD_LIST_CN_NAME VARCHAR(200),
    LV0_PROD_LIST_EN_NAME VARCHAR(200)
  )
  ON COMMIT PRESERVE ROWS 
  DISTRIBUTE BY ROUNDROBIN;
  
    --7月版本需求新增
    --重置变量入参
    V_LV3_PROD_RND_TEAM_CODE := 'LV3_PROD_RND_TEAM_CODE,';
    V_LV3_PROD_RD_TEAM_CN_NAME := 'LV3_PROD_RD_TEAM_CN_NAME,';
	V_LV4_PROD_RND_TEAM_CODE := 'LV4_PROD_RND_TEAM_CODE,';
    V_LV4_PROD_RD_TEAM_CN_NAME := 'LV4_PROD_RD_TEAM_CN_NAME,';
    V_L1_NAME := 'L1_NAME,';
    V_L2_NAME := 'L2_NAME,';
    V_IN_LV3_PROD_RND_TEAM_CODE := 'A.LV3_PROD_RND_TEAM_CODE,';
    V_IN_LV3_PROD_RD_TEAM_CN_NAME :='A.LV3_PROD_RD_TEAM_CN_NAME,';
	V_IN_LV4_PROD_RND_TEAM_CODE := 'A.LV4_PROD_RND_TEAM_CODE,';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME :='A.LV4_PROD_RD_TEAM_CN_NAME,';
    V_IN_L1_NAME := 'A.L1_NAME,';
    V_IN_L2_NAME := 'A.L2_NAME,';
    V_INSERT_LV3_PROD_RND_TEAM_CODE := ' AND NVL(A.LV3_PROD_RND_TEAM_CODE, 3) = NVL(B.LV3_PROD_RND_TEAM_CODE, 3)';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := ' AND NVL(A.LV4_PROD_RND_TEAM_CODE, 3) = NVL(B.LV4_PROD_RND_TEAM_CODE, 3)';
    V_INSERT_L1_NAME := ' AND NVL(A.L1_NAME, 1) = NVL(B.L1_NAME, 1)';
    V_INSERT_L2_NAME := ' AND NVL(A.L2_NAME, 2) = NVL(B.L2_NAME, 2)';
    
    --9月版本需求新增量纲
    V_DIMENSION_CODE := 'DIMENSION_CODE,';
    V_DIMENSION_CN_NAME := 'DIMENSION_CN_NAME,';
    V_DIMENSION_EN_NAME := 'DIMENSION_EN_NAME,';
    V_DIMENSION_SUBCATEGORY_CODE := 'DIMENSION_SUBCATEGORY_CODE,';
    V_DIMENSION_SUBCATEGORY_CN_NAME := 'DIMENSION_SUBCATEGORY_CN_NAME,';
    V_DIMENSION_SUBCATEGORY_EN_NAME := 'DIMENSION_SUBCATEGORY_EN_NAME,';
    V_DIMENSION_SUB_DETAIL_CODE := 'DIMENSION_SUB_DETAIL_CODE,';
    V_DIMENSION_SUB_DETAIL_CN_NAME := 'DIMENSION_SUB_DETAIL_CN_NAME,';
    V_DIMENSION_SUB_DETAIL_EN_NAME := 'DIMENSION_SUB_DETAIL_EN_NAME,';
    V_IN_DIMENSION_CODE := 'A.DIMENSION_CODE,';
    V_IN_DIMENSION_CN_NAME := 'A.DIMENSION_CN_NAME,';
    V_IN_DIMENSION_EN_NAME := 'A.DIMENSION_EN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_CODE := 'A.DIMENSION_SUBCATEGORY_CODE,';
    V_IN_DIMENSION_SUBCATEGORY_CN_NAME := 'A.DIMENSION_SUBCATEGORY_CN_NAME,';
    V_IN_DIMENSION_SUBCATEGORY_EN_NAME := 'A.DIMENSION_SUBCATEGORY_EN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_CODE := 'A.DIMENSION_SUB_DETAIL_CODE,';
    V_IN_DIMENSION_SUB_DETAIL_CN_NAME := 'A.DIMENSION_SUB_DETAIL_CN_NAME,';
    V_IN_DIMENSION_SUB_DETAIL_EN_NAME := 'A.DIMENSION_SUB_DETAIL_EN_NAME,';
    V_INSERT_DIMENSION_CODE := ' AND NVL(A.DIMENSION_CODE, 1) = NVL(B.DIMENSION_CODE, 1)';
    V_INSERT_DIMENSION_SUBCATEGORY_CODE := ' AND NVL(A.DIMENSION_SUBCATEGORY_CODE, 2) = NVL(B.DIMENSION_SUBCATEGORY_CODE, 2)';
    V_INSERT_DIMENSION_SUB_DETAIL_CODE := ' AND NVL(A.DIMENSION_SUB_DETAIL_CODE, 3) = NVL(B.DIMENSION_SUB_DETAIL_CODE, 3)';

--202401月版本需求新增SPART
    V_SPART_CODE :='SPART_CODE,';
    V_SPART_CN_NAME := 'SPART_CN_NAME,';
    V_IN_SPART_CODE := 'A.SPART_CODE,';
    V_IN_SPART_CN_NAME := 'A.SPART_CN_NAME,';
    V_INSERT_SPART_CODE := ' AND NVL(A.SPART_CODE, 4) = NVL(B.SPART_CODE, 4)';
	
	--202405月版本需求新增COA
    V_COA_CODE :='COA_CODE,';
    V_COA_CN_NAME := 'COA_CN_NAME,';
    V_IN_COA_CODE := 'A.COA_CODE,';
    V_IN_COA_CN_NAME := 'A.COA_CN_NAME,';
    V_INSERT_COA_CODE := ' AND NVL(A.COA_CODE, 4) = NVL(B.COA_CODE, 4)';
	
        --通用颗粒度的维度时，不需要L1、L2字段和所有量纲字段
    IF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('E','I') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	   --202407需求新增
	V_LV4_PROD_RND_TEAM_CODE := '';
    V_LV4_PROD_RD_TEAM_CN_NAME := '';
    V_IN_LV4_PROD_RND_TEAM_CODE := '';
    V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
	V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	ELSIF F_DIMENSION_TYPE = 'U' AND F_INDUSTRY_FLAG IN ('IAS') THEN 
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
       
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
	   V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';

	
    --盈利颗粒度的维度时，不需要LV3字段和所有量纲字段
  ELSIF F_DIMENSION_TYPE = 'P' THEN
       V_LV3_PROD_RND_TEAM_CODE := '';
       V_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_IN_LV3_PROD_RND_TEAM_CODE := '';
       V_IN_LV3_PROD_RD_TEAM_CN_NAME := '';
       V_INSERT_LV3_PROD_RND_TEAM_CODE := '';
    
       V_DIMENSION_CODE := '';
       V_DIMENSION_CN_NAME := '';
       V_DIMENSION_EN_NAME := '';
       V_DIMENSION_SUBCATEGORY_CODE := '';
       V_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_DIMENSION_SUB_DETAIL_CODE := '';
       V_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_IN_DIMENSION_CODE := '';
       V_IN_DIMENSION_CN_NAME := '';
       V_IN_DIMENSION_EN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_CODE := '';
       V_IN_DIMENSION_SUBCATEGORY_CN_NAME := '';
       V_IN_DIMENSION_SUBCATEGORY_EN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_CODE := '';
       V_IN_DIMENSION_SUB_DETAIL_CN_NAME := '';
       V_IN_DIMENSION_SUB_DETAIL_EN_NAME := '';
       V_INSERT_DIMENSION_CODE := '';
       V_INSERT_DIMENSION_SUBCATEGORY_CODE := '';
       V_INSERT_DIMENSION_SUB_DETAIL_CODE := '';
       V_SPART_CODE := '';
	   V_SPART_CN_NAME  := '';
	   V_IN_SPART_CODE  := '';
	   V_IN_SPART_CN_NAME := '';
	   V_INSERT_SPART_CODE := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	     --202407需求新增
		V_LV4_PROD_RND_TEAM_CODE := '';
		V_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_IN_LV4_PROD_RND_TEAM_CODE := '';
		V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	   
    --量纲颗粒度的维度时，不需要L1、L2字段
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'I' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   
	      --202407需求新增
		V_LV4_PROD_RND_TEAM_CODE := '';
		V_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_IN_LV4_PROD_RND_TEAM_CODE := '';
		V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_INSERT_LV4_PROD_RND_TEAM_CODE := '';

   ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'E' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
	   
	     --202407需求新增
		V_LV4_PROD_RND_TEAM_CODE := '';
		V_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_IN_LV4_PROD_RND_TEAM_CODE := '';
		V_IN_LV4_PROD_RD_TEAM_CN_NAME := '';
		V_INSERT_LV4_PROD_RND_TEAM_CODE := '';
	
	    --量纲颗粒度的维度时，不需要L1、L2字段
  ELSIF F_DIMENSION_TYPE = 'D' AND F_INDUSTRY_FLAG = 'IAS' THEN
       V_L1_NAME := '';
       V_L2_NAME := '';
       V_IN_L1_NAME := '';
       V_IN_L2_NAME := '';
       V_INSERT_L1_NAME := '';
       V_INSERT_L2_NAME := '';
	   
	   V_COA_CODE := '';
	   V_COA_CN_NAME  := '';
	   V_IN_COA_CODE  := '';
	   V_IN_COA_CN_NAME := '';
	   V_INSERT_COA_CODE := '';
	   

		
    ELSE
      NULL;
    END IF;
    
        DBMS_OUTPUT.PUT_LINE('-------参数重置------------'); 
		       DBMS_OUTPUT.PUT_LINE('-------准备执行SQL------------'); 

  --第一步先往临时表里插数: 分视角分地区(全球、国内、海外)分BG计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重
  V_SQL :=
  'INSERT INTO DM_FOC_WEIGHT_CATE_INFO_TEMP
    (VERSION_ID,
     VERSION_NAME,
     VIEW_FLAG,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
	 V_LV4_PROD_RND_TEAM_CODE ||
     V_LV4_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
	 V_SPART_CODE ||
	 V_SPART_CN_NAME ||
	 V_COA_CODE ||
	 V_COA_CN_NAME ||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     WEIGHT_RATE,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME)
    WITH SUM_COST_TEMP AS
     (
      --分视角, 根据LV0,专家团,品类和年字段,收敛实际发货额
			SELECT  
			T.CALIBER_FLAG,
			T.OVERSEA_FLAG,
			T.LV0_PROD_LIST_CODE,
			T.LV0_PROD_LIST_CN_NAME,
			T.LV0_PROD_LIST_EN_NAME,
			T.VIEW_FLAG,
			T.LV0_PROD_RND_TEAM_CODE,
			T.LV0_PROD_RD_TEAM_CN_NAME,
			T.LV1_PROD_RND_TEAM_CODE,
			T.LV1_PROD_RD_TEAM_CN_NAME,
			T.LV2_PROD_RND_TEAM_CODE,
			T.LV2_PROD_RD_TEAM_CN_NAME,'||
			V_LV3_PROD_RND_TEAM_CODE ||
			V_LV3_PROD_RD_TEAM_CN_NAME ||
			V_LV4_PROD_RND_TEAM_CODE ||
			V_LV4_PROD_RD_TEAM_CN_NAME ||
			V_L1_NAME ||
			V_L2_NAME ||
			V_DIMENSION_CODE ||
			V_DIMENSION_CN_NAME ||
			V_DIMENSION_EN_NAME||
			V_DIMENSION_SUBCATEGORY_CODE ||
			V_DIMENSION_SUBCATEGORY_CN_NAME ||
			V_DIMENSION_SUBCATEGORY_EN_NAME||
			V_DIMENSION_SUB_DETAIL_CODE ||
			V_DIMENSION_SUB_DETAIL_CN_NAME ||
			V_DIMENSION_SUB_DETAIL_EN_NAME ||
			V_SPART_CODE ||
			V_SPART_CN_NAME ||
			V_COA_CODE ||
			V_COA_CN_NAME ||' --202405新增
			T.L3_CEG_CODE,
			T.L3_CEG_CN_NAME,
			T.L3_CEG_SHORT_CN_NAME,
			T.L4_CEG_CODE,
			T.L4_CEG_CN_NAME,
			T.L4_CEG_SHORT_CN_NAME,
			T.CATEGORY_CODE,
			T.CATEGORY_CN_NAME,
			TO_CHAR(T.PERIOD_YEAR) AS PERIOD_YEAR,
			SUM(T.RMB_COST_AMT) AS COST_AMT,
			--SUM(GS_DECRYPT(T.RMB_COST_AMT,''f_keystr'', ''AES128'', ''CBC'', ''SHA256'')) AS COST_AMT,
			DECODE(T.PERIOD_YEAR,
			 YEAR(CURRENT_TIMESTAMP),
			 0,
			 YEAR(CURRENT_TIMESTAMP) - 1,
			 1,
			 NULL) AS YEAR_FLAG
        FROM '||V_FROM_TABLE ||' T
       GROUP BY  T.CALIBER_FLAG,
                 T.OVERSEA_FLAG,
                 T.LV0_PROD_LIST_CODE,
                 T.LV0_PROD_LIST_CN_NAME,
                 T.LV0_PROD_LIST_EN_NAME,
                 T.VIEW_FLAG,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_LV3_PROD_RND_TEAM_CODE ||
                 V_LV3_PROD_RD_TEAM_CN_NAME ||
				 V_LV4_PROD_RND_TEAM_CODE ||
                 V_LV4_PROD_RD_TEAM_CN_NAME ||
                 V_L1_NAME ||
                 V_L2_NAME ||
                 V_DIMENSION_CODE ||
                 V_DIMENSION_CN_NAME ||
                 V_DIMENSION_EN_NAME||
                 V_DIMENSION_SUBCATEGORY_CODE ||
                 V_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_DIMENSION_SUB_DETAIL_CODE ||
                 V_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_DIMENSION_SUB_DETAIL_EN_NAME ||
				 V_SPART_CODE ||
				 V_SPART_CN_NAME ||
				 V_COA_CODE ||
				 V_COA_CN_NAME ||'
                 T.L3_CEG_CODE,
                 T.L3_CEG_CN_NAME,
                 T.L3_CEG_SHORT_CN_NAME,
                 T.L4_CEG_CODE,
                 T.L4_CEG_CN_NAME,
                 T.L4_CEG_SHORT_CN_NAME,
                 T.CATEGORY_CODE,
                 T.CATEGORY_CN_NAME,
                 T.PERIOD_YEAR),
    
    YEAR_AMT_TEMP AS
     ( 
      --分视角开窗求出年的分组总发货额
      SELECT  A.CALIBER_FLAG,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              DECODE(A.PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     A.PERIOD_YEAR || ''YTD'',
                     A.PERIOD_YEAR) AS PERIOD_YEAR,
              A.COST_AMT,
              SUM(A.COST_AMT) OVER(PARTITION BY A.CALIBER_FLAG,A.OVERSEA_FLAG,A.LV0_PROD_LIST_CODE,A.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE, A.PERIOD_YEAR) AS YEAR_AMT
        FROM SUM_COST_TEMP A),
    
    TWO_YEAR_TEMP AS
     (
      --分视角收敛上一年到今年YTD的总金额
      SELECT  A.CALIBER_FLAG,
              A.OVERSEA_FLAG,
              A.LV0_PROD_LIST_CODE,
              A.LV0_PROD_LIST_CN_NAME,
              A.LV0_PROD_LIST_EN_NAME,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              SUM(A.COST_AMT) AS YEAR_AMT,
              SUM(SUM(A.COST_AMT)) OVER(PARTITION BY A.CALIBER_FLAG,A.OVERSEA_FLAG,A.LV0_PROD_LIST_CODE,A.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE ) AS TOTAL_YEAR_AMT
        FROM SUM_COST_TEMP A
       WHERE A.YEAR_FLAG IN (0, 1)
       GROUP BY  A.OVERSEA_FLAG,
                 A.CALIBER_FLAG,
                 A.LV0_PROD_LIST_CODE,
                 A.LV0_PROD_LIST_CN_NAME,
                 A.LV0_PROD_LIST_EN_NAME,
                 A.VIEW_FLAG,
                 A.LV0_PROD_RND_TEAM_CODE,
                 A.LV0_PROD_RD_TEAM_CN_NAME,
                 A.LV1_PROD_RND_TEAM_CODE,
                 A.LV1_PROD_RD_TEAM_CN_NAME,
                 A.LV2_PROD_RND_TEAM_CODE,
                 A.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_LV3_PROD_RND_TEAM_CODE ||
                 V_LV3_PROD_RD_TEAM_CN_NAME ||
				 V_LV4_PROD_RND_TEAM_CODE ||
                 V_LV4_PROD_RD_TEAM_CN_NAME ||
                 V_L1_NAME ||
                 V_L2_NAME ||
                 V_DIMENSION_CODE ||
                 V_DIMENSION_CN_NAME ||
                 V_DIMENSION_EN_NAME||
                 V_DIMENSION_SUBCATEGORY_CODE ||
                 V_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_DIMENSION_SUB_DETAIL_CODE ||
                 V_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_DIMENSION_SUB_DETAIL_EN_NAME ||
				 V_SPART_CODE ||
				 V_SPART_CN_NAME ||
				 V_COA_CODE ||
				 V_COA_CN_NAME ||'
                 A.L3_CEG_CODE,
                 A.L3_CEG_CN_NAME,
                 A.L3_CEG_SHORT_CN_NAME,
                 A.L4_CEG_CODE,
                 A.L4_CEG_CN_NAME,
                 A.L4_CEG_SHORT_CN_NAME,
                 A.CATEGORY_CODE,
                 A.CATEGORY_CN_NAME)
    
    --区间年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           P.VIEW_FLAG,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' || YEAR(CURRENT_TIMESTAMP) ||
           ''YTD'' AS PERIOD_YEAR,
           P.LV0_PROD_RND_TEAM_CODE,
           P.LV0_PROD_RD_TEAM_CN_NAME,
           P.LV1_PROD_RND_TEAM_CODE,
           P.LV1_PROD_RD_TEAM_CN_NAME,
           P.LV2_PROD_RND_TEAM_CODE,
           P.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_LV4_PROD_RND_TEAM_CODE ||
           V_LV4_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_SPART_CODE ||
		   V_SPART_CN_NAME ||
		   V_COA_CODE ||
		   V_COA_CN_NAME ||'
           P.L3_CEG_CODE,
           P.L3_CEG_CN_NAME,
           P.L3_CEG_SHORT_CN_NAME,
           P.L4_CEG_CODE,
           P.L4_CEG_CN_NAME,
           P.L4_CEG_SHORT_CN_NAME,
           P.CATEGORY_CODE,
           P.CATEGORY_CN_NAME,
           P.YEAR_AMT / NULLIF(P.TOTAL_YEAR_AMT,0) AS WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG,
           P.CALIBER_FLAG,
           P.OVERSEA_FLAG,
           P.LV0_PROD_LIST_CODE,
           P.LV0_PROD_LIST_CN_NAME,
           P.LV0_PROD_LIST_EN_NAME
      FROM TWO_YEAR_TEMP P
    UNION ALL
    --单个会计年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           Q.VIEW_FLAG,
           TO_CHAR(Q.PERIOD_YEAR) AS PERIOD_YEAR,
           Q.LV0_PROD_RND_TEAM_CODE,
           Q.LV0_PROD_RD_TEAM_CN_NAME,
           Q.LV1_PROD_RND_TEAM_CODE,
           Q.LV1_PROD_RD_TEAM_CN_NAME,
           Q.LV2_PROD_RND_TEAM_CODE,
           Q.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_LV4_PROD_RND_TEAM_CODE ||
           V_LV4_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_SPART_CODE ||
		   V_SPART_CN_NAME ||
		   V_COA_CODE ||
		   V_COA_CN_NAME ||'
           Q.L3_CEG_CODE,
           Q.L3_CEG_CN_NAME,
           Q.L3_CEG_SHORT_CN_NAME,
           Q.L4_CEG_CODE,
           Q.L4_CEG_CN_NAME,
           Q.L4_CEG_SHORT_CN_NAME,
           Q.CATEGORY_CODE,
           Q.CATEGORY_CN_NAME,
           Q.COST_AMT / NULLIF(Q.YEAR_AMT,0) AS WEIGHT_RATE,
           ''N'' AS DOUBLE_FLAG,
           Q.CALIBER_FLAG,
           Q.OVERSEA_FLAG,
           Q.LV0_PROD_LIST_CODE,
           Q.LV0_PROD_LIST_CN_NAME,
           Q.LV0_PROD_LIST_EN_NAME
      FROM YEAR_AMT_TEMP Q';
     DBMS_OUTPUT.PUT_LINE('-------执行SQL---1----开始执行，包含解密-------------'); 
	  
    EXECUTE IMMEDIATE V_SQL;
    DBMS_OUTPUT.PUT_LINE('-------执行SQL---1----结束-------------'); 
	DBMS_OUTPUT.PUT_LINE(V_SQL); 
  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角分地区(全球、国内、海外)分BG各年TOP品类及权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_FORMULA_SQL_TXT => V_SQL,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
  

  
----------------------------------------------------------------------以上第一步先处理按照分地区(全球、国内、海外)分BG的权重数据-------------

----------------------------------------------------------------------以下第二步重新计算按照总地区(全球)总BG(集团)的角度去打TOP标签数据------------------------


       DBMS_OUTPUT.PUT_LINE('-------2   往临时表里插数: 分视角计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重 准备------------'); 
  --往临时表里插数: 分视角计算前三年和今年YTD,以及去年+今年YTD的TOP品类及权重
  V_SQL :=
  'INSERT INTO DM_FOC_TOP_CATE_INFO_TEMP
    (VERSION_ID,
     VERSION_NAME,
     VIEW_FLAG,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
	 V_LV4_PROD_RND_TEAM_CODE ||
     V_LV4_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
    V_SPART_CODE ||
    V_SPART_CN_NAME ||
	V_COA_CODE ||
	V_COA_CN_NAME ||'
     L3_CEG_CODE,
     L3_CEG_CN_NAME,
     L3_CEG_SHORT_CN_NAME,
     L4_CEG_CODE,
     L4_CEG_CN_NAME,
     L4_CEG_SHORT_CN_NAME,
     CATEGORY_CODE,
     CATEGORY_CN_NAME,
     WEIGHT_RATE,
     DOUBLE_FLAG,
     CALIBER_FLAG)
    WITH SUM_COST_TEMP AS
     (
      --分视角, 根据LV0,专家团,品类和年字段,收敛实际发货额
      SELECT  
              T.CALIBER_FLAG,
              T.VIEW_FLAG,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              TO_CHAR(T.PERIOD_YEAR) AS PERIOD_YEAR,
              --SUM(GS_DECRYPT(T.RMB_COST_AMT,'' f_keystr'', ''AES128'', ''CBC'', ''SHA256'')) AS COST_AMT,
              SUM(T.RMB_COST_AMT) AS COST_AMT,
              DECODE(T.PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     0,
                     YEAR(CURRENT_TIMESTAMP) - 1,
                     1,
                     NULL) AS YEAR_FLAG
        FROM '||V_FROM_TABLE ||' T 
       WHERE T.OVERSEA_FLAG = ''G'' -- 第二步 从总地区(全球)和总BG(集团)的角度去打TOP标签
	     AND T.LV0_PROD_LIST_CODE = ''GR''
       GROUP BY  T.CALIBER_FLAG,
                 T.VIEW_FLAG,
                 T.LV0_PROD_RND_TEAM_CODE,
                 T.LV0_PROD_RD_TEAM_CN_NAME,
                 T.LV1_PROD_RND_TEAM_CODE,
                 T.LV1_PROD_RD_TEAM_CN_NAME,
                 T.LV2_PROD_RND_TEAM_CODE,
                 T.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_LV3_PROD_RND_TEAM_CODE ||
                 V_LV3_PROD_RD_TEAM_CN_NAME ||
				 V_LV4_PROD_RND_TEAM_CODE ||
                 V_LV4_PROD_RD_TEAM_CN_NAME ||
                 V_L1_NAME ||
                 V_L2_NAME ||
                 V_DIMENSION_CODE ||
                 V_DIMENSION_CN_NAME ||
                 V_DIMENSION_EN_NAME||
                 V_DIMENSION_SUBCATEGORY_CODE ||
                 V_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_DIMENSION_SUB_DETAIL_CODE ||
                 V_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_DIMENSION_SUB_DETAIL_EN_NAME ||
				 V_SPART_CODE ||
				 V_SPART_CN_NAME ||
				 V_COA_CODE ||
				 V_COA_CN_NAME ||'
                 T.L3_CEG_CODE,
                 T.L3_CEG_CN_NAME,
                 T.L3_CEG_SHORT_CN_NAME,
                 T.L4_CEG_CODE,
                 T.L4_CEG_CN_NAME,
                 T.L4_CEG_SHORT_CN_NAME,
                 T.CATEGORY_CODE,
                 T.CATEGORY_CN_NAME,
                 T.PERIOD_YEAR),
    
    YEAR_AMT_TEMP AS
     ( 
      --分视角开窗求出年的分组总发货额
      SELECT  A.CALIBER_FLAG,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              DECODE(A.PERIOD_YEAR,
                     YEAR(CURRENT_TIMESTAMP),
                     A.PERIOD_YEAR || ''YTD'',
                     A.PERIOD_YEAR) AS PERIOD_YEAR,
              A.COST_AMT,
              SUM(A.COST_AMT) OVER(PARTITION BY A.CALIBER_FLAG,A.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE, A.PERIOD_YEAR) AS YEAR_AMT
        FROM SUM_COST_TEMP A),
    
    TWO_YEAR_TEMP AS
     (
      --分视角收敛上一年到今年YTD的总金额
      SELECT  A.CALIBER_FLAG,
              A.VIEW_FLAG,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              SUM(A.COST_AMT) AS YEAR_AMT,
              SUM(SUM(A.COST_AMT)) OVER(PARTITION BY A.CALIBER_FLAG,A.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' A.LV0_PROD_RND_TEAM_CODE, A.LV1_PROD_RND_TEAM_CODE, A.LV2_PROD_RND_TEAM_CODE ) AS TOTAL_YEAR_AMT
        FROM SUM_COST_TEMP A
       WHERE A.YEAR_FLAG IN (0, 1)
       GROUP BY  A.CALIBER_FLAG,
                 A.VIEW_FLAG,
                 A.LV0_PROD_RND_TEAM_CODE,
                 A.LV0_PROD_RD_TEAM_CN_NAME,
                 A.LV1_PROD_RND_TEAM_CODE,
                 A.LV1_PROD_RD_TEAM_CN_NAME,
                 A.LV2_PROD_RND_TEAM_CODE,
                 A.LV2_PROD_RD_TEAM_CN_NAME,'||
                 V_LV3_PROD_RND_TEAM_CODE ||
                 V_LV3_PROD_RD_TEAM_CN_NAME ||
				 V_LV4_PROD_RND_TEAM_CODE ||
                 V_LV4_PROD_RD_TEAM_CN_NAME ||
                 V_L1_NAME ||
                 V_L2_NAME ||
                 V_DIMENSION_CODE ||
                 V_DIMENSION_CN_NAME ||
                 V_DIMENSION_EN_NAME||
                 V_DIMENSION_SUBCATEGORY_CODE ||
                 V_DIMENSION_SUBCATEGORY_CN_NAME ||
                 V_DIMENSION_SUBCATEGORY_EN_NAME||
                 V_DIMENSION_SUB_DETAIL_CODE ||
                 V_DIMENSION_SUB_DETAIL_CN_NAME ||
                 V_DIMENSION_SUB_DETAIL_EN_NAME ||
				 V_SPART_CODE ||
				 V_SPART_CN_NAME ||
				 V_COA_CODE ||
				 V_COA_CN_NAME ||'
                 A.L3_CEG_CODE,
                 A.L3_CEG_CN_NAME,
                 A.L3_CEG_SHORT_CN_NAME,
                 A.L4_CEG_CODE,
                 A.L4_CEG_CN_NAME,
                 A.L4_CEG_SHORT_CN_NAME,
                 A.CATEGORY_CODE,
                 A.CATEGORY_CN_NAME)
    
    --区间年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           P.VIEW_FLAG,
           YEAR(CURRENT_TIMESTAMP) - 1 || ''+'' || YEAR(CURRENT_TIMESTAMP) ||
           ''YTD'' AS PERIOD_YEAR,
           P.LV0_PROD_RND_TEAM_CODE,
           P.LV0_PROD_RD_TEAM_CN_NAME,
           P.LV1_PROD_RND_TEAM_CODE,
           P.LV1_PROD_RD_TEAM_CN_NAME,
           P.LV2_PROD_RND_TEAM_CODE,
           P.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_LV4_PROD_RND_TEAM_CODE ||
           V_LV4_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_SPART_CODE ||
		   V_SPART_CN_NAME ||
		   V_COA_CODE ||
		   V_COA_CN_NAME ||'
           P.L3_CEG_CODE,
           P.L3_CEG_CN_NAME,
           P.L3_CEG_SHORT_CN_NAME,
           P.L4_CEG_CODE,
           P.L4_CEG_CN_NAME,
           P.L4_CEG_SHORT_CN_NAME,
           P.CATEGORY_CODE,
           P.CATEGORY_CN_NAME,
           P.YEAR_AMT / NULLIF(P.TOTAL_YEAR_AMT,0) AS WEIGHT_RATE,
           ''Y'' AS DOUBLE_FLAG,
           P.CALIBER_FLAG
      FROM TWO_YEAR_TEMP P
    UNION ALL
    --单个会计年的权重
   SELECT '|| V_VERSION_ID ||' AS VERSION_ID,
          '''|| V_VERSION_NAME ||''' AS VERSION_NAME,
           Q.VIEW_FLAG,
           TO_CHAR(Q.PERIOD_YEAR) AS PERIOD_YEAR,
           Q.LV0_PROD_RND_TEAM_CODE,
           Q.LV0_PROD_RD_TEAM_CN_NAME,
           Q.LV1_PROD_RND_TEAM_CODE,
           Q.LV1_PROD_RD_TEAM_CN_NAME,
           Q.LV2_PROD_RND_TEAM_CODE,
           Q.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_LV3_PROD_RND_TEAM_CODE ||
           V_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_LV4_PROD_RND_TEAM_CODE ||
           V_LV4_PROD_RD_TEAM_CN_NAME ||
           V_L1_NAME ||
           V_L2_NAME ||
           V_DIMENSION_CODE ||
           V_DIMENSION_CN_NAME ||
           V_DIMENSION_EN_NAME||
           V_DIMENSION_SUBCATEGORY_CODE ||
           V_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_DIMENSION_SUBCATEGORY_EN_NAME||
           V_DIMENSION_SUB_DETAIL_CODE ||
           V_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_SPART_CODE ||
		   V_SPART_CN_NAME ||
		   V_COA_CODE ||
		   V_COA_CN_NAME ||'
           Q.L3_CEG_CODE,
           Q.L3_CEG_CN_NAME,
           Q.L3_CEG_SHORT_CN_NAME,
           Q.L4_CEG_CODE,
           Q.L4_CEG_CN_NAME,
           Q.L4_CEG_SHORT_CN_NAME,
           Q.CATEGORY_CODE,
           Q.CATEGORY_CN_NAME,
           Q.COST_AMT / NULLIF(Q.YEAR_AMT,0) AS WEIGHT_RATE,
           ''N'' AS DOUBLE_FLAG,
           Q.CALIBER_FLAG
      FROM YEAR_AMT_TEMP Q';
	
    EXECUTE IMMEDIATE V_SQL;
	DBMS_OUTPUT.PUT_LINE(V_SQL); 
    DBMS_OUTPUT.PUT_LINE('-------2   执行SQL2结束------------'); 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '创建临时表, 并往临时表里插数: 分视角各年TOP品类及权重, 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
    
		
			       DBMS_OUTPUT.PUT_LINE('-------3   往TOP品类清单插数, 打上前95%TOP的标识 准备------------'); 
  --往TOP品类清单插数, 打上前95%TOP的标识
  V_SQL :=
  'INSERT INTO '||V_TO_TABLE||'
    (VERSION_ID,
     VERSION_NAME,
     PERIOD_YEAR,
     LV0_PROD_RND_TEAM_CODE,
     LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE,
     LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE,
     LV2_PROD_RD_TEAM_CN_NAME,'||
     V_LV3_PROD_RND_TEAM_CODE ||
     V_LV3_PROD_RD_TEAM_CN_NAME ||
	 V_LV4_PROD_RND_TEAM_CODE ||
     V_LV4_PROD_RD_TEAM_CN_NAME ||
     V_L1_NAME ||
     V_L2_NAME ||
     V_DIMENSION_CODE ||
     V_DIMENSION_CN_NAME ||
     V_DIMENSION_EN_NAME||
     V_DIMENSION_SUBCATEGORY_CODE ||
     V_DIMENSION_SUBCATEGORY_CN_NAME ||
     V_DIMENSION_SUBCATEGORY_EN_NAME||
     V_DIMENSION_SUB_DETAIL_CODE ||
     V_DIMENSION_SUB_DETAIL_CN_NAME ||
     V_DIMENSION_SUB_DETAIL_EN_NAME ||
     V_SPART_CODE ||
     V_SPART_CN_NAME ||
	 V_COA_CODE ||
	 V_COA_CN_NAME ||'
     TOP_L3_CEG_CODE,
     TOP_L3_CEG_CN_NAME,
     TOP_L3_CEG_SHORT_CN_NAME,
     TOP_L4_CEG_CODE,
     TOP_L4_CEG_CN_NAME,
     TOP_L4_CEG_SHORT_CN_NAME,
     TOP_CATEGORY_CODE,
     TOP_CATEGORY_CN_NAME,
     WEIGHT_RATE,
     CREATED_BY,
     CREATION_DATE,
     LAST_UPDATED_BY,
     LAST_UPDATE_DATE,
     DEL_FLAG,
     VIEW_FLAG,
     IS_TOP_FLAG,
     DOUBLE_FLAG,
     CALIBER_FLAG,
     OVERSEA_FLAG,
     LV0_PROD_LIST_CODE,
     LV0_PROD_LIST_CN_NAME,
     LV0_PROD_LIST_EN_NAME
     )
    WITH BASE_FLAG_TEMP AS
     (
      --如果品类下所有ITEM的权重值相同, 或第二位权重值到末尾权重值相同, 则最大排序值标记为1或2, 反之为一般数据情况
      SELECT R.VERSION_ID,
              R.VERSION_NAME,
              R.VIEW_FLAG,
              R.PERIOD_YEAR,
              R.LV0_PROD_RND_TEAM_CODE,
              R.LV0_PROD_RD_TEAM_CN_NAME,
              R.LV1_PROD_RND_TEAM_CODE,
              R.LV1_PROD_RD_TEAM_CN_NAME,
              R.LV2_PROD_RND_TEAM_CODE,
              R.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
              V_SPART_CODE ||
              V_SPART_CN_NAME ||
	          V_COA_CODE ||
			  V_COA_CN_NAME ||'
              R.L3_CEG_CODE,
              R.L3_CEG_CN_NAME,
              R.L3_CEG_SHORT_CN_NAME,
              R.L4_CEG_CODE,
              R.L4_CEG_CN_NAME,
              R.L4_CEG_SHORT_CN_NAME,
              R.CATEGORY_CODE,
              R.CATEGORY_CN_NAME,
              R.WEIGHT_RATE,
              R.DRANK_SORT,
              R.CALIBER_FLAG,
              MAX(R.DRANK_SORT) OVER(PARTITION BY R.CALIBER_FLAG,R.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' R.LV0_PROD_RND_TEAM_CODE, R.LV1_PROD_RND_TEAM_CODE, R.LV2_PROD_RND_TEAM_CODE) AS MAX_DRANK_SORT
        FROM (SELECT P.VERSION_ID,
                      P.VERSION_NAME,
                      P.VIEW_FLAG,
                      P.PERIOD_YEAR,
                      P.LV0_PROD_RND_TEAM_CODE,
                      P.LV0_PROD_RD_TEAM_CN_NAME,
                      P.LV1_PROD_RND_TEAM_CODE,
                      P.LV1_PROD_RD_TEAM_CN_NAME,
                      P.LV2_PROD_RND_TEAM_CODE,
                      P.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_LV3_PROD_RND_TEAM_CODE ||
                      V_LV3_PROD_RD_TEAM_CN_NAME ||
					  V_LV4_PROD_RND_TEAM_CODE ||
                      V_LV4_PROD_RD_TEAM_CN_NAME ||
                      V_L1_NAME ||
                      V_L2_NAME ||
                      V_DIMENSION_CODE ||
                      V_DIMENSION_CN_NAME ||
                      V_DIMENSION_EN_NAME||
                      V_DIMENSION_SUBCATEGORY_CODE ||
                      V_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_DIMENSION_SUB_DETAIL_CODE ||
                      V_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_DIMENSION_SUB_DETAIL_EN_NAME ||
					  V_SPART_CODE ||
					  V_SPART_CN_NAME ||
					  V_COA_CODE ||
				      V_COA_CN_NAME ||'
                      P.L3_CEG_CODE,
                      P.L3_CEG_CN_NAME,
                      P.L3_CEG_SHORT_CN_NAME,
                      P.L4_CEG_CODE,
                      P.L4_CEG_CN_NAME,
                      P.L4_CEG_SHORT_CN_NAME,
                      P.CATEGORY_CODE,
                      P.CATEGORY_CN_NAME,
                      P.WEIGHT_RATE,
                      P.CALIBER_FLAG,
                      DENSE_RANK() OVER(PARTITION BY P.CALIBER_FLAG,P.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE ||' P.LV0_PROD_RND_TEAM_CODE, P.LV1_PROD_RND_TEAM_CODE, P.LV2_PROD_RND_TEAM_CODE ORDER BY P.WEIGHT_RATE DESC) AS DRANK_SORT
                 FROM DM_FOC_TOP_CATE_INFO_TEMP P
                WHERE P.DOUBLE_FLAG = ''Y'') R),
    
    NORMAL_DATA_TEMP AS
     (
      --筛选一般数据情况: 最大排序值标识大于2的数据, 并带出ROW_NUMBER排序字段
      SELECT T.VERSION_ID,
              T.VERSION_NAME,
              T.VIEW_FLAG,
              T.PERIOD_YEAR,
              T.LV0_PROD_RND_TEAM_CODE,
              T.LV0_PROD_RD_TEAM_CN_NAME,
              T.LV1_PROD_RND_TEAM_CODE,
              T.LV1_PROD_RD_TEAM_CN_NAME,
              T.LV2_PROD_RND_TEAM_CODE,
              T.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              T.L3_CEG_CODE,
              T.L3_CEG_CN_NAME,
              T.L3_CEG_SHORT_CN_NAME,
              T.L4_CEG_CODE,
              T.L4_CEG_CN_NAME,
              T.L4_CEG_SHORT_CN_NAME,
              T.CATEGORY_CODE,
              T.CATEGORY_CN_NAME,
              T.WEIGHT_RATE,
              ROW_NUMBER() OVER(PARTITION BY T.CALIBER_FLAG,T.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE||V_LV4_PROD_RND_TEAM_CODE ||' T.LV0_PROD_RND_TEAM_CODE, T.LV1_PROD_RND_TEAM_CODE, T.LV2_PROD_RND_TEAM_CODE ORDER BY T.WEIGHT_RATE DESC) AS RRANK_SORT,
              T.DRANK_SORT,
              T.MAX_DRANK_SORT,
              T.CALIBER_FLAG
        FROM BASE_FLAG_TEMP T
       WHERE T.MAX_DRANK_SORT > 2),
    
    FLAG_DATA_TEMP AS
     (
      --对NORMAL_DATA_TEMP临时表打上辅助列标识:(ACCU_WEIGHT->ACCU_FLAG->CAL_FLAG)
      SELECT S.VERSION_ID,
              S.VERSION_NAME,
              S.VIEW_FLAG,
              S.PERIOD_YEAR,
              S.LV0_PROD_RND_TEAM_CODE,
              S.LV0_PROD_RD_TEAM_CN_NAME,
              S.LV1_PROD_RND_TEAM_CODE,
              S.LV1_PROD_RD_TEAM_CN_NAME,
              S.LV2_PROD_RND_TEAM_CODE,
              S.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              S.L3_CEG_CODE,
              S.L3_CEG_CN_NAME,
              S.L3_CEG_SHORT_CN_NAME,
              S.L4_CEG_CODE,
              S.L4_CEG_CN_NAME,
              S.L4_CEG_SHORT_CN_NAME,
              S.CATEGORY_CODE,
              S.CATEGORY_CN_NAME,
              S.WEIGHT_RATE,
              S.ACCU_WEIGHT,
              S.ACCU_FLAG,
              S.CALIBER_FLAG,
              SUM(S.ACCU_FLAG) OVER(PARTITION BY S.CALIBER_FLAG,S.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' S.LV0_PROD_RND_TEAM_CODE, S.LV1_PROD_RND_TEAM_CODE, S.LV2_PROD_RND_TEAM_CODE ORDER BY S.RRANK_SORT) AS CAL_FLAG --根据ROW_NUMBER累计求和ACCU_FLAG标识
        FROM (SELECT R.VERSION_ID,
                      R.VERSION_NAME,
                      R.VIEW_FLAG,
                      R.PERIOD_YEAR,
                      R.LV0_PROD_RND_TEAM_CODE,
                      R.LV0_PROD_RD_TEAM_CN_NAME,
                      R.LV1_PROD_RND_TEAM_CODE,
                      R.LV1_PROD_RD_TEAM_CN_NAME,
                      R.LV2_PROD_RND_TEAM_CODE,
                      R.LV2_PROD_RD_TEAM_CN_NAME,'||
                      V_LV3_PROD_RND_TEAM_CODE ||
                      V_LV3_PROD_RD_TEAM_CN_NAME ||
					  V_LV4_PROD_RND_TEAM_CODE ||
                      V_LV4_PROD_RD_TEAM_CN_NAME ||
                      V_L1_NAME ||
                      V_L2_NAME ||
                      V_DIMENSION_CODE ||
                      V_DIMENSION_CN_NAME ||
                      V_DIMENSION_EN_NAME||
                      V_DIMENSION_SUBCATEGORY_CODE ||
                      V_DIMENSION_SUBCATEGORY_CN_NAME ||
                      V_DIMENSION_SUBCATEGORY_EN_NAME||
                      V_DIMENSION_SUB_DETAIL_CODE ||
                      V_DIMENSION_SUB_DETAIL_CN_NAME ||
                      V_DIMENSION_SUB_DETAIL_EN_NAME ||
					  V_SPART_CODE ||
					  V_SPART_CN_NAME ||
					  V_COA_CODE ||
					  V_COA_CN_NAME ||'
                      R.L3_CEG_CODE,
                      R.L3_CEG_CN_NAME,
                      R.L3_CEG_SHORT_CN_NAME,
                      R.L4_CEG_CODE,
                      R.L4_CEG_CN_NAME,
                      R.L4_CEG_SHORT_CN_NAME,
                      R.CATEGORY_CODE,
                      R.CATEGORY_CN_NAME,
                      R.WEIGHT_RATE,
                      R.ACCU_WEIGHT,
                      R.RRANK_SORT,
                      R.CALIBER_FLAG,
                      CASE
                        WHEN R.ACCU_WEIGHT >= 0.95 THEN
                         1
                        ELSE
                         0
                      END AS ACCU_FLAG --累计权重大于95,标识为1,反之为0
                 FROM (SELECT Z.VERSION_ID,
                              Z.VERSION_NAME,
                              Z.VIEW_FLAG,
                              Z.PERIOD_YEAR,
                              Z.LV0_PROD_RND_TEAM_CODE,
                              Z.LV0_PROD_RD_TEAM_CN_NAME,
                              Z.LV1_PROD_RND_TEAM_CODE,
                              Z.LV1_PROD_RD_TEAM_CN_NAME,
                              Z.LV2_PROD_RND_TEAM_CODE,
                              Z.LV2_PROD_RD_TEAM_CN_NAME,'||
                              V_LV3_PROD_RND_TEAM_CODE ||
                              V_LV3_PROD_RD_TEAM_CN_NAME ||
							  V_LV4_PROD_RND_TEAM_CODE ||
                              V_LV4_PROD_RD_TEAM_CN_NAME ||
                              V_L1_NAME ||
                              V_L2_NAME ||
                              V_DIMENSION_CODE ||
                              V_DIMENSION_CN_NAME ||
                              V_DIMENSION_EN_NAME||
                              V_DIMENSION_SUBCATEGORY_CODE ||
                              V_DIMENSION_SUBCATEGORY_CN_NAME ||
                              V_DIMENSION_SUBCATEGORY_EN_NAME||
                              V_DIMENSION_SUB_DETAIL_CODE ||
                              V_DIMENSION_SUB_DETAIL_CN_NAME ||
                              V_DIMENSION_SUB_DETAIL_EN_NAME ||
							  V_SPART_CODE ||
							  V_SPART_CN_NAME ||
							  V_COA_CODE ||
							  V_COA_CN_NAME ||'
                              Z.L3_CEG_CODE,
                              Z.L3_CEG_CN_NAME,
                              Z.L3_CEG_SHORT_CN_NAME,
                              Z.L4_CEG_CODE,
                              Z.L4_CEG_CN_NAME,
                              Z.L4_CEG_SHORT_CN_NAME,
                              Z.CATEGORY_CODE,
                              Z.CATEGORY_CN_NAME,
                              Z.WEIGHT_RATE,
                              SUM(Z.WEIGHT_RATE) OVER(PARTITION BY Z.CALIBER_FLAG,Z.VIEW_FLAG,'||V_COA_CODE|| V_SPART_CODE || V_DIMENSION_SUB_DETAIL_CODE || V_DIMENSION_SUBCATEGORY_CODE || V_DIMENSION_CODE || V_L2_NAME || V_L1_NAME || V_LV3_PROD_RND_TEAM_CODE ||V_LV4_PROD_RND_TEAM_CODE||' Z.LV0_PROD_RND_TEAM_CODE, Z.LV1_PROD_RND_TEAM_CODE, Z.LV2_PROD_RND_TEAM_CODE ORDER BY Z.RRANK_SORT) AS ACCU_WEIGHT, --根据ROW_NUMBER累计求和权重值
                              Z.RRANK_SORT,
                              Z.CALIBER_FLAG
                         FROM NORMAL_DATA_TEMP Z) R) S),
    TOP_DATA_TEMP AS
     ( 
      --TOP品类的数据
      SELECT A.VERSION_ID,
              A.VERSION_NAME,
              A.VIEW_FLAG,
              A.PERIOD_YEAR,
              A.LV0_PROD_RND_TEAM_CODE,
              A.LV0_PROD_RD_TEAM_CN_NAME,
              A.LV1_PROD_RND_TEAM_CODE,
              A.LV1_PROD_RD_TEAM_CN_NAME,
              A.LV2_PROD_RND_TEAM_CODE,
              A.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              A.L3_CEG_CODE,
              A.L3_CEG_CN_NAME,
              A.L3_CEG_SHORT_CN_NAME,
              A.L4_CEG_CODE,
              A.L4_CEG_CN_NAME,
              A.L4_CEG_SHORT_CN_NAME,
              A.CATEGORY_CODE,
              A.CATEGORY_CN_NAME,
              A.WEIGHT_RATE,
              A.CALIBER_FLAG
        FROM BASE_FLAG_TEMP A
       WHERE MAX_DRANK_SORT <= 2
      UNION ALL
      SELECT B.VERSION_ID,
              B.VERSION_NAME,
              B.VIEW_FLAG,
              B.PERIOD_YEAR,
              B.LV0_PROD_RND_TEAM_CODE,
              B.LV0_PROD_RD_TEAM_CN_NAME,
              B.LV1_PROD_RND_TEAM_CODE,
              B.LV1_PROD_RD_TEAM_CN_NAME,
              B.LV2_PROD_RND_TEAM_CODE,
              B.LV2_PROD_RD_TEAM_CN_NAME,'||
              V_LV3_PROD_RND_TEAM_CODE ||
              V_LV3_PROD_RD_TEAM_CN_NAME ||
			  V_LV4_PROD_RND_TEAM_CODE ||
              V_LV4_PROD_RD_TEAM_CN_NAME ||
              V_L1_NAME ||
              V_L2_NAME ||
              V_DIMENSION_CODE ||
              V_DIMENSION_CN_NAME ||
              V_DIMENSION_EN_NAME||
              V_DIMENSION_SUBCATEGORY_CODE ||
              V_DIMENSION_SUBCATEGORY_CN_NAME ||
              V_DIMENSION_SUBCATEGORY_EN_NAME||
              V_DIMENSION_SUB_DETAIL_CODE ||
              V_DIMENSION_SUB_DETAIL_CN_NAME ||
              V_DIMENSION_SUB_DETAIL_EN_NAME ||
			  V_SPART_CODE ||
			  V_SPART_CN_NAME ||
			  V_COA_CODE ||
			  V_COA_CN_NAME ||'
              B.L3_CEG_CODE,
              B.L3_CEG_CN_NAME,
              B.L3_CEG_SHORT_CN_NAME,
              B.L4_CEG_CODE,
              B.L4_CEG_CN_NAME,
              B.L4_CEG_SHORT_CN_NAME,
              B.CATEGORY_CODE,
              B.CATEGORY_CN_NAME,
              B.WEIGHT_RATE,
              B.CALIBER_FLAG
        FROM FLAG_DATA_TEMP B
       WHERE CAL_FLAG <= 2)
    
    --临时表左关联TOP品类临时表, 打上是否为TOP类标识
    SELECT A.VERSION_ID,
           A.VERSION_NAME,
           A.PERIOD_YEAR,
           A.LV0_PROD_RND_TEAM_CODE,
           A.LV0_PROD_RD_TEAM_CN_NAME,
           A.LV1_PROD_RND_TEAM_CODE,
           A.LV1_PROD_RD_TEAM_CN_NAME,
           A.LV2_PROD_RND_TEAM_CODE,
           A.LV2_PROD_RD_TEAM_CN_NAME,'||
           V_IN_LV3_PROD_RND_TEAM_CODE ||
           V_IN_LV3_PROD_RD_TEAM_CN_NAME ||
		   V_IN_LV4_PROD_RND_TEAM_CODE ||
           V_IN_LV4_PROD_RD_TEAM_CN_NAME ||
           V_IN_L1_NAME ||
           V_IN_L2_NAME ||
           V_IN_DIMENSION_CODE ||
           V_IN_DIMENSION_CN_NAME ||
           V_IN_DIMENSION_EN_NAME||
           V_IN_DIMENSION_SUBCATEGORY_CODE ||
           V_IN_DIMENSION_SUBCATEGORY_CN_NAME ||
           V_IN_DIMENSION_SUBCATEGORY_EN_NAME||
           V_IN_DIMENSION_SUB_DETAIL_CODE ||
           V_IN_DIMENSION_SUB_DETAIL_CN_NAME ||
           V_IN_DIMENSION_SUB_DETAIL_EN_NAME ||
		   V_IN_SPART_CODE ||
		   V_IN_SPART_CN_NAME ||
		   V_IN_COA_CODE ||
		   V_IN_COA_CN_NAME ||'
           A.L3_CEG_CODE,
           A.L3_CEG_CN_NAME,
           A.L3_CEG_SHORT_CN_NAME,
           A.L4_CEG_CODE,
           A.L4_CEG_CN_NAME,
           A.L4_CEG_SHORT_CN_NAME,
           A.CATEGORY_CODE,
           A.CATEGORY_CN_NAME,
           A.WEIGHT_RATE,
           -1 AS CREATED_BY,
           CURRENT_TIMESTAMP AS CREATION_DATE,
           -1 AS LAST_UPDATED_BY,
           CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
           ''N'' AS DEL_FLAG,
           A.VIEW_FLAG,
           DECODE(B.CATEGORY_CODE, NULL, ''N'', ''Y'') AS IS_TOP_FLAG,
           A.DOUBLE_FLAG,
           A.CALIBER_FLAG,
           A.OVERSEA_FLAG,
           A.LV0_PROD_LIST_CODE,
           A.LV0_PROD_LIST_CN_NAME,
           A.LV0_PROD_LIST_EN_NAME
      FROM DM_FOC_WEIGHT_CATE_INFO_TEMP A
      LEFT JOIN TOP_DATA_TEMP B
        ON A.VIEW_FLAG = B.VIEW_FLAG
       AND A.PERIOD_YEAR = B.PERIOD_YEAR
       AND A.LV0_PROD_RND_TEAM_CODE = B.LV0_PROD_RND_TEAM_CODE
       AND NVL(A.LV1_PROD_RND_TEAM_CODE, 1) = NVL(B.LV1_PROD_RND_TEAM_CODE, 1)
       AND NVL(A.LV2_PROD_RND_TEAM_CODE, 2) = NVL(B.LV2_PROD_RND_TEAM_CODE, 2)
      '||V_INSERT_LV3_PROD_RND_TEAM_CODE
	   ||V_INSERT_LV4_PROD_RND_TEAM_CODE
       ||V_INSERT_L1_NAME
       ||V_INSERT_L2_NAME
       ||V_INSERT_DIMENSION_CODE
       ||V_INSERT_DIMENSION_SUBCATEGORY_CODE
       ||V_INSERT_DIMENSION_SUB_DETAIL_CODE
       ||V_INSERT_SPART_CODE
	   ||V_INSERT_COA_CODE||'
       AND A.L3_CEG_CODE = B.L3_CEG_CODE
       AND A.L4_CEG_CODE = B.L4_CEG_CODE
       AND A.CATEGORY_CODE = B.CATEGORY_CODE
       AND A.CALIBER_FLAG = B.CALIBER_FLAG';
	
    EXECUTE IMMEDIATE V_SQL;
	DBMS_OUTPUT.PUT_LINE(V_SQL); 
    DBMS_OUTPUT.PUT_LINE('-------3  执行SQL3---结束--------------'); 

  --写入日志
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => '往TOP品类表里插数: 前95%的品类打上TOP标识(兜底逻辑), 版本号='||V_VERSION_ID||', 版本名称='||V_VERSION_NAME||',颗粒度：'||F_DIMENSION_TYPE||',产业类型：'||F_INDUSTRY_FLAG,
   F_DML_ROW_COUNT => SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS,
   F_ERRBUF => 'SUCCESS');
   
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE '||V_TO_TABLE;
  
  --日志结束
  V_STEP_MUM := V_STEP_MUM + 1;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_MUM,
   F_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集'||V_TO_TABLE||'统计信息完成!');
 
  RETURN 'SUCCESS';
  
EXCEPTION
  WHEN OTHERS THEN
  X_RESULT_STATUS := '0';
  
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, 
   F_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   F_RESULT_STATUS => X_RESULT_STATUS, 
   F_ERRBUF => SQLSTATE||':'||SQLERRM
   );
END
$$
/

