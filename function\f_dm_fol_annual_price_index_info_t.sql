-- Name: f_dm_fol_annual_price_index_info_t; Type: Function; Schema: fin_dm_opt_foi;

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_fol_annual_price_index_info_t(p_version_id bigint DEFAULT NULL::bigint, p_refresh_type character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
	/*
创建时间：2024-02-26
创建人  ：zwx1275798
背景描述：物流价格指数年度表，仅计算精品海运
参数描述：参数一(p_refresh_type)：JAVA传入的刷新类型，写入版本信息表
          参数二(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_foi.f_dm_fol_annual_price_index_info_t()
变更记录：2024-8-21 qwx1110218 来源表变更：由 dm_fol_route_info_sum_t （价格补录表内关联航线量集成表）变更为 dm_fol_route_price_info_sum_t （价格补录表左关联航线量集成表），
                                           且需要剔除柜型量为空的数据（即 container_qty is not null）；无需将20GP数量×1，40GP数量×2，40HQ数量×2；
*/


declare
	v_sp_name varchar(500) := 'fin_dm_opt_foi.f_dm_fol_annual_price_index_info_t';
	v_tbl_name varchar(500) := 'fin_dm_opt_foi.dm_fol_annual_price_index_info_t';
	v_dml_row_count number default 0 ;
	v_max_last_update_date timestamp;
	v_max_version_code varchar(30);
	v_version_status varchar(30);
	v_apd_max_version_code varchar(30);
	v_max_version_id int;


begin
	x_success_flag := '1';        --1表示成功

	-- 开始记录日志
  perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => ''||v_tbl_name||'，开始运行！',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
        --从物流价格指数年度表中，删除版本信息表中执行失败的 version_id 数据
        delete from fin_dm_opt_foi.dm_fol_annual_price_index_info_t t1 
		where t1.version_id in (select distinct t2.version_id from fin_dm_opt_foi.dm_fol_version_info_t t2 where  t2.step='2001');
		
		--航线量表临时表，储存对航线量表简单逻辑处理后的数据
		drop table if exists annual_route_tmp;
		create temporary table annual_route_tmp(
		 version_id          integer
	    ,period_id           integer
		,year                integer
	    ,transport_mode      character varying(50)
	    ,region_cn_name      character varying(50)
	    ,route               character varying(200)
	    ,source_country_name character varying(100)
	    ,dest_country_name   character varying(100)
		,price_id            character varying(200)
	    ,supplier_short_name character varying(50)
	    ,container_type      character varying(50)
	    ,currency            character varying(10)
	    ,price               numeric(38,10)
	    ,container_qty       numeric(38,10)
	    );
		
		--货量占比临时表，储存货量占比信息
		drop table if exists annual_qty_tmp;
		create temporary table annual_qty_tmp(
		 version_id          integer
		,year                integer
		,period_id           integer
	    ,transport_mode      character varying(50)
	    ,region_cn_name      character varying(50)
	    ,route               character varying(200)
	    ,container_type      character varying(50)
	    ,currency            character varying(10)
		,level_code          character varying(10)
	    ,level_desc          character varying(50)
		,W_i                 numeric
		,W_x                 numeric
	    );
	
		--均价临时表，储存供应商均价、航线均价信息
		drop table if exists annual_price_tmp;
		create temporary table annual_price_tmp(
		 version_id          integer
	    ,period_id           integer
		,year                integer
	    ,transport_mode      character varying(50)
	    ,region_cn_name      character varying(50)
	    ,route               character varying(200)
	    ,source_country_name character varying(100)
	    ,dest_country_name   character varying(100)
	    ,supplier_short_name character varying(50)
	    ,container_type      character varying(50)
	    ,currency            character varying(10)
	    ,price               numeric(38,10)
	    ,container_qty       numeric(38,10)
		,level_code          character varying(10)
	    ,level_desc          character varying(50)
	    ,avg_price           numeric(38,10)
		,apd_period_flag     character varying(10)
	    );
		
		--价格指数临时表，临时储存各层级价格指数sum前的数据
		drop table if exists annual_price_index_tmp;
		create temporary table annual_price_index_tmp(
		 version_id          integer
		,year                integer
	    ,period_id           integer
		,base_period_id      integer
	    ,transport_mode      character varying(50)
	    ,region_cn_name      character varying(50)
	    ,route               character varying(200)
	    ,source_country_name character varying(100)
	    ,dest_country_name   character varying(100)
	    ,container_type      character varying(50)
	    ,currency            character varying(10)
		,level_code          character varying(10)
	    ,level_desc          character varying(50)
		,price_index         numeric
		,weight              numeric
	    );
		
		
	/*	-- 取航线清单表的最后更新时间
		select max(last_update_date) as max_last_update_date into v_max_last_update_date 
		from fin_dm_opt_foi.dm_fol_route_info_t  
		where upper(version_status) in ('AUTO','FINAL');
		
		 -- 取航线清单表的最大版本编码
        select max(version_code) as max_version_code into v_max_version_code     
		from fin_dm_opt_foi.dm_fol_route_info_t  
		where last_update_date = v_max_last_update_date  ;
		
		-- 取航线清单表最大更新时间时version_status的状态
        select max(version_status) as max_version_status into v_version_status 
		from fin_dm_opt_foi.dm_fol_route_info_t  
		where last_update_date = v_max_last_update_date  ; */
		
        -- 取物流价格补录表的最大版本编码
        select max(version_code) as max_version_code into v_apd_max_version_code 
		from fin_dm_opt_foi.apd_fol_route_price_info_t 
		where upper(status)='FINAL';	
		
		
        -- 如果是传version_id调函数取JAVA传入的p_version_id，如果是自动调度的则取航线量汇总表的最大版本ID
        if p_version_id is not null then 
        select  p_version_id into v_max_version_id ;
        else 
        select max(version_id) as max_version_id into v_max_version_id       
		from fin_dm_opt_foi.dm_fol_route_price_info_sum_t;	
        end if 
        ;			
		
		 -- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 2 
		 and source_en_name = 'f_dm_fol_annual_price_index_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');
		 
		 -- 将执行步骤：2  执行中 插入版本信息表中的
        insert into fin_dm_opt_foi.dm_fol_version_info_t(
                    version_id           -- 版本ID（自动生成）
                  , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
                  , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
                  , source_en_name       -- 来源英文描述（可以是表名、函数名等）
                  , source_cn_name       -- 来源中文描述
                  , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
                  , remark               -- 备注
                  , created_by           -- 创建人
                  , creation_date        -- 创建时间
                  , last_updated_by      -- 修改人
                  , last_update_date     -- 修改时间
                  , del_flag             -- 是否删除
                  )
            /* select v_max_version_id  as version_id
                  , v_max_version_code as version_code
                  , 2 as step
                  , 'f_dm_fol_annual_price_index_info_t' as source_en_name
                  , '物流价格指数年度函数' as source_cn_name
                  , nvl(p_refresh_type,'4_AUTO') as refresh_type
                  , 'version_code为航线清单表的最大版本编码' as remark
  	              , -1 as created_by
  	              , current_timestamp as creation_date
  	              , -1 as last_updated_by
  	              , current_timestamp as last_update_date
  	              , 'N' as del_flag
        union all */
             select v_max_version_id   as version_id
                  , v_apd_max_version_code as version_code
                  , 2 as step
                  , 'f_dm_fol_annual_price_index_info_t' as source_en_name
                  , '物流价格指数年度函数' as source_cn_name
                  , nvl(p_refresh_type,'4_AUTO') as refresh_type
                  , 'version_code为物流价格补录表的最大版本编码' as remark
  	              , -1 as created_by
  	              , current_timestamp as creation_date
  	              , -1 as last_updated_by
  	              , current_timestamp as last_update_date
  	              , 'N' as del_flag
                 ;
		
		--从 航线量表 取出 version_id 为最大版本且只有Top清单的数据
		insert into annual_route_tmp(
                version_id
               ,year				
		       ,period_id          
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,source_country_name
		       ,dest_country_name 
               ,price_id 			   
		       ,supplier_short_name
		       ,container_type     
		       ,currency           
               ,price              
               ,container_qty  
                )		
		select  version_id
		       ,year
	           ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_country_name
			   ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,container_type
			   ,currency
			   ,sum(price) as price
			   --,sum(container_qty) as container_qty
			   ,sum(case when container_type = '20GP' then container_qty
                   when container_type = '40GP' then container_qty/2
                   when container_type = '40HQ' then container_qty/2
              end) as container_qty  -- 20240821 modify by qwx1110218 无需x1x2
		from  fin_dm_opt_foi.dm_fol_route_price_info_sum_t
		where 1=1
		  and transport_mode = '精品海运'
		  and version_id = v_max_version_id
		  and price <> 0
		  and container_qty is not null  -- 20240821 add by qwx1110128 替换了来源表，需要剔除 container_qty 是空的数据
		/*  and source_port_name||'_'||dest_port_name in (select distinct route 
		                                                  from fin_dm_opt_foi.dm_fol_route_info_t 
														  where version_status = v_version_status 
														  and version_code = v_max_version_code 
														  and top_route_flag= 'Y' 
														  and transport_mode = '精品海运')*/
		group by version_id
		       ,year
		       ,period_id
			   ,transport_mode
			   ,region_cn_name
			   ,route
			   ,source_country_name
			   ,dest_country_name
			   ,price_id 
			   ,supplier_short_name
			   ,container_type
			   ,currency
		
			   ;
			   
			 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 2,
            p_log_cal_log_desc => '【 annual_route_tmp 航线量临时表，数据量：'||v_dml_row_count||',version_id为'||v_max_version_id,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;


		 
	   --计算航线占区域的货量占比（区分柜型）
		insert into annual_qty_tmp(
                version_id
               ,year
               ,period_id			   
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,container_type     
		       ,currency 
               ,level_code         
	           ,level_desc         
               ,W_i 
                )		
		with tmp1 as (
		select version_id
		      ,year
			  ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,container_type
			  ,currency
			  --,sum(container_qty) as route_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as route_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1	
         where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		  /* and source_port_name||'_'||dest_port_name in (select distinct route 
		                                                   from fin_dm_opt_foi.dm_fol_route_info_t t2 
														   where version_status = v_version_status 
														   and version_code = v_max_version_code 
														   and top_route_flag= 'Y' 
														   and transport_mode = '精品海运')*/
		   and container_qty <> 0	
		  group by version_id
                  ,year
				  ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,container_type
				  ,currency
		) ,
		 tmp2 as (
		select version_id
              ,year
			  ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,container_type
			  ,currency
			  --,sum(container_qty) as region_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as region_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1		
		  where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		 /*  and source_port_name||'_'||dest_port_name in (select distinct route 
		                                                   from fin_dm_opt_foi.dm_fol_route_info_t t2 
														   where version_status = v_version_status 
														   and version_code = v_max_version_code 
														   and top_route_flag= 'Y'
                                                           and transport_mode = '精品海运' )*/
		   and container_qty <> 0
		  group by version_id
                  ,year
				  ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,container_type
				  ,currency
		) 
		select t1.version_id
		      ,t1.year
			  ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.container_type
			  ,t1.currency
			  ,'03'   as level_code
  			  ,'航线' as level_desc
			  ,t1.route_qty/t2.region_qty as W_i
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.period_id = t2.period_id
		 ; 
		 
		  -- 新增ALL柜型，计算航线占区域的货量占比 
		insert into annual_qty_tmp(
                version_id
               ,year		
               ,period_id			   
		       ,transport_mode     
		       ,region_cn_name     
		       ,route              
		       ,container_type     
		       ,currency 
               ,level_code         
	           ,level_desc         
               ,W_i 
                )		
		with tmp1 as (
		select version_id
		      ,year
			  ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,'ALL' as container_type
			  ,currency
			  --,sum(container_qty) as route_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as route_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1	
         where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		   and container_qty <> 0	 
		  group by version_id
                  ,year
				  ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,currency
		) ,
		 tmp2 as (
		select version_id
              ,year
			  ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,'ALL' as container_type
			  ,currency
			  --,sum(container_qty) as region_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as region_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1		
		  where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		   and container_qty <> 0
		  group by version_id
                  ,year
				  ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,currency
		) 
		select t1.version_id
		      ,t1.year
			  ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.container_type
			  ,t1.currency
			  ,'03'   as level_code
  			  ,'航线' as level_desc
			  ,t1.route_qty/t2.region_qty as W_i
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.period_id = t2.period_id
		 ; 
		 
		  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 3,
            p_log_cal_log_desc => '【 annual_qty_tmp 航线占区域的货量占比，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;
 
        --计算区域占运输方式的货量占比 (区分柜型)
		insert into annual_qty_tmp(
                version_id 
               ,year	
               ,period_id			   
		       ,transport_mode     
		       ,region_cn_name                
		       ,container_type     
		       ,currency 
               ,level_code         
	           ,level_desc         
               ,W_x 
                )		
		with tmp1 as (
		select version_id
              ,year	
              ,period_id			  
			  ,transport_mode
			  ,region_cn_name
			  ,container_type
			  ,currency
			  --,sum(container_qty) as region_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as region_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1
         where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		/*   and source_port_name||'_'||dest_port_name in (select distinct route 
		                                                   from fin_dm_opt_foi.dm_fol_route_info_t t2 
														   where version_status = v_version_status 
														   and version_code = v_max_version_code 
														   and top_route_flag= 'Y'
                                                           and transport_mode = '精品海运' )*/
		   and container_qty <> 0	
		  group by version_id
		          ,year	
                  ,period_id				  
				  ,transport_mode
				  ,region_cn_name
				  ,container_type
				  ,currency
		) ,
		 tmp2 as (
		select version_id
		      ,year
              ,period_id			  
			  ,transport_mode
			  ,container_type
			  ,currency
			  --,sum(container_qty) as transport_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as transport_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1		
		  where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		  /* and source_port_name||'_'||dest_port_name in (select distinct route 
		                                                   from fin_dm_opt_foi.dm_fol_route_info_t t2 
														   where version_status = v_version_status 
														   and version_code = v_max_version_code  
														   and top_route_flag= 'Y'
														   and transport_mode = '精品海运')*/
		   and container_qty <> 0
		  group by version_id
		          ,year
                  ,period_id				  
				  ,transport_mode
				  ,container_type
				  ,currency
		) 
		select t1.version_id
		      ,t1.year	
              ,t1.period_id			  
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.container_type
			  ,t1.currency
			  ,'02'   as level_code
  			  ,'区域' as level_desc
			  ,t1.region_qty/t2.transport_qty as W_x
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.period_id = t2.period_id 
		 ; 
		 
		  -- 新增ALL柜型，计算区域占运输方式的货量占比 
		insert into annual_qty_tmp(
                version_id 
               ,year
               ,period_id			   
		       ,transport_mode     
		       ,region_cn_name                
		       ,container_type     
		       ,currency 
               ,level_code         
	           ,level_desc         
               ,W_x 
                )		
		with tmp1 as (
		select version_id
              ,year
              ,period_id			  
			  ,transport_mode
			  ,region_cn_name
			  ,'ALL' as container_type
			  ,currency
			  --,sum(container_qty) as region_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as region_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1
         where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		   and container_qty <> 0		 
		  group by version_id
		          ,year
                  ,period_id				  
				  ,transport_mode
				  ,region_cn_name
				  ,currency
		) ,
		 tmp2 as (
		select version_id
		      ,year
              ,period_id			  
			  ,transport_mode
			  ,'ALL' as container_type
			  ,currency
			  --,sum(container_qty) as transport_qty
			  ,sum(case when container_type = '20GP' then container_qty
                  when container_type = '40GP' then container_qty/2
                  when container_type = '40HQ' then container_qty/2
             end) as transport_qty  -- 20240821 modify by qwx1110218 无需x1x2
    	  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t t1		
		  where 1=1
		   and version_id = v_max_version_id
		   and transport_mode = '精品海运'
		   and container_qty <> 0
		  group by version_id
		          ,year
                  ,period_id				  
				  ,transport_mode
				  ,currency
		) 
		select t1.version_id
		      ,t1.year
              ,t1.period_id			  
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.container_type
			  ,t1.currency
			  ,'02'   as level_code
  			  ,'区域' as level_desc
			  ,t1.region_qty/t2.transport_qty as W_x
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.year = t2.year
		 and t1.period_id = t2.period_id
		 ; 
		 	 
		 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 4,
            p_log_cal_log_desc => '【 annual_qty_tmp 区域占运输方式的货量占比，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	 
			 
	/*	-- 计算精品海运的供应商均价
		insert into annual_price_tmp(
		       version_id
			  ,year 
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,supplier_short_name
			  ,container_type
			  ,currency
			  ,price 
			  ,container_qty
			  ,level_code
  			  ,level_desc
			  ,avg_price
			  )
			  with tmp1 as (
		select version_id
		      ,year 
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,supplier_short_name
			  ,container_type
			  ,currency
			  ,sum(price*container_qty) as price 
    	  from annual_route_tmp		
		  where 1 = 1
		    and container_qty <> 0
			and transport_mode = '精品海运'
		  group by version_id
		          ,year 
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_country_name
			      ,dest_country_name
			      ,supplier_short_name
			 	  ,container_type
				  ,currency
		) ,
		 tmp2 as (
		select version_id
		      ,year 
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,supplier_short_name
			  ,container_type
			  ,currency
			  ,sum(container_qty) as all_qty
    	  from annual_route_tmp		
		  where 1 = 1
		   and container_qty <> 0
		   and transport_mode = '精品海运'
		  group by version_id
		          ,year 
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_country_name
				  ,dest_country_name
			      ,supplier_short_name
				  ,container_type
				  ,currency
		) 
				select t1.version_id
			  ,t1.year
		      ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.source_country_name
			  ,t1.dest_country_name
			  ,t1.supplier_short_name
			  ,t1.container_type
			  ,t1.currency
			  ,t1.price   as price 
			  ,t2.all_qty as container_qty
			  ,'04'       as level_code
  			  ,'供应商'   as level_desc
			  ,t1.price/t2.all_qty as avg_price
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.year = t2.year
		 and t1.period_id  = t2.period_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.supplier_short_name = t2.supplier_short_name
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 
		 ;
		  
		   v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 5,
            p_log_cal_log_desc => '【 annual_price_tmp 精品海运的供应商均价，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	*/ 
		 
		 
		 
		  --计算精品海运的航线均价(区分柜型)
			insert into annual_price_tmp(
		       version_id
			  ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,container_type
			  ,currency
			  ,price 
			  ,container_qty
			  ,level_code
  			  ,level_desc
			  ,avg_price
			  )
	   with tmp1 as (
		select version_id
		      ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,container_type
			  ,currency
			  ,sum(price*container_qty) as price 
    	  from annual_route_tmp		
		  where transport_mode = '精品海运'
		    and container_qty <> 0
		  group by version_id
		          ,year
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_country_name
			      ,dest_country_name
			 	  ,container_type
				  ,currency
		) ,
		 tmp2 as (
		select version_id
		      ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,container_type
			  ,currency
			  ,sum(container_qty) as all_qty
    	  from annual_route_tmp		
		  where transport_mode = '精品海运'
		   and container_qty <> 0
		  group by version_id
		          ,year
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,container_type
				  ,currency
		) 
		select t1.version_id
		      ,t1.year
		      ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.source_country_name
			  ,t1.dest_country_name
			  ,t1.container_type
			  ,t1.currency
			  ,t1.price           as price 
			  ,t2.all_qty         as container_qty
			  ,'03'               as level_code
  			  ,'航线'             as level_desc
			  ,t1.price/t2.all_qty as avg_price
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.period_id  = t2.period_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route=t2.route
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.year = t2.year		 
		 ;
		 
		  -- 新增ALL柜型，计算精品海运的航线均价 
			insert into annual_price_tmp(
		       version_id
			  ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,container_type
			  ,currency
			  ,price 
			  ,container_qty
			  ,level_code
  			  ,level_desc
			  ,avg_price
			  )
	   with tmp1 as (
		select version_id
		      ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,'ALL' as container_type
			  ,currency
			  ,sum(price*container_qty) as price 
    	  from annual_route_tmp		
		  where transport_mode = '精品海运'
		    and container_qty <> 0
		  group by version_id
		          ,year
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,source_country_name
			      ,dest_country_name
				  ,currency
		) ,
		 tmp2 as (
		select version_id
		      ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,'ALL' as container_type
			  ,currency
			  ,sum(container_qty) as all_qty
    	  from annual_route_tmp		
		  where transport_mode = '精品海运'
		   and container_qty <> 0
		  group by version_id
		          ,year
		          ,period_id
				  ,transport_mode
				  ,region_cn_name
				  ,route
				  ,currency
		) 
		select t1.version_id
		      ,t1.year
		      ,t1.period_id
			  ,t1.transport_mode
			  ,t1.region_cn_name
			  ,t1.route
			  ,t1.source_country_name
			  ,t1.dest_country_name
			  ,t1.container_type
			  ,t1.currency
			  ,t1.price           as price 
			  ,t2.all_qty         as container_qty
			  ,'03'               as level_code
  			  ,'航线'             as level_desc
			  ,t1.price/t2.all_qty as avg_price
	    from tmp1 t1
		left join tmp2 t2
		  on t1.version_id = t2.version_id
		 and t1.period_id  = t2.period_id
		 and t1.transport_mode = t2.transport_mode
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route=t2.route
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.year = t2.year		 
		 ;
		 
		 
		  v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 5,
            p_log_cal_log_desc => '【 annual_price_tmp 精品海运的航线均价，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	 
		 
		 
		
		--  航线均价补齐，均价本月数据存在缺失时，先向之前的月份填充第一个不缺失的均价数据
		insert into annual_price_tmp(
		       version_id
			  ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,container_type
			  ,currency
			  ,price 
			  ,container_qty
			  ,level_code
  			  ,level_desc
			  ,avg_price
              ,apd_period_flag
			  )
		with  time_tmp AS (
              select to_char(generate_series,'YYYYMM')  as period_id
			        ,to_char(generate_series,'YYYY')    as year
              from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 month')
							
              ),
		all_route_tmp as (
		select distinct       
	     transport_mode      
	    ,region_cn_name      
	    ,route    
        ,source_country_name
		,dest_country_name		
	    ,container_type   
	    ,currency            
        from annual_price_tmp
		)
		   select v_max_version_id as version_id
		         ,t1.year
		         ,t1.period_id
			     ,t2.transport_mode
			     ,t2.region_cn_name
			     ,t2.route
				 ,t2.source_country_name
			     ,t2.dest_country_name
			     ,t2.container_type
			     ,t2.currency
				 ,t3.price 
			     ,t3.container_qty
			     ,t3.level_code
  			     ,t3.level_desc
			     ,t3.avg_price
				 ,'Y' AS apd_period_flag
		     from time_tmp t1
			 left join all_route_tmp t2
			 on 1=1
		left join annual_price_tmp t3
		on  t2.transport_mode=t3.transport_mode
		and t2.region_cn_name=t3.region_cn_name
		and t2.route = t3.route
		and t2.container_type=t3.container_type
		and t2.currency = t3.currency
		where t3.period_id = (select max(t3.period_id)  
		                        from annual_price_tmp t3  
								where t3.period_id < t1.period_id 
								and t3.level_code = '03' 
								and t2.transport_mode=t3.transport_mode
		                        and t2.region_cn_name=t3.region_cn_name
		                        and t2.route = t3.route
		                        and t2.container_type=t3.container_type
		                        and t2.currency = t3.currency
                                and t3.transport_mode = '精品海运' )
		  and t1.period_id not in ( select distinct t3.period_id  
		                             from annual_price_tmp t3 
									 where t3.level_code = '03'  
									 and t2.transport_mode=t3.transport_mode
		                             and t2.region_cn_name=t3.region_cn_name
		                             and t2.route = t3.route
		                             and t2.container_type=t3.container_type
		                             and t2.currency = t3.currency  
									 and t3.transport_mode = '精品海运')
		  and t1.period_id <= (select max(period_id)  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t  )
		  and t3.level_code = '03'
		  and t3.transport_mode = '精品海运'
		  ;
		  
		--  航线均价补齐，若之前数据全缺失,则向后面的月份填充第一个不缺失的均本数据
        insert into annual_price_tmp(
		       version_id
              ,year
		      ,period_id
			  ,transport_mode
			  ,region_cn_name
			  ,route
			  ,source_country_name
			  ,dest_country_name
			  ,container_type
			  ,currency
			  ,price 
			  ,container_qty
			  ,level_code
  			  ,level_desc
			  ,avg_price
              ,apd_period_flag
			  )
		with  time_tmp AS (
              select to_char(generate_series,'YYYYMM')  as period_id
			        ,to_char(generate_series,'YYYY')    as year
              from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 month')
              ),
		all_route_tmp as (
		select distinct       
	     transport_mode      
	    ,region_cn_name      
	    ,route    
        ,source_country_name
		,dest_country_name		
	    ,container_type   
	    ,currency            
        from annual_route_tmp
		)
		   select v_max_version_id as version_id
		         ,t1.year
		         ,t1.period_id
			     ,t2.transport_mode
			     ,t2.region_cn_name
			     ,t2.route
				 ,t2.source_country_name
			     ,t2.dest_country_name
			     ,t2.container_type
			     ,t2.currency
				 ,t3.price 
			     ,t3.container_qty
			     ,t3.level_code
  			     ,t3.level_desc
			     ,t3.avg_price
				 ,'Y' AS apd_period_flag
		     from time_tmp t1
			 left join all_route_tmp t2
			 on 1=1
		left join annual_price_tmp t3
		on  t2.transport_mode=t3.transport_mode
		and t2.region_cn_name=t3.region_cn_name
		and t2.route = t3.route
		and t2.container_type=t3.container_type
		and t2.currency = t3.currency
		where t3.period_id = (select min(t3.period_id)  
		                        from annual_price_tmp t3  
								where t3.period_id > t1.period_id 
								and t3.level_code = '03' 
								and t2.transport_mode=t3.transport_mode
		                        and t2.region_cn_name=t3.region_cn_name
		                        and t2.route = t3.route
		                        and t2.container_type=t3.container_type
		                        and t2.currency = t3.currency  
								and t3.transport_mode = '精品海运')
		  and t1.period_id not in ( select distinct t3.period_id  
		                             from annual_price_tmp t3 
									 where t3.level_code = '03'  
									 and t2.transport_mode=t3.transport_mode
		                             and t2.region_cn_name=t3.region_cn_name
		                             and t2.route = t3.route
		                             and t2.container_type=t3.container_type
		                             and t2.currency = t3.currency
                                     and t3.transport_mode = '精品海运' )
		  and t1.period_id <= (select max(period_id)  from fin_dm_opt_foi.dm_fol_route_price_info_sum_t  )
		  and t3.level_code = '03'
		  and t3.transport_mode = '精品海运' 
		    ;
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 7,
            p_log_cal_log_desc => '【 annual_price_tmp  航线均价补齐 ，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	    

						
			-- 将均价信息和航线占区域的货量占比插入物流价格指数年度表
			delete from fin_dm_opt_foi.dm_fol_annual_price_index_info_t where version_id = v_max_version_id ;
			insert into fin_dm_opt_foi.dm_fol_annual_price_index_info_t (
			        version_id
				   ,year
                   ,period_id
                   ,transport_mode
                   ,region_cn_name
                   ,route
                   ,source_country_name
                   ,dest_country_name
                   ,supplier_short_name                  
                   ,container_type
                   ,currency
                   ,price
                   ,container_qty
				   ,level_code
                   ,level_desc
                   ,avg_price
				   ,weight
                   ,apd_period_flag
                   ,remark
                   ,created_by
                   ,creation_date
                   ,last_updated_by
                   ,last_update_date
                   ,del_flag
				   )
			select  t1.version_id  
                   ,t1.year			
			       ,t1.period_id			   
			       ,t1.transport_mode     
			       ,t1.region_cn_name     
			       ,t1.route              
			       ,t1.source_country_name
			       ,t1.dest_country_name  
			       ,t1.supplier_short_name
			       ,t1.container_type     
			       ,t1.currency           
			       ,t1.price              
			       ,t1.container_qty      
			       ,t1.level_code         
			       ,t1.level_desc         
			       ,t1.avg_price 
                   ,t2.W_i	 as weight		 	   
			       ,case when apd_period_flag = 'Y' then apd_period_flag else 'N'  end as apd_period_flag
				   , '' as remark
  	               , -1 as created_by
  	               , current_timestamp as creation_date
  	               , -1 as last_updated_by
  	               , current_timestamp as last_update_date
  	               , 'N' as del_flag
			from annual_price_tmp  t1
			left join annual_qty_tmp t2
		  on t1.version_id = t2.version_id   
         and t1.year = t2.year	
         and t1.period_id = t2.period_id		 
         and t1.transport_mode = t2.transport_mode
         and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route         
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency 
	     and t1.level_code =  t2.level_code
            ;
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 8,
            p_log_cal_log_desc => '【 dm_fol_annual_price_index_info_t  所有均价信息包括补齐的均价 ，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	    
		 
		 
		
		-- 默认基期 - 区域层级的价格指数 I默认 =sum（ 【（航线的报告期均价（即202001~202401每个月的均价）/ 航线的默认基期均价（即202001的均价））】*W_i     ）  
		
		-- 先计算 默认基期 - 区域层级的价格指数SUM前的值，航线层级：【航线的报告期均价（即202001~202401每个月的均价）/ 航线的默认基期均价（即202001的均价））】*W_i
		insert into annual_price_index_tmp(
		        version_id 
               ,year				
               ,period_id 
               ,base_period_id			   
               ,transport_mode     
               ,region_cn_name     
               ,route              
               ,source_country_name
               ,dest_country_name  
               ,container_type     
               ,currency 
               ,level_code
			   ,level_desc
               ,price_index        
			  )
		with tmp as (
   		       select  version_id
			          ,year
		              ,period_id
			          ,transport_mode
			          ,region_cn_name
			          ,route
			          ,source_country_name
			          ,dest_country_name
			          ,container_type
			          ,currency
			          ,level_code
  			          ,level_desc
			          ,avg_price
			   from annual_price_tmp
			   where period_id = year||'01'
			     and level_code = '03') 
	    select t1.version_id
		      ,t1.year
              ,t1.period_id
              ,t1.year||'01' as base_period_id
              ,t1.transport_mode
              ,t1.region_cn_name
			  ,t1.route              
			  ,t1.source_country_name
			  ,t1.dest_country_name
              ,t1.container_type
			  ,t1.currency
              ,'03'   as level_code
              ,'航线' as level_desc           
              ,case when t2.avg_price= 0 then 0 else (t1.avg_price/t2.avg_price)* t3.W_i end as price_index
		from annual_price_tmp t1
		left join tmp  t2
		  on t1.version_id = t2.version_id
		 and t1.year = t2.year		 
		 and t1.transport_mode = t2.transport_mode
		 and t1.container_type = t2.container_type
		 and t1.currency = t2.currency
		 and t1.region_cn_name = t2.region_cn_name
		 and t1.route = t2.route
		 and t1.source_country_name = t2.source_country_name
		 and t1.dest_country_name = t2.dest_country_name
		 and t1.level_code = t2.level_code
		left join annual_qty_tmp t3
		  on t1.version_id = t3.version_id    
		 and t1.year = t3.year
         and t1.period_id = t3.period_id		 
         and t1.transport_mode = t3.transport_mode
         and t1.region_cn_name = t3.region_cn_name
		 and t1.route = t3.route         
		 and t1.container_type = t3.container_type
		 and t1.currency = t3.currency 
	   where t1.level_code =  '03'
	     and t3.level_code =  '03'
         and t3.W_i is not null		 
            ;
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 9,
            p_log_cal_log_desc => '【 annual_price_index_tmp  默认基期-区域层级SUM前的的年度价格指数 ，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
			
		-- 计算 默认基期 区域层级的价格指数 和 区域占运输方式的货量占比，插入物流价格指数年度表
		insert into fin_dm_opt_foi.dm_fol_annual_price_index_info_t (
	           version_id
			  ,year 
              ,period_id
              ,base_period_id
              ,transport_mode
              ,region_cn_name
              ,level_code
              ,level_desc
              ,container_type
              ,currency
              ,price_index
			  ,weight
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
			  )
       select t1.version_id  
             ,t1.year        
             ,t1.period_id          
             ,t1.base_period_id       
             ,t1.transport_mode     
             ,t1.region_cn_name                     
             ,'02'   as level_code
		     ,'区域' as level_desc
		     ,t1.container_type     
             ,t1.currency
             ,sum(t1.price_index)*100   as price_index
			 ,sum(t2.W_x) as weight
             , '' as remark
  	         , -1 as created_by
  	         , current_timestamp as creation_date
  	         , -1 as last_updated_by
  	         , current_timestamp as last_update_date
  	         , 'N' as del_flag
		 from annual_price_index_tmp t1
		 left join annual_qty_tmp t2
		   on t1.version_id = t2.version_id   
          and t1.year = t2.year
          and t1.period_id = t2.period_id		  
          and t1.transport_mode = t2.transport_mode
          and t1.region_cn_name = t2.region_cn_name
		  and t1.container_type = t2.container_type
		  and t1.currency = t2.currency 
		where t1.level_code = '03'
		  and t2.level_code = '02'
		group by t1.version_id
		        ,t1.year
		        ,t1.period_id
                ,t1.base_period_id
				,t1.transport_mode
				,t1.region_cn_name
				,t1.container_type
				,t1.currency
             ;
			 
			 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 10,
            p_log_cal_log_desc => '【 dm_fol_annual_price_index_info_t  默认基期-区域层级的年度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
		 
		 
		 
		-- 默认基期-运输方式层级的年度价格指数 C  = sum（ ( I默认的全部报告期每个月的价格指数 /  I默认-报告期为默认基期的那个月的指数 )  *W_x    ）
		
		-- 计算 默认基期-运输方式层级的年度价格指数
			 insert into fin_dm_opt_foi.dm_fol_annual_price_index_info_t(
		        version_id  
               ,year				
               ,period_id          
               ,base_period_id     
               ,transport_mode        
               ,container_type     
               ,currency 
               ,level_code
			   ,level_desc
               ,price_index  
               ,remark
               ,created_by
               ,creation_date
               ,last_updated_by
               ,last_update_date
               ,del_flag			   
			  )				
	    select t1.version_id
		      ,t1.year
              ,t1.period_id
              ,t1.year||'01' as base_period_id
              ,t1.transport_mode
              ,t1.container_type
			  ,t1.currency
              ,'01'   as level_code
              ,'运输方式' as level_desc           
              ,sum(t1.price_index* t3.W_x) as price_index
			  , '' as remark
  	          , -1 as created_by
  	          , current_timestamp as creation_date
  	          , -1 as last_updated_by
  	          , current_timestamp as last_update_date
  	          , 'N' as del_flag
		from fin_dm_opt_foi.dm_fol_annual_price_index_info_t t1		
		 left join annual_qty_tmp t3
		  on t1.version_id = t3.version_id    
		 and t1.year = t3.year
         and t1.period_id = t3.period_id		 
         and t1.transport_mode = t3.transport_mode
         and t1.region_cn_name = t3.region_cn_name     
		 and t1.container_type = t3.container_type
		 and t1.currency = t3.currency
	   where t1.version_id = v_max_version_id
		 and t1.level_code =  '02'
		 and t3.level_code =  '02'
		 and t1.base_period_id = t1.year||'01'
		 group by t1.version_id
		        ,t1.year
		        ,t1.period_id
                ,t1.year||'01'
				,t1.transport_mode
				,t1.container_type
				,t1.currency
            ;
			
			v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 11,
            p_log_cal_log_desc => '【 annual_price_index_tmp  默认基期-运输方式层级sum前的年度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
		 
		 
			
       /* -- 计算 默认基期-运输方式层级的年度价格指数
        insert into fin_dm_opt_foi.dm_fol_annual_price_index_info_t (
	           version_id
			  ,year
              ,period_id
              ,base_period_id
              ,transport_mode
              ,level_code
              ,level_desc
              ,container_type
              ,currency
              ,price_index
              ,remark
              ,created_by
              ,creation_date
              ,last_updated_by
              ,last_update_date
              ,del_flag
			  )
       select version_id  
             ,year       
             ,period_id          
             ,base_period_id     
             ,transport_mode                     
             ,'01'       as level_code
		     ,'运输方式' as level_desc
		     ,container_type     
             ,currency
             ,sum(price_index)   as price_index
             , '' as remark
  	         , -1 as created_by
  	         , current_timestamp as creation_date
  	         , -1 as last_updated_by
  	         , current_timestamp as last_update_date
  	         , 'N' as del_flag
		 from annual_price_index_tmp
		where level_code = '02'
		  and base_period_id = year||'01'
		group by version_id
		        ,year
		        ,period_id
                ,base_period_id
				,transport_mode
				,container_type
				,currency
             ;
			 */
			 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 12,
            p_log_cal_log_desc => '【 dm_fol_annual_price_index_info_t  默认基期-运输方式层级的年度价格指数，数据量：'||v_dml_row_count,--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
		 
		-- 清理已经写入版本信息表的数据
         delete from fin_dm_opt_foi.dm_fol_version_info_t 
		 where version_id = v_max_version_id 
		 and step = 1 
		 and source_en_name = 'f_dm_fol_annual_price_index_info_t'
		 and refresh_type = nvl(p_refresh_type,'4_AUTO');	 
       
 
       --将版本信息表中的执行步骤改为：1 成功
		update fin_dm_opt_foi.dm_fol_version_info_t 
		set step = 1 
		where source_en_name = 'f_dm_fol_annual_price_index_info_t' 
		  and version_id = v_max_version_id
		  and refresh_type = nvl(p_refresh_type,'4_AUTO');

 v_dml_row_count := sql%rowcount;  -- 收集数据量

        perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
            p_log_version_id => null,                 --版本
            p_log_sp_name => v_sp_name,    --sp名称
            p_log_para_list => '',--参数
            p_log_step_num  => 13,
            p_log_cal_log_desc => '【 dm_fol_version_info_t  成功数据写入到版本信息表，数据量：'||v_dml_row_count||'，运行结束！',--日志描述
            p_log_formula_sql_txt => null,--错误信息
            p_log_row_count => v_dml_row_count,
            p_log_errbuf => null  --错误编码
         ) ;	  
		 
		 
		 
  exception
    when others then
       perform fin_dm_opt_foi.f_dm_fol_capture_log_info_t(
         p_log_version_id => null,                 --版本
         p_log_sp_name => v_sp_name,    --sp名称
         p_log_para_list => '',--参数
         p_log_step_num  => null,
         p_log_cal_log_desc => v_sp_name||'：运行错误',--日志描述
         p_log_formula_sql_txt => sqlerrm,--错误信息
	      p_log_row_count => null,
	      p_log_errbuf => sqlstate  --错误编码
       ) ;
	x_success_flag := '2001';	         --2001表示失败
	
	 -- 失败数据写入到版本信息表
  insert into fin_dm_opt_foi.dm_fol_version_info_t(
           version_id           -- 版本ID（自动生成）
         , version_code         -- 版本编码（格式：yyyymmdd-001、yyyymmdd-002）
         , step                 -- 执行步骤（1、成功   2、执行中   2001、失败）
         , source_en_name       -- 来源英文描述（可以是表名、函数名等）
         , source_cn_name       -- 来源中文描述
         , refresh_type         -- 刷新类型（1_刷新价格表 、2_刷新系统 、3_刷新价格表和系统 、4_AUTO）
         , remark               -- 备注
         , created_by           -- 创建人
         , creation_date        -- 创建时间
         , last_updated_by      -- 修改人
         , last_update_date     -- 修改时间
         , del_flag             -- 是否删除
    )
    select v_max_version_id  as version_id
         , v_max_version_code as version_code
         , 2001 as step
         , 'f_f_dm_fol_annual_price_index_info_t' as source_en_name
         , '物流价格指数年度函数' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
         , 'version_code为航线清单表的最大版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
     union all
    select v_max_version_id   as version_id
         , v_apd_max_version_code as version_code
         , 2001 as step
         , 'f_f_dm_fol_annual_price_index_info_t' as source_en_name
         , '物流价格指数年度函数' as source_cn_name
         , nvl(p_refresh_type,'4_AUTO') as refresh_type
         , 'version_code为物流价格补录表的最大版本编码' as remark
  	     , -1 as created_by
  	     , current_timestamp as creation_date
  	     , -1 as last_updated_by
  	     , current_timestamp as last_update_date
  	     , 'N' as del_flag
  ;

  --收集统计信息
  analyse fin_dm_opt_foi.dm_fol_annual_price_index_info_t;
  analyse fin_dm_opt_foi.dm_fol_version_info_t;
  
end;
$$
/

